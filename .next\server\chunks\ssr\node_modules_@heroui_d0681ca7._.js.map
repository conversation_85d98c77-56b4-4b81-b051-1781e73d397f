{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-aria-modal-overlay/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { ariaHideOutside, usePreventScroll, useOverlayFocusContain } from \"@react-aria/overlays\";\nimport { mergeProps } from \"@react-aria/utils\";\nimport { useEffect } from \"react\";\nimport { useAriaOverlay } from \"@heroui/use-aria-overlay\";\nfunction useAriaModalOverlay(props = {\n  shouldBlockScroll: true\n}, state, ref) {\n  let { overlayProps, underlayProps } = useAriaOverlay(\n    {\n      ...props,\n      isOpen: state.isOpen,\n      onClose: state.close\n    },\n    ref\n  );\n  usePreventScroll({\n    isDisabled: !state.isOpen || !props.shouldBlockScroll\n  });\n  useOverlayFocusContain();\n  useEffect(() => {\n    if (state.isOpen && ref.current) {\n      return ariaHideOutside([ref.current]);\n    }\n  }, [state.isOpen, ref]);\n  return {\n    modalProps: mergeProps(overlayProps),\n    underlayProps\n  };\n}\nexport {\n  useAriaModalOverlay\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf;AAAA;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,oBAAoB,QAAQ;IACnC,mBAAmB;AACrB,CAAC,EAAE,KAAK,EAAE,GAAG;IACX,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EACjD;QACE,GAAG,KAAK;QACR,QAAQ,MAAM,MAAM;QACpB,SAAS,MAAM,KAAK;IACtB,GACA;IAEF,CAAA,GAAA,wKAAA,CAAA,mBAAgB,AAAD,EAAE;QACf,YAAY,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,iBAAiB;IACvD;IACA,CAAA,GAAA,+JAAA,CAAA,yBAAsB,AAAD;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,IAAI,IAAI,OAAO,EAAE;YAC/B,OAAO,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD,EAAE;gBAAC,IAAI,OAAO;aAAC;QACtC;IACF,GAAG;QAAC,MAAM,MAAM;QAAE;KAAI;IACtB,OAAO;QACL,YAAY,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE;QACvB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-4NFT5TQK.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\nimport {\n  dataFocusVisibleClasses\n} from \"./chunk-JGY6VQQQ.mjs\";\n\n// src/components/modal.ts\nvar modal = tv({\n  slots: {\n    wrapper: [\n      \"flex\",\n      \"w-screen\",\n      \"h-[100dvh]\",\n      \"fixed\",\n      \"inset-0\",\n      \"z-50\",\n      \"overflow-x-auto\",\n      \"justify-center\",\n      \"h-[--visual-viewport-height]\"\n    ],\n    base: [\n      \"flex\",\n      \"flex-col\",\n      \"relative\",\n      \"bg-white\",\n      \"z-50\",\n      \"w-full\",\n      \"box-border\",\n      \"bg-content1\",\n      \"outline-solid outline-transparent\",\n      \"mx-1\",\n      \"my-1\",\n      \"sm:mx-6\",\n      \"sm:my-16\"\n    ],\n    backdrop: \"z-50\",\n    header: \"flex py-4 px-6 flex-initial text-large font-semibold\",\n    body: \"flex flex-1 flex-col gap-3 px-6 py-2\",\n    footer: \"flex flex-row gap-2 px-6 py-4 justify-end\",\n    closeButton: [\n      \"absolute\",\n      \"appearance-none\",\n      \"outline-solid outline-transparent\",\n      \"select-none\",\n      \"top-1\",\n      \"end-1\",\n      \"p-2\",\n      \"text-foreground-500\",\n      \"rounded-full\",\n      \"hover:bg-default-100\",\n      \"active:bg-default-200\",\n      \"tap-highlight-transparent\",\n      // focus ring\n      ...dataFocusVisibleClasses\n    ]\n  },\n  variants: {\n    size: {\n      xs: {\n        base: \"max-w-xs\"\n      },\n      sm: {\n        base: \"max-w-sm\"\n      },\n      md: {\n        base: \"max-w-md\"\n      },\n      lg: {\n        base: \"max-w-lg\"\n      },\n      xl: {\n        base: \"max-w-xl\"\n      },\n      \"2xl\": {\n        base: \"max-w-2xl\"\n      },\n      \"3xl\": {\n        base: \"max-w-3xl\"\n      },\n      \"4xl\": {\n        base: \"max-w-4xl\"\n      },\n      \"5xl\": {\n        base: \"max-w-5xl\"\n      },\n      full: {\n        base: \"my-0 mx-0 sm:mx-0 sm:my-0 max-w-full h-[100dvh] min-h-[100dvh] !rounded-none\"\n      }\n    },\n    radius: {\n      none: { base: \"rounded-none\" },\n      sm: { base: \"rounded-small\" },\n      md: { base: \"rounded-medium\" },\n      lg: { base: \"rounded-large\" }\n    },\n    placement: {\n      auto: {\n        wrapper: \"items-end sm:items-center\"\n      },\n      center: {\n        wrapper: \"items-center sm:items-center\"\n      },\n      top: {\n        wrapper: \"items-start sm:items-start\"\n      },\n      \"top-center\": {\n        wrapper: \"items-start sm:items-center\"\n      },\n      bottom: {\n        wrapper: \"items-end sm:items-end\"\n      },\n      \"bottom-center\": {\n        wrapper: \"items-end sm:items-center\"\n      }\n    },\n    shadow: {\n      none: {\n        base: \"shadow-none\"\n      },\n      sm: {\n        base: \"shadow-small\"\n      },\n      md: {\n        base: \"shadow-medium\"\n      },\n      lg: {\n        base: \"shadow-large\"\n      }\n    },\n    backdrop: {\n      transparent: {\n        backdrop: \"hidden\"\n      },\n      opaque: {\n        backdrop: \"bg-overlay/50 backdrop-opacity-disabled\"\n      },\n      blur: {\n        backdrop: \"backdrop-blur-md backdrop-saturate-150 bg-overlay/30\"\n      }\n    },\n    scrollBehavior: {\n      normal: {\n        base: \"overflow-y-hidden\"\n      },\n      inside: {\n        base: \"max-h-[calc(100%_-_8rem)]\",\n        body: \"overflow-y-auto\"\n      },\n      outside: {\n        wrapper: \"items-start sm:items-start overflow-y-auto\",\n        base: \"my-16\"\n      }\n    },\n    disableAnimation: {\n      false: {\n        wrapper: [\n          //  mobile animation vars\n          \"[--scale-enter:100%]\",\n          \"[--scale-exit:100%]\",\n          \"[--slide-enter:0px]\",\n          \"[--slide-exit:80px]\",\n          // tablet/desktop animation vars\n          \"sm:[--scale-enter:100%]\",\n          \"sm:[--scale-exit:103%]\",\n          \"sm:[--slide-enter:0px]\",\n          \"sm:[--slide-exit:0px]\"\n        ]\n      }\n    }\n  },\n  defaultVariants: {\n    size: \"md\",\n    radius: \"lg\",\n    shadow: \"sm\",\n    placement: \"auto\",\n    backdrop: \"opaque\",\n    scrollBehavior: \"normal\"\n  },\n  compoundVariants: [\n    // backdrop (opaque/blur)\n    {\n      backdrop: [\"opaque\", \"blur\"],\n      class: {\n        backdrop: \"w-screen h-screen fixed inset-0\"\n      }\n    }\n  ]\n});\n\nexport {\n  modal\n};\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;AAIA,0BAA0B;AAC1B,IAAI,QAAQ,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACb,OAAO;QACL,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aAAa;eACV,+JAAA,CAAA,0BAAuB;SAC3B;IACH;IACA,UAAU;QACR,MAAM;YACJ,IAAI;gBA<PERSON>,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;YACA,OAAO;gBACL,MAAM;YACR;YACA,OAAO;gBACL,MAAM;YACR;YACA,OAAO;gBACL,MAAM;YACR;YACA,OAAO;gBACL,MAAM;YACR;YACA,MAAM;gBACJ,MAAM;YACR;QACF;QACA,QAAQ;YACN,MAAM;gBAAE,MAAM;YAAe;YAC7B,IAAI;gBAAE,MAAM;YAAgB;YAC5B,IAAI;gBAAE,MAAM;YAAiB;YAC7B,IAAI;gBAAE,MAAM;YAAgB;QAC9B;QACA,WAAW;YACT,MAAM;gBACJ,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;YACX;YACA,KAAK;gBACH,SAAS;YACX;YACA,cAAc;gBACZ,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;YACX;YACA,iBAAiB;gBACf,SAAS;YACX;QACF;QACA,QAAQ;YACN,MAAM;gBACJ,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;YACA,IAAI;gBACF,MAAM;YACR;QACF;QACA,UAAU;YACR,aAAa;gBACX,UAAU;YACZ;YACA,QAAQ;gBACN,UAAU;YACZ;YACA,MAAM;gBACJ,UAAU;YACZ;QACF;QACA,gBAAgB;YACd,QAAQ;gBACN,MAAM;YACR;YACA,QAAQ;gBACN,MAAM;gBACN,MAAM;YACR;YACA,SAAS;gBACP,SAAS;gBACT,MAAM;YACR;QACF;QACA,kBAAkB;YAChB,OAAO;gBACL,SAAS;oBACP,yBAAyB;oBACzB;oBACA;oBACA;oBACA;oBACA,gCAAgC;oBAChC;oBACA;oBACA;oBACA;iBACD;YACH;QACF;IACF;IACA,iBAAiB;QACf,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,UAAU;QACV,gBAAgB;IAClB;IACA,kBAAkB;QAChB,yBAAyB;QACzB;YACE,UAAU;gBAAC;gBAAU;aAAO;YAC5B,OAAO;gBACL,UAAU;YACZ;QACF;KACD;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-UQQ5KWB7.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\nimport {\n  dataFocusVisibleClasses\n} from \"./chunk-JGY6VQQQ.mjs\";\n\n// src/components/select.ts\nvar select = tv({\n  slots: {\n    base: [\"group inline-flex flex-col relative\"],\n    label: [\n      \"block\",\n      \"absolute\",\n      \"z-10\",\n      \"origin-top-left\",\n      \"flex-shrink-0\",\n      // Using RTL here as Tailwind CSS doesn't support `start` and `end` logical properties for transforms yet.\n      \"rtl:origin-top-right\",\n      \"subpixel-antialiased\",\n      \"text-small\",\n      \"text-foreground-500\",\n      \"pointer-events-none\",\n      \"group-data-[has-label-outside=true]:pointer-events-auto\"\n    ],\n    mainWrapper: \"w-full flex flex-col\",\n    trigger: \"relative px-3 gap-3 w-full inline-flex flex-row items-center shadow-xs outline-solid outline-transparent tap-highlight-transparent\",\n    innerWrapper: \"inline-flex h-fit w-[calc(100%_-theme(spacing.6))] min-h-4 items-center gap-1.5 box-border\",\n    selectorIcon: \"absolute end-3 w-4 h-4\",\n    spinner: \"absolute end-3\",\n    value: [\"text-foreground-500\", \"font-normal\", \"w-full\", \"text-start\"],\n    listboxWrapper: \"scroll-py-6 w-full\",\n    listbox: \"\",\n    popoverContent: \"w-full p-1 overflow-hidden\",\n    clearButton: [\n      \"w-4\",\n      \"h-4\",\n      \"z-10\",\n      \"mb-4\",\n      \"relative\",\n      \"start-auto\",\n      \"appearance-none\",\n      \"outline-none\",\n      \"select-none\",\n      \"opacity-70\",\n      \"hover:!opacity-100\",\n      \"cursor-pointer\",\n      \"active:!opacity-70\",\n      \"rounded-full\",\n      // focus ring\n      ...dataFocusVisibleClasses\n    ],\n    helperWrapper: \"p-1 flex relative flex-col gap-1.5 group-data-[has-helper=true]:flex\",\n    description: \"text-tiny text-foreground-400\",\n    errorMessage: \"text-tiny text-danger\",\n    endWrapper: \"flex end-18\",\n    endContent: \"mb-4\"\n  },\n  variants: {\n    variant: {\n      flat: {\n        trigger: [\n          \"bg-default-100\",\n          \"data-[hover=true]:bg-default-200\",\n          \"group-data-[focus=true]:bg-default-200\"\n        ],\n        clearButton: \"mb-4\"\n      },\n      faded: {\n        trigger: [\n          \"bg-default-100\",\n          \"border-medium\",\n          \"border-default-200\",\n          \"data-[hover=true]:border-default-400 data-[focus=true]:border-default-400 data-[open=true]:border-default-400\"\n        ],\n        value: \"group-data-[has-value=true]:text-default-foreground\",\n        clearButton: \"mb-4\"\n      },\n      bordered: {\n        trigger: [\n          \"border-medium\",\n          \"border-default-200\",\n          \"data-[hover=true]:border-default-400\",\n          \"data-[open=true]:border-default-foreground\",\n          \"data-[focus=true]:border-default-foreground\"\n        ],\n        value: \"group-data-[has-value=true]:text-default-foreground\",\n        clearButton: \"mb-4\"\n      },\n      underlined: {\n        trigger: [\n          \"!px-1\",\n          \"!pb-0\",\n          \"!gap-0\",\n          \"relative\",\n          \"box-border\",\n          \"border-b-medium\",\n          \"shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]\",\n          \"border-default-200\",\n          \"!rounded-none\",\n          \"hover:border-default-300\",\n          \"after:content-['']\",\n          \"after:w-0\",\n          \"after:origin-center\",\n          \"after:bg-default-foreground\",\n          \"after:absolute\",\n          \"after:left-1/2\",\n          \"after:-translate-x-1/2\",\n          \"after:-bottom-[2px]\",\n          \"after:h-[2px]\",\n          \"data-[open=true]:after:w-full\",\n          \"data-[focus=true]:after:w-full\"\n        ],\n        value: \"group-data-[has-value=true]:text-default-foreground\",\n        clearButton: \"mb-4 me-2\"\n      }\n    },\n    color: {\n      default: {},\n      primary: {\n        selectorIcon: \"text-primary\"\n      },\n      secondary: {\n        selectorIcon: \"text-secondary\"\n      },\n      success: {\n        selectorIcon: \"text-success\"\n      },\n      warning: {\n        selectorIcon: \"text-warning\"\n      },\n      danger: {\n        selectorIcon: \"text-danger\"\n      }\n    },\n    size: {\n      sm: {\n        label: \"text-tiny\",\n        trigger: \"h-8 min-h-8 px-2 rounded-small\",\n        value: \"text-small\",\n        clearButton: \"text-medium\"\n      },\n      md: {\n        trigger: \"h-10 min-h-10 rounded-medium\",\n        value: \"text-small\",\n        clearButton: \"text-large\"\n      },\n      lg: {\n        trigger: \"h-12 min-h-12 rounded-large\",\n        value: \"text-medium\",\n        clearButton: \"mb-5 text-large\"\n      }\n    },\n    radius: {\n      none: {\n        trigger: \"rounded-none\"\n      },\n      sm: {\n        trigger: \"rounded-small\"\n      },\n      md: {\n        trigger: \"rounded-medium\"\n      },\n      lg: {\n        trigger: \"rounded-large\"\n      },\n      full: {\n        trigger: \"rounded-full\"\n      }\n    },\n    labelPlacement: {\n      outside: {\n        base: \"flex flex-col\",\n        clearButton: \"mb-0\"\n      },\n      \"outside-left\": {\n        base: \"flex-row items-center flex-nowrap data-[has-helper=true]:items-start\",\n        label: \"relative pe-2 text-foreground\",\n        clearButton: \"mb-0\"\n      },\n      inside: {\n        label: \"text-tiny cursor-pointer\",\n        trigger: \"flex-col items-start justify-center gap-0\"\n      }\n    },\n    fullWidth: {\n      true: {\n        base: \"w-full\"\n      },\n      false: {\n        base: \"min-w-40\"\n      }\n    },\n    isClearable: {\n      true: {\n        clearButton: \"peer-data-[filled=true]:opacity-70 peer-data-[filled=true]:block\",\n        endContent: \"ms-3\"\n      }\n    },\n    isDisabled: {\n      true: {\n        base: \"opacity-disabled pointer-events-none\",\n        trigger: \"pointer-events-none\"\n      }\n    },\n    isInvalid: {\n      true: {\n        label: \"!text-danger\",\n        value: \"!text-danger\",\n        selectorIcon: \"text-danger\"\n      }\n    },\n    isRequired: {\n      true: {\n        label: \"after:content-['*'] after:text-danger after:ms-0.5\"\n      }\n    },\n    isMultiline: {\n      true: {\n        label: \"relative\",\n        trigger: \"!h-auto\"\n      },\n      false: {\n        value: \"truncate\"\n      }\n    },\n    disableAnimation: {\n      true: {\n        trigger: \"after:transition-none\",\n        base: \"transition-none\",\n        label: \"transition-none\",\n        selectorIcon: \"transition-none\"\n      },\n      false: {\n        base: \"transition-background motion-reduce:transition-none !duration-150\",\n        label: [\n          \"will-change-auto\",\n          \"origin-top-left\",\n          // Using RTL here as Tailwind CSS doesn't support `start` and `end` logical properties for transforms yet.\n          \"rtl:origin-top-right\",\n          \"!duration-200\",\n          \"!ease-out\",\n          \"transition-[transform,color,left,opacity,translate,scale]\",\n          \"motion-reduce:transition-none\"\n        ],\n        selectorIcon: \"transition-transform duration-150 ease motion-reduce:transition-none\",\n        clearButton: [\"transition-opacity\", \"motion-reduce:transition-none\"]\n      }\n    },\n    disableSelectorIconRotation: {\n      true: {},\n      false: {\n        selectorIcon: \"data-[open=true]:rotate-180\"\n      }\n    }\n  },\n  defaultVariants: {\n    variant: \"flat\",\n    color: \"default\",\n    size: \"md\",\n    fullWidth: true,\n    isDisabled: false,\n    isMultiline: false,\n    disableSelectorIconRotation: false\n  },\n  compoundVariants: [\n    // flat & color\n    {\n      variant: \"flat\",\n      color: \"default\",\n      class: {\n        value: \"group-data-[has-value=true]:text-default-foreground\",\n        trigger: [\"bg-default-100\", \"data-[hover=true]:bg-default-200\"]\n      }\n    },\n    {\n      variant: \"flat\",\n      color: \"primary\",\n      class: {\n        trigger: [\n          \"bg-primary-100\",\n          \"text-primary\",\n          \"data-[hover=true]:bg-primary-50\",\n          \"group-data-[focus=true]:bg-primary-50\"\n        ],\n        value: \"text-primary\",\n        label: \"text-primary\"\n      }\n    },\n    {\n      variant: \"flat\",\n      color: \"secondary\",\n      class: {\n        trigger: [\n          \"bg-secondary-100\",\n          \"text-secondary\",\n          \"data-[hover=true]:bg-secondary-50\",\n          \"group-data-[focus=true]:bg-secondary-50\"\n        ],\n        value: \"text-secondary\",\n        label: \"text-secondary\"\n      }\n    },\n    {\n      variant: \"flat\",\n      color: \"success\",\n      class: {\n        trigger: [\n          \"bg-success-100\",\n          \"text-success-600\",\n          \"dark:text-success\",\n          \"data-[hover=true]:bg-success-50\",\n          \"group-data-[focus=true]:bg-success-50\"\n        ],\n        value: \"text-success-600 dark:text-success\",\n        label: \"text-success-600 dark:text-success\"\n      }\n    },\n    {\n      variant: \"flat\",\n      color: \"warning\",\n      class: {\n        trigger: [\n          \"bg-warning-100\",\n          \"text-warning-600\",\n          \"dark:text-warning\",\n          \"data-[hover=true]:bg-warning-50\",\n          \"group-data-[focus=true]:bg-warning-50\"\n        ],\n        value: \"text-warning-600 dark:text-warning\",\n        label: \"text-warning-600 dark:text-warning\"\n      }\n    },\n    {\n      variant: \"flat\",\n      color: \"danger\",\n      class: {\n        trigger: [\n          \"bg-danger-100\",\n          \"text-danger\",\n          \"dark:text-danger-500\",\n          \"data-[hover=true]:bg-danger-50\",\n          \"group-data-[focus=true]:bg-danger-50\"\n        ],\n        value: \"text-danger dark:text-danger-500\",\n        label: \"text-danger dark:text-danger-500\"\n      }\n    },\n    // faded & color\n    {\n      variant: \"faded\",\n      color: \"primary\",\n      class: {\n        trigger: \"data-[hover=true]:border-primary data-[focus=true]:border-primary data-[open=true]:border-primary\",\n        label: \"text-primary\"\n      }\n    },\n    {\n      variant: \"faded\",\n      color: \"secondary\",\n      class: {\n        trigger: \"data-[hover=true]:border-secondary data-[focus=true]:border-secondary data-[open=true]:border-secondary\",\n        label: \"text-secondary\"\n      }\n    },\n    {\n      variant: \"faded\",\n      color: \"success\",\n      class: {\n        trigger: \"data-[hover=true]:border-success data-[focus=true]:border-success data-[open=true]:border-success\",\n        label: \"text-success\"\n      }\n    },\n    {\n      variant: \"faded\",\n      color: \"warning\",\n      class: {\n        trigger: \"data-[hover=true]:border-warning data-[focus=true]:border-warning data-[open=true]:border-warning\",\n        label: \"text-warning\"\n      }\n    },\n    {\n      variant: \"faded\",\n      color: \"danger\",\n      class: {\n        trigger: \"data-[hover=true]:border-danger data-[focus=true]:border-danger data-[open=true]:border-danger\",\n        label: \"text-danger\"\n      }\n    },\n    // underlined & color\n    // underlined & color\n    {\n      variant: \"underlined\",\n      color: \"default\",\n      class: {\n        value: \"group-data-[has-value=true]:text-foreground\"\n      }\n    },\n    {\n      variant: \"underlined\",\n      color: \"primary\",\n      class: {\n        trigger: \"after:bg-primary\",\n        label: \"text-primary\"\n      }\n    },\n    {\n      variant: \"underlined\",\n      color: \"secondary\",\n      class: {\n        trigger: \"after:bg-secondary\",\n        label: \"text-secondary\"\n      }\n    },\n    {\n      variant: \"underlined\",\n      color: \"success\",\n      class: {\n        trigger: \"after:bg-success\",\n        label: \"text-success\"\n      }\n    },\n    {\n      variant: \"underlined\",\n      color: \"warning\",\n      class: {\n        trigger: \"after:bg-warning\",\n        label: \"text-warning\"\n      }\n    },\n    {\n      variant: \"underlined\",\n      color: \"danger\",\n      class: {\n        trigger: \"after:bg-danger\",\n        label: \"text-danger\"\n      }\n    },\n    // bordered & color\n    {\n      variant: \"bordered\",\n      color: \"primary\",\n      class: {\n        trigger: [\"data-[open=true]:border-primary\", \"data-[focus=true]:border-primary\"],\n        label: \"text-primary\"\n      }\n    },\n    {\n      variant: \"bordered\",\n      color: \"secondary\",\n      class: {\n        trigger: [\"data-[open=true]:border-secondary\", \"data-[focus=true]:border-secondary\"],\n        label: \"text-secondary\"\n      }\n    },\n    {\n      variant: \"bordered\",\n      color: \"success\",\n      class: {\n        trigger: [\"data-[open=true]:border-success\", \"data-[focus=true]:border-success\"],\n        label: \"text-success\"\n      }\n    },\n    {\n      variant: \"bordered\",\n      color: \"warning\",\n      class: {\n        trigger: [\"data-[open=true]:border-warning\", \"data-[focus=true]:border-warning\"],\n        label: \"text-warning\"\n      }\n    },\n    {\n      variant: \"bordered\",\n      color: \"danger\",\n      class: {\n        trigger: [\"data-[open=true]:border-danger\", \"data-[focus=true]:border-danger\"],\n        label: \"text-danger\"\n      }\n    },\n    // labelPlacement=outside & default\n    {\n      labelPlacement: \"inside\",\n      color: \"default\",\n      class: {\n        label: \"group-data-[filled=true]:text-default-600\"\n      }\n    },\n    // labelPlacement=outside & default\n    {\n      labelPlacement: \"outside\",\n      color: \"default\",\n      class: {\n        label: \"group-data-[filled=true]:text-foreground\"\n      }\n    },\n    // radius-full & size\n    {\n      radius: \"full\",\n      size: [\"sm\"],\n      class: {\n        trigger: \"px-3\"\n      }\n    },\n    {\n      radius: \"full\",\n      size: \"md\",\n      class: {\n        trigger: \"px-4\"\n      }\n    },\n    {\n      radius: \"full\",\n      size: \"lg\",\n      class: {\n        trigger: \"px-5\"\n      }\n    },\n    // !disableAnimation & variant\n    {\n      disableAnimation: false,\n      variant: [\"faded\", \"bordered\"],\n      class: {\n        trigger: \"transition-colors motion-reduce:transition-none\"\n      }\n    },\n    {\n      disableAnimation: false,\n      variant: \"underlined\",\n      class: {\n        trigger: \"after:transition-width motion-reduce:after:transition-none\"\n      }\n    },\n    // flat & faded\n    {\n      variant: [\"flat\", \"faded\"],\n      class: {\n        trigger: [\n          // focus ring\n          ...dataFocusVisibleClasses\n        ]\n      }\n    },\n    // isInvalid & variant\n    {\n      isInvalid: true,\n      variant: \"flat\",\n      class: {\n        trigger: [\n          \"bg-danger-50\",\n          \"data-[hover=true]:bg-danger-100\",\n          \"group-data-[focus=true]:bg-danger-50\"\n        ]\n      }\n    },\n    {\n      isInvalid: true,\n      variant: \"bordered\",\n      class: {\n        trigger: \"!border-danger group-data-[focus=true]:border-danger\"\n      }\n    },\n    {\n      isInvalid: true,\n      variant: \"underlined\",\n      class: {\n        trigger: \"after:bg-danger\"\n      }\n    },\n    // size & labelPlacement\n    {\n      labelPlacement: \"inside\",\n      size: \"sm\",\n      class: {\n        trigger: \"h-12 min-h-12 py-1.5 px-3\"\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      size: \"md\",\n      class: {\n        trigger: \"h-14 min-h-14 py-2\"\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      size: \"lg\",\n      class: {\n        label: \"text-medium\",\n        trigger: \"h-16 min-h-16 py-2.5 gap-0\"\n      }\n    },\n    {\n      labelPlacement: \"outside\",\n      isMultiline: false,\n      class: {\n        base: \"group relative justify-end\",\n        label: [\"pb-0\", \"z-20\", \"top-1/2\", \"-translate-y-1/2\", \"group-data-[filled=true]:start-0\"]\n      }\n    },\n    // labelPlacement=[inside]\n    {\n      labelPlacement: [\"inside\"],\n      class: {\n        label: \"group-data-[filled=true]:scale-85\"\n      }\n    },\n    // inside & size\n    {\n      labelPlacement: \"inside\",\n      size: [\"sm\", \"md\"],\n      class: {\n        label: \"text-small\"\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      isMultiline: false,\n      size: \"sm\",\n      class: {\n        label: [\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_8px)]\"\n        ],\n        innerWrapper: \"group-data-[has-label=true]:pt-4\"\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      isMultiline: false,\n      size: \"md\",\n      class: {\n        label: [\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_6px)]\"\n        ],\n        innerWrapper: \"group-data-[has-label=true]:pt-4\"\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      isMultiline: false,\n      size: \"lg\",\n      class: {\n        label: [\n          \"text-medium\",\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_8px)]\"\n        ],\n        innerWrapper: \"group-data-[has-label=true]:pt-5\"\n      }\n    },\n    // inside & size & [faded, bordered]\n    {\n      labelPlacement: \"inside\",\n      variant: [\"faded\", \"bordered\"],\n      isMultiline: false,\n      size: \"sm\",\n      class: {\n        label: [\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_8px_-_var(--heroui-border-width-medium))]\"\n        ]\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      variant: [\"faded\", \"bordered\"],\n      isMultiline: false,\n      size: \"md\",\n      class: {\n        label: [\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_6px_-_var(--heroui-border-width-medium))]\"\n        ]\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      variant: [\"faded\", \"bordered\"],\n      isMultiline: false,\n      size: \"lg\",\n      class: {\n        label: [\n          \"text-medium\",\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_8px_-_var(--heroui-border-width-medium))]\"\n        ]\n      }\n    },\n    // inside & size & underlined\n    {\n      labelPlacement: \"inside\",\n      variant: \"underlined\",\n      isMultiline: false,\n      size: \"sm\",\n      class: {\n        label: [\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_5px)]\"\n        ]\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      variant: \"underlined\",\n      isMultiline: false,\n      size: \"md\",\n      class: {\n        label: [\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_3.5px)]\"\n        ]\n      }\n    },\n    {\n      labelPlacement: \"inside\",\n      variant: \"underlined\",\n      isMultiline: false,\n      size: \"lg\",\n      class: {\n        label: [\n          \"text-medium\",\n          \"group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_4px)]\"\n        ]\n      }\n    },\n    // outside & size\n    {\n      labelPlacement: \"outside\",\n      size: \"sm\",\n      isMultiline: false,\n      class: {\n        label: [\n          \"start-2\",\n          \"text-tiny\",\n          \"group-data-[filled=true]:-translate-y-[calc(100%_+var(--heroui-font-size-tiny)/2_+_16px)]\",\n          \"group-data-[has-helper=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_26px)]\"\n        ],\n        base: \"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_8px)]\"\n      }\n    },\n    {\n      labelPlacement: \"outside\",\n      isMultiline: false,\n      size: \"md\",\n      class: {\n        label: [\n          \"start-3\",\n          \"text-small\",\n          \"group-data-[filled=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_20px)]\",\n          \"group-data-[has-helper=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_30px)]\"\n        ],\n        base: \"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_10px)]\"\n      }\n    },\n    {\n      labelPlacement: \"outside\",\n      isMultiline: false,\n      size: \"lg\",\n      class: {\n        label: [\n          \"start-3\",\n          \"text-medium\",\n          \"group-data-[filled=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_24px)]\",\n          \"group-data-[has-helper=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_34px)]\"\n        ],\n        base: \"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_12px)]\"\n      }\n    },\n    // outside-left & size\n    {\n      labelPlacement: \"outside-left\",\n      size: \"sm\",\n      class: {\n        label: \"group-data-[has-helper=true]:pt-2\"\n      }\n    },\n    {\n      labelPlacement: \"outside-left\",\n      size: \"md\",\n      class: {\n        label: \"group-data-[has-helper=true]:pt-3\"\n      }\n    },\n    {\n      labelPlacement: \"outside-left\",\n      size: \"lg\",\n      class: {\n        label: \"group-data-[has-helper=true]:pt-4\"\n      }\n    },\n    // isMultiline & labelPlacement=\"outside\"\n    {\n      labelPlacement: \"outside\",\n      isMultiline: true,\n      class: {\n        label: \"pb-1.5\"\n      }\n    },\n    // text truncate labelPlacement=[inside,outside]\n    {\n      labelPlacement: [\"inside\", \"outside\"],\n      class: {\n        label: [\"pe-2\", \"max-w-full\", \"text-ellipsis\", \"overflow-hidden\"]\n      }\n    },\n    // isClearable & labelPlacement\n    {\n      labelPlacement: [\"outside\", \"outside-left\"],\n      isClearable: true,\n      class: {\n        endContent: [\"mt-4\"],\n        clearButton: [\"group-data-[has-end-content=true]:mt-4\"]\n      }\n    },\n    {\n      isClearable: false,\n      labelPlacement: [\"outside\", \"outside-left\"],\n      class: {\n        endContent: [\"mt-4\"]\n      }\n    },\n    // isClearable + variant\n    {\n      isClearable: true,\n      variant: [\"underlined\"],\n      class: {\n        clearButton: [\"relative group-data-[has-end-content=true]:left-2\"],\n        endContent: [\"me-2\"]\n      }\n    },\n    {\n      isClearable: false,\n      variant: [\"underlined\"],\n      class: {\n        endContent: [\"me-2\"]\n      }\n    },\n    // isClearable + size\n    {\n      isClearable: true,\n      size: \"sm\",\n      class: {\n        endContent: \"ms-2\"\n      }\n    }\n  ]\n});\n\nexport {\n  select\n};\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;AAIA,2BAA2B;AAC3B,IAAI,SAAS,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACd,OAAO;QACL,MAAM;YAAC;SAAsC;QAC7C,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,0GAA0G;YAC1G;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,OAAO;YAAC;YAAuB;YAAe;YAAU;SAAa;QACrE,gBAAgB;QAChB,SAAS;QACT,gBAAgB;QAChB,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aAAa;eACV,+JAAA,CAAA,0BAAuB;SAC3B;QACD,eAAe;QACf,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA,UAAU;QACR,SAAS;YACP,MAAM;gBACJ,SAAS;oBACP;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;YACA,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,aAAa;YACf;YACA,UAAU;gBACR,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,aAAa;YACf;YACA,YAAY;gBACV,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,aAAa;YACf;QACF;QACA,OAAO;YACL,SAAS,CAAC;YACV,SAAS;gBACP,cAAc;YAChB;YACA,WAAW;gBACT,cAAc;YAChB;YACA,SAAS;gBACP,cAAc;YAChB;YACA,SAAS;gBACP,cAAc;YAChB;YACA,QAAQ;gBACN,cAAc;YAChB;QACF;QACA,MAAM;YACJ,IAAI;gBACF,OAAO;gBACP,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA,IAAI;gBACF,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA,IAAI;gBACF,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;QACA,QAAQ;YACN,MAAM;gBACJ,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;YACA,MAAM;gBACJ,SAAS;YACX;QACF;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,aAAa;YACf;YACA,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA,QAAQ;gBACN,OAAO;gBACP,SAAS;YACX;QACF;QACA,WAAW;YACT,MAAM;gBACJ,MAAM;YACR;YACA,OAAO;gBACL,MAAM;YACR;QACF;QACA,aAAa;YACX,MAAM;gBACJ,aAAa;gBACb,YAAY;YACd;QACF;QACA,YAAY;YACV,MAAM;gBACJ,MAAM;gBACN,SAAS;YACX;QACF;QACA,WAAW;YACT,MAAM;gBACJ,OAAO;gBACP,OAAO;gBACP,cAAc;YAChB;QACF;QACA,YAAY;YACV,MAAM;gBACJ,OAAO;YACT;QACF;QACA,aAAa;YACX,MAAM;gBACJ,OAAO;gBACP,SAAS;YACX;YACA,OAAO;gBACL,OAAO;YACT;QACF;QACA,kBAAkB;YAChB,MAAM;gBACJ,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL;oBACA;oBACA,0GAA0G;oBAC1G;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,cAAc;gBACd,aAAa;oBAAC;oBAAsB;iBAAgC;YACtE;QACF;QACA,6BAA6B;YAC3B,MAAM,CAAC;YACP,OAAO;gBACL,cAAc;YAChB;QACF;IACF;IACA,iBAAiB;QACf,SAAS;QACT,OAAO;QACP,MAAM;QACN,WAAW;QACX,YAAY;QACZ,aAAa;QACb,6BAA6B;IAC/B;IACA,kBAAkB;QAChB,eAAe;QACf;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,SAAS;oBAAC;oBAAkB;iBAAmC;YACjE;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,OAAO;YACT;QACF;QACA,gBAAgB;QAChB;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA,qBAAqB;QACrB,qBAAqB;QACrB;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA,mBAAmB;QACnB;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBAAC;oBAAmC;iBAAmC;gBAChF,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBAAC;oBAAqC;iBAAqC;gBACpF,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBAAC;oBAAmC;iBAAmC;gBAChF,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBAAC;oBAAmC;iBAAmC;gBAChF,OAAO;YACT;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,OAAO;gBACL,SAAS;oBAAC;oBAAkC;iBAAkC;gBAC9E,OAAO;YACT;QACF;QACA,mCAAmC;QACnC;YACE,gBAAgB;YAChB,OAAO;YACP,OAAO;gBACL,OAAO;YACT;QACF;QACA,mCAAmC;QACnC;YACE,gBAAgB;YAChB,OAAO;YACP,OAAO;gBACL,OAAO;YACT;QACF;QACA,qBAAqB;QACrB;YACE,QAAQ;YACR,MAAM;gBAAC;aAAK;YACZ,OAAO;gBACL,SAAS;YACX;QACF;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;gBACL,SAAS;YACX;QACF;QACA;YACE,QAAQ;YACR,MAAM;YACN,OAAO;gBACL,SAAS;YACX;QACF;QACA,8BAA8B;QAC9B;YACE,kBAAkB;YAClB,SAAS;gBAAC;gBAAS;aAAW;YAC9B,OAAO;gBACL,SAAS;YACX;QACF;QACA;YACE,kBAAkB;YAClB,SAAS;YACT,OAAO;gBACL,SAAS;YACX;QACF;QACA,eAAe;QACf;YACE,SAAS;gBAAC;gBAAQ;aAAQ;YAC1B,OAAO;gBACL,SAAS;oBACP,aAAa;uBACV,+JAAA,CAAA,0BAAuB;iBAC3B;YACH;QACF;QACA,sBAAsB;QACtB;YACE,WAAW;YACX,SAAS;YACT,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;iBACD;YACH;QACF;QACA;YACE,WAAW;YACX,SAAS;YACT,OAAO;gBACL,SAAS;YACX;QACF;QACA;YACE,WAAW;YACX,SAAS;YACT,OAAO;gBACL,SAAS;YACX;QACF;QACA,wBAAwB;QACxB;YACE,gBAAgB;YAChB,MAAM;YACN,OAAO;gBACL,SAAS;YACX;QACF;QACA;YACE,gBAAgB;YAChB,MAAM;YACN,OAAO;gBACL,SAAS;YACX;QACF;QACA;YACE,gBAAgB;YAChB,MAAM;YACN,OAAO;gBACL,OAAO;gBACP,SAAS;YACX;QACF;QACA;YACE,gBAAgB;YAChB,aAAa;YACb,OAAO;gBACL,MAAM;gBACN,OAAO;oBAAC;oBAAQ;oBAAQ;oBAAW;oBAAoB;iBAAmC;YAC5F;QACF;QACA,0BAA0B;QAC1B;YACE,gBAAgB;gBAAC;aAAS;YAC1B,OAAO;gBACL,OAAO;YACT;QACF;QACA,gBAAgB;QAChB;YACE,gBAAgB;YAChB,MAAM;gBAAC;gBAAM;aAAK;YAClB,OAAO;gBACL,OAAO;YACT;QACF;QACA;YACE,gBAAgB;YAChB,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;iBACD;gBACD,cAAc;YAChB;QACF;QACA;YACE,gBAAgB;YAChB,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;iBACD;gBACD,cAAc;YAChB;QACF;QACA;YACE,gBAAgB;YAChB,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;oBACA;iBACD;gBACD,cAAc;YAChB;QACF;QACA,oCAAoC;QACpC;YACE,gBAAgB;YAChB,SAAS;gBAAC;gBAAS;aAAW;YAC9B,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;iBACD;YACH;QACF;QACA;YACE,gBAAgB;YAChB,SAAS;gBAAC;gBAAS;aAAW;YAC9B,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;iBACD;YACH;QACF;QACA;YACE,gBAAgB;YAChB,SAAS;gBAAC;gBAAS;aAAW;YAC9B,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;oBACA;iBACD;YACH;QACF;QACA,6BAA6B;QAC7B;YACE,gBAAgB;YAChB,SAAS;YACT,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;iBACD;YACH;QACF;QACA;YACE,gBAAgB;YAChB,SAAS;YACT,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;iBACD;YACH;QACF;QACA;YACE,gBAAgB;YAChB,SAAS;YACT,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;oBACA;iBACD;YACH;QACF;QACA,iBAAiB;QACjB;YACE,gBAAgB;YAChB,MAAM;YACN,aAAa;YACb,OAAO;gBACL,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,MAAM;YACR;QACF;QACA;YACE,gBAAgB;YAChB,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,MAAM;YACR;QACF;QACA;YACE,gBAAgB;YAChB,aAAa;YACb,MAAM;YACN,OAAO;gBACL,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,MAAM;YACR;QACF;QACA,sBAAsB;QACtB;YACE,gBAAgB;YAChB,MAAM;YACN,OAAO;gBACL,OAAO;YACT;QACF;QACA;YACE,gBAAgB;YAChB,MAAM;YACN,OAAO;gBACL,OAAO;YACT;QACF;QACA;YACE,gBAAgB;YAChB,MAAM;YACN,OAAO;gBACL,OAAO;YACT;QACF;QACA,yCAAyC;QACzC;YACE,gBAAgB;YAChB,aAAa;YACb,OAAO;gBACL,OAAO;YACT;QACF;QACA,gDAAgD;QAChD;YACE,gBAAgB;gBAAC;gBAAU;aAAU;YACrC,OAAO;gBACL,OAAO;oBAAC;oBAAQ;oBAAc;oBAAiB;iBAAkB;YACnE;QACF;QACA,+BAA+B;QAC/B;YACE,gBAAgB;gBAAC;gBAAW;aAAe;YAC3C,aAAa;YACb,OAAO;gBACL,YAAY;oBAAC;iBAAO;gBACpB,aAAa;oBAAC;iBAAyC;YACzD;QACF;QACA;YACE,aAAa;YACb,gBAAgB;gBAAC;gBAAW;aAAe;YAC3C,OAAO;gBACL,YAAY;oBAAC;iBAAO;YACtB;QACF;QACA,wBAAwB;QACxB;YACE,aAAa;YACb,SAAS;gBAAC;aAAa;YACvB,OAAO;gBACL,aAAa;oBAAC;iBAAoD;gBAClE,YAAY;oBAAC;iBAAO;YACtB;QACF;QACA;YACE,aAAa;YACb,SAAS;gBAAC;aAAa;YACvB,OAAO;gBACL,YAAY;oBAAC;iBAAO;YACtB;QACF;QACA,qBAAqB;QACrB;YACE,aAAa;YACb,MAAM;YACN,OAAO;gBACL,YAAY;YACd;QACF;KACD;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-NY7ORCUI.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\n\n// src/components/scroll-shadow.ts\nvar verticalShadow = [\n  \"data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]\",\n  \"data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]\",\n  \"data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]\"\n];\nvar horizontalShadow = [\n  \"data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]\",\n  \"data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]\",\n  \"data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]\"\n];\nvar scrollShadow = tv({\n  base: [],\n  variants: {\n    orientation: {\n      vertical: [\"overflow-y-auto\", ...verticalShadow],\n      horizontal: [\"overflow-x-auto\", ...horizontalShadow]\n    },\n    hideScrollBar: {\n      true: \"scrollbar-hide\",\n      false: \"\"\n    }\n  },\n  defaultVariants: {\n    orientation: \"vertical\",\n    hideScrollBar: false\n  }\n});\n\nexport {\n  scrollShadow\n};\n"], "names": [], "mappings": ";;;AAAA;;AAIA,kCAAkC;AAClC,IAAI,iBAAiB;IACnB;IACA;IACA;CACD;AACD,IAAI,mBAAmB;IACrB;IACA;IACA;CACD;AACD,IAAI,eAAe,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACpB,MAAM,EAAE;IACR,UAAU;QACR,aAAa;YACX,UAAU;gBAAC;mBAAsB;aAAe;YAChD,YAAY;gBAAC;mBAAsB;aAAiB;QACtD;QACA,eAAe;YACb,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,aAAa;QACb,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-BSQAJJ3Q.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\nimport {\n  groupDataFocusVisibleClasses,\n  hiddenInputClasses\n} from \"./chunk-JGY6VQQQ.mjs\";\n\n// src/components/radio.ts\nvar radio = tv({\n  slots: {\n    base: \"group relative max-w-fit inline-flex items-center justify-start cursor-pointer tap-highlight-transparent p-2 -m-2 select-none\",\n    wrapper: [\n      \"relative\",\n      \"inline-flex\",\n      \"items-center\",\n      \"justify-center\",\n      \"shrink-0\",\n      \"overflow-hidden\",\n      \"border-solid\",\n      \"border-medium\",\n      \"box-border\",\n      \"border-default\",\n      \"rounded-full\",\n      \"group-data-[hover-unselected=true]:bg-default-100\",\n      // focus ring\n      ...groupDataFocusVisibleClasses\n    ],\n    hiddenInput: hiddenInputClasses,\n    labelWrapper: \"flex flex-col ml-1\",\n    control: [\n      \"z-10\",\n      \"w-2\",\n      \"h-2\",\n      \"opacity-0\",\n      \"scale-0\",\n      \"origin-center\",\n      \"rounded-full\",\n      \"group-data-[selected=true]:opacity-100\",\n      \"group-data-[selected=true]:scale-100\"\n    ],\n    label: \"relative text-foreground select-none\",\n    description: \"relative text-foreground-400\"\n  },\n  variants: {\n    color: {\n      default: {\n        control: \"bg-default-500 text-default-foreground\",\n        wrapper: \"group-data-[selected=true]:border-default-500\"\n      },\n      primary: {\n        control: \"bg-primary text-primary-foreground\",\n        wrapper: \"group-data-[selected=true]:border-primary\"\n      },\n      secondary: {\n        control: \"bg-secondary text-secondary-foreground\",\n        wrapper: \"group-data-[selected=true]:border-secondary\"\n      },\n      success: {\n        control: \"bg-success text-success-foreground\",\n        wrapper: \"group-data-[selected=true]:border-success\"\n      },\n      warning: {\n        control: \"bg-warning text-warning-foreground\",\n        wrapper: \"group-data-[selected=true]:border-warning\"\n      },\n      danger: {\n        control: \"bg-danger text-danger-foreground\",\n        wrapper: \"group-data-[selected=true]:border-danger\"\n      }\n    },\n    size: {\n      sm: {\n        wrapper: \"w-4 h-4\",\n        control: \"w-1.5 h-1.5\",\n        labelWrapper: \"ml-1\",\n        label: \"text-small\",\n        description: \"text-tiny\"\n      },\n      md: {\n        wrapper: \"w-5 h-5\",\n        control: \"w-2 h-2\",\n        labelWrapper: \"ms-2\",\n        label: \"text-medium\",\n        description: \"text-small\"\n      },\n      lg: {\n        wrapper: \"w-6 h-6\",\n        control: \"w-2.5 h-2.5\",\n        labelWrapper: \"ms-2\",\n        label: \"text-large\",\n        description: \"text-medium\"\n      }\n    },\n    isDisabled: {\n      true: {\n        base: \"opacity-disabled pointer-events-none\"\n      }\n    },\n    isInvalid: {\n      true: {\n        control: \"bg-danger text-danger-foreground\",\n        wrapper: \"border-danger group-data-[selected=true]:border-danger\",\n        label: \"text-danger\",\n        description: \"text-danger-300\"\n      }\n    },\n    disableAnimation: {\n      true: {},\n      false: {\n        wrapper: [\n          \"group-data-[pressed=true]:scale-95\",\n          \"transition-transform-colors\",\n          \"motion-reduce:transition-none\"\n        ],\n        control: \"transition-transform-opacity motion-reduce:transition-none\",\n        label: \"transition-colors motion-reduce:transition-none\",\n        description: \"transition-colors motion-reduce:transition-none\"\n      }\n    }\n  },\n  defaultVariants: {\n    color: \"primary\",\n    size: \"md\",\n    isDisabled: false,\n    isInvalid: false\n  }\n});\nvar radioGroup = tv({\n  slots: {\n    base: \"relative flex flex-col gap-2\",\n    label: \"relative text-foreground-500\",\n    wrapper: \"flex flex-col flex-wrap gap-2 data-[orientation=horizontal]:flex-row\",\n    description: \"text-tiny text-foreground-400\",\n    errorMessage: \"text-tiny text-danger\"\n  },\n  variants: {\n    isRequired: {\n      true: {\n        label: \"after:content-['*'] after:text-danger after:ml-0.5\"\n      }\n    },\n    isInvalid: {\n      true: {\n        description: \"text-danger\"\n      }\n    },\n    disableAnimation: {\n      true: {},\n      false: {\n        description: \"transition-colors !duration-150 motion-reduce:transition-none\"\n      }\n    }\n  },\n  defaultVariants: {\n    isInvalid: false,\n    isRequired: false\n  }\n});\n\nexport {\n  radio,\n  radioGroup\n};\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;AAKA,0BAA0B;AAC1B,IAAI,QAAQ,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACb,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aAAa;eACV,+JAAA,CAAA,+BAA4B;SAChC;QACD,aAAa,+JAAA,CAAA,qBAAkB;QAC/B,cAAc;QACd,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,aAAa;IACf;IACA,UAAU;QACR,OAAO;YACL,SAAS;gBACP,SAAS;gBACT,SAAS;YACX;YACA,SAAS;gBACP,SAAS;gBACT,SAAS;YACX;YACA,WAAW;gBACT,SAAS;gBACT,SAAS;YACX;YACA,SAAS;gBACP,SAAS;gBACT,SAAS;YACX;YACA,SAAS;gBACP,SAAS;gBACT,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;gBACT,SAAS;YACX;QACF;QACA,MAAM;YACJ,IAAI;gBACF,SAAS;gBACT,SAAS;gBACT,cAAc;gBACd,OAAO;gBACP,aAAa;YACf;YACA,IAAI;gBACF,SAAS;gBACT,SAAS;gBACT,cAAc;gBACd,OAAO;gBACP,aAAa;YACf;YACA,IAAI;gBACF,SAAS;gBACT,SAAS;gBACT,cAAc;gBACd,OAAO;gBACP,aAAa;YACf;QACF;QACA,YAAY;YACV,MAAM;gBACJ,MAAM;YACR;QACF;QACA,WAAW;YACT,MAAM;gBACJ,SAAS;gBACT,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;QACA,kBAAkB;YAChB,MAAM,CAAC;YACP,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;iBACD;gBACD,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IACA,iBAAiB;QACf,OAAO;QACP,MAAM;QACN,YAAY;QACZ,WAAW;IACb;AACF;AACA,IAAI,aAAa,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IAClB,OAAO;QACL,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;IAChB;IACA,UAAU;QACR,YAAY;YACV,MAAM;gBACJ,OAAO;YACT;QACF;QACA,WAAW;YACT,MAAM;gBACJ,aAAa;YACf;QACF;QACA,kBAAkB;YAChB,MAAM,CAAC;YACP,OAAO;gBACL,aAAa;YACf;QACF;IACF;IACA,iBAAiB;QACf,WAAW;QACX,YAAY;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-CR75Q2EZ.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\nimport {\n  groupDataFocusVisibleClasses,\n  hiddenInputClasses\n} from \"./chunk-JGY6VQQQ.mjs\";\n\n// src/components/checkbox.ts\nvar checkbox = tv({\n  slots: {\n    base: \"group relative max-w-fit inline-flex items-center justify-start cursor-pointer tap-highlight-transparent p-2 -m-2 select-none\",\n    wrapper: [\n      \"relative\",\n      \"inline-flex\",\n      \"items-center\",\n      \"justify-center\",\n      \"shrink-0\",\n      \"overflow-hidden\",\n      // before\n      \"before:content-['']\",\n      \"before:absolute\",\n      \"before:inset-0\",\n      \"before:border-solid\",\n      \"before:border-2\",\n      \"before:box-border\",\n      \"before:border-default\",\n      // after\n      \"after:content-['']\",\n      \"after:absolute\",\n      \"after:inset-0\",\n      \"after:scale-50\",\n      \"after:opacity-0\",\n      \"after:origin-center\",\n      \"group-data-[selected=true]:after:scale-100\",\n      \"group-data-[selected=true]:after:opacity-100\",\n      // hover\n      \"group-data-[hover=true]:before:bg-default-100\",\n      // focus ring\n      ...groupDataFocusVisibleClasses\n    ],\n    hiddenInput: hiddenInputClasses,\n    icon: \"z-10 w-4 h-3 opacity-0 group-data-[selected=true]:opacity-100 pointer-events-none\",\n    label: \"relative text-foreground select-none\"\n  },\n  variants: {\n    color: {\n      default: {\n        wrapper: \"after:bg-default after:text-default-foreground text-default-foreground\"\n      },\n      primary: {\n        wrapper: \"after:bg-primary after:text-primary-foreground text-primary-foreground\"\n      },\n      secondary: {\n        wrapper: \"after:bg-secondary after:text-secondary-foreground text-secondary-foreground\"\n      },\n      success: {\n        wrapper: \"after:bg-success after:text-success-foreground text-success-foreground\"\n      },\n      warning: {\n        wrapper: \"after:bg-warning after:text-warning-foreground text-warning-foreground\"\n      },\n      danger: {\n        wrapper: \"after:bg-danger after:text-danger-foreground text-danger-foreground\"\n      }\n    },\n    size: {\n      sm: {\n        wrapper: [\n          \"w-4 h-4 me-2\",\n          \"rounded-[calc(var(--heroui-radius-medium)*0.5)]\",\n          \"before:rounded-[calc(var(--heroui-radius-medium)*0.5)]\",\n          \"after:rounded-[calc(var(--heroui-radius-medium)*0.5)]\"\n        ],\n        label: \"text-small\",\n        icon: \"w-3 h-2\"\n      },\n      md: {\n        wrapper: [\n          \"w-5 h-5 me-2\",\n          \"rounded-[calc(var(--heroui-radius-medium)*0.6)]\",\n          \"before:rounded-[calc(var(--heroui-radius-medium)*0.6)]\",\n          \"after:rounded-[calc(var(--heroui-radius-medium)*0.6)]\"\n        ],\n        label: \"text-medium\",\n        icon: \"w-4 h-3\"\n      },\n      lg: {\n        wrapper: [\n          \"w-6 h-6 me-2\",\n          \"rounded-[calc(var(--heroui-radius-medium)*0.7)]\",\n          \"before:rounded-[calc(var(--heroui-radius-medium)*0.7)]\",\n          \"after:rounded-[calc(var(--heroui-radius-medium)*0.7)]\"\n        ],\n        label: \"text-large\",\n        icon: \"w-5 h-4\"\n      }\n    },\n    radius: {\n      none: {\n        wrapper: \"rounded-none before:rounded-none after:rounded-none\"\n      },\n      sm: {\n        wrapper: [\n          \"rounded-[calc(var(--heroui-radius-medium)*0.5)]\",\n          \"before:rounded-[calc(var(--heroui-radius-medium)*0.5)]\",\n          \"after:rounded-[calc(var(--heroui-radius-medium)*0.5)]\"\n        ]\n      },\n      md: {\n        wrapper: [\n          \"rounded-[calc(var(--heroui-radius-medium)*0.6)]\",\n          \"before:rounded-[calc(var(--heroui-radius-medium)*0.6)]\",\n          \"after:rounded-[calc(var(--heroui-radius-medium)*0.6)]\"\n        ]\n      },\n      lg: {\n        wrapper: [\n          \"rounded-[calc(var(--heroui-radius-medium)*0.7)]\",\n          \"before:rounded-[calc(var(--heroui-radius-medium)*0.7)]\",\n          \"after:rounded-[calc(var(--heroui-radius-medium)*0.7)]\"\n        ]\n      },\n      full: {\n        wrapper: \"rounded-full before:rounded-full after:rounded-full\"\n      }\n    },\n    lineThrough: {\n      true: {\n        label: [\n          \"inline-flex\",\n          \"items-center\",\n          \"justify-center\",\n          \"before:content-['']\",\n          \"before:absolute\",\n          \"before:bg-foreground\",\n          \"before:w-0\",\n          \"before:h-0.5\",\n          \"group-data-[selected=true]:opacity-60\",\n          \"group-data-[selected=true]:before:w-full\"\n        ]\n      }\n    },\n    isDisabled: {\n      true: {\n        base: \"opacity-disabled pointer-events-none\"\n      }\n    },\n    isInvalid: {\n      true: {\n        wrapper: \"before:border-danger\",\n        label: \"text-danger\"\n      }\n    },\n    disableAnimation: {\n      true: {\n        wrapper: \"transition-none\",\n        icon: \"transition-none\",\n        label: \"transition-none\"\n      },\n      false: {\n        wrapper: [\n          \"before:transition-colors\",\n          \"group-data-[pressed=true]:scale-95\",\n          \"transition-transform\",\n          \"after:transition-transform-opacity\",\n          \"after:!ease-linear\",\n          \"after:!duration-200\",\n          \"motion-reduce:transition-none\"\n        ],\n        icon: \"transition-opacity motion-reduce:transition-none\",\n        label: \"transition-colors-opacity before:transition-width motion-reduce:transition-none\"\n      }\n    }\n  },\n  defaultVariants: {\n    color: \"primary\",\n    size: \"md\",\n    isDisabled: false,\n    lineThrough: false\n  }\n});\nvar checkboxGroup = tv({\n  slots: {\n    base: \"relative flex flex-col gap-2\",\n    label: \"relative text-medium text-foreground-500\",\n    wrapper: \"flex flex-col flex-wrap gap-2 data-[orientation=horizontal]:flex-row\",\n    description: \"text-small text-foreground-400\",\n    errorMessage: \"text-small text-danger\"\n  },\n  variants: {\n    isRequired: {\n      true: {\n        label: \"after:content-['*'] after:text-danger after:ml-0.5\"\n      }\n    },\n    isInvalid: {\n      true: {\n        description: \"text-danger\"\n      }\n    },\n    disableAnimation: {\n      true: {},\n      false: {\n        description: \"transition-colors !duration-150 motion-reduce:transition-none\"\n      }\n    }\n  },\n  defaultVariants: {\n    isInvalid: false,\n    isRequired: false\n  }\n});\n\nexport {\n  checkbox,\n  checkboxGroup\n};\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;AAKA,6BAA6B;AAC7B,IAAI,WAAW,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IAChB,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA,SAAS;YACT;YACA;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR;YACA,aAAa;eACV,+JAAA,CAAA,+BAA4B;SAChC;QACD,aAAa,+JAAA,CAAA,qBAAkB;QAC/B,MAAM;QACN,OAAO;IACT;IACA,UAAU;QACR,OAAO;YACL,SAAS;gBACP,SAAS;YACX;YACA,SAAS;gBACP,SAAS;YACX;YACA,WAAW;gBACT,SAAS;YACX;YACA,SAAS;gBACP,SAAS;YACX;YACA,SAAS;gBACP,SAAS;YACX;YACA,QAAQ;gBACN,SAAS;YACX;QACF;QACA,MAAM;YACJ,IAAI;gBACF,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,MAAM;YACR;YACA,IAAI;gBACF,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,MAAM;YACR;YACA,IAAI;gBACF,SAAS;oBACP;oBACA;oBACA;oBACA;iBACD;gBACD,OAAO;gBACP,MAAM;YACR;QACF;QACA,QAAQ;YACN,MAAM;gBACJ,SAAS;YACX;YACA,IAAI;gBACF,SAAS;oBACP;oBACA;oBACA;iBACD;YACH;YACA,IAAI;gBACF,SAAS;oBACP;oBACA;oBACA;iBACD;YACH;YACA,IAAI;gBACF,SAAS;oBACP;oBACA;oBACA;iBACD;YACH;YACA,MAAM;gBACJ,SAAS;YACX;QACF;QACA,aAAa;YACX,MAAM;gBACJ,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;QACF;QACA,YAAY;YACV,MAAM;gBACJ,MAAM;YACR;QACF;QACA,WAAW;YACT,MAAM;gBACJ,SAAS;gBACT,OAAO;YACT;QACF;QACA,kBAAkB;YAChB,MAAM;gBACJ,SAAS;gBACT,MAAM;gBACN,OAAO;YACT;YACA,OAAO;gBACL,SAAS;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,MAAM;gBACN,OAAO;YACT;QACF;IACF;IACA,iBAAiB;QACf,OAAO;QACP,MAAM;QACN,YAAY;QACZ,aAAa;IACf;AACF;AACA,IAAI,gBAAgB,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACrB,OAAO;QACL,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,cAAc;IAChB;IACA,UAAU;QACR,YAAY;YACV,MAAM;gBACJ,OAAO;YACT;QACF;QACA,WAAW;YACT,MAAM;gBACJ,aAAa;YACf;QACF;QACA,kBAAkB;YAChB,MAAM,CAAC;YACP,OAAO;gBACL,aAAa;YACf;QACF;IACF;IACA,iBAAiB;QACf,WAAW;QACX,YAAY;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1637, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-SZIFQ3NN.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\nimport {\n  dataFocusVisibleClasses\n} from \"./chunk-JGY6VQQQ.mjs\";\n\n// src/components/table.ts\nvar table = tv({\n  slots: {\n    base: \"flex flex-col relative gap-4\",\n    wrapper: [\n      \"p-4\",\n      \"z-0\",\n      \"flex\",\n      \"flex-col\",\n      \"relative\",\n      \"justify-between\",\n      \"gap-4\",\n      \"shadow-small\",\n      \"bg-content1\",\n      \"overflow-auto\"\n    ],\n    table: \"min-w-full h-auto\",\n    thead: \"[&>tr]:first:rounded-lg\",\n    tbody: \"after:block\",\n    tr: [\"group/tr\", \"outline-solid outline-transparent\", ...dataFocusVisibleClasses],\n    th: [\n      \"group/th\",\n      \"px-3\",\n      \"h-10\",\n      \"text-start\",\n      \"align-middle\",\n      \"bg-default-100\",\n      \"whitespace-nowrap\",\n      \"text-foreground-500\",\n      \"text-tiny\",\n      \"font-semibold\",\n      \"first:rounded-s-lg\",\n      \"last:rounded-e-lg\",\n      \"outline-solid outline-transparent\",\n      \"data-[sortable=true]:cursor-pointer\",\n      \"data-[hover=true]:text-foreground-400\",\n      ...dataFocusVisibleClasses\n    ],\n    td: [\n      \"py-2\",\n      \"px-3\",\n      \"relative\",\n      \"align-middle\",\n      \"whitespace-normal\",\n      \"text-small\",\n      \"font-normal\",\n      \"outline-solid outline-transparent\",\n      \"[&>*]:z-1\",\n      \"[&>*]:relative\",\n      ...dataFocusVisibleClasses,\n      // before content for selection\n      \"before:pointer-events-none\",\n      \"before:content-['']\",\n      \"before:absolute\",\n      \"before:z-0\",\n      \"before:inset-0\",\n      \"before:opacity-0\",\n      \"data-[selected=true]:before:opacity-100\",\n      // disabled\n      \"group-data-[disabled=true]/tr:text-foreground-300\",\n      \"group-data-[disabled=true]/tr:cursor-not-allowed\"\n    ],\n    tfoot: \"\",\n    sortIcon: [\n      \"ms-2\",\n      \"mb-px\",\n      \"opacity-0\",\n      \"text-inherit\",\n      \"inline-block\",\n      \"transition-transform-opacity\",\n      \"data-[visible=true]:opacity-100\",\n      \"group-data-[hover=true]/th:opacity-100\",\n      \"data-[direction=ascending]:rotate-180\"\n    ],\n    emptyWrapper: \"text-foreground-400 align-middle text-center h-40\",\n    loadingWrapper: \"absolute inset-0 flex items-center justify-center\"\n  },\n  variants: {\n    color: {\n      default: {\n        td: \"before:bg-default/60 data-[selected=true]:text-default-foreground\"\n      },\n      primary: {\n        td: \"before:bg-primary/20 data-[selected=true]:text-primary\"\n      },\n      secondary: {\n        td: \"before:bg-secondary/20 data-[selected=true]:text-secondary\"\n      },\n      success: {\n        td: \"before:bg-success/20 data-[selected=true]:text-success-600 dark:data-[selected=true]:text-success\"\n      },\n      warning: {\n        td: \"before:bg-warning/20 data-[selected=true]:text-warning-600 dark:data-[selected=true]:text-warning\"\n      },\n      danger: {\n        td: \"before:bg-danger/20 data-[selected=true]:text-danger dark:data-[selected=true]:text-danger-500\"\n      }\n    },\n    layout: {\n      auto: {\n        table: \"table-auto\"\n      },\n      fixed: {\n        table: \"table-fixed\"\n      }\n    },\n    shadow: {\n      none: {\n        wrapper: \"shadow-none\"\n      },\n      sm: {\n        wrapper: \"shadow-small\"\n      },\n      md: {\n        wrapper: \"shadow-medium\"\n      },\n      lg: {\n        wrapper: \"shadow-large\"\n      }\n    },\n    hideHeader: {\n      true: {\n        thead: \"hidden\"\n      }\n    },\n    isStriped: {\n      true: {\n        td: [\n          \"group-data-[odd=true]/tr:before:bg-default-100\",\n          \"group-data-[odd=true]/tr:before:opacity-100\",\n          \"group-data-[odd=true]/tr:before:-z-10\"\n        ]\n      }\n    },\n    isCompact: {\n      true: {\n        td: \"py-1\"\n      },\n      false: {}\n    },\n    isHeaderSticky: {\n      true: {\n        thead: \"sticky top-0 z-20 [&>tr]:first:shadow-small\"\n      }\n    },\n    isSelectable: {\n      true: {\n        tr: \"cursor-default\",\n        td: [\n          \"group-aria-[selected=false]/tr:group-data-[hover=true]/tr:before:bg-default-100\",\n          \"group-aria-[selected=false]/tr:group-data-[hover=true]/tr:before:opacity-70\"\n        ]\n      }\n    },\n    isMultiSelectable: {\n      true: {\n        td: [\n          // first\n          \"group-data-[first=true]/tr:first:before:rounded-ss-lg\",\n          \"group-data-[first=true]/tr:last:before:rounded-se-lg\",\n          // middle\n          \"group-data-[middle=true]/tr:before:rounded-none\",\n          // last\n          \"group-data-[last=true]/tr:first:before:rounded-es-lg\",\n          \"group-data-[last=true]/tr:last:before:rounded-ee-lg\"\n        ]\n      },\n      false: {\n        td: [\"first:before:rounded-s-lg\", \"last:before:rounded-e-lg\"]\n      }\n    },\n    radius: {\n      none: {\n        wrapper: \"rounded-none\",\n        th: [\n          \"first:rounded-s-none\",\n          \"first:before:rounded-s-none\",\n          \"last:rounded-e-none\",\n          \"last:before:rounded-e-none\"\n        ],\n        td: [\n          \"first:before:rounded-s-none\",\n          \"last:before:rounded-e-none\",\n          \"group-data-[first=true]/tr:first:before:rounded-ss-none\",\n          \"group-data-[first=true]/tr:last:before:rounded-se-none\",\n          \"group-data-[last=true]/tr:first:before:rounded-es-none\",\n          \"group-data-[last=true]/tr:last:before:rounded-ee-none\"\n        ]\n      },\n      sm: {\n        wrapper: \"rounded-small\"\n      },\n      md: {\n        wrapper: \"rounded-medium\"\n      },\n      lg: {\n        wrapper: \"rounded-large\"\n      }\n    },\n    fullWidth: {\n      true: {\n        base: \"w-full\",\n        wrapper: \"w-full\",\n        table: \"w-full\"\n      }\n    },\n    align: {\n      start: {\n        th: \"text-start\",\n        td: \"text-start\"\n      },\n      center: {\n        th: \"text-center\",\n        td: \"text-center\"\n      },\n      end: {\n        th: \"text-end\",\n        td: \"text-end\"\n      }\n    }\n  },\n  defaultVariants: {\n    layout: \"auto\",\n    shadow: \"sm\",\n    radius: \"lg\",\n    color: \"default\",\n    isCompact: false,\n    hideHeader: false,\n    isStriped: false,\n    fullWidth: true,\n    align: \"start\"\n  },\n  compoundVariants: [\n    {\n      isStriped: true,\n      color: \"default\",\n      class: {\n        td: \"group-data-[odd=true]/tr:data-[selected=true]/tr:before:bg-default/60\"\n      }\n    },\n    {\n      isStriped: true,\n      color: \"primary\",\n      class: {\n        td: \"group-data-[odd=true]/tr:data-[selected=true]/tr:before:bg-primary/20\"\n      }\n    },\n    {\n      isStriped: true,\n      color: \"secondary\",\n      class: {\n        td: \"group-data-[odd=true]/tr:data-[selected=true]/tr:before:bg-secondary/20\"\n      }\n    },\n    {\n      isStriped: true,\n      color: \"success\",\n      class: {\n        td: \"group-data-[odd=true]/tr:data-[selected=true]/tr:before:bg-success/20\"\n      }\n    },\n    {\n      isStriped: true,\n      color: \"warning\",\n      class: {\n        td: \"group-data-[odd=true]/tr:data-[selected=true]/tr:before:bg-warning/20\"\n      }\n    },\n    {\n      isStriped: true,\n      color: \"danger\",\n      class: {\n        td: \"group-data-[odd=true]/tr:data-[selected=true]/tr:before:bg-danger/20\"\n      }\n    }\n  ]\n});\n\nexport {\n  table\n};\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;AAIA,0BAA0B;AAC1B,IAAI,QAAQ,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACb,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,OAAO;QACP,OAAO;QACP,IAAI;YAAC;YAAY;eAAwC,+JAAA,CAAA,0BAAuB;SAAC;QACjF,IAAI;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;eACG,+JAAA,CAAA,0BAAuB;SAC3B;QACD,IAAI;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;eACG,+JAAA,CAAA,0BAAuB;YAC1B,+BAA+B;YAC/B;YACA;YACA;YACA;YACA;YACA;YACA;YACA,WAAW;YACX;YACA;SACD;QACD,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;QACd,gBAAgB;IAClB;IACA,UAAU;QACR,OAAO;YACL,SAAS;gBACP,IAAI;YACN;YACA,SAAS;gBACP,IAAI;YACN;YACA,WAAW;gBACT,IAAI;YACN;YACA,SAAS;gBACP,IAAI;YACN;YACA,SAAS;gBACP,IAAI;YACN;YACA,QAAQ;gBACN,IAAI;YACN;QACF;QACA,QAAQ;YACN,MAAM;gBACJ,OAAO;YACT;YACA,OAAO;gBACL,OAAO;YACT;QACF;QACA,QAAQ;YACN,MAAM;gBACJ,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;QACF;QACA,YAAY;YACV,MAAM;gBACJ,OAAO;YACT;QACF;QACA,WAAW;YACT,MAAM;gBACJ,IAAI;oBACF;oBACA;oBACA;iBACD;YACH;QACF;QACA,WAAW;YACT,MAAM;gBACJ,IAAI;YACN;YACA,OAAO,CAAC;QACV;QACA,gBAAgB;YACd,MAAM;gBACJ,OAAO;YACT;QACF;QACA,cAAc;YACZ,MAAM;gBACJ,IAAI;gBACJ,IAAI;oBACF;oBACA;iBACD;YACH;QACF;QACA,mBAAmB;YACjB,MAAM;gBACJ,IAAI;oBACF,QAAQ;oBACR;oBACA;oBACA,SAAS;oBACT;oBACA,OAAO;oBACP;oBACA;iBACD;YACH;YACA,OAAO;gBACL,IAAI;oBAAC;oBAA6B;iBAA2B;YAC/D;QACF;QACA,QAAQ;YACN,MAAM;gBACJ,SAAS;gBACT,IAAI;oBACF;oBACA;oBACA;oBACA;iBACD;gBACD,IAAI;oBACF;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA,IAAI;gBACF,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;YACA,IAAI;gBACF,SAAS;YACX;QACF;QACA,WAAW;YACT,MAAM;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,OAAO;YACL,OAAO;gBACL,IAAI;gBACJ,IAAI;YACN;YACA,QAAQ;gBACN,IAAI;gBACJ,IAAI;YACN;YACA,KAAK;gBACH,IAAI;gBACJ,IAAI;YACN;QACF;IACF;IACA,iBAAiB;QACf,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,WAAW;QACX,WAAW;QACX,OAAO;IACT;IACA,kBAAkB;QAChB;YACE,WAAW;YACX,OAAO;YACP,OAAO;gBACL,IAAI;YACN;QACF;QACA;YACE,WAAW;YACX,OAAO;YACP,OAAO;gBACL,IAAI;YACN;QACF;QACA;YACE,WAAW;YACX,OAAO;YACP,OAAO;gBACL,IAAI;YACN;QACF;QACA;YACE,WAAW;YACX,OAAO;YACP,OAAO;gBACL,IAAI;YACN;QACF;QACA;YACE,WAAW;YACX,OAAO;YACP,OAAO;gBACL,IAAI;YACN;QACF;QACA;YACE,WAAW;YACX,OAAO;YACP,OAAO;gBACL,IAAI;YACN;QACF;KACD;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/theme/dist/chunk-SBOMX3YT.mjs"], "sourcesContent": ["import {\n  tv\n} from \"./chunk-TX3FPB7D.mjs\";\n\n// src/components/spacer.ts\nvar spacer = tv({\n  base: \"w-px h-px inline-block\",\n  variants: {\n    isInline: {\n      true: \"inline-block\",\n      false: \"block\"\n    }\n  },\n  defaultVariants: {\n    isInline: false\n  }\n});\n\nexport {\n  spacer\n};\n"], "names": [], "mappings": ";;;AAAA;;AAIA,2BAA2B;AAC3B,IAAI,SAAS,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE;IACd,MAAM;IACN,UAAU;QACR,UAAU;YACR,MAAM;YACN,OAAO;QACT;IACF;IACA,iBAAiB;QACf,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-CWPHHQ5O.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-modal.ts\nimport { useAriaModalOverlay } from \"@heroui/use-aria-modal-overlay\";\nimport { useCallback, useId, useRef, useState, useMemo } from \"react\";\nimport { modal } from \"@heroui/theme\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { useAriaButton } from \"@heroui/use-aria-button\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { clsx, dataAttr, objectToDeps, mergeRefs, mergeProps } from \"@heroui/shared-utils\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { useOverlayTriggerState } from \"@react-stately/overlays\";\nfunction useModal(originalProps) {\n  var _a, _b, _c;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, modal.variantKeys);\n  const {\n    ref,\n    as,\n    className,\n    classNames,\n    isOpen,\n    defaultOpen,\n    onOpenChange,\n    motionProps,\n    closeButton,\n    isDismissable = true,\n    hideCloseButton = false,\n    shouldBlockScroll = true,\n    portalContainer,\n    isKeyboardDismissDisabled = false,\n    onClose,\n    ...otherProps\n  } = props;\n  const Component = as || \"section\";\n  const domRef = useDOMRef(ref);\n  const closeButtonRef = useRef(null);\n  const [headerMounted, setHeaderMounted] = useState(false);\n  const [bodyMounted, setBodyMounted] = useState(false);\n  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;\n  const dialogId = useId();\n  const headerId = useId();\n  const bodyId = useId();\n  const state = useOverlayTriggerState({\n    isOpen,\n    defaultOpen,\n    onOpenChange: (isOpen2) => {\n      onOpenChange == null ? void 0 : onOpenChange(isOpen2);\n      if (!isOpen2) {\n        onClose == null ? void 0 : onClose();\n      }\n    }\n  });\n  const { modalProps, underlayProps } = useAriaModalOverlay(\n    {\n      isDismissable,\n      shouldBlockScroll,\n      isKeyboardDismissDisabled\n    },\n    state,\n    domRef\n  );\n  const { buttonProps: closeButtonProps } = useAriaButton({ onPress: state.close }, closeButtonRef);\n  const { isFocusVisible: isCloseButtonFocusVisible, focusProps: closeButtonFocusProps } = useFocusRing();\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const slots = useMemo(\n    () => modal({\n      ...variantProps,\n      disableAnimation\n    }),\n    [objectToDeps(variantProps), disableAnimation]\n  );\n  const getDialogProps = (props2 = {}, ref2 = null) => {\n    var _a2;\n    return {\n      ref: mergeRefs(ref2, domRef),\n      ...mergeProps(modalProps, otherProps, props2),\n      className: slots.base({ class: clsx(baseStyles, props2.className) }),\n      id: dialogId,\n      \"data-open\": dataAttr(state.isOpen),\n      \"data-dismissable\": dataAttr(isDismissable),\n      \"aria-modal\": dataAttr(true),\n      \"data-placement\": (_a2 = originalProps == null ? void 0 : originalProps.placement) != null ? _a2 : \"right\",\n      \"aria-labelledby\": headerMounted ? headerId : void 0,\n      \"aria-describedby\": bodyMounted ? bodyId : void 0\n    };\n  };\n  const getBackdropProps = useCallback(\n    (props2 = {}) => ({\n      className: slots.backdrop({ class: classNames == null ? void 0 : classNames.backdrop }),\n      ...underlayProps,\n      ...props2\n    }),\n    [slots, classNames, underlayProps]\n  );\n  const getCloseButtonProps = () => {\n    return {\n      role: \"button\",\n      tabIndex: 0,\n      \"aria-label\": \"Close\",\n      \"data-focus-visible\": dataAttr(isCloseButtonFocusVisible),\n      className: slots.closeButton({ class: classNames == null ? void 0 : classNames.closeButton }),\n      ...mergeProps(closeButtonProps, closeButtonFocusProps)\n    };\n  };\n  return {\n    Component,\n    slots,\n    domRef,\n    headerId,\n    bodyId,\n    motionProps,\n    classNames,\n    isDismissable,\n    closeButton,\n    hideCloseButton,\n    portalContainer,\n    shouldBlockScroll,\n    backdrop: (_c = originalProps.backdrop) != null ? _c : \"opaque\",\n    isOpen: state.isOpen,\n    onClose: state.close,\n    disableAnimation,\n    setBodyMounted,\n    setHeaderMounted,\n    getDialogProps,\n    getBackdropProps,\n    getCloseButtonProps\n  };\n}\n\nexport {\n  useModal\n};\n"], "names": [], "mappings": ";;;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;AAYA,SAAS,SAAS,aAAa;IAC7B,IAAI,IAAI,IAAI;IACZ,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,QAAK,CAAC,WAAW;IAC/E,MAAM,EACJ,GAAG,EACH,EAAE,EACF,SAAS,EACT,UAAU,EACV,MAAM,EACN,WAAW,EACX,YAAY,EACZ,WAAW,EACX,WAAW,EACX,gBAAgB,IAAI,EACpB,kBAAkB,KAAK,EACvB,oBAAoB,IAAI,EACxB,eAAe,EACf,4BAA4B,KAAK,EACjC,OAAO,EACP,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB,CAAC,KAAK,CAAC,KAAK,cAAc,gBAAgB,KAAK,OAAO,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK;IACpK,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACrB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACnB,MAAM,QAAQ,CAAA,GAAA,iLAAA,CAAA,yBAAsB,AAAD,EAAE;QACnC;QACA;QACA,cAAc,CAAC;YACb,gBAAgB,OAAO,KAAK,IAAI,aAAa;YAC7C,IAAI,CAAC,SAAS;gBACZ,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF;IACF;IACA,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EACtD;QACE;QACA;QACA;IACF,GACA,OACA;IAEF,MAAM,EAAE,aAAa,gBAAgB,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,gBAAa,AAAD,EAAE;QAAE,SAAS,MAAM,KAAK;IAAC,GAAG;IAClF,MAAM,EAAE,gBAAgB,yBAAyB,EAAE,YAAY,qBAAqB,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IACpG,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,+JAAA,CAAA,QAAK,AAAD,EAAE;YACV,GAAG,YAAY;YACf;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;KAAiB;IAEhD,MAAM,iBAAiB,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI;QAC9C,IAAI;QACJ,OAAO;YACL,KAAK,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,MAAM;YACrB,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,YAAY,OAAO;YAC7C,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,SAAS;YAAE;YAClE,IAAI;YACJ,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM;YAClC,oBAAoB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC7B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,kBAAkB,CAAC,MAAM,iBAAiB,OAAO,KAAK,IAAI,cAAc,SAAS,KAAK,OAAO,MAAM;YACnG,mBAAmB,gBAAgB,WAAW,KAAK;YACnD,oBAAoB,cAAc,SAAS,KAAK;QAClD;IACF;IACA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,WAAW,MAAM,QAAQ,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,QAAQ;YAAC;YACrF,GAAG,aAAa;YAChB,GAAG,MAAM;QACX,CAAC,GACD;QAAC;QAAO;QAAY;KAAc;IAEpC,MAAM,sBAAsB;QAC1B,OAAO;YACL,MAAM;YACN,UAAU;YACV,cAAc;YACd,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;YAAC;YAC3F,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB,sBAAsB;QACxD;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,UAAU,CAAC,KAAK,cAAc,QAAQ,KAAK,OAAO,KAAK;QACvD,QAAQ,MAAM,MAAM;QACpB,SAAS,MAAM,KAAK;QACpB;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2090, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-UX6VCJJD.mjs"], "sourcesContent": ["\"use client\";\n\n// src/modal-context.ts\nimport { createContext } from \"@heroui/react-utils\";\nvar [ModalProvider, useModalContext] = createContext({\n  name: \"ModalContext\",\n  errorMessage: \"useModalContext: `context` is undefined. Seems you forgot to wrap all popover components within `<Modal />`\"\n});\n\nexport {\n  ModalProvider,\n  useModalContext\n};\n"], "names": [], "mappings": ";;;;AAEA,uBAAuB;AACvB;AAHA;;AAIA,IAAI,CAAC,eAAe,gBAAgB,GAAG,CAAA,GAAA,wKAAA,CAAA,gBAAa,AAAD,EAAE;IACnD,MAAM;IACN,cAAc;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2107, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-UKLRQS27.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useModal\n} from \"./chunk-CWPHHQ5O.mjs\";\nimport {\n  ModalProvider\n} from \"./chunk-UX6VCJJD.mjs\";\n\n// src/modal.tsx\nimport { AnimatePresence } from \"framer-motion\";\nimport { Overlay } from \"@react-aria/overlays\";\nimport { forwardRef } from \"@heroui/system\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Modal = forwardRef((props, ref) => {\n  const { children, ...otherProps } = props;\n  const context = useModal({ ...otherProps, ref });\n  const overlay = /* @__PURE__ */ jsx(Overlay, { portalContainer: context.portalContainer, children });\n  return /* @__PURE__ */ jsx(ModalProvider, { value: context, children: context.disableAnimation && context.isOpen ? overlay : /* @__PURE__ */ jsx(AnimatePresence, { children: context.isOpen ? overlay : null }) });\n});\nModal.displayName = \"HeroUI.Modal\";\nvar modal_default = Modal;\n\nexport {\n  modal_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,gBAAgB;AAChB;AACA;AACA;AACA;AAZA;;;;;;;AAaA,IAAI,QAAQ,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAY,GAAG;IACpC,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG,UAAU;QAAE;IAAI;IAC9C,MAAM,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,+JAAA,CAAA,UAAO,EAAE;QAAE,iBAAiB,QAAQ,eAAe;QAAE;IAAS;IAClG,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,+JAAA,CAAA,gBAAa,EAAE;QAAE,OAAO;QAAS,UAAU,QAAQ,gBAAgB,IAAI,QAAQ,MAAM,GAAG,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,yLAAA,CAAA,kBAAe,EAAE;YAAE,UAAU,QAAQ,MAAM,GAAG,UAAU;QAAK;IAAG;AACnN;AACA,MAAM,WAAW,GAAG;AACpB,IAAI,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-I7NTTF2N.mjs"], "sourcesContent": ["\"use client\";\n\n// src/modal-transition.ts\nimport { TRANSITION_EASINGS } from \"@heroui/framer-utils\";\nvar scaleInOut = {\n  enter: {\n    scale: \"var(--scale-enter)\",\n    y: \"var(--slide-enter)\",\n    opacity: 1,\n    willChange: \"auto\",\n    transition: {\n      scale: {\n        duration: 0.4,\n        ease: TRANSITION_EASINGS.ease\n      },\n      opacity: {\n        duration: 0.4,\n        ease: TRANSITION_EASINGS.ease\n      },\n      y: {\n        type: \"spring\",\n        bounce: 0,\n        duration: 0.6\n      }\n    }\n  },\n  exit: {\n    scale: \"var(--scale-exit)\",\n    y: \"var(--slide-exit)\",\n    opacity: 0,\n    willChange: \"transform\",\n    transition: {\n      duration: 0.3,\n      ease: TRANSITION_EASINGS.ease\n    }\n  }\n};\n\nexport {\n  scaleInOut\n};\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B;AAHA;;AAIA,IAAI,aAAa;IACf,OAAO;QACL,OAAO;QACP,GAAG;QACH,SAAS;QACT,YAAY;QACZ,YAAY;YACV,OAAO;gBACL,UAAU;gBACV,MAAM,yKAAA,CAAA,qBAAkB,CAAC,IAAI;YAC/B;YACA,SAAS;gBACP,UAAU;gBACV,MAAM,yKAAA,CAAA,qBAAkB,CAAC,IAAI;YAC/B;YACA,GAAG;gBACD,MAAM;gBACN,QAAQ;gBACR,UAAU;YACZ;QACF;IACF;IACA,MAAM;QACJ,OAAO;QACP,GAAG;QACH,SAAS;QACT,YAAY;QACZ,YAAY;YACV,UAAU;YACV,MAAM,yKAAA,CAAA,qBAAkB,CAAC,IAAI;QAC/B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-NWAOTABO.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  scaleInOut\n} from \"./chunk-I7NTTF2N.mjs\";\nimport {\n  useModalContext\n} from \"./chunk-UX6VCJJD.mjs\";\n\n// src/modal-content.tsx\nimport { cloneElement, isValidElement, useMemo, useCallback } from \"react\";\nimport { DismissButton } from \"@react-aria/overlays\";\nimport { TRANSITION_VARIANTS } from \"@heroui/framer-utils\";\nimport { CloseIcon } from \"@heroui/shared-icons\";\nimport { LazyMotion, m } from \"framer-motion\";\nimport { useDialog } from \"@react-aria/dialog\";\nimport { chain, mergeProps } from \"@heroui/shared-utils\";\nimport { useViewportSize } from \"@heroui/use-viewport-size\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar domAnimation = () => import(\"@heroui/dom-animation\").then((res) => res.default);\nvar ModalContent = (props) => {\n  const { as, children, role = \"dialog\", ...otherProps } = props;\n  const {\n    Component: DialogComponent,\n    domRef,\n    slots,\n    classNames,\n    motionProps,\n    backdrop,\n    closeButton,\n    hideCloseButton,\n    disableAnimation,\n    getDialogProps,\n    getBackdropProps,\n    getCloseButtonProps,\n    onClose\n  } = useModalContext();\n  const Component = as || DialogComponent || \"div\";\n  const viewport = useViewportSize();\n  const { dialogProps } = useDialog(\n    {\n      role\n    },\n    domRef\n  );\n  const closeButtonContent = isValidElement(closeButton) ? cloneElement(closeButton, getCloseButtonProps()) : /* @__PURE__ */ jsx(\"button\", { ...getCloseButtonProps(), children: /* @__PURE__ */ jsx(CloseIcon, {}) });\n  const onKeyDown = useCallback((e) => {\n    if (e.key === \"Tab\" && e.nativeEvent.isComposing) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }, []);\n  const contentProps = getDialogProps(mergeProps(dialogProps, otherProps));\n  const content = /* @__PURE__ */ jsxs(Component, { ...contentProps, onKeyDown: chain(contentProps.onKeyDown, onKeyDown), children: [\n    /* @__PURE__ */ jsx(DismissButton, { onDismiss: onClose }),\n    !hideCloseButton && closeButtonContent,\n    typeof children === \"function\" ? children(onClose) : children,\n    /* @__PURE__ */ jsx(DismissButton, { onDismiss: onClose })\n  ] });\n  const backdropContent = useMemo(() => {\n    if (backdrop === \"transparent\") {\n      return null;\n    }\n    if (disableAnimation) {\n      return /* @__PURE__ */ jsx(\"div\", { ...getBackdropProps() });\n    }\n    return /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(\n      m.div,\n      {\n        animate: \"enter\",\n        exit: \"exit\",\n        initial: \"exit\",\n        variants: TRANSITION_VARIANTS.fade,\n        ...getBackdropProps()\n      }\n    ) });\n  }, [backdrop, disableAnimation, getBackdropProps]);\n  const viewportStyle = {\n    \"--visual-viewport-height\": viewport.height + \"px\"\n  };\n  const contents = disableAnimation ? /* @__PURE__ */ jsx(\n    \"div\",\n    {\n      className: slots.wrapper({ class: classNames == null ? void 0 : classNames.wrapper }),\n      \"data-slot\": \"wrapper\",\n      style: viewportStyle,\n      children: content\n    }\n  ) : /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(\n    m.div,\n    {\n      animate: \"enter\",\n      className: slots.wrapper({ class: classNames == null ? void 0 : classNames.wrapper }),\n      \"data-slot\": \"wrapper\",\n      exit: \"exit\",\n      initial: \"exit\",\n      variants: scaleInOut,\n      ...motionProps,\n      style: viewportStyle,\n      children: content\n    }\n  ) });\n  return /* @__PURE__ */ jsxs(\"div\", { tabIndex: -1, children: [\n    backdropContent,\n    contents\n  ] });\n};\nModalContent.displayName = \"HeroUI.ModalContent\";\nvar modal_content_default = ModalContent;\n\nexport {\n  modal_content_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;AAkBA,IAAI,eAAe,IAAM,qJAAgC,IAAI,CAAC,CAAC,MAAQ,IAAI,OAAO;AAClF,IAAI,eAAe,CAAC;IAClB,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,QAAQ,EAAE,GAAG,YAAY,GAAG;IACzD,MAAM,EACJ,WAAW,eAAe,EAC1B,MAAM,EACN,KAAK,EACL,UAAU,EACV,WAAW,EACX,QAAQ,EACR,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,mBAAmB,EACnB,OAAO,EACR,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAClB,MAAM,YAAY,MAAM,mBAAmB;IAC3C,MAAM,WAAW,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAC9B;QACE;IACF,GACA;IAEF,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,aAAa,yBAAyB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,GAAG,qBAAqB;QAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,yKAAA,CAAA,YAAS,EAAE,CAAC;IAAG;IACnN,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,WAAW,CAAC,WAAW,EAAE;YAChD,EAAE,eAAe;YACjB,EAAE,cAAc;QAClB;IACF,GAAG,EAAE;IACL,MAAM,eAAe,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAC5D,MAAM,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;QAAE,GAAG,YAAY;QAAE,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD,EAAE,aAAa,SAAS,EAAE;QAAY,UAAU;YAChI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,qKAAA,CAAA,gBAAa,EAAE;gBAAE,WAAW;YAAQ;YACxD,CAAC,mBAAmB;YACpB,OAAO,aAAa,aAAa,SAAS,WAAW;YACrD,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,qKAAA,CAAA,gBAAa,EAAE;gBAAE,WAAW;YAAQ;SACzD;IAAC;IACF,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,aAAa,eAAe;YAC9B,OAAO;QACT;QACA,IAAI,kBAAkB;YACpB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,kBAAkB;YAAC;QAC5D;QACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,oLAAA,CAAA,aAAU,EAAE;YAAE,UAAU;YAAc,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC3F,qLAAA,CAAA,IAAC,CAAC,GAAG,EACL;gBACE,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,UAAU,yKAAA,CAAA,sBAAmB,CAAC,IAAI;gBAClC,GAAG,kBAAkB;YACvB;QACA;IACJ,GAAG;QAAC;QAAU;QAAkB;KAAiB;IACjD,MAAM,gBAAgB;QACpB,4BAA4B,SAAS,MAAM,GAAG;IAChD;IACA,MAAM,WAAW,mBAAmB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACpD,OACA;QACE,WAAW,MAAM,OAAO,CAAC;YAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;QAAC;QACnF,aAAa;QACb,OAAO;QACP,UAAU;IACZ,KACE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,oLAAA,CAAA,aAAU,EAAE;QAAE,UAAU;QAAc,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACxF,qLAAA,CAAA,IAAC,CAAC,GAAG,EACL;YACE,SAAS;YACT,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;YAAC;YACnF,aAAa;YACb,MAAM;YACN,SAAS;YACT,UAAU,+JAAA,CAAA,aAAU;YACpB,GAAG,WAAW;YACd,OAAO;YACP,UAAU;QACZ;IACA;IACF,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,UAAU,CAAC;QAAG,UAAU;YAC3D;YACA;SACD;IAAC;AACJ;AACA,aAAa,WAAW,GAAG;AAC3B,IAAI,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2336, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-IGSAU2ZA.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useModalContext\n} from \"./chunk-UX6VCJJD.mjs\";\n\n// src/modal-header.tsx\nimport { useEffect } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { clsx } from \"@heroui/shared-utils\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ModalHeader = forwardRef((props, ref) => {\n  const { as, children, className, ...otherProps } = props;\n  const { slots, classNames, headerId, setHeaderMounted } = useModalContext();\n  const domRef = useDOMRef(ref);\n  const Component = as || \"header\";\n  useEffect(() => {\n    setHeaderMounted(true);\n    return () => setHeaderMounted(false);\n  }, [setHeaderMounted]);\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      className: slots.header({ class: clsx(classNames == null ? void 0 : classNames.header, className) }),\n      id: headerId,\n      ...otherProps,\n      children\n    }\n  );\n});\nModalHeader.displayName = \"HeroUI.ModalHeader\";\nvar modal_header_default = ModalHeader;\n\nexport {\n  modal_header_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AAVA;;;;;;;AAWA,IAAI,cAAc,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACnC,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,YAAY,GAAG;IACnD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,YAAY,MAAM;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;QACjB,OAAO,IAAM,iBAAiB;IAChC,GAAG;QAAC;KAAiB;IACrB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,WAAW,MAAM,MAAM,CAAC;YAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE;QAAW;QAClG,IAAI;QACJ,GAAG,UAAU;QACb;IACF;AAEJ;AACA,YAAY,WAAW,GAAG;AAC1B,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2389, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-FOPEYBSC.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useModalContext\n} from \"./chunk-UX6VCJJD.mjs\";\n\n// src/modal-body.tsx\nimport { useEffect } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { clsx } from \"@heroui/shared-utils\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ModalBody = forwardRef((props, ref) => {\n  const { as, children, className, ...otherProps } = props;\n  const { slots, classNames, bodyId, setBodyMounted } = useModalContext();\n  const domRef = useDOMRef(ref);\n  const Component = as || \"div\";\n  useEffect(() => {\n    setBodyMounted(true);\n    return () => setBodyMounted(false);\n  }, [setBodyMounted]);\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      className: slots.body({ class: clsx(classNames == null ? void 0 : classNames.body, className) }),\n      id: bodyId,\n      ...otherProps,\n      children\n    }\n  );\n});\nModalBody.displayName = \"HeroUI.ModalBody\";\nvar modal_body_default = ModalBody;\n\nexport {\n  modal_body_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AAVA;;;;;;;AAWA,IAAI,YAAY,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACjC,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,YAAY,GAAG;IACnD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IACpE,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,YAAY,MAAM;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,OAAO,IAAM,eAAe;IAC9B,GAAG;QAAC;KAAe;IACnB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,WAAW,MAAM,IAAI,CAAC;YAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;QAAW;QAC9F,IAAI;QACJ,GAAG,UAAU;QACb;IACF;AAEJ;AACA,UAAU,WAAW,GAAG;AACxB,IAAI,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2442, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/modal/dist/chunk-O5MCAK4F.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useModalContext\n} from \"./chunk-UX6VCJJD.mjs\";\n\n// src/modal-footer.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { clsx } from \"@heroui/shared-utils\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ModalFooter = forwardRef((props, ref) => {\n  const { as, children, className, ...otherProps } = props;\n  const { slots, classNames } = useModalContext();\n  const domRef = useDOMRef(ref);\n  const Component = as || \"footer\";\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      className: slots.footer({ class: clsx(classNames == null ? void 0 : classNames.footer, className) }),\n      ...otherProps,\n      children\n    }\n  );\n});\nModalFooter.displayName = \"HeroUI.ModalFooter\";\nvar modal_footer_default = ModalFooter;\n\nexport {\n  modal_footer_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,uBAAuB;AACvB;AACA;AACA;AACA;AATA;;;;;;AAUA,IAAI,cAAc,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACnC,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,YAAY,GAAG;IACnD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAC5C,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,YAAY,MAAM;IACxB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,WAAW,MAAM,MAAM,CAAC;YAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE;QAAW;QAClG,GAAG,UAAU;QACb;IACF;AAEJ;AACA,YAAY,WAAW,GAAG;AAC1B,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/shared-icons/dist/chunk-3JRSRN3Z.mjs"], "sourcesContent": ["// src/close.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar CloseIcon = (props) => {\n  const { isSelected, isIndeterminate, disableAnimation, ...otherProps } = props;\n  return /* @__PURE__ */ jsx(\n    \"svg\",\n    {\n      \"aria-hidden\": \"true\",\n      className: \"fill-current\",\n      fill: \"none\",\n      focusable: \"false\",\n      height: \"1em\",\n      role: \"presentation\",\n      stroke: \"currentColor\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      viewBox: \"0 0 24 24\",\n      width: \"1em\",\n      ...otherProps,\n      children: /* @__PURE__ */ jsx(\"path\", { d: \"M18 6L6 18M6 6l12 12\" })\n    }\n  );\n};\n\nexport {\n  CloseIcon\n};\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;AAChB;;AACA,IAAI,YAAY,CAAC;IACf,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,YAAY,GAAG;IACzE,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,OACA;QACE,eAAe;QACf,WAAW;QACX,MAAM;QACN,WAAW;QACX,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB,aAAa;QACb,SAAS;QACT,OAAO;QACP,GAAG,UAAU;QACb,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAAuB;IACpE;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2518, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/shared-icons/dist/chunk-7F3ZLNJ6.mjs"], "sourcesContent": ["// src/chevron-down.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ChevronDownIcon = ({ strokeWidth = 1.5, ...props }) => /* @__PURE__ */ jsx(\n  \"svg\",\n  {\n    \"aria-hidden\": \"true\",\n    fill: \"none\",\n    focusable: \"false\",\n    height: \"1em\",\n    role: \"presentation\",\n    stroke: \"currentColor\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth,\n    viewBox: \"0 0 24 24\",\n    width: \"1em\",\n    ...props,\n    children: /* @__PURE__ */ jsx(\"path\", { d: \"m6 9 6 6 6-6\" })\n  }\n);\n\nexport {\n  ChevronDownIcon\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB;;AACA,IAAI,kBAAkB,CAAC,EAAE,cAAc,GAAG,EAAE,GAAG,OAAO,GAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC3E,OACA;QACE,eAAe;QACf,MAAM;QACN,WAAW;QACX,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB;QACA,SAAS;QACT,OAAO;QACP,GAAG,KAAK;QACR,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG;QAAe;IAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2546, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-viewport-size/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport React, { useContext, useEffect, useState } from \"react\";\nvar visualViewport = typeof document !== \"undefined\" && window.visualViewport;\nvar IsSSRContext = React.createContext(false);\nfunction getSnapshot() {\n  return false;\n}\nfunction getServerSnapshot() {\n  return true;\n}\nfunction subscribe(onStoreChange) {\n  return () => {\n  };\n}\nfunction useIsSSR() {\n  if (typeof React[\"useSyncExternalStore\"] === \"function\") {\n    return React[\"useSyncExternalStore\"](subscribe, getSnapshot, getServerSnapshot);\n  }\n  return useContext(IsSSRContext);\n}\nfunction useViewportSize() {\n  let isSSR = useIsSSR();\n  let [size, setSize] = useState(() => isSSR ? { width: 0, height: 0 } : getViewportSize());\n  useEffect(() => {\n    let onResize = () => {\n      setSize((size2) => {\n        let newSize = getViewportSize();\n        if (newSize.width === size2.width && newSize.height === size2.height) {\n          return size2;\n        }\n        return newSize;\n      });\n    };\n    if (!visualViewport) {\n      window.addEventListener(\"resize\", onResize);\n    } else {\n      visualViewport.addEventListener(\"resize\", onResize);\n    }\n    return () => {\n      if (!visualViewport) {\n        window.removeEventListener(\"resize\", onResize);\n      } else {\n        visualViewport.removeEventListener(\"resize\", onResize);\n      }\n    };\n  }, []);\n  return size;\n}\nfunction getViewportSize() {\n  return {\n    width: visualViewport && (visualViewport == null ? void 0 : visualViewport.width) || window.innerWidth,\n    height: visualViewport && (visualViewport == null ? void 0 : visualViewport.height) || window.innerHeight\n  };\n}\nexport {\n  useIsSSR,\n  useViewportSize\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AACf;;AACA,IAAI,iBAAiB,OAAO,aAAa,eAAe,OAAO,cAAc;AAC7E,IAAI,eAAe,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;AACvC,SAAS;IACP,OAAO;AACT;AACA,SAAS;IACP,OAAO;AACT;AACA,SAAS,UAAU,aAAa;IAC9B,OAAO,KACP;AACF;AACA,SAAS;IACP,IAAI,OAAO,qMAAA,CAAA,UAAK,CAAC,uBAAuB,KAAK,YAAY;QACvD,OAAO,qMAAA,CAAA,UAAK,CAAC,uBAAuB,CAAC,WAAW,aAAa;IAC/D;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AACA,SAAS;IACP,IAAI,QAAQ;IACZ,IAAI,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,QAAQ;YAAE,OAAO;YAAG,QAAQ;QAAE,IAAI;IACvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,QAAQ,CAAC;gBACP,IAAI,UAAU;gBACd,IAAI,QAAQ,KAAK,KAAK,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,MAAM,MAAM,EAAE;oBACpE,OAAO;gBACT;gBACA,OAAO;YACT;QACF;QACA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gBAAgB,CAAC,UAAU;QACpC,OAAO;YACL,eAAe,gBAAgB,CAAC,UAAU;QAC5C;QACA,OAAO;YACL,IAAI,CAAC,gBAAgB;gBACnB,OAAO,mBAAmB,CAAC,UAAU;YACvC,OAAO;gBACL,eAAe,mBAAmB,CAAC,UAAU;YAC/C;QACF;IACF,GAAG,EAAE;IACL,OAAO;AACT;AACA,SAAS;IACP,OAAO;QACL,OAAO,kBAAkB,CAAC,kBAAkB,OAAO,KAAK,IAAI,eAAe,KAAK,KAAK,OAAO,UAAU;QACtG,QAAQ,kBAAkB,CAAC,kBAAkB,OAAO,KAAK,IAAI,eAAe,MAAM,KAAK,OAAO,WAAW;IAC3G;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-aria-multiselect/dist/chunk-MQVQK7AZ.mjs"], "sourcesContent": ["// src/use-multiselect.ts\nimport { useCollator } from \"@react-aria/i18n\";\nimport { setInteractionModality } from \"@react-aria/interactions\";\nimport { useField } from \"@react-aria/label\";\nimport { useMenuTrigger } from \"@react-aria/menu\";\nimport { ListKeyboardDelegate, useTypeSelect } from \"@react-aria/selection\";\nimport { chain, filterDOMProps, mergeProps, useId } from \"@react-aria/utils\";\nimport { useMemo } from \"react\";\nfunction useMultiSelect(props, state, ref) {\n  const { disallowEmptySelection, isDisabled } = props;\n  const collator = useCollator({ usage: \"search\", sensitivity: \"base\" });\n  const delegate = useMemo(\n    () => new ListKeyboardDelegate(state.collection, state.disabledKeys, null, collator),\n    [state.collection, state.disabledKeys, collator]\n  );\n  const { menuTriggerProps, menuProps } = useMenuTrigger(\n    {\n      isDisabled,\n      type: \"listbox\"\n    },\n    state,\n    ref\n  );\n  const triggerOnKeyDown = (e) => {\n    if (state.selectionMode === \"single\") {\n      switch (e.key) {\n        case \"ArrowLeft\": {\n          e.preventDefault();\n          const key = state.selectedKeys.size > 0 ? delegate.getKeyAbove(state.selectedKeys.values().next().value) : delegate.getFirstKey();\n          if (key) {\n            state.setSelectedKeys([key]);\n          }\n          break;\n        }\n        case \"ArrowRight\": {\n          e.preventDefault();\n          const key = state.selectedKeys.size > 0 ? delegate.getKeyBelow(state.selectedKeys.values().next().value) : delegate.getFirstKey();\n          if (key) {\n            state.setSelectedKeys([key]);\n          }\n          break;\n        }\n      }\n    }\n  };\n  const { typeSelectProps } = useTypeSelect({\n    keyboardDelegate: delegate,\n    selectionManager: state.selectionManager,\n    onTypeSelect(key) {\n      state.setSelectedKeys([key]);\n    }\n  });\n  const { isInvalid, validationErrors, validationDetails } = state.displayValidation;\n  const { labelProps, fieldProps, descriptionProps, errorMessageProps } = useField({\n    ...props,\n    labelElementType: \"span\",\n    isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n  typeSelectProps.onKeyDown = typeSelectProps.onKeyDownCapture;\n  delete typeSelectProps.onKeyDownCapture;\n  menuTriggerProps.onPressStart = (e) => {\n    if (e.pointerType !== \"touch\" && e.pointerType !== \"keyboard\" && !isDisabled) {\n      state.toggle(e.pointerType === \"virtual\" ? \"first\" : null);\n    }\n  };\n  const domProps = filterDOMProps(props, { labelable: true });\n  const triggerProps = mergeProps(typeSelectProps, menuTriggerProps, fieldProps);\n  const valueId = useId();\n  return {\n    labelProps: {\n      ...labelProps,\n      onClick: () => {\n        var _a;\n        if (!props.isDisabled) {\n          (_a = ref.current) == null ? void 0 : _a.focus();\n          setInteractionModality(\"keyboard\");\n        }\n      }\n    },\n    triggerProps: mergeProps(domProps, {\n      ...triggerProps,\n      onKeyDown: chain(triggerProps.onKeyDown, triggerOnKeyDown, props.onKeyDown),\n      onKeyUp: props.onKeyUp,\n      \"aria-labelledby\": [\n        valueId,\n        triggerProps[\"aria-labelledby\"],\n        triggerProps[\"aria-label\"] && !triggerProps[\"aria-labelledby\"] ? triggerProps.id : null\n      ].join(\",\"),\n      onFocus(e) {\n        if (state.isFocused) {\n          return;\n        }\n        if (props.onFocus) {\n          props.onFocus(e);\n        }\n        state.setFocused(true);\n      },\n      onBlur(e) {\n        if (state.isOpen) {\n          return;\n        }\n        if (props.onBlur) {\n          props.onBlur(e);\n        }\n        state.setFocused(false);\n      }\n    }),\n    valueProps: {\n      id: valueId\n    },\n    menuProps: {\n      ...menuProps,\n      disallowEmptySelection,\n      autoFocus: state.focusStrategy || true,\n      shouldSelectOnPressUp: true,\n      shouldFocusOnHover: true,\n      onBlur: (e) => {\n        if (e.currentTarget.contains(e.relatedTarget)) {\n          return;\n        }\n        if (props.onBlur) {\n          props.onBlur(e);\n        }\n        state.setFocused(false);\n      },\n      // @ts-ignore\n      onFocus: menuProps == null ? void 0 : menuProps.onFocus,\n      \"aria-labelledby\": [\n        fieldProps[\"aria-labelledby\"],\n        triggerProps[\"aria-label\"] && !fieldProps[\"aria-labelledby\"] ? triggerProps.id : null\n      ].filter(Boolean).join(\" \")\n    },\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n\nexport {\n  useMultiSelect\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;AACzB;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;AACA,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,GAAG;IACvC,MAAM,EAAE,sBAAsB,EAAE,UAAU,EAAE,GAAG;IAC/C,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE;QAAE,OAAO;QAAU,aAAa;IAAO;IACpE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,IAAI,6KAAA,CAAA,uBAAoB,CAAC,MAAM,UAAU,EAAE,MAAM,YAAY,EAAE,MAAM,WAC3E;QAAC,MAAM,UAAU;QAAE,MAAM,YAAY;QAAE;KAAS;IAElD,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kKAAA,CAAA,iBAAc,AAAD,EACnD;QACE;QACA,MAAM;IACR,GACA,OACA;IAEF,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,aAAa,KAAK,UAAU;YACpC,OAAQ,EAAE,GAAG;gBACX,KAAK;oBAAa;wBAChB,EAAE,cAAc;wBAChB,MAAM,MAAM,MAAM,YAAY,CAAC,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,YAAY,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,SAAS,WAAW;wBAC/H,IAAI,KAAK;4BACP,MAAM,eAAe,CAAC;gCAAC;6BAAI;wBAC7B;wBACA;oBACF;gBACA,KAAK;oBAAc;wBACjB,EAAE,cAAc;wBAChB,MAAM,MAAM,MAAM,YAAY,CAAC,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,YAAY,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,SAAS,WAAW;wBAC/H,IAAI,KAAK;4BACP,MAAM,eAAe,CAAC;gCAAC;6BAAI;wBAC7B;wBACA;oBACF;YACF;QACF;IACF;IACA,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,gBAAa,AAAD,EAAE;QACxC,kBAAkB;QAClB,kBAAkB,MAAM,gBAAgB;QACxC,cAAa,GAAG;YACd,MAAM,eAAe,CAAC;gBAAC;aAAI;QAC7B;IACF;IACA,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAG,MAAM,iBAAiB;IAClF,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/E,GAAG,KAAK;QACR,kBAAkB;QAClB;QACA,cAAc,MAAM,YAAY,IAAI;IACtC;IACA,gBAAgB,SAAS,GAAG,gBAAgB,gBAAgB;IAC5D,OAAO,gBAAgB,gBAAgB;IACvC,iBAAiB,YAAY,GAAG,CAAC;QAC/B,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,cAAc,CAAC,YAAY;YAC5E,MAAM,MAAM,CAAC,EAAE,WAAW,KAAK,YAAY,UAAU;QACvD;IACF;IACA,MAAM,WAAW,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;QAAE,WAAW;IAAK;IACzD,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,kBAAkB;IACnE,MAAM,UAAU,CAAA,GAAA,0JAAA,CAAA,QAAK,AAAD;IACpB,OAAO;QACL,YAAY;YACV,GAAG,UAAU;YACb,SAAS;gBACP,IAAI;gBACJ,IAAI,CAAC,MAAM,UAAU,EAAE;oBACrB,CAAC,KAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK;oBAC9C,CAAA,GAAA,2KAAA,CAAA,yBAAsB,AAAD,EAAE;gBACzB;YACF;QACF;QACA,cAAc,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE,UAAU;YACjC,GAAG,YAAY;YACf,WAAW,CAAA,GAAA,0JAAA,CAAA,QAAK,AAAD,EAAE,aAAa,SAAS,EAAE,kBAAkB,MAAM,SAAS;YAC1E,SAAS,MAAM,OAAO;YACtB,mBAAmB;gBACjB;gBACA,YAAY,CAAC,kBAAkB;gBAC/B,YAAY,CAAC,aAAa,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,aAAa,EAAE,GAAG;aACpF,CAAC,IAAI,CAAC;YACP,SAAQ,CAAC;gBACP,IAAI,MAAM,SAAS,EAAE;oBACnB;gBACF;gBACA,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,OAAO,CAAC;gBAChB;gBACA,MAAM,UAAU,CAAC;YACnB;YACA,QAAO,CAAC;gBACN,IAAI,MAAM,MAAM,EAAE;oBAChB;gBACF;gBACA,IAAI,MAAM,MAAM,EAAE;oBAChB,MAAM,MAAM,CAAC;gBACf;gBACA,MAAM,UAAU,CAAC;YACnB;QACF;QACA,YAAY;YACV,IAAI;QACN;QACA,WAAW;YACT,GAAG,SAAS;YACZ;YACA,WAAW,MAAM,aAAa,IAAI;YAClC,uBAAuB;YACvB,oBAAoB;YACpB,QAAQ,CAAC;gBACP,IAAI,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,aAAa,GAAG;oBAC7C;gBACF;gBACA,IAAI,MAAM,MAAM,EAAE;oBAChB,MAAM,MAAM,CAAC;gBACf;gBACA,MAAM,UAAU,CAAC;YACnB;YACA,aAAa;YACb,SAAS,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO;YACvD,mBAAmB;gBACjB,UAAU,CAAC,kBAAkB;gBAC7B,YAAY,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,kBAAkB,GAAG,aAAa,EAAE,GAAG;aAClF,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2781, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-aria-multiselect/dist/chunk-74XVDT4G.mjs"], "sourcesContent": ["// src/use-multiselect-list-state.ts\nimport { useMemo } from \"react\";\nimport { useListState } from \"@react-stately/list\";\nfunction useMultiSelectListState(props) {\n  const {\n    collection,\n    disabledKeys,\n    selectionManager,\n    selectionManager: { setSelectedKeys, selectedKeys, selectionMode }\n  } = useListState(props);\n  const missingKeys = useMemo(() => {\n    if (!props.isLoading && selectedKeys.size !== 0) {\n      return Array.from(selectedKeys).filter(Boolean).filter((key) => !collection.getItem(key));\n    }\n    return [];\n  }, [selectedKeys, collection]);\n  const selectedItems = selectedKeys.size !== 0 ? Array.from(selectedKeys).map((key) => {\n    return collection.getItem(key);\n  }).filter(Boolean) : null;\n  if (missingKeys.length) {\n    console.warn(\n      `Select: Keys \"${missingKeys.join(\n        \", \"\n      )}\" passed to \"selectedKeys\" are not present in the collection.`\n    );\n  }\n  return {\n    collection,\n    disabledKeys,\n    selection<PERSON>anager,\n    selectionMode,\n    selectedKeys,\n    setSelectedKeys: setSelectedKeys.bind(selectionManager),\n    selectedItems\n  };\n}\n\nexport {\n  useMultiSelectListState\n};\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;AACpC;AACA;;;AACA,SAAS,wBAAwB,KAAK;IACpC,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAAE,eAAe,EAAE,YAAY,EAAE,aAAa,EAAE,EACnE,GAAG,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD,EAAE;IACjB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,CAAC,MAAM,SAAS,IAAI,aAAa,IAAI,KAAK,GAAG;YAC/C,OAAO,MAAM,IAAI,CAAC,cAAc,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC,MAAQ,CAAC,WAAW,OAAO,CAAC;QACtF;QACA,OAAO,EAAE;IACX,GAAG;QAAC;QAAc;KAAW;IAC7B,MAAM,gBAAgB,aAAa,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAC5E,OAAO,WAAW,OAAO,CAAC;IAC5B,GAAG,MAAM,CAAC,WAAW;IACrB,IAAI,YAAY,MAAM,EAAE;QACtB,QAAQ,IAAI,CACV,CAAC,cAAc,EAAE,YAAY,IAAI,CAC/B,MACA,6DAA6D,CAAC;IAEpE;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,iBAAiB,gBAAgB,IAAI,CAAC;QACtC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2821, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-aria-multiselect/dist/chunk-FHVPTEOP.mjs"], "sourcesContent": ["import {\n  useMultiSelectListState\n} from \"./chunk-74XVDT4G.mjs\";\n\n// src/use-multiselect-state.ts\nimport { useMenuTriggerState } from \"@react-stately/menu\";\nimport { useFormValidationState } from \"@react-stately/form\";\nimport { useState } from \"react\";\nfunction useMultiSelectState({\n  validate,\n  validationBehavior,\n  ...props\n}) {\n  const [isFocused, setFocused] = useState(false);\n  const [focusStrategy, setFocusStrategy] = useState(null);\n  const triggerState = useMenuTriggerState(props);\n  const listState = useMultiSelectListState({\n    ...props,\n    onSelectionChange: (keys) => {\n      if (props.onSelectionChange != null) {\n        if (keys === \"all\") {\n          props.onSelectionChange(new Set(listState.collection.getKeys()));\n        } else {\n          props.onSelectionChange(keys);\n        }\n      }\n      if (props.selectionMode === \"single\") {\n        triggerState.close();\n      }\n    }\n  });\n  const validationState = useFormValidationState({\n    ...props,\n    validationBehavior,\n    validate: (value) => {\n      if (!validate) return;\n      const keys = Array.from(value);\n      return validate(props.selectionMode === \"single\" ? keys[0] : keys);\n    },\n    // @ts-ignore\n    value: listState.selectedKeys\n  });\n  const shouldHideContent = listState.collection.size === 0 && props.hideEmptyContent;\n  return {\n    ...validationState,\n    ...listState,\n    ...triggerState,\n    focusStrategy,\n    close() {\n      triggerState.close();\n    },\n    open(focusStrategy2 = null) {\n      if (shouldHideContent) return;\n      setFocusStrategy(focusStrategy2);\n      triggerState.open();\n    },\n    toggle(focusStrategy2 = null) {\n      if (shouldHideContent) return;\n      setFocusStrategy(focusStrategy2);\n      triggerState.toggle();\n    },\n    isFocused,\n    setFocused\n  };\n}\n\nexport {\n  useMultiSelectState\n};\n"], "names": [], "mappings": ";;;AAAA;AAIA,+BAA+B;AAC/B;AACA;AACA;;;;;AACA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,kBAAkB,EAClB,GAAG,OACJ;IACC,MAAM,CAAC,WAAW,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,eAAe,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,oLAAA,CAAA,0BAAuB,AAAD,EAAE;QACxC,GAAG,KAAK;QACR,mBAAmB,CAAC;YAClB,IAAI,MAAM,iBAAiB,IAAI,MAAM;gBACnC,IAAI,SAAS,OAAO;oBAClB,MAAM,iBAAiB,CAAC,IAAI,IAAI,UAAU,UAAU,CAAC,OAAO;gBAC9D,OAAO;oBACL,MAAM,iBAAiB,CAAC;gBAC1B;YACF;YACA,IAAI,MAAM,aAAa,KAAK,UAAU;gBACpC,aAAa,KAAK;YACpB;QACF;IACF;IACA,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,yBAAsB,AAAD,EAAE;QAC7C,GAAG,KAAK;QACR;QACA,UAAU,CAAC;YACT,IAAI,CAAC,UAAU;YACf,MAAM,OAAO,MAAM,IAAI,CAAC;YACxB,OAAO,SAAS,MAAM,aAAa,KAAK,WAAW,IAAI,CAAC,EAAE,GAAG;QAC/D;QACA,aAAa;QACb,OAAO,UAAU,YAAY;IAC/B;IACA,MAAM,oBAAoB,UAAU,UAAU,CAAC,IAAI,KAAK,KAAK,MAAM,gBAAgB;IACnF,OAAO;QACL,GAAG,eAAe;QAClB,GAAG,SAAS;QACZ,GAAG,YAAY;QACf;QACA;YACE,aAAa,KAAK;QACpB;QACA,MAAK,iBAAiB,IAAI;YACxB,IAAI,mBAAmB;YACvB,iBAAiB;YACjB,aAAa,IAAI;QACnB;QACA,QAAO,iBAAiB,IAAI;YAC1B,IAAI,mBAAmB;YACvB,iBAAiB;YACjB,aAAa,MAAM;QACrB;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2891, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/select/dist/chunk-XAZU4BXN.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-select.ts\nimport { mapPropsVariants, useLabelPlacement, useProviderContext } from \"@heroui/system\";\nimport { select } from \"@heroui/theme\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { useMemo, useCallback, useRef, useEffect } from \"react\";\nimport { useAriaButton } from \"@heroui/use-aria-button\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { clsx, dataAttr, objectToDeps, mergeProps } from \"@heroui/shared-utils\";\nimport { useHover, usePress } from \"@react-aria/interactions\";\nimport { useMultiSelect, useMultiSelectState } from \"@heroui/use-aria-multiselect\";\nimport { useSafeLayoutEffect } from \"@heroui/use-safe-layout-effect\";\nimport { FormContext, useSlottedContext } from \"@heroui/form\";\nimport { usePreventScroll } from \"@react-aria/overlays\";\nvar selectData = /* @__PURE__ */ new WeakMap();\nfunction useSelect(originalProps) {\n  var _a, _b, _c, _d, _e, _f;\n  const globalContext = useProviderContext();\n  const { validationBehavior: formValidationBehavior } = useSlottedContext(FormContext) || {};\n  const [props, variantProps] = mapPropsVariants(originalProps, select.variantKeys);\n  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;\n  const {\n    ref,\n    as,\n    label,\n    name,\n    isLoading,\n    selectorIcon,\n    isOpen,\n    defaultOpen,\n    onOpenChange,\n    startContent,\n    endContent,\n    description,\n    renderValue,\n    onSelectionChange,\n    placeholder,\n    isVirtualized,\n    itemHeight = 36,\n    maxListboxHeight = 256,\n    children,\n    disallowEmptySelection = false,\n    selectionMode = \"single\",\n    spinnerRef,\n    scrollRef: scrollRefProp,\n    popoverProps = {},\n    scrollShadowProps = {},\n    listboxProps = {},\n    spinnerProps = {},\n    validationState,\n    onChange,\n    onClose,\n    className,\n    classNames,\n    validationBehavior = (_c = formValidationBehavior != null ? formValidationBehavior : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _c : \"native\",\n    hideEmptyContent = false,\n    onClear,\n    ...otherProps\n  } = props;\n  const scrollShadowRef = useDOMRef(scrollRefProp);\n  const slotsProps = {\n    popoverProps: mergeProps(\n      {\n        placement: \"bottom\",\n        triggerScaleOnOpen: false,\n        offset: 5,\n        disableAnimation\n      },\n      popoverProps\n    ),\n    scrollShadowProps: mergeProps(\n      {\n        ref: scrollShadowRef,\n        isEnabled: (_d = originalProps.showScrollIndicators) != null ? _d : true,\n        hideScrollBar: true,\n        offset: 15\n      },\n      scrollShadowProps\n    ),\n    listboxProps: mergeProps(\n      {\n        disableAnimation\n      },\n      listboxProps\n    )\n  };\n  const Component = as || \"button\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const triggerRef = useRef(null);\n  const listBoxRef = useRef(null);\n  const popoverRef = useRef(null);\n  let state = useMultiSelectState({\n    ...props,\n    isOpen,\n    selectionMode,\n    disallowEmptySelection,\n    validationBehavior,\n    children,\n    isRequired: originalProps.isRequired,\n    isDisabled: originalProps.isDisabled,\n    isInvalid: originalProps.isInvalid,\n    defaultOpen,\n    hideEmptyContent,\n    onOpenChange: (open) => {\n      onOpenChange == null ? void 0 : onOpenChange(open);\n      if (!open) {\n        onClose == null ? void 0 : onClose();\n      }\n    },\n    onSelectionChange: (keys) => {\n      onSelectionChange == null ? void 0 : onSelectionChange(keys);\n      if (onChange && typeof onChange === \"function\") {\n        onChange({\n          target: {\n            ...domRef.current && {\n              ...domRef.current,\n              name: domRef.current.name\n            },\n            value: Array.from(keys).join(\",\")\n          }\n        });\n      }\n      state.commitValidation();\n    }\n  });\n  state = {\n    ...state,\n    ...originalProps.isDisabled && {\n      disabledKeys: /* @__PURE__ */ new Set([...state.collection.getKeys()])\n    }\n  };\n  useSafeLayoutEffect(() => {\n    var _a2;\n    if (!((_a2 = domRef.current) == null ? void 0 : _a2.value)) return;\n    state.setSelectedKeys(/* @__PURE__ */ new Set([...state.selectedKeys, domRef.current.value]));\n  }, [domRef.current]);\n  const {\n    labelProps,\n    triggerProps,\n    valueProps,\n    menuProps,\n    descriptionProps,\n    errorMessageProps,\n    isInvalid: isAriaInvalid,\n    validationErrors,\n    validationDetails\n  } = useMultiSelect(\n    { ...props, disallowEmptySelection, isDisabled: originalProps.isDisabled },\n    state,\n    triggerRef\n  );\n  const handleClear = useCallback(() => {\n    var _a2;\n    state.setSelectedKeys(/* @__PURE__ */ new Set([]));\n    onClear == null ? void 0 : onClear();\n    (_a2 = triggerRef.current) == null ? void 0 : _a2.focus();\n  }, [onClear, state]);\n  const { pressProps: clearPressProps } = usePress({\n    isDisabled: !!(originalProps == null ? void 0 : originalProps.isDisabled),\n    onPress: handleClear\n  });\n  const isInvalid = originalProps.isInvalid || validationState === \"invalid\" || isAriaInvalid;\n  const { isPressed, buttonProps } = useAriaButton(triggerProps, triggerRef);\n  const { focusProps, isFocused, isFocusVisible } = useFocusRing();\n  const { focusProps: clearFocusProps, isFocusVisible: isClearButtonFocusVisible } = useFocusRing();\n  const { isHovered, hoverProps } = useHover({ isDisabled: originalProps.isDisabled });\n  const labelPlacement = useLabelPlacement({\n    labelPlacement: originalProps.labelPlacement,\n    label\n  });\n  const hasPlaceholder = !!placeholder;\n  const shouldLabelBeOutside = labelPlacement === \"outside-left\" || labelPlacement === \"outside\";\n  const shouldLabelBeInside = labelPlacement === \"inside\";\n  const isOutsideLeft = labelPlacement === \"outside-left\";\n  const isClearable = originalProps.isClearable;\n  const isFilled = state.isOpen || hasPlaceholder || !!((_e = state.selectedItems) == null ? void 0 : _e.length) || !!startContent || !!endContent || !!originalProps.isMultiline;\n  const hasValue = !!((_f = state.selectedItems) == null ? void 0 : _f.length);\n  const hasLabel = !!label;\n  const hasLabelOutside = hasLabel && (isOutsideLeft || shouldLabelBeOutside && hasPlaceholder);\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const slots = useMemo(\n    () => select({\n      ...variantProps,\n      isInvalid,\n      isClearable,\n      labelPlacement,\n      disableAnimation\n    }),\n    [objectToDeps(variantProps), isInvalid, labelPlacement, disableAnimation]\n  );\n  usePreventScroll({\n    isDisabled: !state.isOpen\n  });\n  const errorMessage = typeof props.errorMessage === \"function\" ? props.errorMessage({ isInvalid, validationErrors, validationDetails }) : props.errorMessage || (validationErrors == null ? void 0 : validationErrors.join(\" \"));\n  const hasHelper = !!description || !!errorMessage;\n  const hasEndContent = !!endContent;\n  useEffect(() => {\n    if (state.isOpen && popoverRef.current && triggerRef.current) {\n      let selectRect = triggerRef.current.getBoundingClientRect();\n      let popover = popoverRef.current;\n      popover.style.width = selectRect.width + \"px\";\n    }\n  }, [state.isOpen]);\n  const getBaseProps = useCallback(\n    (props2 = {}) => ({\n      \"data-slot\": \"base\",\n      \"data-filled\": dataAttr(isFilled),\n      \"data-has-value\": dataAttr(hasValue),\n      \"data-has-label\": dataAttr(hasLabel),\n      \"data-has-helper\": dataAttr(hasHelper),\n      \"data-has-end-content\": dataAttr(hasEndContent),\n      \"data-invalid\": dataAttr(isInvalid),\n      \"data-has-label-outside\": dataAttr(hasLabelOutside),\n      className: slots.base({\n        class: clsx(baseStyles, props2.className)\n      }),\n      ...props2\n    }),\n    [slots, hasHelper, hasValue, hasLabel, hasLabelOutside, isFilled, baseStyles]\n  );\n  const getTriggerProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ref: triggerRef,\n        \"data-slot\": \"trigger\",\n        \"data-open\": dataAttr(state.isOpen),\n        \"data-disabled\": dataAttr(originalProps == null ? void 0 : originalProps.isDisabled),\n        \"data-focus\": dataAttr(isFocused),\n        \"data-pressed\": dataAttr(isPressed),\n        \"data-focus-visible\": dataAttr(isFocusVisible),\n        \"data-hover\": dataAttr(isHovered),\n        className: slots.trigger({ class: classNames == null ? void 0 : classNames.trigger }),\n        ...mergeProps(\n          buttonProps,\n          focusProps,\n          hoverProps,\n          filterDOMProps(otherProps, {\n            enabled: shouldFilterDOMProps\n          }),\n          filterDOMProps(props2)\n        )\n      };\n    },\n    [\n      slots,\n      triggerRef,\n      state.isOpen,\n      classNames == null ? void 0 : classNames.trigger,\n      originalProps == null ? void 0 : originalProps.isDisabled,\n      isFocused,\n      isPressed,\n      isFocusVisible,\n      isHovered,\n      buttonProps,\n      focusProps,\n      hoverProps,\n      otherProps,\n      shouldFilterDOMProps\n    ]\n  );\n  const getHiddenSelectProps = useCallback(\n    (props2 = {}) => ({\n      state,\n      triggerRef,\n      selectRef: domRef,\n      selectionMode,\n      label: originalProps == null ? void 0 : originalProps.label,\n      name: originalProps == null ? void 0 : originalProps.name,\n      isRequired: originalProps == null ? void 0 : originalProps.isRequired,\n      autoComplete: originalProps == null ? void 0 : originalProps.autoComplete,\n      isDisabled: originalProps == null ? void 0 : originalProps.isDisabled,\n      form: originalProps == null ? void 0 : originalProps.form,\n      onChange,\n      ...props2\n    }),\n    [\n      state,\n      selectionMode,\n      originalProps == null ? void 0 : originalProps.label,\n      originalProps == null ? void 0 : originalProps.autoComplete,\n      originalProps == null ? void 0 : originalProps.name,\n      originalProps == null ? void 0 : originalProps.isDisabled,\n      triggerRef\n    ]\n  );\n  const getLabelProps = useCallback(\n    (props2 = {}) => ({\n      \"data-slot\": \"label\",\n      className: slots.label({\n        class: clsx(classNames == null ? void 0 : classNames.label, props2.className)\n      }),\n      ...labelProps,\n      ...props2\n    }),\n    [slots, classNames == null ? void 0 : classNames.label, labelProps]\n  );\n  const getValueProps = useCallback(\n    (props2 = {}) => ({\n      \"data-slot\": \"value\",\n      className: slots.value({\n        class: clsx(classNames == null ? void 0 : classNames.value, props2.className)\n      }),\n      ...valueProps,\n      ...props2\n    }),\n    [slots, classNames == null ? void 0 : classNames.value, valueProps]\n  );\n  const getListboxWrapperProps = useCallback(\n    (props2 = {}) => ({\n      \"data-slot\": \"listboxWrapper\",\n      className: slots.listboxWrapper({\n        class: clsx(classNames == null ? void 0 : classNames.listboxWrapper, props2 == null ? void 0 : props2.className)\n      }),\n      style: {\n        maxHeight: maxListboxHeight != null ? maxListboxHeight : 256,\n        ...props2.style\n      },\n      ...mergeProps(slotsProps.scrollShadowProps, props2)\n    }),\n    [\n      slots.listboxWrapper,\n      classNames == null ? void 0 : classNames.listboxWrapper,\n      slotsProps.scrollShadowProps,\n      maxListboxHeight\n    ]\n  );\n  const getListboxProps = (props2 = {}) => {\n    const shouldVirtualize = isVirtualized != null ? isVirtualized : state.collection.size > 50;\n    return {\n      state,\n      ref: listBoxRef,\n      isVirtualized: shouldVirtualize,\n      virtualization: shouldVirtualize ? {\n        maxListboxHeight,\n        itemHeight\n      } : void 0,\n      \"data-slot\": \"listbox\",\n      className: slots.listbox({\n        class: clsx(classNames == null ? void 0 : classNames.listbox, props2 == null ? void 0 : props2.className)\n      }),\n      scrollShadowProps: slotsProps.scrollShadowProps,\n      ...mergeProps(slotsProps.listboxProps, props2, menuProps)\n    };\n  };\n  const getPopoverProps = useCallback(\n    (props2 = {}) => {\n      var _a2, _b2;\n      const popoverProps2 = mergeProps(slotsProps.popoverProps, props2);\n      return {\n        state,\n        triggerRef,\n        ref: popoverRef,\n        \"data-slot\": \"popover\",\n        scrollRef: listBoxRef,\n        triggerType: \"listbox\",\n        classNames: {\n          content: slots.popoverContent({\n            class: clsx(classNames == null ? void 0 : classNames.popoverContent, props2.className)\n          })\n        },\n        ...popoverProps2,\n        offset: state.selectedItems && state.selectedItems.length > 0 ? (\n          // forces the popover to update its position when the selected items change\n          state.selectedItems.length * 1e-8 + (((_a2 = slotsProps.popoverProps) == null ? void 0 : _a2.offset) || 0)\n        ) : (_b2 = slotsProps.popoverProps) == null ? void 0 : _b2.offset\n      };\n    },\n    [\n      slots,\n      classNames == null ? void 0 : classNames.popoverContent,\n      slotsProps.popoverProps,\n      triggerRef,\n      state,\n      state.selectedItems\n    ]\n  );\n  const getSelectorIconProps = useCallback(\n    () => ({\n      \"data-slot\": \"selectorIcon\",\n      \"aria-hidden\": dataAttr(true),\n      \"data-open\": dataAttr(state.isOpen),\n      className: slots.selectorIcon({ class: classNames == null ? void 0 : classNames.selectorIcon })\n    }),\n    [slots, classNames == null ? void 0 : classNames.selectorIcon, state.isOpen]\n  );\n  const getInnerWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"innerWrapper\",\n        className: slots.innerWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.innerWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.innerWrapper]\n  );\n  const getHelperWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"helperWrapper\",\n        className: slots.helperWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.helperWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.helperWrapper]\n  );\n  const getDescriptionProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ...descriptionProps,\n        \"data-slot\": \"description\",\n        className: slots.description({ class: clsx(classNames == null ? void 0 : classNames.description, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.description]\n  );\n  const getMainWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"mainWrapper\",\n        className: slots.mainWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.mainWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.mainWrapper]\n  );\n  const getEndWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"end-wrapper\",\n        className: slots.endWrapper({\n          class: clsx(classNames == null ? void 0 : classNames.endWrapper, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.endWrapper]\n  );\n  const getEndContentProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"data-slot\": \"end-content\",\n        className: slots.endContent({\n          class: clsx(classNames == null ? void 0 : classNames.endContent, props2 == null ? void 0 : props2.className)\n        })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.endContent]\n  );\n  const getErrorMessageProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ...errorMessageProps,\n        \"data-slot\": \"error-message\",\n        className: slots.errorMessage({ class: clsx(classNames == null ? void 0 : classNames.errorMessage, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, errorMessageProps, classNames == null ? void 0 : classNames.errorMessage]\n  );\n  const getSpinnerProps = useCallback(\n    (props2 = {}) => {\n      return {\n        \"aria-hidden\": dataAttr(true),\n        \"data-slot\": \"spinner\",\n        color: \"current\",\n        size: \"sm\",\n        ...spinnerProps,\n        ...props2,\n        ref: spinnerRef,\n        className: slots.spinner({ class: clsx(classNames == null ? void 0 : classNames.spinner, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, spinnerRef, spinnerProps, classNames == null ? void 0 : classNames.spinner]\n  );\n  const getClearButtonProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        type: \"button\",\n        tabIndex: -1,\n        \"aria-label\": \"clear selection\",\n        \"data-slot\": \"clear-button\",\n        \"data-focus-visible\": dataAttr(isClearButtonFocusVisible),\n        className: slots.clearButton({ class: clsx(classNames == null ? void 0 : classNames.clearButton, props2 == null ? void 0 : props2.className) }),\n        ...mergeProps(clearPressProps, clearFocusProps)\n      };\n    },\n    [slots, isClearButtonFocusVisible, clearPressProps, clearFocusProps, classNames == null ? void 0 : classNames.clearButton]\n  );\n  selectData.set(state, {\n    isDisabled: originalProps == null ? void 0 : originalProps.isDisabled,\n    isRequired: originalProps == null ? void 0 : originalProps.isRequired,\n    name: originalProps == null ? void 0 : originalProps.name,\n    isInvalid,\n    validationBehavior\n  });\n  return {\n    Component,\n    domRef,\n    state,\n    label,\n    name,\n    triggerRef,\n    isLoading,\n    placeholder,\n    startContent,\n    endContent,\n    description,\n    selectorIcon,\n    hasHelper,\n    labelPlacement,\n    hasPlaceholder,\n    renderValue,\n    selectionMode,\n    disableAnimation,\n    isOutsideLeft,\n    shouldLabelBeOutside,\n    shouldLabelBeInside,\n    isInvalid,\n    errorMessage,\n    isClearable,\n    getClearButtonProps,\n    getBaseProps,\n    getTriggerProps,\n    getLabelProps,\n    getValueProps,\n    getListboxProps,\n    getPopoverProps,\n    getSpinnerProps,\n    getMainWrapperProps,\n    getListboxWrapperProps,\n    getHiddenSelectProps,\n    getInnerWrapperProps,\n    getHelperWrapperProps,\n    getDescriptionProps,\n    getErrorMessageProps,\n    getSelectorIconProps,\n    getEndWrapperProps,\n    getEndContentProps\n  };\n}\n\nexport {\n  selectData,\n  useSelect\n};\n"], "names": [], "mappings": ";;;;AAEA,oBAAoB;AACpB;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAdA;;;;;;;;;;;;;AAeA,IAAI,aAAa,aAAa,GAAG,IAAI;AACrC,SAAS,UAAU,aAAa;IAC9B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IACxB,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,EAAE,oBAAoB,sBAAsB,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,8JAAA,CAAA,cAAW,KAAK,CAAC;IAC1F,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,SAAM,CAAC,WAAW;IAChF,MAAM,mBAAmB,CAAC,KAAK,CAAC,KAAK,cAAc,gBAAgB,KAAK,OAAO,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK;IACpK,MAAM,EACJ,GAAG,EACH,EAAE,EACF,KAAK,EACL,IAAI,EACJ,SAAS,EACT,YAAY,EACZ,MAAM,EACN,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,WAAW,EACX,aAAa,EACb,aAAa,EAAE,EACf,mBAAmB,GAAG,EACtB,QAAQ,EACR,yBAAyB,KAAK,EAC9B,gBAAgB,QAAQ,EACxB,UAAU,EACV,WAAW,aAAa,EACxB,eAAe,CAAC,CAAC,EACjB,oBAAoB,CAAC,CAAC,EACtB,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,EACjB,eAAe,EACf,QAAQ,EACR,OAAO,EACP,SAAS,EACT,UAAU,EACV,qBAAqB,CAAC,KAAK,0BAA0B,OAAO,yBAAyB,iBAAiB,OAAO,KAAK,IAAI,cAAc,kBAAkB,KAAK,OAAO,KAAK,QAAQ,EAC/K,mBAAmB,KAAK,EACxB,OAAO,EACP,GAAG,YACJ,GAAG;IACJ,MAAM,kBAAkB,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IAClC,MAAM,aAAa;QACjB,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACrB;YACE,WAAW;YACX,oBAAoB;YACpB,QAAQ;YACR;QACF,GACA;QAEF,mBAAmB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAC1B;YACE,KAAK;YACL,WAAW,CAAC,KAAK,cAAc,oBAAoB,KAAK,OAAO,KAAK;YACpE,eAAe;YACf,QAAQ;QACV,GACA;QAEF,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACrB;YACE;QACF,GACA;IAEJ;IACA,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,IAAI,QAAQ,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE;QAC9B,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA,YAAY,cAAc,UAAU;QACpC,YAAY,cAAc,UAAU;QACpC,WAAW,cAAc,SAAS;QAClC;QACA;QACA,cAAc,CAAC;YACb,gBAAgB,OAAO,KAAK,IAAI,aAAa;YAC7C,IAAI,CAAC,MAAM;gBACT,WAAW,OAAO,KAAK,IAAI;YAC7B;QACF;QACA,mBAAmB,CAAC;YAClB,qBAAqB,OAAO,KAAK,IAAI,kBAAkB;YACvD,IAAI,YAAY,OAAO,aAAa,YAAY;gBAC9C,SAAS;oBACP,QAAQ;wBACN,GAAG,OAAO,OAAO,IAAI;4BACnB,GAAG,OAAO,OAAO;4BACjB,MAAM,OAAO,OAAO,CAAC,IAAI;wBAC3B,CAAC;wBACD,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC;oBAC/B;gBACF;YACF;YACA,MAAM,gBAAgB;QACxB;IACF;IACA,QAAQ;QACN,GAAG,KAAK;QACR,GAAG,cAAc,UAAU,IAAI;YAC7B,cAAc,aAAa,GAAG,IAAI,IAAI;mBAAI,MAAM,UAAU,CAAC,OAAO;aAAG;QACvE,CAAC;IACH;IACA,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,MAAM,OAAO,OAAO,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK,GAAG;QAC5D,MAAM,eAAe,CAAC,aAAa,GAAG,IAAI,IAAI;eAAI,MAAM,YAAY;YAAE,OAAO,OAAO,CAAC,KAAK;SAAC;IAC7F,GAAG;QAAC,OAAO,OAAO;KAAC;IACnB,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,UAAU,EACV,SAAS,EACT,gBAAgB,EAChB,iBAAiB,EACjB,WAAW,aAAa,EACxB,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EACf;QAAE,GAAG,KAAK;QAAE;QAAwB,YAAY,cAAc,UAAU;IAAC,GACzE,OACA;IAEF,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;QACJ,MAAM,eAAe,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE;QAChD,WAAW,OAAO,KAAK,IAAI;QAC3B,CAAC,MAAM,WAAW,OAAO,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK;IACzD,GAAG;QAAC;QAAS;KAAM;IACnB,MAAM,EAAE,YAAY,eAAe,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,YAAY,CAAC,CAAC,CAAC,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QACxE,SAAS;IACX;IACA,MAAM,YAAY,cAAc,SAAS,IAAI,oBAAoB,aAAa;IAC9E,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;IAC/D,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAC7D,MAAM,EAAE,YAAY,eAAe,EAAE,gBAAgB,yBAAyB,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAC9F,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,YAAY,cAAc,UAAU;IAAC;IAClF,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;QACvC,gBAAgB,cAAc,cAAc;QAC5C;IACF;IACA,MAAM,iBAAiB,CAAC,CAAC;IACzB,MAAM,uBAAuB,mBAAmB,kBAAkB,mBAAmB;IACrF,MAAM,sBAAsB,mBAAmB;IAC/C,MAAM,gBAAgB,mBAAmB;IACzC,MAAM,cAAc,cAAc,WAAW;IAC7C,MAAM,WAAW,MAAM,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,aAAa,KAAK,OAAO,KAAK,IAAI,GAAG,MAAM,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,CAAC,cAAc,WAAW;IAC/K,MAAM,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,aAAa,KAAK,OAAO,KAAK,IAAI,GAAG,MAAM;IAC3E,MAAM,WAAW,CAAC,CAAC;IACnB,MAAM,kBAAkB,YAAY,CAAC,iBAAiB,wBAAwB,cAAc;IAC5F,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE;YACX,GAAG,YAAY;YACf;YACA;YACA;YACA;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;QAAW;QAAgB;KAAiB;IAE3E,CAAA,GAAA,wKAAA,CAAA,mBAAgB,AAAD,EAAE;QACf,YAAY,CAAC,MAAM,MAAM;IAC3B;IACA,MAAM,eAAe,OAAO,MAAM,YAAY,KAAK,aAAa,MAAM,YAAY,CAAC;QAAE;QAAW;QAAkB;IAAkB,KAAK,MAAM,YAAY,IAAI,CAAC,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,IAAI,CAAC,IAAI;IAC9N,MAAM,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC;IACrC,MAAM,gBAAgB,CAAC,CAAC;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,EAAE;YAC5D,IAAI,aAAa,WAAW,OAAO,CAAC,qBAAqB;YACzD,IAAI,UAAU,WAAW,OAAO;YAChC,QAAQ,KAAK,CAAC,KAAK,GAAG,WAAW,KAAK,GAAG;QAC3C;IACF,GAAG;QAAC,MAAM,MAAM;KAAC;IACjB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,aAAa;YACb,eAAe,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACxB,kBAAkB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC3B,kBAAkB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC3B,mBAAmB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC5B,wBAAwB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACjC,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,0BAA0B,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACnC,WAAW,MAAM,IAAI,CAAC;gBACpB,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,SAAS;YAC1C;YACA,GAAG,MAAM;QACX,CAAC,GACD;QAAC;QAAO;QAAW;QAAU;QAAU;QAAiB;QAAU;KAAW;IAE/E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,KAAK;YACL,aAAa;YACb,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM;YAClC,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;YACnF,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;YAAC;YACnF,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,aACA,YACA,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;gBACzB,SAAS;YACX,IACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,QAChB;QACH;IACF,GACA;QACE;QACA;QACA,MAAM,MAAM;QACZ,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;QAChD,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QACzD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAEH,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB;YACA;YACA,WAAW;YACX;YACA,OAAO,iBAAiB,OAAO,KAAK,IAAI,cAAc,KAAK;YAC3D,MAAM,iBAAiB,OAAO,KAAK,IAAI,cAAc,IAAI;YACzD,YAAY,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;YACrE,cAAc,iBAAiB,OAAO,KAAK,IAAI,cAAc,YAAY;YACzE,YAAY,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;YACrE,MAAM,iBAAiB,OAAO,KAAK,IAAI,cAAc,IAAI;YACzD;YACA,GAAG,MAAM;QACX,CAAC,GACD;QACE;QACA;QACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,KAAK;QACpD,iBAAiB,OAAO,KAAK,IAAI,cAAc,YAAY;QAC3D,iBAAiB,OAAO,KAAK,IAAI,cAAc,IAAI;QACnD,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QACzD;KACD;IAEH,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,aAAa;YACb,WAAW,MAAM,KAAK,CAAC;gBACrB,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,OAAO,SAAS;YAC9E;YACA,GAAG,UAAU;YACb,GAAG,MAAM;QACX,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;QAAE;KAAW;IAErE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,aAAa;YACb,WAAW,MAAM,KAAK,CAAC;gBACrB,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,OAAO,SAAS;YAC9E;YACA,GAAG,UAAU;YACb,GAAG,MAAM;QACX,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;QAAE;KAAW;IAErE,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,aAAa;YACb,WAAW,MAAM,cAAc,CAAC;gBAC9B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,cAAc,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YACjH;YACA,OAAO;gBACL,WAAW,oBAAoB,OAAO,mBAAmB;gBACzD,GAAG,OAAO,KAAK;YACjB;YACA,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,iBAAiB,EAAE,OAAO;QACrD,CAAC,GACD;QACE,MAAM,cAAc;QACpB,cAAc,OAAO,KAAK,IAAI,WAAW,cAAc;QACvD,WAAW,iBAAiB;QAC5B;KACD;IAEH,MAAM,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAClC,MAAM,mBAAmB,iBAAiB,OAAO,gBAAgB,MAAM,UAAU,CAAC,IAAI,GAAG;QACzF,OAAO;YACL;YACA,KAAK;YACL,eAAe;YACf,gBAAgB,mBAAmB;gBACjC;gBACA;YACF,IAAI,KAAK;YACT,aAAa;YACb,WAAW,MAAM,OAAO,CAAC;gBACvB,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAC1G;YACA,mBAAmB,WAAW,iBAAiB;YAC/C,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,YAAY,EAAE,QAAQ,UAAU;QAC3D;IACF;IACA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAS,CAAC,CAAC;QACV,IAAI,KAAK;QACT,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,YAAY,EAAE;QAC1D,OAAO;YACL;YACA;YACA,KAAK;YACL,aAAa;YACb,WAAW;YACX,aAAa;YACb,YAAY;gBACV,SAAS,MAAM,cAAc,CAAC;oBAC5B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,cAAc,EAAE,OAAO,SAAS;gBACvF;YACF;YACA,GAAG,aAAa;YAChB,QAAQ,MAAM,aAAa,IAAI,MAAM,aAAa,CAAC,MAAM,GAAG,IAC1D,2EAA2E;YAC3E,MAAM,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,WAAW,YAAY,KAAK,OAAO,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,IACvG,CAAC,MAAM,WAAW,YAAY,KAAK,OAAO,KAAK,IAAI,IAAI,MAAM;QACnE;IACF,GACA;QACE;QACA,cAAc,OAAO,KAAK,IAAI,WAAW,cAAc;QACvD,WAAW,YAAY;QACvB;QACA;QACA,MAAM,aAAa;KACpB;IAEH,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,IAAM,CAAC;YACL,aAAa;YACb,eAAe,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACxB,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,MAAM;YAClC,WAAW,MAAM,YAAY,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;YAAC;QAC/F,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;QAAE,MAAM,MAAM;KAAC;IAE9E,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,aAAa;YACb,WAAW,MAAM,YAAY,CAAC;gBAC5B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAC/G;QACF;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;KAAC;IAEhE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,aAAa;YACb,WAAW,MAAM,aAAa,CAAC;gBAC7B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,aAAa,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAChH;QACF;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,aAAa;KAAC;IAEjE,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,GAAG,gBAAgB;YACnB,aAAa;YACb,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QAC/I;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;KAAC;IAE/D,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,aAAa;YACb,WAAW,MAAM,WAAW,CAAC;gBAC3B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAC9G;QACF;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;KAAC;IAE/D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,aAAa;YACb,WAAW,MAAM,UAAU,CAAC;gBAC1B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,UAAU,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAC7G;QACF;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,UAAU;KAAC;IAE9D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,aAAa;YACb,WAAW,MAAM,UAAU,CAAC;gBAC1B,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,UAAU,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAC7G;QACF;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,UAAU;KAAC;IAE9D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,GAAG,iBAAiB;YACpB,aAAa;YACb,WAAW,MAAM,YAAY,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QACjJ;IACF,GACA;QAAC;QAAO;QAAmB,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;KAAC;IAEnF,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,eAAe,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACxB,aAAa;YACb,OAAO;YACP,MAAM;YACN,GAAG,YAAY;YACf,GAAG,MAAM;YACT,KAAK;YACL,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QACvI;IACF,GACA;QAAC;QAAO;QAAY;QAAc,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;KAAC;IAErF,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,MAAM;YACN,UAAU,CAAC;YACX,cAAc;YACd,aAAa;YACb,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;YAC7I,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,gBAAgB;QACjD;IACF,GACA;QAAC;QAAO;QAA2B;QAAiB;QAAiB,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;KAAC;IAE5H,WAAW,GAAG,CAAC,OAAO;QACpB,YAAY,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QACrE,YAAY,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QACrE,MAAM,iBAAiB,OAAO,KAAK,IAAI,cAAc,IAAI;QACzD;QACA;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3438, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/select/dist/chunk-EYCVA6TZ.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  selectData\n} from \"./chunk-XAZU4BXN.mjs\";\n\n// src/hidden-select.tsx\nimport { useFormReset } from \"@heroui/use-form-reset\";\nimport { useVisuallyHidden } from \"@react-aria/visually-hidden\";\nimport { useFormValidation } from \"@react-aria/form\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nfunction useHiddenSelect(props, state, triggerRef) {\n  var _a;\n  let data = selectData.get(state) || {};\n  let {\n    autoComplete,\n    name = data.name,\n    isDisabled = data.isDisabled,\n    selectionMode,\n    onChange,\n    form\n  } = props;\n  let { validationBehavior, isRequired, isInvalid } = data;\n  let { visuallyHiddenProps } = useVisuallyHidden();\n  useFormReset(props.selectRef, state.selectedKeys, state.setSelectedKeys);\n  useFormValidation(\n    {\n      validationBehavior,\n      focus: () => {\n        var _a2;\n        return (_a2 = triggerRef.current) == null ? void 0 : _a2.focus();\n      }\n    },\n    state,\n    props.selectRef\n  );\n  return {\n    containerProps: {\n      ...visuallyHiddenProps,\n      \"aria-hidden\": true,\n      [\"data-a11y-ignore\"]: \"aria-hidden-focus\"\n    },\n    inputProps: {\n      style: { display: \"none\" }\n    },\n    selectProps: {\n      form,\n      autoComplete,\n      disabled: isDisabled,\n      \"aria-invalid\": isInvalid || void 0,\n      \"aria-required\": isRequired && validationBehavior === \"aria\" || void 0,\n      required: isRequired && validationBehavior === \"native\",\n      name,\n      tabIndex: -1,\n      value: selectionMode === \"multiple\" ? [...state.selectedKeys].map((k) => String(k)) : (_a = [...state.selectedKeys][0]) != null ? _a : \"\",\n      multiple: selectionMode === \"multiple\",\n      onChange: (e) => {\n        state.setSelectedKeys(e.target.value);\n        onChange == null ? void 0 : onChange(e);\n      }\n    }\n  };\n}\nfunction HiddenSelect(props) {\n  var _a;\n  let { state, triggerRef, selectRef, label, name, isDisabled, form } = props;\n  let { containerProps, selectProps } = useHiddenSelect({ ...props, selectRef }, state, triggerRef);\n  if (state.collection.size <= 300) {\n    return /* @__PURE__ */ jsx(\"div\", { ...containerProps, \"data-testid\": \"hidden-select-container\", children: /* @__PURE__ */ jsxs(\"label\", { children: [\n      label,\n      /* @__PURE__ */ jsxs(\"select\", { ...selectProps, ref: selectRef, children: [\n        /* @__PURE__ */ jsx(\"option\", {}),\n        [...state.collection.getKeys()].map((key) => {\n          let item = state.collection.getItem(key);\n          if ((item == null ? void 0 : item.type) === \"item\") {\n            return /* @__PURE__ */ jsx(\"option\", { value: item.key, children: item.textValue }, item.key);\n          }\n        })\n      ] })\n    ] }) });\n  } else if (name) {\n    return /* @__PURE__ */ jsx(\n      \"input\",\n      {\n        autoComplete: selectProps.autoComplete,\n        disabled: isDisabled,\n        form,\n        name,\n        type: \"hidden\",\n        value: (_a = [...state.selectedKeys].join(\",\")) != null ? _a : \"\"\n      }\n    );\n  }\n  return null;\n}\n\nexport {\n  useHiddenSelect,\n  HiddenSelect\n};\n"], "names": [], "mappings": ";;;;AACA;AAIA,wBAAwB;AACxB;AACA;AACA;AACA;AATA;;;;;;AAUA,SAAS,gBAAgB,KAAK,EAAE,KAAK,EAAE,UAAU;IAC/C,IAAI;IACJ,IAAI,OAAO,gKAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UAAU,CAAC;IACrC,IAAI,EACF,YAAY,EACZ,OAAO,KAAK,IAAI,EAChB,aAAa,KAAK,UAAU,EAC5B,aAAa,EACb,QAAQ,EACR,IAAI,EACL,GAAG;IACJ,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG;IACpD,IAAI,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,gLAAA,CAAA,oBAAiB,AAAD;IAC9C,CAAA,GAAA,kKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS,EAAE,MAAM,YAAY,EAAE,MAAM,eAAe;IACvE,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD,EACd;QACE;QACA,OAAO;YACL,IAAI;YACJ,OAAO,CAAC,MAAM,WAAW,OAAO,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK;QAChE;IACF,GACA,OACA,MAAM,SAAS;IAEjB,OAAO;QACL,gBAAgB;YACd,GAAG,mBAAmB;YACtB,eAAe;YACf,CAAC,mBAAmB,EAAE;QACxB;QACA,YAAY;YACV,OAAO;gBAAE,SAAS;YAAO;QAC3B;QACA,aAAa;YACX;YACA;YACA,UAAU;YACV,gBAAgB,aAAa,KAAK;YAClC,iBAAiB,cAAc,uBAAuB,UAAU,KAAK;YACrE,UAAU,cAAc,uBAAuB;YAC/C;YACA,UAAU,CAAC;YACX,OAAO,kBAAkB,aAAa;mBAAI,MAAM,YAAY;aAAC,CAAC,GAAG,CAAC,CAAC,IAAM,OAAO,MAAM,CAAC,KAAK;mBAAI,MAAM,YAAY;aAAC,CAAC,EAAE,KAAK,OAAO,KAAK;YACvI,UAAU,kBAAkB;YAC5B,UAAU,CAAC;gBACT,MAAM,eAAe,CAAC,EAAE,MAAM,CAAC,KAAK;gBACpC,YAAY,OAAO,KAAK,IAAI,SAAS;YACvC;QACF;IACF;AACF;AACA,SAAS,aAAa,KAAK;IACzB,IAAI;IACJ,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;IACtE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,gBAAgB;QAAE,GAAG,KAAK;QAAE;IAAU,GAAG,OAAO;IACtF,IAAI,MAAM,UAAU,CAAC,IAAI,IAAI,KAAK;QAChC,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAAE,GAAG,cAAc;YAAE,eAAe;YAA2B,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,SAAS;gBAAE,UAAU;oBACnJ;oBACA,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,UAAU;wBAAE,GAAG,WAAW;wBAAE,KAAK;wBAAW,UAAU;4BACzE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU,CAAC;4BAC/B;mCAAI,MAAM,UAAU,CAAC,OAAO;6BAAG,CAAC,GAAG,CAAC,CAAC;gCACnC,IAAI,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;gCACpC,IAAI,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,QAAQ;oCAClD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;wCAAE,OAAO,KAAK,GAAG;wCAAE,UAAU,KAAK,SAAS;oCAAC,GAAG,KAAK,GAAG;gCAC9F;4BACF;yBACD;oBAAC;iBACH;YAAC;QAAG;IACP,OAAO,IAAI,MAAM;QACf,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,SACA;YACE,cAAc,YAAY,YAAY;YACtC,UAAU;YACV;YACA;YACA,MAAM;YACN,OAAO,CAAC,KAAK;mBAAI,MAAM,YAAY;aAAC,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,KAAK;QACjE;IAEJ;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3555, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/select/dist/chunk-Y2AYO5NJ.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  HiddenSelect\n} from \"./chunk-EYCVA6TZ.mjs\";\nimport {\n  useSelect\n} from \"./chunk-XAZU4BXN.mjs\";\n\n// src/select.tsx\nimport { Listbox } from \"@heroui/listbox\";\nimport { FreeSoloPopover } from \"@heroui/popover\";\nimport { ChevronDownIcon, CloseFilledIcon } from \"@heroui/shared-icons\";\nimport { Spinner } from \"@heroui/spinner\";\nimport { useMemo } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { ScrollShadow } from \"@heroui/scroll-shadow\";\nimport { cloneElement } from \"react\";\nimport { VisuallyHidden } from \"@react-aria/visually-hidden\";\nimport { AnimatePresence } from \"framer-motion\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Select = forwardRef(function Select2(props, ref) {\n  var _a;\n  const {\n    Component,\n    state,\n    label,\n    hasHelper,\n    isLoading,\n    triggerRef,\n    selectorIcon = /* @__PURE__ */ jsx(ChevronDownIcon, {}),\n    description,\n    errorMessage,\n    isInvalid,\n    startContent,\n    endContent,\n    placeholder,\n    renderValue,\n    shouldLabelBeOutside,\n    disableAnimation,\n    getBaseProps,\n    getLabelProps,\n    getTriggerProps,\n    getValueProps,\n    getListboxProps,\n    getPopoverProps,\n    getSpinnerProps,\n    getMainWrapperProps,\n    getInnerWrapperProps,\n    getHiddenSelectProps,\n    getHelperWrapperProps,\n    getListboxWrapperProps,\n    getDescriptionProps,\n    getErrorMessageProps,\n    getSelectorIconProps,\n    isClearable,\n    getClearButtonProps,\n    getEndWrapperProps,\n    getEndContentProps\n  } = useSelect({ ...props, ref });\n  const labelContent = label ? /* @__PURE__ */ jsx(\"label\", { ...getLabelProps(), children: label }) : null;\n  const clonedIcon = cloneElement(selectorIcon, getSelectorIconProps());\n  const clearButton = useMemo(() => {\n    var _a2;\n    if (isClearable && ((_a2 = state.selectedItems) == null ? void 0 : _a2.length)) {\n      return /* @__PURE__ */ jsx(\"span\", { ...getClearButtonProps(), children: /* @__PURE__ */ jsx(CloseFilledIcon, {}) });\n    }\n    return null;\n  }, [isClearable, getClearButtonProps, (_a = state.selectedItems) == null ? void 0 : _a.length]);\n  const end = useMemo(() => {\n    if (clearButton) {\n      return /* @__PURE__ */ jsxs(\"div\", { ...getEndWrapperProps(), children: [\n        clearButton,\n        endContent && /* @__PURE__ */ jsx(\"span\", { ...getEndContentProps(), children: endContent })\n      ] });\n    }\n    return endContent && /* @__PURE__ */ jsx(\"span\", { ...getEndContentProps(), children: endContent });\n  }, [clearButton, endContent, getEndWrapperProps, getEndContentProps]);\n  const helperWrapper = useMemo(() => {\n    const shouldShowError = isInvalid && errorMessage;\n    const hasContent = shouldShowError || description;\n    if (!hasHelper || !hasContent) return null;\n    return /* @__PURE__ */ jsx(\"div\", { ...getHelperWrapperProps(), children: shouldShowError ? /* @__PURE__ */ jsx(\"div\", { ...getErrorMessageProps(), children: errorMessage }) : /* @__PURE__ */ jsx(\"div\", { ...getDescriptionProps(), children: description }) });\n  }, [\n    hasHelper,\n    isInvalid,\n    errorMessage,\n    description,\n    getHelperWrapperProps,\n    getErrorMessageProps,\n    getDescriptionProps\n  ]);\n  const renderSelectedItem = useMemo(() => {\n    var _a2;\n    if (!((_a2 = state.selectedItems) == null ? void 0 : _a2.length)) return placeholder;\n    if (renderValue && typeof renderValue === \"function\") {\n      const mappedItems = [...state.selectedItems].map((item) => ({\n        key: item.key,\n        data: item.value,\n        type: item.type,\n        props: item.props,\n        textValue: item.textValue,\n        rendered: item.rendered,\n        \"aria-label\": item[\"aria-label\"]\n      }));\n      return renderValue(mappedItems);\n    }\n    return state.selectedItems.map((item) => item.textValue).join(\", \");\n  }, [state.selectedItems, renderValue, placeholder]);\n  const renderIndicator = useMemo(() => {\n    if (isLoading) {\n      return /* @__PURE__ */ jsx(Spinner, { ...getSpinnerProps() });\n    }\n    return clonedIcon;\n  }, [isLoading, clonedIcon, getSpinnerProps]);\n  const popoverContent = useMemo(\n    () => state.isOpen ? /* @__PURE__ */ jsx(FreeSoloPopover, { ...getPopoverProps(), children: /* @__PURE__ */ jsx(ScrollShadow, { ...getListboxWrapperProps(), children: /* @__PURE__ */ jsx(Listbox, { ...getListboxProps() }) }) }) : null,\n    [state.isOpen, getPopoverProps, state, triggerRef, getListboxWrapperProps, getListboxProps]\n  );\n  return /* @__PURE__ */ jsxs(\"div\", { ...getBaseProps(), children: [\n    /* @__PURE__ */ jsx(HiddenSelect, { ...getHiddenSelectProps() }),\n    shouldLabelBeOutside ? labelContent : null,\n    /* @__PURE__ */ jsxs(\"div\", { ...getMainWrapperProps(), children: [\n      /* @__PURE__ */ jsxs(Component, { ...getTriggerProps(), children: [\n        !shouldLabelBeOutside ? labelContent : null,\n        /* @__PURE__ */ jsxs(\"div\", { ...getInnerWrapperProps(), children: [\n          startContent,\n          /* @__PURE__ */ jsx(\"span\", { ...getValueProps(), children: renderSelectedItem }),\n          endContent && state.selectedItems && /* @__PURE__ */ jsx(VisuallyHidden, { elementType: \"span\", children: \",\" }),\n          end\n        ] }),\n        renderIndicator\n      ] }),\n      helperWrapper\n    ] }),\n    disableAnimation ? popoverContent : /* @__PURE__ */ jsx(AnimatePresence, { children: popoverContent })\n  ] });\n});\nvar select_default = Select;\n\nexport {\n  select_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,iBAAiB;AACjB;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAnBA;;;;;;;;;;;;;;AAoBA,IAAI,SAAS,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,SAAS,QAAQ,KAAK,EAAE,GAAG;IACjD,IAAI;IACJ,MAAM,EACJ,SAAS,EACT,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,EACT,UAAU,EACV,eAAe,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,yKAAA,CAAA,kBAAe,EAAE,CAAC,EAAE,EACvD,WAAW,EACX,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,UAAU,EACV,WAAW,EACX,WAAW,EACX,oBAAoB,EACpB,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,eAAe,EACf,aAAa,EACb,eAAe,EACf,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACpB,oBAAoB,EACpB,qBAAqB,EACrB,sBAAsB,EACtB,mBAAmB,EACnB,oBAAoB,EACpB,oBAAoB,EACpB,WAAW,EACX,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;IAAI;IAC9B,MAAM,eAAe,QAAQ,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,SAAS;QAAE,GAAG,eAAe;QAAE,UAAU;IAAM,KAAK;IACrG,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,cAAc;IAC9C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI;QACJ,IAAI,eAAe,CAAC,CAAC,MAAM,MAAM,aAAa,KAAK,OAAO,KAAK,IAAI,IAAI,MAAM,GAAG;YAC9E,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,qBAAqB;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,yKAAA,CAAA,kBAAe,EAAE,CAAC;YAAG;QACpH;QACA,OAAO;IACT,GAAG;QAAC;QAAa;QAAqB,CAAC,KAAK,MAAM,aAAa,KAAK,OAAO,KAAK,IAAI,GAAG,MAAM;KAAC;IAC9F,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAClB,IAAI,aAAa;YACf,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;gBAAE,GAAG,oBAAoB;gBAAE,UAAU;oBACtE;oBACA,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wBAAE,GAAG,oBAAoB;wBAAE,UAAU;oBAAW;iBAC3F;YAAC;QACJ;QACA,OAAO,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,GAAG,oBAAoB;YAAE,UAAU;QAAW;IACnG,GAAG;QAAC;QAAa;QAAY;QAAoB;KAAmB;IACpE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,kBAAkB,aAAa;QACrC,MAAM,aAAa,mBAAmB;QACtC,IAAI,CAAC,aAAa,CAAC,YAAY,OAAO;QACtC,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAAE,GAAG,uBAAuB;YAAE,UAAU,kBAAkB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,sBAAsB;gBAAE,UAAU;YAAa,KAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,qBAAqB;gBAAE,UAAU;YAAY;QAAG;IAClQ,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,MAAM,MAAM,aAAa,KAAK,OAAO,KAAK,IAAI,IAAI,MAAM,GAAG,OAAO;QACzE,IAAI,eAAe,OAAO,gBAAgB,YAAY;YACpD,MAAM,cAAc;mBAAI,MAAM,aAAa;aAAC,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC1D,KAAK,KAAK,GAAG;oBACb,MAAM,KAAK,KAAK;oBAChB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,cAAc,IAAI,CAAC,aAAa;gBAClC,CAAC;YACD,OAAO,YAAY;QACrB;QACA,OAAO,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,SAAS,EAAE,IAAI,CAAC;IAChE,GAAG;QAAC,MAAM,aAAa;QAAE;QAAa;KAAY;IAClD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,WAAW;YACb,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,+MAAA,CAAA,UAAO,EAAE;gBAAE,GAAG,iBAAiB;YAAC;QAC7D;QACA,OAAO;IACT,GAAG;QAAC;QAAW;QAAY;KAAgB;IAC3C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC3B,IAAM,MAAM,MAAM,GAAG,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iOAAA,CAAA,kBAAe,EAAE;YAAE,GAAG,iBAAiB;YAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,mOAAA,CAAA,eAAY,EAAE;gBAAE,GAAG,wBAAwB;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,+MAAA,CAAA,UAAO,EAAE;oBAAE,GAAG,iBAAiB;gBAAC;YAAG;QAAG,KAAK,MACtO;QAAC,MAAM,MAAM;QAAE;QAAiB;QAAO;QAAY;QAAwB;KAAgB;IAE7F,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,GAAG,cAAc;QAAE,UAAU;YAChE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gKAAA,CAAA,eAAY,EAAE;gBAAE,GAAG,sBAAsB;YAAC;YAC9D,uBAAuB,eAAe;YACtC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;gBAAE,GAAG,qBAAqB;gBAAE,UAAU;oBAChE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;wBAAE,GAAG,iBAAiB;wBAAE,UAAU;4BAChE,CAAC,uBAAuB,eAAe;4BACvC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;gCAAE,GAAG,sBAAsB;gCAAE,UAAU;oCACjE;oCACA,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wCAAE,GAAG,eAAe;wCAAE,UAAU;oCAAmB;oCAC/E,cAAc,MAAM,aAAa,IAAI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gLAAA,CAAA,iBAAc,EAAE;wCAAE,aAAa;wCAAQ,UAAU;oCAAI;oCAC9G;iCACD;4BAAC;4BACF;yBACD;oBAAC;oBACF;iBACD;YAAC;YACF,mBAAmB,iBAAiB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,yLAAA,CAAA,kBAAe,EAAE;gBAAE,UAAU;YAAe;SACrG;IAAC;AACJ;AACA,IAAI,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3763, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-form-reset/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport React, { useEffect, useRef, useCallback } from \"react\";\nvar useLayoutEffect = typeof document !== \"undefined\" ? React.useLayoutEffect : () => {\n};\nfunction useEffectEvent(fn) {\n  const ref = useRef(null);\n  useLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  return useCallback((...args) => {\n    const f = ref.current;\n    return f == null ? void 0 : f(...args);\n  }, []);\n}\nfunction useFormReset(ref, initialValue, onReset) {\n  let resetValue = useRef(initialValue);\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(resetValue.current);\n    }\n  });\n  useEffect(() => {\n    var _a;\n    let form = (_a = ref == null ? void 0 : ref.current) == null ? void 0 : _a.form;\n    form == null ? void 0 : form.addEventListener(\"reset\", handleReset);\n    return () => {\n      form == null ? void 0 : form.removeEventListener(\"reset\", handleReset);\n    };\n  }, [ref, handleReset]);\n}\nexport {\n  useEffectEvent,\n  useFormReset,\n  useLayoutEffect\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;;AACf;;AACA,IAAI,kBAAkB,OAAO,aAAa,cAAc,qMAAA,CAAA,UAAK,CAAC,eAAe,GAAG,KAChF;AACA,SAAS,eAAe,EAAE;IACxB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,gBAAgB;QACd,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAG;IACP,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,GAAG;QACrB,MAAM,IAAI,IAAI,OAAO;QACrB,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK;IACnC,GAAG,EAAE;AACP;AACA,SAAS,aAAa,GAAG,EAAE,YAAY,EAAE,OAAO;IAC9C,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,IAAI,cAAc,eAAe;QAC/B,IAAI,SAAS;YACX,QAAQ,WAAW,OAAO;QAC5B;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,OAAO,CAAC,KAAK,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI;QAC/E,QAAQ,OAAO,KAAK,IAAI,KAAK,gBAAgB,CAAC,SAAS;QACvD,OAAO;YACL,QAAQ,OAAO,KAAK,IAAI,KAAK,mBAAmB,CAAC,SAAS;QAC5D;IACF,GAAG;QAAC;QAAK;KAAY;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3808, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-MZOWMNSQ.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-listbox.ts\nimport { useListBox as useAriaListbox } from \"@react-aria/listbox\";\nimport { useProviderContext } from \"@heroui/system\";\nimport { listbox } from \"@heroui/theme\";\nimport { useListState } from \"@react-stately/list\";\nimport { filterDOMProps, useDOMRef } from \"@heroui/react-utils\";\nimport { useMemo } from \"react\";\nimport { clsx } from \"@heroui/shared-utils\";\nfunction useListbox(props) {\n  var _a;\n  const globalContext = useProviderContext();\n  const {\n    ref,\n    as,\n    state: propState,\n    variant,\n    color,\n    onAction,\n    children,\n    onSelectionChange,\n    disableAnimation = (_a = globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _a : false,\n    itemClasses,\n    className,\n    topContent,\n    bottomContent,\n    emptyContent = \"No items.\",\n    hideSelectedIcon = false,\n    hideEmptyContent = false,\n    shouldHighlightOnFocus = false,\n    classNames,\n    ...otherProps\n  } = props;\n  const Component = as || \"ul\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const innerState = useListState({ ...props, children, onSelectionChange });\n  const state = propState || innerState;\n  const { listBoxProps } = useAriaListbox({ ...props, onAction }, state, domRef);\n  const slots = useMemo(() => listbox(), []);\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const getBaseProps = (props2 = {}) => {\n    return {\n      ref: domRef,\n      \"data-slot\": \"base\",\n      className: slots.base({ class: baseStyles }),\n      ...filterDOMProps(otherProps, {\n        enabled: shouldFilterDOMProps\n      }),\n      ...props2\n    };\n  };\n  const getListProps = (props2 = {}) => {\n    return {\n      \"data-slot\": \"list\",\n      className: slots.list({ class: classNames == null ? void 0 : classNames.list }),\n      ...listBoxProps,\n      ...props2\n    };\n  };\n  const getEmptyContentProps = (props2 = {}) => {\n    return {\n      \"data-slot\": \"empty-content\",\n      children: emptyContent,\n      className: slots.emptyContent({ class: classNames == null ? void 0 : classNames.emptyContent }),\n      ...props2\n    };\n  };\n  return {\n    Component,\n    state,\n    variant,\n    color,\n    slots,\n    classNames,\n    topContent,\n    bottomContent,\n    emptyContent,\n    hideEmptyContent,\n    shouldHighlightOnFocus,\n    hideSelectedIcon,\n    disableAnimation,\n    className,\n    itemClasses,\n    getBaseProps,\n    getListProps,\n    getEmptyContentProps\n  };\n}\n\nexport {\n  useListbox\n};\n"], "names": [], "mappings": ";;;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AATA;;;;;;;;AAUA,SAAS,WAAW,KAAK;IACvB,IAAI;IACJ,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,EACJ,GAAG,EACH,EAAE,EACF,OAAO,SAAS,EAChB,OAAO,EACP,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,iBAAiB,EACjB,mBAAmB,CAAC,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK,KAAK,EAC9G,WAAW,EACX,SAAS,EACT,UAAU,EACV,aAAa,EACb,eAAe,WAAW,EAC1B,mBAAmB,KAAK,EACxB,mBAAmB,KAAK,EACxB,yBAAyB,KAAK,EAC9B,UAAU,EACV,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,mKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;QAAU;IAAkB;IACxE,MAAM,QAAQ,aAAa;IAC3B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,aAAc,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;IAAS,GAAG,OAAO;IACvE,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,kMAAA,CAAA,UAAO,AAAD,KAAK,EAAE;IACzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,eAAe,CAAC,SAAS,CAAC,CAAC;QAC/B,OAAO;YACL,KAAK;YACL,aAAa;YACb,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO;YAAW;YAC1C,GAAG,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;gBAC5B,SAAS;YACX,EAAE;YACF,GAAG,MAAM;QACX;IACF;IACA,MAAM,eAAe,CAAC,SAAS,CAAC,CAAC;QAC/B,OAAO;YACL,aAAa;YACb,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI;YAAC;YAC7E,GAAG,YAAY;YACf,GAAG,MAAM;QACX;IACF;IACA,MAAM,uBAAuB,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO;YACL,aAAa;YACb,UAAU;YACV,WAAW,MAAM,YAAY,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;YAAC;YAC7F,GAAG,MAAM;QACX;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3906, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-65JTUBIW.mjs"], "sourcesContent": ["\"use client\";\n\n// src/listbox-selected-icon.tsx\nimport { jsx } from \"react/jsx-runtime\";\nfunction ListboxSelectedIcon(props) {\n  const { isSelected, disableAnimation, ...otherProps } = props;\n  return /* @__PURE__ */ jsx(\n    \"svg\",\n    {\n      \"aria-hidden\": \"true\",\n      \"data-selected\": isSelected,\n      role: \"presentation\",\n      viewBox: \"0 0 17 18\",\n      ...otherProps,\n      children: /* @__PURE__ */ jsx(\n        \"polyline\",\n        {\n          fill: \"none\",\n          points: \"1 9 7 14 15 4\",\n          stroke: \"currentColor\",\n          strokeDasharray: 22,\n          strokeDashoffset: isSelected ? 44 : 66,\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 1.5,\n          style: !disableAnimation ? {\n            transition: \"stroke-dashoffset 200ms ease\"\n          } : {}\n        }\n      )\n    }\n  );\n}\n\nexport {\n  ListboxSelectedIcon\n};\n"], "names": [], "mappings": ";;;AAEA,gCAAgC;AAChC;AAHA;;AAIA,SAAS,oBAAoB,KAAK;IAChC,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,YAAY,GAAG;IACxD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,OACA;QACE,eAAe;QACf,iBAAiB;QACjB,MAAM;QACN,SAAS;QACT,GAAG,UAAU;QACb,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC1B,YACA;YACE,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,iBAAiB;YACjB,kBAAkB,aAAa,KAAK;YACpC,eAAe;YACf,gBAAgB;YAChB,aAAa;YACb,OAAO,CAAC,mBAAmB;gBACzB,YAAY;YACd,IAAI,CAAC;QACP;IAEJ;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3941, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-RMKUVOBG.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-listbox-item.ts\nimport { useMemo, useRef, useCallback } from \"react\";\nimport { listboxItem } from \"@heroui/theme\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, objectToDeps, removeEvents, mergeProps } from \"@heroui/shared-utils\";\nimport { useOption } from \"@react-aria/listbox\";\nimport { useHover, usePress } from \"@react-aria/interactions\";\nimport { useIsMobile } from \"@heroui/use-is-mobile\";\nfunction useListboxItem(originalProps) {\n  var _a, _b;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, listboxItem.variantKeys);\n  const {\n    as,\n    item,\n    state,\n    description,\n    startContent,\n    endContent,\n    isVirtualized,\n    selectedIcon,\n    className,\n    classNames,\n    autoFocus,\n    onPress,\n    onPressUp,\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onClick,\n    shouldHighlightOnFocus,\n    hideSelectedIcon = false,\n    isReadOnly = false,\n    ...otherProps\n  } = props;\n  const disableAnimation = (_b = (_a = originalProps.disableAnimation) != null ? _a : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false;\n  const domRef = useRef(null);\n  const Component = as || (originalProps.href ? \"a\" : \"li\");\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const { rendered, key } = item;\n  const isDisabled = state.disabledKeys.has(key) || originalProps.isDisabled;\n  const isSelectable = state.selectionManager.selectionMode !== \"none\";\n  const isMobile = useIsMobile();\n  const { pressProps, isPressed } = usePress({\n    ref: domRef,\n    isDisabled,\n    onClick,\n    onPress,\n    onPressUp,\n    onPressStart,\n    onPressEnd,\n    onPressChange\n  });\n  const { isHovered, hoverProps } = useHover({\n    isDisabled\n  });\n  const { isFocusVisible, focusProps } = useFocusRing({\n    autoFocus\n  });\n  const { isFocused, isSelected, optionProps, labelProps, descriptionProps } = useOption(\n    {\n      key,\n      isDisabled,\n      \"aria-label\": props[\"aria-label\"],\n      isVirtualized\n    },\n    state,\n    domRef\n  );\n  let itemProps = optionProps;\n  const slots = useMemo(\n    () => listboxItem({\n      ...variantProps,\n      isDisabled,\n      disableAnimation,\n      hasTitleTextChild: typeof rendered === \"string\",\n      hasDescriptionTextChild: typeof description === \"string\"\n    }),\n    [objectToDeps(variantProps), isDisabled, disableAnimation, rendered, description]\n  );\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  if (isReadOnly) {\n    itemProps = removeEvents(itemProps);\n  }\n  const isHighlighted = shouldHighlightOnFocus && isFocused || (isMobile ? isHovered || isPressed : isHovered || isFocused && !isFocusVisible);\n  const getItemProps = (props2 = {}) => ({\n    ref: domRef,\n    ...mergeProps(\n      itemProps,\n      isReadOnly ? {} : mergeProps(focusProps, pressProps),\n      hoverProps,\n      filterDOMProps(otherProps, {\n        enabled: shouldFilterDOMProps\n      }),\n      props2\n    ),\n    \"data-selectable\": dataAttr(isSelectable),\n    \"data-focus\": dataAttr(isFocused),\n    \"data-hover\": dataAttr(isHighlighted),\n    \"data-disabled\": dataAttr(isDisabled),\n    \"data-selected\": dataAttr(isSelected),\n    \"data-pressed\": dataAttr(isPressed),\n    \"data-focus-visible\": dataAttr(isFocusVisible),\n    className: slots.base({ class: clsx(baseStyles, props2.className) })\n  });\n  const getLabelProps = (props2 = {}) => ({\n    ...mergeProps(labelProps, props2),\n    \"data-label\": dataAttr(true),\n    className: slots.title({ class: classNames == null ? void 0 : classNames.title })\n  });\n  const getDescriptionProps = (props2 = {}) => ({\n    ...mergeProps(descriptionProps, props2),\n    className: slots.description({ class: classNames == null ? void 0 : classNames.description })\n  });\n  const getWrapperProps = (props2 = {}) => ({\n    ...mergeProps(props2),\n    className: slots.wrapper({ class: classNames == null ? void 0 : classNames.wrapper })\n  });\n  const getSelectedIconProps = useCallback(\n    (props2 = {}) => {\n      return {\n        \"aria-hidden\": dataAttr(true),\n        \"data-disabled\": dataAttr(isDisabled),\n        className: slots.selectedIcon({ class: classNames == null ? void 0 : classNames.selectedIcon }),\n        ...props2\n      };\n    },\n    [isDisabled, slots, classNames]\n  );\n  return {\n    Component,\n    domRef,\n    slots,\n    classNames,\n    isSelectable,\n    isSelected,\n    isDisabled,\n    rendered,\n    description,\n    startContent,\n    endContent,\n    selectedIcon,\n    hideSelectedIcon,\n    disableAnimation,\n    getItemProps,\n    getLabelProps,\n    getWrapperProps,\n    getDescriptionProps,\n    getSelectedIconProps\n  };\n}\n\nexport {\n  useListboxItem\n};\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAXA;;;;;;;;;;AAYA,SAAS,eAAe,aAAa;IACnC,IAAI,IAAI;IACR,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,0MAAA,CAAA,cAAW,CAAC,WAAW;IACrF,MAAM,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACL,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,YAAY,EACZ,SAAS,EACT,UAAU,EACV,SAAS,EACT,OAAO,EACP,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,OAAO,EACP,sBAAsB,EACtB,mBAAmB,KAAK,EACxB,aAAa,KAAK,EAClB,GAAG,YACJ,GAAG;IACJ,MAAM,mBAAmB,CAAC,KAAK,CAAC,KAAK,cAAc,gBAAgB,KAAK,OAAO,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK;IACpK,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,YAAY,MAAM,CAAC,cAAc,IAAI,GAAG,MAAM,IAAI;IACxD,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG;IAC1B,MAAM,aAAa,MAAM,YAAY,CAAC,GAAG,CAAC,QAAQ,cAAc,UAAU;IAC1E,MAAM,eAAe,MAAM,gBAAgB,CAAC,aAAa,KAAK;IAC9D,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,KAAK;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QACzC;IACF;IACA,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAClD;IACF;IACA,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EACnF;QACE;QACA;QACA,cAAc,KAAK,CAAC,aAAa;QACjC;IACF,GACA,OACA;IAEF,IAAI,YAAY;IAChB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,0MAAA,CAAA,cAAW,AAAD,EAAE;YAChB,GAAG,YAAY;YACf;YACA;YACA,mBAAmB,OAAO,aAAa;YACvC,yBAAyB,OAAO,gBAAgB;QAClD,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;QAAY;QAAkB;QAAU;KAAY;IAEnF,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,IAAI,YAAY;QACd,YAAY,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;IAC3B;IACA,MAAM,gBAAgB,0BAA0B,aAAa,CAAC,WAAW,aAAa,YAAY,aAAa,aAAa,CAAC,cAAc;IAC3I,MAAM,eAAe,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YACrC,KAAK;YACL,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,WACA,aAAa,CAAC,IAAI,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,aACzC,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;gBACzB,SAAS;YACX,IACA,OACD;YACD,mBAAmB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC5B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,SAAS;YAAE;QACpE,CAAC;IACD,MAAM,gBAAgB,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YACtC,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,OAAO;YACjC,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,WAAW,MAAM,KAAK,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;YAAC;QACjF,CAAC;IACD,MAAM,sBAAsB,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAC5C,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB,OAAO;YACvC,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;YAAC;QAC7F,CAAC;IACD,MAAM,kBAAkB,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YACxC,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACrB,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;YAAC;QACrF,CAAC;IACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,eAAe,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACxB,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,WAAW,MAAM,YAAY,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;YAAC;YAC7F,GAAG,MAAM;QACX;IACF,GACA;QAAC;QAAY;QAAO;KAAW;IAEjC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4096, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-4QJLEUWX.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  ListboxSelectedIcon\n} from \"./chunk-65JTUBIW.mjs\";\nimport {\n  useListboxItem\n} from \"./chunk-RMKUVOBG.mjs\";\n\n// src/listbox-item.tsx\nimport { useMemo } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ListboxItem = (props) => {\n  const {\n    Component,\n    rendered,\n    description,\n    isSelectable,\n    isSelected,\n    isDisabled,\n    selectedIcon,\n    startContent,\n    endContent,\n    hideSelectedIcon,\n    disableAnimation,\n    getItemProps,\n    getLabelProps,\n    getWrapperProps,\n    getDescriptionProps,\n    getSelectedIconProps\n  } = useListboxItem(props);\n  const selectedContent = useMemo(() => {\n    const defaultIcon = /* @__PURE__ */ jsx(ListboxSelectedIcon, { disableAnimation, isSelected });\n    if (typeof selectedIcon === \"function\") {\n      return selectedIcon({ icon: defaultIcon, isSelected, isDisabled });\n    }\n    if (selectedIcon) return selectedIcon;\n    return defaultIcon;\n  }, [selectedIcon, isSelected, isDisabled, disableAnimation]);\n  return /* @__PURE__ */ jsxs(Component, { ...getItemProps(), children: [\n    startContent,\n    description ? /* @__PURE__ */ jsxs(\"div\", { ...getWrapperProps(), children: [\n      /* @__PURE__ */ jsx(\"span\", { ...getLabelProps(), children: rendered }),\n      /* @__PURE__ */ jsx(\"span\", { ...getDescriptionProps(), children: description })\n    ] }) : /* @__PURE__ */ jsx(\"span\", { ...getLabelProps(), children: rendered }),\n    isSelectable && !hideSelectedIcon && /* @__PURE__ */ jsx(\"span\", { ...getSelectedIconProps(), children: selectedContent }),\n    endContent\n  ] });\n};\nListboxItem.displayName = \"HeroUI.ListboxItem\";\nvar listbox_item_default = ListboxItem;\n\nexport {\n  listbox_item_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,uBAAuB;AACvB;AACA;AAVA;;;;;AAWA,IAAI,cAAc,CAAC;IACjB,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,UAAU,EACV,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACrB,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD,EAAE;IACnB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iKAAA,CAAA,sBAAmB,EAAE;YAAE;YAAkB;QAAW;QAC5F,IAAI,OAAO,iBAAiB,YAAY;YACtC,OAAO,aAAa;gBAAE,MAAM;gBAAa;gBAAY;YAAW;QAClE;QACA,IAAI,cAAc,OAAO;QACzB,OAAO;IACT,GAAG;QAAC;QAAc;QAAY;QAAY;KAAiB;IAC3D,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;QAAE,GAAG,cAAc;QAAE,UAAU;YACpE;YACA,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;gBAAE,GAAG,iBAAiB;gBAAE,UAAU;oBAC1E,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wBAAE,GAAG,eAAe;wBAAE,UAAU;oBAAS;oBACrE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wBAAE,GAAG,qBAAqB;wBAAE,UAAU;oBAAY;iBAC/E;YAAC,KAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,eAAe;gBAAE,UAAU;YAAS;YAC5E,gBAAgB,CAAC,oBAAoB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,sBAAsB;gBAAE,UAAU;YAAgB;YACxH;SACD;IAAC;AACJ;AACA,YAAY,WAAW,GAAG;AAC1B,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4166, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-BXSPTCK3.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  listbox_item_default\n} from \"./chunk-4QJLEUWX.mjs\";\n\n// src/listbox-section.tsx\nimport { listboxSection } from \"@heroui/theme\";\nimport { useMemo } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { clsx, mergeProps } from \"@heroui/shared-utils\";\nimport { Divider } from \"@heroui/divider\";\nimport { useListBoxSection } from \"@react-aria/listbox\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ListboxSection = forwardRef(\n  ({\n    item,\n    state,\n    as,\n    variant,\n    color,\n    disableAnimation,\n    className,\n    classNames,\n    hideSelectedIcon,\n    showDivider = false,\n    dividerProps = {},\n    itemClasses,\n    // removed title from props to avoid browsers showing a tooltip on hover\n    // the title props is already inside the rendered prop\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    title,\n    // removed items from props to avoid show in html element\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    items,\n    ...otherProps\n  }, _) => {\n    const Component = as || \"li\";\n    const slots = useMemo(() => listboxSection(), []);\n    const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n    const dividerStyles = clsx(classNames == null ? void 0 : classNames.divider, dividerProps == null ? void 0 : dividerProps.className);\n    const { itemProps, headingProps, groupProps } = useListBoxSection({\n      heading: item.rendered,\n      \"aria-label\": item[\"aria-label\"]\n    });\n    return /* @__PURE__ */ jsxs(\n      Component,\n      {\n        \"data-slot\": \"base\",\n        ...mergeProps(itemProps, otherProps),\n        className: slots.base({ class: baseStyles }),\n        children: [\n          item.rendered && /* @__PURE__ */ jsx(\n            \"span\",\n            {\n              ...headingProps,\n              className: slots.heading({ class: classNames == null ? void 0 : classNames.heading }),\n              \"data-slot\": \"heading\",\n              children: item.rendered\n            }\n          ),\n          /* @__PURE__ */ jsxs(\n            \"ul\",\n            {\n              ...groupProps,\n              className: slots.group({ class: classNames == null ? void 0 : classNames.group }),\n              \"data-has-title\": !!item.rendered,\n              \"data-slot\": \"group\",\n              children: [\n                [...item.childNodes].map((node) => {\n                  const { key: nodeKey, props: nodeProps } = node;\n                  let listboxItem = /* @__PURE__ */ jsx(\n                    listbox_item_default,\n                    {\n                      classNames: itemClasses,\n                      color,\n                      disableAnimation,\n                      hideSelectedIcon,\n                      item: node,\n                      state,\n                      variant,\n                      ...nodeProps\n                    },\n                    nodeKey\n                  );\n                  if (node.wrapper) {\n                    listboxItem = node.wrapper(listboxItem);\n                  }\n                  return listboxItem;\n                }),\n                showDivider && /* @__PURE__ */ jsx(\n                  Divider,\n                  {\n                    as: \"li\",\n                    className: slots.divider({\n                      class: dividerStyles\n                    }),\n                    ...dividerProps\n                  }\n                )\n              ]\n            }\n          )\n        ]\n      },\n      item.key\n    );\n  }\n);\nListboxSection.displayName = \"HeroUI.ListboxSection\";\nvar listbox_section_default = ListboxSection;\n\nexport {\n  listbox_section_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;AAaA,IAAI,iBAAiB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAC5B,CAAC,EACC,IAAI,EACJ,KAAK,EACL,EAAE,EACF,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,cAAc,KAAK,EACnB,eAAe,CAAC,CAAC,EACjB,WAAW,EACX,wEAAwE;AACxE,sDAAsD;AACtD,6DAA6D;AAC7D,KAAK,EACL,yDAAyD;AACzD,6DAA6D;AAC7D,KAAK,EACL,GAAG,YACJ,EAAE;IACD,MAAM,YAAY,MAAM;IACxB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,gNAAA,CAAA,iBAAc,AAAD,KAAK,EAAE;IAChD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS;IACnI,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD,EAAE;QAChE,SAAS,KAAK,QAAQ;QACtB,cAAc,IAAI,CAAC,aAAa;IAClC;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACxB,WACA;QACE,aAAa;QACb,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,WAAW;QACpC,WAAW,MAAM,IAAI,CAAC;YAAE,OAAO;QAAW;QAC1C,UAAU;YACR,KAAK,QAAQ,IAAI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACjC,QACA;gBACE,GAAG,YAAY;gBACf,WAAW,MAAM,OAAO,CAAC;oBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;gBAAC;gBACnF,aAAa;gBACb,UAAU,KAAK,QAAQ;YACzB;YAEF,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACjB,MACA;gBACE,GAAG,UAAU;gBACb,WAAW,MAAM,KAAK,CAAC;oBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;gBAAC;gBAC/E,kBAAkB,CAAC,CAAC,KAAK,QAAQ;gBACjC,aAAa;gBACb,UAAU;oBACR;2BAAI,KAAK,UAAU;qBAAC,CAAC,GAAG,CAAC,CAAC;wBACxB,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO,SAAS,EAAE,GAAG;wBAC3C,IAAI,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAClC,iKAAA,CAAA,uBAAoB,EACpB;4BACE,YAAY;4BACZ;4BACA;4BACA;4BACA,MAAM;4BACN;4BACA;4BACA,GAAG,SAAS;wBACd,GACA;wBAEF,IAAI,KAAK,OAAO,EAAE;4BAChB,cAAc,KAAK,OAAO,CAAC;wBAC7B;wBACA,OAAO;oBACT;oBACA,eAAe,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC/B,+MAAA,CAAA,UAAO,EACP;wBACE,IAAI;wBACJ,WAAW,MAAM,OAAO,CAAC;4BACvB,OAAO;wBACT;wBACA,GAAG,YAAY;oBACjB;iBAEH;YACH;SAEH;IACH,GACA,KAAK,GAAG;AAEZ;AAEF,eAAe,WAAW,GAAG;AAC7B,IAAI,0BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-7UIGGLV2.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  listbox_section_default\n} from \"./chunk-BXSPTCK3.mjs\";\nimport {\n  listbox_item_default\n} from \"./chunk-4QJLEUWX.mjs\";\n\n// src/virtualized-listbox.tsx\nimport { useMemo as useMemo2, useRef as useRef2, useState } from \"react\";\nimport { useVirtualizer } from \"@tanstack/react-virtual\";\nimport { isEmpty, mergeProps } from \"@heroui/shared-utils\";\n\n// ../scroll-shadow/src/use-scroll-shadow.ts\nimport { mapPropsVariants } from \"@heroui/system\";\nimport { scrollShadow } from \"@heroui/theme\";\nimport { useDOMRef } from \"@heroui/react-utils\";\n\n// ../../hooks/use-data-scroll-overflow/src/index.ts\nimport { capitalize } from \"@heroui/shared-utils\";\nimport { useEffect, useRef } from \"react\";\nfunction useDataScrollOverflow(props = {}) {\n  const {\n    domRef,\n    isEnabled = true,\n    overflowCheck = \"vertical\",\n    visibility = \"auto\",\n    offset = 0,\n    onVisibilityChange,\n    updateDeps = []\n  } = props;\n  const visibleRef = useRef(visibility);\n  useEffect(() => {\n    const el = domRef == null ? void 0 : domRef.current;\n    if (!el || !isEnabled) return;\n    const setAttributes = (direction, hasBefore, hasAfter, prefix, suffix) => {\n      if (visibility === \"auto\") {\n        const both = `${prefix}${capitalize(suffix)}Scroll`;\n        if (hasBefore && hasAfter) {\n          el.dataset[both] = \"true\";\n          el.removeAttribute(`data-${prefix}-scroll`);\n          el.removeAttribute(`data-${suffix}-scroll`);\n        } else {\n          el.dataset[`${prefix}Scroll`] = hasBefore.toString();\n          el.dataset[`${suffix}Scroll`] = hasAfter.toString();\n          el.removeAttribute(`data-${prefix}-${suffix}-scroll`);\n        }\n      } else {\n        const next = hasBefore && hasAfter ? \"both\" : hasBefore ? prefix : hasAfter ? suffix : \"none\";\n        if (next !== visibleRef.current) {\n          onVisibilityChange == null ? void 0 : onVisibilityChange(next);\n          visibleRef.current = next;\n        }\n      }\n    };\n    const checkOverflow = () => {\n      var _a, _b;\n      const directions = [\n        { type: \"vertical\", prefix: \"top\", suffix: \"bottom\" },\n        { type: \"horizontal\", prefix: \"left\", suffix: \"right\" }\n      ];\n      const listbox = el.querySelector('ul[data-slot=\"list\"]');\n      const scrollHeight = +((_a = listbox == null ? void 0 : listbox.getAttribute(\"data-virtual-scroll-height\")) != null ? _a : el.scrollHeight);\n      const scrollTop = +((_b = listbox == null ? void 0 : listbox.getAttribute(\"data-virtual-scroll-top\")) != null ? _b : el.scrollTop);\n      for (const { type, prefix, suffix } of directions) {\n        if (overflowCheck === type || overflowCheck === \"both\") {\n          const hasBefore = type === \"vertical\" ? scrollTop > offset : el.scrollLeft > offset;\n          const hasAfter = type === \"vertical\" ? scrollTop + el.clientHeight + offset < scrollHeight : el.scrollLeft + el.clientWidth + offset < el.scrollWidth;\n          setAttributes(type, hasBefore, hasAfter, prefix, suffix);\n        }\n      }\n    };\n    const clearOverflow = () => {\n      [\"top\", \"bottom\", \"top-bottom\", \"left\", \"right\", \"left-right\"].forEach((attr) => {\n        el.removeAttribute(`data-${attr}-scroll`);\n      });\n    };\n    checkOverflow();\n    el.addEventListener(\"scroll\", checkOverflow, true);\n    if (visibility !== \"auto\") {\n      clearOverflow();\n      if (visibility === \"both\") {\n        el.dataset.topBottomScroll = String(overflowCheck === \"vertical\");\n        el.dataset.leftRightScroll = String(overflowCheck === \"horizontal\");\n      } else {\n        el.dataset.topBottomScroll = \"false\";\n        el.dataset.leftRightScroll = \"false\";\n        [\"top\", \"bottom\", \"left\", \"right\"].forEach((attr) => {\n          el.dataset[`${attr}Scroll`] = String(visibility === attr);\n        });\n      }\n    }\n    return () => {\n      el.removeEventListener(\"scroll\", checkOverflow, true);\n      clearOverflow();\n    };\n  }, [...updateDeps, isEnabled, visibility, overflowCheck, onVisibilityChange, domRef]);\n}\n\n// ../scroll-shadow/src/use-scroll-shadow.ts\nimport { useMemo } from \"react\";\nimport { objectToDeps } from \"@heroui/shared-utils\";\nfunction useScrollShadow(originalProps) {\n  var _a;\n  const [props, variantProps] = mapPropsVariants(originalProps, scrollShadow.variantKeys);\n  const {\n    ref,\n    as,\n    children,\n    className,\n    style,\n    size = 40,\n    offset = 0,\n    visibility = \"auto\",\n    isEnabled = true,\n    onVisibilityChange,\n    ...otherProps\n  } = props;\n  const Component = as || \"div\";\n  const domRef = useDOMRef(ref);\n  useDataScrollOverflow({\n    domRef,\n    offset,\n    visibility,\n    isEnabled,\n    onVisibilityChange,\n    updateDeps: [children],\n    overflowCheck: (_a = originalProps.orientation) != null ? _a : \"vertical\"\n  });\n  const styles = useMemo(\n    () => scrollShadow({\n      ...variantProps,\n      className\n    }),\n    [objectToDeps(variantProps), className]\n  );\n  const getBaseProps = (props2 = {}) => {\n    var _a2;\n    return {\n      ref: domRef,\n      className: styles,\n      \"data-orientation\": (_a2 = originalProps.orientation) != null ? _a2 : \"vertical\",\n      style: {\n        \"--scroll-shadow-size\": `${size}px`,\n        ...style,\n        ...props2.style\n      },\n      ...otherProps,\n      ...props2\n    };\n  };\n  return { Component, styles, domRef, children, getBaseProps };\n}\n\n// src/virtualized-listbox.tsx\nimport { filterDOMProps } from \"@heroui/react-utils\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar getItemSizesForCollection = (collection, itemHeight) => {\n  const sizes = [];\n  for (const item of collection) {\n    if (item.type === \"section\") {\n      sizes.push(([...item.childNodes].length + 1) * itemHeight);\n    } else {\n      sizes.push(itemHeight);\n    }\n  }\n  return sizes;\n};\nvar getScrollState = (element) => {\n  if (!element || element.scrollTop === void 0 || element.clientHeight === void 0 || element.scrollHeight === void 0) {\n    return {\n      isTop: false,\n      isBottom: false,\n      isMiddle: false\n    };\n  }\n  const isAtTop = element.scrollTop === 0;\n  const isAtBottom = Math.ceil(element.scrollTop + element.clientHeight) >= element.scrollHeight;\n  const isInMiddle = !isAtTop && !isAtBottom;\n  return {\n    isTop: isAtTop,\n    isBottom: isAtBottom,\n    isMiddle: isInMiddle\n  };\n};\nvar VirtualizedListbox = (props) => {\n  var _a;\n  const {\n    Component,\n    state,\n    color,\n    variant,\n    itemClasses,\n    getBaseProps,\n    topContent,\n    bottomContent,\n    hideEmptyContent,\n    hideSelectedIcon,\n    shouldHighlightOnFocus,\n    disableAnimation,\n    getEmptyContentProps,\n    getListProps,\n    scrollShadowProps\n  } = props;\n  const { virtualization } = props;\n  if (!virtualization || !isEmpty(virtualization) && !virtualization.maxListboxHeight && !virtualization.itemHeight) {\n    throw new Error(\n      \"You are using a virtualized listbox. VirtualizedListbox requires 'virtualization' props with 'maxListboxHeight' and 'itemHeight' properties. This error might have originated from autocomplete components that use VirtualizedListbox. Please provide these props to use the virtualized listbox.\"\n    );\n  }\n  const { maxListboxHeight, itemHeight } = virtualization;\n  const listHeight = Math.min(maxListboxHeight, itemHeight * state.collection.size);\n  const parentRef = useRef2(null);\n  const itemSizes = useMemo2(\n    () => getItemSizesForCollection([...state.collection], itemHeight),\n    [state.collection, itemHeight]\n  );\n  const rowVirtualizer = useVirtualizer({\n    count: [...state.collection].length,\n    getScrollElement: () => parentRef.current,\n    estimateSize: (i) => itemSizes[i]\n  });\n  const virtualItems = rowVirtualizer.getVirtualItems();\n  const virtualScrollHeight = rowVirtualizer.getTotalSize();\n  const { getBaseProps: getBasePropsScrollShadow } = useScrollShadow({ ...scrollShadowProps });\n  const renderRow = (virtualItem) => {\n    var _a2;\n    const item = [...state.collection][virtualItem.index];\n    if (!item) {\n      return null;\n    }\n    const itemProps = {\n      color,\n      item,\n      state,\n      variant,\n      disableAnimation,\n      hideSelectedIcon,\n      ...item.props\n    };\n    const virtualizerStyle = {\n      position: \"absolute\",\n      top: 0,\n      left: 0,\n      width: \"100%\",\n      height: `${virtualItem.size}px`,\n      transform: `translateY(${virtualItem.start}px)`\n    };\n    if (item.type === \"section\") {\n      return /* @__PURE__ */ jsx(\n        listbox_section_default,\n        {\n          ...itemProps,\n          itemClasses,\n          style: { ...virtualizerStyle, ...itemProps.style }\n        },\n        item.key\n      );\n    }\n    let listboxItem = /* @__PURE__ */ jsx(\n      listbox_item_default,\n      {\n        ...itemProps,\n        classNames: mergeProps(itemClasses, (_a2 = item.props) == null ? void 0 : _a2.classNames),\n        shouldHighlightOnFocus,\n        style: { ...virtualizerStyle, ...itemProps.style }\n      },\n      item.key\n    );\n    if (item.wrapper) {\n      listboxItem = item.wrapper(listboxItem);\n    }\n    return listboxItem;\n  };\n  const [scrollState, setScrollState] = useState({\n    isTop: false,\n    isBottom: true,\n    isMiddle: false\n  });\n  const content = /* @__PURE__ */ jsxs(\n    Component,\n    {\n      ...getListProps(),\n      \"data-virtual-scroll-height\": virtualScrollHeight,\n      \"data-virtual-scroll-top\": (_a = parentRef == null ? void 0 : parentRef.current) == null ? void 0 : _a.scrollTop,\n      children: [\n        !state.collection.size && !hideEmptyContent && /* @__PURE__ */ jsx(\"li\", { children: /* @__PURE__ */ jsx(\"div\", { ...getEmptyContentProps() }) }),\n        /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            ...filterDOMProps(getBasePropsScrollShadow()),\n            ref: parentRef,\n            style: {\n              height: maxListboxHeight,\n              overflow: \"auto\"\n            },\n            onScroll: (e) => {\n              setScrollState(getScrollState(e.target));\n            },\n            children: listHeight > 0 && itemHeight > 0 && /* @__PURE__ */ jsx(\n              \"div\",\n              {\n                style: {\n                  height: `${virtualScrollHeight}px`,\n                  width: \"100%\",\n                  position: \"relative\"\n                },\n                children: virtualItems.map((virtualItem) => renderRow(virtualItem))\n              }\n            )\n          }\n        )\n      ]\n    }\n  );\n  return /* @__PURE__ */ jsxs(\"div\", { ...getBaseProps(), children: [\n    topContent,\n    content,\n    bottomContent\n  ] });\n};\nvar virtualized_listbox_default = VirtualizedListbox;\n\nexport {\n  virtualized_listbox_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,8BAA8B;AAC9B;AACA;AACA;AAEA,4CAA4C;AAC5C;AACA;AACA;AA0IA,8BAA8B;AAC9B;AACA;AA5JA;;;;;;;;;;;AAqBA,SAAS,sBAAsB,QAAQ,CAAC,CAAC;IACvC,MAAM,EACJ,MAAM,EACN,YAAY,IAAI,EAChB,gBAAgB,UAAU,EAC1B,aAAa,MAAM,EACnB,SAAS,CAAC,EACV,kBAAkB,EAClB,aAAa,EAAE,EAChB,GAAG;IACJ,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO;QACnD,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,MAAM,gBAAgB,CAAC,WAAW,WAAW,UAAU,QAAQ;YAC7D,IAAI,eAAe,QAAQ;gBACzB,MAAM,OAAO,GAAG,SAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,MAAM,CAAC;gBACnD,IAAI,aAAa,UAAU;oBACzB,GAAG,OAAO,CAAC,KAAK,GAAG;oBACnB,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC;oBAC1C,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC;gBAC5C,OAAO;oBACL,GAAG,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,QAAQ;oBAClD,GAAG,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,QAAQ;oBACjD,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC;gBACtD;YACF,OAAO;gBACL,MAAM,OAAO,aAAa,WAAW,SAAS,YAAY,SAAS,WAAW,SAAS;gBACvF,IAAI,SAAS,WAAW,OAAO,EAAE;oBAC/B,sBAAsB,OAAO,KAAK,IAAI,mBAAmB;oBACzD,WAAW,OAAO,GAAG;gBACvB;YACF;QACF;QACA,MAAM,gBAAgB;YACpB,IAAI,IAAI;YACR,MAAM,aAAa;gBACjB;oBAAE,MAAM;oBAAY,QAAQ;oBAAO,QAAQ;gBAAS;gBACpD;oBAAE,MAAM;oBAAc,QAAQ;oBAAQ,QAAQ;gBAAQ;aACvD;YACD,MAAM,UAAU,GAAG,aAAa,CAAC;YACjC,MAAM,eAAe,CAAC,CAAC,CAAC,KAAK,WAAW,OAAO,KAAK,IAAI,QAAQ,YAAY,CAAC,6BAA6B,KAAK,OAAO,KAAK,GAAG,YAAY;YAC1I,MAAM,YAAY,CAAC,CAAC,CAAC,KAAK,WAAW,OAAO,KAAK,IAAI,QAAQ,YAAY,CAAC,0BAA0B,KAAK,OAAO,KAAK,GAAG,SAAS;YACjI,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,WAAY;gBACjD,IAAI,kBAAkB,QAAQ,kBAAkB,QAAQ;oBACtD,MAAM,YAAY,SAAS,aAAa,YAAY,SAAS,GAAG,UAAU,GAAG;oBAC7E,MAAM,WAAW,SAAS,aAAa,YAAY,GAAG,YAAY,GAAG,SAAS,eAAe,GAAG,UAAU,GAAG,GAAG,WAAW,GAAG,SAAS,GAAG,WAAW;oBACrJ,cAAc,MAAM,WAAW,UAAU,QAAQ;gBACnD;YACF;QACF;QACA,MAAM,gBAAgB;YACpB;gBAAC;gBAAO;gBAAU;gBAAc;gBAAQ;gBAAS;aAAa,CAAC,OAAO,CAAC,CAAC;gBACtE,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC;YAC1C;QACF;QACA;QACA,GAAG,gBAAgB,CAAC,UAAU,eAAe;QAC7C,IAAI,eAAe,QAAQ;YACzB;YACA,IAAI,eAAe,QAAQ;gBACzB,GAAG,OAAO,CAAC,eAAe,GAAG,OAAO,kBAAkB;gBACtD,GAAG,OAAO,CAAC,eAAe,GAAG,OAAO,kBAAkB;YACxD,OAAO;gBACL,GAAG,OAAO,CAAC,eAAe,GAAG;gBAC7B,GAAG,OAAO,CAAC,eAAe,GAAG;gBAC7B;oBAAC;oBAAO;oBAAU;oBAAQ;iBAAQ,CAAC,OAAO,CAAC,CAAC;oBAC1C,GAAG,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,OAAO,eAAe;gBACtD;YACF;QACF;QACA,OAAO;YACL,GAAG,mBAAmB,CAAC,UAAU,eAAe;YAChD;QACF;IACF,GAAG;WAAI;QAAY;QAAW;QAAY;QAAe;QAAoB;KAAO;AACtF;;;AAKA,SAAS,gBAAgB,aAAa;IACpC,IAAI;IACJ,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,eAAY,CAAC,WAAW;IACtF,MAAM,EACJ,GAAG,EACH,EAAE,EACF,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EAAE,EACT,SAAS,CAAC,EACV,aAAa,MAAM,EACnB,YAAY,IAAI,EAChB,kBAAkB,EAClB,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,sBAAsB;QACpB;QACA;QACA;QACA;QACA;QACA,YAAY;YAAC;SAAS;QACtB,eAAe,CAAC,KAAK,cAAc,WAAW,KAAK,OAAO,KAAK;IACjE;IACA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACnB,IAAM,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE;YACjB,GAAG,YAAY;YACf;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;KAAU;IAEzC,MAAM,eAAe,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI;QACJ,OAAO;YACL,KAAK;YACL,WAAW;YACX,oBAAoB,CAAC,MAAM,cAAc,WAAW,KAAK,OAAO,MAAM;YACtE,OAAO;gBACL,wBAAwB,GAAG,KAAK,EAAE,CAAC;gBACnC,GAAG,KAAK;gBACR,GAAG,OAAO,KAAK;YACjB;YACA,GAAG,UAAU;YACb,GAAG,MAAM;QACX;IACF;IACA,OAAO;QAAE;QAAW;QAAQ;QAAQ;QAAU;IAAa;AAC7D;;;AAKA,IAAI,4BAA4B,CAAC,YAAY;IAC3C,MAAM,QAAQ,EAAE;IAChB,KAAK,MAAM,QAAQ,WAAY;QAC7B,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,MAAM,IAAI,CAAC,CAAC;mBAAI,KAAK,UAAU;aAAC,CAAC,MAAM,GAAG,CAAC,IAAI;QACjD,OAAO;YACL,MAAM,IAAI,CAAC;QACb;IACF;IACA,OAAO;AACT;AACA,IAAI,iBAAiB,CAAC;IACpB,IAAI,CAAC,WAAW,QAAQ,SAAS,KAAK,KAAK,KAAK,QAAQ,YAAY,KAAK,KAAK,KAAK,QAAQ,YAAY,KAAK,KAAK,GAAG;QAClH,OAAO;YACL,OAAO;YACP,UAAU;YACV,UAAU;QACZ;IACF;IACA,MAAM,UAAU,QAAQ,SAAS,KAAK;IACtC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ,SAAS,GAAG,QAAQ,YAAY,KAAK,QAAQ,YAAY;IAC9F,MAAM,aAAa,CAAC,WAAW,CAAC;IAChC,OAAO;QACL,OAAO;QACP,UAAU;QACV,UAAU;IACZ;AACF;AACA,IAAI,qBAAqB,CAAC;IACxB,IAAI;IACJ,MAAM,EACJ,SAAS,EACT,KAAK,EACL,KAAK,EACL,OAAO,EACP,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,sBAAsB,EACtB,gBAAgB,EAChB,oBAAoB,EACpB,YAAY,EACZ,iBAAiB,EAClB,GAAG;IACJ,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,IAAI,CAAC,kBAAkB,CAAC,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,CAAC,eAAe,gBAAgB,IAAI,CAAC,eAAe,UAAU,EAAE;QACjH,MAAM,IAAI,MACR;IAEJ;IACA,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,GAAG;IACzC,MAAM,aAAa,KAAK,GAAG,CAAC,kBAAkB,aAAa,MAAM,UAAU,CAAC,IAAI;IAChF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAO,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAQ,AAAD,EACvB,IAAM,0BAA0B;eAAI,MAAM,UAAU;SAAC,EAAE,aACvD;QAAC,MAAM,UAAU;QAAE;KAAW;IAEhC,MAAM,iBAAiB,CAAA,GAAA,6NAAA,CAAA,iBAAc,AAAD,EAAE;QACpC,OAAO;eAAI,MAAM,UAAU;SAAC,CAAC,MAAM;QACnC,kBAAkB,IAAM,UAAU,OAAO;QACzC,cAAc,CAAC,IAAM,SAAS,CAAC,EAAE;IACnC;IACA,MAAM,eAAe,eAAe,eAAe;IACnD,MAAM,sBAAsB,eAAe,YAAY;IACvD,MAAM,EAAE,cAAc,wBAAwB,EAAE,GAAG,gBAAgB;QAAE,GAAG,iBAAiB;IAAC;IAC1F,MAAM,YAAY,CAAC;QACjB,IAAI;QACJ,MAAM,OAAO;eAAI,MAAM,UAAU;SAAC,CAAC,YAAY,KAAK,CAAC;QACrD,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,MAAM,YAAY;YAChB;YACA;YACA;YACA;YACA;YACA;YACA,GAAG,KAAK,KAAK;QACf;QACA,MAAM,mBAAmB;YACvB,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ,GAAG,YAAY,IAAI,CAAC,EAAE,CAAC;YAC/B,WAAW,CAAC,WAAW,EAAE,YAAY,KAAK,CAAC,GAAG,CAAC;QACjD;QACA,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,iKAAA,CAAA,0BAAuB,EACvB;gBACE,GAAG,SAAS;gBACZ;gBACA,OAAO;oBAAE,GAAG,gBAAgB;oBAAE,GAAG,UAAU,KAAK;gBAAC;YACnD,GACA,KAAK,GAAG;QAEZ;QACA,IAAI,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAClC,iKAAA,CAAA,uBAAoB,EACpB;YACE,GAAG,SAAS;YACZ,YAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,aAAa,CAAC,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,IAAI,UAAU;YACxF;YACA,OAAO;gBAAE,GAAG,gBAAgB;gBAAE,GAAG,UAAU,KAAK;YAAC;QACnD,GACA,KAAK,GAAG;QAEV,IAAI,KAAK,OAAO,EAAE;YAChB,cAAc,KAAK,OAAO,CAAC;QAC7B;QACA,OAAO;IACT;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,OAAO;QACP,UAAU;QACV,UAAU;IACZ;IACA,MAAM,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACjC,WACA;QACE,GAAG,cAAc;QACjB,8BAA8B;QAC9B,2BAA2B,CAAC,KAAK,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;QAChH,UAAU;YACR,CAAC,MAAM,UAAU,CAAC,IAAI,IAAI,CAAC,oBAAoB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;oBAAE,GAAG,sBAAsB;gBAAC;YAAG;YAC/I,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAChB,OACA;gBACE,GAAG,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,2BAA2B;gBAC7C,KAAK;gBACL,OAAO;oBACL,QAAQ;oBACR,UAAU;gBACZ;gBACA,UAAU,CAAC;oBACT,eAAe,eAAe,EAAE,MAAM;gBACxC;gBACA,UAAU,aAAa,KAAK,aAAa,KAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC9D,OACA;oBACE,OAAO;wBACL,QAAQ,GAAG,oBAAoB,EAAE,CAAC;wBAClC,OAAO;wBACP,UAAU;oBACZ;oBACA,UAAU,aAAa,GAAG,CAAC,CAAC,cAAgB,UAAU;gBACxD;YAEJ;SAEH;IACH;IAEF,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,GAAG,cAAc;QAAE,UAAU;YAChE;YACA;YACA;SACD;IAAC;AACJ;AACA,IAAI,8BAA8B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4594, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-CLI57HS2.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useListbox\n} from \"./chunk-MZOWMNSQ.mjs\";\nimport {\n  virtualized_listbox_default\n} from \"./chunk-7UIGGLV2.mjs\";\nimport {\n  listbox_section_default\n} from \"./chunk-BXSPTCK3.mjs\";\nimport {\n  listbox_item_default\n} from \"./chunk-4QJLEUWX.mjs\";\n\n// src/listbox.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { mergeProps } from \"@heroui/shared-utils\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Listbox = forwardRef(function Listbox2(props, ref) {\n  const { isVirtualized, ...restProps } = props;\n  const useListboxProps = useListbox({ ...restProps, ref });\n  const {\n    Component,\n    state,\n    color,\n    variant,\n    itemClasses,\n    getBaseProps,\n    topContent,\n    bottomContent,\n    hideEmptyContent,\n    hideSelectedIcon,\n    shouldHighlightOnFocus,\n    disableAnimation,\n    getEmptyContentProps,\n    getListProps\n  } = useListboxProps;\n  if (isVirtualized) {\n    return /* @__PURE__ */ jsx(virtualized_listbox_default, { ...props, ...useListboxProps });\n  }\n  const content = /* @__PURE__ */ jsxs(Component, { ...getListProps(), children: [\n    !state.collection.size && !hideEmptyContent && /* @__PURE__ */ jsx(\"li\", { children: /* @__PURE__ */ jsx(\"div\", { ...getEmptyContentProps() }) }),\n    [...state.collection].map((item) => {\n      var _a;\n      const itemProps = {\n        color,\n        item,\n        state,\n        variant,\n        disableAnimation,\n        hideSelectedIcon,\n        ...item.props\n      };\n      if (item.type === \"section\") {\n        return /* @__PURE__ */ jsx(listbox_section_default, { ...itemProps, itemClasses }, item.key);\n      }\n      let listboxItem = /* @__PURE__ */ jsx(\n        listbox_item_default,\n        {\n          ...itemProps,\n          classNames: mergeProps(itemClasses, (_a = item.props) == null ? void 0 : _a.classNames),\n          shouldHighlightOnFocus\n        },\n        item.key\n      );\n      if (item.wrapper) {\n        listboxItem = item.wrapper(listboxItem);\n      }\n      return listboxItem;\n    })\n  ] });\n  return /* @__PURE__ */ jsxs(\"div\", { ...getBaseProps(), children: [\n    topContent,\n    content,\n    bottomContent\n  ] });\n});\nvar listbox_default = Listbox;\n\nexport {\n  listbox_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAGA;AAGA;AAIA,kBAAkB;AAClB;AACA;AACA;AAjBA;;;;;;;;AAkBA,IAAI,UAAU,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS,KAAK,EAAE,GAAG;IACnD,MAAM,EAAE,aAAa,EAAE,GAAG,WAAW,GAAG;IACxC,MAAM,kBAAkB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE;QAAE,GAAG,SAAS;QAAE;IAAI;IACvD,MAAM,EACJ,SAAS,EACT,KAAK,EACL,KAAK,EACL,OAAO,EACP,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,gBAAgB,EAChB,sBAAsB,EACtB,gBAAgB,EAChB,oBAAoB,EACpB,YAAY,EACb,GAAG;IACJ,IAAI,eAAe;QACjB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iKAAA,CAAA,8BAA2B,EAAE;YAAE,GAAG,KAAK;YAAE,GAAG,eAAe;QAAC;IACzF;IACA,MAAM,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;QAAE,GAAG,cAAc;QAAE,UAAU;YAC7E,CAAC,MAAM,UAAU,CAAC,IAAI,IAAI,CAAC,oBAAoB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;oBAAE,GAAG,sBAAsB;gBAAC;YAAG;YAC/I;mBAAI,MAAM,UAAU;aAAC,CAAC,GAAG,CAAC,CAAC;gBACzB,IAAI;gBACJ,MAAM,YAAY;oBAChB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,GAAG,KAAK,KAAK;gBACf;gBACA,IAAI,KAAK,IAAI,KAAK,WAAW;oBAC3B,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iKAAA,CAAA,0BAAuB,EAAE;wBAAE,GAAG,SAAS;wBAAE;oBAAY,GAAG,KAAK,GAAG;gBAC7F;gBACA,IAAI,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAClC,iKAAA,CAAA,uBAAoB,EACpB;oBACE,GAAG,SAAS;oBACZ,YAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,UAAU;oBACtF;gBACF,GACA,KAAK,GAAG;gBAEV,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,OAAO,CAAC;gBAC7B;gBACA,OAAO;YACT;SACD;IAAC;IACF,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,GAAG,cAAc;QAAE,UAAU;YAChE;YACA;YACA;SACD;IAAC;AACJ;AACA,IAAI,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4688, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/dist/chunk-BJFJ4DRR.mjs"], "sourcesContent": ["\"use client\";\n\n// src/base/listbox-item-base.tsx\nimport { BaseItem } from \"@heroui/aria-utils\";\nvar ListboxItemBase = BaseItem;\nvar listbox_item_base_default = ListboxItemBase;\n\nexport {\n  listbox_item_base_default\n};\n"], "names": [], "mappings": ";;;AAEA,iCAAiC;AACjC;AAHA;;AAIA,IAAI,kBAAkB,sMAAA,CAAA,WAAQ;AAC9B,IAAI,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4710, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@heroui/listbox/node_modules/@tanstack/virtual-core/dist/esm/utils.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/node_modules/%40tanstack/virtual-core/src/utils.ts"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  return (): TResult => {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) < 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAIgB,SAAA,KACd,OAAA,EACA,EAAA,EACA,IAAA,EAMA;IACI,IAAA,OAAO,KAAK,WAAA,IAAe,CAAC,CAAA;IAC5B,IAAA;IAEJ,OAAO,MAAe;QAbR,IAAA,IAAA,IAAA,IAAA;QAcR,IAAA;QACJ,IAAI,KAAK,GAAA,IAAA,CAAA,CAAO,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,KAAA,EAAgB,CAAA,UAAU,KAAK,GAAA,CAAI;QAEnD,MAAM,UAAU,QAAQ;QAExB,MAAM,cACJ,QAAQ,MAAA,KAAW,KAAK,MAAA,IACxB,QAAQ,IAAA,CAAK,CAAC,KAAU,QAAkB,IAAA,CAAK,KAAK,CAAA,KAAM,GAAG;QAE/D,IAAI,CAAC,aAAa;YACT,OAAA;QAAA;QAGF,OAAA;QAEH,IAAA;QACJ,IAAI,KAAK,GAAA,IAAA,CAAA,CAAO,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,KAAA,EAAgB,CAAA,aAAa,KAAK,GAAA,CAAI;QAE7C,SAAA,GAAG,GAAG,OAAO;QAEtB,IAAI,KAAK,GAAA,IAAA,CAAA,CAAO,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,KAAA,GAAgB;YACxB,MAAA,aAAa,KAAK,KAAA,CAAA,CAAO,KAAK,GAAA,KAAQ,OAAA,IAAY,GAAG,IAAI;YACzD,MAAA,gBAAgB,KAAK,KAAA,CAAA,CAAO,KAAK,GAAA,KAAQ,UAAA,IAAe,GAAG,IAAI;YACrE,MAAM,sBAAsB,gBAAgB;YAEtC,MAAA,MAAM,CAAC,KAAsB,QAAgB;gBACjD,MAAM,OAAO,GAAG;gBACT,MAAA,IAAI,MAAA,GAAS,IAAK;oBACvB,MAAM,MAAM;gBAAA;gBAEP,OAAA;YACT;YAEQ,QAAA,IAAA,CACN,CAAA,IAAA,EAAO,IAAI,eAAe,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,YAAY,CAAC,CAAC,CAAA,GAAA,CAAA,EACnD,CAAA;;;uBAAA,EAGiB,KAAK,GAAA,CAChB,GACA,KAAK,GAAA,CAAI,MAAM,MAAM,qBAAqB,GAAG,GAC9C,cAAA,CAAA,EACL,QAAA,OAAA,KAAA,IAAA,KAAM,GAAA;QACR;QAGF,CAAA,KAAA,QAAA,OAAA,KAAA,IAAA,KAAM,QAAA,KAAN,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,MAAiB;QAEV,OAAA;IACT;AACF;AAEgB,SAAA,aAAgB,KAAA,EAAsB,GAAA,EAAiB;IACrE,IAAI,UAAU,KAAA,GAAW;QACjB,MAAA,IAAI,MAAM,CAAA,oBAAA,EAAuB,MAAM,CAAA,EAAA,EAAK,GAAG,EAAA,GAAK,EAAE,EAAE;IAAA,OACzD;QACE,OAAA;IAAA;AAEX;AAEa,MAAA,cAAc,CAAC,GAAW,IAAc,KAAK,GAAA,CAAI,IAAI,CAAC,IAAI;AAEhE,MAAM,WAAW,CACtB,cACA,IACA,OACG;IACC,IAAA;IACJ,OAAO,SAAA,GAAwB,IAAA,EAAkB;QAC/C,aAAa,YAAA,CAAa,SAAS;QACvB,YAAA,aAAa,UAAA,CAAW,IAAM,GAAG,KAAA,CAAM,IAAA,EAAM,IAAI,GAAG,EAAE;IACpE;AACF", "debugId": null}}, {"offset": {"line": 4773, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@heroui/listbox/node_modules/@tanstack/virtual-core/dist/esm/index.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/node_modules/%40tanstack/virtual-core/src/index.ts"], "sourcesContent": ["import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(element.getBoundingClientRect())\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const entry = entries[0]\n    if (entry?.borderBoxSize) {\n      const box = entry.borderBoxSize[0]\n      if (box) {\n        handler({ width: box.inlineSize, height: box.blockSize })\n        return\n      }\n    }\n    handler(element.getBoundingClientRect())\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('scroll', handler)\n    element.removeEventListener('scrollend', endHandler)\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('scroll', handler)\n    element.removeEventListener('scrollend', endHandler)\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n  return Math.round(\n    element.getBoundingClientRect()[\n      instance.options.horizontal ? 'width' : 'height'\n    ],\n  )\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  private scrollToIndexTimeoutId: number | null = null\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          this._measureElement(entry.target as TItemElement, entry)\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: true,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [this.getMeasurements(), this.getSize(), this.getScrollOffset()],\n    (measurements, outerSize, scrollOffset) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  private getIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (toOffset: number, align: ScrollAlignment) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (toOffset >= scrollOffset + size) {\n        align = 'end'\n      }\n    }\n\n    if (align === 'end') {\n      toOffset -= size\n    }\n\n    const scrollSizeProp = this.options.horizontal\n      ? 'scrollWidth'\n      : 'scrollHeight'\n    const scrollSize = this.scrollElement\n      ? 'document' in this.scrollElement\n        ? this.scrollElement.document.documentElement[scrollSizeProp]\n        : this.scrollElement[scrollSizeProp]\n      : 0\n\n    const maxOffset = scrollSize - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const centerOffset =\n      item.start - this.options.scrollPaddingStart + (item.size - size) / 2\n\n    switch (align) {\n      case 'center':\n        return [this.getOffsetForAlignment(centerOffset, align), align] as const\n      case 'end':\n        return [\n          this.getOffsetForAlignment(\n            item.end + this.options.scrollPaddingEnd,\n            align,\n          ),\n          align,\n        ] as const\n      default:\n        return [\n          this.getOffsetForAlignment(\n            item.start - this.options.scrollPaddingStart,\n            align,\n          ),\n          align,\n        ] as const\n    }\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  private cancelScrollToIndex = () => {\n    if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n      this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId)\n      this.scrollToIndexTimeoutId = null\n    }\n  }\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    const offsetAndAlign = this.getOffsetForIndex(index, initialAlign)\n    if (!offsetAndAlign) return\n\n    const [offset, align] = offsetAndAlign\n\n    this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n    if (behavior !== 'smooth' && this.isDynamicMode() && this.targetWindow) {\n      this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n        this.scrollToIndexTimeoutId = null\n\n        const elementInDOM = this.elementsCache.has(\n          this.options.getItemKey(index),\n        )\n\n        if (elementInDOM) {\n          const [latestOffset] = notUndefined(\n            this.getOffsetForIndex(index, align),\n          )\n\n          if (!approxEqual(latestOffset, this.getScrollOffset())) {\n            this.scrollToIndex(index, { align, behavior })\n          }\n        } else {\n          this.scrollToIndex(index, { align, behavior })\n        }\n      })\n    }\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else {\n      // If lanes is 1, use the last measurement's end, otherwise find the maximum end value among all measurements\n      end =\n        this.options.lanes === 1\n          ? (measurements[measurements.length - 1]?.end ?? 0)\n          : Math.max(\n              ...measurements.slice(-this.options.lanes).map((m) => m.end),\n            )\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n}) {\n  const count = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  const startIndex = findNearestBinarySearch(0, count, getOffset, scrollOffset)\n  let endIndex = startIndex\n\n  while (\n    endIndex < count &&\n    measurements[endIndex]!.end < scrollOffset + outerSize\n  ) {\n    endIndex++\n  }\n\n  return { startIndex, endIndex }\n}\n"], "names": ["opts"], "mappings": ";;;;;;;;;;;;;;AA8Ca,MAAA,sBAAsB,CAAC,QAAkB;AAEzC,MAAA,wBAAwB,CAAC,UAAiB;IACrD,MAAM,QAAQ,KAAK,GAAA,CAAI,MAAM,UAAA,GAAa,MAAM,QAAA,EAAU,CAAC;IACrD,MAAA,MAAM,KAAK,GAAA,CAAI,MAAM,QAAA,GAAW,MAAM,QAAA,EAAU,MAAM,KAAA,GAAQ,CAAC;IAErE,MAAM,MAAM,CAAC,CAAA;IAEb,IAAA,IAAS,IAAI,OAAO,KAAK,KAAK,IAAK;QACjC,IAAI,IAAA,CAAK,CAAC;IAAA;IAGL,OAAA;AACT;AAEa,MAAA,qBAAqB,CAChC,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAEF,MAAM,eAAe,SAAS,YAAA;IAC9B,IAAI,CAAC,cAAc;QACjB;IAAA;IAGI,MAAA,UAAU,CAAC,SAAe;QACxB,MAAA,EAAE,KAAA,EAAO,MAAA,CAAA,CAAA,GAAW;QACvB,GAAA;YAAE,OAAO,KAAK,KAAA,CAAM,KAAK;YAAG,QAAQ,KAAK,KAAA,CAAM,MAAM;QAAA,CAAG;IAC7D;IAEQ,QAAA,QAAQ,qBAAA,EAAuB;IAEnC,IAAA,CAAC,aAAa,cAAA,EAAgB;QAChC,OAAO,KAAO,CAAD;IAAC;IAGhB,MAAM,WAAW,IAAI,aAAa,cAAA,CAAe,CAAC,YAAY;QACtD,MAAA,QAAQ,OAAA,CAAQ,CAAC,CAAA;QACvB,IAAI,SAAA,OAAA,KAAA,IAAA,MAAO,aAAA,EAAe;YAClB,MAAA,MAAM,MAAM,aAAA,CAAc,CAAC,CAAA;YACjC,IAAI,KAAK;gBACP,QAAQ;oBAAE,OAAO,IAAI,UAAA;oBAAY,QAAQ,IAAI,SAAA;gBAAA,CAAW;gBACxD;YAAA;QACF;QAEM,QAAA,QAAQ,qBAAA,EAAuB;IAAA,CACxC;IAED,SAAS,OAAA,CAAQ,SAAS;QAAE,KAAK;IAAA,CAAc;IAE/C,OAAO,MAAM;QACX,SAAS,SAAA,CAAU,OAAO;IAC5B;AACF;AAEA,MAAM,0BAA0B;IAC9B,SAAS;AACX;AAEa,MAAA,oBAAoB,CAC/B,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAGF,MAAM,UAAU,MAAM;QACpB,GAAG;YAAE,OAAO,QAAQ,UAAA;YAAY,QAAQ,QAAQ,WAAA;QAAA,CAAa;IAC/D;IACQ,QAAA;IAEA,QAAA,gBAAA,CAAiB,UAAU,SAAS,uBAAuB;IAEnE,OAAO,MAAM;QACH,QAAA,mBAAA,CAAoB,UAAU,OAAO;IAC/C;AACF;AAEA,MAAM,oBACJ,OAAO,UAAU,sBAAc,OAAO,iBAAiB;AAI5C,MAAA,uBAAuB,CAClC,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAEF,MAAM,eAAe,SAAS,YAAA;IAC9B,IAAI,CAAC,cAAc;QACjB;IAAA;IAGF,IAAI,SAAS;IACb,MAAM,WACJ,SAAS,OAAA,CAAQ,iBAAA,IAAqB,oBAClC,IAAM,KAAA,qNACN,WAAA,EACE,cACA,MAAM;QACJ,GAAG,QAAQ,KAAK;IAClB,GACA,SAAS,OAAA,CAAQ,qBAAA;IAGnB,MAAA,gBAAgB,CAAC,cAAyB,MAAM;YACpD,MAAM,EAAE,UAAA,EAAY,KAAA,CAAM,CAAA,GAAI,SAAS,OAAA;YAC9B,SAAA,aACL,OAAA,CAAQ,YAAY,CAAA,GAAA,CAAM,SAAS,CAAA,KAAO,CAAA,IAC1C,OAAA,CAAQ,WAAW,CAAA;YACd,SAAA;YACT,GAAG,QAAQ,WAAW;QACxB;IACM,MAAA,UAAU,cAAc,IAAI;IAC5B,MAAA,aAAa,cAAc,KAAK;IAC3B,WAAA;IAEH,QAAA,gBAAA,CAAiB,UAAU,SAAS,uBAAuB;IAC3D,QAAA,gBAAA,CAAiB,aAAa,YAAY,uBAAuB;IAEzE,OAAO,MAAM;QACH,QAAA,mBAAA,CAAoB,UAAU,OAAO;QACrC,QAAA,mBAAA,CAAoB,aAAa,UAAU;IACrD;AACF;AAEa,MAAA,sBAAsB,CACjC,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAEF,MAAM,eAAe,SAAS,YAAA;IAC9B,IAAI,CAAC,cAAc;QACjB;IAAA;IAGF,IAAI,SAAS;IACb,MAAM,WACJ,SAAS,OAAA,CAAQ,iBAAA,IAAqB,oBAClC,IAAM,KAAA,qNACN,WAAA,EACE,cACA,MAAM;QACJ,GAAG,QAAQ,KAAK;IAClB,GACA,SAAS,OAAA,CAAQ,qBAAA;IAGnB,MAAA,gBAAgB,CAAC,cAAyB,MAAM;YACpD,SAAS,OAAA,CAAQ,SAAS,OAAA,CAAQ,UAAA,GAAa,YAAY,SAAS,CAAA;YAC3D,SAAA;YACT,GAAG,QAAQ,WAAW;QACxB;IACM,MAAA,UAAU,cAAc,IAAI;IAC5B,MAAA,aAAa,cAAc,KAAK;IAC3B,WAAA;IAEH,QAAA,gBAAA,CAAiB,UAAU,SAAS,uBAAuB;IAC3D,QAAA,gBAAA,CAAiB,aAAa,YAAY,uBAAuB;IAEzE,OAAO,MAAM;QACH,QAAA,mBAAA,CAAoB,UAAU,OAAO;QACrC,QAAA,mBAAA,CAAoB,aAAa,UAAU;IACrD;AACF;AAEO,MAAM,iBAAiB,CAC5B,SACA,OACA,aACG;IACH,IAAI,SAAA,OAAA,KAAA,IAAA,MAAO,aAAA,EAAe;QAClB,MAAA,MAAM,MAAM,aAAA,CAAc,CAAC,CAAA;QACjC,IAAI,KAAK;YACP,MAAM,OAAO,KAAK,KAAA,CAChB,GAAA,CAAI,SAAS,OAAA,CAAQ,UAAA,GAAa,eAAe,WAAW,CAAA;YAEvD,OAAA;QAAA;IACT;IAEF,OAAO,KAAK,KAAA,CACV,QAAQ,qBAAA,CAAsB,CAAA,CAC5B,SAAS,OAAA,CAAQ,UAAA,GAAa,UAAU,QAC1C,CAAA;AAEJ;AAEa,MAAA,eAAe,CAC1B,QACA,EACE,cAAc,CAAA,EACd,QAAA,EACF,EACA,aACG;;IACH,MAAM,WAAW,SAAS;IAE1B,CAAA,KAAA,CAAA,KAAA,SAAS,aAAA,KAAT,OAAA,KAAA,IAAA,GAAwB,QAAA,KAAxB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmC;QACjC,CAAC,SAAS,OAAA,CAAQ,UAAA,GAAa,SAAS,KAAK,CAAA,EAAG;QAChD;IAAA;AAEJ;AAEa,MAAA,gBAAgB,CAC3B,QACA,EACE,cAAc,CAAA,EACd,QAAA,EACF,EACA,aACG;;IACH,MAAM,WAAW,SAAS;IAE1B,CAAA,KAAA,CAAA,KAAA,SAAS,aAAA,KAAT,OAAA,KAAA,IAAA,GAAwB,QAAA,KAAxB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmC;QACjC,CAAC,SAAS,OAAA,CAAQ,UAAA,GAAa,SAAS,KAAK,CAAA,EAAG;QAChD;IAAA;AAEJ;AAyDO,MAAM,YAGX;IAqDA,YAAY,IAAA,CAAwD;QApDpE,IAAA,CAAQ,MAAA,GAAqC,CAAC,CAAA;QAEP,IAAA,CAAA,aAAA,GAAA;QACa,IAAA,CAAA,YAAA,GAAA;QACtC,IAAA,CAAA,WAAA,GAAA;QACd,IAAA,CAAQ,sBAAA,GAAwC;QAChD,IAAA,CAAA,iBAAA,GAAwC,CAAC,CAAA;QACjC,IAAA,CAAA,aAAA,GAAA,aAAA,GAAA,IAAoB,IAAiB;QAC7C,IAAA,CAAQ,2BAAA,GAA6C,CAAC,CAAA;QAC5B,IAAA,CAAA,UAAA,GAAA;QACI,IAAA,CAAA,YAAA,GAAA;QACY,IAAA,CAAA,eAAA,GAAA;QAC1C,IAAA,CAAQ,iBAAA,GAAoB;QAQ5B,IAAA,CAAA,aAAA,GAAA,aAAA,GAAA,IAAoB,IAAuB;QAC3C,IAAA,CAAQ,QAAA,GAAkB,aAAA,GAAA,CAAA,MAAA;YACxB,IAAI,MAA6B;YAEjC,MAAM,MAAM,MAAM;gBAChB,IAAI,KAAK;oBACA,OAAA;gBAAA;gBAGT,IAAI,CAAC,IAAA,CAAK,YAAA,IAAgB,CAAC,IAAA,CAAK,YAAA,CAAa,cAAA,EAAgB;oBACpD,OAAA;gBAAA;gBAGT,OAAQ,MAAM,IAAI,IAAA,CAAK,YAAA,CAAa,cAAA,CAAe,CAAC,YAAY;oBACtD,QAAA,OAAA,CAAQ,CAAC,UAAU;wBACpB,IAAA,CAAA,eAAA,CAAgB,MAAM,MAAA,EAAwB,KAAK;oBAAA,CACzD;gBAAA,CACF;YACH;YAEO,OAAA;gBACL,YAAY,MAAM;;oBAChB,CAAA,KAAA,IAAA,CAAA,KAAA,OAAA,KAAA,IAAA,GAAO,UAAA;oBACD,MAAA;gBACR;gBACA,SAAS,CAAC,WAAA;;oBACR,OAAA,CAAA,KAAA,IAAI,CAAA,KAAJ,OAAA,KAAA,IAAA,GAAO,OAAA,CAAQ,QAAQ;wBAAE,KAAK;oBAAA;;gBAChC,WAAW,CAAC,WAAA;;oBAAoB,OAAA,CAAA,KAAA,IAAI,CAAA,KAAJ,OAAA,KAAA,IAAA,GAAO,SAAA,CAAU;gBAAA;YACnD;QAAA,CAAA,EACC;QACsD,IAAA,CAAA,KAAA,GAAA;QAMzD,IAAA,CAAA,UAAA,GAAa,CAACA,UAA2D;YAChE,OAAA,OAAA,CAAQA,KAAI,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;gBAC7C,IAAI,OAAO,UAAU,YAAa,CAAA,OAAQA,KAAAA,CAAa,GAAG,CAAA;YAAA,CAC3D;YAED,IAAA,CAAK,OAAA,GAAU;gBACb,OAAO;gBACP,eAAe;gBACf,UAAU;gBACV,cAAc;gBACd,YAAY;gBACZ,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;gBACZ,YAAY;gBACZ,gBAAgB;gBAChB,UAAU,KAAO,CAAD;gBAChB;gBACA,aAAa;oBAAE,OAAO;oBAAG,QAAQ;gBAAE;gBACnC,cAAc;gBACd,KAAK;gBACL,gBAAgB;gBAChB,0BAA0B,CAAC,CAAA;gBAC3B,OAAO;gBACP,uBAAuB;gBACvB,SAAS;gBACT,OAAO;gBACP,mBAAmB;gBACnB,GAAGA,KAAAA;YACL;QACF;QAEQ,IAAA,CAAA,MAAA,GAAS,CAAC,SAAkB;;YAC7B,CAAA,KAAA,CAAA,KAAA,IAAA,CAAA,OAAA,EAAQ,QAAA,KAAR,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmB,IAAA,EAAM;QAChC;QAEA,IAAA,CAAQ,WAAA,oNAAc,OAAA,EACpB,MAAM;YACJ,IAAA,CAAK,cAAA,CAAe;YAEb,OAAA;gBACL,IAAA,CAAK,WAAA;gBACL,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,UAAA,GAAa;gBACrC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW;aACrC;QACF,GACA,CAAC,gBAAgB;YACf,IAAA,CAAK,MAAA,CAAO,WAAW;QACzB,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;YAC1B,aAAa;gBACX,IAAA,CAAK,WAAA;gBACL,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,UAAA,GAAa;gBACrC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW;aAAA;QACrC;QAIJ,IAAA,CAAQ,OAAA,GAAU,MAAM;YACjB,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,OAAO,EAAE,OAAA,CAAQ,CAAC,IAAM,GAAI;YAC/C,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA;YACf,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW;YACzB,IAAA,CAAK,aAAA,GAAgB;YACrB,IAAA,CAAK,YAAA,GAAe;QACtB;QAEA,IAAA,CAAA,SAAA,GAAY,MAAM;YAChB,OAAO,MAAM;gBACX,IAAA,CAAK,OAAA,CAAQ;YACf;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,MAAM;;YAClB,MAAM,gBAAgB,IAAA,CAAK,OAAA,CAAQ,OAAA,GAC/B,IAAA,CAAK,OAAA,CAAQ,gBAAA,KACb;YAEA,IAAA,IAAA,CAAK,aAAA,KAAkB,eAAe;gBACxC,IAAA,CAAK,OAAA,CAAQ;gBAEb,IAAI,CAAC,eAAe;oBAClB,IAAA,CAAK,WAAA,CAAY;oBACjB;gBAAA;gBAGF,IAAA,CAAK,aAAA,GAAgB;gBAErB,IAAI,IAAA,CAAK,aAAA,IAAiB,mBAAmB,IAAA,CAAK,aAAA,EAAe;oBAC1D,IAAA,CAAA,YAAA,GAAe,IAAA,CAAK,aAAA,CAAc,aAAA,CAAc,WAAA;gBAAA,OAChD;oBACA,IAAA,CAAA,YAAA,GAAA,CAAA,CAAe,KAAA,IAAA,CAAK,aAAA,KAAL,OAAA,KAAA,IAAA,GAAoB,MAAA,KAAU;gBAAA;gBAG/C,IAAA,CAAA,aAAA,CAAc,OAAA,CAAQ,CAAC,WAAW;oBAChC,IAAA,CAAA,QAAA,CAAS,OAAA,CAAQ,MAAM;gBAAA,CAC7B;gBAEI,IAAA,CAAA,eAAA,CAAgB,IAAA,CAAK,eAAA,IAAmB;oBAC3C,aAAa,KAAA;oBACb,UAAU,KAAA;gBAAA,CACX;gBAED,IAAA,CAAK,MAAA,CAAO,IAAA,CACV,IAAA,CAAK,OAAA,CAAQ,kBAAA,CAAmB,IAAA,EAAM,CAAC,SAAS;oBAC9C,IAAA,CAAK,UAAA,GAAa;oBAClB,IAAA,CAAK,WAAA,CAAY;gBAClB,CAAA;gBAGH,IAAA,CAAK,MAAA,CAAO,IAAA,CACV,IAAA,CAAK,OAAA,CAAQ,oBAAA,CAAqB,IAAA,EAAM,CAAC,QAAQ,gBAAgB;oBAC/D,IAAA,CAAK,iBAAA,GAAoB;oBACzB,IAAA,CAAK,eAAA,GAAkB,cACnB,IAAA,CAAK,eAAA,KAAoB,SACvB,YACA,aACF;oBACJ,IAAA,CAAK,YAAA,GAAe;oBACpB,IAAA,CAAK,WAAA,GAAc;oBAEnB,IAAA,CAAK,WAAA,CAAY;gBAClB,CAAA;YACH;QAEJ;QAEA,IAAA,CAAQ,OAAA,GAAU,MAAM;YAClB,IAAA,CAAC,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;gBACzB,IAAA,CAAK,UAAA,GAAa;gBACX,OAAA;YAAA;YAGT,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,OAAA,CAAQ,WAAA;YAElD,OAAO,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,UAAA,GAAa,UAAU,QAAQ,CAAA;QACrE;QAEA,IAAA,CAAQ,eAAA,GAAkB,MAAM;YAC1B,IAAA,CAAC,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;gBACzB,IAAA,CAAK,YAAA,GAAe;gBACb,OAAA;YAAA;YAGT,IAAA,CAAK,YAAA,GACH,IAAA,CAAK,YAAA,IAAA,CACJ,OAAO,IAAA,CAAK,OAAA,CAAQ,aAAA,KAAkB,aACnC,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc,IAC3B,IAAA,CAAK,OAAA,CAAQ,aAAA;YAEnB,OAAO,IAAA,CAAK,YAAA;QACd;QAEQ,IAAA,CAAA,sBAAA,GAAyB,CAC/B,cACA,UACG;YACG,MAAA,4BAAA,aAAA,GAAA,IAAgC,IAAkB;YAClD,MAAA,uBAAA,aAAA,GAAA,IAA2B,IAAyB;YAC1D,IAAA,IAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAK;gBAC7B,MAAA,cAAc,YAAA,CAAa,CAAC,CAAA;gBAElC,IAAI,0BAA0B,GAAA,CAAI,YAAY,IAAI,GAAG;oBACnD;gBAAA;gBAGF,MAAM,8BAA8B,qBAAqB,GAAA,CACvD,YAAY,IAAA;gBAEd,IACE,+BAA+B,QAC/B,YAAY,GAAA,GAAM,4BAA4B,GAAA,EAC9C;oBACqB,qBAAA,GAAA,CAAI,YAAY,IAAA,EAAM,WAAW;gBAC7C,OAAA,IAAA,YAAY,GAAA,GAAM,4BAA4B,GAAA,EAAK;oBAClC,0BAAA,GAAA,CAAI,YAAY,IAAA,EAAM,IAAI;gBAAA;gBAGtD,IAAI,0BAA0B,IAAA,KAAS,IAAA,CAAK,OAAA,CAAQ,KAAA,EAAO;oBACzD;gBAAA;YACF;YAGF,OAAO,qBAAqB,IAAA,KAAS,IAAA,CAAK,OAAA,CAAQ,KAAA,GAC9C,MAAM,IAAA,CAAK,qBAAqB,MAAA,CAAA,CAAQ,EAAE,IAAA,CAAK,CAAC,GAAG,MAAM;gBACnD,IAAA,EAAE,GAAA,KAAQ,EAAE,GAAA,EAAK;oBACZ,OAAA,EAAE,KAAA,GAAQ,EAAE,KAAA;gBAAA;gBAGd,OAAA,EAAE,GAAA,GAAM,EAAE,GAAA;YAAA,CAClB,CAAA,CAAE,CAAC,CAAA,GACJ,KAAA;QACN;QAEA,IAAA,CAAQ,qBAAA,oNAAwB,OAAA,EAC9B,IAAM;gBACJ,IAAA,CAAK,OAAA,CAAQ,KAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,YAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,YAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,UAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,OAAA;aACf,EACA,CAAC,OAAO,cAAc,cAAc,YAAY,YAAY;YAC1D,IAAA,CAAK,2BAAA,GAA8B,CAAC,CAAA;YAC7B,OAAA;gBACL;gBACA;gBACA;gBACA;gBACA;YACF;QACF,GACA;YACE,KAAK;QAAA;QAIT,IAAA,CAAQ,eAAA,OAAkB,oNAAA,EACxB,IAAM;gBAAC,IAAA,CAAK,qBAAA;gBAAyB,IAAA,CAAK,aAAa;aAAA,EACvD,CACE,EAAE,KAAA,EAAO,YAAA,EAAc,YAAA,EAAc,UAAA,EAAY,OAAA,EAAA,EACjD,kBACG;YACH,IAAI,CAAC,SAAS;gBACZ,IAAA,CAAK,iBAAA,GAAoB,CAAC,CAAA;gBAC1B,IAAA,CAAK,aAAA,CAAc,KAAA,CAAM;gBACzB,OAAO,CAAC,CAAA;YAAA;YAGN,IAAA,IAAA,CAAK,iBAAA,CAAkB,MAAA,KAAW,GAAG;gBAClC,IAAA,CAAA,iBAAA,GAAoB,IAAA,CAAK,OAAA,CAAQ,wBAAA;gBACjC,IAAA,CAAA,iBAAA,CAAkB,OAAA,CAAQ,CAAC,SAAS;oBACvC,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,KAAK,GAAA,EAAK,KAAK,IAAI;gBAAA,CAC3C;YAAA;YAGG,MAAA,MACJ,IAAA,CAAK,2BAAA,CAA4B,MAAA,GAAS,IACtC,KAAK,GAAA,CAAI,GAAG,IAAA,CAAK,2BAA2B,IAC5C;YACN,IAAA,CAAK,2BAAA,GAA8B,CAAC,CAAA;YAEpC,MAAM,eAAe,IAAA,CAAK,iBAAA,CAAkB,KAAA,CAAM,GAAG,GAAG;YAExD,IAAA,IAAS,IAAI,KAAK,IAAI,OAAO,IAAK;gBAC1B,MAAA,MAAM,WAAW,CAAC;gBAExB,MAAM,sBACJ,IAAA,CAAK,OAAA,CAAQ,KAAA,KAAU,IACnB,YAAA,CAAa,IAAI,CAAC,CAAA,GAClB,IAAA,CAAK,sBAAA,CAAuB,cAAc,CAAC;gBAEjD,MAAM,QAAQ,sBACV,oBAAoB,GAAA,GAAM,IAAA,CAAK,OAAA,CAAQ,GAAA,GACvC,eAAe;gBAEb,MAAA,eAAe,cAAc,GAAA,CAAI,GAAG;gBACpC,MAAA,OACJ,OAAO,iBAAiB,WACpB,eACA,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAa,CAAC;gBAEjC,MAAM,MAAM,QAAQ;gBAEpB,MAAM,OAAO,sBACT,oBAAoB,IAAA,GACpB,IAAI,IAAA,CAAK,OAAA,CAAQ,KAAA;gBAErB,YAAA,CAAa,CAAC,CAAA,GAAI;oBAChB,OAAO;oBACP;oBACA;oBACA;oBACA;oBACA;gBACF;YAAA;YAGF,IAAA,CAAK,iBAAA,GAAoB;YAElB,OAAA;QACT,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAIb,IAAA,CAAA,cAAA,GAAA,wNAAA,EACf,IAAM;gBAAC,IAAA,CAAK,eAAA;gBAAmB,IAAA,CAAK,OAAA;gBAAW,IAAA,CAAK,eAAA,EAAiB;aAAA,EACrE,CAAC,cAAc,WAAW,iBAAiB;YACzC,OAAQ,IAAA,CAAK,KAAA,GACX,aAAa,MAAA,GAAS,KAAK,YAAY,IACnC,eAAe;gBACb;gBACA;gBACA;YACD,CAAA,IACD;QACR,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAI9B,IAAA,CAAQ,UAAA,oNAAa,OAAA,EACnB,MAAM;YACJ,IAAI,aAA4B;YAChC,IAAI,WAA0B;YACxB,MAAA,QAAQ,IAAA,CAAK,cAAA,CAAe;YAClC,IAAI,OAAO;gBACT,aAAa,MAAM,UAAA;gBACnB,WAAW,MAAM,QAAA;YAAA;YAEZ,OAAA;gBACL,IAAA,CAAK,OAAA,CAAQ,cAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,QAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,KAAA;gBACb;gBACA;aACF;QACF,GACA,CAAC,gBAAgB,UAAU,OAAO,YAAY,aAAa;YACzD,OAAO,eAAe,QAAQ,aAAa,OACvC,CAAA,CAAA,GACA,eAAe;gBACb;gBACA;gBACA;gBACA;YAAA,CACD;QACP,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAI9B,IAAA,CAAA,gBAAA,GAAmB,CAAC,SAAuB;YACnC,MAAA,gBAAgB,IAAA,CAAK,OAAA,CAAQ,cAAA;YAC7B,MAAA,WAAW,KAAK,YAAA,CAAa,aAAa;YAEhD,IAAI,CAAC,UAAU;gBACL,QAAA,IAAA,CACN,CAAA,wBAAA,EAA2B,aAAa,CAAA,8BAAA,CAAA;gBAEnC,OAAA,CAAA;YAAA;YAGF,OAAA,SAAS,UAAU,EAAE;QAC9B;QAEQ,IAAA,CAAA,eAAA,GAAkB,CACxB,MACA,UACG;YACG,MAAA,QAAQ,IAAA,CAAK,gBAAA,CAAiB,IAAI;YAClC,MAAA,OAAO,IAAA,CAAK,iBAAA,CAAkB,KAAK,CAAA;YACzC,IAAI,CAAC,MAAM;gBACT;YAAA;YAEF,MAAM,MAAM,KAAK,GAAA;YACjB,MAAM,WAAW,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,GAAG;YAE3C,IAAI,aAAa,MAAM;gBACrB,IAAI,UAAU;oBACP,IAAA,CAAA,QAAA,CAAS,SAAA,CAAU,QAAQ;gBAAA;gBAE7B,IAAA,CAAA,QAAA,CAAS,OAAA,CAAQ,IAAI;gBACrB,IAAA,CAAA,aAAA,CAAc,GAAA,CAAI,KAAK,IAAI;YAAA;YAGlC,IAAI,KAAK,WAAA,EAAa;gBACf,IAAA,CAAA,UAAA,CAAW,OAAO,IAAA,CAAK,OAAA,CAAQ,cAAA,CAAe,MAAM,OAAO,IAAI,CAAC;YAAA;QAEzE;QAEa,IAAA,CAAA,UAAA,GAAA,CAAC,OAAe,SAAiB;YACtC,MAAA,OAAO,IAAA,CAAK,iBAAA,CAAkB,KAAK,CAAA;YACzC,IAAI,CAAC,MAAM;gBACT;YAAA;YAEF,MAAM,WAAW,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,KAAK,GAAG,KAAK,KAAK,IAAA;YAC1D,MAAM,QAAQ,OAAO;YAErB,IAAI,UAAU,GAAG;gBACf,IACE,IAAA,CAAK,0CAAA,KAA+C,KAAA,IAChD,IAAA,CAAK,0CAAA,CAA2C,MAAM,OAAO,IAAI,IACjE,KAAK,KAAA,GAAQ,IAAA,CAAK,eAAA,CAAgB,IAAI,IAAA,CAAK,iBAAA,EAC/C;oBACA,IAAI,QAAQ,IAAI,wCAAa,gBAAgB,IAAA,CAAK,OAAA,CAAQ,KAAA,EAAO;wBACvD,QAAA,IAAA,CAAK,cAAc,KAAK;oBAAA;oBAG7B,IAAA,CAAA,eAAA,CAAgB,IAAA,CAAK,eAAA,IAAmB;wBAC3C,aAAc,IAAA,CAAK,iBAAA,IAAqB;wBACxC,UAAU,KAAA;oBAAA,CACX;gBAAA;gBAGE,IAAA,CAAA,2BAAA,CAA4B,IAAA,CAAK,KAAK,KAAK;gBAC3C,IAAA,CAAA,aAAA,GAAgB,IAAI,IAAI,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,KAAK,GAAA,EAAK,IAAI,CAAC;gBAEnE,IAAA,CAAK,MAAA,CAAO,KAAK;YAAA;QAErB;QAEA,IAAA,CAAA,cAAA,GAAiB,CAAC,SAA0C;YAC1D,IAAI,CAAC,MAAM;gBACT,IAAA,CAAK,aAAA,CAAc,OAAA,CAAQ,CAAC,QAAQ,QAAQ;oBACtC,IAAA,CAAC,OAAO,WAAA,EAAa;wBAClB,IAAA,CAAA,QAAA,CAAS,SAAA,CAAU,MAAM;wBACzB,IAAA,CAAA,aAAA,CAAc,MAAA,CAAO,GAAG;oBAAA;gBAC/B,CACD;gBACD;YAAA;YAGG,IAAA,CAAA,eAAA,CAAgB,MAAM,KAAA,CAAS;QACtC;QAEkB,IAAA,CAAA,eAAA,oNAAA,OAAA,EAChB,IAAM;gBAAC,IAAA,CAAK,UAAA,CAAc;gBAAA,IAAA,CAAK,eAAA,EAAiB;aAAA,EAChD,CAAC,SAAS,iBAAiB;YACzB,MAAM,eAAmC,CAAC,CAAA;YAE1C,IAAA,IAAS,IAAI,GAAG,MAAM,QAAQ,MAAA,EAAQ,IAAI,KAAK,IAAK;gBAC5C,MAAA,IAAI,OAAA,CAAQ,CAAC,CAAA;gBACb,MAAA,cAAc,YAAA,CAAa,CAAC,CAAA;gBAElC,aAAa,IAAA,CAAK,WAAW;YAAA;YAGxB,OAAA;QACT,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAI9B,IAAA,CAAA,uBAAA,GAA0B,CAAC,WAAmB;YACtC,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YACtC,IAAA,aAAa,MAAA,KAAW,GAAG;gBACtB,OAAA,KAAA;YAAA;YAEF,OAAA,gOAAA,EACL,YAAA,CACE,wBACE,GACA,aAAa,MAAA,GAAS,GACtB,CAAC,QAAkB,gOAAA,EAAa,YAAA,CAAa,KAAK,CAAC,EAAE,KAAA,EACrD,QAEJ;QAEJ;QAEwB,IAAA,CAAA,qBAAA,GAAA,CAAC,UAAkB,UAA2B;YAC9D,MAAA,OAAO,IAAA,CAAK,OAAA,CAAQ;YACpB,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YAE1C,IAAI,UAAU,QAAQ;gBAChB,IAAA,YAAY,eAAe,MAAM;oBAC3B,QAAA;gBAAA;YACV;YAGF,IAAI,UAAU,OAAO;gBACP,YAAA;YAAA;YAGd,MAAM,iBAAiB,IAAA,CAAK,OAAA,CAAQ,UAAA,GAChC,gBACA;YACJ,MAAM,aAAa,IAAA,CAAK,aAAA,GACpB,cAAc,IAAA,CAAK,aAAA,GACjB,IAAA,CAAK,aAAA,CAAc,QAAA,CAAS,eAAA,CAAgB,cAAc,CAAA,GAC1D,IAAA,CAAK,aAAA,CAAc,cAAc,CAAA,GACnC;YAEJ,MAAM,YAAY,aAAa;YAE/B,OAAO,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,WAAW,QAAQ,GAAG,CAAC;QAClD;QAEoB,IAAA,CAAA,iBAAA,GAAA,CAAC,OAAe,QAAyB,MAAA,KAAW;YAC9D,QAAA,KAAK,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA,GAAQ,CAAC,CAAC;YAErD,MAAA,OAAO,IAAA,CAAK,iBAAA,CAAkB,KAAK,CAAA;YACzC,IAAI,CAAC,MAAM;gBACF,OAAA,KAAA;YAAA;YAGH,MAAA,OAAO,IAAA,CAAK,OAAA,CAAQ;YACpB,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YAE1C,IAAI,UAAU,QAAQ;gBACpB,IAAI,KAAK,GAAA,IAAO,eAAe,OAAO,IAAA,CAAK,OAAA,CAAQ,gBAAA,EAAkB;oBAC3D,QAAA;gBAAA,OAAA,IACC,KAAK,KAAA,IAAS,eAAe,IAAA,CAAK,OAAA,CAAQ,kBAAA,EAAoB;oBAC/D,QAAA;gBAAA,OACH;oBACE,OAAA;wBAAC;wBAAc,KAAK;qBAAA;gBAAA;YAC7B;YAGI,MAAA,eACJ,KAAK,KAAA,GAAQ,IAAA,CAAK,OAAA,CAAQ,kBAAA,GAAA,CAAsB,KAAK,IAAA,GAAO,IAAA,IAAQ;YAEtE,OAAQ,OAAO;gBACb,KAAK;oBACH,OAAO;wBAAC,IAAA,CAAK,qBAAA,CAAsB,cAAc,KAAK;wBAAG,KAAK;qBAAA;gBAChE,KAAK;oBACI,OAAA;wBACL,IAAA,CAAK,qBAAA,CACH,KAAK,GAAA,GAAM,IAAA,CAAK,OAAA,CAAQ,gBAAA,EACxB;wBAEF;qBACF;gBACF;oBACS,OAAA;wBACL,IAAA,CAAK,qBAAA,CACH,KAAK,KAAA,GAAQ,IAAA,CAAK,OAAA,CAAQ,kBAAA,EAC1B;wBAEF;qBACF;YAAA;QAEN;QAEA,IAAA,CAAQ,aAAA,GAAgB,IAAM,IAAA,CAAK,aAAA,CAAc,IAAA,GAAO;QAExD,IAAA,CAAQ,mBAAA,GAAsB,MAAM;YAClC,IAAI,IAAA,CAAK,sBAAA,KAA2B,QAAQ,IAAA,CAAK,YAAA,EAAc;gBACxD,IAAA,CAAA,YAAA,CAAa,YAAA,CAAa,IAAA,CAAK,sBAAsB;gBAC1D,IAAA,CAAK,sBAAA,GAAyB;YAAA;QAElC;QAEiB,IAAA,CAAA,cAAA,GAAA,CACf,UACA,EAAE,QAAQ,OAAA,EAAS,QAAA,CAAS,CAAA,GAA2B,CAAA,CAAA,KACpD;YACH,IAAA,CAAK,mBAAA,CAAoB;YAEzB,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAA,GAAiB;gBACzC,QAAA,IAAA,CACN;YACF;YAGF,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,qBAAA,CAAsB,UAAU,KAAK,GAAG;gBAChE,aAAa,KAAA;gBACb;YAAA,CACD;QACH;QAEgB,IAAA,CAAA,aAAA,GAAA,CACd,OACA,EAAE,OAAO,eAAe,MAAA,EAAQ,QAAA,CAAmC,CAAA,GAAA,CAAA,CAAA,KAChE;YACK,QAAA,KAAK,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA,GAAQ,CAAC,CAAC;YAE3D,IAAA,CAAK,mBAAA,CAAoB;YAEzB,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAA,GAAiB;gBACzC,QAAA,IAAA,CACN;YACF;YAGF,MAAM,iBAAiB,IAAA,CAAK,iBAAA,CAAkB,OAAO,YAAY;YACjE,IAAI,CAAC,eAAgB,CAAA;YAEf,MAAA,CAAC,QAAQ,KAAK,CAAA,GAAI;YAExB,IAAA,CAAK,eAAA,CAAgB,QAAQ;gBAAE,aAAa,KAAA;gBAAW;YAAA,CAAU;YAEjE,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAc,KAAK,IAAA,CAAK,YAAA,EAAc;gBACtE,IAAA,CAAK,sBAAA,GAAyB,IAAA,CAAK,YAAA,CAAa,UAAA,CAAW,MAAM;oBAC/D,IAAA,CAAK,sBAAA,GAAyB;oBAExB,MAAA,eAAe,IAAA,CAAK,aAAA,CAAc,GAAA,CACtC,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,KAAK;oBAG/B,IAAI,cAAc;wBACV,MAAA,CAAC,YAAY,CAAA,oNAAI,eAAA,EACrB,IAAA,CAAK,iBAAA,CAAkB,OAAO,KAAK;wBAGrC,IAAI,kNAAC,cAAA,EAAY,cAAc,IAAA,CAAK,eAAA,CAAiB,CAAA,GAAG;4BACtD,IAAA,CAAK,aAAA,CAAc,OAAO;gCAAE;gCAAO;4BAAA,CAAU;wBAAA;oBAC/C,OACK;wBACL,IAAA,CAAK,aAAA,CAAc,OAAO;4BAAE;4BAAO;wBAAA,CAAU;oBAAA;gBAC/C,CACD;YAAA;QAEL;QAEA,IAAA,CAAA,QAAA,GAAW,CAAC,OAAe,EAAE,QAAA,CAAS,CAAA,GAA2B,CAAA,CAAA,KAAO;YACtE,IAAA,CAAK,mBAAA,CAAoB;YAEzB,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAA,GAAiB;gBACzC,QAAA,IAAA,CACN;YACF;YAGF,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,eAAA,CAAgB,IAAI,OAAO;gBACnD,aAAa,KAAA;gBACb;YAAA,CACD;QACH;QAEA,IAAA,CAAA,YAAA,GAAe,MAAM;;YACb,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YAEtC,IAAA;YAEA,IAAA,aAAa,MAAA,KAAW,GAAG;gBAC7B,MAAM,IAAA,CAAK,OAAA,CAAQ,YAAA;YAAA,OACd;gBAGH,MAAA,IAAA,CAAK,OAAA,CAAQ,KAAA,KAAU,IAAA,CAAA,CAClB,KAAA,YAAA,CAAa,aAAa,MAAA,GAAS,CAAC,CAAA,KAApC,OAAA,KAAA,IAAA,GAAuC,GAAA,KAAO,IAC/C,KAAK,GAAA,IACA,aAAa,KAAA,CAAM,CAAC,IAAA,CAAK,OAAA,CAAQ,KAAK,EAAE,GAAA,CAAI,CAAC,IAAM,EAAE,GAAG;YAC7D;YAGR,OAAO,KAAK,GAAA,CACV,MAAM,IAAA,CAAK,OAAA,CAAQ,YAAA,GAAe,IAAA,CAAK,OAAA,CAAQ,UAAA,EAC/C;QAEJ;QAEQ,IAAA,CAAA,eAAA,GAAkB,CACxB,QACA,EACE,WAAA,EACA,QAAA,EAAA,KAKC;YACH,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,QAAQ;gBAAE;gBAAU;YAAA,GAAe,IAAI;QACjE;QAEA,IAAA,CAAA,OAAA,GAAU,MAAM;YACT,IAAA,CAAA,aAAA,GAAA,aAAA,GAAA,IAAoB,IAAI;YAC7B,IAAA,CAAK,MAAA,CAAO,KAAK;QACnB;QAvpBE,IAAA,CAAK,UAAA,CAAW,IAAI;IAAA;AAwpBxB;AAEA,MAAM,0BAA0B,CAC9B,KACA,MACA,iBACA,UACG;IACH,MAAO,OAAO,KAAM;QACZ,MAAA,SAAA,CAAW,MAAM,IAAA,IAAQ,IAAK;QAC9B,MAAA,eAAe,gBAAgB,MAAM;QAE3C,IAAI,eAAe,OAAO;YACxB,MAAM,SAAS;QAAA,OAAA,IACN,eAAe,OAAO;YAC/B,OAAO,SAAS;QAAA,OACX;YACE,OAAA;QAAA;IACT;IAGF,IAAI,MAAM,GAAG;QACX,OAAO,MAAM;IAAA,OACR;QACE,OAAA;IAAA;AAEX;AAEA,SAAS,eAAe,EACtB,YAAA,EACA,SAAA,EACA,YAAA,EACF,EAIG;IACK,MAAA,QAAQ,aAAa,MAAA,GAAS;IACpC,MAAM,YAAY,CAAC,QAAkB,YAAA,CAAa,KAAK,CAAA,CAAG,KAAA;IAE1D,MAAM,aAAa,wBAAwB,GAAG,OAAO,WAAW,YAAY;IAC5E,IAAI,WAAW;IAEf,MACE,WAAW,SACX,YAAA,CAAa,QAAQ,CAAA,CAAG,GAAA,GAAM,eAAe,UAC7C;QACA;IAAA;IAGK,OAAA;QAAE;QAAY;IAAS;AAChC", "debugId": null}}, {"offset": {"line": 5494, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@heroui/table/node_modules/@tanstack/virtual-core/dist/esm/utils.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/node_modules/%40tanstack/virtual-core/src/utils.ts"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  return (): TResult => {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) < 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAIgB,SAAA,KACd,OAAA,EACA,EAAA,EACA,IAAA,EAMA;IACI,IAAA,OAAO,KAAK,WAAA,IAAe,CAAC,CAAA;IAC5B,IAAA;IAEJ,OAAO,MAAe;QAbR,IAAA,IAAA,IAAA,IAAA;QAcR,IAAA;QACJ,IAAI,KAAK,GAAA,IAAA,CAAA,CAAO,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,KAAA,EAAgB,CAAA,UAAU,KAAK,GAAA,CAAI;QAEnD,MAAM,UAAU,QAAQ;QAExB,MAAM,cACJ,QAAQ,MAAA,KAAW,KAAK,MAAA,IACxB,QAAQ,IAAA,CAAK,CAAC,KAAU,QAAkB,IAAA,CAAK,KAAK,CAAA,KAAM,GAAG;QAE/D,IAAI,CAAC,aAAa;YACT,OAAA;QAAA;QAGF,OAAA;QAEH,IAAA;QACJ,IAAI,KAAK,GAAA,IAAA,CAAA,CAAO,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,KAAA,EAAgB,CAAA,aAAa,KAAK,GAAA,CAAI;QAE7C,SAAA,GAAG,GAAG,OAAO;QAEtB,IAAI,KAAK,GAAA,IAAA,CAAA,CAAO,KAAA,KAAK,KAAA,KAAL,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,KAAA,GAAgB;YACxB,MAAA,aAAa,KAAK,KAAA,CAAA,CAAO,KAAK,GAAA,KAAQ,OAAA,IAAY,GAAG,IAAI;YACzD,MAAA,gBAAgB,KAAK,KAAA,CAAA,CAAO,KAAK,GAAA,KAAQ,UAAA,IAAe,GAAG,IAAI;YACrE,MAAM,sBAAsB,gBAAgB;YAEtC,MAAA,MAAM,CAAC,KAAsB,QAAgB;gBACjD,MAAM,OAAO,GAAG;gBACT,MAAA,IAAI,MAAA,GAAS,IAAK;oBACvB,MAAM,MAAM;gBAAA;gBAEP,OAAA;YACT;YAEQ,QAAA,IAAA,CACN,CAAA,IAAA,EAAO,IAAI,eAAe,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,YAAY,CAAC,CAAC,CAAA,GAAA,CAAA,EACnD,CAAA;;;uBAAA,EAGiB,KAAK,GAAA,CAChB,GACA,KAAK,GAAA,CAAI,MAAM,MAAM,qBAAqB,GAAG,GAC9C,cAAA,CAAA,EACL,QAAA,OAAA,KAAA,IAAA,KAAM,GAAA;QACR;QAGF,CAAA,KAAA,QAAA,OAAA,KAAA,IAAA,KAAM,QAAA,KAAN,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,MAAiB;QAEV,OAAA;IACT;AACF;AAEgB,SAAA,aAAgB,KAAA,EAAsB,GAAA,EAAiB;IACrE,IAAI,UAAU,KAAA,GAAW;QACjB,MAAA,IAAI,MAAM,CAAA,oBAAA,EAAuB,MAAM,CAAA,EAAA,EAAK,GAAG,EAAA,GAAK,EAAE,EAAE;IAAA,OACzD;QACE,OAAA;IAAA;AAEX;AAEa,MAAA,cAAc,CAAC,GAAW,IAAc,KAAK,GAAA,CAAI,IAAI,CAAC,IAAI;AAEhE,MAAM,WAAW,CACtB,cACA,IACA,OACG;IACC,IAAA;IACJ,OAAO,SAAA,GAAwB,IAAA,EAAkB;QAC/C,aAAa,YAAA,CAAa,SAAS;QACvB,YAAA,aAAa,UAAA,CAAW,IAAM,GAAG,KAAA,CAAM,IAAA,EAAM,IAAI,GAAG,EAAE;IACpE;AACF", "debugId": null}}, {"offset": {"line": 5557, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@heroui/table/node_modules/@tanstack/virtual-core/dist/esm/index.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/node_modules/%40tanstack/virtual-core/src/index.ts"], "sourcesContent": ["import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(element.getBoundingClientRect())\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const entry = entries[0]\n    if (entry?.borderBoxSize) {\n      const box = entry.borderBoxSize[0]\n      if (box) {\n        handler({ width: box.inlineSize, height: box.blockSize })\n        return\n      }\n    }\n    handler(element.getBoundingClientRect())\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('scroll', handler)\n    element.removeEventListener('scrollend', endHandler)\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('scroll', handler)\n    element.removeEventListener('scrollend', endHandler)\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n  return Math.round(\n    element.getBoundingClientRect()[\n      instance.options.horizontal ? 'width' : 'height'\n    ],\n  )\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  private scrollToIndexTimeoutId: number | null = null\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          this._measureElement(entry.target as TItemElement, entry)\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: true,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [this.getMeasurements(), this.getSize(), this.getScrollOffset()],\n    (measurements, outerSize, scrollOffset) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  private getIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (toOffset: number, align: ScrollAlignment) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (toOffset >= scrollOffset + size) {\n        align = 'end'\n      }\n    }\n\n    if (align === 'end') {\n      toOffset -= size\n    }\n\n    const scrollSizeProp = this.options.horizontal\n      ? 'scrollWidth'\n      : 'scrollHeight'\n    const scrollSize = this.scrollElement\n      ? 'document' in this.scrollElement\n        ? this.scrollElement.document.documentElement[scrollSizeProp]\n        : this.scrollElement[scrollSizeProp]\n      : 0\n\n    const maxOffset = scrollSize - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const centerOffset =\n      item.start - this.options.scrollPaddingStart + (item.size - size) / 2\n\n    switch (align) {\n      case 'center':\n        return [this.getOffsetForAlignment(centerOffset, align), align] as const\n      case 'end':\n        return [\n          this.getOffsetForAlignment(\n            item.end + this.options.scrollPaddingEnd,\n            align,\n          ),\n          align,\n        ] as const\n      default:\n        return [\n          this.getOffsetForAlignment(\n            item.start - this.options.scrollPaddingStart,\n            align,\n          ),\n          align,\n        ] as const\n    }\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  private cancelScrollToIndex = () => {\n    if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n      this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId)\n      this.scrollToIndexTimeoutId = null\n    }\n  }\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    const offsetAndAlign = this.getOffsetForIndex(index, initialAlign)\n    if (!offsetAndAlign) return\n\n    const [offset, align] = offsetAndAlign\n\n    this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n    if (behavior !== 'smooth' && this.isDynamicMode() && this.targetWindow) {\n      this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n        this.scrollToIndexTimeoutId = null\n\n        const elementInDOM = this.elementsCache.has(\n          this.options.getItemKey(index),\n        )\n\n        if (elementInDOM) {\n          const [latestOffset] = notUndefined(\n            this.getOffsetForIndex(index, align),\n          )\n\n          if (!approxEqual(latestOffset, this.getScrollOffset())) {\n            this.scrollToIndex(index, { align, behavior })\n          }\n        } else {\n          this.scrollToIndex(index, { align, behavior })\n        }\n      })\n    }\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else {\n      // If lanes is 1, use the last measurement's end, otherwise find the maximum end value among all measurements\n      end =\n        this.options.lanes === 1\n          ? (measurements[measurements.length - 1]?.end ?? 0)\n          : Math.max(\n              ...measurements.slice(-this.options.lanes).map((m) => m.end),\n            )\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n}) {\n  const count = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  const startIndex = findNearestBinarySearch(0, count, getOffset, scrollOffset)\n  let endIndex = startIndex\n\n  while (\n    endIndex < count &&\n    measurements[endIndex]!.end < scrollOffset + outerSize\n  ) {\n    endIndex++\n  }\n\n  return { startIndex, endIndex }\n}\n"], "names": ["opts"], "mappings": ";;;;;;;;;;;;;;AA8Ca,MAAA,sBAAsB,CAAC,QAAkB;AAEzC,MAAA,wBAAwB,CAAC,UAAiB;IACrD,MAAM,QAAQ,KAAK,GAAA,CAAI,MAAM,UAAA,GAAa,MAAM,QAAA,EAAU,CAAC;IACrD,MAAA,MAAM,KAAK,GAAA,CAAI,MAAM,QAAA,GAAW,MAAM,QAAA,EAAU,MAAM,KAAA,GAAQ,CAAC;IAErE,MAAM,MAAM,CAAC,CAAA;IAEb,IAAA,IAAS,IAAI,OAAO,KAAK,KAAK,IAAK;QACjC,IAAI,IAAA,CAAK,CAAC;IAAA;IAGL,OAAA;AACT;AAEa,MAAA,qBAAqB,CAChC,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAEF,MAAM,eAAe,SAAS,YAAA;IAC9B,IAAI,CAAC,cAAc;QACjB;IAAA;IAGI,MAAA,UAAU,CAAC,SAAe;QACxB,MAAA,EAAE,KAAA,EAAO,MAAA,CAAA,CAAA,GAAW;QACvB,GAAA;YAAE,OAAO,KAAK,KAAA,CAAM,KAAK;YAAG,QAAQ,KAAK,KAAA,CAAM,MAAM;QAAA,CAAG;IAC7D;IAEQ,QAAA,QAAQ,qBAAA,EAAuB;IAEnC,IAAA,CAAC,aAAa,cAAA,EAAgB;QAChC,OAAO,KAAO,CAAD;IAAC;IAGhB,MAAM,WAAW,IAAI,aAAa,cAAA,CAAe,CAAC,YAAY;QACtD,MAAA,QAAQ,OAAA,CAAQ,CAAC,CAAA;QACvB,IAAI,SAAA,OAAA,KAAA,IAAA,MAAO,aAAA,EAAe;YAClB,MAAA,MAAM,MAAM,aAAA,CAAc,CAAC,CAAA;YACjC,IAAI,KAAK;gBACP,QAAQ;oBAAE,OAAO,IAAI,UAAA;oBAAY,QAAQ,IAAI,SAAA;gBAAA,CAAW;gBACxD;YAAA;QACF;QAEM,QAAA,QAAQ,qBAAA,EAAuB;IAAA,CACxC;IAED,SAAS,OAAA,CAAQ,SAAS;QAAE,KAAK;IAAA,CAAc;IAE/C,OAAO,MAAM;QACX,SAAS,SAAA,CAAU,OAAO;IAC5B;AACF;AAEA,MAAM,0BAA0B;IAC9B,SAAS;AACX;AAEa,MAAA,oBAAoB,CAC/B,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAGF,MAAM,UAAU,MAAM;QACpB,GAAG;YAAE,OAAO,QAAQ,UAAA;YAAY,QAAQ,QAAQ,WAAA;QAAA,CAAa;IAC/D;IACQ,QAAA;IAEA,QAAA,gBAAA,CAAiB,UAAU,SAAS,uBAAuB;IAEnE,OAAO,MAAM;QACH,QAAA,mBAAA,CAAoB,UAAU,OAAO;IAC/C;AACF;AAEA,MAAM,oBACJ,OAAO,UAAU,sBAAc,OAAO,iBAAiB;AAI5C,MAAA,uBAAuB,CAClC,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAEF,MAAM,eAAe,SAAS,YAAA;IAC9B,IAAI,CAAC,cAAc;QACjB;IAAA;IAGF,IAAI,SAAS;IACb,MAAM,WACJ,SAAS,OAAA,CAAQ,iBAAA,IAAqB,oBAClC,IAAM,KAAA,mNACN,WAAA,EACE,cACA,MAAM;QACJ,GAAG,QAAQ,KAAK;IAClB,GACA,SAAS,OAAA,CAAQ,qBAAA;IAGnB,MAAA,gBAAgB,CAAC,cAAyB,MAAM;YACpD,MAAM,EAAE,UAAA,EAAY,KAAA,CAAM,CAAA,GAAI,SAAS,OAAA;YAC9B,SAAA,aACL,OAAA,CAAQ,YAAY,CAAA,GAAA,CAAM,SAAS,CAAA,KAAO,CAAA,IAC1C,OAAA,CAAQ,WAAW,CAAA;YACd,SAAA;YACT,GAAG,QAAQ,WAAW;QACxB;IACM,MAAA,UAAU,cAAc,IAAI;IAC5B,MAAA,aAAa,cAAc,KAAK;IAC3B,WAAA;IAEH,QAAA,gBAAA,CAAiB,UAAU,SAAS,uBAAuB;IAC3D,QAAA,gBAAA,CAAiB,aAAa,YAAY,uBAAuB;IAEzE,OAAO,MAAM;QACH,QAAA,mBAAA,CAAoB,UAAU,OAAO;QACrC,QAAA,mBAAA,CAAoB,aAAa,UAAU;IACrD;AACF;AAEa,MAAA,sBAAsB,CACjC,UACA,OACG;IACH,MAAM,UAAU,SAAS,aAAA;IACzB,IAAI,CAAC,SAAS;QACZ;IAAA;IAEF,MAAM,eAAe,SAAS,YAAA;IAC9B,IAAI,CAAC,cAAc;QACjB;IAAA;IAGF,IAAI,SAAS;IACb,MAAM,WACJ,SAAS,OAAA,CAAQ,iBAAA,IAAqB,oBAClC,IAAM,KAAA,mNACN,WAAA,EACE,cACA,MAAM;QACJ,GAAG,QAAQ,KAAK;IAClB,GACA,SAAS,OAAA,CAAQ,qBAAA;IAGnB,MAAA,gBAAgB,CAAC,cAAyB,MAAM;YACpD,SAAS,OAAA,CAAQ,SAAS,OAAA,CAAQ,UAAA,GAAa,YAAY,SAAS,CAAA;YAC3D,SAAA;YACT,GAAG,QAAQ,WAAW;QACxB;IACM,MAAA,UAAU,cAAc,IAAI;IAC5B,MAAA,aAAa,cAAc,KAAK;IAC3B,WAAA;IAEH,QAAA,gBAAA,CAAiB,UAAU,SAAS,uBAAuB;IAC3D,QAAA,gBAAA,CAAiB,aAAa,YAAY,uBAAuB;IAEzE,OAAO,MAAM;QACH,QAAA,mBAAA,CAAoB,UAAU,OAAO;QACrC,QAAA,mBAAA,CAAoB,aAAa,UAAU;IACrD;AACF;AAEO,MAAM,iBAAiB,CAC5B,SACA,OACA,aACG;IACH,IAAI,SAAA,OAAA,KAAA,IAAA,MAAO,aAAA,EAAe;QAClB,MAAA,MAAM,MAAM,aAAA,CAAc,CAAC,CAAA;QACjC,IAAI,KAAK;YACP,MAAM,OAAO,KAAK,KAAA,CAChB,GAAA,CAAI,SAAS,OAAA,CAAQ,UAAA,GAAa,eAAe,WAAW,CAAA;YAEvD,OAAA;QAAA;IACT;IAEF,OAAO,KAAK,KAAA,CACV,QAAQ,qBAAA,CAAsB,CAAA,CAC5B,SAAS,OAAA,CAAQ,UAAA,GAAa,UAAU,QAC1C,CAAA;AAEJ;AAEa,MAAA,eAAe,CAC1B,QACA,EACE,cAAc,CAAA,EACd,QAAA,EACF,EACA,aACG;;IACH,MAAM,WAAW,SAAS;IAE1B,CAAA,KAAA,CAAA,KAAA,SAAS,aAAA,KAAT,OAAA,KAAA,IAAA,GAAwB,QAAA,KAAxB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmC;QACjC,CAAC,SAAS,OAAA,CAAQ,UAAA,GAAa,SAAS,KAAK,CAAA,EAAG;QAChD;IAAA;AAEJ;AAEa,MAAA,gBAAgB,CAC3B,QACA,EACE,cAAc,CAAA,EACd,QAAA,EACF,EACA,aACG;;IACH,MAAM,WAAW,SAAS;IAE1B,CAAA,KAAA,CAAA,KAAA,SAAS,aAAA,KAAT,OAAA,KAAA,IAAA,GAAwB,QAAA,KAAxB,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmC;QACjC,CAAC,SAAS,OAAA,CAAQ,UAAA,GAAa,SAAS,KAAK,CAAA,EAAG;QAChD;IAAA;AAEJ;AAyDO,MAAM,YAGX;IAqDA,YAAY,IAAA,CAAwD;QApDpE,IAAA,CAAQ,MAAA,GAAqC,CAAC,CAAA;QAEP,IAAA,CAAA,aAAA,GAAA;QACa,IAAA,CAAA,YAAA,GAAA;QACtC,IAAA,CAAA,WAAA,GAAA;QACd,IAAA,CAAQ,sBAAA,GAAwC;QAChD,IAAA,CAAA,iBAAA,GAAwC,CAAC,CAAA;QACjC,IAAA,CAAA,aAAA,GAAA,aAAA,GAAA,IAAoB,IAAiB;QAC7C,IAAA,CAAQ,2BAAA,GAA6C,CAAC,CAAA;QAC5B,IAAA,CAAA,UAAA,GAAA;QACI,IAAA,CAAA,YAAA,GAAA;QACY,IAAA,CAAA,eAAA,GAAA;QAC1C,IAAA,CAAQ,iBAAA,GAAoB;QAQ5B,IAAA,CAAA,aAAA,GAAA,aAAA,GAAA,IAAoB,IAAuB;QAC3C,IAAA,CAAQ,QAAA,GAAkB,aAAA,GAAA,CAAA,MAAA;YACxB,IAAI,MAA6B;YAEjC,MAAM,MAAM,MAAM;gBAChB,IAAI,KAAK;oBACA,OAAA;gBAAA;gBAGT,IAAI,CAAC,IAAA,CAAK,YAAA,IAAgB,CAAC,IAAA,CAAK,YAAA,CAAa,cAAA,EAAgB;oBACpD,OAAA;gBAAA;gBAGT,OAAQ,MAAM,IAAI,IAAA,CAAK,YAAA,CAAa,cAAA,CAAe,CAAC,YAAY;oBACtD,QAAA,OAAA,CAAQ,CAAC,UAAU;wBACpB,IAAA,CAAA,eAAA,CAAgB,MAAM,MAAA,EAAwB,KAAK;oBAAA,CACzD;gBAAA,CACF;YACH;YAEO,OAAA;gBACL,YAAY,MAAM;;oBAChB,CAAA,KAAA,IAAA,CAAA,KAAA,OAAA,KAAA,IAAA,GAAO,UAAA;oBACD,MAAA;gBACR;gBACA,SAAS,CAAC,WAAA;;oBACR,OAAA,CAAA,KAAA,IAAI,CAAA,KAAJ,OAAA,KAAA,IAAA,GAAO,OAAA,CAAQ,QAAQ;wBAAE,KAAK;oBAAA;;gBAChC,WAAW,CAAC,WAAA;;oBAAoB,OAAA,CAAA,KAAA,IAAI,CAAA,KAAJ,OAAA,KAAA,IAAA,GAAO,SAAA,CAAU;gBAAA;YACnD;QAAA,CAAA,EACC;QACsD,IAAA,CAAA,KAAA,GAAA;QAMzD,IAAA,CAAA,UAAA,GAAa,CAACA,UAA2D;YAChE,OAAA,OAAA,CAAQA,KAAI,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;gBAC7C,IAAI,OAAO,UAAU,YAAa,CAAA,OAAQA,KAAAA,CAAa,GAAG,CAAA;YAAA,CAC3D;YAED,IAAA,CAAK,OAAA,GAAU;gBACb,OAAO;gBACP,eAAe;gBACf,UAAU;gBACV,cAAc;gBACd,YAAY;gBACZ,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;gBACZ,YAAY;gBACZ,gBAAgB;gBAChB,UAAU,KAAO,CAAD;gBAChB;gBACA,aAAa;oBAAE,OAAO;oBAAG,QAAQ;gBAAE;gBACnC,cAAc;gBACd,KAAK;gBACL,gBAAgB;gBAChB,0BAA0B,CAAC,CAAA;gBAC3B,OAAO;gBACP,uBAAuB;gBACvB,SAAS;gBACT,OAAO;gBACP,mBAAmB;gBACnB,GAAGA,KAAAA;YACL;QACF;QAEQ,IAAA,CAAA,MAAA,GAAS,CAAC,SAAkB;;YAC7B,CAAA,KAAA,CAAA,KAAA,IAAA,CAAA,OAAA,EAAQ,QAAA,KAAR,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAmB,IAAA,EAAM;QAChC;QAEA,IAAA,CAAQ,WAAA,kNAAc,OAAA,EACpB,MAAM;YACJ,IAAA,CAAK,cAAA,CAAe;YAEb,OAAA;gBACL,IAAA,CAAK,WAAA;gBACL,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,UAAA,GAAa;gBACrC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW;aACrC;QACF,GACA,CAAC,gBAAgB;YACf,IAAA,CAAK,MAAA,CAAO,WAAW;QACzB,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;YAC1B,aAAa;gBACX,IAAA,CAAK,WAAA;gBACL,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,UAAA,GAAa;gBACrC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW;aAAA;QACrC;QAIJ,IAAA,CAAQ,OAAA,GAAU,MAAM;YACjB,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,OAAO,EAAE,OAAA,CAAQ,CAAC,IAAM,GAAI;YAC/C,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA;YACf,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW;YACzB,IAAA,CAAK,aAAA,GAAgB;YACrB,IAAA,CAAK,YAAA,GAAe;QACtB;QAEA,IAAA,CAAA,SAAA,GAAY,MAAM;YAChB,OAAO,MAAM;gBACX,IAAA,CAAK,OAAA,CAAQ;YACf;QACF;QAEA,IAAA,CAAA,WAAA,GAAc,MAAM;;YAClB,MAAM,gBAAgB,IAAA,CAAK,OAAA,CAAQ,OAAA,GAC/B,IAAA,CAAK,OAAA,CAAQ,gBAAA,KACb;YAEA,IAAA,IAAA,CAAK,aAAA,KAAkB,eAAe;gBACxC,IAAA,CAAK,OAAA,CAAQ;gBAEb,IAAI,CAAC,eAAe;oBAClB,IAAA,CAAK,WAAA,CAAY;oBACjB;gBAAA;gBAGF,IAAA,CAAK,aAAA,GAAgB;gBAErB,IAAI,IAAA,CAAK,aAAA,IAAiB,mBAAmB,IAAA,CAAK,aAAA,EAAe;oBAC1D,IAAA,CAAA,YAAA,GAAe,IAAA,CAAK,aAAA,CAAc,aAAA,CAAc,WAAA;gBAAA,OAChD;oBACA,IAAA,CAAA,YAAA,GAAA,CAAA,CAAe,KAAA,IAAA,CAAK,aAAA,KAAL,OAAA,KAAA,IAAA,GAAoB,MAAA,KAAU;gBAAA;gBAG/C,IAAA,CAAA,aAAA,CAAc,OAAA,CAAQ,CAAC,WAAW;oBAChC,IAAA,CAAA,QAAA,CAAS,OAAA,CAAQ,MAAM;gBAAA,CAC7B;gBAEI,IAAA,CAAA,eAAA,CAAgB,IAAA,CAAK,eAAA,IAAmB;oBAC3C,aAAa,KAAA;oBACb,UAAU,KAAA;gBAAA,CACX;gBAED,IAAA,CAAK,MAAA,CAAO,IAAA,CACV,IAAA,CAAK,OAAA,CAAQ,kBAAA,CAAmB,IAAA,EAAM,CAAC,SAAS;oBAC9C,IAAA,CAAK,UAAA,GAAa;oBAClB,IAAA,CAAK,WAAA,CAAY;gBAClB,CAAA;gBAGH,IAAA,CAAK,MAAA,CAAO,IAAA,CACV,IAAA,CAAK,OAAA,CAAQ,oBAAA,CAAqB,IAAA,EAAM,CAAC,QAAQ,gBAAgB;oBAC/D,IAAA,CAAK,iBAAA,GAAoB;oBACzB,IAAA,CAAK,eAAA,GAAkB,cACnB,IAAA,CAAK,eAAA,KAAoB,SACvB,YACA,aACF;oBACJ,IAAA,CAAK,YAAA,GAAe;oBACpB,IAAA,CAAK,WAAA,GAAc;oBAEnB,IAAA,CAAK,WAAA,CAAY;gBAClB,CAAA;YACH;QAEJ;QAEA,IAAA,CAAQ,OAAA,GAAU,MAAM;YAClB,IAAA,CAAC,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;gBACzB,IAAA,CAAK,UAAA,GAAa;gBACX,OAAA;YAAA;YAGT,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,OAAA,CAAQ,WAAA;YAElD,OAAO,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,UAAA,GAAa,UAAU,QAAQ,CAAA;QACrE;QAEA,IAAA,CAAQ,eAAA,GAAkB,MAAM;YAC1B,IAAA,CAAC,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;gBACzB,IAAA,CAAK,YAAA,GAAe;gBACb,OAAA;YAAA;YAGT,IAAA,CAAK,YAAA,GACH,IAAA,CAAK,YAAA,IAAA,CACJ,OAAO,IAAA,CAAK,OAAA,CAAQ,aAAA,KAAkB,aACnC,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc,IAC3B,IAAA,CAAK,OAAA,CAAQ,aAAA;YAEnB,OAAO,IAAA,CAAK,YAAA;QACd;QAEQ,IAAA,CAAA,sBAAA,GAAyB,CAC/B,cACA,UACG;YACG,MAAA,4BAAA,aAAA,GAAA,IAAgC,IAAkB;YAClD,MAAA,uBAAA,aAAA,GAAA,IAA2B,IAAyB;YAC1D,IAAA,IAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAK;gBAC7B,MAAA,cAAc,YAAA,CAAa,CAAC,CAAA;gBAElC,IAAI,0BAA0B,GAAA,CAAI,YAAY,IAAI,GAAG;oBACnD;gBAAA;gBAGF,MAAM,8BAA8B,qBAAqB,GAAA,CACvD,YAAY,IAAA;gBAEd,IACE,+BAA+B,QAC/B,YAAY,GAAA,GAAM,4BAA4B,GAAA,EAC9C;oBACqB,qBAAA,GAAA,CAAI,YAAY,IAAA,EAAM,WAAW;gBAC7C,OAAA,IAAA,YAAY,GAAA,GAAM,4BAA4B,GAAA,EAAK;oBAClC,0BAAA,GAAA,CAAI,YAAY,IAAA,EAAM,IAAI;gBAAA;gBAGtD,IAAI,0BAA0B,IAAA,KAAS,IAAA,CAAK,OAAA,CAAQ,KAAA,EAAO;oBACzD;gBAAA;YACF;YAGF,OAAO,qBAAqB,IAAA,KAAS,IAAA,CAAK,OAAA,CAAQ,KAAA,GAC9C,MAAM,IAAA,CAAK,qBAAqB,MAAA,CAAA,CAAQ,EAAE,IAAA,CAAK,CAAC,GAAG,MAAM;gBACnD,IAAA,EAAE,GAAA,KAAQ,EAAE,GAAA,EAAK;oBACZ,OAAA,EAAE,KAAA,GAAQ,EAAE,KAAA;gBAAA;gBAGd,OAAA,EAAE,GAAA,GAAM,EAAE,GAAA;YAAA,CAClB,CAAA,CAAE,CAAC,CAAA,GACJ,KAAA;QACN;QAEA,IAAA,CAAQ,qBAAA,kNAAwB,OAAA,EAC9B,IAAM;gBACJ,IAAA,CAAK,OAAA,CAAQ,KAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,YAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,YAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,UAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,OAAA;aACf,EACA,CAAC,OAAO,cAAc,cAAc,YAAY,YAAY;YAC1D,IAAA,CAAK,2BAAA,GAA8B,CAAC,CAAA;YAC7B,OAAA;gBACL;gBACA;gBACA;gBACA;gBACA;YACF;QACF,GACA;YACE,KAAK;QAAA;QAIT,IAAA,CAAQ,eAAA,OAAkB,kNAAA,EACxB,IAAM;gBAAC,IAAA,CAAK,qBAAA;gBAAyB,IAAA,CAAK,aAAa;aAAA,EACvD,CACE,EAAE,KAAA,EAAO,YAAA,EAAc,YAAA,EAAc,UAAA,EAAY,OAAA,EAAA,EACjD,kBACG;YACH,IAAI,CAAC,SAAS;gBACZ,IAAA,CAAK,iBAAA,GAAoB,CAAC,CAAA;gBAC1B,IAAA,CAAK,aAAA,CAAc,KAAA,CAAM;gBACzB,OAAO,CAAC,CAAA;YAAA;YAGN,IAAA,IAAA,CAAK,iBAAA,CAAkB,MAAA,KAAW,GAAG;gBAClC,IAAA,CAAA,iBAAA,GAAoB,IAAA,CAAK,OAAA,CAAQ,wBAAA;gBACjC,IAAA,CAAA,iBAAA,CAAkB,OAAA,CAAQ,CAAC,SAAS;oBACvC,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,KAAK,GAAA,EAAK,KAAK,IAAI;gBAAA,CAC3C;YAAA;YAGG,MAAA,MACJ,IAAA,CAAK,2BAAA,CAA4B,MAAA,GAAS,IACtC,KAAK,GAAA,CAAI,GAAG,IAAA,CAAK,2BAA2B,IAC5C;YACN,IAAA,CAAK,2BAAA,GAA8B,CAAC,CAAA;YAEpC,MAAM,eAAe,IAAA,CAAK,iBAAA,CAAkB,KAAA,CAAM,GAAG,GAAG;YAExD,IAAA,IAAS,IAAI,KAAK,IAAI,OAAO,IAAK;gBAC1B,MAAA,MAAM,WAAW,CAAC;gBAExB,MAAM,sBACJ,IAAA,CAAK,OAAA,CAAQ,KAAA,KAAU,IACnB,YAAA,CAAa,IAAI,CAAC,CAAA,GAClB,IAAA,CAAK,sBAAA,CAAuB,cAAc,CAAC;gBAEjD,MAAM,QAAQ,sBACV,oBAAoB,GAAA,GAAM,IAAA,CAAK,OAAA,CAAQ,GAAA,GACvC,eAAe;gBAEb,MAAA,eAAe,cAAc,GAAA,CAAI,GAAG;gBACpC,MAAA,OACJ,OAAO,iBAAiB,WACpB,eACA,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAa,CAAC;gBAEjC,MAAM,MAAM,QAAQ;gBAEpB,MAAM,OAAO,sBACT,oBAAoB,IAAA,GACpB,IAAI,IAAA,CAAK,OAAA,CAAQ,KAAA;gBAErB,YAAA,CAAa,CAAC,CAAA,GAAI;oBAChB,OAAO;oBACP;oBACA;oBACA;oBACA;oBACA;gBACF;YAAA;YAGF,IAAA,CAAK,iBAAA,GAAoB;YAElB,OAAA;QACT,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAIb,IAAA,CAAA,cAAA,GAAA,sNAAA,EACf,IAAM;gBAAC,IAAA,CAAK,eAAA;gBAAmB,IAAA,CAAK,OAAA;gBAAW,IAAA,CAAK,eAAA,EAAiB;aAAA,EACrE,CAAC,cAAc,WAAW,iBAAiB;YACzC,OAAQ,IAAA,CAAK,KAAA,GACX,aAAa,MAAA,GAAS,KAAK,YAAY,IACnC,eAAe;gBACb;gBACA;gBACA;YACD,CAAA,IACD;QACR,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAI9B,IAAA,CAAQ,UAAA,kNAAa,OAAA,EACnB,MAAM;YACJ,IAAI,aAA4B;YAChC,IAAI,WAA0B;YACxB,MAAA,QAAQ,IAAA,CAAK,cAAA,CAAe;YAClC,IAAI,OAAO;gBACT,aAAa,MAAM,UAAA;gBACnB,WAAW,MAAM,QAAA;YAAA;YAEZ,OAAA;gBACL,IAAA,CAAK,OAAA,CAAQ,cAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,QAAA;gBACb,IAAA,CAAK,OAAA,CAAQ,KAAA;gBACb;gBACA;aACF;QACF,GACA,CAAC,gBAAgB,UAAU,OAAO,YAAY,aAAa;YACzD,OAAO,eAAe,QAAQ,aAAa,OACvC,CAAA,CAAA,GACA,eAAe;gBACb;gBACA;gBACA;gBACA;YAAA,CACD;QACP,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAI9B,IAAA,CAAA,gBAAA,GAAmB,CAAC,SAAuB;YACnC,MAAA,gBAAgB,IAAA,CAAK,OAAA,CAAQ,cAAA;YAC7B,MAAA,WAAW,KAAK,YAAA,CAAa,aAAa;YAEhD,IAAI,CAAC,UAAU;gBACL,QAAA,IAAA,CACN,CAAA,wBAAA,EAA2B,aAAa,CAAA,8BAAA,CAAA;gBAEnC,OAAA,CAAA;YAAA;YAGF,OAAA,SAAS,UAAU,EAAE;QAC9B;QAEQ,IAAA,CAAA,eAAA,GAAkB,CACxB,MACA,UACG;YACG,MAAA,QAAQ,IAAA,CAAK,gBAAA,CAAiB,IAAI;YAClC,MAAA,OAAO,IAAA,CAAK,iBAAA,CAAkB,KAAK,CAAA;YACzC,IAAI,CAAC,MAAM;gBACT;YAAA;YAEF,MAAM,MAAM,KAAK,GAAA;YACjB,MAAM,WAAW,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,GAAG;YAE3C,IAAI,aAAa,MAAM;gBACrB,IAAI,UAAU;oBACP,IAAA,CAAA,QAAA,CAAS,SAAA,CAAU,QAAQ;gBAAA;gBAE7B,IAAA,CAAA,QAAA,CAAS,OAAA,CAAQ,IAAI;gBACrB,IAAA,CAAA,aAAA,CAAc,GAAA,CAAI,KAAK,IAAI;YAAA;YAGlC,IAAI,KAAK,WAAA,EAAa;gBACf,IAAA,CAAA,UAAA,CAAW,OAAO,IAAA,CAAK,OAAA,CAAQ,cAAA,CAAe,MAAM,OAAO,IAAI,CAAC;YAAA;QAEzE;QAEa,IAAA,CAAA,UAAA,GAAA,CAAC,OAAe,SAAiB;YACtC,MAAA,OAAO,IAAA,CAAK,iBAAA,CAAkB,KAAK,CAAA;YACzC,IAAI,CAAC,MAAM;gBACT;YAAA;YAEF,MAAM,WAAW,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,KAAK,GAAG,KAAK,KAAK,IAAA;YAC1D,MAAM,QAAQ,OAAO;YAErB,IAAI,UAAU,GAAG;gBACf,IACE,IAAA,CAAK,0CAAA,KAA+C,KAAA,IAChD,IAAA,CAAK,0CAAA,CAA2C,MAAM,OAAO,IAAI,IACjE,KAAK,KAAA,GAAQ,IAAA,CAAK,eAAA,CAAgB,IAAI,IAAA,CAAK,iBAAA,EAC/C;oBACA,IAAI,QAAQ,IAAI,wCAAa,gBAAgB,IAAA,CAAK,OAAA,CAAQ,KAAA,EAAO;wBACvD,QAAA,IAAA,CAAK,cAAc,KAAK;oBAAA;oBAG7B,IAAA,CAAA,eAAA,CAAgB,IAAA,CAAK,eAAA,IAAmB;wBAC3C,aAAc,IAAA,CAAK,iBAAA,IAAqB;wBACxC,UAAU,KAAA;oBAAA,CACX;gBAAA;gBAGE,IAAA,CAAA,2BAAA,CAA4B,IAAA,CAAK,KAAK,KAAK;gBAC3C,IAAA,CAAA,aAAA,GAAgB,IAAI,IAAI,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,KAAK,GAAA,EAAK,IAAI,CAAC;gBAEnE,IAAA,CAAK,MAAA,CAAO,KAAK;YAAA;QAErB;QAEA,IAAA,CAAA,cAAA,GAAiB,CAAC,SAA0C;YAC1D,IAAI,CAAC,MAAM;gBACT,IAAA,CAAK,aAAA,CAAc,OAAA,CAAQ,CAAC,QAAQ,QAAQ;oBACtC,IAAA,CAAC,OAAO,WAAA,EAAa;wBAClB,IAAA,CAAA,QAAA,CAAS,SAAA,CAAU,MAAM;wBACzB,IAAA,CAAA,aAAA,CAAc,MAAA,CAAO,GAAG;oBAAA;gBAC/B,CACD;gBACD;YAAA;YAGG,IAAA,CAAA,eAAA,CAAgB,MAAM,KAAA,CAAS;QACtC;QAEkB,IAAA,CAAA,eAAA,kNAAA,OAAA,EAChB,IAAM;gBAAC,IAAA,CAAK,UAAA,CAAc;gBAAA,IAAA,CAAK,eAAA,EAAiB;aAAA,EAChD,CAAC,SAAS,iBAAiB;YACzB,MAAM,eAAmC,CAAC,CAAA;YAE1C,IAAA,IAAS,IAAI,GAAG,MAAM,QAAQ,MAAA,EAAQ,IAAI,KAAK,IAAK;gBAC5C,MAAA,IAAI,OAAA,CAAQ,CAAC,CAAA;gBACb,MAAA,cAAc,YAAA,CAAa,CAAC,CAAA;gBAElC,aAAa,IAAA,CAAK,WAAW;YAAA;YAGxB,OAAA;QACT,GACA;YACE,KAAK,QAAQ,IAAI,wCAAa,gBAAgB;YAC9C,OAAO,IAAM,IAAA,CAAK,OAAA,CAAQ,KAAA;QAAA;QAI9B,IAAA,CAAA,uBAAA,GAA0B,CAAC,WAAmB;YACtC,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YACtC,IAAA,aAAa,MAAA,KAAW,GAAG;gBACtB,OAAA,KAAA;YAAA;YAEF,OAAA,8NAAA,EACL,YAAA,CACE,wBACE,GACA,aAAa,MAAA,GAAS,GACtB,CAAC,QAAkB,8NAAA,EAAa,YAAA,CAAa,KAAK,CAAC,EAAE,KAAA,EACrD,QAEJ;QAEJ;QAEwB,IAAA,CAAA,qBAAA,GAAA,CAAC,UAAkB,UAA2B;YAC9D,MAAA,OAAO,IAAA,CAAK,OAAA,CAAQ;YACpB,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YAE1C,IAAI,UAAU,QAAQ;gBAChB,IAAA,YAAY,eAAe,MAAM;oBAC3B,QAAA;gBAAA;YACV;YAGF,IAAI,UAAU,OAAO;gBACP,YAAA;YAAA;YAGd,MAAM,iBAAiB,IAAA,CAAK,OAAA,CAAQ,UAAA,GAChC,gBACA;YACJ,MAAM,aAAa,IAAA,CAAK,aAAA,GACpB,cAAc,IAAA,CAAK,aAAA,GACjB,IAAA,CAAK,aAAA,CAAc,QAAA,CAAS,eAAA,CAAgB,cAAc,CAAA,GAC1D,IAAA,CAAK,aAAA,CAAc,cAAc,CAAA,GACnC;YAEJ,MAAM,YAAY,aAAa;YAE/B,OAAO,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,WAAW,QAAQ,GAAG,CAAC;QAClD;QAEoB,IAAA,CAAA,iBAAA,GAAA,CAAC,OAAe,QAAyB,MAAA,KAAW;YAC9D,QAAA,KAAK,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA,GAAQ,CAAC,CAAC;YAErD,MAAA,OAAO,IAAA,CAAK,iBAAA,CAAkB,KAAK,CAAA;YACzC,IAAI,CAAC,MAAM;gBACF,OAAA,KAAA;YAAA;YAGH,MAAA,OAAO,IAAA,CAAK,OAAA,CAAQ;YACpB,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YAE1C,IAAI,UAAU,QAAQ;gBACpB,IAAI,KAAK,GAAA,IAAO,eAAe,OAAO,IAAA,CAAK,OAAA,CAAQ,gBAAA,EAAkB;oBAC3D,QAAA;gBAAA,OAAA,IACC,KAAK,KAAA,IAAS,eAAe,IAAA,CAAK,OAAA,CAAQ,kBAAA,EAAoB;oBAC/D,QAAA;gBAAA,OACH;oBACE,OAAA;wBAAC;wBAAc,KAAK;qBAAA;gBAAA;YAC7B;YAGI,MAAA,eACJ,KAAK,KAAA,GAAQ,IAAA,CAAK,OAAA,CAAQ,kBAAA,GAAA,CAAsB,KAAK,IAAA,GAAO,IAAA,IAAQ;YAEtE,OAAQ,OAAO;gBACb,KAAK;oBACH,OAAO;wBAAC,IAAA,CAAK,qBAAA,CAAsB,cAAc,KAAK;wBAAG,KAAK;qBAAA;gBAChE,KAAK;oBACI,OAAA;wBACL,IAAA,CAAK,qBAAA,CACH,KAAK,GAAA,GAAM,IAAA,CAAK,OAAA,CAAQ,gBAAA,EACxB;wBAEF;qBACF;gBACF;oBACS,OAAA;wBACL,IAAA,CAAK,qBAAA,CACH,KAAK,KAAA,GAAQ,IAAA,CAAK,OAAA,CAAQ,kBAAA,EAC1B;wBAEF;qBACF;YAAA;QAEN;QAEA,IAAA,CAAQ,aAAA,GAAgB,IAAM,IAAA,CAAK,aAAA,CAAc,IAAA,GAAO;QAExD,IAAA,CAAQ,mBAAA,GAAsB,MAAM;YAClC,IAAI,IAAA,CAAK,sBAAA,KAA2B,QAAQ,IAAA,CAAK,YAAA,EAAc;gBACxD,IAAA,CAAA,YAAA,CAAa,YAAA,CAAa,IAAA,CAAK,sBAAsB;gBAC1D,IAAA,CAAK,sBAAA,GAAyB;YAAA;QAElC;QAEiB,IAAA,CAAA,cAAA,GAAA,CACf,UACA,EAAE,QAAQ,OAAA,EAAS,QAAA,CAAS,CAAA,GAA2B,CAAA,CAAA,KACpD;YACH,IAAA,CAAK,mBAAA,CAAoB;YAEzB,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAA,GAAiB;gBACzC,QAAA,IAAA,CACN;YACF;YAGF,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,qBAAA,CAAsB,UAAU,KAAK,GAAG;gBAChE,aAAa,KAAA;gBACb;YAAA,CACD;QACH;QAEgB,IAAA,CAAA,aAAA,GAAA,CACd,OACA,EAAE,OAAO,eAAe,MAAA,EAAQ,QAAA,CAAmC,CAAA,GAAA,CAAA,CAAA,KAChE;YACK,QAAA,KAAK,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA,GAAQ,CAAC,CAAC;YAE3D,IAAA,CAAK,mBAAA,CAAoB;YAEzB,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAA,GAAiB;gBACzC,QAAA,IAAA,CACN;YACF;YAGF,MAAM,iBAAiB,IAAA,CAAK,iBAAA,CAAkB,OAAO,YAAY;YACjE,IAAI,CAAC,eAAgB,CAAA;YAEf,MAAA,CAAC,QAAQ,KAAK,CAAA,GAAI;YAExB,IAAA,CAAK,eAAA,CAAgB,QAAQ;gBAAE,aAAa,KAAA;gBAAW;YAAA,CAAU;YAEjE,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAc,KAAK,IAAA,CAAK,YAAA,EAAc;gBACtE,IAAA,CAAK,sBAAA,GAAyB,IAAA,CAAK,YAAA,CAAa,UAAA,CAAW,MAAM;oBAC/D,IAAA,CAAK,sBAAA,GAAyB;oBAExB,MAAA,eAAe,IAAA,CAAK,aAAA,CAAc,GAAA,CACtC,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,KAAK;oBAG/B,IAAI,cAAc;wBACV,MAAA,CAAC,YAAY,CAAA,kNAAI,eAAA,EACrB,IAAA,CAAK,iBAAA,CAAkB,OAAO,KAAK;wBAGrC,IAAI,gNAAC,cAAA,EAAY,cAAc,IAAA,CAAK,eAAA,CAAiB,CAAA,GAAG;4BACtD,IAAA,CAAK,aAAA,CAAc,OAAO;gCAAE;gCAAO;4BAAA,CAAU;wBAAA;oBAC/C,OACK;wBACL,IAAA,CAAK,aAAA,CAAc,OAAO;4BAAE;4BAAO;wBAAA,CAAU;oBAAA;gBAC/C,CACD;YAAA;QAEL;QAEA,IAAA,CAAA,QAAA,GAAW,CAAC,OAAe,EAAE,QAAA,CAAS,CAAA,GAA2B,CAAA,CAAA,KAAO;YACtE,IAAA,CAAK,mBAAA,CAAoB;YAEzB,IAAI,aAAa,YAAY,IAAA,CAAK,aAAA,CAAA,GAAiB;gBACzC,QAAA,IAAA,CACN;YACF;YAGF,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,eAAA,CAAgB,IAAI,OAAO;gBACnD,aAAa,KAAA;gBACb;YAAA,CACD;QACH;QAEA,IAAA,CAAA,YAAA,GAAe,MAAM;;YACb,MAAA,eAAe,IAAA,CAAK,eAAA,CAAgB;YAEtC,IAAA;YAEA,IAAA,aAAa,MAAA,KAAW,GAAG;gBAC7B,MAAM,IAAA,CAAK,OAAA,CAAQ,YAAA;YAAA,OACd;gBAGH,MAAA,IAAA,CAAK,OAAA,CAAQ,KAAA,KAAU,IAAA,CAAA,CAClB,KAAA,YAAA,CAAa,aAAa,MAAA,GAAS,CAAC,CAAA,KAApC,OAAA,KAAA,IAAA,GAAuC,GAAA,KAAO,IAC/C,KAAK,GAAA,IACA,aAAa,KAAA,CAAM,CAAC,IAAA,CAAK,OAAA,CAAQ,KAAK,EAAE,GAAA,CAAI,CAAC,IAAM,EAAE,GAAG;YAC7D;YAGR,OAAO,KAAK,GAAA,CACV,MAAM,IAAA,CAAK,OAAA,CAAQ,YAAA,GAAe,IAAA,CAAK,OAAA,CAAQ,UAAA,EAC/C;QAEJ;QAEQ,IAAA,CAAA,eAAA,GAAkB,CACxB,QACA,EACE,WAAA,EACA,QAAA,EAAA,KAKC;YACH,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,QAAQ;gBAAE;gBAAU;YAAA,GAAe,IAAI;QACjE;QAEA,IAAA,CAAA,OAAA,GAAU,MAAM;YACT,IAAA,CAAA,aAAA,GAAA,aAAA,GAAA,IAAoB,IAAI;YAC7B,IAAA,CAAK,MAAA,CAAO,KAAK;QACnB;QAvpBE,IAAA,CAAK,UAAA,CAAW,IAAI;IAAA;AAwpBxB;AAEA,MAAM,0BAA0B,CAC9B,KACA,MACA,iBACA,UACG;IACH,MAAO,OAAO,KAAM;QACZ,MAAA,SAAA,CAAW,MAAM,IAAA,IAAQ,IAAK;QAC9B,MAAA,eAAe,gBAAgB,MAAM;QAE3C,IAAI,eAAe,OAAO;YACxB,MAAM,SAAS;QAAA,OAAA,IACN,eAAe,OAAO;YAC/B,OAAO,SAAS;QAAA,OACX;YACE,OAAA;QAAA;IACT;IAGF,IAAI,MAAM,GAAG;QACX,OAAO,MAAM;IAAA,OACR;QACE,OAAA;IAAA;AAEX;AAEA,SAAS,eAAe,EACtB,YAAA,EACA,SAAA,EACA,YAAA,EACF,EAIG;IACK,MAAA,QAAQ,aAAa,MAAA,GAAS;IACpC,MAAM,YAAY,CAAC,QAAkB,YAAA,CAAa,KAAK,CAAA,CAAG,KAAA;IAE1D,MAAM,aAAa,wBAAwB,GAAG,OAAO,WAAW,YAAY;IAC5E,IAAI,WAAW;IAEf,MACE,WAAW,SACX,YAAA,CAAa,QAAQ,CAAA,CAAG,GAAA,GAAM,eAAe,UAC7C;QACA;IAAA;IAGK,OAAA;QAAE;QAAY;IAAS;AAChC", "debugId": null}}, {"offset": {"line": 6278, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@heroui/listbox/node_modules/@tanstack/react-virtual/dist/esm/index.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/listbox/node_modules/%40tanstack/react-virtual/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\n\nexport * from '@tanstack/virtual-core'\n\nconst useIsomorphicLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: VirtualizerOptions<TScrollElement, TItemElement>,\n): Virtualizer<TScrollElement, TItemElement> {\n  const rerender = React.useReducer(() => ({}), {})[1]\n\n  const resolvedOptions: VirtualizerOptions<TScrollElement, TItemElement> = {\n    ...options,\n    onChange: (instance, sync) => {\n      if (sync) {\n        flushSync(rerender)\n      } else {\n        rerender()\n      }\n      options.onChange?.(instance, sync)\n    },\n  }\n\n  const [instance] = React.useState(\n    () => new Virtualizer<TScrollElement, TItemElement>(resolvedOptions),\n  )\n\n  instance.setOptions(resolvedOptions)\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount()\n  }, [])\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate()\n  })\n\n  return instance\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: PartialKeys<\n    VirtualizerOptions<TScrollElement, TItemElement>,\n    'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n  >,\n): Virtualizer<TScrollElement, TItemElement> {\n  return useVirtualizerBase<TScrollElement, TItemElement>({\n    observeElementRect: observeElementRect,\n    observeElementOffset: observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options,\n  })\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: PartialKeys<\n    VirtualizerOptions<Window, TItemElement>,\n    | 'getScrollElement'\n    | 'observeElementRect'\n    | 'observeElementOffset'\n    | 'scrollToFn'\n  >,\n): Virtualizer<Window, TItemElement> {\n  return useVirtualizerBase<Window, TItemElement>({\n    getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => (typeof document !== 'undefined' ? window.scrollY : 0),\n    ...options,\n  })\n}\n"], "names": ["instance"], "mappings": ";;;;;;;;;;;AAeA,MAAM,4BACJ,OAAO,aAAa,oNAAc,MAAM,YAAA,yMAAkB,MAAM,MAAA;AAElE,SAAS,mBAIP,OAAA,EAC2C;IACrC,MAAA,iNAAW,MAAM,OAAA,CAAW,IAAA,CAAO,CAAA,CAAA,GAAK,CAAA,CAAE,CAAA,CAAE,CAAC,CAAA;IAEnD,MAAM,kBAAoE;QACxE,GAAG,OAAA;QACH,UAAU,CAACA,WAAU,SAAS;;YAC5B,IAAI,MAAM;gBACR,CAAA,GAAA,4MAAA,CAAA,YAAA,EAAU,QAAQ;YAAA,OACb;gBACI,SAAA;YAAA;YAEH,CAAA,KAAA,QAAA,QAAA,KAAA,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,SAAWA,WAAU;QAAI;IAErC;IAEM,MAAA,CAAC,QAAQ,CAAA,yMAAI,MAAM,KAAA,CACvB,IAAM,iOAAI,cAAA,CAA0C,eAAe;IAGrE,SAAS,UAAA,CAAW,eAAe;IAEnC,0BAA0B,MAAM;QAC9B,OAAO,SAAS,SAAA,CAAU;IAC5B,GAAG,EAAE;IAEL,0BAA0B,MAAM;QAC9B,OAAO,SAAS,WAAA,CAAY;IAAA,CAC7B;IAEM,OAAA;AACT;AAEO,SAAS,eAId,OAAA,EAI2C;IAC3C,OAAO,mBAAiD;4BACtD,kPAAA;2PACA,uBAAA;QACA,yOAAY,gBAAA;QACZ,GAAG,OAAA;IAAA,CACJ;AACH;AAEO,SAAS,qBACd,OAAA,EAOmC;IACnC,OAAO,mBAAyC;QAC9C,kBAAkB,IAAO,OAAO,aAAa,cAAc,SAAS;QACpE,iPAAoB,oBAAA;QACpB,sBAAsB,mPAAA;QACtB,yOAAY,eAAA;QACZ,eAAe,IAAO,OAAO,aAAa,cAAc,OAAO,OAAA,GAAU;QACzE,GAAG,OAAA;IAAA,CACJ;AACH", "debugId": null}}, {"offset": {"line": 6338, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@heroui/table/node_modules/@tanstack/react-virtual/dist/esm/index.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/node_modules/%40tanstack/react-virtual/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\n\nexport * from '@tanstack/virtual-core'\n\nconst useIsomorphicLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: VirtualizerOptions<TScrollElement, TItemElement>,\n): Virtualizer<TScrollElement, TItemElement> {\n  const rerender = React.useReducer(() => ({}), {})[1]\n\n  const resolvedOptions: VirtualizerOptions<TScrollElement, TItemElement> = {\n    ...options,\n    onChange: (instance, sync) => {\n      if (sync) {\n        flushSync(rerender)\n      } else {\n        rerender()\n      }\n      options.onChange?.(instance, sync)\n    },\n  }\n\n  const [instance] = React.useState(\n    () => new Virtualizer<TScrollElement, TItemElement>(resolvedOptions),\n  )\n\n  instance.setOptions(resolvedOptions)\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount()\n  }, [])\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate()\n  })\n\n  return instance\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: PartialKeys<\n    VirtualizerOptions<TScrollElement, TItemElement>,\n    'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n  >,\n): Virtualizer<TScrollElement, TItemElement> {\n  return useVirtualizerBase<TScrollElement, TItemElement>({\n    observeElementRect: observeElementRect,\n    observeElementOffset: observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options,\n  })\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: PartialKeys<\n    VirtualizerOptions<Window, TItemElement>,\n    | 'getScrollElement'\n    | 'observeElementRect'\n    | 'observeElementOffset'\n    | 'scrollToFn'\n  >,\n): Virtualizer<Window, TItemElement> {\n  return useVirtualizerBase<Window, TItemElement>({\n    getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => (typeof document !== 'undefined' ? window.scrollY : 0),\n    ...options,\n  })\n}\n"], "names": ["instance"], "mappings": ";;;;;;;;;;;AAeA,MAAM,4BACJ,OAAO,aAAa,oNAAc,MAAM,YAAA,yMAAkB,MAAM,MAAA;AAElE,SAAS,mBAIP,OAAA,EAC2C;IACrC,MAAA,iNAAW,MAAM,OAAA,CAAW,IAAA,CAAO,CAAA,CAAA,GAAK,CAAA,CAAE,CAAA,CAAE,CAAC,CAAA;IAEnD,MAAM,kBAAoE;QACxE,GAAG,OAAA;QACH,UAAU,CAACA,WAAU,SAAS;;YAC5B,IAAI,MAAM;gBACR,CAAA,GAAA,4MAAA,CAAA,YAAA,EAAU,QAAQ;YAAA,OACb;gBACI,SAAA;YAAA;YAEH,CAAA,KAAA,QAAA,QAAA,KAAA,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,SAAWA,WAAU;QAAI;IAErC;IAEM,MAAA,CAAC,QAAQ,CAAA,yMAAI,MAAM,KAAA,CACvB,IAAM,+NAAI,cAAA,CAA0C,eAAe;IAGrE,SAAS,UAAA,CAAW,eAAe;IAEnC,0BAA0B,MAAM;QAC9B,OAAO,SAAS,SAAA,CAAU;IAC5B,GAAG,EAAE;IAEL,0BAA0B,MAAM;QAC9B,OAAO,SAAS,WAAA,CAAY;IAAA,CAC7B;IAEM,OAAA;AACT;AAEO,SAAS,eAId,OAAA,EAI2C;IAC3C,OAAO,mBAAiD;4BACtD,gPAAA;yPACA,uBAAA;QACA,uOAAY,gBAAA;QACZ,GAAG,OAAA;IAAA,CACJ;AACH;AAEO,SAAS,qBACd,OAAA,EAOmC;IACnC,OAAO,mBAAyC;QAC9C,kBAAkB,IAAO,OAAO,aAAa,cAAc,SAAS;QACpE,+OAAoB,oBAAA;QACpB,sBAAsB,iPAAA;QACtB,uOAAY,eAAA;QACZ,eAAe,IAAO,OAAO,aAAa,cAAc,OAAO,OAAA,GAAU;QACzE,GAAG,OAAA;IAAA,CACJ;AACH", "debugId": null}}, {"offset": {"line": 6398, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/popover/dist/chunk-VAMG3REK.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  usePopover\n} from \"./chunk-YGSGKG3C.mjs\";\n\n// src/free-solo-popover.tsx\nimport * as React from \"react\";\nimport { DismissButton, Overlay } from \"@react-aria/overlays\";\nimport { forwardRef } from \"@heroui/system\";\nimport { LazyMotion, m } from \"framer-motion\";\nimport { mergeProps } from \"@heroui/shared-utils\";\nimport { getTransformOrigins } from \"@heroui/aria-utils\";\nimport { TRANSITION_VARIANTS } from \"@heroui/framer-utils\";\nimport { useDialog } from \"@react-aria/dialog\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar domAnimation = () => import(\"@heroui/dom-animation\").then((res) => res.default);\nvar FreeSoloPopoverWrapper = forwardRef(\n  ({\n    children,\n    motionProps,\n    placement,\n    disableAnimation,\n    style: styleProp = {},\n    transformOrigin = {},\n    ...otherProps\n  }, ref) => {\n    let style = styleProp;\n    if (transformOrigin.originX !== void 0 || transformOrigin.originY !== void 0) {\n      style = {\n        ...style,\n        // @ts-ignore\n        transformOrigin\n      };\n    } else if (placement) {\n      style = {\n        ...style,\n        ...getTransformOrigins(placement === \"center\" ? \"top\" : placement)\n      };\n    }\n    return disableAnimation ? /* @__PURE__ */ jsx(\"div\", { ...otherProps, ref, children }) : /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(\n      m.div,\n      {\n        ref,\n        animate: \"enter\",\n        exit: \"exit\",\n        initial: \"initial\",\n        style,\n        variants: TRANSITION_VARIANTS.scaleSpringOpacity,\n        ...mergeProps(otherProps, motionProps),\n        children\n      }\n    ) });\n  }\n);\nFreeSoloPopoverWrapper.displayName = \"HeroUI.FreeSoloPopoverWrapper\";\nvar FreeSoloPopover = forwardRef(\n  ({ children, transformOrigin, disableDialogFocus = false, ...props }, ref) => {\n    const {\n      Component,\n      state,\n      placement,\n      backdrop,\n      portalContainer,\n      disableAnimation,\n      motionProps,\n      isNonModal,\n      getPopoverProps,\n      getBackdropProps,\n      getDialogProps,\n      getContentProps\n    } = usePopover({\n      ...props,\n      ref\n    });\n    const dialogRef = React.useRef(null);\n    const { dialogProps: ariaDialogProps, titleProps } = useDialog({}, dialogRef);\n    const dialogProps = getDialogProps({\n      // by default, focus is moved into the dialog on mount\n      // we can use `disableDialogFocus` to disable this behaviour\n      // e.g. in autocomplete, the focus should be moved to the input (handled in autocomplete hook) instead of the dialog first\n      ...!disableDialogFocus && { ref: dialogRef },\n      ...ariaDialogProps\n    });\n    const backdropContent = React.useMemo(() => {\n      if (backdrop === \"transparent\") {\n        return null;\n      }\n      if (disableAnimation) {\n        return /* @__PURE__ */ jsx(\"div\", { ...getBackdropProps() });\n      }\n      return /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(\n        m.div,\n        {\n          animate: \"enter\",\n          exit: \"exit\",\n          initial: \"exit\",\n          variants: TRANSITION_VARIANTS.fade,\n          ...getBackdropProps()\n        }\n      ) });\n    }, [backdrop, disableAnimation, getBackdropProps]);\n    return /* @__PURE__ */ jsxs(Overlay, { portalContainer, children: [\n      !isNonModal && backdropContent,\n      /* @__PURE__ */ jsx(Component, { ...getPopoverProps(), children: /* @__PURE__ */ jsxs(\n        FreeSoloPopoverWrapper,\n        {\n          disableAnimation,\n          motionProps,\n          placement,\n          tabIndex: -1,\n          transformOrigin,\n          ...dialogProps,\n          children: [\n            !isNonModal && /* @__PURE__ */ jsx(DismissButton, { onDismiss: state.close }),\n            /* @__PURE__ */ jsx(\"div\", { ...getContentProps(), children: typeof children === \"function\" ? children(titleProps) : children }),\n            /* @__PURE__ */ jsx(DismissButton, { onDismiss: state.close })\n          ]\n        }\n      ) })\n    ] });\n  }\n);\nFreeSoloPopover.displayName = \"HeroUI.FreeSoloPopover\";\nvar free_solo_popover_default = FreeSoloPopover;\n\nexport {\n  free_solo_popover_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,4BAA4B;AAC5B;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;AAeA,IAAI,eAAe,IAAM,qJAAgC,IAAI,CAAC,CAAC,MAAQ,IAAI,OAAO;AAClF,IAAI,yBAAyB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EACpC,CAAC,EACC,QAAQ,EACR,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,OAAO,YAAY,CAAC,CAAC,EACrB,kBAAkB,CAAC,CAAC,EACpB,GAAG,YACJ,EAAE;IACD,IAAI,QAAQ;IACZ,IAAI,gBAAgB,OAAO,KAAK,KAAK,KAAK,gBAAgB,OAAO,KAAK,KAAK,GAAG;QAC5E,QAAQ;YACN,GAAG,KAAK;YACR,aAAa;YACb;QACF;IACF,OAAO,IAAI,WAAW;QACpB,QAAQ;YACN,GAAG,KAAK;YACR,GAAG,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc,WAAW,QAAQ,UAAU;QACpE;IACF;IACA,OAAO,mBAAmB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,GAAG,UAAU;QAAE;QAAK;IAAS,KAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,oLAAA,CAAA,aAAU,EAAE;QAAE,UAAU;QAAc,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC7K,qLAAA,CAAA,IAAC,CAAC,GAAG,EACL;YACE;YACA,SAAS;YACT,MAAM;YACN,SAAS;YACT;YACA,UAAU,yKAAA,CAAA,sBAAmB,CAAC,kBAAkB;YAChD,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,YAAY;YACtC;QACF;IACA;AACJ;AAEF,uBAAuB,WAAW,GAAG;AACrC,IAAI,kBAAkB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAC7B,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,qBAAqB,KAAK,EAAE,GAAG,OAAO,EAAE;IACpE,MAAM,EACJ,SAAS,EACT,KAAK,EACL,SAAS,EACT,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,eAAe,EAChB,GAAG,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE;QACb,GAAG,KAAK;QACR;IACF;IACA,MAAM,YAAY,qMAAA,CAAA,SAAY,CAAC;IAC/B,MAAM,EAAE,aAAa,eAAe,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE,CAAC,GAAG;IACnE,MAAM,cAAc,eAAe;QACjC,sDAAsD;QACtD,4DAA4D;QAC5D,0HAA0H;QAC1H,GAAG,CAAC,sBAAsB;YAAE,KAAK;QAAU,CAAC;QAC5C,GAAG,eAAe;IACpB;IACA,MAAM,kBAAkB,qMAAA,CAAA,UAAa,CAAC;QACpC,IAAI,aAAa,eAAe;YAC9B,OAAO;QACT;QACA,IAAI,kBAAkB;YACpB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,kBAAkB;YAAC;QAC5D;QACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,oLAAA,CAAA,aAAU,EAAE;YAAE,UAAU;YAAc,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC3F,qLAAA,CAAA,IAAC,CAAC,GAAG,EACL;gBACE,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,UAAU,yKAAA,CAAA,sBAAmB,CAAC,IAAI;gBAClC,GAAG,kBAAkB;YACvB;QACA;IACJ,GAAG;QAAC;QAAU;QAAkB;KAAiB;IACjD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,+JAAA,CAAA,UAAO,EAAE;QAAE;QAAiB,UAAU;YAChE,CAAC,cAAc;YACf,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW;gBAAE,GAAG,iBAAiB;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAClF,wBACA;oBACE;oBACA;oBACA;oBACA,UAAU,CAAC;oBACX;oBACA,GAAG,WAAW;oBACd,UAAU;wBACR,CAAC,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,qKAAA,CAAA,gBAAa,EAAE;4BAAE,WAAW,MAAM,KAAK;wBAAC;wBAC3E,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;4BAAE,GAAG,iBAAiB;4BAAE,UAAU,OAAO,aAAa,aAAa,SAAS,cAAc;wBAAS;wBAC9H,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,qKAAA,CAAA,gBAAa,EAAE;4BAAE,WAAW,MAAM,KAAK;wBAAC;qBAC7D;gBACH;YACA;SACH;IAAC;AACJ;AAEF,gBAAgB,WAAW,GAAG;AAC9B,IAAI,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6544, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-data-scroll-overflow/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { capitalize } from \"@heroui/shared-utils\";\nimport { useEffect, useRef } from \"react\";\nfunction useDataScrollOverflow(props = {}) {\n  const {\n    domRef,\n    isEnabled = true,\n    overflowCheck = \"vertical\",\n    visibility = \"auto\",\n    offset = 0,\n    onVisibilityChange,\n    updateDeps = []\n  } = props;\n  const visibleRef = useRef(visibility);\n  useEffect(() => {\n    const el = domRef == null ? void 0 : domRef.current;\n    if (!el || !isEnabled) return;\n    const setAttributes = (direction, hasBefore, hasAfter, prefix, suffix) => {\n      if (visibility === \"auto\") {\n        const both = `${prefix}${capitalize(suffix)}Scroll`;\n        if (hasBefore && hasAfter) {\n          el.dataset[both] = \"true\";\n          el.removeAttribute(`data-${prefix}-scroll`);\n          el.removeAttribute(`data-${suffix}-scroll`);\n        } else {\n          el.dataset[`${prefix}Scroll`] = hasBefore.toString();\n          el.dataset[`${suffix}Scroll`] = hasAfter.toString();\n          el.removeAttribute(`data-${prefix}-${suffix}-scroll`);\n        }\n      } else {\n        const next = hasBefore && hasAfter ? \"both\" : hasBefore ? prefix : hasAfter ? suffix : \"none\";\n        if (next !== visibleRef.current) {\n          onVisibilityChange == null ? void 0 : onVisibilityChange(next);\n          visibleRef.current = next;\n        }\n      }\n    };\n    const checkOverflow = () => {\n      var _a, _b;\n      const directions = [\n        { type: \"vertical\", prefix: \"top\", suffix: \"bottom\" },\n        { type: \"horizontal\", prefix: \"left\", suffix: \"right\" }\n      ];\n      const listbox = el.querySelector('ul[data-slot=\"list\"]');\n      const scrollHeight = +((_a = listbox == null ? void 0 : listbox.getAttribute(\"data-virtual-scroll-height\")) != null ? _a : el.scrollHeight);\n      const scrollTop = +((_b = listbox == null ? void 0 : listbox.getAttribute(\"data-virtual-scroll-top\")) != null ? _b : el.scrollTop);\n      for (const { type, prefix, suffix } of directions) {\n        if (overflowCheck === type || overflowCheck === \"both\") {\n          const hasBefore = type === \"vertical\" ? scrollTop > offset : el.scrollLeft > offset;\n          const hasAfter = type === \"vertical\" ? scrollTop + el.clientHeight + offset < scrollHeight : el.scrollLeft + el.clientWidth + offset < el.scrollWidth;\n          setAttributes(type, hasBefore, hasAfter, prefix, suffix);\n        }\n      }\n    };\n    const clearOverflow = () => {\n      [\"top\", \"bottom\", \"top-bottom\", \"left\", \"right\", \"left-right\"].forEach((attr) => {\n        el.removeAttribute(`data-${attr}-scroll`);\n      });\n    };\n    checkOverflow();\n    el.addEventListener(\"scroll\", checkOverflow, true);\n    if (visibility !== \"auto\") {\n      clearOverflow();\n      if (visibility === \"both\") {\n        el.dataset.topBottomScroll = String(overflowCheck === \"vertical\");\n        el.dataset.leftRightScroll = String(overflowCheck === \"horizontal\");\n      } else {\n        el.dataset.topBottomScroll = \"false\";\n        el.dataset.leftRightScroll = \"false\";\n        [\"top\", \"bottom\", \"left\", \"right\"].forEach((attr) => {\n          el.dataset[`${attr}Scroll`] = String(visibility === attr);\n        });\n      }\n    }\n    return () => {\n      el.removeEventListener(\"scroll\", checkOverflow, true);\n      clearOverflow();\n    };\n  }, [...updateDeps, isEnabled, visibility, overflowCheck, onVisibilityChange, domRef]);\n}\nexport {\n  useDataScrollOverflow\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf;AACA;;;AACA,SAAS,sBAAsB,QAAQ,CAAC,CAAC;IACvC,MAAM,EACJ,MAAM,EACN,YAAY,IAAI,EAChB,gBAAgB,UAAU,EAC1B,aAAa,MAAM,EACnB,SAAS,CAAC,EACV,kBAAkB,EAClB,aAAa,EAAE,EAChB,GAAG;IACJ,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO;QACnD,IAAI,CAAC,MAAM,CAAC,WAAW;QACvB,MAAM,gBAAgB,CAAC,WAAW,WAAW,UAAU,QAAQ;YAC7D,IAAI,eAAe,QAAQ;gBACzB,MAAM,OAAO,GAAG,SAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,MAAM,CAAC;gBACnD,IAAI,aAAa,UAAU;oBACzB,GAAG,OAAO,CAAC,KAAK,GAAG;oBACnB,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC;oBAC1C,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,OAAO,CAAC;gBAC5C,OAAO;oBACL,GAAG,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,QAAQ;oBAClD,GAAG,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS,QAAQ;oBACjD,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,OAAO,CAAC;gBACtD;YACF,OAAO;gBACL,MAAM,OAAO,aAAa,WAAW,SAAS,YAAY,SAAS,WAAW,SAAS;gBACvF,IAAI,SAAS,WAAW,OAAO,EAAE;oBAC/B,sBAAsB,OAAO,KAAK,IAAI,mBAAmB;oBACzD,WAAW,OAAO,GAAG;gBACvB;YACF;QACF;QACA,MAAM,gBAAgB;YACpB,IAAI,IAAI;YACR,MAAM,aAAa;gBACjB;oBAAE,MAAM;oBAAY,QAAQ;oBAAO,QAAQ;gBAAS;gBACpD;oBAAE,MAAM;oBAAc,QAAQ;oBAAQ,QAAQ;gBAAQ;aACvD;YACD,MAAM,UAAU,GAAG,aAAa,CAAC;YACjC,MAAM,eAAe,CAAC,CAAC,CAAC,KAAK,WAAW,OAAO,KAAK,IAAI,QAAQ,YAAY,CAAC,6BAA6B,KAAK,OAAO,KAAK,GAAG,YAAY;YAC1I,MAAM,YAAY,CAAC,CAAC,CAAC,KAAK,WAAW,OAAO,KAAK,IAAI,QAAQ,YAAY,CAAC,0BAA0B,KAAK,OAAO,KAAK,GAAG,SAAS;YACjI,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,WAAY;gBACjD,IAAI,kBAAkB,QAAQ,kBAAkB,QAAQ;oBACtD,MAAM,YAAY,SAAS,aAAa,YAAY,SAAS,GAAG,UAAU,GAAG;oBAC7E,MAAM,WAAW,SAAS,aAAa,YAAY,GAAG,YAAY,GAAG,SAAS,eAAe,GAAG,UAAU,GAAG,GAAG,WAAW,GAAG,SAAS,GAAG,WAAW;oBACrJ,cAAc,MAAM,WAAW,UAAU,QAAQ;gBACnD;YACF;QACF;QACA,MAAM,gBAAgB;YACpB;gBAAC;gBAAO;gBAAU;gBAAc;gBAAQ;gBAAS;aAAa,CAAC,OAAO,CAAC,CAAC;gBACtE,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC;YAC1C;QACF;QACA;QACA,GAAG,gBAAgB,CAAC,UAAU,eAAe;QAC7C,IAAI,eAAe,QAAQ;YACzB;YACA,IAAI,eAAe,QAAQ;gBACzB,GAAG,OAAO,CAAC,eAAe,GAAG,OAAO,kBAAkB;gBACtD,GAAG,OAAO,CAAC,eAAe,GAAG,OAAO,kBAAkB;YACxD,OAAO;gBACL,GAAG,OAAO,CAAC,eAAe,GAAG;gBAC7B,GAAG,OAAO,CAAC,eAAe,GAAG;gBAC7B;oBAAC;oBAAO;oBAAU;oBAAQ;iBAAQ,CAAC,OAAO,CAAC,CAAC;oBAC1C,GAAG,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,OAAO,eAAe;gBACtD;YACF;QACF;QACA,OAAO;YACL,GAAG,mBAAmB,CAAC,UAAU,eAAe;YAChD;QACF;IACF,GAAG;WAAI;QAAY;QAAW;QAAY;QAAe;QAAoB;KAAO;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6653, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/scroll-shadow/dist/chunk-XKHEX3UH.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-scroll-shadow.ts\nimport { mapPropsVariants } from \"@heroui/system\";\nimport { scrollShadow } from \"@heroui/theme\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { useDataScrollOverflow } from \"@heroui/use-data-scroll-overflow\";\nimport { useMemo } from \"react\";\nimport { objectToDeps } from \"@heroui/shared-utils\";\nfunction useScrollShadow(originalProps) {\n  var _a;\n  const [props, variantProps] = mapPropsVariants(originalProps, scrollShadow.variantKeys);\n  const {\n    ref,\n    as,\n    children,\n    className,\n    style,\n    size = 40,\n    offset = 0,\n    visibility = \"auto\",\n    isEnabled = true,\n    onVisibilityChange,\n    ...otherProps\n  } = props;\n  const Component = as || \"div\";\n  const domRef = useDOMRef(ref);\n  useDataScrollOverflow({\n    domRef,\n    offset,\n    visibility,\n    isEnabled,\n    onVisibilityChange,\n    updateDeps: [children],\n    overflowCheck: (_a = originalProps.orientation) != null ? _a : \"vertical\"\n  });\n  const styles = useMemo(\n    () => scrollShadow({\n      ...variantProps,\n      className\n    }),\n    [objectToDeps(variantProps), className]\n  );\n  const getBaseProps = (props2 = {}) => {\n    var _a2;\n    return {\n      ref: domRef,\n      className: styles,\n      \"data-orientation\": (_a2 = originalProps.orientation) != null ? _a2 : \"vertical\",\n      style: {\n        \"--scroll-shadow-size\": `${size}px`,\n        ...style,\n        ...props2.style\n      },\n      ...otherProps,\n      ...props2\n    };\n  };\n  return { Component, styles, domRef, children, getBaseProps };\n}\n\nexport {\n  useScrollShadow\n};\n"], "names": [], "mappings": ";;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;AASA,SAAS,gBAAgB,aAAa;IACpC,IAAI;IACJ,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,eAAY,CAAC,WAAW;IACtF,MAAM,EACJ,GAAG,EACH,EAAE,EACF,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EAAE,EACT,SAAS,CAAC,EACV,aAAa,MAAM,EACnB,YAAY,IAAI,EAChB,kBAAkB,EAClB,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,CAAA,GAAA,+KAAA,CAAA,wBAAqB,AAAD,EAAE;QACpB;QACA;QACA;QACA;QACA;QACA,YAAY;YAAC;SAAS;QACtB,eAAe,CAAC,KAAK,cAAc,WAAW,KAAK,OAAO,KAAK;IACjE;IACA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACnB,IAAM,CAAA,GAAA,+JAAA,CAAA,eAAY,AAAD,EAAE;YACjB,GAAG,YAAY;YACf;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;KAAU;IAEzC,MAAM,eAAe,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI;QACJ,OAAO;YACL,KAAK;YACL,WAAW;YACX,oBAAoB,CAAC,MAAM,cAAc,WAAW,KAAK,OAAO,MAAM;YACtE,OAAO;gBACL,wBAAwB,GAAG,KAAK,EAAE,CAAC;gBACnC,GAAG,KAAK;gBACR,GAAG,OAAO,KAAK;YACjB;YACA,GAAG,UAAU;YACb,GAAG,MAAM;QACX;IACF;IACA,OAAO;QAAE;QAAW;QAAQ;QAAQ;QAAU;IAAa;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6722, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/scroll-shadow/dist/chunk-4EXC76WE.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useScrollShadow\n} from \"./chunk-XKHEX3UH.mjs\";\n\n// src/scroll-shadow.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ScrollShadow = forwardRef((props, ref) => {\n  const { Component, children, getBaseProps } = useScrollShadow({ ...props, ref });\n  return /* @__PURE__ */ jsx(Component, { ...getBaseProps(), children });\n});\nScrollShadow.displayName = \"HeroUI.ScrollShadow\";\nvar scroll_shadow_default = ScrollShadow;\n\nexport {\n  scroll_shadow_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,wBAAwB;AACxB;AACA;AAPA;;;;AAQA,IAAI,eAAe,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACpC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0KAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;IAAI;IAC9E,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW;QAAE,GAAG,cAAc;QAAE;IAAS;AACtE;AACA,aAAa,WAAW,GAAG;AAC3B,IAAI,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6758, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/radio/dist/chunk-EJJT6KNY.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-radio-group.ts\nimport { radioGroup } from \"@heroui/theme\";\nimport { useCallback, useMemo } from \"react\";\nimport { useRadioGroupState } from \"@react-stately/radio\";\nimport { useRadioGroup as useReactAriaRadioGroup } from \"@react-aria/radio\";\nimport { useProviderContext } from \"@heroui/system\";\nimport { filterDOMProps, useDOMRef } from \"@heroui/react-utils\";\nimport { clsx, safeAriaLabel, mergeProps } from \"@heroui/shared-utils\";\nimport { FormContext, useSlottedContext } from \"@heroui/form\";\nfunction useRadioGroup(props) {\n  var _a, _b;\n  const globalContext = useProviderContext();\n  const { validationBehavior: formValidationBehavior } = useSlottedContext(FormContext) || {};\n  const {\n    as,\n    ref,\n    classNames,\n    children,\n    label,\n    value,\n    name,\n    isInvalid: isInvalidProp,\n    validationState,\n    validationBehavior = (_a = formValidationBehavior != null ? formValidationBehavior : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _a : \"native\",\n    size = \"md\",\n    color = \"primary\",\n    isDisabled = false,\n    disableAnimation = (_b = globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false,\n    orientation = \"vertical\",\n    isRequired = false,\n    isReadOnly,\n    errorMessage,\n    description,\n    className,\n    onChange,\n    onValueChange,\n    ...otherProps\n  } = props;\n  const Component = as || \"div\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const otherPropsWithOrientation = useMemo(() => {\n    return {\n      ...otherProps,\n      value,\n      name,\n      \"aria-label\": safeAriaLabel(otherProps[\"aria-label\"], label),\n      isRequired,\n      isReadOnly,\n      isInvalid: validationState === \"invalid\" || isInvalidProp,\n      orientation,\n      validationBehavior,\n      onChange: onValueChange\n    };\n  }, [\n    otherProps,\n    value,\n    name,\n    label,\n    isRequired,\n    isReadOnly,\n    isInvalidProp,\n    validationState,\n    validationBehavior,\n    orientation,\n    onValueChange\n  ]);\n  const groupState = useRadioGroupState(otherPropsWithOrientation);\n  const {\n    labelProps,\n    radioGroupProps: groupProps,\n    errorMessageProps,\n    descriptionProps,\n    isInvalid: isAriaInvalid,\n    validationErrors,\n    validationDetails\n  } = useReactAriaRadioGroup(otherPropsWithOrientation, groupState);\n  const isInvalid = otherPropsWithOrientation.isInvalid || isAriaInvalid || groupState.isInvalid;\n  const context = useMemo(\n    () => ({\n      size,\n      color,\n      groupState,\n      isRequired,\n      isInvalid,\n      isDisabled,\n      disableAnimation,\n      onChange\n    }),\n    [\n      size,\n      color,\n      isRequired,\n      isDisabled,\n      isInvalid,\n      onChange,\n      disableAnimation,\n      groupState.name,\n      groupState.isDisabled,\n      groupState.isReadOnly,\n      groupState.isRequired,\n      groupState.selectedValue,\n      groupState.lastFocusedValue\n    ]\n  );\n  const slots = useMemo(\n    () => radioGroup({ isRequired, isInvalid, disableAnimation }),\n    [isInvalid, isRequired, disableAnimation]\n  );\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const getGroupProps = useCallback(() => {\n    return {\n      ref: domRef,\n      className: slots.base({ class: baseStyles }),\n      ...mergeProps(\n        groupProps,\n        filterDOMProps(otherProps, {\n          enabled: shouldFilterDOMProps\n        })\n      )\n    };\n  }, [domRef, slots, baseStyles, groupProps, otherProps]);\n  const getLabelProps = useCallback(() => {\n    return {\n      className: slots.label({ class: classNames == null ? void 0 : classNames.label }),\n      ...labelProps\n    };\n  }, [slots, classNames == null ? void 0 : classNames.label, labelProps, classNames == null ? void 0 : classNames.label]);\n  const getWrapperProps = useCallback(() => {\n    return {\n      className: slots.wrapper({ class: classNames == null ? void 0 : classNames.wrapper }),\n      role: \"presentation\",\n      \"data-orientation\": orientation\n    };\n  }, [slots, classNames == null ? void 0 : classNames.wrapper, orientation, slots.wrapper]);\n  const getDescriptionProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ...descriptionProps,\n        className: slots.description({ class: clsx(classNames == null ? void 0 : classNames.description, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.description, descriptionProps, slots.description]\n  );\n  const getErrorMessageProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ...errorMessageProps,\n        className: slots.errorMessage({ class: clsx(classNames == null ? void 0 : classNames.errorMessage, props2 == null ? void 0 : props2.className) })\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.errorMessage, errorMessageProps]\n  );\n  return {\n    Component,\n    children,\n    label,\n    context,\n    description,\n    isInvalid,\n    errorMessage: typeof errorMessage === \"function\" ? errorMessage({ isInvalid, validationErrors, validationDetails }) : errorMessage || (validationErrors == null ? void 0 : validationErrors.join(\" \")),\n    getGroupProps,\n    getLabelProps,\n    getWrapperProps,\n    getDescriptionProps,\n    getErrorMessageProps\n  };\n}\n\nexport {\n  useRadioGroup\n};\n"], "names": [], "mappings": ";;;AAEA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAVA;;;;;;;;;AAWA,SAAS,cAAc,KAAK;IAC1B,IAAI,IAAI;IACR,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,EAAE,oBAAoB,sBAAsB,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,8JAAA,CAAA,cAAW,KAAK,CAAC;IAC1F,MAAM,EACJ,EAAE,EACF,GAAG,EACH,UAAU,EACV,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,WAAW,aAAa,EACxB,eAAe,EACf,qBAAqB,CAAC,KAAK,0BAA0B,OAAO,yBAAyB,iBAAiB,OAAO,KAAK,IAAI,cAAc,kBAAkB,KAAK,OAAO,KAAK,QAAQ,EAC/K,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,aAAa,KAAK,EAClB,mBAAmB,CAAC,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK,KAAK,EAC9G,cAAc,UAAU,EACxB,aAAa,KAAK,EAClB,UAAU,EACV,YAAY,EACZ,WAAW,EACX,SAAS,EACT,QAAQ,EACR,aAAa,EACb,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxC,OAAO;YACL,GAAG,UAAU;YACb;YACA;YACA,cAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,CAAC,aAAa,EAAE;YACtD;YACA;YACA,WAAW,oBAAoB,aAAa;YAC5C;YACA;YACA,UAAU;QACZ;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,aAAa,CAAA,GAAA,0KAAA,CAAA,qBAAkB,AAAD,EAAE;IACtC,MAAM,EACJ,UAAU,EACV,iBAAiB,UAAU,EAC3B,iBAAiB,EACjB,gBAAgB,EAChB,WAAW,aAAa,EACxB,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,CAAA,GAAA,kKAAA,CAAA,gBAAsB,AAAD,EAAE,2BAA2B;IACtD,MAAM,YAAY,0BAA0B,SAAS,IAAI,iBAAiB,WAAW,SAAS;IAC9F,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACpB,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,IAAI;QACf,WAAW,UAAU;QACrB,WAAW,UAAU;QACrB,WAAW,UAAU;QACrB,WAAW,aAAa;QACxB,WAAW,gBAAgB;KAC5B;IAEH,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,+JAAA,CAAA,aAAU,AAAD,EAAE;YAAE;YAAY;YAAW;QAAiB,IAC3D;QAAC;QAAW;QAAY;KAAiB;IAE3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,OAAO;YACL,KAAK;YACL,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO;YAAW;YAC1C,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;gBACzB,SAAS;YACX,GACD;QACH;IACF,GAAG;QAAC;QAAQ;QAAO;QAAY;QAAY;KAAW;IACtD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,OAAO;YACL,WAAW,MAAM,KAAK,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;YAAC;YAC/E,GAAG,UAAU;QACf;IACF,GAAG;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;QAAE;QAAY,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;KAAC;IACtH,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,OAAO;YACL,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;YAAC;YACnF,MAAM;YACN,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;QAAE;QAAa,MAAM,OAAO;KAAC;IACxF,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,GAAG,gBAAgB;YACnB,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QAC/I;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;QAAE;QAAkB,MAAM,WAAW;KAAC;IAEpG,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,GAAG,iBAAiB;YACpB,WAAW,MAAM,YAAY,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QACjJ;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;QAAE;KAAkB;IAEnF,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,cAAc,OAAO,iBAAiB,aAAa,aAAa;YAAE;YAAW;YAAkB;QAAkB,KAAK,gBAAgB,CAAC,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,IAAI,CAAC,IAAI;QACrM;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6947, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/radio/dist/chunk-A5ZCUROT.mjs"], "sourcesContent": ["\"use client\";\n\n// src/radio-group-context.ts\nimport { createContext } from \"@heroui/react-utils\";\nvar [RadioGroupProvider, useRadioGroupContext] = createContext({\n  name: \"RadioGroupContext\",\n  strict: false\n});\n\nexport {\n  RadioGroupProvider,\n  useRadioGroupContext\n};\n"], "names": [], "mappings": ";;;;AAEA,6BAA6B;AAC7B;AAHA;;AAIA,IAAI,CAAC,oBAAoB,qBAAqB,GAAG,CAAA,GAAA,wKAAA,CAAA,gBAAa,AAAD,EAAE;IAC7D,MAAM;IACN,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6964, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/radio/dist/chunk-ERF42GR7.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useRadioGroup\n} from \"./chunk-EJJT6KNY.mjs\";\nimport {\n  RadioGroupProvider\n} from \"./chunk-A5ZCUROT.mjs\";\n\n// src/radio-group.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar RadioGroup = forwardRef((props, ref) => {\n  const {\n    Component,\n    children,\n    label,\n    context,\n    description,\n    isInvalid,\n    errorMessage,\n    getGroupProps,\n    getLabelProps,\n    getWrapperProps,\n    getDescriptionProps,\n    getErrorMessageProps\n  } = useRadioGroup({ ...props, ref });\n  return /* @__PURE__ */ jsxs(Component, { ...getGroupProps(), children: [\n    label && /* @__PURE__ */ jsx(\"span\", { ...getLabelProps(), children: label }),\n    /* @__PURE__ */ jsx(\"div\", { ...getWrapperProps(), children: /* @__PURE__ */ jsx(RadioGroupProvider, { value: context, children }) }),\n    isInvalid && errorMessage ? /* @__PURE__ */ jsx(\"div\", { ...getErrorMessageProps(), children: errorMessage }) : description ? /* @__PURE__ */ jsx(\"div\", { ...getDescriptionProps(), children: description }) : null\n  ] });\n});\nRadioGroup.displayName = \"HeroUI.RadioGroup\";\nvar radio_group_default = RadioGroup;\n\nexport {\n  radio_group_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,sBAAsB;AACtB;AACA;AAVA;;;;;AAWA,IAAI,aAAa,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAClC,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,WAAW,EACX,SAAS,EACT,YAAY,EACZ,aAAa,EACb,aAAa,EACb,eAAe,EACf,mBAAmB,EACnB,oBAAoB,EACrB,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;IAAI;IAClC,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;QAAE,GAAG,eAAe;QAAE,UAAU;YACrE,SAAS,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,eAAe;gBAAE,UAAU;YAAM;YAC3E,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,iBAAiB;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,+JAAA,CAAA,qBAAkB,EAAE;oBAAE,OAAO;oBAAS;gBAAS;YAAG;YACnI,aAAa,eAAe,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,sBAAsB;gBAAE,UAAU;YAAa,KAAK,cAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;gBAAE,GAAG,qBAAqB;gBAAE,UAAU;YAAY,KAAK;SACjN;IAAC;AACJ;AACA,WAAW,WAAW,GAAG;AACzB,IAAI,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7021, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/radio/dist/chunk-HO6GG4EN.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useRadioGroupContext\n} from \"./chunk-A5ZCUROT.mjs\";\n\n// src/use-radio.ts\nimport { useCallback, useId } from \"react\";\nimport { useMemo, useRef } from \"react\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { useHover } from \"@react-aria/interactions\";\nimport { radio } from \"@heroui/theme\";\nimport { useRadio as useReactAriaRadio } from \"@react-aria/radio\";\nimport { useProviderContext } from \"@heroui/system\";\nimport { __DEV__, warn, clsx, dataAttr, chain, mergeProps } from \"@heroui/shared-utils\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nfunction useRadio(props) {\n  var _a, _b, _c, _d, _e;\n  const globalContext = useProviderContext();\n  const groupContext = useRadioGroupContext();\n  const {\n    as,\n    ref,\n    classNames,\n    id,\n    value,\n    children,\n    description,\n    size = (_a = groupContext == null ? void 0 : groupContext.size) != null ? _a : \"md\",\n    color = (_b = groupContext == null ? void 0 : groupContext.color) != null ? _b : \"primary\",\n    isDisabled: isDisabledProp = (_c = groupContext == null ? void 0 : groupContext.isDisabled) != null ? _c : false,\n    disableAnimation = (_e = (_d = groupContext == null ? void 0 : groupContext.disableAnimation) != null ? _d : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _e : false,\n    onChange = groupContext == null ? void 0 : groupContext.onChange,\n    autoFocus = false,\n    className,\n    ...otherProps\n  } = props;\n  if (groupContext && __DEV__) {\n    if (\"checked\" in otherProps) {\n      warn('Remove props \"checked\" if in the Radio.Group.', \"Radio\");\n    }\n    if (value === void 0) {\n      warn('Props \"value\" must be defined if in the Radio.Group.', \"Radio\");\n    }\n  }\n  const Component = as || \"label\";\n  const domRef = useDOMRef(ref);\n  const inputRef = useRef(null);\n  const labelId = useId();\n  const descriptionId = useId();\n  const isRequired = useMemo(() => {\n    var _a2;\n    return (_a2 = groupContext.isRequired) != null ? _a2 : false;\n  }, [groupContext.isRequired]);\n  const isInvalid = groupContext.isInvalid;\n  const ariaRadioProps = useMemo(() => {\n    const ariaDescribedBy = [otherProps[\"aria-describedby\"], descriptionId].filter(Boolean).join(\" \") || void 0;\n    return {\n      id,\n      isRequired,\n      isDisabled: isDisabledProp,\n      \"aria-label\": otherProps[\"aria-label\"],\n      \"aria-labelledby\": otherProps[\"aria-labelledby\"] || labelId,\n      \"aria-describedby\": ariaDescribedBy\n    };\n  }, [\n    id,\n    isDisabledProp,\n    isRequired,\n    description,\n    otherProps[\"aria-label\"],\n    otherProps[\"aria-labelledby\"],\n    otherProps[\"aria-describedby\"],\n    descriptionId\n  ]);\n  const { inputProps, isDisabled, isSelected, isPressed } = useReactAriaRadio(\n    {\n      value,\n      children: typeof children === \"function\" ? true : children,\n      ...ariaRadioProps\n    },\n    groupContext.groupState,\n    inputRef\n  );\n  const { focusProps, isFocused, isFocusVisible } = useFocusRing({\n    autoFocus\n  });\n  const interactionDisabled = isDisabled || inputProps.readOnly;\n  const { hoverProps, isHovered } = useHover({\n    isDisabled: interactionDisabled\n  });\n  const pressed = interactionDisabled ? false : isPressed;\n  const slots = useMemo(\n    () => radio({\n      color,\n      size,\n      isInvalid,\n      isDisabled,\n      disableAnimation\n    }),\n    [color, size, isDisabled, isInvalid, disableAnimation]\n  );\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const getBaseProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        ref: domRef,\n        className: slots.base({ class: baseStyles }),\n        \"data-disabled\": dataAttr(isDisabled),\n        \"data-focus\": dataAttr(isFocused),\n        \"data-focus-visible\": dataAttr(isFocusVisible),\n        \"data-selected\": dataAttr(isSelected),\n        \"data-invalid\": dataAttr(isInvalid),\n        \"data-hover\": dataAttr(isHovered),\n        \"data-pressed\": dataAttr(pressed),\n        \"data-hover-unselected\": dataAttr(isHovered && !isSelected),\n        \"data-readonly\": dataAttr(inputProps.readOnly),\n        \"aria-required\": dataAttr(isRequired),\n        ...mergeProps(hoverProps, otherProps)\n      };\n    },\n    [\n      slots,\n      baseStyles,\n      domRef,\n      isDisabled,\n      isFocused,\n      isFocusVisible,\n      isSelected,\n      isInvalid,\n      isHovered,\n      pressed,\n      inputProps.readOnly,\n      isRequired,\n      otherProps\n    ]\n  );\n  const getWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"aria-hidden\": true,\n        className: clsx(slots.wrapper({ class: clsx(classNames == null ? void 0 : classNames.wrapper, props2.className) }))\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.wrapper]\n  );\n  const getInputProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ref: inputRef,\n        ...mergeProps(props2, inputProps, focusProps),\n        className: slots.hiddenInput({ class: classNames == null ? void 0 : classNames.hiddenInput }),\n        onChange: chain(inputProps.onChange, onChange)\n      };\n    },\n    [inputProps, focusProps, onChange]\n  );\n  const getLabelProps = useCallback(\n    (props2 = {}) => ({\n      ...props2,\n      id: labelId,\n      className: slots.label({ class: classNames == null ? void 0 : classNames.label })\n    }),\n    [slots, classNames == null ? void 0 : classNames.label, isDisabled, isSelected, isInvalid]\n  );\n  const getLabelWrapperProps = useCallback(\n    (props2 = {}) => ({\n      ...props2,\n      className: slots.labelWrapper({ class: classNames == null ? void 0 : classNames.labelWrapper })\n    }),\n    [slots, classNames == null ? void 0 : classNames.labelWrapper]\n  );\n  const getControlProps = useCallback(\n    (props2 = {}) => ({\n      ...props2,\n      className: slots.control({ class: classNames == null ? void 0 : classNames.control })\n    }),\n    [slots, classNames == null ? void 0 : classNames.control]\n  );\n  const getDescriptionProps = useCallback(\n    (props2 = {}) => ({\n      ...props2,\n      id: descriptionId,\n      className: slots.description({ class: classNames == null ? void 0 : classNames.description })\n    }),\n    [slots, classNames == null ? void 0 : classNames.description]\n  );\n  return {\n    Component,\n    children,\n    isSelected,\n    isDisabled,\n    isInvalid,\n    isFocusVisible,\n    description,\n    getBaseProps,\n    getWrapperProps,\n    getInputProps,\n    getLabelProps,\n    getLabelWrapperProps,\n    getControlProps,\n    getDescriptionProps\n  };\n}\n\nexport {\n  useRadio\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,mBAAmB;AACnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;AAeA,SAAS,SAAS,KAAK;IACrB,IAAI,IAAI,IAAI,IAAI,IAAI;IACpB,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD;IACxC,MAAM,EACJ,EAAE,EACF,GAAG,EACH,UAAU,EACV,EAAE,EACF,KAAK,EACL,QAAQ,EACR,WAAW,EACX,OAAO,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,IAAI,KAAK,OAAO,KAAK,IAAI,EACnF,QAAQ,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,KAAK,KAAK,OAAO,KAAK,SAAS,EAC1F,YAAY,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,UAAU,KAAK,OAAO,KAAK,KAAK,EAChH,mBAAmB,CAAC,KAAK,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,gBAAgB,KAAK,OAAO,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK,KAAK,EAClM,WAAW,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ,EAChE,YAAY,KAAK,EACjB,SAAS,EACT,GAAG,YACJ,GAAG;IACJ,IAAI,gBAAgB,6JAAA,CAAA,UAAO,EAAE;QAC3B,IAAI,aAAa,YAAY;YAC3B,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,iDAAiD;QACxD;QACA,IAAI,UAAU,KAAK,GAAG;YACpB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,wDAAwD;QAC/D;IACF;IACA,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACpB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI;QACJ,OAAO,CAAC,MAAM,aAAa,UAAU,KAAK,OAAO,MAAM;IACzD,GAAG;QAAC,aAAa,UAAU;KAAC;IAC5B,MAAM,YAAY,aAAa,SAAS;IACxC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,kBAAkB;YAAC,UAAU,CAAC,mBAAmB;YAAE;SAAc,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ,KAAK;QAC1G,OAAO;YACL;YACA;YACA,YAAY;YACZ,cAAc,UAAU,CAAC,aAAa;YACtC,mBAAmB,UAAU,CAAC,kBAAkB,IAAI;YACpD,oBAAoB;QACtB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,kBAAkB;QAC7B,UAAU,CAAC,mBAAmB;QAC9B;KACD;IACD,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAiB,AAAD,EACxE;QACE;QACA,UAAU,OAAO,aAAa,aAAa,OAAO;QAClD,GAAG,cAAc;IACnB,GACA,aAAa,UAAU,EACvB;IAEF,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAC7D;IACF;IACA,MAAM,sBAAsB,cAAc,WAAW,QAAQ;IAC7D,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,YAAY;IACd;IACA,MAAM,UAAU,sBAAsB,QAAQ;IAC9C,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,+JAAA,CAAA,QAAK,AAAD,EAAE;YACV;YACA;YACA;YACA;YACA;QACF,IACA;QAAC;QAAO;QAAM;QAAY;QAAW;KAAiB;IAExD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,KAAK;YACL,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO;YAAW;YAC1C,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,yBAAyB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,CAAC;YAChD,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,QAAQ;YAC7C,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,WAAW;QACvC;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,QAAQ;QACnB;QACA;KACD;IAEH,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,eAAe;YACf,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,OAAO,SAAS;YAAE;QAClH;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;KAAC;IAE3D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,KAAK;YACL,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,YAAY,WAAW;YAC7C,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;YAAC;YAC3F,UAAU,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD,EAAE,WAAW,QAAQ,EAAE;QACvC;IACF,GACA;QAAC;QAAY;QAAY;KAAS;IAEpC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,GAAG,MAAM;YACT,IAAI;YACJ,WAAW,MAAM,KAAK,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;YAAC;QACjF,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;QAAE;QAAY;QAAY;KAAU;IAE5F,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,GAAG,MAAM;YACT,WAAW,MAAM,YAAY,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;YAAC;QAC/F,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;KAAC;IAEhE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,GAAG,MAAM;YACT,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;YAAC;QACrF,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;KAAC;IAE3D,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YAChB,GAAG,MAAM;YACT,IAAI;YACJ,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;YAAC;QAC7F,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;KAAC;IAE/D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/radio/dist/chunk-IRK6BJWJ.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useRadio\n} from \"./chunk-HO6GG4EN.mjs\";\n\n// src/radio.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Radio = forwardRef((props, ref) => {\n  const {\n    Component,\n    children,\n    description,\n    getBaseProps,\n    getWrapperProps,\n    getInputProps,\n    getLabelProps,\n    getLabelWrapperProps,\n    getControlProps,\n    getDescriptionProps\n  } = useRadio({ ...props, ref });\n  return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [\n    /* @__PURE__ */ jsx(\"input\", { ...getInputProps() }),\n    /* @__PURE__ */ jsx(\"span\", { ...getWrapperProps(), children: /* @__PURE__ */ jsx(\"span\", { ...getControlProps() }) }),\n    /* @__PURE__ */ jsxs(\"div\", { ...getLabelWrapperProps(), children: [\n      children && /* @__PURE__ */ jsx(\"span\", { ...getLabelProps(), children }),\n      description && /* @__PURE__ */ jsx(\"span\", { ...getDescriptionProps(), children: description })\n    ] })\n  ] });\n});\nRadio.displayName = \"HeroUI.Radio\";\nvar radio_default = Radio;\n\nexport {\n  radio_default\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,gBAAgB;AAChB;AACA;AAPA;;;;AAQA,IAAI,QAAQ,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC7B,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,eAAe,EACf,aAAa,EACb,aAAa,EACb,oBAAoB,EACpB,eAAe,EACf,mBAAmB,EACpB,GAAG,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;IAAI;IAC7B,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;QAAE,GAAG,cAAc;QAAE,UAAU;YACpE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,SAAS;gBAAE,GAAG,eAAe;YAAC;YAClD,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,iBAAiB;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;oBAAE,GAAG,iBAAiB;gBAAC;YAAG;YACpH,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;gBAAE,GAAG,sBAAsB;gBAAE,UAAU;oBACjE,YAAY,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wBAAE,GAAG,eAAe;wBAAE;oBAAS;oBACvE,eAAe,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wBAAE,GAAG,qBAAqB;wBAAE,UAAU;oBAAY;iBAC9F;YAAC;SACH;IAAC;AACJ;AACA,MAAM,WAAW,GAAG;AACpB,IAAI,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/checkbox/dist/chunk-FFVF7OUL.mjs"], "sourcesContent": ["\"use client\";\n\n// src/checkbox-icon.tsx\nimport { jsx } from \"react/jsx-runtime\";\nfunction CheckIcon(props) {\n  const { isSelected, disableAnimation, ...otherProps } = props;\n  return /* @__PURE__ */ jsx(\n    \"svg\",\n    {\n      \"aria-hidden\": \"true\",\n      fill: \"none\",\n      role: \"presentation\",\n      stroke: \"currentColor\",\n      strokeDasharray: 22,\n      strokeDashoffset: isSelected ? 44 : 66,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      style: !disableAnimation && isSelected ? {\n        transition: \"stroke-dashoffset 250ms linear 0.2s\"\n      } : {},\n      viewBox: \"0 0 17 18\",\n      ...otherProps,\n      children: /* @__PURE__ */ jsx(\"polyline\", { points: \"1 9 7 14 15 4\" })\n    }\n  );\n}\nfunction IndeterminateIcon(props) {\n  const { isSelected, disableAnimation, ...otherProps } = props;\n  return /* @__PURE__ */ jsx(\"svg\", { stroke: \"currentColor\", strokeWidth: 3, viewBox: \"0 0 24 24\", ...otherProps, children: /* @__PURE__ */ jsx(\"line\", { x1: \"21\", x2: \"3\", y1: \"12\", y2: \"12\" }) });\n}\nfunction CheckboxIcon(props) {\n  const { isIndeterminate, ...otherProps } = props;\n  const BaseIcon = isIndeterminate ? IndeterminateIcon : CheckIcon;\n  return /* @__PURE__ */ jsx(BaseIcon, { ...otherProps });\n}\n\nexport {\n  CheckboxIcon\n};\n"], "names": [], "mappings": ";;;AAEA,wBAAwB;AACxB;AAHA;;AAIA,SAAS,UAAU,KAAK;IACtB,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,YAAY,GAAG;IACxD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,OACA;QACE,eAAe;QACf,MAAM;QACN,MAAM;QACN,QAAQ;QACR,iBAAiB;QACjB,kBAAkB,aAAa,KAAK;QACpC,eAAe;QACf,gBAAgB;QAChB,aAAa;QACb,OAAO,CAAC,oBAAoB,aAAa;YACvC,YAAY;QACd,IAAI,CAAC;QACL,SAAS;QACT,GAAG,UAAU;QACb,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,YAAY;YAAE,QAAQ;QAAgB;IACtE;AAEJ;AACA,SAAS,kBAAkB,KAAK;IAC9B,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG,YAAY,GAAG;IACxD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAAE,QAAQ;QAAgB,aAAa;QAAG,SAAS;QAAa,GAAG,UAAU;QAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,IAAI;YAAM,IAAI;YAAK,IAAI;YAAM,IAAI;QAAK;IAAG;AACpM;AACA,SAAS,aAAa,KAAK;IACzB,MAAM,EAAE,eAAe,EAAE,GAAG,YAAY,GAAG;IAC3C,MAAM,WAAW,kBAAkB,oBAAoB;IACvD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,UAAU;QAAE,GAAG,UAAU;IAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/checkbox/dist/chunk-LK575OWX.mjs"], "sourcesContent": ["\"use client\";\n\n// src/checkbox-group-context.ts\nimport { createContext } from \"@heroui/react-utils\";\nvar [CheckboxGroupProvider, useCheckboxGroupContext] = createContext({\n  name: \"CheckboxGroupContext\",\n  strict: false\n});\n\nexport {\n  CheckboxGroupProvider,\n  useCheckboxGroupContext\n};\n"], "names": [], "mappings": ";;;;AAEA,gCAAgC;AAChC;AAHA;;AAIA,IAAI,CAAC,uBAAuB,wBAAwB,GAAG,CAAA,GAAA,wKAAA,CAAA,gBAAa,AAAD,EAAE;IACnE,MAAM;IACN,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7375, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/checkbox/dist/chunk-VBM6YDUQ.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useCheckboxGroupContext\n} from \"./chunk-LK575OWX.mjs\";\n\n// src/use-checkbox.ts\nimport { useProviderContext } from \"@heroui/system\";\nimport { useCallback, useId } from \"react\";\nimport { useMemo, useRef } from \"react\";\nimport { useToggleState } from \"@react-stately/toggle\";\nimport { checkbox } from \"@heroui/theme\";\nimport { useCallbackRef } from \"@heroui/use-callback-ref\";\nimport { useHover } from \"@react-aria/interactions\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport {\n  __DEV__,\n  warn,\n  clsx,\n  dataAttr,\n  safeAriaLabel,\n  mergeProps,\n  chain\n} from \"@heroui/shared-utils\";\nimport {\n  useCheckbox as useReactAriaCheckbox,\n  useCheckboxGroupItem as useReactAriaCheckboxGroupItem\n} from \"@react-aria/checkbox\";\nimport { useSafeLayoutEffect } from \"@heroui/use-safe-layout-effect\";\nimport { mergeRefs } from \"@heroui/react-utils\";\nimport { FormContext, useSlottedContext } from \"@heroui/form\";\nfunction useCheckbox(props = {}) {\n  var _a, _b, _c, _d, _e, _f, _g, _h;\n  const globalContext = useProviderContext();\n  const groupContext = useCheckboxGroupContext();\n  const { validationBehavior: formValidationBehavior } = useSlottedContext(FormContext) || {};\n  const isInGroup = !!groupContext;\n  const {\n    as,\n    ref,\n    value = \"\",\n    children,\n    icon,\n    name,\n    isRequired,\n    isReadOnly: isReadOnlyProp = false,\n    autoFocus = false,\n    isSelected: isSelectedProp,\n    size = (_a = groupContext == null ? void 0 : groupContext.size) != null ? _a : \"md\",\n    color = (_b = groupContext == null ? void 0 : groupContext.color) != null ? _b : \"primary\",\n    radius = groupContext == null ? void 0 : groupContext.radius,\n    lineThrough = (_c = groupContext == null ? void 0 : groupContext.lineThrough) != null ? _c : false,\n    isDisabled: isDisabledProp = (_d = groupContext == null ? void 0 : groupContext.isDisabled) != null ? _d : false,\n    disableAnimation = (_f = (_e = groupContext == null ? void 0 : groupContext.disableAnimation) != null ? _e : globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _f : false,\n    validationState,\n    isInvalid: isInvalidProp = validationState ? validationState === \"invalid\" : (_g = groupContext == null ? void 0 : groupContext.isInvalid) != null ? _g : false,\n    isIndeterminate = false,\n    validationBehavior = isInGroup ? groupContext.validationBehavior : (_h = formValidationBehavior != null ? formValidationBehavior : globalContext == null ? void 0 : globalContext.validationBehavior) != null ? _h : \"native\",\n    defaultSelected,\n    classNames,\n    className,\n    onValueChange,\n    validate,\n    ...otherProps\n  } = props;\n  if (groupContext && __DEV__) {\n    if (isSelectedProp) {\n      warn(\n        \"The Checkbox.Group is being used, `isSelected` will be ignored. Use the `value` of the Checkbox.Group instead.\",\n        \"Checkbox\"\n      );\n    }\n    if (defaultSelected) {\n      warn(\n        \"The Checkbox.Group is being used, `defaultSelected` will be ignored. Use the `defaultValue` of the Checkbox.Group instead.\",\n        \"Checkbox\"\n      );\n    }\n  }\n  const Component = as || \"label\";\n  const domRef = useRef(null);\n  const inputRef = useRef(null);\n  let onChange = props.onChange;\n  if (isInGroup) {\n    const dispatch = () => {\n      groupContext.groupState.resetValidation();\n    };\n    onChange = chain(dispatch, onChange);\n  }\n  const labelId = useId();\n  const ariaCheckboxProps = useMemo(\n    () => ({\n      name,\n      value,\n      children,\n      autoFocus,\n      defaultSelected,\n      isIndeterminate,\n      isRequired,\n      isInvalid: isInvalidProp,\n      isSelected: isSelectedProp,\n      isDisabled: isDisabledProp,\n      isReadOnly: isReadOnlyProp,\n      \"aria-label\": safeAriaLabel(otherProps[\"aria-label\"], children),\n      \"aria-labelledby\": otherProps[\"aria-labelledby\"] || labelId,\n      onChange: onValueChange\n    }),\n    [\n      name,\n      value,\n      children,\n      autoFocus,\n      defaultSelected,\n      isIndeterminate,\n      isRequired,\n      isInvalidProp,\n      isSelectedProp,\n      isDisabledProp,\n      isReadOnlyProp,\n      otherProps[\"aria-label\"],\n      otherProps[\"aria-labelledby\"],\n      labelId,\n      onValueChange\n    ]\n  );\n  const toggleState = useToggleState(ariaCheckboxProps);\n  const validationProps = {\n    isInvalid: isInvalidProp,\n    isRequired,\n    validate,\n    validationState,\n    validationBehavior\n  };\n  const {\n    inputProps,\n    isSelected,\n    isDisabled,\n    isReadOnly,\n    isPressed,\n    isInvalid: isAriaInvalid\n  } = isInGroup ? (\n    // eslint-disable-next-line\n    useReactAriaCheckboxGroupItem(\n      { ...ariaCheckboxProps, ...validationProps },\n      groupContext.groupState,\n      inputRef\n    )\n  ) : (\n    // eslint-disable-next-line\n    useReactAriaCheckbox({ ...ariaCheckboxProps, ...validationProps }, toggleState, inputRef)\n  );\n  const isInteractionDisabled = isDisabled || isReadOnly;\n  const isInvalid = validationState === \"invalid\" || isInvalidProp || isAriaInvalid;\n  const pressed = isInteractionDisabled ? false : isPressed;\n  const { hoverProps, isHovered } = useHover({\n    isDisabled: inputProps.disabled\n  });\n  const { focusProps, isFocused, isFocusVisible } = useFocusRing({\n    autoFocus: inputProps.autoFocus\n  });\n  const slots = useMemo(\n    () => checkbox({\n      color,\n      size,\n      radius,\n      isInvalid,\n      lineThrough,\n      isDisabled,\n      disableAnimation\n    }),\n    [color, size, radius, isInvalid, lineThrough, isDisabled, disableAnimation]\n  );\n  useSafeLayoutEffect(() => {\n    if (!inputRef.current) return;\n    const isInputRefChecked = !!inputRef.current.checked;\n    toggleState.setSelected(isInputRefChecked);\n  }, [inputRef.current]);\n  const onChangeProp = useCallbackRef(onChange);\n  const handleCheckboxChange = useCallback(\n    (event) => {\n      if (isReadOnly || isDisabled) {\n        event.preventDefault();\n        return;\n      }\n      onChangeProp == null ? void 0 : onChangeProp(event);\n    },\n    [isReadOnly, isDisabled, onChangeProp]\n  );\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const getBaseProps = useCallback(() => {\n    return {\n      ref: domRef,\n      className: slots.base({ class: baseStyles }),\n      \"data-disabled\": dataAttr(isDisabled),\n      \"data-selected\": dataAttr(isSelected || isIndeterminate),\n      \"data-invalid\": dataAttr(isInvalid),\n      \"data-hover\": dataAttr(isHovered),\n      \"data-focus\": dataAttr(isFocused),\n      \"data-pressed\": dataAttr(pressed),\n      \"data-readonly\": dataAttr(inputProps.readOnly),\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      \"data-indeterminate\": dataAttr(isIndeterminate),\n      ...mergeProps(hoverProps, otherProps)\n    };\n  }, [\n    slots,\n    baseStyles,\n    isDisabled,\n    isSelected,\n    isIndeterminate,\n    isInvalid,\n    isHovered,\n    isFocused,\n    pressed,\n    inputProps.readOnly,\n    isFocusVisible,\n    hoverProps,\n    otherProps\n  ]);\n  const getWrapperProps = useCallback(\n    (props2 = {}) => {\n      return {\n        ...props2,\n        \"aria-hidden\": true,\n        className: clsx(slots.wrapper({ class: clsx(classNames == null ? void 0 : classNames.wrapper, props2 == null ? void 0 : props2.className) }))\n      };\n    },\n    [slots, classNames == null ? void 0 : classNames.wrapper]\n  );\n  const getInputProps = useCallback(() => {\n    return {\n      ref: mergeRefs(inputRef, ref),\n      ...mergeProps(inputProps, focusProps),\n      className: slots.hiddenInput({ class: classNames == null ? void 0 : classNames.hiddenInput }),\n      onChange: chain(inputProps.onChange, handleCheckboxChange)\n    };\n  }, [inputProps, focusProps, handleCheckboxChange, classNames == null ? void 0 : classNames.hiddenInput]);\n  const getLabelProps = useCallback(\n    () => ({\n      id: labelId,\n      className: slots.label({ class: classNames == null ? void 0 : classNames.label })\n    }),\n    [slots, classNames == null ? void 0 : classNames.label, isDisabled, isSelected, isInvalid]\n  );\n  const getIconProps = useCallback(\n    () => ({\n      isSelected,\n      isIndeterminate,\n      disableAnimation,\n      className: slots.icon({ class: classNames == null ? void 0 : classNames.icon })\n    }),\n    [slots, classNames == null ? void 0 : classNames.icon, isSelected, isIndeterminate, disableAnimation]\n  );\n  return {\n    Component,\n    icon,\n    children,\n    isSelected,\n    isDisabled,\n    isInvalid,\n    isFocused,\n    isHovered,\n    isFocusVisible,\n    getBaseProps,\n    getWrapperProps,\n    getInputProps,\n    getLabelProps,\n    getIconProps\n  };\n}\n\nexport {\n  useCheckbox\n};\n"], "names": [], "mappings": ";;;AACA;AAIA,sBAAsB;AACtB;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AASA;AAAA;AAIA;AACA;AACA;AAAA;AA7BA;;;;;;;;;;;;;;;AA8BA,SAAS,YAAY,QAAQ,CAAC,CAAC;IAC7B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IAChC,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,0BAAuB,AAAD;IAC3C,MAAM,EAAE,oBAAoB,sBAAsB,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,8JAAA,CAAA,cAAW,KAAK,CAAC;IAC1F,MAAM,YAAY,CAAC,CAAC;IACpB,MAAM,EACJ,EAAE,EACF,GAAG,EACH,QAAQ,EAAE,EACV,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,iBAAiB,KAAK,EAClC,YAAY,KAAK,EACjB,YAAY,cAAc,EAC1B,OAAO,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,IAAI,KAAK,OAAO,KAAK,IAAI,EACnF,QAAQ,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,KAAK,KAAK,OAAO,KAAK,SAAS,EAC1F,SAAS,gBAAgB,OAAO,KAAK,IAAI,aAAa,MAAM,EAC5D,cAAc,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,WAAW,KAAK,OAAO,KAAK,KAAK,EAClG,YAAY,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,UAAU,KAAK,OAAO,KAAK,KAAK,EAChH,mBAAmB,CAAC,KAAK,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,gBAAgB,KAAK,OAAO,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK,KAAK,EAClM,eAAe,EACf,WAAW,gBAAgB,kBAAkB,oBAAoB,YAAY,CAAC,KAAK,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS,KAAK,OAAO,KAAK,KAAK,EAC/J,kBAAkB,KAAK,EACvB,qBAAqB,YAAY,aAAa,kBAAkB,GAAG,CAAC,KAAK,0BAA0B,OAAO,yBAAyB,iBAAiB,OAAO,KAAK,IAAI,cAAc,kBAAkB,KAAK,OAAO,KAAK,QAAQ,EAC7N,eAAe,EACf,UAAU,EACV,SAAS,EACT,aAAa,EACb,QAAQ,EACR,GAAG,YACJ,GAAG;IACJ,IAAI,gBAAgB,6JAAA,CAAA,UAAO,EAAE;QAC3B,IAAI,gBAAgB;YAClB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EACD,kHACA;QAEJ;QACA,IAAI,iBAAiB;YACnB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EACD,8HACA;QAEJ;IACF;IACA,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,WAAW;QACb,MAAM,WAAW;YACf,aAAa,UAAU,CAAC,eAAe;QACzC;QACA,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD,EAAE,UAAU;IAC7B;IACA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACpB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC9B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA,WAAW;YACX,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,cAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,CAAC,aAAa,EAAE;YACtD,mBAAmB,UAAU,CAAC,kBAAkB,IAAI;YACpD,UAAU;QACZ,CAAC,GACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,kBAAkB;QAC7B;QACA;KACD;IAEH,MAAM,cAAc,CAAA,GAAA,uKAAA,CAAA,iBAAc,AAAD,EAAE;IACnC,MAAM,kBAAkB;QACtB,WAAW;QACX;QACA;QACA;QACA;IACF;IACA,MAAM,EACJ,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,SAAS,EACT,WAAW,aAAa,EACzB,GAAG,YACF,2BAA2B;IAC3B,CAAA,GAAA,4KAAA,CAAA,uBAA6B,AAAD,EAC1B;QAAE,GAAG,iBAAiB;QAAE,GAAG,eAAe;IAAC,GAC3C,aAAa,UAAU,EACvB,YAGF,2BAA2B;IAC3B,CAAA,GAAA,mKAAA,CAAA,cAAoB,AAAD,EAAE;QAAE,GAAG,iBAAiB;QAAE,GAAG,eAAe;IAAC,GAAG,aAAa;IAElF,MAAM,wBAAwB,cAAc;IAC5C,MAAM,YAAY,oBAAoB,aAAa,iBAAiB;IACpE,MAAM,UAAU,wBAAwB,QAAQ;IAChD,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,YAAY,WAAW,QAAQ;IACjC;IACA,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAC7D,WAAW,WAAW,SAAS;IACjC;IACA,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE;YACb;YACA;YACA;YACA;YACA;YACA;YACA;QACF,IACA;QAAC;QAAO;QAAM;QAAQ;QAAW;QAAa;QAAY;KAAiB;IAE7E,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,IAAI,CAAC,SAAS,OAAO,EAAE;QACvB,MAAM,oBAAoB,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO;QACpD,YAAY,WAAW,CAAC;IAC1B,GAAG;QAAC,SAAS,OAAO;KAAC;IACrB,MAAM,eAAe,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EAAE;IACpC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC;QACC,IAAI,cAAc,YAAY;YAC5B,MAAM,cAAc;YACpB;QACF;QACA,gBAAgB,OAAO,KAAK,IAAI,aAAa;IAC/C,GACA;QAAC;QAAY;QAAY;KAAa;IAExC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,OAAO;YACL,KAAK;YACL,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO;YAAW;YAC1C,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1B,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;YACxC,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,QAAQ;YAC7C,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC/B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,WAAW;QACvC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,QAAQ;QACnB;QACA;QACA;KACD;IACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAS,CAAC,CAAC;QACV,OAAO;YACL,GAAG,MAAM;YACT,eAAe;YACf,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QAC5I;IACF,GACA;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;KAAC;IAE3D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,OAAO;YACL,KAAK,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE,UAAU;YACzB,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,YAAY,WAAW;YACrC,WAAW,MAAM,WAAW,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;YAAC;YAC3F,UAAU,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD,EAAE,WAAW,QAAQ,EAAE;QACvC;IACF,GAAG;QAAC;QAAY;QAAY;QAAsB,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW;KAAC;IACvG,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,IAAM,CAAC;YACL,IAAI;YACJ,WAAW,MAAM,KAAK,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;YAAC;QACjF,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;QAAE;QAAY;QAAY;KAAU;IAE5F,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,IAAM,CAAC;YACL;YACA;YACA;YACA,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI;YAAC;QAC/E,CAAC,GACD;QAAC;QAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI;QAAE;QAAY;QAAiB;KAAiB;IAEvG,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7637, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/checkbox/dist/chunk-XJ2YRSUP.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  CheckboxIcon\n} from \"./chunk-FFVF7OUL.mjs\";\nimport {\n  useCheckbox\n} from \"./chunk-VBM6YDUQ.mjs\";\n\n// src/checkbox.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { cloneElement } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar Checkbox = forwardRef((props, ref) => {\n  const {\n    Component,\n    children,\n    icon = /* @__PURE__ */ jsx(CheckboxIcon, {}),\n    getBaseProps,\n    getWrapperProps,\n    getInputProps,\n    getIconProps,\n    getLabelProps\n  } = useCheckbox({ ...props, ref });\n  const clonedIcon = typeof icon === \"function\" ? icon(getIconProps()) : cloneElement(icon, getIconProps());\n  return /* @__PURE__ */ jsxs(Component, { ...getBaseProps(), children: [\n    /* @__PURE__ */ jsx(\"input\", { ...getInputProps() }),\n    /* @__PURE__ */ jsx(\"span\", { ...getWrapperProps(), children: clonedIcon }),\n    children && /* @__PURE__ */ jsx(\"span\", { ...getLabelProps(), children })\n  ] });\n});\nCheckbox.displayName = \"HeroUI.Checkbox\";\nvar checkbox_default = Checkbox;\n\nexport {\n  checkbox_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,mBAAmB;AACnB;AACA;AACA;AAXA;;;;;;AAYA,IAAI,WAAW,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAChC,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,kKAAA,CAAA,eAAY,EAAE,CAAC,EAAE,EAC5C,YAAY,EACZ,eAAe,EACf,aAAa,EACb,YAAY,EACZ,aAAa,EACd,GAAG,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,GAAG,KAAK;QAAE;IAAI;IAChC,MAAM,aAAa,OAAO,SAAS,aAAa,KAAK,kBAAkB,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC1F,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;QAAE,GAAG,cAAc;QAAE,UAAU;YACpE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,SAAS;gBAAE,GAAG,eAAe;YAAC;YAClD,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,iBAAiB;gBAAE,UAAU;YAAW;YACzE,YAAY,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;gBAAE,GAAG,eAAe;gBAAE;YAAS;SACxE;IAAC;AACJ;AACA,SAAS,WAAW,GAAG;AACvB,IAAI,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7690, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-callback-ref/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { useCallback, useRef } from \"react\";\nimport { useSafeLayoutEffect } from \"@heroui/use-safe-layout-effect\";\nfunction useCallbackRef(fn, deps = []) {\n  const ref = useRef(fn);\n  useSafeLayoutEffect(() => {\n    ref.current = fn;\n  });\n  return useCallback((...args) => {\n    var _a;\n    return (_a = ref.current) == null ? void 0 : _a.call(ref, ...args);\n  }, deps);\n}\nexport {\n  useCallbackRef\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf;AACA;;;AACA,SAAS,eAAe,EAAE,EAAE,OAAO,EAAE;IACnC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,IAAI,OAAO,GAAG;IAChB;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,GAAG;QACrB,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ;IAC/D,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7713, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-YQRLDUCT.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-select-all-checkbox.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableColumnHeader, useTableSelectAllCheckbox } from \"@react-aria/table\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { Checkbox } from \"@heroui/checkbox\";\nimport { VisuallyHidden } from \"@react-aria/visually-hidden\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TableSelectAllCheckbox = forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n    as,\n    className,\n    node,\n    slots,\n    state,\n    selectionMode,\n    color,\n    checkboxesProps,\n    disableAnimation,\n    classNames,\n    ...otherProps\n  } = props;\n  const Component = as || \"th\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { columnHeaderProps } = useTableColumnHeader({ node }, state, domRef);\n  const { isFocusVisible, focusProps } = useFocusRing();\n  const { checkboxProps } = useTableSelectAllCheckbox(state);\n  const thStyles = clsx(classNames == null ? void 0 : classNames.th, className, (_a = node.props) == null ? void 0 : _a.className);\n  const isSingleSelectionMode = selectionMode === \"single\";\n  const { onChange, ...otherCheckboxProps } = checkboxProps;\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      ...mergeProps(\n        columnHeaderProps,\n        focusProps,\n        filterDOMProps(node.props, {\n          enabled: shouldFilterDOMProps\n        }),\n        filterDOMProps(otherProps, {\n          enabled: shouldFilterDOMProps\n        })\n      ),\n      className: (_b = slots.th) == null ? void 0 : _b.call(slots, { class: thStyles }),\n      children: isSingleSelectionMode ? /* @__PURE__ */ jsx(VisuallyHidden, { children: checkboxProps[\"aria-label\"] }) : /* @__PURE__ */ jsx(\n        Checkbox,\n        {\n          color,\n          disableAnimation,\n          onValueChange: onChange,\n          ...mergeProps(checkboxesProps, otherCheckboxProps)\n        }\n      )\n    }\n  );\n});\nTableSelectAllCheckbox.displayName = \"HeroUI.TableSelectAllCheckbox\";\nvar table_select_all_checkbox_default = TableSelectAllCheckbox;\n\nexport {\n  table_select_all_checkbox_default\n};\n"], "names": [], "mappings": ";;;AAEA,oCAAoC;AACpC;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAWA,IAAI,yBAAyB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC9C,IAAI,IAAI;IACR,MAAM,EACJ,EAAE,EACF,SAAS,EACT,IAAI,EACJ,KAAK,EACL,KAAK,EACL,aAAa,EACb,KAAK,EACL,eAAe,EACf,gBAAgB,EAChB,UAAU,EACV,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,uBAAoB,AAAD,EAAE;QAAE;IAAK,GAAG,OAAO;IACpE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAClD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,4BAAyB,AAAD,EAAE;IACpD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;IAC/H,MAAM,wBAAwB,kBAAkB;IAChD,MAAM,EAAE,QAAQ,EAAE,GAAG,oBAAoB,GAAG;IAC5C,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,mBACA,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE;YACzB,SAAS;QACX,IACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;YACzB,SAAS;QACX,GACD;QACD,WAAW,CAAC,KAAK,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAS;QAC/E,UAAU,wBAAwB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gLAAA,CAAA,iBAAc,EAAE;YAAE,UAAU,aAAa,CAAC,aAAa;QAAC,KAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACnI,kNAAA,CAAA,WAAQ,EACR;YACE;YACA;YACA,eAAe;YACf,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,mBAAmB;QACpD;IAEJ;AAEJ;AACA,uBAAuB,WAAW,GAAG;AACrC,IAAI,oCAAoC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7778, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-6QNXQNN7.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-table.ts\nimport { useCallback } from \"react\";\nimport { useTableState } from \"@react-stately/table\";\nimport { useTable as useReactAriaTable } from \"@react-aria/table\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { table } from \"@heroui/theme\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, objectToDeps, mergeProps } from \"@heroui/shared-utils\";\nimport { useMemo } from \"react\";\nfunction useTable(originalProps) {\n  var _a;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, table.variantKeys);\n  const {\n    ref,\n    as,\n    baseRef,\n    children,\n    className,\n    classNames,\n    removeWrapper = false,\n    disableAnimation = (_a = globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _a : false,\n    isKeyboardNavigationDisabled = false,\n    selectionMode = \"none\",\n    topContentPlacement = \"inside\",\n    bottomContentPlacement = \"inside\",\n    selectionBehavior = selectionMode === \"none\" ? null : \"toggle\",\n    disabledBehavior = \"selection\",\n    showSelectionCheckboxes = selectionMode === \"multiple\" && selectionBehavior !== \"replace\",\n    BaseComponent = \"div\",\n    checkboxesProps,\n    topContent,\n    bottomContent,\n    sortIcon,\n    onRowAction,\n    onCellAction,\n    ...otherProps\n  } = props;\n  const Component = as || \"table\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const domBaseRef = useDOMRef(baseRef);\n  const state = useTableState({\n    ...originalProps,\n    children,\n    showSelectionCheckboxes\n  });\n  if (isKeyboardNavigationDisabled && !state.isKeyboardNavigationDisabled) {\n    state.setKeyboardNavigationDisabled(true);\n  }\n  const { collection } = state;\n  const { layout, ...otherOriginalProps } = originalProps;\n  const { gridProps } = useReactAriaTable({ ...otherOriginalProps }, state, domRef);\n  const isSelectable = selectionMode !== \"none\";\n  const isMultiSelectable = selectionMode === \"multiple\";\n  const slots = useMemo(\n    () => table({\n      ...variantProps,\n      isSelectable,\n      isMultiSelectable\n    }),\n    [objectToDeps(variantProps), isSelectable, isMultiSelectable]\n  );\n  const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);\n  const values = useMemo(\n    () => {\n      var _a2;\n      return {\n        state,\n        slots,\n        isSelectable,\n        collection,\n        classNames,\n        color: originalProps == null ? void 0 : originalProps.color,\n        disableAnimation,\n        checkboxesProps,\n        isHeaderSticky: (_a2 = originalProps == null ? void 0 : originalProps.isHeaderSticky) != null ? _a2 : false,\n        selectionMode,\n        selectionBehavior,\n        disabledBehavior,\n        showSelectionCheckboxes,\n        onRowAction,\n        onCellAction\n      };\n    },\n    [\n      slots,\n      state,\n      collection,\n      isSelectable,\n      classNames,\n      selectionMode,\n      selectionBehavior,\n      checkboxesProps,\n      disabledBehavior,\n      disableAnimation,\n      showSelectionCheckboxes,\n      originalProps == null ? void 0 : originalProps.color,\n      originalProps == null ? void 0 : originalProps.isHeaderSticky,\n      onRowAction,\n      onCellAction\n    ]\n  );\n  const getBaseProps = useCallback(\n    (props2) => ({\n      ...props2,\n      ref: domBaseRef,\n      className: slots.base({ class: clsx(baseStyles, props2 == null ? void 0 : props2.className) })\n    }),\n    [baseStyles, slots]\n  );\n  const getWrapperProps = useCallback(\n    (props2) => ({\n      ...props2,\n      ref: domBaseRef,\n      className: slots.wrapper({ class: clsx(classNames == null ? void 0 : classNames.wrapper, props2 == null ? void 0 : props2.className) })\n    }),\n    [classNames == null ? void 0 : classNames.wrapper, slots]\n  );\n  const getTableProps = useCallback(\n    (props2) => ({\n      ...mergeProps(\n        gridProps,\n        filterDOMProps(otherProps, {\n          enabled: shouldFilterDOMProps\n        }),\n        props2\n      ),\n      // avoid typeahead debounce wait for input / textarea\n      // so that typing with space won't be blocked\n      onKeyDownCapture: void 0,\n      ref: domRef,\n      className: slots.table({ class: clsx(classNames == null ? void 0 : classNames.table, props2 == null ? void 0 : props2.className) })\n    }),\n    [classNames == null ? void 0 : classNames.table, shouldFilterDOMProps, slots, gridProps, otherProps]\n  );\n  return {\n    BaseComponent,\n    Component,\n    children,\n    state,\n    collection,\n    values,\n    topContent,\n    bottomContent,\n    removeWrapper,\n    topContentPlacement,\n    bottomContentPlacement,\n    sortIcon,\n    getBaseProps,\n    getWrapperProps,\n    getTableProps\n  };\n}\n\nexport {\n  useTable\n};\n"], "names": [], "mappings": ";;;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;AAWA,SAAS,SAAS,aAAa;IAC7B,IAAI;IACJ,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,QAAK,CAAC,WAAW;IAC/E,MAAM,EACJ,GAAG,EACH,EAAE,EACF,OAAO,EACP,QAAQ,EACR,SAAS,EACT,UAAU,EACV,gBAAgB,KAAK,EACrB,mBAAmB,CAAC,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK,KAAK,EAC9G,+BAA+B,KAAK,EACpC,gBAAgB,MAAM,EACtB,sBAAsB,QAAQ,EAC9B,yBAAyB,QAAQ,EACjC,oBAAoB,kBAAkB,SAAS,OAAO,QAAQ,EAC9D,mBAAmB,WAAW,EAC9B,0BAA0B,kBAAkB,cAAc,sBAAsB,SAAS,EACzF,gBAAgB,KAAK,EACrB,eAAe,EACf,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IAC7B,MAAM,QAAQ,CAAA,GAAA,qKAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,GAAG,aAAa;QAChB;QACA;IACF;IACA,IAAI,gCAAgC,CAAC,MAAM,4BAA4B,EAAE;QACvE,MAAM,6BAA6B,CAAC;IACtC;IACA,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,oBAAoB,GAAG;IAC1C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAiB,AAAD,EAAE;QAAE,GAAG,kBAAkB;IAAC,GAAG,OAAO;IAC1E,MAAM,eAAe,kBAAkB;IACvC,MAAM,oBAAoB,kBAAkB;IAC5C,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAClB,IAAM,CAAA,GAAA,+JAAA,CAAA,QAAK,AAAD,EAAE;YACV,GAAG,YAAY;YACf;YACA;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;QAAc;KAAkB;IAE/D,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE;IACvE,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACnB;QACE,IAAI;QACJ,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,OAAO,iBAAiB,OAAO,KAAK,IAAI,cAAc,KAAK;YAC3D;YACA;YACA,gBAAgB,CAAC,MAAM,iBAAiB,OAAO,KAAK,IAAI,cAAc,cAAc,KAAK,OAAO,MAAM;YACtG;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,KAAK;QACpD,iBAAiB,OAAO,KAAK,IAAI,cAAc,cAAc;QAC7D;QACA;KACD;IAEH,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC,SAAW,CAAC;YACX,GAAG,MAAM;YACT,KAAK;YACL,WAAW,MAAM,IAAI,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,YAAY,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QAC9F,CAAC,GACD;QAAC;QAAY;KAAM;IAErB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,SAAW,CAAC;YACX,GAAG,MAAM;YACT,KAAK;YACL,WAAW,MAAM,OAAO,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QACvI,CAAC,GACD;QAAC,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;QAAE;KAAM;IAE3D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAW,CAAC;YACX,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,WACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;gBACzB,SAAS;YACX,IACA,OACD;YACD,qDAAqD;YACrD,6CAA6C;YAC7C,kBAAkB,KAAK;YACvB,KAAK;YACL,WAAW,MAAM,KAAK,CAAC;gBAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,UAAU,OAAO,KAAK,IAAI,OAAO,SAAS;YAAE;QACnI,CAAC,GACD;QAAC,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK;QAAE;QAAsB;QAAO;QAAW;KAAW;IAEtG,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7931, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-LNVX26OF.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-cell.tsx\nimport { useMemo } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableCell } from \"@react-aria/table\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TableCell = forwardRef((props, ref) => {\n  var _a, _b, _c;\n  const { as, className, node, rowKey, slots, state, classNames, ...otherProps } = props;\n  const Component = as || \"td\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { gridCellProps } = useTableCell({ node }, state, domRef);\n  const tdStyles = clsx(classNames == null ? void 0 : classNames.td, className, (_a = node.props) == null ? void 0 : _a.className);\n  const { isFocusVisible, focusProps } = useFocusRing();\n  const isRowSelected = state.selectionManager.isSelected(rowKey);\n  const cell = useMemo(() => {\n    const cellType = typeof node.rendered;\n    return cellType !== \"object\" && cellType !== \"function\" ? /* @__PURE__ */ jsx(\"span\", { children: node.rendered }) : node.rendered;\n  }, [node.rendered]);\n  const columnProps = ((_b = node.column) == null ? void 0 : _b.props) || {};\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      \"data-selected\": dataAttr(isRowSelected),\n      ...mergeProps(\n        gridCellProps,\n        focusProps,\n        filterDOMProps(node.props, {\n          enabled: shouldFilterDOMProps\n        }),\n        otherProps\n      ),\n      className: (_c = slots.td) == null ? void 0 : _c.call(slots, { align: columnProps.align, class: tdStyles }),\n      children: cell\n    }\n  );\n});\nTableCell.displayName = \"HeroUI.TableCell\";\nvar table_cell_default = TableCell;\n\nexport {\n  table_cell_default\n};\n"], "names": [], "mappings": ";;;AAEA,qBAAqB;AACrB;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;AAUA,IAAI,YAAY,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACjC,IAAI,IAAI,IAAI;IACZ,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;IACjF,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE;IAAK,GAAG,OAAO;IACxD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;IAC/H,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAClD,MAAM,gBAAgB,MAAM,gBAAgB,CAAC,UAAU,CAAC;IACxD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnB,MAAM,WAAW,OAAO,KAAK,QAAQ;QACrC,OAAO,aAAa,YAAY,aAAa,aAAa,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;YAAE,UAAU,KAAK,QAAQ;QAAC,KAAK,KAAK,QAAQ;IACpI,GAAG;QAAC,KAAK,QAAQ;KAAC;IAClB,MAAM,cAAc,CAAC,CAAC,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK,CAAC;IACzE,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,eACA,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE;YACzB,SAAS;QACX,IACA,WACD;QACD,WAAW,CAAC,KAAK,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO,YAAY,KAAK;YAAE,OAAO;QAAS;QACzG,UAAU;IACZ;AAEJ;AACA,UAAU,WAAW,GAAG;AACxB,IAAI,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7993, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-IAFUOJEK.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-checkbox-cell.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableCell, useTableSelectionCheckbox } from \"@react-aria/table\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { Checkbox } from \"@heroui/checkbox\";\nimport { VisuallyHidden } from \"@react-aria/visually-hidden\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TableCheckboxCell = forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n    as,\n    className,\n    node,\n    rowKey,\n    slots,\n    state,\n    color,\n    disableAnimation,\n    checkboxesProps,\n    selectionMode,\n    classNames,\n    ...otherProps\n  } = props;\n  const Component = as || \"td\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { gridCellProps } = useTableCell({ node }, state, domRef);\n  const { isFocusVisible, focusProps } = useFocusRing();\n  const { checkboxProps } = useTableSelectionCheckbox({ key: (node == null ? void 0 : node.parentKey) || node.key }, state);\n  const tdStyles = clsx(classNames == null ? void 0 : classNames.td, className, (_a = node.props) == null ? void 0 : _a.className);\n  const isSingleSelectionMode = selectionMode === \"single\";\n  const { onChange, ...otherCheckboxProps } = checkboxProps;\n  const isRowSelected = state.selectionManager.isSelected(rowKey);\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      \"data-selected\": dataAttr(isRowSelected),\n      ...mergeProps(\n        gridCellProps,\n        focusProps,\n        filterDOMProps(node.props, {\n          enabled: shouldFilterDOMProps\n        }),\n        otherProps\n      ),\n      className: (_b = slots.td) == null ? void 0 : _b.call(slots, { class: tdStyles }),\n      children: isSingleSelectionMode ? /* @__PURE__ */ jsx(VisuallyHidden, { children: checkboxProps[\"aria-label\"] }) : /* @__PURE__ */ jsx(\n        Checkbox,\n        {\n          color,\n          disableAnimation,\n          onValueChange: onChange,\n          ...mergeProps(checkboxesProps, otherCheckboxProps)\n        }\n      )\n    }\n  );\n});\nTableCheckboxCell.displayName = \"HeroUI.TableCheckboxCell\";\nvar table_checkbox_cell_default = TableCheckboxCell;\n\nexport {\n  table_checkbox_cell_default\n};\n"], "names": [], "mappings": ";;;AAEA,8BAA8B;AAC9B;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAWA,IAAI,oBAAoB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACzC,IAAI,IAAI;IACR,MAAM,EACJ,EAAE,EACF,SAAS,EACT,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,UAAU,EACV,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE;IAAK,GAAG,OAAO;IACxD,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAClD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,4BAAyB,AAAD,EAAE;QAAE,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,KAAK,GAAG;IAAC,GAAG;IACnH,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;IAC/H,MAAM,wBAAwB,kBAAkB;IAChD,MAAM,EAAE,QAAQ,EAAE,GAAG,oBAAoB,GAAG;IAC5C,MAAM,gBAAgB,MAAM,gBAAgB,CAAC,UAAU,CAAC;IACxD,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,eACA,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE;YACzB,SAAS;QACX,IACA,WACD;QACD,WAAW,CAAC,KAAK,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAS;QAC/E,UAAU,wBAAwB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gLAAA,CAAA,iBAAc,EAAE;YAAE,UAAU,aAAa,CAAC,aAAa;QAAC,KAAK,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACnI,kNAAA,CAAA,WAAQ,EACR;YACE;YACA;YACA,eAAe;YACf,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,mBAAmB;QACpD;IAEJ;AAEJ;AACA,kBAAkB,WAAW,GAAG;AAChC,IAAI,8BAA8B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8060, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-CKWQQGXG.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-row.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableRow } from \"@react-aria/table\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { useHover } from \"@react-aria/interactions\";\nimport { useMemo } from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TableRow = forwardRef((props, ref) => {\n  var _a, _b;\n  const { as, className, children, node, slots, state, isSelectable, classNames, ...otherProps } = props;\n  const Component = as || ((props == null ? void 0 : props.href) ? \"a\" : \"tr\");\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { rowProps } = useTableRow({ node }, state, domRef);\n  const trStyles = clsx(classNames == null ? void 0 : classNames.tr, className, (_a = node.props) == null ? void 0 : _a.className);\n  const { isFocusVisible, focusProps } = useFocusRing();\n  const isDisabled = state.disabledKeys.has(node.key);\n  const isSelected = state.selectionManager.isSelected(node.key);\n  const { isHovered, hoverProps } = useHover({ isDisabled });\n  const { isFirst, isLast, isMiddle, isOdd } = useMemo(() => {\n    const isFirst2 = node.key === state.collection.getFirstKey();\n    const isLast2 = node.key === state.collection.getLastKey();\n    const isMiddle2 = !isFirst2 && !isLast2;\n    const isOdd2 = (node == null ? void 0 : node.index) ? (node.index + 1) % 2 === 0 : false;\n    return {\n      isFirst: isFirst2,\n      isLast: isLast2,\n      isMiddle: isMiddle2,\n      isOdd: isOdd2\n    };\n  }, [node, state.collection]);\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      \"data-disabled\": dataAttr(isDisabled),\n      \"data-first\": dataAttr(isFirst),\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      \"data-hover\": dataAttr(isHovered),\n      \"data-last\": dataAttr(isLast),\n      \"data-middle\": dataAttr(isMiddle),\n      \"data-odd\": dataAttr(isOdd),\n      \"data-selected\": dataAttr(isSelected),\n      ...mergeProps(\n        rowProps,\n        focusProps,\n        isSelectable ? hoverProps : {},\n        filterDOMProps(node.props, {\n          enabled: shouldFilterDOMProps\n        }),\n        otherProps\n      ),\n      className: (_b = slots.tr) == null ? void 0 : _b.call(slots, { class: trStyles }),\n      children\n    }\n  );\n});\nTableRow.displayName = \"HeroUI.TableRow\";\nvar table_row_default = TableRow;\n\nexport {\n  table_row_default\n};\n"], "names": [], "mappings": ";;;AAEA,oBAAoB;AACpB;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAWA,IAAI,WAAW,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAChC,IAAI,IAAI;IACR,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;IACjG,MAAM,YAAY,MAAM,CAAC,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI;IAC3E,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAK,GAAG,OAAO;IAClD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;IAC/H,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAClD,MAAM,aAAa,MAAM,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG;IAClD,MAAM,aAAa,MAAM,gBAAgB,CAAC,UAAU,CAAC,KAAK,GAAG;IAC7D,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;QAAE;IAAW;IACxD,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnD,MAAM,WAAW,KAAK,GAAG,KAAK,MAAM,UAAU,CAAC,WAAW;QAC1D,MAAM,UAAU,KAAK,GAAG,KAAK,MAAM,UAAU,CAAC,UAAU;QACxD,MAAM,YAAY,CAAC,YAAY,CAAC;QAChC,MAAM,SAAS,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,MAAM,IAAI;QACnF,OAAO;YACL,SAAS;YACT,QAAQ;YACR,UAAU;YACV,OAAO;QACT;IACF,GAAG;QAAC;QAAM,MAAM,UAAU;KAAC;IAC3B,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,eAAe,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACxB,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrB,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,UACA,YACA,eAAe,aAAa,CAAC,GAC7B,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE;YACzB,SAAS;QACX,IACA,WACD;QACD,WAAW,CAAC,KAAK,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAS;QAC/E;IACF;AAEJ;AACA,SAAS,WAAW,GAAG;AACvB,IAAI,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8139, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-XELIGTI4.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  table_cell_default\n} from \"./chunk-LNVX26OF.mjs\";\nimport {\n  table_checkbox_cell_default\n} from \"./chunk-IAFUOJEK.mjs\";\nimport {\n  table_row_default\n} from \"./chunk-CKWQQGXG.mjs\";\n\n// src/virtualized-table-body.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableRowGroup } from \"@react-aria/table\";\nimport { filterDOMProps } from \"@heroui/react-utils\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar VirtualizedTableBody = forwardRef((props, ref) => {\n  var _a;\n  const {\n    as,\n    className,\n    slots,\n    state,\n    collection,\n    isSelectable,\n    color,\n    disableAnimation,\n    checkboxesProps,\n    selectionMode,\n    classNames,\n    rowVirtualizer,\n    ...otherProps\n  } = props;\n  const Component = as || \"tbody\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { rowGroupProps } = useTableRowGroup();\n  const tbodyStyles = clsx(classNames == null ? void 0 : classNames.tbody, className);\n  const bodyProps = collection == null ? void 0 : collection.body.props;\n  const isLoading = (bodyProps == null ? void 0 : bodyProps.isLoading) || (bodyProps == null ? void 0 : bodyProps.loadingState) === \"loading\" || (bodyProps == null ? void 0 : bodyProps.loadingState) === \"loadingMore\";\n  const items = [...collection.body.childNodes];\n  const virtualItems = rowVirtualizer.getVirtualItems();\n  let emptyContent;\n  let loadingContent;\n  if (collection.size === 0 && bodyProps.emptyContent) {\n    emptyContent = /* @__PURE__ */ jsx(\"tr\", { role: \"row\", children: /* @__PURE__ */ jsx(\n      \"td\",\n      {\n        className: slots == null ? void 0 : slots.emptyWrapper({ class: classNames == null ? void 0 : classNames.emptyWrapper }),\n        colSpan: collection.columnCount,\n        role: \"gridcell\",\n        children: !isLoading && bodyProps.emptyContent\n      }\n    ) });\n  }\n  if (isLoading && bodyProps.loadingContent) {\n    loadingContent = /* @__PURE__ */ jsxs(\"tr\", { role: \"row\", children: [\n      /* @__PURE__ */ jsx(\n        \"td\",\n        {\n          className: slots == null ? void 0 : slots.loadingWrapper({ class: classNames == null ? void 0 : classNames.loadingWrapper }),\n          colSpan: collection.columnCount,\n          role: \"gridcell\",\n          children: bodyProps.loadingContent\n        }\n      ),\n      !emptyContent && collection.size === 0 ? /* @__PURE__ */ jsx(\"td\", { className: slots == null ? void 0 : slots.emptyWrapper({ class: classNames == null ? void 0 : classNames.emptyWrapper }) }) : null\n    ] });\n  }\n  return /* @__PURE__ */ jsxs(\n    Component,\n    {\n      ref: domRef,\n      ...mergeProps(\n        rowGroupProps,\n        filterDOMProps(bodyProps, {\n          enabled: shouldFilterDOMProps\n        }),\n        otherProps\n      ),\n      className: (_a = slots.tbody) == null ? void 0 : _a.call(slots, { class: tbodyStyles }),\n      \"data-empty\": dataAttr(collection.size === 0),\n      \"data-loading\": dataAttr(isLoading),\n      children: [\n        virtualItems.map((virtualRow, index) => {\n          const row = items[virtualRow.index];\n          if (!row) {\n            return null;\n          }\n          return /* @__PURE__ */ jsx(\n            table_row_default,\n            {\n              classNames,\n              isSelectable,\n              node: row,\n              slots,\n              state,\n              style: {\n                transform: `translateY(${virtualRow.start - index * virtualRow.size}px)`,\n                height: `${virtualRow.size}px`\n              },\n              children: [...row.childNodes].map(\n                (cell) => cell.props.isSelectionCell ? /* @__PURE__ */ jsx(\n                  table_checkbox_cell_default,\n                  {\n                    checkboxesProps,\n                    classNames,\n                    color,\n                    disableAnimation,\n                    node: cell,\n                    rowKey: row.key,\n                    selectionMode,\n                    slots,\n                    state\n                  },\n                  String(cell.key)\n                ) : /* @__PURE__ */ jsx(\n                  table_cell_default,\n                  {\n                    classNames,\n                    node: cell,\n                    rowKey: row.key,\n                    slots,\n                    state\n                  },\n                  String(cell.key)\n                )\n              )\n            },\n            String(row.key)\n          );\n        }),\n        loadingContent,\n        emptyContent\n      ]\n    }\n  );\n});\nVirtualizedTableBody.displayName = \"HeroUI.VirtualizedTableBody\";\nvar virtualized_table_body_default = VirtualizedTableBody;\n\nexport {\n  virtualized_table_body_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAGA;AAIA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;AAkBA,IAAI,uBAAuB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC5C,IAAI;IACJ,MAAM,EACJ,EAAE,EACF,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,EACZ,KAAK,EACL,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,UAAU,EACV,cAAc,EACd,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE;IACzE,MAAM,YAAY,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,KAAK;IACrE,MAAM,YAAY,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,SAAS,KAAK,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,YAAY,MAAM,aAAa,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,YAAY,MAAM;IACzM,MAAM,QAAQ;WAAI,WAAW,IAAI,CAAC,UAAU;KAAC;IAC7C,MAAM,eAAe,eAAe,eAAe;IACnD,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW,IAAI,KAAK,KAAK,UAAU,YAAY,EAAE;QACnD,eAAe,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YAAE,MAAM;YAAO,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAClF,MACA;gBACE,WAAW,SAAS,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;oBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;gBAAC;gBACtH,SAAS,WAAW,WAAW;gBAC/B,MAAM;gBACN,UAAU,CAAC,aAAa,UAAU,YAAY;YAChD;QACA;IACJ;IACA,IAAI,aAAa,UAAU,cAAc,EAAE;QACzC,iBAAiB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,MAAM;YAAE,MAAM;YAAO,UAAU;gBACnE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAChB,MACA;oBACE,WAAW,SAAS,OAAO,KAAK,IAAI,MAAM,cAAc,CAAC;wBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,cAAc;oBAAC;oBAC1H,SAAS,WAAW,WAAW;oBAC/B,MAAM;oBACN,UAAU,UAAU,cAAc;gBACpC;gBAEF,CAAC,gBAAgB,WAAW,IAAI,KAAK,IAAI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;oBAAE,WAAW,SAAS,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;wBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;oBAAC;gBAAG,KAAK;aACpM;QAAC;IACJ;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACxB,WACA;QACE,KAAK;QACL,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,eACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YACxB,SAAS;QACX,IACA,WACD;QACD,WAAW,CAAC,KAAK,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAY;QACrF,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,IAAI,KAAK;QAC3C,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,UAAU;YACR,aAAa,GAAG,CAAC,CAAC,YAAY;gBAC5B,MAAM,MAAM,KAAK,CAAC,WAAW,KAAK,CAAC;gBACnC,IAAI,CAAC,KAAK;oBACR,OAAO;gBACT;gBACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,+JAAA,CAAA,oBAAiB,EACjB;oBACE;oBACA;oBACA,MAAM;oBACN;oBACA;oBACA,OAAO;wBACL,WAAW,CAAC,WAAW,EAAE,WAAW,KAAK,GAAG,QAAQ,WAAW,IAAI,CAAC,GAAG,CAAC;wBACxE,QAAQ,GAAG,WAAW,IAAI,CAAC,EAAE,CAAC;oBAChC;oBACA,UAAU;2BAAI,IAAI,UAAU;qBAAC,CAAC,GAAG,CAC/B,CAAC,OAAS,KAAK,KAAK,CAAC,eAAe,GAAG,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvD,+JAAA,CAAA,8BAA2B,EAC3B;4BACE;4BACA;4BACA;4BACA;4BACA,MAAM;4BACN,QAAQ,IAAI,GAAG;4BACf;4BACA;4BACA;wBACF,GACA,OAAO,KAAK,GAAG,KACb,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACpB,+JAAA,CAAA,qBAAkB,EAClB;4BACE;4BACA,MAAM;4BACN,QAAQ,IAAI,GAAG;4BACf;4BACA;wBACF,GACA,OAAO,KAAK,GAAG;gBAGrB,GACA,OAAO,IAAI,GAAG;YAElB;YACA;YACA;SACD;IACH;AAEJ;AACA,qBAAqB,WAAW,GAAG;AACnC,IAAI,iCAAiC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8270, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-SAU5MAVM.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-column-header.tsx\nimport { cloneElement, isValidElement } from \"react\";\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableColumnHeader } from \"@react-aria/table\";\nimport { ChevronDownIcon } from \"@heroui/shared-icons\";\nimport { useFocusRing } from \"@react-aria/focus\";\nimport { VisuallyHidden } from \"@react-aria/visually-hidden\";\nimport { useHover } from \"@react-aria/interactions\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TableColumnHeader = forwardRef((props, ref) => {\n  var _a, _b, _c, _d, _e;\n  const { as, className, state, node, slots, classNames, sortIcon, ...otherProps } = props;\n  const Component = as || \"th\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { columnHeaderProps } = useTableColumnHeader({ node }, state, domRef);\n  const thStyles = clsx(classNames == null ? void 0 : classNames.th, className, (_a = node.props) == null ? void 0 : _a.className);\n  const { isFocusVisible, focusProps } = useFocusRing();\n  const { isHovered, hoverProps } = useHover({});\n  const { hideHeader, align, ...columnProps } = node.props;\n  const allowsSorting = columnProps.allowsSorting;\n  const sortIconProps = {\n    \"aria-hidden\": true,\n    \"data-direction\": (_b = state.sortDescriptor) == null ? void 0 : _b.direction,\n    \"data-visible\": dataAttr(((_c = state.sortDescriptor) == null ? void 0 : _c.column) === node.key),\n    className: (_d = slots.sortIcon) == null ? void 0 : _d.call(slots, { class: classNames == null ? void 0 : classNames.sortIcon })\n  };\n  const customSortIcon = typeof sortIcon === \"function\" ? sortIcon(sortIconProps) : isValidElement(sortIcon) && cloneElement(sortIcon, sortIconProps);\n  return /* @__PURE__ */ jsxs(\n    Component,\n    {\n      ref: domRef,\n      colSpan: node.colspan,\n      \"data-focus-visible\": dataAttr(isFocusVisible),\n      \"data-hover\": dataAttr(isHovered),\n      \"data-sortable\": dataAttr(allowsSorting),\n      ...mergeProps(\n        columnHeaderProps,\n        focusProps,\n        filterDOMProps(columnProps, {\n          enabled: shouldFilterDOMProps\n        }),\n        allowsSorting ? hoverProps : {},\n        otherProps\n      ),\n      className: (_e = slots.th) == null ? void 0 : _e.call(slots, { align, class: thStyles }),\n      children: [\n        hideHeader ? /* @__PURE__ */ jsx(VisuallyHidden, { children: node.rendered }) : node.rendered,\n        allowsSorting && (customSortIcon || /* @__PURE__ */ jsx(ChevronDownIcon, { strokeWidth: 3, ...sortIconProps }))\n      ]\n    }\n  );\n});\nTableColumnHeader.displayName = \"HeroUI.TableColumnHeader\";\nvar table_column_header_default = TableColumnHeader;\n\nexport {\n  table_column_header_default\n};\n"], "names": [], "mappings": ";;;AAEA,8BAA8B;AAC9B;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;AAaA,IAAI,oBAAoB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACzC,IAAI,IAAI,IAAI,IAAI,IAAI;IACpB,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,YAAY,GAAG;IACnF,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,uBAAoB,AAAD,EAAE;QAAE;IAAK,GAAG,OAAO;IACpE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;IAC/H,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;IAClD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,aAAa,GAAG,KAAK,KAAK;IACxD,MAAM,gBAAgB,YAAY,aAAa;IAC/C,MAAM,gBAAgB;QACpB,eAAe;QACf,kBAAkB,CAAC,KAAK,MAAM,cAAc,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;QAC7E,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,CAAC,KAAK,MAAM,cAAc,KAAK,OAAO,KAAK,IAAI,GAAG,MAAM,MAAM,KAAK,GAAG;QAChG,WAAW,CAAC,KAAK,MAAM,QAAQ,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,QAAQ;QAAC;IAChI;IACA,MAAM,iBAAiB,OAAO,aAAa,aAAa,SAAS,iBAAiB,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,CAAA,GAAA,qMAAA,CAAA,eAAY,AAAD,EAAE,UAAU;IACrI,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACxB,WACA;QACE,KAAK;QACL,SAAS,KAAK,OAAO;QACrB,sBAAsB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,iBAAiB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,mBACA,YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;YAC1B,SAAS;QACX,IACA,gBAAgB,aAAa,CAAC,GAC9B,WACD;QACD,WAAW,CAAC,KAAK,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE;YAAO,OAAO;QAAS;QACtF,UAAU;YACR,aAAa,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gLAAA,CAAA,iBAAc,EAAE;gBAAE,UAAU,KAAK,QAAQ;YAAC,KAAK,KAAK,QAAQ;YAC7F,iBAAiB,CAAC,kBAAkB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,yKAAA,CAAA,kBAAe,EAAE;gBAAE,aAAa;gBAAG,GAAG,aAAa;YAAC,EAAE;SAC/G;IACH;AAEJ;AACA,kBAAkB,WAAW,GAAG;AAChC,IAAI,8BAA8B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8350, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-UWYZDOES.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-header-row.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef, filterDOMProps } from \"@heroui/react-utils\";\nimport { clsx, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableHeaderRow } from \"@react-aria/table\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TableHeaderRow = forwardRef((props, ref) => {\n  var _a, _b;\n  const { as, className, children, node, slots, classNames, state, ...otherProps } = props;\n  const Component = as || \"tr\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { rowProps } = useTableHeaderRow({ node }, state, domRef);\n  const trStyles = clsx(classNames == null ? void 0 : classNames.tr, className, (_a = node.props) == null ? void 0 : _a.className);\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      ...mergeProps(\n        rowProps,\n        filterDOMProps(node.props, {\n          enabled: shouldFilterDOMProps\n        }),\n        otherProps\n      ),\n      className: (_b = slots.tr) == null ? void 0 : _b.call(slots, { class: trStyles }),\n      children\n    }\n  );\n});\nTableHeaderRow.displayName = \"HeroUI.TableHeaderRow\";\nvar table_header_row_default = TableHeaderRow;\n\nexport {\n  table_header_row_default\n};\n"], "names": [], "mappings": ";;;AAEA,2BAA2B;AAC3B;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;AAQA,IAAI,iBAAiB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACtC,IAAI,IAAI;IACR,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,YAAY,GAAG;IACnF,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE;IAAK,GAAG,OAAO;IACxD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,SAAS;IAC/H,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,UACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE;YACzB,SAAS;QACX,IACA,WACD;QACD,WAAW,CAAC,KAAK,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAS;QAC/E;IACF;AAEJ;AACA,eAAe,WAAW,GAAG;AAC7B,IAAI,2BAA2B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8394, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-UHE3PMQE.mjs"], "sourcesContent": ["\"use client\";\n\n// src/table-row-group.tsx\nimport { forwardRef } from \"react\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { clsx, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableRowGroup } from \"@react-aria/table\";\nimport { jsx } from \"react/jsx-runtime\";\nvar TableRowGroup = forwardRef((props, ref) => {\n  var _a;\n  const { as, className, children, slots, classNames, ...otherProps } = props;\n  const Component = as || \"thead\";\n  const domRef = useDOMRef(ref);\n  const { rowGroupProps } = useTableRowGroup();\n  const theadStyles = clsx(classNames == null ? void 0 : classNames.thead, className);\n  return /* @__PURE__ */ jsx(\n    Component,\n    {\n      ref: domRef,\n      className: (_a = slots.thead) == null ? void 0 : _a.call(slots, { class: theadStyles }),\n      ...mergeProps(rowGroupProps, otherProps),\n      children\n    }\n  );\n});\nTableRowGroup.displayName = \"HeroUI.TableRowGroup\";\nvar table_row_group_default = TableRowGroup;\n\nexport {\n  table_row_group_default\n};\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AAPA;;;;;;AAQA,IAAI,gBAAgB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACrC,IAAI;IACJ,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;IACtE,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE;IACzE,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,WACA;QACE,KAAK;QACL,WAAW,CAAC,KAAK,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAY;QACrF,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,eAAe,WAAW;QACxC;IACF;AAEJ;AACA,cAAc,WAAW,GAAG;AAC5B,IAAI,0BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8432, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-LSK6ZGMB.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  table_select_all_checkbox_default\n} from \"./chunk-YQRLDUCT.mjs\";\nimport {\n  useTable\n} from \"./chunk-6QNXQNN7.mjs\";\nimport {\n  virtualized_table_body_default\n} from \"./chunk-XELIGTI4.mjs\";\nimport {\n  table_column_header_default\n} from \"./chunk-SAU5MAVM.mjs\";\nimport {\n  table_header_row_default\n} from \"./chunk-UWYZDOES.mjs\";\nimport {\n  table_row_group_default\n} from \"./chunk-UHE3PMQE.mjs\";\n\n// src/virtualized-table.tsx\nimport { useCallback, useLayoutEffect, useRef, useState } from \"react\";\nimport { Spacer } from \"@heroui/spacer\";\nimport { forwardRef } from \"@heroui/system\";\nimport { useVirtualizer } from \"@tanstack/react-virtual\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar VirtualizedTable = forwardRef((props, ref) => {\n  const {\n    BaseComponent,\n    Component,\n    collection,\n    values,\n    topContent,\n    topContentPlacement,\n    bottomContentPlacement,\n    bottomContent,\n    getBaseProps,\n    getWrapperProps,\n    getTableProps\n  } = useTable({\n    ...props,\n    ref\n  });\n  const { rowHeight = 40, maxTableHeight = 600 } = props;\n  const Wrapper = useCallback(\n    ({ children }) => {\n      return /* @__PURE__ */ jsx(\n        BaseComponent,\n        {\n          ...getWrapperProps(),\n          ref: parentRef,\n          style: { height: maxTableHeight, display: \"block\" },\n          children\n        }\n      );\n    },\n    [getWrapperProps, maxTableHeight]\n  );\n  const items = [...collection.body.childNodes];\n  const count = items.length;\n  const parentRef = useRef(null);\n  const [headerHeight, setHeaderHeight] = useState(0);\n  const headerRef = useRef(null);\n  useLayoutEffect(() => {\n    if (headerRef.current) {\n      setHeaderHeight(headerRef.current.getBoundingClientRect().height);\n    }\n  }, [headerRef]);\n  const rowVirtualizer = useVirtualizer({\n    count,\n    getScrollElement: () => parentRef.current,\n    estimateSize: () => rowHeight,\n    overscan: 5\n  });\n  const tableProps = getTableProps();\n  return /* @__PURE__ */ jsxs(\"div\", { ...getBaseProps(), children: [\n    topContentPlacement === \"outside\" && topContent,\n    /* @__PURE__ */ jsx(Wrapper, { children: /* @__PURE__ */ jsxs(Fragment, { children: [\n      topContentPlacement === \"inside\" && topContent,\n      /* @__PURE__ */ jsxs(\n        Component,\n        {\n          ...tableProps,\n          style: {\n            height: `calc(${rowVirtualizer.getTotalSize() + headerHeight}px)`,\n            ...tableProps.style\n          },\n          children: [\n            /* @__PURE__ */ jsxs(table_row_group_default, { ref: headerRef, classNames: values.classNames, slots: values.slots, children: [\n              collection.headerRows.map((headerRow) => /* @__PURE__ */ jsx(\n                table_header_row_default,\n                {\n                  classNames: values.classNames,\n                  node: headerRow,\n                  slots: values.slots,\n                  state: values.state,\n                  children: [...headerRow.childNodes].map(\n                    (column) => {\n                      var _a;\n                      return ((_a = column == null ? void 0 : column.props) == null ? void 0 : _a.isSelectionCell) ? /* @__PURE__ */ jsx(\n                        table_select_all_checkbox_default,\n                        {\n                          checkboxesProps: values.checkboxesProps,\n                          classNames: values.classNames,\n                          color: values.color,\n                          disableAnimation: values.disableAnimation,\n                          node: column,\n                          selectionMode: values.selectionMode,\n                          slots: values.slots,\n                          state: values.state\n                        },\n                        column == null ? void 0 : column.key\n                      ) : /* @__PURE__ */ jsx(\n                        table_column_header_default,\n                        {\n                          classNames: values.classNames,\n                          node: column,\n                          slots: values.slots,\n                          state: values.state\n                        },\n                        column == null ? void 0 : column.key\n                      );\n                    }\n                  )\n                },\n                headerRow == null ? void 0 : headerRow.key\n              )),\n              /* @__PURE__ */ jsx(Spacer, { as: \"tr\", tabIndex: -1, y: 1 })\n            ] }),\n            /* @__PURE__ */ jsx(\n              virtualized_table_body_default,\n              {\n                checkboxesProps: values.checkboxesProps,\n                classNames: values.classNames,\n                collection: values.collection,\n                color: values.color,\n                disableAnimation: values.disableAnimation,\n                isSelectable: values.isSelectable,\n                rowVirtualizer,\n                selectionMode: values.selectionMode,\n                slots: values.slots,\n                state: values.state\n              }\n            )\n          ]\n        }\n      ),\n      bottomContentPlacement === \"inside\" && bottomContent\n    ] }) }),\n    bottomContentPlacement === \"outside\" && bottomContent\n  ] });\n});\nVirtualizedTable.displayName = \"HeroUI.VirtualizedTable\";\nvar virtualized_table_default = VirtualizedTable;\n\nexport {\n  virtualized_table_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAGA;AAGA;AAGA;AAGA;AAIA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AAzBA;;;;;;;;;;;;AA0BA,IAAI,mBAAmB,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACxC,MAAM,EACJ,aAAa,EACb,SAAS,EACT,UAAU,EACV,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,sBAAsB,EACtB,aAAa,EACb,YAAY,EACZ,eAAe,EACf,aAAa,EACd,GAAG,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE;QACX,GAAG,KAAK;QACR;IACF;IACA,MAAM,EAAE,YAAY,EAAE,EAAE,iBAAiB,GAAG,EAAE,GAAG;IACjD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,CAAC,EAAE,QAAQ,EAAE;QACX,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,eACA;YACE,GAAG,iBAAiB;YACpB,KAAK;YACL,OAAO;gBAAE,QAAQ;gBAAgB,SAAS;YAAQ;YAClD;QACF;IAEJ,GACA;QAAC;QAAiB;KAAe;IAEnC,MAAM,QAAQ;WAAI,WAAW,IAAI,CAAC,UAAU;KAAC;IAC7C,MAAM,QAAQ,MAAM,MAAM;IAC1B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,IAAI,UAAU,OAAO,EAAE;YACrB,gBAAgB,UAAU,OAAO,CAAC,qBAAqB,GAAG,MAAM;QAClE;IACF,GAAG;QAAC;KAAU;IACd,MAAM,iBAAiB,CAAA,GAAA,2NAAA,CAAA,iBAAc,AAAD,EAAE;QACpC;QACA,kBAAkB,IAAM,UAAU,OAAO;QACzC,cAAc,IAAM;QACpB,UAAU;IACZ;IACA,MAAM,aAAa;IACnB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,GAAG,cAAc;QAAE,UAAU;YAChE,wBAAwB,aAAa;YACrC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,SAAS;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;oBAAE,UAAU;wBAClF,wBAAwB,YAAY;wBACpC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACjB,WACA;4BACE,GAAG,UAAU;4BACb,OAAO;gCACL,QAAQ,CAAC,KAAK,EAAE,eAAe,YAAY,KAAK,aAAa,GAAG,CAAC;gCACjE,GAAG,WAAW,KAAK;4BACrB;4BACA,UAAU;gCACR,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,+JAAA,CAAA,0BAAuB,EAAE;oCAAE,KAAK;oCAAW,YAAY,OAAO,UAAU;oCAAE,OAAO,OAAO,KAAK;oCAAE,UAAU;wCAC5H,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,YAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACzD,+JAAA,CAAA,2BAAwB,EACxB;gDACE,YAAY,OAAO,UAAU;gDAC7B,MAAM;gDACN,OAAO,OAAO,KAAK;gDACnB,OAAO,OAAO,KAAK;gDACnB,UAAU;uDAAI,UAAU,UAAU;iDAAC,CAAC,GAAG,CACrC,CAAC;oDACC,IAAI;oDACJ,OAAO,CAAC,CAAC,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,eAAe,IAAI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC/G,+JAAA,CAAA,oCAAiC,EACjC;wDACE,iBAAiB,OAAO,eAAe;wDACvC,YAAY,OAAO,UAAU;wDAC7B,OAAO,OAAO,KAAK;wDACnB,kBAAkB,OAAO,gBAAgB;wDACzC,MAAM;wDACN,eAAe,OAAO,aAAa;wDACnC,OAAO,OAAO,KAAK;wDACnB,OAAO,OAAO,KAAK;oDACrB,GACA,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG,IAClC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACpB,+JAAA,CAAA,8BAA2B,EAC3B;wDACE,YAAY,OAAO,UAAU;wDAC7B,MAAM;wDACN,OAAO,OAAO,KAAK;wDACnB,OAAO,OAAO,KAAK;oDACrB,GACA,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG;gDAExC;4CAEJ,GACA,aAAa,OAAO,KAAK,IAAI,UAAU,GAAG;wCAE5C,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,4MAAA,CAAA,SAAM,EAAE;4CAAE,IAAI;4CAAM,UAAU,CAAC;4CAAG,GAAG;wCAAE;qCAC5D;gCAAC;gCACF,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAChB,+JAAA,CAAA,iCAA8B,EAC9B;oCACE,iBAAiB,OAAO,eAAe;oCACvC,YAAY,OAAO,UAAU;oCAC7B,YAAY,OAAO,UAAU;oCAC7B,OAAO,OAAO,KAAK;oCACnB,kBAAkB,OAAO,gBAAgB;oCACzC,cAAc,OAAO,YAAY;oCACjC;oCACA,eAAe,OAAO,aAAa;oCACnC,OAAO,OAAO,KAAK;oCACnB,OAAO,OAAO,KAAK;gCACrB;6BAEH;wBACH;wBAEF,2BAA2B,YAAY;qBACxC;gBAAC;YAAG;YACL,2BAA2B,aAAa;SACzC;IAAC;AACJ;AACA,iBAAiB,WAAW,GAAG;AAC/B,IAAI,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-SU3P7OYB.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  table_cell_default\n} from \"./chunk-LNVX26OF.mjs\";\nimport {\n  table_checkbox_cell_default\n} from \"./chunk-IAFUOJEK.mjs\";\nimport {\n  table_row_default\n} from \"./chunk-CKWQQGXG.mjs\";\n\n// src/table-body.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { clsx, dataAttr, mergeProps } from \"@heroui/shared-utils\";\nimport { useTableRowGroup } from \"@react-aria/table\";\nimport { filterDOMProps } from \"@heroui/react-utils\";\nimport { useMemo } from \"react\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TableBody = forwardRef((props, ref) => {\n  var _a;\n  const {\n    as,\n    className,\n    slots,\n    state,\n    collection,\n    isSelectable,\n    color,\n    disableAnimation,\n    checkboxesProps,\n    selectionMode,\n    classNames,\n    ...otherProps\n  } = props;\n  const Component = as || \"tbody\";\n  const shouldFilterDOMProps = typeof Component === \"string\";\n  const domRef = useDOMRef(ref);\n  const { rowGroupProps } = useTableRowGroup();\n  const tbodyStyles = clsx(classNames == null ? void 0 : classNames.tbody, className);\n  const bodyProps = collection == null ? void 0 : collection.body.props;\n  const isLoading = (bodyProps == null ? void 0 : bodyProps.isLoading) || (bodyProps == null ? void 0 : bodyProps.loadingState) === \"loading\" || (bodyProps == null ? void 0 : bodyProps.loadingState) === \"loadingMore\";\n  const renderRows = useMemo(() => {\n    return [...collection.body.childNodes].map((row) => /* @__PURE__ */ jsx(\n      table_row_default,\n      {\n        classNames,\n        isSelectable,\n        node: row,\n        slots,\n        state,\n        children: [...row.childNodes].map(\n          (cell) => cell.props.isSelectionCell ? /* @__PURE__ */ jsx(\n            table_checkbox_cell_default,\n            {\n              checkboxesProps,\n              classNames,\n              color,\n              disableAnimation,\n              node: cell,\n              rowKey: row.key,\n              selectionMode,\n              slots,\n              state\n            },\n            cell.key\n          ) : /* @__PURE__ */ jsx(\n            table_cell_default,\n            {\n              classNames,\n              node: cell,\n              rowKey: row.key,\n              slots,\n              state\n            },\n            cell.key\n          )\n        )\n      },\n      row.key\n    ));\n  }, [collection.body.childNodes, classNames, isSelectable, slots, state]);\n  let emptyContent;\n  let loadingContent;\n  if (collection.size === 0 && bodyProps.emptyContent) {\n    emptyContent = /* @__PURE__ */ jsx(\"tr\", { role: \"row\", children: /* @__PURE__ */ jsx(\n      \"td\",\n      {\n        className: slots == null ? void 0 : slots.emptyWrapper({ class: classNames == null ? void 0 : classNames.emptyWrapper }),\n        colSpan: collection.columnCount,\n        role: \"gridcell\",\n        children: !isLoading && bodyProps.emptyContent\n      }\n    ) });\n  }\n  if (isLoading && bodyProps.loadingContent) {\n    loadingContent = /* @__PURE__ */ jsxs(\"tr\", { role: \"row\", children: [\n      /* @__PURE__ */ jsx(\n        \"td\",\n        {\n          className: slots == null ? void 0 : slots.loadingWrapper({ class: classNames == null ? void 0 : classNames.loadingWrapper }),\n          colSpan: collection.columnCount,\n          role: \"gridcell\",\n          children: bodyProps.loadingContent\n        }\n      ),\n      !emptyContent && collection.size === 0 ? /* @__PURE__ */ jsx(\"td\", { className: slots == null ? void 0 : slots.emptyWrapper({ class: classNames == null ? void 0 : classNames.emptyWrapper }) }) : null\n    ] });\n  }\n  return /* @__PURE__ */ jsxs(\n    Component,\n    {\n      ref: domRef,\n      ...mergeProps(\n        rowGroupProps,\n        filterDOMProps(bodyProps, {\n          enabled: shouldFilterDOMProps\n        }),\n        otherProps\n      ),\n      className: (_a = slots.tbody) == null ? void 0 : _a.call(slots, { class: tbodyStyles }),\n      \"data-empty\": dataAttr(collection.size === 0),\n      \"data-loading\": dataAttr(isLoading),\n      children: [\n        renderRows,\n        loadingContent,\n        emptyContent\n      ]\n    }\n  );\n});\nTableBody.displayName = \"HeroUI.TableBody\";\nvar table_body_default = TableBody;\n\nexport {\n  table_body_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAGA;AAIA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;AAmBA,IAAI,YAAY,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACjC,IAAI;IACJ,MAAM,EACJ,EAAE,EACF,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,YAAY,EACZ,KAAK,EACL,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,UAAU,EACV,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,uBAAuB,OAAO,cAAc;IAClD,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE;IACzE,MAAM,YAAY,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,KAAK;IACrE,MAAM,YAAY,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,SAAS,KAAK,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,YAAY,MAAM,aAAa,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,YAAY,MAAM;IACzM,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO;eAAI,WAAW,IAAI,CAAC,UAAU;SAAC,CAAC,GAAG,CAAC,CAAC,MAAQ,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACpE,+JAAA,CAAA,oBAAiB,EACjB;gBACE;gBACA;gBACA,MAAM;gBACN;gBACA;gBACA,UAAU;uBAAI,IAAI,UAAU;iBAAC,CAAC,GAAG,CAC/B,CAAC,OAAS,KAAK,KAAK,CAAC,eAAe,GAAG,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvD,+JAAA,CAAA,8BAA2B,EAC3B;wBACE;wBACA;wBACA;wBACA;wBACA,MAAM;wBACN,QAAQ,IAAI,GAAG;wBACf;wBACA;wBACA;oBACF,GACA,KAAK,GAAG,IACN,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACpB,+JAAA,CAAA,qBAAkB,EAClB;wBACE;wBACA,MAAM;wBACN,QAAQ,IAAI,GAAG;wBACf;wBACA;oBACF,GACA,KAAK,GAAG;YAGd,GACA,IAAI,GAAG;IAEX,GAAG;QAAC,WAAW,IAAI,CAAC,UAAU;QAAE;QAAY;QAAc;QAAO;KAAM;IACvE,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW,IAAI,KAAK,KAAK,UAAU,YAAY,EAAE;QACnD,eAAe,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YAAE,MAAM;YAAO,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAClF,MACA;gBACE,WAAW,SAAS,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;oBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;gBAAC;gBACtH,SAAS,WAAW,WAAW;gBAC/B,MAAM;gBACN,UAAU,CAAC,aAAa,UAAU,YAAY;YAChD;QACA;IACJ;IACA,IAAI,aAAa,UAAU,cAAc,EAAE;QACzC,iBAAiB,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,MAAM;YAAE,MAAM;YAAO,UAAU;gBACnE,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAChB,MACA;oBACE,WAAW,SAAS,OAAO,KAAK,IAAI,MAAM,cAAc,CAAC;wBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,cAAc;oBAAC;oBAC1H,SAAS,WAAW,WAAW;oBAC/B,MAAM;oBACN,UAAU,UAAU,cAAc;gBACpC;gBAEF,CAAC,gBAAgB,WAAW,IAAI,KAAK,IAAI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,MAAM;oBAAE,WAAW,SAAS,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;wBAAE,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY;oBAAC;gBAAG,KAAK;aACpM;QAAC;IACJ;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EACxB,WACA;QACE,KAAK;QACL,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EACV,eACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YACxB,SAAS;QACX,IACA,WACD;QACD,WAAW,CAAC,KAAK,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,OAAO;YAAE,OAAO;QAAY;QACrF,cAAc,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,IAAI,KAAK;QAC3C,gBAAgB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,UAAU;YACR;YACA;YACA;SACD;IACH;AAEJ;AACA,UAAU,WAAW,GAAG;AACxB,IAAI,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8712, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-S6VICFMP.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  virtualized_table_default\n} from \"./chunk-LSK6ZGMB.mjs\";\nimport {\n  table_select_all_checkbox_default\n} from \"./chunk-YQRLDUCT.mjs\";\nimport {\n  useTable\n} from \"./chunk-6QNXQNN7.mjs\";\nimport {\n  table_body_default\n} from \"./chunk-SU3P7OYB.mjs\";\nimport {\n  table_column_header_default\n} from \"./chunk-SAU5MAVM.mjs\";\nimport {\n  table_header_row_default\n} from \"./chunk-UWYZDOES.mjs\";\nimport {\n  table_row_group_default\n} from \"./chunk-UHE3PMQE.mjs\";\n\n// src/table.tsx\nimport { useCallback } from \"react\";\nimport { Spacer } from \"@heroui/spacer\";\nimport { forwardRef } from \"@heroui/system\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar Table = forwardRef((props, ref) => {\n  const {\n    BaseComponent,\n    Component,\n    collection,\n    values,\n    topContent,\n    topContentPlacement,\n    bottomContentPlacement,\n    bottomContent,\n    removeWrapper,\n    sortIcon,\n    getBaseProps,\n    getWrapperProps,\n    getTableProps\n  } = useTable({\n    ...props,\n    ref\n  });\n  const { isVirtualized, rowHeight = 40, maxTableHeight = 600 } = props;\n  const shouldVirtualize = isVirtualized;\n  const Wrapper = useCallback(\n    ({ children }) => {\n      if (removeWrapper) {\n        return children;\n      }\n      return /* @__PURE__ */ jsx(BaseComponent, { ...getWrapperProps(), children });\n    },\n    [removeWrapper, getWrapperProps]\n  );\n  if (shouldVirtualize) {\n    return /* @__PURE__ */ jsx(\n      virtualized_table_default,\n      {\n        ...props,\n        ref,\n        maxTableHeight,\n        rowHeight\n      }\n    );\n  }\n  return /* @__PURE__ */ jsxs(\"div\", { ...getBaseProps(), children: [\n    topContentPlacement === \"outside\" && topContent,\n    /* @__PURE__ */ jsx(Wrapper, { children: /* @__PURE__ */ jsxs(Fragment, { children: [\n      topContentPlacement === \"inside\" && topContent,\n      /* @__PURE__ */ jsxs(Component, { ...getTableProps(), children: [\n        /* @__PURE__ */ jsxs(table_row_group_default, { classNames: values.classNames, slots: values.slots, children: [\n          collection.headerRows.map((headerRow) => /* @__PURE__ */ jsx(\n            table_header_row_default,\n            {\n              classNames: values.classNames,\n              node: headerRow,\n              slots: values.slots,\n              state: values.state,\n              children: [...headerRow.childNodes].map(\n                (column) => {\n                  var _a;\n                  return ((_a = column == null ? void 0 : column.props) == null ? void 0 : _a.isSelectionCell) ? /* @__PURE__ */ jsx(\n                    table_select_all_checkbox_default,\n                    {\n                      checkboxesProps: values.checkboxesProps,\n                      classNames: values.classNames,\n                      color: values.color,\n                      disableAnimation: values.disableAnimation,\n                      node: column,\n                      selectionMode: values.selectionMode,\n                      slots: values.slots,\n                      state: values.state\n                    },\n                    column == null ? void 0 : column.key\n                  ) : /* @__PURE__ */ jsx(\n                    table_column_header_default,\n                    {\n                      classNames: values.classNames,\n                      node: column,\n                      slots: values.slots,\n                      sortIcon,\n                      state: values.state\n                    },\n                    column == null ? void 0 : column.key\n                  );\n                }\n              )\n            },\n            headerRow == null ? void 0 : headerRow.key\n          )),\n          /* @__PURE__ */ jsx(Spacer, { as: \"tr\", tabIndex: -1, y: 1 })\n        ] }),\n        /* @__PURE__ */ jsx(\n          table_body_default,\n          {\n            checkboxesProps: values.checkboxesProps,\n            classNames: values.classNames,\n            collection: values.collection,\n            color: values.color,\n            disableAnimation: values.disableAnimation,\n            isSelectable: values.isSelectable,\n            selectionMode: values.selectionMode,\n            slots: values.slots,\n            state: values.state\n          }\n        )\n      ] }),\n      bottomContentPlacement === \"inside\" && bottomContent\n    ] }) }),\n    bottomContentPlacement === \"outside\" && bottomContent\n  ] });\n});\nTable.displayName = \"HeroUI.Table\";\nvar table_default = Table;\n\nexport {\n  table_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAIA,gBAAgB;AAChB;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;AA4BA,IAAI,QAAQ,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC7B,MAAM,EACJ,aAAa,EACb,SAAS,EACT,UAAU,EACV,MAAM,EACN,UAAU,EACV,mBAAmB,EACnB,sBAAsB,EACtB,aAAa,EACb,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,aAAa,EACd,GAAG,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE;QACX,GAAG,KAAK;QACR;IACF;IACA,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,EAAE,iBAAiB,GAAG,EAAE,GAAG;IAChE,MAAM,mBAAmB;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,CAAC,EAAE,QAAQ,EAAE;QACX,IAAI,eAAe;YACjB,OAAO;QACT;QACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;YAAE,GAAG,iBAAiB;YAAE;QAAS;IAC7E,GACA;QAAC;QAAe;KAAgB;IAElC,IAAI,kBAAkB;QACpB,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACvB,+JAAA,CAAA,4BAAyB,EACzB;YACE,GAAG,KAAK;YACR;YACA;YACA;QACF;IAEJ;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAE,GAAG,cAAc;QAAE,UAAU;YAChE,wBAAwB,aAAa;YACrC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,SAAS;gBAAE,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;oBAAE,UAAU;wBAClF,wBAAwB,YAAY;wBACpC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,WAAW;4BAAE,GAAG,eAAe;4BAAE,UAAU;gCAC9D,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,+JAAA,CAAA,0BAAuB,EAAE;oCAAE,YAAY,OAAO,UAAU;oCAAE,OAAO,OAAO,KAAK;oCAAE,UAAU;wCAC5G,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,YAAc,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACzD,+JAAA,CAAA,2BAAwB,EACxB;gDACE,YAAY,OAAO,UAAU;gDAC7B,MAAM;gDACN,OAAO,OAAO,KAAK;gDACnB,OAAO,OAAO,KAAK;gDACnB,UAAU;uDAAI,UAAU,UAAU;iDAAC,CAAC,GAAG,CACrC,CAAC;oDACC,IAAI;oDACJ,OAAO,CAAC,CAAC,KAAK,UAAU,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,GAAG,eAAe,IAAI,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAC/G,+JAAA,CAAA,oCAAiC,EACjC;wDACE,iBAAiB,OAAO,eAAe;wDACvC,YAAY,OAAO,UAAU;wDAC7B,OAAO,OAAO,KAAK;wDACnB,kBAAkB,OAAO,gBAAgB;wDACzC,MAAM;wDACN,eAAe,OAAO,aAAa;wDACnC,OAAO,OAAO,KAAK;wDACnB,OAAO,OAAO,KAAK;oDACrB,GACA,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG,IAClC,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EACpB,+JAAA,CAAA,8BAA2B,EAC3B;wDACE,YAAY,OAAO,UAAU;wDAC7B,MAAM;wDACN,OAAO,OAAO,KAAK;wDACnB;wDACA,OAAO,OAAO,KAAK;oDACrB,GACA,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG;gDAExC;4CAEJ,GACA,aAAa,OAAO,KAAK,IAAI,UAAU,GAAG;wCAE5C,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,4MAAA,CAAA,SAAM,EAAE;4CAAE,IAAI;4CAAM,UAAU,CAAC;4CAAG,GAAG;wCAAE;qCAC5D;gCAAC;gCACF,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAChB,+JAAA,CAAA,qBAAkB,EAClB;oCACE,iBAAiB,OAAO,eAAe;oCACvC,YAAY,OAAO,UAAU;oCAC7B,YAAY,OAAO,UAAU;oCAC7B,OAAO,OAAO,KAAK;oCACnB,kBAAkB,OAAO,gBAAgB;oCACzC,cAAc,OAAO,YAAY;oCACjC,eAAe,OAAO,aAAa;oCACnC,OAAO,OAAO,KAAK;oCACnB,OAAO,OAAO,KAAK;gCACrB;6BAEH;wBAAC;wBACF,2BAA2B,YAAY;qBACxC;gBAAC;YAAG;YACL,2BAA2B,aAAa;SACzC;IAAC;AACJ;AACA,MAAM,WAAW,GAAG;AACpB,IAAI,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8851, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-YRZGWF2W.mjs"], "sourcesContent": ["\"use client\";\n\n// src/base/table-header.tsx\nimport { TableHeader as TableHeaderBase } from \"@react-stately/table\";\nvar TableHeader = TableHeaderBase;\nvar table_header_default = TableHeader;\n\nexport {\n  table_header_default\n};\n"], "names": [], "mappings": ";;;AAEA,4BAA4B;AAC5B;AAHA;;AAIA,IAAI,cAAc,mKAAA,CAAA,cAAe;AACjC,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-TSPNSPCL.mjs"], "sourcesContent": ["\"use client\";\n\n// src/base/table-column.tsx\nimport { Column } from \"@react-stately/table\";\nvar TableColumn = Column;\nvar table_column_default = TableColumn;\n\nexport {\n  table_column_default\n};\n"], "names": [], "mappings": ";;;AAEA,4BAA4B;AAC5B;AAHA;;AAIA,IAAI,cAAc,8JAAA,CAAA,SAAM;AACxB,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8895, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-FKPXBCGS.mjs"], "sourcesContent": ["\"use client\";\n\n// src/base/table-body.tsx\nimport { TableBody as TableBodyBase } from \"@react-stately/table\";\nvar TableBody = TableBodyBase;\nvar table_body_default = TableBody;\n\nexport {\n  table_body_default\n};\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B;AAHA;;AAIA,IAAI,YAAY,iKAAA,CAAA,YAAa;AAC7B,IAAI,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8917, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-CIL4Y7FA.mjs"], "sourcesContent": ["\"use client\";\n\n// src/base/table-row.tsx\nimport { Row } from \"@react-stately/table\";\nvar TableRow = Row;\nvar table_row_default = TableRow;\n\nexport {\n  table_row_default\n};\n"], "names": [], "mappings": ";;;AAEA,yBAAyB;AACzB;AAHA;;AAIA,IAAI,WAAW,2JAAA,CAAA,MAAG;AAClB,IAAI,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8939, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/table/dist/chunk-F3UDT23P.mjs"], "sourcesContent": ["\"use client\";\n\n// src/base/table-cell.tsx\nimport { Cell } from \"@react-stately/table\";\nvar TableCell = Cell;\nvar table_cell_default = TableCell;\n\nexport {\n  table_cell_default\n};\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B;AAHA;;AAIA,IAAI,YAAY,4JAAA,CAAA,OAAI;AACpB,IAAI,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8961, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/spacer/dist/chunk-CH7FQ62Q.mjs"], "sourcesContent": ["// src/utils.ts\nvar spacing = {\n  px: \"1px\",\n  0: \"0px\",\n  0.5: \"0.125rem\",\n  1: \"0.25rem\",\n  1.5: \"0.375rem\",\n  2: \"0.5rem\",\n  2.5: \"0.625rem\",\n  3: \"0.75rem\",\n  3.5: \"0.875rem\",\n  4: \"1rem\",\n  5: \"1.25rem\",\n  6: \"1.5rem\",\n  7: \"1.75rem\",\n  8: \"2rem\",\n  9: \"2.25rem\",\n  10: \"2.5rem\",\n  11: \"2.75rem\",\n  12: \"3rem\",\n  14: \"3.5rem\",\n  16: \"4rem\",\n  20: \"5rem\",\n  24: \"6rem\",\n  28: \"7rem\",\n  32: \"8rem\",\n  36: \"9rem\",\n  40: \"10rem\",\n  44: \"11rem\",\n  48: \"12rem\",\n  52: \"13rem\",\n  56: \"14rem\",\n  60: \"15rem\",\n  64: \"16rem\",\n  72: \"18rem\",\n  80: \"20rem\",\n  96: \"24rem\"\n};\n\nexport {\n  spacing\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf,IAAI,UAAU;IACZ,IAAI;IACJ,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9007, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/spacer/dist/chunk-BRFD4NXM.mjs"], "sourcesContent": ["import {\n  spacing\n} from \"./chunk-CH7FQ62Q.mjs\";\n\n// src/use-spacer.ts\nimport { mapPropsVariants } from \"@heroui/system-rsc\";\nimport { spacer } from \"@heroui/theme\";\nimport { clsx, dataAttr, objectToDeps } from \"@heroui/shared-utils\";\nimport { useMemo } from \"react\";\nvar getMargin = (value) => {\n  var _a;\n  return (_a = spacing[value]) != null ? _a : value;\n};\nfunction useSpacer(originalProps) {\n  const [props, variantProps] = mapPropsVariants(originalProps, spacer.variantKeys);\n  const { as, className, x = 1, y = 1, ...otherProps } = props;\n  const Component = as || \"span\";\n  const styles = useMemo(\n    () => spacer({\n      ...variantProps,\n      className\n    }),\n    [objectToDeps(variantProps), className]\n  );\n  const marginLeft = getMargin(x);\n  const marginTop = getMargin(y);\n  const getSpacerProps = (props2 = {}) => ({\n    ...props2,\n    ...otherProps,\n    \"aria-hidden\": dataAttr(true),\n    className: clsx(styles, props2.className),\n    style: {\n      ...props2.style,\n      ...otherProps.style,\n      marginLeft,\n      marginTop\n    }\n  });\n  return { Component, getSpacerProps };\n}\n\nexport {\n  getMargin,\n  useSpacer\n};\n"], "names": [], "mappings": ";;;;AAAA;AAIA,oBAAoB;AACpB;AACA;AACA;AACA;;;;;;AACA,IAAI,YAAY,CAAC;IACf,IAAI;IACJ,OAAO,CAAC,KAAK,gKAAA,CAAA,UAAO,CAAC,MAAM,KAAK,OAAO,KAAK;AAC9C;AACA,SAAS,UAAU,aAAa;IAC9B,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,SAAM,CAAC,WAAW;IAChF,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,YAAY,GAAG;IACvD,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACnB,IAAM,CAAA,GAAA,+JAAA,CAAA,SAAM,AAAD,EAAE;YACX,GAAG,YAAY;YACf;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;KAAU;IAEzC,MAAM,aAAa,UAAU;IAC7B,MAAM,YAAY,UAAU;IAC5B,MAAM,iBAAiB,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;YACvC,GAAG,MAAM;YACT,GAAG,UAAU;YACb,eAAe,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YACxB,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO,SAAS;YACxC,OAAO;gBACL,GAAG,OAAO,KAAK;gBACf,GAAG,WAAW,KAAK;gBACnB;gBACA;YACF;QACF,CAAC;IACD,OAAO;QAAE;QAAW;IAAe;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9061, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/spacer/dist/chunk-AWMMSYR4.mjs"], "sourcesContent": ["import {\n  useSpacer\n} from \"./chunk-BRFD4NXM.mjs\";\n\n// src/spacer.tsx\nimport { forwardRef } from \"@heroui/system-rsc\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Spacer = forwardRef((props, ref) => {\n  const { Component, getSpacerProps } = useSpacer({ ...props });\n  return /* @__PURE__ */ jsx(Component, { ref, ...getSpacerProps() });\n});\nSpacer.displayName = \"HeroUI.Spacer\";\nvar spacer_default = Spacer;\n\nexport {\n  spacer_default\n};\n"], "names": [], "mappings": ";;;AAAA;AAIA,iBAAiB;AACjB;AACA;;;;AACA,IAAI,SAAS,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC9B,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE;QAAE,GAAG,KAAK;IAAC;IAC3D,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW;QAAE;QAAK,GAAG,gBAAgB;IAAC;AACnE;AACA,OAAO,WAAW,GAAG;AACrB,IAAI,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9095, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/use-infinite-scroll/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { useLayoutEffect, useRef, useCallback } from \"react\";\nimport { debounce } from \"@heroui/shared-utils\";\nfunction useInfiniteScroll(props = {}) {\n  const {\n    hasMore = true,\n    distance = 250,\n    isEnabled = true,\n    shouldUseLoader = true,\n    onLoadMore\n  } = props;\n  const scrollContainerRef = useRef(null);\n  const loaderRef = useRef(null);\n  const observerRef = useRef(null);\n  const isLoadingRef = useRef(false);\n  const loadMore = useCallback(() => {\n    let timer;\n    if (!isLoadingRef.current && hasMore && onLoadMore) {\n      isLoadingRef.current = true;\n      onLoadMore();\n      timer = setTimeout(() => {\n        isLoadingRef.current = false;\n      }, 100);\n    }\n    return () => clearTimeout(timer);\n  }, [hasMore, onLoadMore]);\n  useLayoutEffect(() => {\n    const scrollContainerNode = scrollContainerRef.current;\n    if (!isEnabled || !scrollContainerNode || !hasMore) return;\n    if (shouldUseLoader) {\n      const loaderNode = loaderRef.current;\n      if (!loaderNode) return;\n      const options = {\n        root: scrollContainerNode,\n        rootMargin: `0px 0px ${distance}px 0px`,\n        threshold: 0.1\n      };\n      const observer = new IntersectionObserver((entries) => {\n        const [entry] = entries;\n        if (entry.isIntersecting) {\n          loadMore();\n        }\n      }, options);\n      observer.observe(loaderNode);\n      observerRef.current = observer;\n      return () => {\n        if (observerRef.current) {\n          observerRef.current.disconnect();\n        }\n      };\n    }\n    const debouncedCheckIfNearBottom = debounce(() => {\n      if (scrollContainerNode.scrollHeight - scrollContainerNode.scrollTop <= scrollContainerNode.clientHeight + distance) {\n        loadMore();\n      }\n    }, 100);\n    scrollContainerNode.addEventListener(\"scroll\", debouncedCheckIfNearBottom);\n    return () => {\n      scrollContainerNode.removeEventListener(\"scroll\", debouncedCheckIfNearBottom);\n    };\n  }, [hasMore, distance, isEnabled, shouldUseLoader, loadMore]);\n  return [loaderRef, scrollContainerRef];\n}\nexport {\n  useInfiniteScroll\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf;AACA;;;AACA,SAAS,kBAAkB,QAAQ,CAAC,CAAC;IACnC,MAAM,EACJ,UAAU,IAAI,EACd,WAAW,GAAG,EACd,YAAY,IAAI,EAChB,kBAAkB,IAAI,EACtB,UAAU,EACX,GAAG;IACJ,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,IAAI;QACJ,IAAI,CAAC,aAAa,OAAO,IAAI,WAAW,YAAY;YAClD,aAAa,OAAO,GAAG;YACvB;YACA,QAAQ,WAAW;gBACjB,aAAa,OAAO,GAAG;YACzB,GAAG;QACL;QACA,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAS;KAAW;IACxB,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,MAAM,sBAAsB,mBAAmB,OAAO;QACtD,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,SAAS;QACpD,IAAI,iBAAiB;YACnB,MAAM,aAAa,UAAU,OAAO;YACpC,IAAI,CAAC,YAAY;YACjB,MAAM,UAAU;gBACd,MAAM;gBACN,YAAY,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC;gBACvC,WAAW;YACb;YACA,MAAM,WAAW,IAAI,qBAAqB,CAAC;gBACzC,MAAM,CAAC,MAAM,GAAG;gBAChB,IAAI,MAAM,cAAc,EAAE;oBACxB;gBACF;YACF,GAAG;YACH,SAAS,OAAO,CAAC;YACjB,YAAY,OAAO,GAAG;YACtB,OAAO;gBACL,IAAI,YAAY,OAAO,EAAE;oBACvB,YAAY,OAAO,CAAC,UAAU;gBAChC;YACF;QACF;QACA,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAC1C,IAAI,oBAAoB,YAAY,GAAG,oBAAoB,SAAS,IAAI,oBAAoB,YAAY,GAAG,UAAU;gBACnH;YACF;QACF,GAAG;QACH,oBAAoB,gBAAgB,CAAC,UAAU;QAC/C,OAAO;YACL,oBAAoB,mBAAmB,CAAC,UAAU;QACpD;IACF,GAAG;QAAC;QAAS;QAAU;QAAW;QAAiB;KAAS;IAC5D,OAAO;QAAC;QAAW;KAAmB;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/button/dist/chunk-6XRBX2TW.mjs"], "sourcesContent": ["\"use client\";\n\n// src/use-button-group.ts\nimport { buttonGroup } from \"@heroui/theme\";\nimport { mapPropsVariants, useProviderContext } from \"@heroui/system\";\nimport { useDOMRef } from \"@heroui/react-utils\";\nimport { useMemo, useCallback } from \"react\";\nimport { objectToDeps } from \"@heroui/shared-utils\";\nfunction useButtonGroup(originalProps) {\n  var _a, _b;\n  const globalContext = useProviderContext();\n  const [props, variantProps] = mapPropsVariants(originalProps, buttonGroup.variantKeys);\n  const {\n    ref,\n    as,\n    children,\n    color = \"default\",\n    size = \"md\",\n    variant = \"solid\",\n    radius,\n    isDisabled = false,\n    isIconOnly = false,\n    disableRipple = (_a = globalContext == null ? void 0 : globalContext.disableRipple) != null ? _a : false,\n    disableAnimation = (_b = globalContext == null ? void 0 : globalContext.disableAnimation) != null ? _b : false,\n    className,\n    ...otherProps\n  } = props;\n  const Component = as || \"div\";\n  const domRef = useDOMRef(ref);\n  const classNames = useMemo(\n    () => buttonGroup({\n      ...variantProps,\n      className\n    }),\n    [objectToDeps(variantProps), className]\n  );\n  const context = useMemo(\n    () => ({\n      size,\n      color,\n      variant,\n      radius,\n      isIconOnly,\n      isDisabled,\n      disableAnimation,\n      disableRipple,\n      fullWidth: !!(originalProps == null ? void 0 : originalProps.fullWidth)\n    }),\n    [\n      size,\n      color,\n      variant,\n      radius,\n      isDisabled,\n      isIconOnly,\n      disableAnimation,\n      disableRipple,\n      originalProps == null ? void 0 : originalProps.fullWidth\n    ]\n  );\n  const getButtonGroupProps = useCallback(\n    () => ({\n      role: \"group\",\n      ...otherProps\n    }),\n    [otherProps]\n  );\n  return {\n    Component,\n    children,\n    domRef,\n    context,\n    classNames,\n    getButtonGroupProps\n  };\n}\n\nexport {\n  useButtonGroup\n};\n"], "names": [], "mappings": ";;;AAEA,0BAA0B;AAC1B;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;AAQA,SAAS,eAAe,aAAa;IACnC,IAAI,IAAI;IACR,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,qBAAkB,AAAD;IACvC,MAAM,CAAC,OAAO,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,+JAAA,CAAA,cAAW,CAAC,WAAW;IACrF,MAAM,EACJ,GAAG,EACH,EAAE,EACF,QAAQ,EACR,QAAQ,SAAS,EACjB,OAAO,IAAI,EACX,UAAU,OAAO,EACjB,MAAM,EACN,aAAa,KAAK,EAClB,aAAa,KAAK,EAClB,gBAAgB,CAAC,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,aAAa,KAAK,OAAO,KAAK,KAAK,EACxG,mBAAmB,CAAC,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,gBAAgB,KAAK,OAAO,KAAK,KAAK,EAC9G,SAAS,EACT,GAAG,YACJ,GAAG;IACJ,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACvB,IAAM,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE;YAChB,GAAG,YAAY;YACf;QACF,IACA;QAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;QAAe;KAAU;IAEzC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACpB,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,WAAW,CAAC,CAAC,CAAC,iBAAiB,OAAO,KAAK,IAAI,cAAc,SAAS;QACxE,CAAC,GACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,SAAS;KACzD;IAEH,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,IAAM,CAAC;YACL,MAAM;YACN,GAAG,UAAU;QACf,CAAC,GACD;QAAC;KAAW;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9245, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/button/dist/chunk-57V4RE7B.mjs"], "sourcesContent": ["\"use client\";\nimport {\n  useButtonGroup\n} from \"./chunk-6XRBX2TW.mjs\";\nimport {\n  ButtonGroupProvider\n} from \"./chunk-3SAWKTTV.mjs\";\n\n// src/button-group.tsx\nimport { forwardRef } from \"@heroui/system\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ButtonGroup = forwardRef((props, ref) => {\n  const { Component, domRef, context, children, classNames, getButtonGroupProps } = useButtonGroup({\n    ...props,\n    ref\n  });\n  return /* @__PURE__ */ jsx(ButtonGroupProvider, { value: context, children: /* @__PURE__ */ jsx(Component, { ref: domRef, className: classNames, ...getButtonGroupProps(), children }) });\n});\nButtonGroup.displayName = \"HeroUI.ButtonGroup\";\nvar button_group_default = ButtonGroup;\n\nexport {\n  button_group_default\n};\n"], "names": [], "mappings": ";;;AACA;AAGA;AAIA,uBAAuB;AACvB;AACA;AAVA;;;;;AAWA,IAAI,cAAc,CAAA,GAAA,uKAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACnC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;QAC/F,GAAG,KAAK;QACR;IACF;IACA,OAAO,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,gKAAA,CAAA,sBAAmB,EAAE;QAAE,OAAO;QAAS,UAAU,aAAa,GAAG,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,WAAW;YAAE,KAAK;YAAQ,WAAW;YAAY,GAAG,qBAAqB;YAAE;QAAS;IAAG;AACzL;AACA,YAAY,WAAW,GAAG;AAC1B,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}]}