{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs"], "sourcesContent": ["/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText (text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, { maxWidth: styles.maxWidth || 100, align: 'justify' });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\n\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n            this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        }\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1 &&\n                this.jsPDFDocument.setFontStyle) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = { width: pageSize.getWidth(), height: pageSize.getHeight() };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: { head: { fontStyle: 'bold' }, foot: { fontStyle: 'bold' } },\n    };\n    return themes[name];\n}\n\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = parseSpacing(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\nfunction validateInput(global, document, current) {\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n    }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\n\nfunction parseInput(d, current) {\n    var doc = new DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    validateInput(global, document, current);\n    var options = assign({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent$1(doc, options, win);\n    return { id: current.tableId, content: content, hooks: hooks, styles: styles, settings: settings };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = assign({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent$1(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return { columns: columns, head: head, body: body, foot: foot };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\n\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = parseSpacing(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = getPageAvailableWidth(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= getStringWidth(text, styles, doc)) {\n        return text;\n    }\n    while (width < getStringWidth(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\nfunction createTable(jsPDFDoc, input) {\n    var doc = new DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new Table(input, content);\n    calculateWidths(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a;\n        var key;\n        if (typeof input === 'object') {\n            key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n        }\n        else {\n            key = index;\n        }\n        return new Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = getTheme(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? assign({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = defaultStyles(scaleFactor);\n    var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = getPageAvailableWidth(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\n\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = { x: margin.left, y: startY };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = assign({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    addTableBorder(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = assign(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        autoTableText(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    addTableBorder(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\nfunction applyPlugin(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options = args[0];\n        var input = parseInput(this, options);\n        var table = createTable(this, input);\n        drawTable(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        autoTableText(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new DocHandler(this);\n        var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n}\n\nvar _a;\nfunction autoTable(d, options) {\n    var input = parseInput(d, options);\n    var table = createTable(d, input);\n    drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = parseInput(d, options);\n    return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n    drawTable(d, table);\n}\ntry {\n    if (typeof window !== 'undefined' && window) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        var anyWindow = window;\n        var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n        if (jsPDF) {\n            applyPlugin(jsPDF);\n        }\n    }\n}\ncatch (error) {\n    console.error('Could not apply autoTable plugin', error);\n}\n\nexport { Cell, CellHookData, Column, HookData, Row, Table, __createTable, __drawTable, applyPlugin, autoTable, autoTable as default };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AACD,SAAS,cAAe,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG;IAC3C,SAAS,UAAU,CAAC;IACpB,IAAI,uBAAuB;IAC3B,IAAI,IAAI,IAAI,QAAQ,CAAC,WAAW;IAChC,IAAI,WAAW,IAAI,QAAQ,CAAC,WAAW,KAAK;IAC5C,IAAI,mBAAmB,IAAI,mBAAmB,GACxC,IAAI,mBAAmB,KACvB;IACN,IAAI,aAAa,WAAW;IAC5B,IAAI,aAAa;IACjB,IAAI,YAAY;IAChB,IAAI,YAAY;IAChB,IAAI,OAAO,MAAM,KAAK,YAClB,OAAO,MAAM,KAAK,YAClB,OAAO,MAAM,KAAK,YAClB,OAAO,MAAM,KAAK,SAAS;QAC3B,YAAY,OAAO,SAAS,WAAW,KAAK,KAAK,CAAC,cAAc;QAChE,YAAY,UAAU,MAAM,IAAI;IACpC;IACA,gBAAgB;IAChB,KAAK,WAAW,CAAC,IAAI,oBAAoB;IACzC,IAAI,OAAO,MAAM,KAAK,UAClB,KAAK,AAAC,YAAY,IAAK;SACtB,IAAI,OAAO,MAAM,KAAK,UACvB,KAAK,YAAY;IACrB,IAAI,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,KAAK,SAAS;QACzD,IAAI,YAAY;QAChB,IAAI,OAAO,MAAM,KAAK,UAClB,aAAa;QACjB,IAAI,aAAa,aAAa,GAAG;YAC7B,IAAK,IAAI,QAAQ,GAAG,QAAQ,UAAU,MAAM,EAAE,QAAS;gBACnD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,IAAI,kBAAkB,CAAC,SAAS,CAAC,MAAM,IAAI,WAAW;gBACrF,KAAK;YACT;YACA,OAAO;QACX;QACA,KAAK,IAAI,kBAAkB,CAAC,QAAQ;IACxC;IACA,IAAI,OAAO,MAAM,KAAK,WAAW;QAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG;YAAE,UAAU,OAAO,QAAQ,IAAI;YAAK,OAAO;QAAU;IAC9E,OACK;QACD,IAAI,IAAI,CAAC,MAAM,GAAG;IACtB;IACA,OAAO;AACX;AAEA,IAAI,iBAAiB,CAAC;AACtB,IAAI,aAA4B;IAC5B,SAAS,WAAW,aAAa;QAC7B,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG;YACd,mDAAmD;YACnD,WAAW,cAAc,YAAY,GAC/B,IAAI,CAAC,aAAa,CAAC,YAAY,KAC/B;YACN,UAAU,cAAc,QAAQ,CAAC,WAAW;YAC5C,WAAW,cAAc,QAAQ,CAAC,OAAO,GAAG,SAAS;YACrD,MAAM,cAAc,QAAQ,CAAC,OAAO,GAAG,QAAQ;YAC/C,+CAA+C;YAC/C,WAAW,cAAc,YAAY,GAC/B,IAAI,CAAC,aAAa,CAAC,YAAY,KAC/B;YACN,mDAAmD;YACnD,WAAW,cAAc,YAAY,GAC/B,IAAI,CAAC,aAAa,CAAC,YAAY,KAC/B;QACV;IACJ;IACA,WAAW,WAAW,GAAG,SAAU,QAAQ,EAAE,GAAG;QAC5C,IAAI,QAAQ,KAAK,GAAG;YAAE,MAAM;QAAM;QAClC,IAAI,KAAK;YACL,IAAI,2BAA2B,GAAG;QACtC,OACK;YACD,iBAAiB;QACrB;IACJ;IACA,WAAW,UAAU,GAAG,SAAU,CAAC;QAC/B,IAAI,MAAM,OAAO,CAAC,IAAI;YAClB,OAAO;QACX,OACK,IAAI,OAAO,MAAM,UAAU;YAC5B,OAAO;gBAAC;gBAAG;gBAAG;aAAE;QACpB,OACK,IAAI,OAAO,MAAM,UAAU;YAC5B,OAAO;gBAAC;aAAE;QACd,OACK;YACD,OAAO;QACX;IACJ;IACA,WAAW,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM,EAAE,QAAQ;QACzD,6CAA6C;QAC7C,+DAA+D;QAC/D,IAAI,IAAI,IAAI;QACZ,IAAI,aAAa,KAAK,GAAG;YAAE,WAAW;QAAO;QAC7C,IAAI,OAAO,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;YACrD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,SAAS;QACpD;QACA,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,IAAI,YAAY,GAAG,SAAS,EAAE,WAAW,GAAG,QAAQ;QAChG,IAAI,OAAO,IAAI,EACX,WAAW,OAAO,IAAI;QAC1B,IAAI,OAAO,SAAS,EAAE;YAClB,YAAY,OAAO,SAAS;YAC5B,IAAI,sBAAsB,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS;YACtD,IAAI,uBACA,oBAAoB,OAAO,CAAC,eAAe,CAAC,KAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;gBACjC,oDAAoD;gBACpD,2CAA2C;gBAC3C,+DAA+D;gBAC/D,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;gBACtD,YAAY,mBAAmB,CAAC,EAAE;YACtC;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU;QACrC,IAAI,OAAO,QAAQ,EACf,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,QAAQ;QAClD,IAAI,UAAU;YACV,QAAQ,0BAA0B;QACtC;QACA,IAAI,QAAQ,WAAW,UAAU,CAAC,OAAO,SAAS;QAClD,IAAI,OACA,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI;QACrD,QAAQ,WAAW,UAAU,CAAC,OAAO,SAAS;QAC9C,IAAI,OACA,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI;QACrD,QAAQ,WAAW,UAAU,CAAC,OAAO,SAAS;QAC9C,IAAI,OACA,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI;QACrD,IAAI,OAAO,OAAO,SAAS,KAAK,UAAU;YACtC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,SAAS;QACpD;IACJ;IACA,WAAW,SAAS,CAAC,eAAe,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,IAAI;QAC7D,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,MAAM;IAC1D;IACA;;;;;;;KAOC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QAChE,0EAA0E;QAC1E,kEAAkE;QAClE,6DAA6D;QAC7D,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,QAAQ;IACxD;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACpC,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,IAAI;IAC/C;IACA,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;IAC3C;IACA,WAAW,SAAS,CAAC,WAAW,GAAG;QAC/B,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI;QACzC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IAC/B;IACA,WAAW,SAAS,CAAC,OAAO,GAAG;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO;IACrC;IACA,WAAW,SAAS,CAAC,WAAW,GAAG;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW;IACzC;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACpC,OAAO,kBAAkB,CAAC;IAC9B;IACA,WAAW,SAAS,CAAC,kBAAkB,GAAG;QACtC,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,IAAI,CAAC;IAC9D;IACA,WAAW,SAAS,CAAC,QAAQ,GAAG;QAC5B,IAAI,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ;QACnD,iEAAiE;QACjE,IAAI,SAAS,KAAK,IAAI,MAAM;YACxB,WAAW;gBAAE,OAAO,SAAS,QAAQ;gBAAI,QAAQ,SAAS,SAAS;YAAG;QAC1E;QACA,OAAO;IACX;IACA,WAAW,SAAS,CAAC,WAAW,GAAG;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW;IAClD;IACA,WAAW,SAAS,CAAC,mBAAmB,GAAG;QACvC,IAAI,MAAM,IAAI,CAAC,aAAa;QAC5B,OAAO,IAAI,mBAAmB,GAAG,IAAI,mBAAmB,KAAK;IACjE;IACA,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,QAAQ;QACnD,OAAO,AAAC,WAAW,IAAI,CAAC,WAAW,KAAM,IAAI,CAAC,mBAAmB;IACrE;IACA,WAAW,SAAS,CAAC,UAAU,GAAG;QAC9B,IAAI,WAAW,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB;QAC7D,IAAI,CAAC,UAAU;YACX,6CAA6C;YAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,gBAAgB;QACvD;QACA,OAAO,SAAS,UAAU;IAC9B;IACA,OAAO;AACX;AAEA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D,GAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC7B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC5B;AAEA,SAAS,UAAU,CAAC,EAAE,CAAC;IACnB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACvF;AAEA,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IAC1F,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACnF;AAEA,IAAI,eAA8B,SAAU,MAAM;IAC9C,UAAU,cAAc;IACxB,SAAS,aAAa,OAAO;QACzB,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,QAAQ,GAAG;QACjB,OAAO;IACX;IACA,OAAO;AACX,EAAE;AACF,4BAA4B;AAC5B,SAAS,cAAc,WAAW;IAC9B,OAAO;QACH,MAAM;QACN,WAAW;QACX,UAAU;QACV,WAAW;QACX,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,aAAa,IAAI;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,eAAe;QACf,cAAc;IAClB;AACJ;AACA,SAAS,SAAS,IAAI;IAClB,IAAI,SAAS;QACT,SAAS;YACL,OAAO;gBAAE,WAAW;gBAAK,WAAW;gBAAI,WAAW;YAAS;YAC5D,MAAM;gBAAE,WAAW;gBAAK,WAAW;oBAAC;oBAAI;oBAAK;iBAAI;gBAAE,WAAW;YAAO;YACrE,MAAM,CAAC;YACP,MAAM;gBAAE,WAAW;gBAAK,WAAW;oBAAC;oBAAI;oBAAK;iBAAI;gBAAE,WAAW;YAAO;YACrE,cAAc;gBAAE,WAAW;YAAI;QACnC;QACA,MAAM;YACF,OAAO;gBACH,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;YACf;YACA,MAAM;gBACF,WAAW;gBACX,WAAW;oBAAC;oBAAI;oBAAK;iBAAI;gBACzB,WAAW;gBACX,WAAW;YACf;YACA,MAAM,CAAC;YACP,MAAM;gBACF,WAAW;gBACX,WAAW;oBAAC;oBAAI;oBAAK;iBAAI;gBACzB,WAAW;gBACX,WAAW;YACf;YACA,cAAc,CAAC;QACnB;QACA,OAAO;YAAE,MAAM;gBAAE,WAAW;YAAO;YAAG,MAAM;gBAAE,WAAW;YAAO;QAAE;IACtE;IACA,OAAO,MAAM,CAAC,KAAK;AACvB;AAEA,SAAS,eAAe,IAAI,EAAE,MAAM,EAAE,GAAG;IACrC,IAAI,WAAW,CAAC,QAAQ;IACxB,IAAI,UAAU,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAC;KAAK;IACjD,IAAI,kBAAkB,QACjB,GAAG,CAAC,SAAU,IAAI;QAAI,OAAO,IAAI,YAAY,CAAC;IAAO,GACrD,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,KAAK,GAAG,CAAC,GAAG;IAAI,GAAG;IACxD,OAAO;AACX;AACA,SAAS,eAAe,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM;IAChD,IAAI,YAAY,MAAM,QAAQ,CAAC,cAAc;IAC7C,IAAI,YAAY,MAAM,QAAQ,CAAC,cAAc;IAC7C,IAAI,WAAW,CAAC;QAAE,WAAW;QAAW,WAAW;IAAU;IAC7D,IAAI,YAAY,aAAa,WAAW;IACxC,IAAI,WAAW;QACX,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,QAAQ,CAAC,IAAI,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,SAAS,CAAC,EAAE;IAClG;AACJ;AACA,SAAS,aAAa,SAAS,EAAE,SAAS;IACtC,IAAI,WAAW,YAAY;IAC3B,IAAI,iBAAiB,aAAa,cAAc;IAChD,IAAI,YAAY,gBAAgB;QAC5B,OAAO,MAAM,mBAAmB;IACpC,OACK,IAAI,UAAU;QACf,OAAO,KAAK,uCAAuC;IACvD,OACK,IAAI,gBAAgB;QACrB,OAAO,KAAK,uBAAuB;IACvC,OACK;QACD,OAAO;IACX;AACJ;AACA,SAAS,aAAa,KAAK,EAAE,YAAY;IACrC,IAAI,IAAI,IAAI,IAAI;IAChB,QAAQ,SAAS;IACjB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,IAAI,MAAM,MAAM,IAAI,GAAG;YACnB,OAAO;gBACH,KAAK,KAAK,CAAC,EAAE;gBACb,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,KAAK,CAAC,EAAE;gBAChB,MAAM,KAAK,CAAC,EAAE;YAClB;QACJ,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;YACzB,OAAO;gBACH,KAAK,KAAK,CAAC,EAAE;gBACb,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,KAAK,CAAC,EAAE;gBAChB,MAAM,KAAK,CAAC,EAAE;YAClB;QACJ,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;YACzB,OAAO;gBACH,KAAK,KAAK,CAAC,EAAE;gBACb,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,KAAK,CAAC,EAAE;gBAChB,MAAM,KAAK,CAAC,EAAE;YAClB;QACJ,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;YACzB,QAAQ,KAAK,CAAC,EAAE;QACpB,OACK;YACD,QAAQ;QACZ;IACJ;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,IAAI,OAAO,MAAM,QAAQ,KAAK,UAAU;YACpC,MAAM,GAAG,GAAG,MAAM,QAAQ;YAC1B,MAAM,MAAM,GAAG,MAAM,QAAQ;QACjC;QACA,IAAI,OAAO,MAAM,UAAU,KAAK,UAAU;YACtC,MAAM,KAAK,GAAG,MAAM,UAAU;YAC9B,MAAM,IAAI,GAAG,MAAM,UAAU;QACjC;QACA,OAAO;YACH,MAAM,CAAC,KAAK,MAAM,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACzD,KAAK,CAAC,KAAK,MAAM,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YACvD,OAAO,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YAC3D,QAAQ,CAAC,KAAK,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACjE;IACJ;IACA,IAAI,OAAO,UAAU,UAAU;QAC3B,QAAQ;IACZ;IACA,OAAO;QAAE,KAAK;QAAO,OAAO;QAAO,QAAQ;QAAO,MAAM;IAAM;AAClE;AACA,SAAS,sBAAsB,GAAG,EAAE,KAAK;IACrC,IAAI,UAAU,aAAa,MAAM,QAAQ,CAAC,MAAM,EAAE;IAClD,OAAO,IAAI,QAAQ,GAAG,KAAK,GAAG,CAAC,QAAQ,IAAI,GAAG,QAAQ,KAAK;AAC/D;AAEA,cAAc;AACd,kCAAkC;AAClC,gCAAgC;AAChC,SAAS,SAAS,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM;IACjE,IAAI,SAAS,CAAC;IACd,IAAI,gBAAgB,KAAK;IACzB,IAAI,kBAAkB,WAAW,SAAS,SAAU,IAAI;QACpD,OAAO,OAAO,gBAAgB,CAAC,KAAK,CAAC,kBAAkB;IAC3D;IACA,IAAI,mBAAmB,MACnB,OAAO,SAAS,GAAG;IACvB,IAAI,YAAY,WAAW,SAAS,SAAU,IAAI;QAC9C,OAAO,OAAO,gBAAgB,CAAC,KAAK,CAAC,QAAQ;IACjD;IACA,IAAI,aAAa,MACb,OAAO,SAAS,GAAG;IACvB,IAAI,UAAU,aAAa,OAAO;IAClC,IAAI,SACA,OAAO,WAAW,GAAG;IACzB,IAAI,kBAAkB;IACtB,IAAI,mBAAmB,gBAAgB;IACvC,IAAI,MAAM,MAAM,cAAc;IAC9B,IAAI,MAAM,iBAAiB,KAAK,OAC5B,MAAM,gBAAgB,KAAK,OAC3B,MAAM,eAAe,KAAK,KAAK;QAC/B,IAAI,cAAc,CAAC,WAAW,QAAQ,CAAC,IAAI;QAC3C,IAAI,aACA,OAAO,SAAS,GAAG;IAC3B,OACK;QACD,OAAO,SAAS,GAAG;YACf,KAAK,CAAC,WAAW,MAAM,cAAc,KAAK,CAAC,IAAI;YAC/C,OAAO,CAAC,WAAW,MAAM,gBAAgB,KAAK,CAAC,IAAI;YACnD,QAAQ,CAAC,WAAW,MAAM,iBAAiB,KAAK,CAAC,IAAI;YACrD,MAAM,CAAC,WAAW,MAAM,eAAe,KAAK,CAAC,IAAI;QACrD;QACA,8CAA8C;QAC9C,sDAAsD;QACtD,IAAI,CAAC,OAAO,SAAS,CAAC,GAAG,EAAE;YACvB,IAAI,OAAO,SAAS,CAAC,KAAK,EAAE;gBACxB,kBAAkB;YACtB,OACK,IAAI,OAAO,SAAS,CAAC,MAAM,EAAE;gBAC9B,kBAAkB;YACtB,OACK,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE;gBAC5B,kBAAkB;YACtB;QACJ;IACJ;IACA,IAAI,cAAc,WAAW,SAAS,SAAU,IAAI;QAChD,OAAO,OAAO,gBAAgB,CAAC,KAAK,CAAC,gBAAgB;IACzD;IACA,IAAI,eAAe,MACf,OAAO,SAAS,GAAG;IACvB,IAAI,WAAW;QAAC;QAAQ;QAAS;QAAU;KAAU;IACrD,IAAI,SAAS,OAAO,CAAC,MAAM,SAAS,MAAM,CAAC,GAAG;QAC1C,OAAO,MAAM,GAAG,MAAM,SAAS;IACnC;IACA,WAAW;QAAC;QAAU;QAAU;KAAM;IACtC,IAAI,SAAS,OAAO,CAAC,MAAM,aAAa,MAAM,CAAC,GAAG;QAC9C,OAAO,MAAM,GAAG,MAAM,aAAa;IACvC;IACA,IAAI,MAAM,SAAS,MAAM,QAAQ,IAAI;IACrC,IAAI,CAAC,MAAM,MACP,OAAO,QAAQ,GAAG,MAAM;IAC5B,IAAI,YAAY,eAAe;IAC/B,IAAI,WACA,OAAO,SAAS,GAAG;IACvB,IAAI,OAAO,CAAC,MAAM,UAAU,IAAI,EAAE,EAAE,WAAW;IAC/C,IAAI,eAAe,OAAO,CAAC,UAAU,CAAC,GAAG;QACrC,OAAO,IAAI,GAAG;IAClB;IACA,OAAO;AACX;AACA,SAAS,eAAe,KAAK;IACzB,IAAI,MAAM;IACV,IAAI,MAAM,UAAU,KAAK,UACrB,MAAM,UAAU,KAAK,YACrB,SAAS,MAAM,UAAU,KAAK,KAAK;QACnC,MAAM;IACV;IACA,IAAI,MAAM,SAAS,KAAK,YAAY,MAAM,SAAS,KAAK,WAAW;QAC/D,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,WAAW,OAAO,EAAE,WAAW;IACpC,IAAI,WAAW,UAAU,SAAS;IAClC,IAAI,CAAC,UACD,OAAO;IACX,IAAI,OAAO,SAAS,KAAK,CAAC;IAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;QAC/B,OAAO;IACX;IACA,IAAI,QAAQ;QACR,SAAS,IAAI,CAAC,EAAE;QAChB,SAAS,IAAI,CAAC,EAAE;QAChB,SAAS,IAAI,CAAC,EAAE;KACnB;IACD,IAAI,QAAQ,SAAS,IAAI,CAAC,EAAE;IAC5B,IAAI,UAAU,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC,EAAE,GAAG;QACtE,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAI,EAAE,WAAW;IAChC,IAAI,KAAK,YAAY;IACrB,IAAI,OAAO,sBACP,OAAO,iBACP,OAAO,aACP,OAAO,WAAW;QAClB,IAAI,KAAK,aAAa,IAAI,MAAM;YAC5B,OAAO;QACX;QACA,OAAO,UAAU,KAAK,aAAa,EAAE;IACzC,OACK;QACD,OAAO;IACX;AACJ;AACA,SAAS,aAAa,KAAK,EAAE,WAAW;IACpC,IAAI,MAAM;QACN,MAAM,UAAU;QAChB,MAAM,YAAY;QAClB,MAAM,aAAa;QACnB,MAAM,WAAW;KACpB;IACD,IAAI,gBAAgB,KAAK,CAAC,KAAK,WAAW;IAC1C,IAAI,cAAc,CAAC,SAAS,MAAM,UAAU,IAAI,SAAS,MAAM,QAAQ,CAAC,IAAI,cAAc;IAC1F,IAAI,eAAe,IAAI,GAAG,CAAC,SAAU,CAAC;QAClC,OAAO,SAAS,KAAK,OAAO;IAChC;IACA,IAAI,UAAU,aAAa,cAAc;IACzC,IAAI,cAAc,QAAQ,GAAG,EAAE;QAC3B,QAAQ,GAAG,GAAG;IAClB;IACA,IAAI,cAAc,QAAQ,MAAM,EAAE;QAC9B,QAAQ,MAAM,GAAG;IACrB;IACA,OAAO;AACX;AAEA,SAAS,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM;IAC5D,IAAI,IAAI;IACR,IAAI,sBAAsB,KAAK,GAAG;QAAE,oBAAoB;IAAO;IAC/D,IAAI,WAAW,KAAK,GAAG;QAAE,SAAS;IAAO;IACzC,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC3B,eAAe,OAAO,QAAQ,CAAC,aAAa,CAAC;IACjD,OACK;QACD,eAAe;IACnB;IACA,IAAI,iBAAiB,OAAO,IAAI,CAAC,IAAI,WAAW;IAChD,IAAI,cAAc,IAAI,WAAW;IACjC,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE;IACnC,IAAI,CAAC,cAAc;QACf,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,MAAM;YAAM,MAAM;YAAM,MAAM;QAAK;IAChD;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAI,CAAC,MAAM,EAAE,IAAK;QAC/C,IAAI,UAAU,aAAa,IAAI,CAAC,EAAE;QAClC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW;QACvM,IAAI,MAAM,gBAAgB,gBAAgB,aAAa,QAAQ,SAAS,mBAAmB;QAC3F,IAAI,CAAC,KACD;QACJ,IAAI,YAAY,SAAS;YACrB,KAAK,IAAI,CAAC;QACd,OACK,IAAI,YAAY,SAAS;YAC1B,KAAK,IAAI,CAAC;QACd,OACK;YACD,+CAA+C;YAC/C,KAAK,IAAI,CAAC;QACd;IACJ;IACA,OAAO;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAK;AAChD;AACA,SAAS,gBAAgB,cAAc,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,EAAE,aAAa,EAAE,MAAM;IACpF,IAAI,YAAY,IAAI,aAAa;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,IAAK;QACvC,IAAI,OAAO,IAAI,KAAK,CAAC,EAAE;QACvB,IAAI,UAAU,OAAO,gBAAgB,CAAC;QACtC,IAAI,iBAAiB,QAAQ,OAAO,KAAK,QAAQ;YAC7C,IAAI,aAAa,KAAK;YACtB,IAAI,QAAQ;gBACR,aAAa,SAAS,gBAAgB,MAAM,aAAa,SAAS;YACtE;YACA,UAAU,IAAI,CAAC;gBACX,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO;gBACrB,QAAQ;gBACR,UAAU;gBACV,SAAS,iBAAiB;YAC9B;QACJ;IACJ;IACA,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,IAAI,UAAU,MAAM,GAAG,KAAK,CAAC,iBAAiB,MAAM,OAAO,KAAK,MAAM,GAAG;QACrE,OAAO;IACX;AACJ;AACA,SAAS,iBAAiB,OAAO;IAC7B,wEAAwE;IACxE,IAAI,OAAO,QAAQ,SAAS,CAAC;IAC7B,0EAA0E;IAC1E,8BAA8B;IAC9B,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO;IAClE,+CAA+C;IAC/C,KAAK,SAAS,GAAG,KAAK,SAAS,CAC1B,KAAK,CAAC,WAAW,qCAAqC;KACtD,GAAG,CAAC,SAAU,IAAI;QAAI,OAAO,KAAK,IAAI;IAAI,GAC1C,IAAI,CAAC;IACV,mBAAmB;IACnB,OAAO,KAAK,SAAS,IAAI,KAAK,WAAW,IAAI;AACjD;AAEA,SAAS,cAAc,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC5C,IAAK,IAAI,KAAK,GAAG,KAAK;QAAC;QAAQ;QAAU;KAAQ,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;QACrE,IAAI,UAAU,EAAE,CAAC,GAAG;QACpB,IAAI,WAAW,OAAO,YAAY,UAAU;YACxC,QAAQ,KAAK,CAAC,yDAAyD,OAAO;QAClF;QACA,IAAI,QAAQ,MAAM,IAAI,OAAO,QAAQ,MAAM,KAAK,UAAU;YACtD,QAAQ,KAAK,CAAC,mCAAmC,QAAQ,MAAM;YAC/D,OAAO,QAAQ,MAAM;QACzB;IACJ;AACJ;AAEA,oDAAoD,GACpD,iGAAiG;AACjG,SAAS,OAAO,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACjC,IAAI,UAAU,MAAM;QAChB,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,KAAK,OAAO;IAChB,IAAK,IAAI,QAAQ,GAAG,QAAQ,UAAU,MAAM,EAAE,QAAS;QACnD,8CAA8C;QAC9C,IAAI,aAAa,SAAS,CAAC,MAAM;QACjC,IAAI,cAAc,MAAM;YACpB,iCAAiC;YACjC,IAAK,IAAI,WAAW,WAAY;gBAC5B,6CAA6C;gBAC7C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,UAAU;oBAC3D,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ;gBACrC;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,WAAW,CAAC,EAAE,OAAO;IAC1B,IAAI,MAAM,IAAI,WAAW;IACzB,IAAI,WAAW,IAAI,kBAAkB;IACrC,IAAI,SAAS,IAAI,gBAAgB;IACjC,cAAc,QAAQ,UAAU;IAChC,IAAI,UAAU,OAAO,CAAC,GAAG,QAAQ,UAAU;IAC3C,IAAI;IACJ;;IAGA,IAAI,SAAS,YAAY,QAAQ,UAAU;IAC3C,IAAI,QAAQ,WAAW,QAAQ,UAAU;IACzC,IAAI,WAAW,cAAc,KAAK;IAClC,IAAI,UAAU,eAAe,KAAK,SAAS;IAC3C,OAAO;QAAE,IAAI,QAAQ,OAAO;QAAE,SAAS;QAAS,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAS;AACrG;AACA,SAAS,YAAY,MAAM,EAAE,MAAM,EAAE,MAAM;IACvC,IAAI,eAAe;QACf,QAAQ,CAAC;QACT,YAAY,CAAC;QACb,YAAY,CAAC;QACb,YAAY,CAAC;QACb,oBAAoB,CAAC;QACrB,cAAc,CAAC;IACnB;IACA,IAAI,UAAU,SAAU,IAAI;QACxB,IAAI,SAAS,gBAAgB;YACzB,IAAI,WAAW,MAAM,CAAC,KAAK;YAC3B,IAAI,aAAa,MAAM,CAAC,KAAK;YAC7B,IAAI,UAAU,MAAM,CAAC,KAAK;YAC1B,aAAa,YAAY,GAAG,OAAO,CAAC,GAAG,UAAU,YAAY;QACjE,OACK;YACD,IAAI,aAAa;gBAAC;gBAAQ;gBAAQ;aAAO;YACzC,IAAI,SAAS,WAAW,GAAG,CAAC,SAAU,IAAI;gBAAI,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC;YAAG;YACvE,YAAY,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;QACnE;IACJ;IACA,IAAK,IAAI,KAAK,GAAG,KAAK,OAAO,IAAI,CAAC,eAAe,KAAK,GAAG,MAAM,EAAE,KAAM;QACnE,IAAI,OAAO,EAAE,CAAC,GAAG;QACjB,QAAQ;IACZ;IACA,OAAO;AACX;AACA,SAAS,WAAW,MAAM,EAAE,QAAQ,EAAE,OAAO;IACzC,IAAI,aAAa;QAAC;QAAQ;QAAU;KAAQ;IAC5C,IAAI,SAAS;QACT,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,aAAa,EAAE;QACf,cAAc,EAAE;QAChB,aAAa,EAAE;IACnB;IACA,IAAK,IAAI,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,MAAM,EAAE,KAAM;QACxE,IAAI,UAAU,YAAY,CAAC,GAAG;QAC9B,IAAI,QAAQ,YAAY,EACpB,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,YAAY;QACjD,IAAI,QAAQ,YAAY,EACpB,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,YAAY;QACjD,IAAI,QAAQ,WAAW,EACnB,OAAO,WAAW,CAAC,IAAI,CAAC,QAAQ,WAAW;QAC/C,IAAI,QAAQ,YAAY,EACpB,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,YAAY;QACjD,IAAI,QAAQ,WAAW,EACnB,OAAO,WAAW,CAAC,IAAI,CAAC,QAAQ,WAAW;IACnD;IACA,OAAO;AACX;AACA,SAAS,cAAc,GAAG,EAAE,OAAO;IAC/B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;IAChD,IAAI,SAAS,aAAa,QAAQ,MAAM,EAAE,KAAK,IAAI,WAAW;IAC9D,IAAI,SAAS,CAAC,KAAK,UAAU,KAAK,QAAQ,MAAM,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,GAAG;IAC9F,IAAI;IACJ,IAAI,QAAQ,QAAQ,KAAK,MAAM;QAC3B,WAAW;IACf,OACK,IAAI,QAAQ,QAAQ,KAAK,OAAO;QACjC,WAAW;IACf,OACK;QACD,WAAW,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACxE;IACA,IAAI;IACJ,IAAI,QAAQ,QAAQ,KAAK,MAAM;QAC3B,WAAW;IACf,OACK,IAAI,QAAQ,QAAQ,KAAK,OAAO;QACjC,WAAW;IACf,OACK;QACD,WAAW,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACxE;IACA,IAAI,SAAS,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACpE,IAAI,QAAQ,QAAQ,KAAK,IAAI,CAAC,SAAS,UAAU,SAAS;IAC1D,IAAI,sBAAsB,CAAC,CAAC,QAAQ,mBAAmB;IACvD,IAAI,4BAA4B,CAAC,KAAK,QAAQ,yBAAyB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC1G,OAAO;QACH,mBAAmB,CAAC,KAAK,QAAQ,iBAAiB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACrF,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACrE,cAAc,CAAC,KAAK,QAAQ,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC3E,YAAY,CAAC,KAAK,QAAQ,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACvE,UAAU;QACV,UAAU;QACV,gBAAgB,CAAC,KAAK,QAAQ,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC/E,gBAAgB,CAAC,KAAK,QAAQ,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC/E,qBAAqB;QACrB,2BAA2B;QAC3B,8BAA8B,CAAC,KAAK,QAAQ,4BAA4B,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC/G;AACJ;AACA,SAAS,UAAU,GAAG,EAAE,UAAU;IAC9B,IAAI,WAAW,IAAI,gBAAgB;IACnC,IAAI,KAAK,IAAI,WAAW;IACxB,IAAI,cAAc,IAAI,UAAU;IAChC,IAAI,4BAA4B;IAChC,IAAI,YAAY,SAAS,eAAe,EAAE;QACtC,IAAI,aAAa,SAAS,eAAe,GAAG,SAAS,UAAU,GAAG;QAClE,4BAA4B,eAAe;IAC/C;IACA,IAAI,OAAO,eAAe,UAAU;QAChC,OAAO;IACX,OACK,IAAI,cAAc,QAAQ,eAAe,OAAO;QACjD,IAAI,6BAA6B,CAAC,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,MAAM,KAAK,MAAM;YAC5G,wEAAwE;YACxE,0EAA0E;YAC1E,OAAO,SAAS,MAAM,GAAG,KAAK;QAClC;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,GAAG,EAAE,OAAO,EAAE,MAAM;IACxC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE;IAC7B,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE;IAC7B,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE;IAC7B,IAAI,QAAQ,IAAI,EAAE;QACd,IAAI,SAAS,QAAQ,iBAAiB;QACtC,IAAI,QAAQ;YACR,IAAI,cAAc,UAAU,KAAK,QAAQ,IAAI,EAAE,QAAQ,QAAQ,QAAQ,MAAM,KAAK,CAAC;YACnF,OAAO,YAAY,IAAI,IAAI;YAC3B,OAAO,YAAY,IAAI,IAAI;YAC3B,OAAO,YAAY,IAAI,IAAI;QAC/B,OACK;YACD,QAAQ,KAAK,CAAC;QAClB;IACJ;IACA,IAAI,UAAU,QAAQ,OAAO,IAAI,aAAa,MAAM,MAAM;IAC1D,OAAO;QAAE,SAAS;QAAS,MAAM;QAAM,MAAM;QAAM,MAAM;IAAK;AAClE;AACA,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI;IAClC,IAAI,WAAW,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE;IAClD,IAAI,SAAS,EAAE;IACf,OAAO,IAAI,CAAC,UACP,MAAM,CAAC,SAAU,GAAG;QAAI,OAAO,QAAQ;IAAY,GACnD,OAAO,CAAC,SAAU,GAAG;QACtB,IAAI,UAAU;QACd,IAAI;QACJ,IAAI,MAAM,OAAO,CAAC,WAAW;YACzB,QAAQ,QAAQ,CAAC,SAAS,KAAK;QACnC,OACK;YACD,QAAQ,QAAQ,CAAC,IAAI;QACzB;QACA,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC,QAAQ;YACpD,UAAU,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,KAAK;QAC/E;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;YAC9B,IAAI,KAAK,KAAK;YACd,IAAI,MAAM,OAAO,CAAC,WAAW;gBACzB,KAAK,OAAO,MAAM;YACtB,OACK;gBACD,KAAK,MAAM,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;YAC1C;YACA,IAAI,YAAY;gBAAE,SAAS;YAAG;YAC9B,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AAEA,IAAI,WAA0B;IAC1B,SAAS,SAAS,GAAG,EAAE,KAAK,EAAE,MAAM;QAChC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG,IAAI,WAAW;IAC9B;IACA,OAAO;AACX;AACA,IAAI,eAA8B,SAAU,MAAM;IAC9C,UAAU,cAAc;IACxB,SAAS,aAAa,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;QACvD,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,OAAO,WAAW,IAAI;QACzD,MAAM,IAAI,GAAG;QACb,MAAM,GAAG,GAAG;QACZ,MAAM,MAAM,GAAG;QACf,MAAM,OAAO,GAAG,IAAI,OAAO;QAC3B,OAAO;IACX;IACA,OAAO;AACX,EAAE;AAEF,IAAI,QAAuB;IACvB,SAAS,MAAM,KAAK,EAAE,OAAO;QACzB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;QAClB,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;QAC1B,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;QACxB,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;QAC9B,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;QACxB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;QACxB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI;IAC5B;IACA,MAAM,SAAS,CAAC,aAAa,GAAG,SAAU,OAAO;QAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;YAAI,OAAO,MAAM,IAAI,gBAAgB,CAAC;QAAU,GAAG;IACjG;IACA,MAAM,SAAS,CAAC,aAAa,GAAG,SAAU,OAAO;QAC7C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;YAAI,OAAO,MAAM,IAAI,gBAAgB,CAAC;QAAU,GAAG;IACjG;IACA,MAAM,SAAS,CAAC,OAAO,GAAG;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI;IACvD;IACA,MAAM,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;QAC9E,IAAK,IAAI,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,MAAM,EAAE,KAAM;YAClE,IAAI,UAAU,UAAU,CAAC,GAAG;YAC5B,IAAI,OAAO,IAAI,aAAa,KAAK,IAAI,EAAE,MAAM,KAAK,QAAQ;YAC1D,IAAI,SAAS,QAAQ,UAAU;YAC/B,iEAAiE;YACjE,KAAK,IAAI,GAAG,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG;gBAAC,KAAK,IAAI;aAAC;YAC9D,IAAI,QAAQ;gBACR,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG,EAAE,MAAM;QACpD,IAAI,WAAW,CAAC,IAAI,UAAU;QAC9B,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChE,IAAI,UAAU,EAAE,CAAC,GAAG;YACpB,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE;QACpC;IACJ;IACA,MAAM,SAAS,CAAC,qBAAqB,GAAG,SAAU,GAAG,EAAE,MAAM;QACzD,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YACjE,IAAI,UAAU,EAAE,CAAC,GAAG;YACpB,QAAQ,IAAI,SAAS,KAAK,IAAI,EAAE;QACpC;IACJ;IACA,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,SAAS;QAC1C,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,UAAU;YAC9C,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU;QACnC,OACK,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,QAAQ;YAC1C,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAU,KAAK,EAAE,GAAG;gBAAI,OAAO,QAAQ,IAAI,YAAY;YAAE,GAAG;YACnG,OAAO;QACX,OACK;YACD,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;YACjC,OAAO,YAAY,OAAO,IAAI,GAAG,OAAO,KAAK;QACjD;IACJ;IACA,OAAO;AACX;AACA,IAAI,MAAqB;IACrB,SAAS,IAAI,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,kBAAkB;QACvD,IAAI,uBAAuB,KAAK,GAAG;YAAE,qBAAqB;QAAO;QACjE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,eAAe,cAAc;YAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ;QAC/B;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA,IAAI,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO;QAC9C,IAAI,QAAQ,IAAI;QAChB,OAAO,QAAQ,MAAM,CAAC,SAAU,GAAG,EAAE,MAAM;YAAI,IAAI;YAAI,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK;QAAI,GAAG;IAC5K;IACA,IAAI,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO;QACxC,IAAI,QAAQ,IAAI;QAChB,OAAQ,QAAQ,MAAM,CAAC,SAAU,MAAM;YACnC,IAAI,OAAO,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC;YACpC,IAAI,CAAC,MACD,OAAO;YACX,OAAO,KAAK,OAAO,GAAG;QAC1B,GAAG,MAAM,GAAG;IAChB;IACA,IAAI,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM,EAAE,OAAO;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY;IAC7C;IACA,IAAI,SAAS,CAAC,mBAAmB,GAAG,SAAU,OAAO,EAAE,GAAG;QACtD,IAAI,QAAQ,IAAI;QAChB,OAAO,QAAQ,MAAM,CAAC,SAAU,GAAG,EAAE,MAAM;YACvC,IAAI,OAAO,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC;YACpC,IAAI,CAAC,MACD,OAAO;YACX,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,MAAM,CAAC,QAAQ;YACvD,IAAI,WAAW,KAAK,OAAO,CAAC;YAC5B,IAAI,eAAe,WAAW;YAC9B,OAAO,eAAe,MAAM,eAAe;QAC/C,GAAG;IACP;IACA,OAAO;AACX;AACA,IAAI,OAAsB;IACtB,SAAS,KAAK,GAAG,EAAE,MAAM,EAAE,OAAO;QAC9B,IAAI;QACJ,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,UAAU;QACd,IAAI,OAAO,QAAQ,OAAO,QAAQ,YAAY,CAAC,MAAM,OAAO,CAAC,MAAM;YAC/D,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,IAAI;YAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,IAAI;YAC9B,UAAU,CAAC,KAAK,IAAI,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YAC9D,IAAI,IAAI,QAAQ,EAAE;gBACd,IAAI,CAAC,GAAG,GAAG,IAAI,QAAQ;YAC3B;QACJ,OACK;YACD,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,mDAAmD;QACnD,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;QAC5C,IAAI,aAAa;QACjB,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC;IAC3B;IACA,KAAK,SAAS,CAAC,UAAU,GAAG;QACxB,IAAI;QACJ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,OAAO;YAC9B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU;YACtC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5C,OACK;YACD,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC3C,IAAI,IAAI,CAAC,CAAC,GAAG,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC;QAC9C;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS;YAChC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU;YACtC,IAAI,WAAW,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;YACzC,IAAI,IAAI,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC;QAC7C,OACK;YACD,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B;QACA,OAAO;YAAE,GAAG;YAAG,GAAG;QAAE;IACxB;IACA,uDAAuD;IACvD,KAAK,SAAS,CAAC,gBAAgB,GAAG,SAAU,WAAW,EAAE,gBAAgB;QACrE,IAAI,qBAAqB,KAAK,GAAG;YAAE,mBAAmB;QAAM;QAC5D,IAAI,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAC9D,IAAI,aAAa,AAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,cAAe;QACxD,IAAI,SAAS,YAAY,aAAa,IAAI,CAAC,OAAO,CAAC;QACnD,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa;IACrD;IACA,KAAK,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI;QACnC,IAAI,UAAU,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;QACpD,IAAI,SAAS,YAAY;YACrB,OAAO,QAAQ,GAAG,GAAG,QAAQ,MAAM;QACvC,OACK,IAAI,SAAS,cAAc;YAC5B,OAAO,QAAQ,IAAI,GAAG,QAAQ,KAAK;QACvC,OACK;YACD,OAAO,OAAO,CAAC,KAAK;QACxB;IACJ;IACA,OAAO;AACX;AACA,IAAI,SAAwB;IACxB,SAAS,OAAO,OAAO,EAAE,GAAG,EAAE,KAAK;QAC/B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,OAAO,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAK;QACpD,IAAI,MAAM;QACV,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,MAAM,EAAE,KAAM;YACzD,IAAI,MAAM,EAAE,CAAC,GAAG;YAChB,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;YAChC,IAAI,QAAQ,OAAO,KAAK,MAAM,CAAC,SAAS,KAAK,UAAU;gBACnD,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,SAAS;YAC7C;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;AAEA;;CAEC,GACD,SAAS,gBAAgB,GAAG,EAAE,KAAK;IAC/B,UAAU,KAAK;IACf,IAAI,mBAAmB,EAAE;IACzB,IAAI,oBAAoB;IACxB,MAAM,OAAO,CAAC,OAAO,CAAC,SAAU,MAAM;QAClC,IAAI,cAAc,OAAO,qBAAqB,CAAC;QAC/C,IAAI,aAAa;YACb,qBAAqB;YACrB,OAAO,KAAK,GAAG;QACnB,OACK;YACD,yCAAyC;YACzC,OAAO,KAAK,GAAG,OAAO,YAAY;YAClC,iBAAiB,IAAI,CAAC;QAC1B;QACA,qBAAqB,OAAO,KAAK;IACrC;IACA,gDAAgD;IAChD,IAAI,cAAc,MAAM,QAAQ,CAAC,IAAI,QAAQ,GAAG,KAAK,IAAI;IACzD,sEAAsE;IACtE,IAAI,aAAa;QACb,cAAc,cAAc,kBAAkB,aAAa,SAAU,MAAM;YACvE,OAAO,KAAK,GAAG,CAAC,OAAO,gBAAgB,EAAE,OAAO,QAAQ;QAC5D;IACJ;IACA,sEAAsE;IACtE,IAAI,aAAa;QACb,cAAc,cAAc,kBAAkB,aAAa,SAAU,MAAM;YAAI,OAAO,OAAO,QAAQ;QAAE;IAC3G;IACA,cAAc,KAAK,GAAG,CAAC;IACvB,IAAI,CAAC,MAAM,QAAQ,CAAC,mBAAmB,IACnC,cAAc,MAAM,IAAI,WAAW,IAAI;QACvC,uEAAuE;QACvE,0DAA0D;QAC1D,oEAAoE;QACpE,8CAA8C;QAC9C,cAAc,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;QACzD,QAAQ,IAAI,CAAC,yBAAyB,MAAM,CAAC,aAAa;IAC9D;IACA,cAAc;IACd,WAAW,OAAO;IAClB,cAAc;AAClB;AACA,SAAS,UAAU,GAAG,EAAE,KAAK;IACzB,IAAI,KAAK,IAAI,WAAW;IACxB,IAAI,sBAAsB,MAAM,QAAQ,CAAC,mBAAmB;IAC5D,IAAI,qBAAqB,sBAAsB,KAAK;IACpD,MAAM,OAAO,GAAG,OAAO,CAAC,SAAU,GAAG;QACjC,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YACvD,IAAI,SAAS,EAAE,CAAC,GAAG;YACnB,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;YAClC,IAAI,CAAC,MACD;YACJ,IAAI,QAAQ,MAAM,KAAK,CAAC,YAAY;YACpC,MAAM,aAAa,CAAC,KAAK,OAAO,MAAM,KAAK,QAAQ;YACnD,IAAI,UAAU,KAAK,OAAO,CAAC;YAC3B,KAAK,YAAY,GAAG,eAAe,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,OAAO;YAClE,wEAAwE;YACxE,sEAAsE;YACtE,wEAAwE;YACxE,eAAe;YACf,IAAI,mBAAmB,eAAe,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,iBAAiB,KAAK,MAAM,EAAE;YAC9F,KAAK,gBAAgB,GAAG,mBAAmB,KAAK,OAAO,CAAC;YACxD,IAAI,OAAO,KAAK,MAAM,CAAC,SAAS,KAAK,UAAU;gBAC3C,KAAK,QAAQ,GAAG,KAAK,MAAM,CAAC,SAAS;gBACrC,KAAK,YAAY,GAAG,KAAK,MAAM,CAAC,SAAS;YAC7C,OACK,IAAI,KAAK,MAAM,CAAC,SAAS,KAAK,UAC/B,wBAAwB,MAAM;gBAC9B,0DAA0D;gBAC1D,IAAI,KAAK,YAAY,GAAG,oBAAoB;oBACxC,KAAK,QAAQ,GAAG;oBAChB,KAAK,YAAY,GAAG;gBACxB,OACK;oBACD,KAAK,QAAQ,GAAG,KAAK,YAAY;oBACjC,KAAK,YAAY,GAAG,KAAK,YAAY;gBACzC;YACJ,OACK;gBACD,OAAO;gBACP,IAAI,kBAAkB,KAAK;gBAC3B,KAAK,QAAQ,GAAG,KAAK,MAAM,CAAC,YAAY,IAAI;gBAC5C,KAAK,YAAY,GAAG,KAAK,YAAY;gBACrC,IAAI,KAAK,QAAQ,GAAG,KAAK,YAAY,EAAE;oBACnC,KAAK,YAAY,GAAG,KAAK,QAAQ;gBACrC;YACJ;QACJ;IACJ;IACA,MAAM,OAAO,GAAG,OAAO,CAAC,SAAU,GAAG;QACjC,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YACvD,IAAI,SAAS,EAAE,CAAC,GAAG;YACnB,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;YAClC,oGAAoG;YACpG,2CAA2C;YAC3C,IAAI,QAAQ,KAAK,OAAO,KAAK,GAAG;gBAC5B,OAAO,YAAY,GAAG,KAAK,GAAG,CAAC,OAAO,YAAY,EAAE,KAAK,YAAY;gBACrE,OAAO,QAAQ,GAAG,KAAK,GAAG,CAAC,OAAO,QAAQ,EAAE,KAAK,QAAQ;gBACzD,OAAO,gBAAgB,GAAG,KAAK,GAAG,CAAC,OAAO,gBAAgB,EAAE,KAAK,gBAAgB;YACrF,OACK;gBACD,kFAAkF;gBAClF,6EAA6E;gBAC7E,mFAAmF;gBACnF,mCAAmC;gBACnC,mFAAmF;gBACnF,oBAAoB;gBACpB,IAAI,eAAe,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,OAAO,CAAC,IACxD,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,KAAK,CAAC,IACvC,CAAC;gBACL,IAAI,YAAY,aAAa,SAAS,IAAI,aAAa,YAAY;gBACnE,IAAI,aAAa,OAAO,cAAc,UAAU;oBAC5C,OAAO,QAAQ,GAAG;oBAClB,OAAO,YAAY,GAAG;gBAC1B;YACJ;YACA,IAAI,MAAM;gBACN,oGAAoG;gBACpG,IAAI,KAAK,OAAO,GAAG,KAAK,CAAC,OAAO,QAAQ,EAAE;oBACtC,OAAO,QAAQ,GAAG,KAAK,QAAQ;gBACnC;gBACA,IAAI,KAAK,OAAO,GAAG,KAAK,CAAC,OAAO,YAAY,EAAE;oBAC1C,OAAO,YAAY,GAAG,KAAK,QAAQ;gBACvC;YACJ;QACJ;IACJ;AACJ;AACA;;CAEC,GACD,SAAS,cAAc,OAAO,EAAE,WAAW,EAAE,WAAW;IACpD,IAAI,qBAAqB;IACzB,IAAI,kBAAkB,QAAQ,MAAM,CAAC,SAAU,GAAG,EAAE,MAAM;QAAI,OAAO,MAAM,OAAO,YAAY;IAAE,GAAG;IACnG,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACrC,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,YAAY,GAAG;QAClC,IAAI,kBAAkB,qBAAqB;QAC3C,IAAI,iBAAiB,OAAO,KAAK,GAAG;QACpC,IAAI,WAAW,YAAY;QAC3B,IAAI,WAAW,iBAAiB,WAAW,WAAW;QACtD,eAAe,WAAW,OAAO,KAAK;QACtC,OAAO,KAAK,GAAG;IACnB;IACA,cAAc,KAAK,KAAK,CAAC,cAAc,QAAQ;IAC/C,yDAAyD;IACzD,6DAA6D;IAC7D,IAAI,aAAa;QACb,IAAI,mBAAmB,QAAQ,MAAM,CAAC,SAAU,MAAM;YAClD,OAAO,cAAc,IACf,OAAO,KAAK,GAAG,YAAY,QAAQ,6BAA6B;eAChE,MAAM,2BAA2B;QAC3C;QACA,IAAI,iBAAiB,MAAM,EAAE;YACzB,cAAc,cAAc,kBAAkB,aAAa;QAC/D;IACJ;IACA,OAAO;AACX;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,eAAe,CAAC;IACpB,IAAI,kBAAkB;IACtB,IAAI,MAAM,MAAM,OAAO;IACvB,IAAK,IAAI,WAAW,GAAG,WAAW,IAAI,MAAM,EAAE,WAAY;QACtD,IAAI,MAAM,GAAG,CAAC,SAAS;QACvB,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YACvD,IAAI,SAAS,EAAE,CAAC,GAAG;YACnB,IAAI,OAAO,YAAY,CAAC,OAAO,KAAK,CAAC;YACrC,IAAI,kBAAkB,GAAG;gBACrB;gBACA,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;YAClC,OACK,IAAI,MAAM;gBACX,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM;gBAC9B,kBAAkB,KAAK,IAAI,CAAC,OAAO;gBACnC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;gBAC9B,KAAK,IAAI;gBACT,IAAI,KAAK,IAAI,IAAI,GAAG;oBAChB,OAAO,YAAY,CAAC,OAAO,KAAK,CAAC;gBACrC;YACJ,OACK;gBACD,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;gBAClC,IAAI,CAAC,MAAM;oBACP;gBACJ;gBACA,KAAK,MAAM,GAAG,IAAI,MAAM;gBACxB,IAAI,KAAK,OAAO,GAAG,GAAG;oBAClB,IAAI,YAAY,IAAI,MAAM,GAAG;oBAC7B,IAAI,OAAO,KAAK,OAAO,GAAG,YAAY,YAAY,KAAK,OAAO;oBAC9D,YAAY,CAAC,OAAO,KAAK,CAAC,GAAG;wBAAE,MAAM;wBAAM,MAAM;wBAAM,KAAK;oBAAI;gBACpE;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,MAAM,MAAM,OAAO;IACvB,IAAK,IAAI,WAAW,GAAG,WAAW,IAAI,MAAM,EAAE,WAAY;QACtD,IAAI,MAAM,GAAG,CAAC,SAAS;QACvB,IAAI,cAAc;QAClB,IAAI,uBAAuB;QAC3B,IAAI,eAAe;QACnB,IAAK,IAAI,cAAc,GAAG,cAAc,MAAM,OAAO,CAAC,MAAM,EAAE,cAAe;YACzE,IAAI,SAAS,MAAM,OAAO,CAAC,YAAY;YACvC,oBAAoB;YACpB,gBAAgB;YAChB,IAAI,eAAe,KAAK,MAAM,OAAO,CAAC,cAAc,EAAE,EAAE;gBACpD,wBAAwB,OAAO,KAAK;gBACpC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;YAClC,OACK,IAAI,aAAa;gBAClB,IAAI,OAAO;gBACX,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;gBAC9B,cAAc;gBACd,KAAK,KAAK,GAAG,OAAO,KAAK,GAAG;YAChC,OACK;gBACD,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;gBAClC,IAAI,CAAC,MACD;gBACJ,eAAe,KAAK,OAAO;gBAC3B,uBAAuB;gBACvB,IAAI,KAAK,OAAO,GAAG,GAAG;oBAClB,cAAc;oBACd,wBAAwB,OAAO,KAAK;oBACpC;gBACJ;gBACA,KAAK,KAAK,GAAG,OAAO,KAAK,GAAG;YAChC;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,KAAK,EAAE,GAAG;IAC1B,IAAI,gBAAgB;QAAE,OAAO;QAAG,QAAQ;IAAE;IAC1C,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG,MAAM,EAAE,KAAM;QACzD,IAAI,MAAM,EAAE,CAAC,GAAG;QAChB,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YACvD,IAAI,SAAS,EAAE,CAAC,GAAG;YACnB,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;YAClC,IAAI,CAAC,MACD;YACJ,IAAI,WAAW,CAAC,KAAK,MAAM,EAAE;YAC7B,IAAI,YAAY,KAAK,KAAK,GAAG,KAAK,OAAO,CAAC;YAC1C,IAAI,KAAK,MAAM,CAAC,QAAQ,KAAK,aAAa;gBACtC,gDAAgD;gBAChD,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC,KAAK,IAAI,EAAE,YAAY,IAAI,IAAI,WAAW,IAAI;oBAAE,UAAU,KAAK,MAAM,CAAC,QAAQ;gBAAC;YACnH,OACK,IAAI,KAAK,MAAM,CAAC,QAAQ,KAAK,aAAa;gBAC3C,KAAK,IAAI,GAAG,UAAU,KAAK,IAAI,EAAE,WAAW,KAAK,MAAM,EAAE,KAAK;YAClE,OACK,IAAI,KAAK,MAAM,CAAC,QAAQ,KAAK,UAAU;gBACxC,KAAK,IAAI,GAAG,UAAU,KAAK,IAAI,EAAE,WAAW,KAAK,MAAM,EAAE,KAAK;YAClE,OACK,IAAI,OAAO,KAAK,MAAM,CAAC,QAAQ,KAAK,YAAY;gBACjD,IAAI,SAAS,KAAK,MAAM,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBAC7C,IAAI,OAAO,WAAW,UAAU;oBAC5B,KAAK,IAAI,GAAG;wBAAC;qBAAO;gBACxB,OACK;oBACD,KAAK,IAAI,GAAG;gBAChB;YACJ;YACA,KAAK,aAAa,GAAG,KAAK,gBAAgB,CAAC,IAAI,WAAW,IAAI,IAAI,mBAAmB;YACrF,IAAI,oBAAoB,KAAK,aAAa,GAAG,KAAK,OAAO;YACzD,IAAI,KAAK,OAAO,GAAG,KACf,cAAc,KAAK,GAAG,cAAc,MAAM,GACtC,oBAAoB,KAAK,OAAO,EAAE;gBACtC,gBAAgB;oBAAE,QAAQ;oBAAmB,OAAO,KAAK,OAAO;gBAAC;YACrE,OACK,IAAI,iBAAiB,cAAc,KAAK,GAAG,GAAG;gBAC/C,IAAI,cAAc,MAAM,GAAG,mBAAmB;oBAC1C,oBAAoB,cAAc,MAAM;gBAC5C;YACJ;YACA,IAAI,oBAAoB,IAAI,MAAM,EAAE;gBAChC,IAAI,MAAM,GAAG;YACjB;QACJ;QACA,cAAc,KAAK;IACvB;AACJ;AACA,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ;IACjD,OAAO,KAAK,GAAG,CAAC,SAAU,GAAG;QAAI,OAAO,aAAa,KAAK,OAAO,QAAQ,KAAK;IAAW;AAC7F;AACA,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ;IACpD,IAAI,YAAY,QAAQ,IAAI,WAAW;IACvC,QAAQ,KAAK,IAAI,CAAC,QAAQ,aAAa;IACvC,IAAI,SAAS,eAAe,MAAM,QAAQ,MAAM;QAC5C,OAAO;IACX;IACA,MAAO,QAAQ,eAAe,OAAO,UAAU,QAAQ,KAAM;QACzD,IAAI,KAAK,MAAM,IAAI,GAAG;YAClB;QACJ;QACA,OAAO,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG;IAC3C;IACA,OAAO,KAAK,IAAI,KAAK;AACzB;AAEA,SAAS,YAAY,QAAQ,EAAE,KAAK;IAChC,IAAI,MAAM,IAAI,WAAW;IACzB,IAAI,UAAU,aAAa,OAAO,IAAI,WAAW;IACjD,IAAI,QAAQ,IAAI,MAAM,OAAO;IAC7B,gBAAgB,KAAK;IACrB,IAAI,WAAW,CAAC,IAAI,UAAU;IAC9B,OAAO;AACX;AACA,SAAS,aAAa,KAAK,EAAE,EAAE;IAC3B,IAAI,UAAU,MAAM,OAAO;IAC3B,IAAI,UAAU,cAAc,QAAQ,OAAO;IAC3C,yEAAyE;IACzE,IAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,GAAG;QAC3B,IAAI,aAAa,mBAAmB,SAAS;QAC7C,IAAI,YACA,QAAQ,IAAI,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,GAAG;QAC3B,IAAI,aAAa,mBAAmB,SAAS;QAC7C,IAAI,YACA,QAAQ,IAAI,CAAC,IAAI,CAAC;IAC1B;IACA,IAAI,QAAQ,MAAM,QAAQ,CAAC,KAAK;IAChC,IAAI,SAAS,MAAM,MAAM;IACzB,OAAO;QACH,SAAS;QACT,MAAM,aAAa,QAAQ,QAAQ,IAAI,EAAE,SAAS,QAAQ,OAAO;QACjE,MAAM,aAAa,QAAQ,QAAQ,IAAI,EAAE,SAAS,QAAQ,OAAO;QACjE,MAAM,aAAa,QAAQ,QAAQ,IAAI,EAAE,SAAS,QAAQ,OAAO;IACrE;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW;IACnF,IAAI,wBAAwB,CAAC;IAC7B,IAAI,SAAS,YAAY,GAAG,CAAC,SAAU,MAAM,EAAE,QAAQ;QACnD,IAAI,wBAAwB;QAC5B,IAAI,QAAQ,CAAC;QACb,IAAI,gBAAgB;QACpB,IAAI,kBAAkB;QACtB,IAAK,IAAI,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,MAAM,EAAE,KAAM;YAC/D,IAAI,SAAS,SAAS,CAAC,GAAG;YAC1B,IAAI,qBAAqB,CAAC,OAAO,KAAK,CAAC,IAAI,QACvC,qBAAqB,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG;gBAChD,IAAI,oBAAoB,GAAG;oBACvB,IAAI,UAAU,KAAK;oBACnB,IAAI,MAAM,OAAO,CAAC,SAAS;wBACvB,UACI,MAAM,CAAC,OAAO,KAAK,GAAG,gBAAgB,sBAAsB;oBACpE,OACK;wBACD,UAAU,MAAM,CAAC,OAAO,OAAO,CAAC;oBACpC;oBACA,IAAI,kBAAkB,CAAC;oBACvB,IAAI,OAAO,YAAY,YAAY,CAAC,MAAM,OAAO,CAAC,UAAU;wBACxD,kBAAkB,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,MAAM,KAAK,CAAC;oBAC7F;oBACA,IAAI,SAAS,WAAW,aAAa,QAAQ,UAAU,OAAO,YAAY,aAAa;oBACvF,IAAI,OAAO,IAAI,KAAK,SAAS,QAAQ;oBACrC,sDAAsD;oBACtD,4BAA4B;oBAC5B,KAAK,CAAC,OAAO,OAAO,CAAC,GAAG;oBACxB,KAAK,CAAC,OAAO,KAAK,CAAC,GAAG;oBACtB,kBAAkB,KAAK,OAAO,GAAG;oBACjC,qBAAqB,CAAC,OAAO,KAAK,CAAC,GAAG;wBAClC,MAAM,KAAK,OAAO,GAAG;wBACrB,OAAO;oBACX;gBACJ,OACK;oBACD;oBACA;gBACJ;YACJ,OACK;gBACD,qBAAqB,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI;gBACxC,kBAAkB,qBAAqB,CAAC,OAAO,KAAK,CAAC,CAAC,KAAK;gBAC3D;YACJ;QACJ;QACA,OAAO,IAAI,IAAI,QAAQ,UAAU,aAAa;IAClD;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,OAAO,EAAE,OAAO;IACxC,IAAI,aAAa,CAAC;IAClB,QAAQ,OAAO,CAAC,SAAU,GAAG;QACzB,IAAI,IAAI,GAAG,IAAI,MAAM;YACjB,IAAI,QAAQ,gBAAgB,SAAS,IAAI,GAAG;YAC5C,IAAI,SAAS,MACT,UAAU,CAAC,IAAI,OAAO,CAAC,GAAG;QAClC;IACJ;IACA,OAAO,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,IAAI,aAAa;AAC7D;AACA,SAAS,gBAAgB,OAAO,EAAE,MAAM;IACpC,IAAI,YAAY,QAAQ;QACpB,IAAI,OAAO,WAAW,UAAU;YAC5B,OAAO,OAAO,MAAM,IAAI;QAC5B,OACK,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC/D,OAAO;QACX;IACJ,OACK,IAAI,YAAY,UAAU,OAAO,WAAW,UAAU;QACvD,OAAO,OAAO,MAAM;IACxB;IACA,OAAO;AACX;AACA,SAAS,cAAc,OAAO;IAC1B,OAAO,QAAQ,GAAG,CAAC,SAAU,KAAK,EAAE,KAAK;QACrC,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO,UAAU,UAAU;YAC3B,MAAM,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAChE,OACK;YACD,MAAM;QACV;QACA,OAAO,IAAI,OAAO,KAAK,OAAO;IAClC;AACJ;AACA,SAAS,WAAW,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe;IAC9F,IAAI,QAAQ,SAAS;IACrB,IAAI;IACJ,IAAI,gBAAgB,QAAQ;QACxB,gBAAgB,OAAO,UAAU;IACrC,OACK,IAAI,gBAAgB,QAAQ;QAC7B,gBAAgB,OAAO,UAAU;IACrC,OACK,IAAI,gBAAgB,QAAQ;QAC7B,gBAAgB,OAAO,UAAU;IACrC;IACA,IAAI,cAAc,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,CAAC,YAAY,EAAE,OAAO,MAAM,EAAE;IAC7E,IAAI,eAAe,OAAO,YAAY,CAAC,OAAO,OAAO,CAAC,IAClD,OAAO,YAAY,CAAC,OAAO,KAAK,CAAC,IACjC,CAAC;IACL,IAAI,YAAY,gBAAgB,SAAS,eAAe,CAAC;IACzD,IAAI,YAAY,gBAAgB,UAAU,WAAW,MAAM,IACrD,OAAO,CAAC,GAAG,MAAM,YAAY,EAAE,OAAO,kBAAkB,IACxD,CAAC;IACP,IAAI,eAAe,cAAc;IACjC,IAAI,cAAc,OAAO,CAAC,GAAG,cAAc,aAAa,WAAW;IACnE,OAAO,OAAO,aAAa;AAC/B;AAEA,mCAAmC;AACnC,SAAS,uBAAuB,GAAG,EAAE,KAAK,EAAE,MAAM;IAC9C,IAAI;IACJ,IAAI,WAAW,KAAK,GAAG;QAAE,SAAS,CAAC;IAAG;IACtC,iBAAiB;IACjB,IAAI,iBAAiB,sBAAsB,KAAK;IAChD,gCAAgC;IAChC,IAAI,mBAAmB,IAAI;IAC3B,IAAI,aAAa,EAAE;IACnB,IAAI,UAAU,EAAE;IAChB,IAAI,4BAA4B,EAAE;IAClC,IAAI,MAAM,OAAO,CAAC,MAAM,QAAQ,CAAC,yBAAyB,GAAG;QACzD,4BAA4B,MAAM,QAAQ,CAAC,yBAAyB;IACpE,qEAAqE;IACzE,OACK,IAAI,OAAO,MAAM,QAAQ,CAAC,yBAAyB,KAAK,YACzD,OAAO,MAAM,QAAQ,CAAC,yBAAyB,KAAK,UAAU;QAC9D,4BAA4B;YAAC,MAAM,QAAQ,CAAC,yBAAyB;SAAC;IAC1E;IACA,iDAAiD;IACjD,0BAA0B,OAAO,CAAC,SAAU,KAAK;QAC7C,IAAI,MAAM,MAAM,OAAO,CAAC,IAAI,CAAC,SAAU,IAAI;YAAI,OAAO,KAAK,OAAO,KAAK,SAAS,KAAK,KAAK,KAAK;QAAO;QACtG,IAAI,OAAO,CAAC,iBAAiB,GAAG,CAAC,IAAI,KAAK,GAAG;YACzC,iBAAiB,GAAG,CAAC,IAAI,KAAK,EAAE;YAChC,WAAW,IAAI,CAAC,IAAI,KAAK;YACzB,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,KAAK,CAAC;YACrC,kBAAkB,IAAI,YAAY;QACtC;IACJ;IACA,IAAI,QAAQ;IACZ,IAAI,IAAI,CAAC,KAAK,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,GAAG,iDAAiD;IACjK,MAAO,IAAI,MAAM,OAAO,CAAC,MAAM,CAAE;QAC7B,qBAAqB;QACrB,IAAI,iBAAiB,GAAG,CAAC,IAAI;YACzB;YACA;QACJ;QACA,IAAI,WAAW,MAAM,OAAO,CAAC,EAAE,CAAC,YAAY;QAC5C,kDAAkD;QAClD,IAAI,SAAS,kBAAkB,UAAU;YACrC,QAAQ;YACR,WAAW,IAAI,CAAC;YAChB,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE;YAC7B,kBAAkB;QACtB,OACK;YACD;QACJ;QACA;IACJ;IACA,OAAO;QAAE,YAAY;QAAY,SAAS;QAAS,WAAW,IAAI;IAAE;AACxE;AACA,SAAS,gCAAgC,GAAG,EAAE,KAAK;IAC/C,IAAI,aAAa,EAAE;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;QAC3C,IAAI,SAAS,uBAAuB,KAAK,OAAO;YAAE,OAAO;QAAE;QAC3D,IAAI,OAAO,OAAO,CAAC,MAAM,EAAE;YACvB,WAAW,IAAI,CAAC;YAChB,IAAI,OAAO,SAAS;QACxB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,UAAU,QAAQ,EAAE,KAAK;IAC9B,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,SAAS;QAAE,GAAG,OAAO,IAAI;QAAE,GAAG;IAAO;IACzC,IAAI,iBAAiB,MAAM,aAAa,CAAC,MAAM,OAAO,IAAI,MAAM,aAAa,CAAC,MAAM,OAAO;IAC3F,IAAI,oBAAoB,SAAS,OAAO,MAAM,GAAG;IACjD,IAAI,SAAS,SAAS,KAAK,SAAS;QAChC,IAAI,OAAO,MAAM,IAAI;QACrB,IAAI,cAAc,KAAK,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;YAAI,OAAO,MAAM,IAAI,MAAM;QAAE,GAAG;QAChF,qBAAqB;IACzB;IACA,IAAI,MAAM,IAAI,WAAW;IACzB,IAAI,SAAS,SAAS,KAAK,YACtB,SAAS,MAAM,IAAI,QAAQ,oBAAoB,IAAI,QAAQ,GAAG,MAAM,EAAG;QACxE,SAAS;QACT,OAAO,CAAC,GAAG,OAAO,GAAG;IACzB;IACA,MAAM,qBAAqB,CAAC,KAAK;IACjC,IAAI,WAAW,OAAO,CAAC,GAAG;IAC1B,MAAM,eAAe,GAAG,IAAI,UAAU;IACtC,IAAI,SAAS,mBAAmB,EAAE;QAC9B,iCAAiC;QACjC,kCAAkC,KAAK,OAAO,UAAU;IAC5D,OACK;QACD,cAAc;QACd,IAAI,WAAW,CAAC,IAAI,UAAU;QAC9B,IAAI,SAAS,QAAQ,KAAK,eACtB,SAAS,QAAQ,KAAK,aAAa;YACnC,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG;gBAC5B,OAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;YAC1D;QACJ;QACA,IAAI,WAAW,CAAC,IAAI,UAAU;QAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG,EAAE,KAAK;YACnC,IAAI,YAAY,UAAU,MAAM,IAAI,CAAC,MAAM,GAAG;YAC9C,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ,MAAM,OAAO;QAC5E;QACA,IAAI,WAAW,CAAC,IAAI,UAAU;QAC9B,IAAI,SAAS,QAAQ,KAAK,cAAc,SAAS,QAAQ,KAAK,aAAa;YACvE,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG;gBAC5B,OAAO,SAAS,KAAK,OAAO,KAAK,QAAQ,MAAM,OAAO;YAC1D;QACJ;IACJ;IACA,eAAe,KAAK,OAAO,UAAU;IACrC,MAAM,gBAAgB,CAAC,KAAK;IAC5B,MAAM,MAAM,GAAG,OAAO,CAAC;IACvB,SAAS,aAAa,GAAG;IACzB,IAAI,WAAW,CAAC,IAAI,UAAU;AAClC;AACA,SAAS,kCAAkC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM;IACnE,2EAA2E;IAC3E,IAAI,yBAAyB,gCAAgC,KAAK;IAClE,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,SAAS,4BAA4B,KAAK,gBAAgB;QAC1D,uBAAuB,OAAO,CAAC,SAAU,cAAc,EAAE,KAAK;YAC1D,IAAI,WAAW,CAAC,IAAI,UAAU;YAC9B,6CAA6C;YAC7C,IAAI,QAAQ,GAAG;gBACX,8DAA8D;gBAC9D,6DAA6D;gBAC7D,QAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO,EAAE;YAClE,OACK;gBACD,kCAAkC;gBAClC,UAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;YACxD;YACA,2CAA2C;YAC3C,UAAU,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO;YAC9D,UAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;QACxD;IACJ,OACK;QACD,IAAI,2BAA2B,CAAC;QAChC,IAAI,0BAA0B,sBAAsB,CAAC,EAAE;QACvD,IAAI,UAAU;YACV,+DAA+D;YAC/D,IAAI,sBAAsB;YAC1B,IAAI,yBAAyB;gBACzB,IAAI,WAAW,CAAC,IAAI,UAAU;gBAC9B,IAAI,oBAAoB,wBAAwB,OAAO;gBACvD,IAAI,4BAA4B,GAAG;oBAC/B,8DAA8D;oBAC9D,6DAA6D;oBAC7D,QAAQ,KAAK,OAAO,UAAU,QAAQ,mBAAmB;gBAC7D,OACK;oBACD,UAAU,KAAK,OAAO,QAAQ;gBAClC;gBACA,sBAAsB,2BAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ;gBACnG,UAAU,KAAK,OAAO,QAAQ;YAClC;YACA,iGAAiG;YACjG,IAAI,kBAAkB,sBAAsB;YAC5C,yDAAyD;YACzD,uBAAuB,KAAK,CAAC,GAAG,OAAO,CAAC,SAAU,cAAc;gBAC5D,IAAI,WAAW,CAAC,IAAI,UAAU;gBAC9B,8DAA8D;gBAC9D,6DAA6D;gBAC7D,QAAQ,KAAK,OAAO,UAAU,QAAQ,eAAe,OAAO,EAAE;gBAC9D,2BAA2B,KAAK,OAAO,2BAA2B,GAAG,QAAQ,eAAe,OAAO,EAAE;gBACrG,UAAU,KAAK,OAAO,QAAQ,eAAe,OAAO;YACxD;YACA,2BAA2B;QAC/B;QACA,MAAO,2BAA2B,MAAM,IAAI,CAAC,MAAM,GAAG,EAAG;YACrD;QACJ;IACJ;AACJ;AACA,SAAS,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,WAAW,CAAC,IAAI,UAAU;IAC9B,IAAI,SAAS,QAAQ,KAAK,eAAe,SAAS,QAAQ,KAAK,aAAa;QACxE,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG;YAAI,OAAO,SAAS,KAAK,OAAO,KAAK,QAAQ;QAAU;IAC3F;AACJ;AACA,SAAS,UAAU,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IACpD,IAAI,WAAW,CAAC,IAAI,UAAU;IAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG,EAAE,KAAK;QACnC,IAAI,YAAY,UAAU,MAAM,IAAI,CAAC,MAAM,GAAG;QAC9C,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ;IAC/D;AACJ;AACA,SAAS,2BAA2B,GAAG,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;IAC3F,IAAI,WAAW,CAAC,IAAI,UAAU;IAC9B,kBAAkB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,MAAM,IAAI,CAAC,MAAM;IAC9G,IAAI,cAAc,KAAK,GAAG,CAAC,gBAAgB,iBAAiB,MAAM,IAAI,CAAC,MAAM;IAC7E,IAAI,sBAAsB,CAAC;IAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,aAAa,OAAO,CAAC,SAAU,GAAG,EAAE,KAAK;QACrE,IAAI,YAAY,gBAAgB,UAAU,MAAM,IAAI,CAAC,MAAM,GAAG;QAC9D,IAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW;QAClE,IAAI,IAAI,eAAe,CAAC,gBAAgB,UAAU;YAC9C,SAAS,KAAK,OAAO,KAAK,QAAQ;YAClC,sBAAsB,gBAAgB;QAC1C;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,WAAW,CAAC,IAAI,UAAU;IAC9B,IAAI,SAAS,QAAQ,KAAK,cAAc,SAAS,QAAQ,KAAK,aAAa;QACvE,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG;YAAI,OAAO,SAAS,KAAK,OAAO,KAAK,QAAQ;QAAU;IAC3F;AACJ;AACA,SAAS,sBAAsB,IAAI,EAAE,kBAAkB,EAAE,GAAG;IACxD,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,MAAM,CAAC,QAAQ;IACvD,IAAI,WAAW,KAAK,OAAO,CAAC;IAC5B,IAAI,iBAAiB,KAAK,KAAK,CAAC,CAAC,qBAAqB,QAAQ,IAAI;IAClE,OAAO,KAAK,GAAG,CAAC,GAAG;AACvB;AACA,SAAS,eAAe,GAAG,EAAE,kBAAkB,EAAE,KAAK,EAAE,GAAG;IACvD,IAAI,QAAQ,CAAC;IACb,IAAI,kBAAkB,GAAG;IACzB,IAAI,MAAM,GAAG;IACb,IAAI,YAAY;IAChB,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;QACvD,IAAI,SAAS,EAAE,CAAC,GAAG;QACnB,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;QAClC,IAAI,CAAC,MACD;QACJ,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;YAC3B,KAAK,IAAI,GAAG;gBAAC,KAAK,IAAI;aAAC;QAC3B;QACA,IAAI,gBAAgB,IAAI,KAAK,KAAK,GAAG,EAAE,KAAK,MAAM,EAAE,KAAK,OAAO;QAChE,gBAAgB,OAAO,eAAe;QACtC,cAAc,IAAI,GAAG,EAAE;QACvB,IAAI,qBAAqB,sBAAsB,MAAM,oBAAoB;QACzE,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,oBAAoB;YACvC,cAAc,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,oBAAoB,KAAK,IAAI,CAAC,MAAM;QAC9E;QACA,IAAI,cAAc,IAAI,WAAW;QACjC,IAAI,mBAAmB,IAAI,mBAAmB;QAC9C,KAAK,aAAa,GAAG,KAAK,gBAAgB,CAAC,aAAa;QACxD,IAAI,KAAK,aAAa,IAAI,oBAAoB;YAC1C,KAAK,aAAa,GAAG;YACrB,cAAc,MAAM,CAAC,aAAa,IAAI;QAC1C;QACA,IAAI,KAAK,aAAa,GAAG,IAAI,MAAM,EAAE;YACjC,IAAI,MAAM,GAAG,KAAK,aAAa;QACnC;QACA,cAAc,aAAa,GAAG,cAAc,gBAAgB,CAAC,aAAa;QAC1E,IAAI,cAAc,aAAa,GAAG,WAAW;YACzC,YAAY,cAAc,aAAa;QAC3C;QACA,KAAK,CAAC,OAAO,KAAK,CAAC,GAAG;IAC1B;IACA,IAAI,eAAe,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,OAAO,EAAE,OAAO;IAC5D,aAAa,MAAM,GAAG;IACtB,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;QACvD,IAAI,SAAS,EAAE,CAAC,GAAG;QACnB,IAAI,gBAAgB,aAAa,KAAK,CAAC,OAAO,KAAK,CAAC;QACpD,IAAI,eAAe;YACf,cAAc,MAAM,GAAG,aAAa,MAAM;QAC9C;QACA,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;QAClC,IAAI,MAAM;YACN,KAAK,MAAM,GAAG,IAAI,MAAM;QAC5B;IACJ;IACA,OAAO;AACX;AACA,SAAS,yBAAyB,GAAG,EAAE,GAAG,EAAE,kBAAkB,EAAE,KAAK;IACjE,IAAI,aAAa,IAAI,QAAQ,GAAG,MAAM;IACtC,IAAI,SAAS,MAAM,QAAQ,CAAC,MAAM;IAClC,IAAI,eAAe,OAAO,GAAG,GAAG,OAAO,MAAM;IAC7C,IAAI,eAAe,aAAa;IAChC,IAAI,IAAI,OAAO,KAAK,QAAQ;QACxB,0DAA0D;QAC1D,mCAAmC;QACnC,gBACI,MAAM,aAAa,CAAC,MAAM,OAAO,IAAI,MAAM,aAAa,CAAC,MAAM,OAAO;IAC9E;IACA,IAAI,eAAe,IAAI,mBAAmB,CAAC,MAAM,OAAO,EAAE;IAC1D,IAAI,aAAa,eAAe;IAChC,IAAI,eAAe,cAAc;QAC7B,QAAQ,KAAK,CAAC,iCAAiC,MAAM,CAAC,IAAI,KAAK,EAAE;QACjE,OAAO;IACX;IACA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,IAAI,oBAAoB,IAAI,UAAU,CAAC,MAAM,OAAO;IACpD,IAAI,oBAAoB,IAAI,gBAAgB,CAAC,MAAM,OAAO,IAAI;IAC9D,IAAI,mBAAmB;QACnB,IAAI,mBAAmB;YACnB,QAAQ,KAAK,CAAC,sBAAsB,MAAM,CAAC,IAAI,KAAK,EAAE;QAC1D;QACA,OAAO;IACX;IACA,IAAI,mBAAmB;QACnB,4EAA4E;QAC5E,OAAO;IACX;IACA,IAAI,MAAM,QAAQ,CAAC,YAAY,KAAK,SAAS;QACzC,OAAO;IACX;IACA,mDAAmD;IACnD,OAAO;AACX;AACA,SAAS,aAAa,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;IACvE,IAAI,iBAAiB,sBAAsB,KAAK,OAAO,WAAW;IAClE,IAAI,IAAI,eAAe,CAAC,gBAAgB,UAAU;QAC9C,mCAAmC;QACnC,SAAS,KAAK,OAAO,KAAK,QAAQ;IACtC,OACK,IAAI,yBAAyB,KAAK,KAAK,gBAAgB,QAAQ;QAChE,yDAAyD;QACzD,IAAI,eAAe,eAAe,KAAK,gBAAgB,OAAO;QAC9D,SAAS,KAAK,OAAO,KAAK,QAAQ;QAClC,QAAQ,KAAK,OAAO,UAAU,QAAQ;QACtC,aAAa,KAAK,OAAO,cAAc,WAAW,UAAU,QAAQ;IACxE,OACK;QACD,iDAAiD;QACjD,QAAQ,KAAK,OAAO,UAAU,QAAQ;QACtC,aAAa,KAAK,OAAO,KAAK,WAAW,UAAU,QAAQ;IAC/D;AACJ;AACA,SAAS,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO;IAC9C,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI;IACrC,IAAK,IAAI,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,MAAM,EAAE,KAAM;QAC/D,IAAI,SAAS,SAAS,CAAC,GAAG;QAC1B,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC;QAClC,IAAI,CAAC,MAAM;YACP,OAAO,CAAC,IAAI,OAAO,KAAK;YACxB;QACJ;QACA,IAAI,WAAW,CAAC,KAAK,MAAM;QAC3B,KAAK,CAAC,GAAG,OAAO,CAAC;QACjB,KAAK,CAAC,GAAG,OAAO,CAAC;QACjB,IAAI,SAAS,MAAM,aAAa,CAAC,KAAK,MAAM,KAAK,CAAC,YAAY,EAAE,MAAM,KAAK,QAAQ;QACnF,IAAI,WAAW,OAAO;YAClB,OAAO,CAAC,IAAI,OAAO,KAAK;YACxB;QACJ;QACA,aAAa,KAAK,MAAM;QACxB,IAAI,UAAU,KAAK,UAAU;QAC7B,cAAc,KAAK,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE;YAC3C,QAAQ,KAAK,MAAM,CAAC,MAAM;YAC1B,QAAQ,KAAK,MAAM,CAAC,MAAM;YAC1B,UAAU,KAAK,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,OAAO,CAAC,UAAU,KAAK,OAAO,CAAC;QACzE,GAAG,IAAI,WAAW;QAClB,MAAM,aAAa,CAAC,KAAK,MAAM,KAAK,CAAC,WAAW,EAAE,MAAM,KAAK,QAAQ;QACrE,OAAO,CAAC,IAAI,OAAO,KAAK;IAC5B;IACA,OAAO,CAAC,IAAI,IAAI,MAAM;AAC1B;AACA,SAAS,aAAa,GAAG,EAAE,IAAI,EAAE,MAAM;IACnC,IAAI,aAAa,KAAK,MAAM;IAC5B,+DAA+D;IAC/D,8BAA8B;IAC9B,IAAI,WAAW,GAAG,YAAY,CAAC,IAAI,WAAW,GAAG,YAAY;IAC7D,IAAI,OAAO,WAAW,SAAS,KAAK,UAAU;QAC1C,2CAA2C;QAC3C,IAAI,YAAY,aAAa,WAAW,SAAS,EAAE,WAAW,SAAS;QACvE,IAAI,WAAW;YACX,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE;QACxD;IACJ,OACK,IAAI,OAAO,WAAW,SAAS,KAAK,UAAU;QAC/C,uBAAuB;QACvB,IAAI,WAAW,SAAS,EAAE;YACtB,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE;QACxD;QACA,+BAA+B;QAC/B,gBAAgB,KAAK,MAAM,QAAQ,WAAW,SAAS;IAC3D;AACJ;AACA;;;;;;;;CAQC,GACD,SAAS,gBAAgB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS;IACjD,IAAI,IAAI,IAAI,IAAI;IAChB,IAAI,UAAU,GAAG,EAAE;QACf,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC,GAAG,KAAK,KAAK;QAC1B,KAAK,OAAO,CAAC;QACb,IAAI,UAAU,KAAK,EAAE;YACjB,MAAM,MAAM,UAAU,KAAK;QAC/B;QACA,IAAI,UAAU,IAAI,EAAE;YAChB,MAAM,MAAM,UAAU,IAAI;QAC9B;QACA,SAAS,UAAU,GAAG,EAAE,IAAI,IAAI,IAAI;IACxC;IACA,IAAI,UAAU,MAAM,EAAE;QAClB,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM;QAC3B,KAAK,OAAO,CAAC,GAAG,KAAK,KAAK;QAC1B,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM;QAC3B,IAAI,UAAU,KAAK,EAAE;YACjB,MAAM,MAAM,UAAU,KAAK;QAC/B;QACA,IAAI,UAAU,IAAI,EAAE;YAChB,MAAM,MAAM,UAAU,IAAI;QAC9B;QACA,SAAS,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI;IAC3C;IACA,IAAI,UAAU,IAAI,EAAE;QAChB,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM;QAC3B,IAAI,UAAU,GAAG,EAAE;YACf,MAAM,MAAM,UAAU,GAAG;QAC7B;QACA,IAAI,UAAU,MAAM,EAAE;YAClB,MAAM,MAAM,UAAU,MAAM;QAChC;QACA,SAAS,UAAU,IAAI,EAAE,IAAI,IAAI,IAAI;IACzC;IACA,IAAI,UAAU,KAAK,EAAE;QACjB,KAAK,OAAO,CAAC,GAAG,KAAK,KAAK;QAC1B,KAAK,OAAO,CAAC;QACb,KAAK,OAAO,CAAC,GAAG,KAAK,KAAK;QAC1B,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM;QAC3B,IAAI,UAAU,GAAG,EAAE;YACf,MAAM,MAAM,UAAU,GAAG;QAC7B;QACA,IAAI,UAAU,MAAM,EAAE;YAClB,MAAM,MAAM,UAAU,MAAM;QAChC;QACA,SAAS,UAAU,KAAK,EAAE,IAAI,IAAI,IAAI;IAC1C;IACA,SAAS,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACnC,IAAI,WAAW,GAAG,YAAY,CAAC;QAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;IAC3C;AACJ;AACA,SAAS,sBAAsB,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM;IACxD,IAAI,sBAAsB,MAAM,QAAQ,CAAC,MAAM,CAAC,MAAM;IACtD,IAAI,WAAW,MAAM,QAAQ,CAAC,QAAQ;IACtC,IAAI,aAAa,eAAgB,aAAa,cAAc,WAAY;QACpE,uBAAuB,MAAM,aAAa,CAAC,MAAM,OAAO;IAC5D;IACA,OAAO,IAAI,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG;AAC9C;AACA,SAAS,QAAQ,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc;IAClE,IAAI,YAAY,KAAK,GAAG;QAAE,UAAU,EAAE;IAAE;IACxC,IAAI,mBAAmB,KAAK,GAAG;QAAE,iBAAiB;IAAO;IACzD,IAAI,WAAW,CAAC,IAAI,UAAU;IAC9B,IAAI,MAAM,QAAQ,CAAC,QAAQ,KAAK,eAAe,CAAC,gBAAgB;QAC5D,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG;YAAI,OAAO,SAAS,KAAK,OAAO,KAAK,QAAQ;QAAU;IAC3F;IACA,8DAA8D;IAC9D,0CAA0C;IAC1C,MAAM,gBAAgB,CAAC,KAAK;IAC5B,IAAI,SAAS,MAAM,QAAQ,CAAC,MAAM;IAClC,eAAe,KAAK,OAAO,UAAU;IACrC,SAAS;IACT,MAAM,UAAU;IAChB,OAAO,CAAC,GAAG,OAAO,IAAI;IACtB,OAAO,CAAC,GAAG,OAAO,GAAG;IACrB,SAAS,CAAC,GAAG,OAAO,GAAG;IACvB,gEAAgE;IAChE,MAAM,qBAAqB,CAAC,KAAK;IACjC,IAAI,MAAM,QAAQ,CAAC,QAAQ,KAAK,aAAa;QACzC,MAAM,IAAI,CAAC,OAAO,CAAC,SAAU,GAAG;YAAI,OAAO,SAAS,KAAK,OAAO,KAAK,QAAQ;QAAU;QACvF,IAAI,WAAW,CAAC,IAAI,UAAU;IAClC;AACJ;AACA,SAAS,SAAS,GAAG;IACjB,IAAI,UAAU,IAAI,UAAU;IAC5B,IAAI,OAAO,CAAC,UAAU;IACtB,IAAI,aAAa,IAAI,UAAU;IAC/B,IAAI,eAAe,SAAS;QACxB,IAAI,OAAO;QACX,OAAO;IACX;IACA,OAAO;AACX;AAEA,SAAS,YAAY,KAAK;IACtB,8DAA8D;IAC9D,MAAM,GAAG,CAAC,SAAS,GAAG;QAClB,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC5B;QACA,IAAI,UAAU,IAAI,CAAC,EAAE;QACrB,IAAI,QAAQ,WAAW,IAAI,EAAE;QAC7B,IAAI,QAAQ,YAAY,IAAI,EAAE;QAC9B,UAAU,IAAI,EAAE;QAChB,OAAO,IAAI;IACf;IACA,gEAAgE;IAChE,MAAM,GAAG,CAAC,aAAa,GAAG;IAC1B,MAAM,GAAG,CAAC,aAAa,GAAG,SAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;QAClD,cAAc,MAAM,GAAG,GAAG,QAAQ,IAAI;IAC1C;IACA,MAAM,GAAG,CAAC,oBAAoB,GAAG,SAAU,QAAQ;QAC/C,WAAW,WAAW,CAAC,UAAU,IAAI;QACrC,OAAO,IAAI;IACf;IACA,MAAM,oBAAoB,GAAG,SAAU,QAAQ,EAAE,GAAG;QAChD,WAAW,WAAW,CAAC,UAAU;IACrC;IACA,MAAM,GAAG,CAAC,mBAAmB,GAAG,SAAU,SAAS,EAAE,qBAAqB;QACtE,IAAI;QACJ,IAAI,0BAA0B,KAAK,GAAG;YAAE,wBAAwB;QAAO;QACvE,wCAAmC;YAC/B,QAAQ,KAAK,CAAC;YACd,OAAO;QACX;;;QACA,IAAI;QACJ,IAAI,IAAsE,MAAgB;QAC1F,IAAI;IAER;AACJ;AAEA,IAAI;AACJ,SAAS,UAAU,CAAC,EAAE,OAAO;IACzB,IAAI,QAAQ,WAAW,GAAG;IAC1B,IAAI,QAAQ,YAAY,GAAG;IAC3B,UAAU,GAAG;AACjB;AACA,sBAAsB;AACtB,SAAS,cAAc,CAAC,EAAE,OAAO;IAC7B,IAAI,QAAQ,WAAW,GAAG;IAC1B,OAAO,YAAY,GAAG;AAC1B;AACA,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,UAAU,GAAG;AACjB;AACA,IAAI;IACA;;QACI,8DAA8D;QAC9D,IAAI;QACJ,IAAI;;AAKZ,EACA,OAAO,OAAO;IACV,QAAQ,KAAK,CAAC,oCAAoC;AACtD", "ignoreList": [0], "debugId": null}}]}