{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/toastError.jsx"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\n\r\n// Fungsi untuk menampilkan pesan toast dengan SweetAlert2\r\nconst ToastError = (title, text, icon = \"error\") => {\r\n  Swal.fire({\r\n    title,\r\n    text,\r\n    icon,\r\n    position: \"top-end\", // Menentukan posisi di atas sebelah kanan\r\n    toast: true,\r\n    showConfirmButton: false, // Tidak menampilkan tombol OK\r\n    timer: 5000,\r\n    showCloseButton: true,\r\n    background: \"red\",\r\n    color: \"white\",\r\n    // color: \"#716add\",\r\n    // customClass: {\r\n    //   container: \"toast-container\",\r\n    //   popup: \"colored-toast\",\r\n    // },\r\n    timerProgressBar: true,\r\n  });\r\n};\r\n\r\n// Fungsi untuk menampilkan pesan error berdasarkan kode status HTTP\r\nconst handleHttpError = (status, text) => {\r\n  switch (status) {\r\n    case 400:\r\n      ToastError(`<PERSON><PERSON>ahan <PERSON>, Permintaan tidak valid. (${text})`);\r\n      break;\r\n    case 401:\r\n      ToastError(\r\n        `Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (${text})`\r\n      );\r\n      break;\r\n    case 403:\r\n      ToastError(`<PERSON><PERSON><PERSON>, <PERSON>ks<PERSON> ke sumber daya dilarang. (${text})`);\r\n      break;\r\n    case 404:\r\n      ToastError(`Error Refresh Token. Silahkan Login Ulang... (${text})`);\r\n      break;\r\n    case 429:\r\n      ToastError(\r\n        `Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${text})`\r\n      );\r\n      break;\r\n    case 422:\r\n      ToastError(\r\n        `Unprocessable Entity, Permintaan tidak dapat diolah. (${text})`\r\n      );\r\n      break;\r\n    case 500:\r\n      ToastError(\"Kesalahan Pada Query\", text);\r\n      break;\r\n    case 503:\r\n      ToastError(\r\n        `Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${text})`\r\n      );\r\n      break;\r\n    case 504:\r\n      ToastError(`Waktu Habis, Permintaan waktu habis. (${text})`);\r\n      break;\r\n    case 505:\r\n      ToastError(\r\n        `Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${text})`\r\n      );\r\n      break;\r\n    case 507:\r\n      ToastError(\r\n        `Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${text})`\r\n      );\r\n      break;\r\n    case 511:\r\n      ToastError(`Autentikasi Diperlukan, Autentikasi diperlukan. (${text})`);\r\n      break;\r\n    default:\r\n      ToastError(`Kesalahan Server, ${text} `);\r\n      break;\r\n  }\r\n};\r\n\r\nexport { ToastError, handleHttpError };\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,0DAA0D;AAC1D,MAAM,aAAa,SAAC,OAAO;QAAM,wEAAO;IACtC,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACR;QACA;QACA;QACA,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,OAAO;QACP,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,oBAAoB;QACpB,iBAAiB;QACjB,kCAAkC;QAClC,4BAA4B;QAC5B,KAAK;QACL,kBAAkB;IACpB;AACF;KAnBM;AAqBN,oEAAoE;AACpE,MAAM,kBAAkB,CAAC,QAAQ;IAC/B,OAAQ;QACN,KAAK;YACH,WAAW,AAAC,kDAAsD,OAAL,MAAK;YAClE;QACF,KAAK;YACH,WACE,AAAC,6DAAiE,OAAL,MAAK;YAEpE;QACF,KAAK;YACH,WAAW,AAAC,kDAAsD,OAAL,MAAK;YAClE;QACF,KAAK;YACH,WAAW,AAAC,iDAAqD,OAAL,MAAK;YACjE;QACF,KAAK;YACH,WACE,AAAC,qEAAyE,OAAL,MAAK;YAE5E;QACF,KAAK;YACH,WACE,AAAC,yDAA6D,OAAL,MAAK;YAEhE;QACF,KAAK;YACH,WAAW,wBAAwB;YACnC;QACF,KAAK;YACH,WACE,AAAC,6DAAiE,OAAL,MAAK;YAEpE;QACF,KAAK;YACH,WAAW,AAAC,yCAA6C,OAAL,MAAK;YACzD;QACF,KAAK;YACH,WACE,AAAC,0DAA8D,OAAL,MAAK;YAEjE;QACF,KAAK;YACH,WACE,AAAC,0DAA8D,OAAL,MAAK;YAEjE;QACF,KAAK;YACH,WAAW,AAAC,oDAAwD,OAAL,MAAK;YACpE;QACF;YACE,WAAW,AAAC,qBAAyB,OAAL,MAAK;YACrC;IACJ;AACF", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/charts/trenApbn.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useContext, useEffect, useState } from \"react\";\r\nimport { <PERSON>, <PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Skeleton, Chip } from \"@heroui/react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { Database, FileX } from \"lucide-react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport { handleHttpError } from \"@/components/ui/feedback/toastError\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\n\r\nconst Chart = dynamic(() => import(\"react-apexcharts\"), {\r\n  ssr: false,\r\n  loading: () => (\r\n    <div className=\"w-full h-full flex items-center justify-center\">\r\n      <Skeleton className=\"w-full h-64\" />\r\n    </div>\r\n  ),\r\n});\r\n\r\nexport default function TrenApbn({ selectedKanwil, selectedKddept }) {\r\n  const [dataRencanaReal, setDataRencanaReal] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const context = useContext(MyContext);\r\n  const { theme } = useTheme();\r\n\r\n  const { token, axiosJWT } = context || {};\r\n\r\n  const formatTrillions = (amount) =>\r\n    new Intl.NumberFormat(\"id-ID\", {\r\n      minimumFractionDigits: 1,\r\n      maximumFractionDigits: 1,\r\n    }).format(amount) + \" T\";\r\n\r\n  const getThemeColors = () => {\r\n    const isDark = theme === \"dark\";\r\n    return {\r\n      foreColor: isDark ? \"#a1a1aa\" : \"#52525b\",\r\n      borderColor: isDark ? \"#3f3f46\" : \"#e4e4e7\",\r\n      gridColor: isDark ? \"#27272a\" : \"#f4f4f5\",\r\n      textPrimary: isDark ? \"#f3f4f6\" : \"#374151\",\r\n      primary: \"#3B82F6\",\r\n      success: \"#10B981\",\r\n    };\r\n  };\r\n\r\n  const getData = async () => {\r\n    let kanwilFilter =\r\n      selectedKanwil && selectedKanwil !== \"00\"\r\n        ? ` and a.kdkanwil='${selectedKanwil}'`\r\n        : \"\";\r\n    let kddeptFilter =\r\n      selectedKddept && selectedKddept !== \"000\"\r\n        ? ` and a.kddept='${selectedKddept}'`\r\n        : \"\";\r\n\r\n    const rawQuery = `SELECT a.kddept,b.nmdept,\r\n      ROUND(sum(renc1)/1, 0) as renc1, ROUND(sum(real1)/1, 0) as real1,\r\n      ROUND(sum(renc2)/1, 0) as renc2, ROUND(sum(real2)/1, 0) as real2,\r\n      ROUND(sum(renc3)/1, 0) as renc3, ROUND(sum(real3)/1, 0) as real3,\r\n      ROUND(sum(renc4)/1, 0) as renc4, ROUND(sum(real4)/1, 0) as real4,\r\n      ROUND(sum(renc5)/1, 0) as renc5, ROUND(sum(real5)/1, 0) as real5,\r\n      ROUND(sum(renc6)/1, 0) as renc6, ROUND(sum(real6)/1, 0) as real6,\r\n      ROUND(sum(renc7)/1, 0) as renc7, ROUND(sum(real7)/1, 0) as real7,\r\n      ROUND(sum(renc8)/1, 0) as renc8, ROUND(sum(real8)/1, 0) as real8,\r\n      ROUND(sum(renc9)/1, 0) as renc9, ROUND(sum(real9)/1, 0) as real9,\r\n      ROUND(sum(renc10)/1, 0) as renc10, ROUND(sum(real10)/1, 0) as real10,\r\n      ROUND(sum(renc11)/1, 0) as renc11, ROUND(sum(real11)/1, 0) as real11,\r\n      ROUND(sum(renc12)/1, 0) as renc12, ROUND(sum(real12)/1, 0) as real12\r\n      FROM dashboard.rencana_real_bulanan a\r\n      INNER JOIN dbref.t_dept_2025 b ON a.kddept=b.kddept\r\n      ${kanwilFilter}${kddeptFilter};`;\r\n\r\n    const encryptedQuery = Encrypt(rawQuery.replace(/\\n|\\s+/g, \" \").trim());\r\n\r\n    try {\r\n      setLoading(true);\r\n      const response = await axiosJWT.post(\r\n        process.env.NEXT_PUBLIC_GET_REFERENSI,\r\n        { query: encryptedQuery }\r\n      );\r\n      setDataRencanaReal(response.data.result || []);\r\n    } catch (err) {\r\n      setDataRencanaReal([]);\r\n      const { status, data } = err.response || {};\r\n      handleHttpError(\r\n        status,\r\n        data?.error || \"Terjadi Permasalahan Koneksi atau Server Backend\"\r\n      );\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, [selectedKanwil, selectedKddept]);\r\n\r\n  const colors = getThemeColors();\r\n  const data = dataRencanaReal[0] || {};\r\n\r\n  const rencana = Array.from(\r\n    { length: 12 },\r\n    (_, i) => (data[`renc${i + 1}`] || 0) / 1e12\r\n  );\r\n  const realisasi = Array.from(\r\n    { length: 12 },\r\n    (_, i) => (data[`real${i + 1}`] || 0) / 1e12\r\n  );\r\n\r\n  const series = [\r\n    { name: \"Target APBN\", data: rencana },\r\n    { name: \"Realisasi APBN\", data: realisasi },\r\n  ];\r\n\r\n  const options = {\r\n    chart: {\r\n      type: \"area\",\r\n      stacked: true,\r\n      animations: { speed: 300 },\r\n      toolbar: { show: false },\r\n      foreColor: colors.foreColor,\r\n    },\r\n    dataLabels: {\r\n      enabled: true,\r\n      formatter: (value) => formatTrillions(value),\r\n    },\r\n    xaxis: {\r\n      categories: [\r\n        \"Jan\",\r\n        \"Feb\",\r\n        \"Mar\",\r\n        \"Apr\",\r\n        \"Mei\",\r\n        \"Jun\",\r\n        \"Jul\",\r\n        \"Agt\",\r\n        \"Sep\",\r\n        \"Okt\",\r\n        \"Nov\",\r\n        \"Des\",\r\n      ],\r\n      labels: { style: { colors: colors.foreColor } },\r\n      axisBorder: { color: colors.borderColor },\r\n      axisTicks: { color: colors.borderColor },\r\n    },\r\n    yaxis: {\r\n      labels: {\r\n        style: { colors: colors.foreColor },\r\n        formatter: (value) => formatTrillions(value),\r\n      },\r\n    },\r\n    tooltip: {\r\n      theme: theme === \"dark\" ? \"dark\" : \"light\",\r\n      style: { fontSize: \"12px\" },\r\n      y: { formatter: (value) => formatTrillions(value) },\r\n    },\r\n    colors: [colors.primary, colors.success],\r\n    legend: {\r\n      position: \"top\",\r\n      horizontalAlign: \"center\",\r\n      labels: { colors: colors.textPrimary },\r\n      markers: { size: 8 },\r\n      itemMargin: { horizontal: 10, vertical: 5 },\r\n    },\r\n    grid: { borderColor: colors.gridColor, strokeDashArray: 0 },\r\n    stroke: { curve: \"smooth\" },\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card className=\"shadow-sm\">\r\n        <CardBody>\r\n          <Skeleton className=\"h-4 w-48 mb-2\" />\r\n          <Skeleton className=\"h-64 w-full\" />\r\n        </CardBody>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (!data.kddept) {\r\n    return (\r\n      <Card className=\"shadow-sm\">\r\n        <CardBody className=\"flex flex-col items-center justify-center py-12\">\r\n          <FileX className=\"w-12 h-12 text-default-400 mb-4\" />\r\n          <Chip\r\n            variant=\"flat\"\r\n            color=\"warning\"\r\n            startContent={<Database className=\"w-3 h-3\" />}\r\n            className=\"text-xs\"\r\n          >\r\n            Data Tidak Tersedia\r\n          </Chip>\r\n        </CardBody>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full h-full\">\r\n      <Chart\r\n        key={theme}\r\n        options={options}\r\n        series={series}\r\n        type=\"area\"\r\n        height=\"100%\"\r\n        width=\"100%\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AA8EQ;;AA5ER;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;AATA;;;;;;;;;AAWA,MAAM,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACpB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;;;;;;;KAJpB;AASS,SAAS,SAAS,KAAkC;QAAlC,EAAE,cAAc,EAAE,cAAc,EAAE,GAAlC;;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;IAExC,MAAM,kBAAkB,CAAC,SACvB,IAAI,KAAK,YAAY,CAAC,SAAS;YAC7B,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,UAAU;IAEtB,MAAM,iBAAiB;QACrB,MAAM,SAAS,UAAU;QACzB,OAAO;YACL,WAAW,SAAS,YAAY;YAChC,aAAa,SAAS,YAAY;YAClC,WAAW,SAAS,YAAY;YAChC,aAAa,SAAS,YAAY;YAClC,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,UAAU;QACd,IAAI,eACF,kBAAkB,mBAAmB,OACjC,AAAC,oBAAkC,OAAf,gBAAe,OACnC;QACN,IAAI,eACF,kBAAkB,mBAAmB,QACjC,AAAC,kBAAgC,OAAf,gBAAe,OACjC;QAEN,MAAM,WAAW,AAAC,mgCAeC,OAAf,cAA4B,OAAb,cAAa;QAEhC,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,CAAC,WAAW,KAAK,IAAI;QAEpE,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,SAAS,IAAI,yEAElC;gBAAE,OAAO;YAAe;YAE1B,mBAAmB,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QAC/C,EAAE,OAAO,KAAK;YACZ,mBAAmB,EAAE;YACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC;YAC1C,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EACZ,QACA,CAAA,iBAAA,2BAAA,KAAM,KAAK,KAAI;QAEnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,SAAS;IACf,MAAM,OAAO,eAAe,CAAC,EAAE,IAAI,CAAC;IAEpC,MAAM,UAAU,MAAM,IAAI,CACxB;QAAE,QAAQ;IAAG,GACb,CAAC,GAAG,IAAM,CAAC,IAAI,CAAC,AAAC,OAAY,OAAN,IAAI,GAAI,IAAI,CAAC,IAAI;IAE1C,MAAM,YAAY,MAAM,IAAI,CAC1B;QAAE,QAAQ;IAAG,GACb,CAAC,GAAG,IAAM,CAAC,IAAI,CAAC,AAAC,OAAY,OAAN,IAAI,GAAI,IAAI,CAAC,IAAI;IAG1C,MAAM,SAAS;QACb;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAkB,MAAM;QAAU;KAC3C;IAED,MAAM,UAAU;QACd,OAAO;YACL,MAAM;YACN,SAAS;YACT,YAAY;gBAAE,OAAO;YAAI;YACzB,SAAS;gBAAE,MAAM;YAAM;YACvB,WAAW,OAAO,SAAS;QAC7B;QACA,YAAY;YACV,SAAS;YACT,WAAW,CAAC,QAAU,gBAAgB;QACxC;QACA,OAAO;YACL,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;gBAAE,OAAO;oBAAE,QAAQ,OAAO,SAAS;gBAAC;YAAE;YAC9C,YAAY;gBAAE,OAAO,OAAO,WAAW;YAAC;YACxC,WAAW;gBAAE,OAAO,OAAO,WAAW;YAAC;QACzC;QACA,OAAO;YACL,QAAQ;gBACN,OAAO;oBAAE,QAAQ,OAAO,SAAS;gBAAC;gBAClC,WAAW,CAAC,QAAU,gBAAgB;YACxC;QACF;QACA,SAAS;YACP,OAAO,UAAU,SAAS,SAAS;YACnC,OAAO;gBAAE,UAAU;YAAO;YAC1B,GAAG;gBAAE,WAAW,CAAC,QAAU,gBAAgB;YAAO;QACpD;QACA,QAAQ;YAAC,OAAO,OAAO;YAAE,OAAO,OAAO;SAAC;QACxC,QAAQ;YACN,UAAU;YACV,iBAAiB;YACjB,QAAQ;gBAAE,QAAQ,OAAO,WAAW;YAAC;YACrC,SAAS;gBAAE,MAAM;YAAE;YACnB,YAAY;gBAAE,YAAY;gBAAI,UAAU;YAAE;QAC5C;QACA,MAAM;YAAE,aAAa,OAAO,SAAS;YAAE,iBAAiB;QAAE;QAC1D,QAAQ;YAAE,OAAO;QAAS;IAC5B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,yMAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;;kCACP,6LAAC,qNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,qNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAI5B;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,6LAAC,yMAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gBAAC,WAAU;;kCAClB,6LAAC,2MAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC,yMAAA,CAAA,OAAI;wBACH,SAAQ;wBACR,OAAM;wBACN,4BAAc,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAClC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAEC,SAAS;YACT,QAAQ;YACR,MAAK;YACL,QAAO;YACP,OAAM;WALD;;;;;;;;;;AASb;GA9LwB;;QAIJ,mJAAA,CAAA,WAAQ;;;MAJJ", "debugId": null}}]}