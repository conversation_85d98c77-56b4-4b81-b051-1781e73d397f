{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/utils/exportUtils.js"], "sourcesContent": ["// Utility for robust CSV export\r\nexport function exportToCSV(data, filename = \"data.csv\") {\r\n  if (!data || !data.length) return;\r\n  const replacer = (key, value) =>\r\n    value === null || value === undefined ? \"\" : value;\r\n  const header = Object.keys(data[0]);\r\n  const csv = [\r\n    header.join(\",\"),\r\n    ...data.map((row) =>\r\n      header\r\n        .map((fieldName) => JSON.stringify(row[fieldName], replacer))\r\n        .join(\",\")\r\n    ),\r\n  ].join(\"\\r\\n\");\r\n  const blob = new Blob([csv], { type: \"text/csv\" });\r\n  const url = URL.createObjectURL(blob);\r\n  const link = document.createElement(\"a\");\r\n  link.setAttribute(\"href\", url);\r\n  link.setAttribute(\"download\", filename);\r\n  link.style.visibility = \"hidden\";\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  URL.revokeObjectURL(url);\r\n}\r\n\r\n// Utility for robust Excel export using xlsx\r\nexport async function exportToExcel(data, filename = \"data.xlsx\") {\r\n  if (!data || !data.length) return;\r\n  const xlsx = await import(\"xlsx\");\r\n  const ws = xlsx.utils.json_to_sheet(data);\r\n  const wb = xlsx.utils.book_new();\r\n  xlsx.utils.book_append_sheet(wb, ws, \"Sheet1\");\r\n  const wbout = xlsx.write(wb, { bookType: \"xlsx\", type: \"array\" });\r\n  const blob = new Blob([wbout], { type: \"application/octet-stream\" });\r\n  const url = URL.createObjectURL(blob);\r\n  const link = document.createElement(\"a\");\r\n  link.setAttribute(\"href\", url);\r\n  link.setAttribute(\"download\", filename);\r\n  link.style.visibility = \"hidden\";\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  URL.revokeObjectURL(url);\r\n}\r\n\r\n// Utility for robust JSON export\r\nexport function exportToJSON(data, filename = \"data.json\") {\r\n  if (!data || !data.length) return;\r\n  const jsonContent = JSON.stringify(data, null, 2);\r\n  const blob = new Blob([jsonContent], { type: \"application/json\" });\r\n  const url = URL.createObjectURL(blob);\r\n  const link = document.createElement(\"a\");\r\n  link.setAttribute(\"href\", url);\r\n  link.setAttribute(\"download\", filename);\r\n  link.style.visibility = \"hidden\";\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  URL.revokeObjectURL(url);\r\n}\r\n\r\n// Utility for robust Text export (pretty-printed JSON as .txt)\r\nexport function exportToText(data, filename = \"data.txt\") {\r\n  if (!data || !data.length) return;\r\n  const textContent = JSON.stringify(data, null, 2);\r\n  const blob = new Blob([textContent], { type: \"text/plain\" });\r\n  const url = URL.createObjectURL(blob);\r\n  const link = document.createElement(\"a\");\r\n  link.setAttribute(\"href\", url);\r\n  link.setAttribute(\"download\", filename);\r\n  link.style.visibility = \"hidden\";\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  URL.revokeObjectURL(url);\r\n}\r\n\r\n// Utility for robust PDF export using jsPDF and autoTable\r\nexport async function exportToPDF(data, filename = \"data.pdf\") {\r\n  if (!data || !data.length) return;\r\n  const jsPDF = (await import(\"jspdf\")).default;\r\n  const autoTable = (await import(\"jspdf-autotable\")).default;\r\n  const doc = new jsPDF();\r\n  const header = Object.keys(data[0]);\r\n  const rows = data.map((row) => header.map((field) => row[field]));\r\n  autoTable(doc, {\r\n    head: [header],\r\n    body: rows,\r\n    styles: { fontSize: 8 },\r\n    headStyles: { fillColor: [41, 128, 185] },\r\n  });\r\n  doc.save(filename);\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;;;;AACzB,SAAS,YAAY,IAAI;QAAE,WAAA,iEAAW;IAC3C,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;IAC3B,MAAM,WAAW,CAAC,KAAK,QACrB,UAAU,QAAQ,UAAU,YAAY,KAAK;IAC/C,MAAM,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IAClC,MAAM,MAAM;QACV,OAAO,IAAI,CAAC;WACT,KAAK,GAAG,CAAC,CAAC,MACX,OACG,GAAG,CAAC,CAAC,YAAc,KAAK,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,WAClD,IAAI,CAAC;KAEX,CAAC,IAAI,CAAC;IACP,MAAM,OAAO,IAAI,KAAK;QAAC;KAAI,EAAE;QAAE,MAAM;IAAW;IAChD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY;IAC9B,KAAK,KAAK,CAAC,UAAU,GAAG;IACxB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,eAAe,cAAc,IAAI;QAAE,WAAA,iEAAW;IACnD,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;IAC3B,MAAM,OAAO;IACb,MAAM,KAAK,KAAK,KAAK,CAAC,aAAa,CAAC;IACpC,MAAM,KAAK,KAAK,KAAK,CAAC,QAAQ;IAC9B,KAAK,KAAK,CAAC,iBAAiB,CAAC,IAAI,IAAI;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,IAAI;QAAE,UAAU;QAAQ,MAAM;IAAQ;IAC/D,MAAM,OAAO,IAAI,KAAK;QAAC;KAAM,EAAE;QAAE,MAAM;IAA2B;IAClE,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY;IAC9B,KAAK,KAAK,CAAC,UAAU,GAAG;IACxB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,aAAa,IAAI;QAAE,WAAA,iEAAW;IAC5C,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;IAC3B,MAAM,cAAc,KAAK,SAAS,CAAC,MAAM,MAAM;IAC/C,MAAM,OAAO,IAAI,KAAK;QAAC;KAAY,EAAE;QAAE,MAAM;IAAmB;IAChE,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY;IAC9B,KAAK,KAAK,CAAC,UAAU,GAAG;IACxB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,aAAa,IAAI;QAAE,WAAA,iEAAW;IAC5C,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;IAC3B,MAAM,cAAc,KAAK,SAAS,CAAC,MAAM,MAAM;IAC/C,MAAM,OAAO,IAAI,KAAK;QAAC;KAAY,EAAE;QAAE,MAAM;IAAa;IAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY;IAC9B,KAAK,KAAK,CAAC,UAAU,GAAG;IACxB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,eAAe,YAAY,IAAI;QAAE,WAAA,iEAAW;IACjD,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;IAC3B,MAAM,QAAQ,CAAC,mJAAqB,EAAE,OAAO;IAC7C,MAAM,YAAY,CAAC,wKAA+B,EAAE,OAAO;IAC3D,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IAClC,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,MAAQ,OAAO,GAAG,CAAC,CAAC,QAAU,GAAG,CAAC,MAAM;IAC/D,UAAU,KAAK;QACb,MAAM;YAAC;SAAO;QACd,MAAM;QACN,QAAQ;YAAE,UAAU;QAAE;QACtB,YAAY;YAAE,WAAW;gBAAC;gBAAI;gBAAK;aAAI;QAAC;IAC1C;IACA,IAAI,IAAI,CAAC;AACX", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/utils/filterUtils.js"], "sourcesContent": ["import kdoutputData from \"@/data/reference/functional/Kdoutput.json\";\r\n\r\n/**\r\n * Get filtered data based on parent selections\r\n * This function provides hierarchical filtering for program -> kegiatan -> output -> suboutput -> komponen -> subkomponen\r\n */\r\n\r\n// Utility function to get unique values from array of objects\r\nconst getUniqueValues = (data, key, nameKey) => {\r\n  const unique = new Map();\r\n  data.forEach((item) => {\r\n    if (item[key] && !unique.has(item[key])) {\r\n      unique.set(item[key], {\r\n        value: item[key],\r\n        label: item[nameKey] || item[key],\r\n      });\r\n    }\r\n  });\r\n  return Array.from(unique.values());\r\n};\r\n\r\n/**\r\n * Get programs based on department and unit selection\r\n */\r\nexport const getFilteredPrograms = (dept, kdunit) => {\r\n  let filteredData = kdoutputData;\r\n\r\n  if (dept && dept !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kddept === dept);\r\n  }\r\n\r\n  if (kdunit && kdunit !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kdunit === kdunit);\r\n  }\r\n\r\n  return getUniqueValues(filteredData, \"kdprogram\", \"nmprogram\");\r\n};\r\n\r\n/**\r\n * Get activities (kegiatan) based on department, unit, and program selection\r\n */\r\nexport const getFilteredKegiatan = (dept, kdunit, program) => {\r\n  let filteredData = kdoutputData;\r\n\r\n  if (dept && dept !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kddept === dept);\r\n  }\r\n\r\n  if (kdunit && kdunit !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kdunit === kdunit);\r\n  }\r\n\r\n  if (program && program !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kdprogram === program);\r\n  }\r\n\r\n  return getUniqueValues(filteredData, \"kdgiat\", \"nmgiat\");\r\n};\r\n\r\n/**\r\n * Get outputs based on department, unit, program, and kegiatan selection\r\n */\r\nexport const getFilteredOutputs = (dept, kdunit, program, kegiatan) => {\r\n  let filteredData = kdoutputData;\r\n\r\n  if (dept && dept !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kddept === dept);\r\n  }\r\n\r\n  if (kdunit && kdunit !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kdunit === kdunit);\r\n  }\r\n\r\n  if (program && program !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kdprogram === program);\r\n  }\r\n\r\n  if (kegiatan && kegiatan !== \"XX\") {\r\n    filteredData = filteredData.filter((item) => item.kdgiat === kegiatan);\r\n  }\r\n\r\n  return getUniqueValues(filteredData, \"kdoutput\", \"nmoutput\");\r\n};\r\n\r\n// Note: Since there's no child data for suboutput, komponen, and subkomponen,\r\n// these filters will use the existing component logic without parent-child filtering\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA;;;CAGC,GAED,8DAA8D;AAC9D,MAAM,kBAAkB,CAAC,MAAM,KAAK;IAClC,MAAM,SAAS,IAAI;IACnB,KAAK,OAAO,CAAC,CAAC;QACZ,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG;YACvC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;gBACpB,OAAO,IAAI,CAAC,IAAI;gBAChB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI;YACnC;QACF;IACF;IACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM;AACjC;AAKO,MAAM,sBAAsB,CAAC,MAAM;IACxC,IAAI,eAAe,mHAAA,CAAA,UAAY;IAE/B,IAAI,QAAQ,SAAS,MAAM;QACzB,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,IAAI,UAAU,WAAW,MAAM;QAC7B,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,OAAO,gBAAgB,cAAc,aAAa;AACpD;AAKO,MAAM,sBAAsB,CAAC,MAAM,QAAQ;IAChD,IAAI,eAAe,mHAAA,CAAA,UAAY;IAE/B,IAAI,QAAQ,SAAS,MAAM;QACzB,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,IAAI,UAAU,WAAW,MAAM;QAC7B,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,IAAI,WAAW,YAAY,MAAM;QAC/B,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,KAAK;IAClE;IAEA,OAAO,gBAAgB,cAAc,UAAU;AACjD;AAKO,MAAM,qBAAqB,CAAC,MAAM,QAAQ,SAAS;IACxD,IAAI,eAAe,mHAAA,CAAA,UAAY;IAE/B,IAAI,QAAQ,SAAS,MAAM;QACzB,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,IAAI,UAAU,WAAW,MAAM;QAC7B,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,IAAI,WAAW,YAAY,MAAM;QAC/B,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,KAAK;IAClE;IAEA,IAAI,YAAY,aAAa,MAAM;QACjC,eAAe,aAAa,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;IAC/D;IAEA,OAAO,gBAAgB,cAAc,YAAY;AACnD,GAEA,8EAA8E;CAC9E,qFAAqF", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/hooks/useInquiryState.js"], "sourcesContent": ["\"use client\";\r\nimport { useState, useContext } from \"react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\n\r\nexport default function useInquiryState() {\r\n  const {\r\n    role,\r\n    telp,\r\n    verified,\r\n    loadingExcell,\r\n    setloadingExcell,\r\n    kdkppn: kodekppn,\r\n    kdkanwil: kodekanwil,\r\n    settampilAI,\r\n  } = useContext(MyContext);\r\n\r\n  // Modal states\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [showModalKedua, setShowModalKedua] = useState(false);\r\n  const [showModalsql, setShowModalsql] = useState(false);\r\n  const [showModalApbn, setShowModalApbn] = useState(false);\r\n  const [showModalAkumulasi, setShowModalAkumulasi] = useState(false);\r\n  const [showModalBulanan, setShowModalBulanan] = useState(false);\r\n  const [showModalBlokir, setShowModalBlokir] = useState(false);\r\n  const [showModalPN, setShowModalPN] = useState(false);\r\n  const [showModalPN2, setShowModalPN2] = useState(false);\r\n  const [showModalJnsblokir, setShowModalJnsblokir] = useState(false);\r\n  const [showModalPDF, setShowModalPDF] = useState(false);\r\n  const [showModalsimpan, setShowModalsimpan] = useState(false);\r\n\r\n  // Form states\r\n  const [jenlap, setJenlap] = useState(\"2\");\r\n  const [thang, setThang] = useState(new Date().getFullYear().toString());\r\n  const [tanggal, setTanggal] = useState(false);\r\n  const [cutoff, setCutoff] = useState(\"0\");\r\n  const [pembulatan, setPembulatan] = useState(\"1\");\r\n  const [akumulatif, setAkumulatif] = useState(\"0\");\r\n  const [selectedFormat, setSelectedFormat] = useState(\"pdf\");\r\n  const [export2, setExport2] = useState(false);\r\n  const [loadingStatus, setLoadingStatus] = useState(false);\r\n  const [showFormatDropdown, setShowFormatDropdown] = useState(false);\r\n\r\n  // Filter visibility states\r\n  const [kddept, setKddept] = useState(true);\r\n  const [unit, setUnit] = useState(false);\r\n  const [kddekon, setKddekon] = useState(false);\r\n  const [kdlokasi, setKdlokasi] = useState(false);\r\n  const [kdkabkota, setKdkabkota] = useState(false);\r\n  const [kdkanwil, setKdkanwil] = useState(false);\r\n  const [kdkppn, setKdkppn] = useState(false);\r\n  const [kdsatker, setKdsatker] = useState(false);\r\n  const [kdfungsi, setKdfungsi] = useState(false);\r\n  const [kdsfungsi, setKdsfungsi] = useState(false);\r\n  const [kdprogram, setKdprogram] = useState(false);\r\n  const [kdgiat, setKdgiat] = useState(false);\r\n  const [kdoutput, setKdoutput] = useState(false);\r\n  const [kdsoutput, setKdsoutput] = useState(false);\r\n  const [kdkomponen, setKdkomponen] = useState(false);\r\n  const [kdskomponen, setKdskomponen] = useState(false);\r\n  const [kdakun, setKdakun] = useState(false);\r\n  const [kdsdana, setKdsdana] = useState(false);\r\n  const [kdregister, setKdregister] = useState(false);\r\n\r\n  // Special filter switches\r\n  const [kdInflasi, setKdInflasi] = useState(false);\r\n  const [kdIkn, setKdIkn] = useState(false);\r\n  const [kdKemiskinan, setKdKemiskinan] = useState(false);\r\n  const [KdPRI, setKdPRI] = useState(false);\r\n  const [KdPangan, setKdPangan] = useState(false);\r\n  const [KdStunting, setKdStunting] = useState(false);\r\n  const [KdPemilu, setKdPemilu] = useState(false);\r\n  const [KdTema, setKdTema] = useState(false);\r\n  const [KdPN, setKdPN] = useState(false);\r\n  const [KdPP, setKdPP] = useState(false);\r\n  const [KdKegPP, setKdKegPP] = useState(false);\r\n  const [KdMP, setKdMP] = useState(false);\r\n\r\n  // Filter values\r\n  const [dept, setDept] = useState(\"000\");\r\n  const [deptkondisi, setDeptkondisi] = useState(\"\"); // NEW: for advanced Kementerian filter\r\n  const [katadept, setKatadept] = useState(\"\"); // NEW: for advanced Kementerian filter\r\n  const [kdunit, setKdunit] = useState(\"XX\");\r\n  const [unitkondisi, setUnitkondisi] = useState(\"\"); // NEW: for advanced Unit filter\r\n  const [kataunit, setKataunit] = useState(\"\"); // NEW: for advanced Unit filter\r\n  const [dekon, setDekon] = useState(\"XX\");\r\n  const [dekonkondisi, setDekonkondisi] = useState(\"\"); // NEW: for advanced Dekon filter\r\n  const [katadekon, setKatadekon] = useState(\"\"); // NEW: for advanced Dekon filter\r\n  const [prov, setProv] = useState(\"XX\");\r\n  const [lokasikondisi, setLokasikondisi] = useState(\"\"); // NEW: for advanced Lokasi filter\r\n  const [katalokasi, setKatalokasi] = useState(\"\"); // NEW: for advanced Lokasi filter\r\n  const [kabkota, setKabkota] = useState(\"XX\");\r\n  const [kabkotakondisi, setKabkotakondisi] = useState(\"\"); // NEW: for advanced Kabkota filter\r\n  const [katakabkota, setKatakabkota] = useState(\"\"); // NEW: for advanced Kabkota filter\r\n  const [kanwil, setKanwil] = useState(\"XX\");\r\n  const [kanwilkondisi, setKanwilkondisi] = useState(\"\"); // NEW: for advanced Kanwil filter\r\n  const [katakanwil, setKatakanwil] = useState(\"\"); // NEW: for advanced Kanwil filter\r\n  const [kppn, setKppn] = useState(\"XX\");\r\n  const [kppnkondisi, setKppnkondisi] = useState(\"\"); // NEW: for advanced KPPN filter\r\n  const [katakppn, setKatakppn] = useState(\"\"); // NEW: for advanced KPPN filter\r\n  const [satker, setSatker] = useState(\"XX\");\r\n  const [satkerkondisi, setSatkerkondisi] = useState(\"\"); // NEW: for advanced Satker filter\r\n  const [katasatker, setKatasatker] = useState(\"\"); // NEW: for advanced Satker filter\r\n  const [fungsi, setFungsi] = useState(\"XX\");\r\n  const [fungsikondisi, setFungsikondisi] = useState(\"\"); // NEW: for advanced Fungsi filter\r\n  const [katafungsi, setKatafungsi] = useState(\"\"); // NEW: for advanced Fungsi filter\r\n  const [sfungsi, setSfungsi] = useState(\"XX\");\r\n  const [subfungsikondisi, setSubfungsikondisi] = useState(\"\"); // NEW: for advanced Subfungsi filter\r\n  const [katasubfungsi, setKatasubfungsi] = useState(\"\"); // NEW: for advanced Subfungsi filter\r\n  const [program, setProgram] = useState(\"XX\");\r\n  const [programkondisi, setProgramkondisi] = useState(\"\"); // NEW: for advanced Program filter\r\n  const [kataprogram, setKataprogram] = useState(\"\"); // NEW: for advanced Program filter\r\n  const [giat, setGiat] = useState(\"XX\");\r\n  const [giatkondisi, setGiatkondisi] = useState(\"\"); // NEW: for advanced Giat filter\r\n  const [katagiat, setKatagiat] = useState(\"\"); // NEW: for advanced Giat filter\r\n  const [output, setOutput] = useState(\"XX\");\r\n  const [outputkondisi, setOutputkondisi] = useState(\"\"); // NEW: for advanced Output filter\r\n  const [kataoutput, setKataoutput] = useState(\"\"); // NEW: for advanced Output filter\r\n  const [soutput, setsOutput] = useState(\"XX\");\r\n  const [soutputkondisi, setSoutputkondisi] = useState(\"\"); // NEW: for advanced Soutput filter\r\n  const [katasoutput, setKatasoutput] = useState(\"\"); // NEW: for advanced Soutput filter\r\n  const [komponen, setKomponen] = useState(\"XX\");\r\n  const [komponenkondisi, setKomponenkondisi] = useState(\"\"); // NEW: for advanced Komponen filter\r\n  const [katakomponen, setKatakomponen] = useState(\"\"); // NEW: for advanced Komponen filter\r\n  const [skomponen, setSkomponen] = useState(\"XX\");\r\n  const [skomponenkondisi, setSkomponenkondisi] = useState(\"\"); // NEW: for advanced Subkomponen filter\r\n  const [kataskomponen, setKataskomponen] = useState(\"\"); // NEW: for advanced Subkomponen filter\r\n  const [akun, setAkun] = useState(\"AKUN\");\r\n  const [akunkondisi, setAkunkondisi] = useState(\"\"); // NEW: for advanced Akun filter\r\n  const [kataakun, setKataakun] = useState(\"\"); // NEW: for advanced Akun filter\r\n  const [sdana, setSdana] = useState(\"XX\");\r\n  const [sdanakondisi, setSdanakondisi] = useState(\"\"); // NEW: for advanced Sdana filter\r\n  const [katasdana, setKatasdana] = useState(\"\"); // NEW: for advanced Sdana filter\r\n  const [register, setRegister] = useState(\"XX\");\r\n  const [registerkondisi, setRegisterkondisi] = useState(\"\"); // NEW: for advanced Register filter\r\n  const [kataregister, setKataregister] = useState(\"\"); // NEW: for advanced Register filter\r\n  const [PN, setPN] = useState(\"XX\");\r\n  const [PP, setPP] = useState(\"XX\");\r\n  const [PRI, setPRI] = useState(\"XX\");\r\n  const [MP, setMP] = useState(\"XX\");\r\n  const [Tema, setTema] = useState(\"XX\");\r\n  const [Inflasi, setInflasi] = useState(\"XX\");\r\n  const [Stunting, setStunting] = useState(\"XX\");\r\n  const [Miskin, setMiskin] = useState(\"XX\");\r\n  const [Pemilu, setPemilu] = useState(\"XX\");\r\n  const [Ikn, setIkn] = useState(\"XX\");\r\n  const [Pangan, setPangan] = useState(\"XX\");\r\n\r\n  // Radio states\r\n  const [deptradio, setDeptradio] = useState(\"1\");\r\n  const [unitradio, setUnitradio] = useState(\"1\");\r\n  const [dekonradio, setDekonradio] = useState(\"1\");\r\n  const [locradio, setLocradio] = useState(\"1\"); // Changed from provradio to locradio\r\n  const [kabkotaradio, setKabkotaradio] = useState(\"1\");\r\n  const [kanwilradio, setKanwilradio] = useState(\"1\");\r\n  const [kppnradio, setKppnradio] = useState(\"1\");\r\n  const [satkerradio, setSatkerradio] = useState(\"1\");\r\n  const [fungsiradio, setFungsiradio] = useState(\"1\");\r\n  const [subfungsiradio, setSubfungsiradio] = useState(\"1\");\r\n  const [programradio, setProgramradio] = useState(\"1\");\r\n  const [kegiatanradio, setKegiatanradio] = useState(\"1\");\r\n  const [outputradio, setOutputradio] = useState(\"1\");\r\n  const [soutputradio, setsOutputradio] = useState(\"1\");\r\n  const [komponenradio, setKomponenradio] = useState(\"1\");\r\n  const [skomponenradio, setSkomponenradio] = useState(\"1\");\r\n  const [akunradio, setAkunradio] = useState(\"1\");\r\n  const [sdanaradio, setSdanaradio] = useState(\"1\");\r\n  const [registerradio, setRegisterradio] = useState(\"1\");\r\n  const [inflasiradio, setInflasiradio] = useState(\"1\");\r\n  const [iknradio, setIknradio] = useState(\"1\");\r\n  const [kemiskinanradio, setKemiskinanradio] = useState(\"1\");\r\n  const [priradio, setPriradio] = useState(\"1\");\r\n  const [panganradio, setPanganradio] = useState(\"1\");\r\n  const [stuntingradio, setStuntingradio] = useState(\"1\");\r\n  const [pemiluradio, setPemiluradio] = useState(\"1\");\r\n  const [pnradio, setPnradio] = useState(\"1\");\r\n  const [ppradio, setPpradio] = useState(\"1\");\r\n  const [mpradio, setMpradio] = useState(\"1\");\r\n  const [temaradio, setTemaradio] = useState(\"1\");\r\n\r\n  // Special filter option states\r\n  const [opsidept, setOpsidept] = useState(\"pilihdept\");\r\n  const [opsiInflasi, setOpsiInflasi] = useState(\"pilihInflasi\");\r\n  const [opsiIkn, setOpsiIkn] = useState(\"pilihikn\");\r\n  const [opsiKemiskinan, setOpsiKemiskinan] = useState(\"pilihKemiskinan\");\r\n\r\n  // SQL state\r\n  const [sql, setSql] = useState(\"\");\r\n  const [from, setFrom] = useState(\"\");\r\n  const [select, setSelect] = useState(\r\n    \", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR\"\r\n  );\r\n\r\n  // Kegiatan Prioritas filter value and radio\r\n  const [kegiatanprioritas, setKegiatanPrioritas] = useState(\"XX\");\r\n  const [kegiatanprioritasradio, setKegiatanPrioritasRadio] = useState(\"1\");\r\n\r\n  return {\r\n    // Context values\r\n    role,\r\n    telp,\r\n    verified,\r\n    loadingExcell,\r\n    setloadingExcell,\r\n    kodekppn,\r\n    kodekanwil,\r\n    settampilAI,\r\n\r\n    // Modal states\r\n    showModal,\r\n    setShowModal,\r\n    showModalKedua,\r\n    setShowModalKedua,\r\n    showModalsql,\r\n    setShowModalsql,\r\n    showModalApbn,\r\n    setShowModalApbn,\r\n    showModalAkumulasi,\r\n    setShowModalAkumulasi,\r\n    showModalBulanan,\r\n    setShowModalBulanan,\r\n    showModalBlokir,\r\n    setShowModalBlokir,\r\n    showModalPN,\r\n    setShowModalPN,\r\n    showModalPN2,\r\n    setShowModalPN2,\r\n    showModalJnsblokir,\r\n    setShowModalJnsblokir,\r\n    showModalPDF,\r\n    setShowModalPDF,\r\n    showModalsimpan,\r\n    setShowModalsimpan,\r\n\r\n    // Form states\r\n    jenlap,\r\n    setJenlap,\r\n    thang,\r\n    setThang,\r\n    tanggal,\r\n    setTanggal,\r\n    cutoff,\r\n    setCutoff,\r\n    pembulatan,\r\n    setPembulatan,\r\n    akumulatif,\r\n    setAkumulatif,\r\n    selectedFormat,\r\n    setSelectedFormat,\r\n    export2,\r\n    setExport2,\r\n    loadingStatus,\r\n    setLoadingStatus,\r\n    showFormatDropdown,\r\n    setShowFormatDropdown,\r\n\r\n    // Filter visibility states\r\n    kddept,\r\n    setKddept,\r\n    unit,\r\n    setUnit,\r\n    kddekon,\r\n    setKddekon,\r\n    kdlokasi,\r\n    setKdlokasi,\r\n    kdkabkota,\r\n    setKdkabkota,\r\n    kdkanwil,\r\n    setKdkanwil,\r\n    kdkppn,\r\n    setKdkppn,\r\n    kdsatker,\r\n    setKdsatker,\r\n    kdfungsi,\r\n    setKdfungsi,\r\n    kdsfungsi,\r\n    setKdsfungsi,\r\n    kdprogram,\r\n    setKdprogram,\r\n    kdgiat,\r\n    setKdgiat,\r\n    kdoutput,\r\n    setKdoutput,\r\n    kdsoutput,\r\n    setKdsoutput,\r\n    kdkomponen,\r\n    setKdkomponen,\r\n    kdskomponen,\r\n    setKdskomponen,\r\n    kdakun,\r\n    setKdakun,\r\n    kdsdana,\r\n    setKdsdana,\r\n    kdregister,\r\n    setKdregister,\r\n    kdInflasi,\r\n    setKdInflasi,\r\n    kdIkn,\r\n    setKdIkn,\r\n    kdKemiskinan,\r\n    setKdKemiskinan,\r\n    KdPRI,\r\n    setKdPRI,\r\n    KdPangan,\r\n    setKdPangan,\r\n    KdStunting,\r\n    setKdStunting,\r\n    KdPemilu,\r\n    setKdPemilu,\r\n    KdTema,\r\n    setKdTema,\r\n    KdPN,\r\n    setKdPN,\r\n    KdPP,\r\n    setKdPP,\r\n    KdKegPP,\r\n    setKdKegPP,\r\n    KdMP,\r\n    setKdMP,\r\n\r\n    // Filter values\r\n    dept,\r\n    setDept,\r\n    deptkondisi,\r\n    setDeptkondisi,\r\n    katadept,\r\n    setKatadept,\r\n    kdunit,\r\n    setKdunit,\r\n    unitkondisi,\r\n    setUnitkondisi,\r\n    kataunit,\r\n    setKataunit,\r\n    dekon,\r\n    setDekon,\r\n    dekonkondisi,\r\n    setDekonkondisi,\r\n    katadekon,\r\n    setKatadekon,\r\n    prov,\r\n    setProv,\r\n    lokasikondisi,\r\n    setLokasikondisi,\r\n    katalokasi,\r\n    setKatalokasi,\r\n    kabkota,\r\n    setKabkota,\r\n    kabkotakondisi,\r\n    setKabkotakondisi,\r\n    katakabkota,\r\n    setKatakabkota,\r\n    kanwil,\r\n    setKanwil,\r\n    kanwilkondisi,\r\n    setKanwilkondisi,\r\n    katakanwil,\r\n    setKatakanwil,\r\n    kppn,\r\n    setKppn,\r\n    kppnkondisi,\r\n    setKppnkondisi,\r\n    katakppn,\r\n    setKatakppn,\r\n    satker,\r\n    setSatker,\r\n    satkerkondisi,\r\n    setSatkerkondisi,\r\n    katasatker,\r\n    setKatasatker,\r\n    fungsi,\r\n    setFungsi,\r\n    fungsikondisi,\r\n    setFungsikondisi,\r\n    katafungsi,\r\n    setKatafungsi,\r\n    sfungsi,\r\n    setSfungsi,\r\n    subfungsikondisi,\r\n    setSubfungsikondisi,\r\n    katasubfungsi,\r\n    setKatasubfungsi,\r\n    program,\r\n    setProgram,\r\n    programkondisi,\r\n    setProgramkondisi,\r\n    kataprogram,\r\n    setKataprogram,\r\n    giat,\r\n    setGiat,\r\n    giatkondisi,\r\n    setGiatkondisi,\r\n    katagiat,\r\n    setKatagiat,\r\n    output,\r\n    setOutput,\r\n    outputkondisi,\r\n    setOutputkondisi,\r\n    kataoutput,\r\n    setKataoutput,\r\n    soutput,\r\n    setsOutput,\r\n    soutputkondisi,\r\n    setSoutputkondisi,\r\n    katasoutput,\r\n    setKatasoutput,\r\n    komponen,\r\n    setKomponen,\r\n    komponenkondisi,\r\n    setKomponenkondisi,\r\n    katakomponen,\r\n    setKatakomponen,\r\n    skomponen,\r\n    setSkomponen,\r\n    skomponenkondisi,\r\n    setSkomponenkondisi,\r\n    kataskomponen,\r\n    setKataskomponen,\r\n    akun,\r\n    setAkun,\r\n    akunkondisi,\r\n    setAkunkondisi,\r\n    kataakun,\r\n    setKataakun,\r\n    sdana,\r\n    setSdana,\r\n    sdanakondisi,\r\n    setSdanakondisi,\r\n    katasdana,\r\n    setKatasdana,\r\n    register,\r\n    setRegister,\r\n    registerkondisi,\r\n    setRegisterkondisi,\r\n    kataregister,\r\n    setKataregister,\r\n    PN,\r\n    setPN,\r\n    PP,\r\n    setPP,\r\n    PRI,\r\n    setPRI,\r\n    MP,\r\n    setMP,\r\n    Tema,\r\n    setTema,\r\n    Inflasi,\r\n    setInflasi,\r\n    Stunting,\r\n    setStunting,\r\n    Miskin,\r\n    setMiskin,\r\n    Pemilu,\r\n    setPemilu,\r\n    Ikn,\r\n    setIkn,\r\n    Pangan,\r\n    setPangan,\r\n\r\n    // Radio states\r\n    deptradio,\r\n    setDeptradio,\r\n    unitradio,\r\n    setUnitradio,\r\n    dekonradio,\r\n    setDekonradio,\r\n    locradio,\r\n    setLocradio,\r\n    kabkotaradio,\r\n    setKabkotaradio,\r\n    kanwilradio,\r\n    setKanwilradio,\r\n    kppnradio,\r\n    setKppnradio,\r\n    satkerradio,\r\n    setSatkerradio,\r\n    fungsiradio,\r\n    setFungsiradio,\r\n    subfungsiradio,\r\n    setSubfungsiradio,\r\n    programradio,\r\n    setProgramradio,\r\n    kegiatanradio,\r\n    setKegiatanradio,\r\n    outputradio,\r\n    setOutputradio,\r\n    soutputradio,\r\n    setsOutputradio,\r\n    komponenradio,\r\n    setKomponenradio,\r\n    skomponenradio,\r\n    setSkomponenradio,\r\n    akunradio,\r\n    setAkunradio,\r\n    sdanaradio,\r\n    setSdanaradio,\r\n    registerradio,\r\n    setRegisterradio,\r\n    inflasiradio,\r\n    setInflasiradio,\r\n    iknradio,\r\n    setIknradio,\r\n    kemiskinanradio,\r\n    setKemiskinanradio,\r\n    priradio,\r\n    setPriradio,\r\n    panganradio,\r\n    setPanganradio,\r\n    stuntingradio,\r\n    setStuntingradio,\r\n    pemiluradio,\r\n    setPemiluradio,\r\n    pnradio,\r\n    setPnradio,\r\n    ppradio,\r\n    setPpradio,\r\n    mpradio,\r\n    setMpradio,\r\n    temaradio,\r\n    setTemaradio,\r\n\r\n    // Special filter option states\r\n    opsidept,\r\n    setOpsidept,\r\n    opsiInflasi,\r\n    setOpsiInflasi,\r\n    opsiIkn,\r\n    setOpsiIkn,\r\n    opsiKemiskinan,\r\n    setOpsiKemiskinan,\r\n\r\n    // SQL state\r\n    sql,\r\n    setSql,\r\n    from,\r\n    setFrom,\r\n    select,\r\n    setSelect,\r\n\r\n    // Kegiatan Prioritas filter value and radio\r\n    kegiatanprioritas,\r\n    setKegiatanPrioritas,\r\n    kegiatanprioritasradio,\r\n    setKegiatanPrioritasRadio,\r\n  };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AACA;AACA;;AAFA;;;AAIe,SAAS;;IACtB,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,QAAQ,QAAQ,EAChB,UAAU,UAAU,EACpB,WAAW,EACZ,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IAExB,eAAe;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,cAAc;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,QAAQ;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,2BAA2B;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,gBAAgB;IAChB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uCAAuC;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uCAAuC;IACrF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,iCAAiC;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,iCAAiC;IACjF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mCAAmC;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mCAAmC;IACvF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IACpF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,qCAAqC;IACnG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,qCAAqC;IAC7F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mCAAmC;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mCAAmC;IACvF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IAC1F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,kCAAkC;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mCAAmC;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,mCAAmC;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oCAAoC;IAChG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oCAAoC;IAC1F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uCAAuC;IACrG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uCAAuC;IAC/F,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gCAAgC;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,iCAAiC;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,iCAAiC;IACjF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oCAAoC;IAChG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oCAAoC;IAC1F,MAAM,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7B,MAAM,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7B,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,eAAe;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,qCAAqC;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,+BAA+B;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,YAAY;IACZ,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjC;IAGF,4CAA4C;IAC5C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,OAAO;QACL,iBAAiB;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,2BAA2B;QAC3B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,gBAAgB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,+BAA+B;QAC/B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;QAEA,4CAA4C;QAC5C;QACA;QACA;QACA;IACF;AACF;GA3hBwB", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/hooks/useQueryBuilderModular.js"], "sourcesContent": ["/**\r\n * Modern useQueryBuilder Hook - Modular and Scalable\r\n *\r\n * This replaces the 1800+ line monolithic query builder with a clean,\r\n * modular approach using separate filter classes.\r\n */\r\n\r\n\"use client\";\r\nimport { useState, useEffect, useMemo, useCallback } from \"react\";\r\nimport { QueryBuilder } from \"../filters\";\r\n\r\nexport default function useQueryBuilder(inquiryState) {\r\n  const [queryCache, setQueryCache] = useState({});\r\n\r\n  // Initialize query builder instance\r\n  const queryBuilder = useMemo(() => new QueryBuilder(), []);\r\n\r\n  // Extract key state for dependency tracking\r\n  const {\r\n    thang,\r\n    jenlap,\r\n    cutoff,\r\n    tanggal,\r\n    akumulatif,\r\n    pembulatan,\r\n    // Filter switches and values are automatically handled by the filter modules\r\n    setFrom,\r\n    setSelect,\r\n    setSql,\r\n  } = inquiryState;\r\n\r\n  /**\r\n   * Build the complete SQL query\r\n   */\r\n  const buildQuery = () => {\r\n    try {\r\n      const query = queryBuilder.buildQuery(inquiryState);\r\n\r\n      // Update state components for backward compatibility\r\n      const preview = queryBuilder.generateSqlPreview(inquiryState);\r\n      setFrom && setFrom(preview.fromClause);\r\n      setSelect && setSelect(preview.selectClause);\r\n      setSql && setSql(query);\r\n\r\n      return query;\r\n    } catch (error) {\r\n      console.error(\"Error building query:\", error);\r\n      return \"\";\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get query performance metrics\r\n   */\r\n  const getQueryPerformanceMetrics = () => {\r\n    return queryBuilder.getQueryPerformanceMetrics(inquiryState);\r\n  };\r\n\r\n  /**\r\n   * Generate SQL preview without full execution\r\n   */\r\n  const generateSqlPreview = () => {\r\n    return queryBuilder.generateSqlPreview(inquiryState);\r\n  };\r\n\r\n  /**\r\n   * Validate current query\r\n   */\r\n  const validateQuery = (query = buildQuery) => {\r\n    return queryBuilder.validateQuery(query);\r\n  };\r\n\r\n  /**\r\n   * Get filter statistics\r\n   */\r\n  const getFilterStats = () => {\r\n    return queryBuilder.filterBuilder.getFilterStats(inquiryState);\r\n  };\r\n\r\n  /**\r\n   * Check if specific filter is enabled\r\n   */\r\n  const isFilterEnabled = (filterName) => {\r\n    return queryBuilder.filterBuilder.isFilterEnabled(filterName, inquiryState);\r\n  };\r\n\r\n  /**\r\n   * Get all available filters\r\n   */\r\n  const getAvailableFilters = () => {\r\n    return queryBuilder.filterBuilder.getAvailableFilters();\r\n  };\r\n\r\n  /**\r\n   * Build specific filter only\r\n   */\r\n  const buildFilter = (filterName) => {\r\n    return queryBuilder.filterBuilder.buildFilter(filterName, inquiryState);\r\n  };\r\n\r\n  /**\r\n   * Debug filter behavior\r\n   */\r\n  const debugFilter = (filterName) => {\r\n    const filterResult = buildFilter(filterName);\r\n    const isEnabled = isFilterEnabled(filterName);\r\n\r\n    console.log(`🔍 Debug Filter: ${filterName}`, {\r\n      isEnabled,\r\n      columns: filterResult.columns,\r\n      joinClause: filterResult.joinClause,\r\n      whereConditions: filterResult.whereConditions,\r\n      groupBy: filterResult.groupBy,\r\n    });\r\n\r\n    return { filterName, isEnabled, ...filterResult };\r\n  };\r\n\r\n  /**\r\n   * Debug special filters for jenlap = 6 and 7\r\n   */\r\n  const debugSpecialFilters = () => {\r\n    const { jenlap } = inquiryState;\r\n\r\n    if (jenlap === \"6\") {\r\n      // jenlap = 6 now uses blokir filter (original jenlap = 7 logic)\r\n      return debugFilter(\"blokir\");\r\n    } else if (jenlap === \"7\") {\r\n      // jenlap = 7 now uses special filters (original jenlap = 6 logic)\r\n      const specialFilters = [\r\n        \"inflasi\",\r\n        \"stunting\",\r\n        \"kemiskinan\",\r\n        \"pemilu\",\r\n        \"ikn\",\r\n        \"pangan\",\r\n        \"specialgrouping\",\r\n      ];\r\n\r\n      return specialFilters.map((filterName) => debugFilter(filterName));\r\n    }\r\n\r\n    return [];\r\n  };\r\n\r\n  /**\r\n   * Get query complexity analysis\r\n   */\r\n  const analyzeQueryComplexity = () => {\r\n    const metrics = getQueryPerformanceMetrics();\r\n    const stats = getFilterStats();\r\n\r\n    return {\r\n      complexity: {\r\n        low:\r\n          stats.enabledFilters <= 3 && metrics.validation.stats.joinCount <= 3,\r\n        medium:\r\n          stats.enabledFilters <= 6 && metrics.validation.stats.joinCount <= 6,\r\n        high:\r\n          stats.enabledFilters > 6 || metrics.validation.stats.joinCount > 6,\r\n      },\r\n      metrics,\r\n      stats,\r\n      recommendations: metrics.recommendations,\r\n    };\r\n  };\r\n\r\n  /**\r\n   * Cache queries for performance\r\n   */\r\n  const getCachedQuery = (cacheKey) => {\r\n    return queryCache[cacheKey];\r\n  };\r\n\r\n  const setCachedQuery = (cacheKey, query) => {\r\n    setQueryCache((prev) => ({\r\n      ...prev,\r\n      [cacheKey]: {\r\n        query,\r\n        timestamp: Date.now(),\r\n      },\r\n    }));\r\n  };\r\n\r\n  /**\r\n   * Clear query cache\r\n   */\r\n  const clearQueryCache = () => {\r\n    setQueryCache({});\r\n  };\r\n\r\n  // Log performance metrics in development\r\n  useEffect(() => {\r\n    if (process.env.NODE_ENV === \"development\") {\r\n      try {\r\n        const metrics = getQueryPerformanceMetrics();\r\n\r\n        if (metrics.validation.warnings.length > 0) {\r\n          console.warn(\r\n            \"⚠️ Query Builder Warnings:\",\r\n            metrics.validation.warnings\r\n          );\r\n        }\r\n\r\n        if (metrics.recommendations.length > 0) {\r\n          console.info(\r\n            \"💡 Query Builder Recommendations:\",\r\n            metrics.recommendations\r\n          );\r\n        }\r\n\r\n        console.log(\"📊 Query Stats:\", {\r\n          buildTime: `${metrics.buildTime.toFixed(2)}ms`,\r\n          enabledFilters: metrics.filterStats.enabledFilters,\r\n          queryLength: metrics.validation.stats.queryLength,\r\n          joinCount: metrics.validation.stats.joinCount,\r\n        });\r\n      } catch (error) {\r\n        console.warn(\"Error getting query performance metrics:\", error);\r\n      }\r\n    }\r\n  }, [thang, jenlap, cutoff, tanggal, akumulatif, pembulatan]); // Use specific dependencies instead of buildQuery\r\n\r\n  // Backward compatibility methods (matching original useQueryBuilder interface)\r\n  const legacyMethods = {\r\n    generateSqlPreview,\r\n    generateOptimizedSql: () => buildQuery, // Same as buildQuery in new version\r\n    parseAdvancedConditions: (kondisiValue, fieldName) => {\r\n      // Delegate to BaseFilter logic\r\n      const baseFilter =\r\n        new queryBuilder.filterBuilder.filters.department.constructor();\r\n      return baseFilter.parseKondisiConditions(kondisiValue);\r\n    },\r\n    optimizeGroupBy: (columns, groupFields) => {\r\n      return [...new Set(groupFields)].filter((group) =>\r\n        columns.some((col) => col.includes(group) || group.includes(\"a.\"))\r\n      );\r\n    },\r\n    optimizeJoins: (joinClause) => {\r\n      return queryBuilder.filterBuilder.optimizeJoins(\r\n        Array.isArray(joinClause) ? joinClause : [joinClause]\r\n      );\r\n    },\r\n    validateQuery,\r\n    getQueryPerformanceMetrics,\r\n    getQueryStats: getFilterStats, // Renamed but same functionality\r\n  };\r\n\r\n  return {\r\n    // Core functionality - Return both string value and function for compatibility\r\n    buildQuery: buildQuery, // The memoized query string\r\n    getBuildQuery: () => buildQuery, // Function that returns the query\r\n\r\n    // Analysis and debugging\r\n    generateSqlPreview,\r\n    validateQuery,\r\n    getQueryPerformanceMetrics,\r\n    getFilterStats,\r\n    analyzeQueryComplexity,\r\n\r\n    // Filter management\r\n    isFilterEnabled,\r\n    getAvailableFilters,\r\n    buildFilter,\r\n    debugFilter,\r\n    debugSpecialFilters,\r\n\r\n    // Performance\r\n    getCachedQuery,\r\n    setCachedQuery,\r\n    clearQueryCache,\r\n\r\n    // Backward compatibility\r\n    ...legacyMethods,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AA4LO;AAzLR;AACA;AAAA;;AAFA;;;AAIe,SAAS,gBAAgB,YAAY;;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE9C,oCAAoC;IACpC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE,IAAM,IAAI,2NAAA,CAAA,eAAY;gDAAI,EAAE;IAEzD,4CAA4C;IAC5C,MAAM,EACJ,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,UAAU,EACV,UAAU,EACV,6EAA6E;IAC7E,OAAO,EACP,SAAS,EACT,MAAM,EACP,GAAG;IAEJ;;GAEC,GACD,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,aAAa,UAAU,CAAC;YAEtC,qDAAqD;YACrD,MAAM,UAAU,aAAa,kBAAkB,CAAC;YAChD,WAAW,QAAQ,QAAQ,UAAU;YACrC,aAAa,UAAU,QAAQ,YAAY;YAC3C,UAAU,OAAO;YAEjB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,6BAA6B;QACjC,OAAO,aAAa,0BAA0B,CAAC;IACjD;IAEA;;GAEC,GACD,MAAM,qBAAqB;QACzB,OAAO,aAAa,kBAAkB,CAAC;IACzC;IAEA;;GAEC,GACD,MAAM,gBAAgB;YAAC,yEAAQ;QAC7B,OAAO,aAAa,aAAa,CAAC;IACpC;IAEA;;GAEC,GACD,MAAM,iBAAiB;QACrB,OAAO,aAAa,aAAa,CAAC,cAAc,CAAC;IACnD;IAEA;;GAEC,GACD,MAAM,kBAAkB,CAAC;QACvB,OAAO,aAAa,aAAa,CAAC,eAAe,CAAC,YAAY;IAChE;IAEA;;GAEC,GACD,MAAM,sBAAsB;QAC1B,OAAO,aAAa,aAAa,CAAC,mBAAmB;IACvD;IAEA;;GAEC,GACD,MAAM,cAAc,CAAC;QACnB,OAAO,aAAa,aAAa,CAAC,WAAW,CAAC,YAAY;IAC5D;IAEA;;GAEC,GACD,MAAM,cAAc,CAAC;QACnB,MAAM,eAAe,YAAY;QACjC,MAAM,YAAY,gBAAgB;QAElC,QAAQ,GAAG,CAAC,AAAC,oBAA8B,OAAX,aAAc;YAC5C;YACA,SAAS,aAAa,OAAO;YAC7B,YAAY,aAAa,UAAU;YACnC,iBAAiB,aAAa,eAAe;YAC7C,SAAS,aAAa,OAAO;QAC/B;QAEA,OAAO;YAAE;YAAY;YAAW,GAAG,YAAY;QAAC;IAClD;IAEA;;GAEC,GACD,MAAM,sBAAsB;QAC1B,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,IAAI,WAAW,KAAK;YAClB,gEAAgE;YAChE,OAAO,YAAY;QACrB,OAAO,IAAI,WAAW,KAAK;YACzB,kEAAkE;YAClE,MAAM,iBAAiB;gBACrB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,OAAO,eAAe,GAAG,CAAC,CAAC,aAAe,YAAY;QACxD;QAEA,OAAO,EAAE;IACX;IAEA;;GAEC,GACD,MAAM,yBAAyB;QAC7B,MAAM,UAAU;QAChB,MAAM,QAAQ;QAEd,OAAO;YACL,YAAY;gBACV,KACE,MAAM,cAAc,IAAI,KAAK,QAAQ,UAAU,CAAC,KAAK,CAAC,SAAS,IAAI;gBACrE,QACE,MAAM,cAAc,IAAI,KAAK,QAAQ,UAAU,CAAC,KAAK,CAAC,SAAS,IAAI;gBACrE,MACE,MAAM,cAAc,GAAG,KAAK,QAAQ,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG;YACrE;YACA;YACA;YACA,iBAAiB,QAAQ,eAAe;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,CAAC;QACtB,OAAO,UAAU,CAAC,SAAS;IAC7B;IAEA,MAAM,iBAAiB,CAAC,UAAU;QAChC,cAAc,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE;oBACV;oBACA,WAAW,KAAK,GAAG;gBACrB;YACF,CAAC;IACH;IAEA;;GAEC,GACD,MAAM,kBAAkB;QACtB,cAAc,CAAC;IACjB;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,wCAA4C;gBAC1C,IAAI;oBACF,MAAM,UAAU;oBAEhB,IAAI,QAAQ,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;wBAC1C,QAAQ,IAAI,CACV,8BACA,QAAQ,UAAU,CAAC,QAAQ;oBAE/B;oBAEA,IAAI,QAAQ,eAAe,CAAC,MAAM,GAAG,GAAG;wBACtC,QAAQ,IAAI,CACV,qCACA,QAAQ,eAAe;oBAE3B;oBAEA,QAAQ,GAAG,CAAC,mBAAmB;wBAC7B,WAAW,AAAC,GAA+B,OAA7B,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAG;wBAC3C,gBAAgB,QAAQ,WAAW,CAAC,cAAc;wBAClD,aAAa,QAAQ,UAAU,CAAC,KAAK,CAAC,WAAW;wBACjD,WAAW,QAAQ,UAAU,CAAC,KAAK,CAAC,SAAS;oBAC/C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,4CAA4C;gBAC3D;YACF;QACF;oCAAG;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAY;KAAW,GAAG,kDAAkD;IAEhH,+EAA+E;IAC/E,MAAM,gBAAgB;QACpB;QACA,sBAAsB,IAAM;QAC5B,yBAAyB,CAAC,cAAc;YACtC,+BAA+B;YAC/B,MAAM,aACJ,IAAI,aAAa,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW;YAC/D,OAAO,WAAW,sBAAsB,CAAC;QAC3C;QACA,iBAAiB,CAAC,SAAS;YACzB,OAAO;mBAAI,IAAI,IAAI;aAAa,CAAC,MAAM,CAAC,CAAC,QACvC,QAAQ,IAAI,CAAC,CAAC,MAAQ,IAAI,QAAQ,CAAC,UAAU,MAAM,QAAQ,CAAC;QAEhE;QACA,eAAe,CAAC;YACd,OAAO,aAAa,aAAa,CAAC,aAAa,CAC7C,MAAM,OAAO,CAAC,cAAc,aAAa;gBAAC;aAAW;QAEzD;QACA;QACA;QACA,eAAe;IACjB;IAEA,OAAO;QACL,+EAA+E;QAC/E,YAAY;QACZ,eAAe,IAAM;QAErB,yBAAyB;QACzB;QACA;QACA;QACA;QACA;QAEA,oBAAoB;QACpB;QACA;QACA;QACA;QACA;QAEA,cAAc;QACd;QACA;QACA;QAEA,yBAAyB;QACzB,GAAG,aAAa;IAClB;AACF;GAxQwB", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/BaseFilter.js"], "sourcesContent": ["/**\r\n * Base Filter Class\r\n * Contains common filter logic that all specific filters extend from\r\n */\r\nclass BaseFilter {\r\n  constructor(fieldName, tableName, referenceTable = null) {\r\n    this.fieldName = fieldName;\r\n    this.tableName = tableName;\r\n    this.referenceTable = referenceTable;\r\n  }\r\n\r\n  /**\r\n   * Build column selection based on radio button value\r\n   * @param {string} radio - Radio button value (\"1\" = code, \"2\" = code+text, \"3\" = text)\r\n   * @param {string} thang - Year parameter\r\n   * @param {object} inquiryState - (optional) full inquiry state, used for jenlap-specific logic\r\n   * @returns {object} - { columns: [], joinClause: string, groupBy: [] }\r\n   */\r\n  buildColumns(radio, thang = \"\", inquiryState = {}) {\r\n    const result = {\r\n      columns: [],\r\n      joinClause: \"\",\r\n      groupBy: [],\r\n    };\r\n\r\n    if (!radio) return result;\r\n\r\n    const codeField = `a.${this.fieldName}`;\r\n    const textField = this.referenceTable\r\n      ? `${this.referenceTable.alias}.${this.referenceTable.nameField}`\r\n      : null;\r\n\r\n    // Determine pagu field name based on jenlap (for use in child filters)\r\n    const paguField =\r\n      inquiryState && inquiryState.jenlap === \"1\" ? \"a.pagu_apbn\" : \"a.pagu\";\r\n\r\n    switch (radio) {\r\n      case \"1\": // Code only\r\n        result.columns.push(codeField);\r\n        result.groupBy.push(codeField);\r\n        break;\r\n\r\n      case \"2\": // Code + Text\r\n        result.columns.push(codeField);\r\n        if (textField) {\r\n          result.columns.push(textField);\r\n          result.joinClause = this.buildJoinClause(thang);\r\n          result.groupBy.push(textField); // Add text field to GROUP BY\r\n        }\r\n        result.groupBy.push(codeField);\r\n        break;\r\n\r\n      case \"3\": // Text only\r\n        if (textField) {\r\n          result.columns.push(textField);\r\n          result.joinClause = this.buildJoinClause(thang);\r\n          result.groupBy.push(textField); // Add text field to GROUP BY\r\n        }\r\n        result.groupBy.push(codeField);\r\n        break;\r\n    }\r\n\r\n    // Optionally expose paguField for use in child filters\r\n    result.paguField = paguField;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Build JOIN clause for reference table\r\n   * @param {string} thang - Year parameter\r\n   * @returns {string} - JOIN clause\r\n   */\r\n  buildJoinClause(thang = \"\") {\r\n    if (!this.referenceTable) return \"\";\r\n\r\n    const yearSuffix = this.referenceTable.hasYear ? `_${thang}` : \"\";\r\n    const tableName = `${this.referenceTable.schema}.${this.referenceTable.table}${yearSuffix}`;\r\n\r\n    return ` LEFT JOIN ${tableName} ${this.referenceTable.alias} ON ${this.referenceTable.joinCondition}`;\r\n  }\r\n\r\n  /**\r\n   * Build WHERE conditions based on filter type\r\n   * @param {object} filterData - Filter configuration\r\n   * @returns {string[]} - Array of WHERE conditions\r\n   */\r\n  buildWhereConditions(filterData) {\r\n    const conditions = [];\r\n    const {\r\n      pilihValue,\r\n      kondisiValue,\r\n      kataValue,\r\n      opsiType,\r\n      defaultValues = [\r\n        \"XXX\",\r\n        \"000\",\r\n        \"XX\",\r\n        \"00\",\r\n        \"XXXX\",\r\n        \"0000\",\r\n        \"XXXXXX\",\r\n        \"000000\",\r\n      ],\r\n    } = filterData;\r\n\r\n    // Priority: 1. kata (keyword), 2. kondisi (conditions), 3. pilih (select)\r\n    if (kataValue && kataValue.trim() !== \"\") {\r\n      const textField = this.referenceTable\r\n        ? `${this.referenceTable.alias}.${this.referenceTable.nameField}`\r\n        : `a.${this.fieldName}`;\r\n      conditions.push(`${textField} LIKE '%${kataValue}%'`);\r\n    } else if (kondisiValue && kondisiValue.trim() !== \"\") {\r\n      conditions.push(this.parseKondisiConditions(kondisiValue));\r\n    } else if (pilihValue && !defaultValues.includes(pilihValue)) {\r\n      conditions.push(`a.${this.fieldName} = '${pilihValue}'`);\r\n    }\r\n\r\n    return conditions.filter(\r\n      (condition) => condition && condition.trim() !== \"\"\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Parse kondisi (conditions) input with advanced operators\r\n   * @param {string} kondisiValue - Conditions string\r\n   * @returns {string} - WHERE condition\r\n   */\r\n  parseKondisiConditions(kondisiValue) {\r\n    if (!kondisiValue || kondisiValue.trim() === \"\") return \"\";\r\n\r\n    const fieldName = `a.${this.fieldName}`;\r\n\r\n    // Handle NOT IN conditions (starting with !)\r\n    if (kondisiValue.substring(0, 1) === \"!\") {\r\n      const cleanValue = kondisiValue.substring(1);\r\n      const values = cleanValue\r\n        .split(\",\")\r\n        .map((val) => val.trim())\r\n        .filter((val) => val !== \"\");\r\n\r\n      if (values.length > 0) {\r\n        const formattedValues = values.map((val) => `'${val}'`).join(\",\");\r\n        return `${fieldName} NOT IN (${formattedValues})`;\r\n      }\r\n    }\r\n    // Handle LIKE conditions (containing %)\r\n    else if (kondisiValue.includes(\"%\")) {\r\n      return `${fieldName} LIKE '${kondisiValue}'`;\r\n    }\r\n    // Handle range conditions (containing -)\r\n    else if (kondisiValue.includes(\"-\") && !kondisiValue.includes(\",\")) {\r\n      const [start, end] = kondisiValue.split(\"-\").map((val) => val.trim());\r\n      if (start && end) {\r\n        return `${fieldName} BETWEEN '${start}' AND '${end}'`;\r\n      }\r\n    }\r\n    // Handle standard IN conditions\r\n    else {\r\n      const values = kondisiValue\r\n        .split(\",\")\r\n        .map((val) => val.trim())\r\n        .filter((val) => val !== \"\");\r\n\r\n      if (values.length > 0) {\r\n        const formattedValues = values.map((val) => `'${val}'`).join(\",\");\r\n        return `${fieldName} IN (${formattedValues})`;\r\n      }\r\n    }\r\n\r\n    return \"\";\r\n  }\r\n\r\n  /**\r\n   * Main method to build complete filter\r\n   * @param {object} filterState - Complete filter state\r\n   * @param {string} thang - Year parameter\r\n   * @returns {object} - Complete filter result\r\n   */\r\n  build(filterState, thang = \"\") {\r\n    const { isEnabled, radio, pilihValue, kondisiValue, kataValue, opsiType } =\r\n      filterState;\r\n\r\n    const result = {\r\n      columns: [],\r\n      joinClause: \"\",\r\n      groupBy: [],\r\n      whereConditions: [],\r\n    };\r\n\r\n    // Only build if filter is enabled\r\n    if (!isEnabled) return result;\r\n\r\n    // Build columns and joins\r\n    const columnResult = this.buildColumns(radio, thang);\r\n    result.columns = columnResult.columns;\r\n    result.joinClause = columnResult.joinClause;\r\n    result.groupBy = columnResult.groupBy;\r\n\r\n    // Special handling for kata (keyword) filters\r\n    // When using kata filter, we need the descriptive column and JOIN regardless of radio setting\r\n    if (kataValue && kataValue.trim() !== \"\" && this.referenceTable) {\r\n      const textField = `${this.referenceTable.alias}.${this.referenceTable.nameField}`;\r\n\r\n      // Add JOIN if not already present\r\n      if (!result.joinClause) {\r\n        result.joinClause = this.buildJoinClause(thang);\r\n      }\r\n\r\n      // Add descriptive column to SELECT if not already present\r\n      if (!result.columns.includes(textField)) {\r\n        result.columns.push(textField);\r\n        // Also add to GROUP BY to avoid GROUP BY errors\r\n        if (!result.groupBy.includes(textField)) {\r\n          result.groupBy.push(textField);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Build WHERE conditions\r\n    result.whereConditions = this.buildWhereConditions({\r\n      pilihValue,\r\n      kondisiValue,\r\n      kataValue,\r\n      opsiType,\r\n    });\r\n\r\n    return result;\r\n  }\r\n\r\n  getEmptyResult() {\r\n    return {\r\n      columns: [],\r\n      joinClause: \"\",\r\n      whereConditions: [],\r\n      groupBy: [],\r\n    };\r\n  }\r\n}\r\n\r\nexport default BaseFilter;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACD,MAAM;IAOJ;;;;;;GAMC,GACD,aAAa,KAAK,EAAiC;YAA/B,QAAA,iEAAQ,IAAI,eAAA,iEAAe,CAAC;QAC9C,MAAM,SAAS;YACb,SAAS,EAAE;YACX,YAAY;YACZ,SAAS,EAAE;QACb;QAEA,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,YAAY,AAAC,KAAmB,OAAf,IAAI,CAAC,SAAS;QACrC,MAAM,YAAY,IAAI,CAAC,cAAc,GACjC,AAAC,GAA+B,OAA7B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC,KAAiC,OAA9B,IAAI,CAAC,cAAc,CAAC,SAAS,IAC7D;QAEJ,uEAAuE;QACvE,MAAM,YACJ,gBAAgB,aAAa,MAAM,KAAK,MAAM,gBAAgB;QAEhE,OAAQ;YACN,KAAK;gBACH,OAAO,OAAO,CAAC,IAAI,CAAC;gBACpB,OAAO,OAAO,CAAC,IAAI,CAAC;gBACpB;YAEF,KAAK;gBACH,OAAO,OAAO,CAAC,IAAI,CAAC;gBACpB,IAAI,WAAW;oBACb,OAAO,OAAO,CAAC,IAAI,CAAC;oBACpB,OAAO,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;oBACzC,OAAO,OAAO,CAAC,IAAI,CAAC,YAAY,6BAA6B;gBAC/D;gBACA,OAAO,OAAO,CAAC,IAAI,CAAC;gBACpB;YAEF,KAAK;gBACH,IAAI,WAAW;oBACb,OAAO,OAAO,CAAC,IAAI,CAAC;oBACpB,OAAO,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;oBACzC,OAAO,OAAO,CAAC,IAAI,CAAC,YAAY,6BAA6B;gBAC/D;gBACA,OAAO,OAAO,CAAC,IAAI,CAAC;gBACpB;QACJ;QAEA,uDAAuD;QACvD,OAAO,SAAS,GAAG;QAEnB,OAAO;IACT;IAEA;;;;GAIC,GACD,kBAA4B;YAAZ,QAAA,iEAAQ;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO;QAEjC,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,AAAC,IAAS,OAAN,SAAU;QAC/D,MAAM,YAAY,AAAC,GAAgC,OAA9B,IAAI,CAAC,cAAc,CAAC,MAAM,EAAC,KAA+B,OAA5B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAc,OAAX;QAE/E,OAAO,AAAC,cAA0B,OAAb,WAAU,KAAmC,OAAhC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC,QAAwC,OAAlC,IAAI,CAAC,cAAc,CAAC,aAAa;IACrG;IAEA;;;;GAIC,GACD,qBAAqB,UAAU,EAAE;QAC/B,MAAM,aAAa,EAAE;QACrB,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACF,GAAG;QAEJ,0EAA0E;QAC1E,IAAI,aAAa,UAAU,IAAI,OAAO,IAAI;YACxC,MAAM,YAAY,IAAI,CAAC,cAAc,GACjC,AAAC,GAA+B,OAA7B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC,KAAiC,OAA9B,IAAI,CAAC,cAAc,CAAC,SAAS,IAC7D,AAAC,KAAmB,OAAf,IAAI,CAAC,SAAS;YACvB,WAAW,IAAI,CAAC,AAAC,GAAsB,OAApB,WAAU,YAAoB,OAAV,WAAU;QACnD,OAAO,IAAI,gBAAgB,aAAa,IAAI,OAAO,IAAI;YACrD,WAAW,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC;QAC9C,OAAO,IAAI,cAAc,CAAC,cAAc,QAAQ,CAAC,aAAa;YAC5D,WAAW,IAAI,CAAC,AAAC,KAAyB,OAArB,IAAI,CAAC,SAAS,EAAC,QAAiB,OAAX,YAAW;QACvD;QAEA,OAAO,WAAW,MAAM,CACtB,CAAC,YAAc,aAAa,UAAU,IAAI,OAAO;IAErD;IAEA;;;;GAIC,GACD,uBAAuB,YAAY,EAAE;QACnC,IAAI,CAAC,gBAAgB,aAAa,IAAI,OAAO,IAAI,OAAO;QAExD,MAAM,YAAY,AAAC,KAAmB,OAAf,IAAI,CAAC,SAAS;QAErC,6CAA6C;QAC7C,IAAI,aAAa,SAAS,CAAC,GAAG,OAAO,KAAK;YACxC,MAAM,aAAa,aAAa,SAAS,CAAC;YAC1C,MAAM,SAAS,WACZ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,IACrB,MAAM,CAAC,CAAC,MAAQ,QAAQ;YAE3B,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,MAAM,kBAAkB,OAAO,GAAG,CAAC,CAAC,MAAQ,AAAC,IAAO,OAAJ,KAAI,MAAI,IAAI,CAAC;gBAC7D,OAAO,AAAC,GAAuB,OAArB,WAAU,aAA2B,OAAhB,iBAAgB;YACjD;QACF,OAEK,IAAI,aAAa,QAAQ,CAAC,MAAM;YACnC,OAAO,AAAC,GAAqB,OAAnB,WAAU,WAAsB,OAAb,cAAa;QAC5C,OAEK,IAAI,aAAa,QAAQ,CAAC,QAAQ,CAAC,aAAa,QAAQ,CAAC,MAAM;YAClE,MAAM,CAAC,OAAO,IAAI,GAAG,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI;YAClE,IAAI,SAAS,KAAK;gBAChB,OAAO,AAAC,GAAwB,OAAtB,WAAU,cAA2B,OAAf,OAAM,WAAa,OAAJ,KAAI;YACrD;QACF,OAEK;YACH,MAAM,SAAS,aACZ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,IACrB,MAAM,CAAC,CAAC,MAAQ,QAAQ;YAE3B,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,MAAM,kBAAkB,OAAO,GAAG,CAAC,CAAC,MAAQ,AAAC,IAAO,OAAJ,KAAI,MAAI,IAAI,CAAC;gBAC7D,OAAO,AAAC,GAAmB,OAAjB,WAAU,SAAuB,OAAhB,iBAAgB;YAC7C;QACF;QAEA,OAAO;IACT;IAEA;;;;;GAKC,GACD,MAAM,WAAW,EAAc;YAAZ,QAAA,iEAAQ;QACzB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,GACvE;QAEF,MAAM,SAAS;YACb,SAAS,EAAE;YACX,YAAY;YACZ,SAAS,EAAE;YACX,iBAAiB,EAAE;QACrB;QAEA,kCAAkC;QAClC,IAAI,CAAC,WAAW,OAAO;QAEvB,0BAA0B;QAC1B,MAAM,eAAe,IAAI,CAAC,YAAY,CAAC,OAAO;QAC9C,OAAO,OAAO,GAAG,aAAa,OAAO;QACrC,OAAO,UAAU,GAAG,aAAa,UAAU;QAC3C,OAAO,OAAO,GAAG,aAAa,OAAO;QAErC,8CAA8C;QAC9C,8FAA8F;QAC9F,IAAI,aAAa,UAAU,IAAI,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE;YAC/D,MAAM,YAAY,AAAC,GAA+B,OAA7B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAC,KAAiC,OAA9B,IAAI,CAAC,cAAc,CAAC,SAAS;YAE/E,kCAAkC;YAClC,IAAI,CAAC,OAAO,UAAU,EAAE;gBACtB,OAAO,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;YAC3C;YAEA,0DAA0D;YAC1D,IAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,YAAY;gBACvC,OAAO,OAAO,CAAC,IAAI,CAAC;gBACpB,gDAAgD;gBAChD,IAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACvC,OAAO,OAAO,CAAC,IAAI,CAAC;gBACtB;YACF;QACF;QAEA,yBAAyB;QACzB,OAAO,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACjD;YACA;YACA;YACA;QACF;QAEA,OAAO;IACT;IAEA,iBAAiB;QACf,OAAO;YACL,SAAS,EAAE;YACX,YAAY;YACZ,iBAAiB,EAAE;YACnB,SAAS,EAAE;QACb;IACF;IAxOA,YAAY,SAAS,EAAE,SAAS,EAAE,iBAAiB,IAAI,CAAE;QACvD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,cAAc,GAAG;IACxB;AAqOF;uCAEe", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/DepartmentFilter.js"], "sourcesContent": ["import BaseFilter from \"./BaseFilter\";\r\n\r\n/**\r\n * Department Filter Handler\r\n */\r\nclass DepartmentFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kddept\", \"department\", {\r\n      schema: \"dbref\",\r\n      table: \"t_dept\",\r\n      alias: \"b\",\r\n      nameField: \"nmdept\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kddept=b.kddept\",\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Build department filter from inquiry state\r\n   * @param {object} inquiryState - Full inquiry state\r\n   * @returns {object} - Filter result\r\n   */\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kddept: isEnabled,\r\n      dept: pilihValue,\r\n      deptkondisi: kondisiValue,\r\n      katadept: kataValue,\r\n      deptradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Unit Filter Handler\r\n */\r\nclass UnitFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdunit\", \"unit\", {\r\n      schema: \"dbref\",\r\n      table: \"t_unit\",\r\n      alias: \"c\",\r\n      nameField: \"nmunit\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kddept=c.kddept AND a.kdunit=c.kdunit\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      unit: isEnabled,\r\n      kdunit: pilihValue,\r\n      unitkondisi: kondisiValue,\r\n      kataunit: kataValue,\r\n      unitradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Dekonsentrasi Filter Handler\r\n */\r\nclass DekonFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kddekon\", \"dekonsentrasi\", {\r\n      schema: \"dbref\",\r\n      table: \"t_dekon\",\r\n      alias: \"d\",\r\n      nameField: \"nmdekon\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kddekon=d.kddekon\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kddekon: isEnabled,\r\n      dekon: pilihValue,\r\n      dekonkondisi: kondisiValue,\r\n      katadekon: kataValue,\r\n      dekonradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Satker Filter Handler\r\n */\r\nclass SatkerFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdsatker\", \"satker\", {\r\n      schema: \"dbref\",\r\n      table: \"t_satker\",\r\n      alias: \"s\",\r\n      nameField: \"nmsatker\",\r\n      hasYear: true,\r\n      joinCondition:\r\n        \"a.kddept=s.kddept AND a.kdunit=s.kdunit AND a.kdsatker=s.kdsatker\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdsatker: isEnabled,\r\n      satker: pilihValue,\r\n      satkerkondisi: kondisiValue,\r\n      katasatker: kataValue,\r\n      satkerradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\nexport { DepartmentFilter, UnitFilter, DekonFilter, SatkerFilter };\r\nexport default DepartmentFilter;\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,yBAAyB,8KAAA,CAAA,UAAU;IAYvC;;;;GAIC,GACD,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,QAAQ,SAAS,EACjB,MAAM,UAAU,EAChB,aAAa,YAAY,EACzB,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IApCA,aAAc;QACZ,KAAK,CAAC,UAAU,cAAc;YAC5B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AA4BF;AAEA;;CAEC,GACD,MAAM,mBAAmB,8KAAA,CAAA,UAAU;IAYjC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,MAAM,SAAS,EACf,QAAQ,UAAU,EAClB,aAAa,YAAY,EACzB,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,UAAU,QAAQ;YACtB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,oBAAoB,8KAAA,CAAA,UAAU;IAYlC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,SAAS,SAAS,EAClB,OAAO,UAAU,EACjB,cAAc,YAAY,EAC1B,WAAW,SAAS,EACpB,YAAY,KAAK,EACjB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,WAAW,iBAAiB;YAChC,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAanC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,UAAU,SAAS,EACnB,QAAQ,UAAU,EAClB,eAAe,YAAY,EAC3B,YAAY,SAAS,EACrB,aAAa,KAAK,EAClB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IAhCA,aAAc;QACZ,KAAK,CAAC,YAAY,UAAU;YAC1B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eACE;QACJ;IACF;AAuBF;;uCAGe", "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/LocationFilter.js"], "sourcesContent": ["import BaseFilter from \"./BaseFilter\";\r\n\r\n/**\r\n * <PERSON><PERSON><PERSON>lter <PERSON>\r\n */\r\nclass ProvinsiFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdlokasi\", \"provinsi\", {\r\n      schema: \"dbref\",\r\n      table: \"t_lokasi\",\r\n      alias: \"p\",\r\n      nameField: \"nmlokasi\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdlokasi=p.kdlokasi\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdlokasi: isEnabled,\r\n      prov: pilihValue,\r\n      lokasikondisi: kondisiValue,\r\n      katalokasi: kataValue,\r\n      locradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Kabupaten/Kota Filter Handler\r\n */\r\nclass KabkotaFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdkabkota\", \"kabkota\", {\r\n      schema: \"dbref\",\r\n      table: \"t_kabkota\",\r\n      alias: \"kk\",\r\n      nameField: \"nmkabkota\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdlokasi=kk.kdlokasi AND a.kdkabkota=kk.kdkabkota\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdkabkota: isEnabled,\r\n      kabkota: pilihValue,\r\n      kabkotakondisi: kondisiValue,\r\n      katakabkota: kataValue,\r\n      kabkotaradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Kanwil Filter Handler\r\n */\r\nclass KanwilFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdkanwil\", \"kanwil\", {\r\n      schema: \"dbref\",\r\n      table: \"t_kanwil\",\r\n      alias: \"kw\",\r\n      nameField: \"nmkanwil\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdkanwil=kw.kdkanwil\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdkanwil: isEnabled,\r\n      kanwil: pilihValue,\r\n      kanwilkondisi: kondisiValue,\r\n      katakanwil: kataValue,\r\n      kanwilradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * KPPN Filter Handler\r\n */\r\nclass KppnFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdkppn\", \"kppn\", {\r\n      schema: \"dbref\",\r\n      table: \"t_kppn\",\r\n      alias: \"kp\",\r\n      nameField: \"nmkppn\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdkppn=kp.kdkppn\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdkppn: isEnabled,\r\n      kppn: pilihValue,\r\n      kppnkondisi: kondisiValue,\r\n      katakppn: kataValue,\r\n      kppnradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\nexport { ProvinsiFilter, KabkotaFilter, KanwilFilter, KppnFilter };\r\nexport default ProvinsiFilter;\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,uBAAuB,8KAAA,CAAA,UAAU;IAYrC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,UAAU,SAAS,EACnB,MAAM,UAAU,EAChB,eAAe,YAAY,EAC3B,YAAY,SAAS,EACrB,UAAU,KAAK,EACf,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,YAAY,YAAY;YAC5B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,sBAAsB,8KAAA,CAAA,UAAU;IAYpC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,WAAW,SAAS,EACpB,SAAS,UAAU,EACnB,gBAAgB,YAAY,EAC5B,aAAa,SAAS,EACtB,cAAc,KAAK,EACnB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,aAAa,WAAW;YAC5B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAYnC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,UAAU,SAAS,EACnB,QAAQ,UAAU,EAClB,eAAe,YAAY,EAC3B,YAAY,SAAS,EACrB,aAAa,KAAK,EAClB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,YAAY,UAAU;YAC1B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,mBAAmB,8KAAA,CAAA,UAAU;IAYjC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,QAAQ,SAAS,EACjB,MAAM,UAAU,EAChB,aAAa,YAAY,EACzB,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,UAAU,QAAQ;YACtB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;;uCAGe", "debugId": null}}, {"offset": {"line": 1410, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/ProgramFilter.js"], "sourcesContent": ["import BaseFilter from \"./BaseFilter\";\r\n\r\n/**\r\n * Fungs<PERSON> Filter Handler\r\n */\r\nclass FungsiFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdfungsi\", \"fungsi\", {\r\n      schema: \"dbref\",\r\n      table: \"t_fungsi\",\r\n      alias: \"f\",\r\n      nameField: \"nmfungsi\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdfungsi=f.kdfungsi\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdfungsi: isEnabled,\r\n      fungsi: pilihValue,\r\n      fungsikondisi: kondisiValue,\r\n      katafungsi: kataValue,\r\n      fungsiradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Sub Fungsi Filter Handler\r\n */\r\nclass SubFungsiFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdsfung\", \"subfungsi\", {\r\n      schema: \"dbref\",\r\n      table: \"t_sfung\",\r\n      alias: \"sf\",\r\n      nameField: \"nmsfung\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdfungsi=sf.kdfungsi AND a.kdsfung=sf.kdsfung\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdsfungsi: isEnabled,\r\n      sfungsi: pilihValue,\r\n      subfungsikondisi: kondisiValue,\r\n      katasubfungsi: kataValue,\r\n      subfungsiradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Program Filter Handler\r\n */\r\nclass ProgramFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdprogram\", \"program\", {\r\n      schema: \"dbref\",\r\n      table: \"t_program\",\r\n      alias: \"pr\",\r\n      nameField: \"nmprogram\",\r\n      hasYear: true,\r\n      joinCondition:\r\n        \"a.kddept=pr.kddept AND a.kdunit=pr.kdunit AND a.kdprogram=pr.kdprogram\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdprogram: isEnabled,\r\n      program: pilihValue,\r\n      programkondisi: kondisiValue,\r\n      kataprogram: kataValue,\r\n      programradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Kegiatan Filter Handler\r\n */\r\nclass KegiatanFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdgiat\", \"kegiatan\", {\r\n      schema: \"dbref\",\r\n      table: \"t_giat\",\r\n      alias: \"g\",\r\n      nameField: \"nmgiat\",\r\n      hasYear: true,\r\n      joinCondition:\r\n        \"a.kddept=g.kddept AND a.kdunit=g.kdunit AND a.kdprogram=g.kdprogram AND a.kdgiat=g.kdgiat\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdgiat: isEnabled,\r\n      giat: pilihValue,\r\n      giatkondisi: kondisiValue,\r\n      katagiat: kataValue,\r\n      kegiatanradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Output Filter Handler\r\n */\r\nclass OutputFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdoutput\", \"output\", {\r\n      schema: \"dbref\",\r\n      table: \"t_output\",\r\n      alias: \"o\",\r\n      nameField: \"nmoutput\",\r\n      hasYear: true,\r\n      joinCondition:\r\n        \"a.kddept=o.kddept AND a.kdunit=o.kdunit AND a.kdprogram=o.kdprogram AND a.kdgiat=o.kdgiat AND a.kdoutput=o.kdoutput\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdoutput: isEnabled,\r\n      output: pilihValue,\r\n      outputkondisi: kondisiValue,\r\n      kataoutput: kataValue,\r\n      outputradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Sub Output Filter Handler\r\n */\r\nclass SubOutputFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdsoutput\", \"suboutput\", {\r\n      schema: \"dbref\",\r\n      table: \"t_soutput\",\r\n      alias: \"so\",\r\n      nameField: \"nmsoutput\",\r\n      hasYear: true,\r\n      joinCondition:\r\n        \"a.kddept=so.kddept AND a.kdunit=so.kdunit AND a.kdprogram=so.kdprogram AND a.kdgiat=so.kdgiat AND a.kdoutput=so.kdoutput AND a.kdsoutput=so.kdsoutput\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdsoutput: isEnabled,\r\n      soutput: pilihValue,\r\n      soutputkondisi: kondisiValue,\r\n      katasoutput: kataValue,\r\n      soutputradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\nexport {\r\n  FungsiFilter,\r\n  SubFungsiFilter,\r\n  ProgramFilter,\r\n  KegiatanFilter,\r\n  OutputFilter,\r\n  SubOutputFilter,\r\n};\r\nexport default ProgramFilter;\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAYnC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,UAAU,SAAS,EACnB,QAAQ,UAAU,EAClB,eAAe,YAAY,EAC3B,YAAY,SAAS,EACrB,aAAa,KAAK,EAClB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,YAAY,UAAU;YAC1B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,wBAAwB,8KAAA,CAAA,UAAU;IAYtC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,WAAW,SAAS,EACpB,SAAS,UAAU,EACnB,kBAAkB,YAAY,EAC9B,eAAe,SAAS,EACxB,gBAAgB,KAAK,EACrB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,WAAW,aAAa;YAC5B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,sBAAsB,8KAAA,CAAA,UAAU;IAapC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,WAAW,SAAS,EACpB,SAAS,UAAU,EACnB,gBAAgB,YAAY,EAC5B,aAAa,SAAS,EACtB,cAAc,KAAK,EACnB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IAhCA,aAAc;QACZ,KAAK,CAAC,aAAa,WAAW;YAC5B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eACE;QACJ;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,uBAAuB,8KAAA,CAAA,UAAU;IAarC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,QAAQ,SAAS,EACjB,MAAM,UAAU,EAChB,aAAa,YAAY,EACzB,UAAU,SAAS,EACnB,eAAe,KAAK,EACpB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IAhCA,aAAc;QACZ,KAAK,CAAC,UAAU,YAAY;YAC1B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eACE;QACJ;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAanC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,UAAU,SAAS,EACnB,QAAQ,UAAU,EAClB,eAAe,YAAY,EAC3B,YAAY,SAAS,EACrB,aAAa,KAAK,EAClB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IAhCA,aAAc;QACZ,KAAK,CAAC,YAAY,UAAU;YAC1B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eACE;QACJ;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,wBAAwB,8KAAA,CAAA,UAAU;IAatC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,WAAW,SAAS,EACpB,SAAS,UAAU,EACnB,gBAAgB,YAAY,EAC5B,aAAa,SAAS,EACtB,cAAc,KAAK,EACnB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IAhCA,aAAc;QACZ,KAAK,CAAC,aAAa,aAAa;YAC9B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eACE;QACJ;IACF;AAuBF;;uCAUe", "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/AccountFilter.js"], "sourcesContent": ["import BaseFilter from \"./BaseFilter\";\r\n\r\n/**\r\n * <PERSON><PERSON><PERSON>\r\n */\r\nclass AkunFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdakun\", \"akun\", {\r\n      schema: \"dbref\",\r\n      table: \"t_akun\",\r\n      alias: \"ak\",\r\n      nameField: \"nmakun\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdakun=ak.kdakun\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdakun: isEnabled,\r\n      akun: pilihValue,\r\n      akunkondisi: kondisiValue,\r\n      kataakun: kataValue,\r\n      akunradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    // Universal: If <PERSON><PERSON> is '<PERSON><PERSON>' (radio === '4'), return empty result (no-op)\r\n    if (isEnabled && radio === \"4\") {\r\n      return {\r\n        columns: [],\r\n        groupBy: [],\r\n        joinClause: \"\",\r\n        whereConditions: [],\r\n      };\r\n    }\r\n\r\n    // Special: If 'Kode BKPK' or '<PERSON><PERSON> Belanja' is selected, customize SELECT, GROUP BY, and filter logic\r\n    if (isEnabled && (pilihValue === \"BKPK\" || pilihValue === \"JENBEL\")) {\r\n      // Use 4 for BKPK, 2 for JENBEL\r\n      const leftLen = pilihValue === \"BKPK\" ? 4 : 2;\r\n      // Build default, but override columns, groupBy, joinClause, and whereConditions\r\n      const result = this.build(\r\n        {\r\n          isEnabled: true,\r\n          radio,\r\n          pilihValue: \"\", // no value, so no whereCondition\r\n          kondisiValue: \"\",\r\n          kataValue: \"\",\r\n        },\r\n        thang\r\n      );\r\n      if (pilihValue === \"BKPK\") {\r\n        const bkpkTable = `dbref.t_bkpk_${thang}`;\r\n        if (radio === \"3\") {\r\n          result.columns = [\"bk.nmbkpk\"];\r\n          result.joinClause = ` LEFT JOIN ${bkpkTable} bk ON LEFT(a.kdakun,${leftLen}) = bk.kdbkpk`;\r\n          result.groupBy = [`LEFT(a.kdakun,${leftLen})`];\r\n        } else if (radio === \"2\") {\r\n          result.columns = [`LEFT(a.kdakun,${leftLen}) AS kdbkpk`, \"bk.nmbkpk\"];\r\n          result.joinClause = ` LEFT JOIN ${bkpkTable} bk ON LEFT(a.kdakun,${leftLen}) = bk.kdbkpk`;\r\n          result.groupBy = [`LEFT(a.kdakun,${leftLen})`];\r\n        } else {\r\n          result.columns = [`LEFT(a.kdakun,${leftLen}) AS kdbkpk`];\r\n          result.groupBy = [`LEFT(a.kdakun,${leftLen})`];\r\n          result.joinClause = \"\";\r\n        }\r\n        // Custom filter for kondisi (akunkondisi)\r\n        if (kondisiValue && /^[0-9]+$/.test(kondisiValue)) {\r\n          const n = kondisiValue.length;\r\n          result.whereConditions = [\r\n            `LEFT(a.kdakun,${n}) IN ('${kondisiValue}')`,\r\n          ];\r\n        }\r\n        // Custom filter for kata (bk.nmbkpk)\r\n        if (kataValue && kataValue.trim() !== \"\") {\r\n          result.whereConditions = [`bk.nmbkpk LIKE '%${kataValue.trim()}%'`];\r\n        }\r\n      } else if (pilihValue === \"JENBEL\") {\r\n        const gbkpkTable = `dbref.t_gbkpk_${thang}`;\r\n        if (radio === \"3\") {\r\n          result.columns = [\"gb.nmgbkpk\"];\r\n          result.joinClause = ` LEFT JOIN ${gbkpkTable} gb ON LEFT(a.kdakun,${leftLen}) = gb.kdgbkpk`;\r\n          result.groupBy = [`LEFT(a.kdakun,${leftLen})`];\r\n        } else if (radio === \"2\") {\r\n          result.columns = [\r\n            `LEFT(a.kdakun,${leftLen}) AS kdgbkpk`,\r\n            \"gb.nmgbkpk\",\r\n          ];\r\n          result.joinClause = ` LEFT JOIN ${gbkpkTable} gb ON LEFT(a.kdakun,${leftLen}) = gb.kdgbkpk`;\r\n          result.groupBy = [`LEFT(a.kdakun,${leftLen})`];\r\n        } else {\r\n          result.columns = [`LEFT(a.kdakun,${leftLen}) AS kdgbkpk`];\r\n          result.groupBy = [`LEFT(a.kdakun,${leftLen})`];\r\n          result.joinClause = \"\";\r\n        }\r\n        // Custom filter for kondisi (akunkondisi)\r\n        if (kondisiValue && /^[0-9]+$/.test(kondisiValue)) {\r\n          const n = kondisiValue.length;\r\n          result.whereConditions = [\r\n            `LEFT(a.kdakun,${n}) IN ('${kondisiValue}')`,\r\n          ];\r\n        }\r\n        // Custom filter for kata (gb.nmgbkpk)\r\n        if (kataValue && kataValue.trim() !== \"\") {\r\n          result.whereConditions = [`gb.nmgbkpk LIKE '%${kataValue.trim()}%'`];\r\n        }\r\n      }\r\n      return result;\r\n    }\r\n\r\n    // If 'Kode Akun' (AKUN) is selected, skip whereCondition but keep columns\r\n    if (\r\n      isEnabled &&\r\n      (pilihValue === \"AKUN\" || !pilihValue) &&\r\n      !kondisiValue &&\r\n      !kataValue\r\n    ) {\r\n      // Pass isEnabled true, but pilihValue empty so build() only returns columns\r\n      return this.build(\r\n        {\r\n          isEnabled: true,\r\n          radio,\r\n          pilihValue: \"\", // no value, so no whereCondition\r\n          kondisiValue: \"\",\r\n          kataValue: \"\",\r\n        },\r\n        thang\r\n      );\r\n    }\r\n\r\n    // Custom: if user enters a value in akunkondisi, use LEFT(a.kdakun, N) IN (...)\r\n    if (isEnabled && kondisiValue && /^[0-9]+$/.test(kondisiValue)) {\r\n      const n = kondisiValue.length;\r\n      const whereCondition = `LEFT(a.kdakun,${n}) IN ('${kondisiValue}')`;\r\n      // Call build, but override whereConditions\r\n      const result = this.build(\r\n        {\r\n          isEnabled,\r\n          radio,\r\n          pilihValue: \"\", // ignore pilihValue for this case\r\n          kondisiValue: \"\", // prevent default whereCondition\r\n          kataValue,\r\n        },\r\n        thang\r\n      );\r\n      // Inject custom whereCondition\r\n      return {\r\n        ...result,\r\n        whereConditions: [whereCondition],\r\n      };\r\n    }\r\n\r\n    // Custom: if user enters a value in kataValue, use ak.nmakun LIKE '%kataValue%'\r\n    if (isEnabled && kataValue && kataValue.trim() !== \"\") {\r\n      const whereCondition = `ak.nmakun LIKE '%${kataValue.trim()}%'`;\r\n      const result = this.build(\r\n        {\r\n          isEnabled,\r\n          radio,\r\n          pilihValue: \"\", // ignore pilihValue for this case\r\n          kondisiValue: \"\", // prevent default whereCondition\r\n          kataValue,\r\n        },\r\n        thang\r\n      );\r\n      return {\r\n        ...result,\r\n        whereConditions: [whereCondition],\r\n      };\r\n    }\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Sumber Dana Filter Handler\r\n */\r\nclass SumberDanaFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdsdana\", \"sdana\", {\r\n      schema: \"dbref\",\r\n      table: \"t_sdana\",\r\n      alias: \"sd\",\r\n      nameField: \"nmsdana\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdsdana=sd.kdsdana\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdsdana: isEnabled,\r\n      sdana: pilihValue,\r\n      sdanakondisi: kondisiValue,\r\n      opsikatasdana: kataValue,\r\n      sdanaradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Register Filter Handler\r\n */\r\nclass RegisterFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdregister\", \"register\", {\r\n      schema: \"dbref\",\r\n      table: \"t_register\",\r\n      alias: \"r\",\r\n      nameField: \"nmregister\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdregister=r.kdregister\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      kdregister: isEnabled,\r\n      register: pilihValue,\r\n      registerkondisi: kondisiValue,\r\n      opsikataregister: kataValue,\r\n      registerradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\nexport { AkunFilter, SumberDanaFilter, RegisterFilter };\r\nexport default AkunFilter;\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,mBAAmB,8KAAA,CAAA,UAAU;IAYjC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,QAAQ,SAAS,EACjB,MAAM,UAAU,EAChB,aAAa,YAAY,EACzB,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,KAAK,EACN,GAAG;QAEJ,kGAAkG;QAClG,IAAI,aAAa,UAAU,KAAK;YAC9B,OAAO;gBACL,SAAS,EAAE;gBACX,SAAS,EAAE;gBACX,YAAY;gBACZ,iBAAiB,EAAE;YACrB;QACF;QAEA,uGAAuG;QACvG,IAAI,aAAa,CAAC,eAAe,UAAU,eAAe,QAAQ,GAAG;YACnE,+BAA+B;YAC/B,MAAM,UAAU,eAAe,SAAS,IAAI;YAC5C,gFAAgF;YAChF,MAAM,SAAS,IAAI,CAAC,KAAK,CACvB;gBACE,WAAW;gBACX;gBACA,YAAY;gBACZ,cAAc;gBACd,WAAW;YACb,GACA;YAEF,IAAI,eAAe,QAAQ;gBACzB,MAAM,YAAY,AAAC,gBAAqB,OAAN;gBAClC,IAAI,UAAU,KAAK;oBACjB,OAAO,OAAO,GAAG;wBAAC;qBAAY;oBAC9B,OAAO,UAAU,GAAG,AAAC,cAA8C,OAAjC,WAAU,yBAA+B,OAAR,SAAQ;oBAC3E,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAG;gBAChD,OAAO,IAAI,UAAU,KAAK;oBACxB,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;wBAAc;qBAAY;oBACrE,OAAO,UAAU,GAAG,AAAC,cAA8C,OAAjC,WAAU,yBAA+B,OAAR,SAAQ;oBAC3E,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAG;gBAChD,OAAO;oBACL,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAa;oBACxD,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAG;oBAC9C,OAAO,UAAU,GAAG;gBACtB;gBACA,0CAA0C;gBAC1C,IAAI,gBAAgB,WAAW,IAAI,CAAC,eAAe;oBACjD,MAAM,IAAI,aAAa,MAAM;oBAC7B,OAAO,eAAe,GAAG;wBACtB,iBAA2B,OAAX,GAAE,WAAsB,OAAb,cAAa;qBAC1C;gBACH;gBACA,qCAAqC;gBACrC,IAAI,aAAa,UAAU,IAAI,OAAO,IAAI;oBACxC,OAAO,eAAe,GAAG;wBAAE,oBAAoC,OAAjB,UAAU,IAAI,IAAG;qBAAI;gBACrE;YACF,OAAO,IAAI,eAAe,UAAU;gBAClC,MAAM,aAAa,AAAC,iBAAsB,OAAN;gBACpC,IAAI,UAAU,KAAK;oBACjB,OAAO,OAAO,GAAG;wBAAC;qBAAa;oBAC/B,OAAO,UAAU,GAAG,AAAC,cAA+C,OAAlC,YAAW,yBAA+B,OAAR,SAAQ;oBAC5E,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAG;gBAChD,OAAO,IAAI,UAAU,KAAK;oBACxB,OAAO,OAAO,GAAG;wBACd,iBAAwB,OAAR,SAAQ;wBACzB;qBACD;oBACD,OAAO,UAAU,GAAG,AAAC,cAA+C,OAAlC,YAAW,yBAA+B,OAAR,SAAQ;oBAC5E,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAG;gBAChD,OAAO;oBACL,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAc;oBACzD,OAAO,OAAO,GAAG;wBAAE,iBAAwB,OAAR,SAAQ;qBAAG;oBAC9C,OAAO,UAAU,GAAG;gBACtB;gBACA,0CAA0C;gBAC1C,IAAI,gBAAgB,WAAW,IAAI,CAAC,eAAe;oBACjD,MAAM,IAAI,aAAa,MAAM;oBAC7B,OAAO,eAAe,GAAG;wBACtB,iBAA2B,OAAX,GAAE,WAAsB,OAAb,cAAa;qBAC1C;gBACH;gBACA,sCAAsC;gBACtC,IAAI,aAAa,UAAU,IAAI,OAAO,IAAI;oBACxC,OAAO,eAAe,GAAG;wBAAE,qBAAqC,OAAjB,UAAU,IAAI,IAAG;qBAAI;gBACtE;YACF;YACA,OAAO;QACT;QAEA,0EAA0E;QAC1E,IACE,aACA,CAAC,eAAe,UAAU,CAAC,UAAU,KACrC,CAAC,gBACD,CAAC,WACD;YACA,4EAA4E;YAC5E,OAAO,IAAI,CAAC,KAAK,CACf;gBACE,WAAW;gBACX;gBACA,YAAY;gBACZ,cAAc;gBACd,WAAW;YACb,GACA;QAEJ;QAEA,gFAAgF;QAChF,IAAI,aAAa,gBAAgB,WAAW,IAAI,CAAC,eAAe;YAC9D,MAAM,IAAI,aAAa,MAAM;YAC7B,MAAM,iBAAiB,AAAC,iBAA2B,OAAX,GAAE,WAAsB,OAAb,cAAa;YAChE,2CAA2C;YAC3C,MAAM,SAAS,IAAI,CAAC,KAAK,CACvB;gBACE;gBACA;gBACA,YAAY;gBACZ,cAAc;gBACd;YACF,GACA;YAEF,+BAA+B;YAC/B,OAAO;gBACL,GAAG,MAAM;gBACT,iBAAiB;oBAAC;iBAAe;YACnC;QACF;QAEA,gFAAgF;QAChF,IAAI,aAAa,aAAa,UAAU,IAAI,OAAO,IAAI;YACrD,MAAM,iBAAiB,AAAC,oBAAoC,OAAjB,UAAU,IAAI,IAAG;YAC5D,MAAM,SAAS,IAAI,CAAC,KAAK,CACvB;gBACE;gBACA;gBACA,YAAY;gBACZ,cAAc;gBACd;YACF,GACA;YAEF,OAAO;gBACL,GAAG,MAAM;gBACT,iBAAiB;oBAAC;iBAAe;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IAhLA,aAAc;QACZ,KAAK,CAAC,UAAU,QAAQ;YACtB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAwKF;AAEA;;CAEC,GACD,MAAM,yBAAyB,8KAAA,CAAA,UAAU;IAYvC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,SAAS,SAAS,EAClB,OAAO,UAAU,EACjB,cAAc,YAAY,EAC1B,eAAe,SAAS,EACxB,YAAY,KAAK,EACjB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,WAAW,SAAS;YACxB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,uBAAuB,8KAAA,CAAA,UAAU;IAYrC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,YAAY,SAAS,EACrB,UAAU,UAAU,EACpB,iBAAiB,YAAY,EAC7B,kBAAkB,SAAS,EAC3B,eAAe,KAAK,EACpB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,cAAc,YAAY;YAC9B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;;uCAGe", "debugId": null}}, {"offset": {"line": 1821, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/PriorityFilter.js"], "sourcesContent": ["import BaseFilter from \"./BaseFilter\";\r\n\r\n/**\r\n * Pronas (PN) Filter Handler\r\n */\r\nclass PronasFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdpn\", \"pronas\", {\r\n      schema: \"dbref\",\r\n      table: \"t_prinas\",\r\n      alias: \"pn\",\r\n      nameField: \"nmpn\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdpn=pn.kdpn\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      KdPN: isEnabled,\r\n      PN: pilihValue,\r\n      PNkondisi: kondisiValue,\r\n      opsikataPN: kataValue,\r\n      pnradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Propres (PP) Filter Handler\r\n */\r\nclass PropresFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdpp\", \"propres\", {\r\n      schema: \"dbref\",\r\n      table: \"t_priprog\",\r\n      alias: \"pp\",\r\n      nameField: \"nmpp\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdpp=pp.kdpp\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      KdPP: isEnabled,\r\n      PP: pilihValue,\r\n      PPkondisi: kondisiValue,\r\n      opsikataPP: kataValue,\r\n      ppradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    // Handle composite PP value (e.g., \"01-01\" should become \"01\")\r\n    let processedPilihValue = pilihValue;\r\n    if (pilihValue && pilihValue.includes(\"-\")) {\r\n      processedPilihValue = pilihValue.split(\"-\")[1]; // Get the part after the dash\r\n      console.log(\r\n        `PropresFilter (inquiry) - Extracted PP from composite value: \"${pilihValue}\" -> \"${processedPilihValue}\"`\r\n      );\r\n    }\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue: processedPilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Kegiatan Prioritas (KP) Filter Handler\r\n */\r\nclass KegiatanPrioritasFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdkp\", \"kegiatanprioritas\", {\r\n      schema: \"dbref\",\r\n      table: \"t_prigiat\", // Base table name, year will be appended\r\n      alias: \"pg\",\r\n      nameField: \"nmkp\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdkp=pg.kdkp AND a.kdpp=pg.kdpp AND a.kdpn=pg.kdpn\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      KdKegPP: isEnabled,\r\n      kegiatanprioritas: pilihValue,\r\n      kegiatanprioritasradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n    const kondisiValue = undefined;\r\n    const kataValue = undefined;\r\n\r\n    // DEBUG: Log the state values\r\n    console.log(\"🔍 KegiatanPrioritasFilter DEBUG:\", {\r\n      isEnabled,\r\n      pilihValue,\r\n      radio,\r\n      thang,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n\r\n    const result = this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n\r\n    // DEBUG: Log the result\r\n    console.log(\"🔍 KegiatanPrioritasFilter RESULT:\", result);\r\n\r\n    // SPECIAL HANDLING: For Kegiatan Prioritas, we always need the JOIN clause when enabled,\r\n    // even if pilihValue is \"00\" (Semua) and radio is \"4\" (Jangan Tampilkan)\r\n    // This ensures the table is included in the query structure for proper filtering\r\n    if (isEnabled && !result.joinClause) {\r\n      result.joinClause = this.buildJoinClause(thang);\r\n      console.log(\"🔍 KegiatanPrioritasFilter FORCED JOIN:\", result.joinClause);\r\n    }\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Prioritas (PRI) Filter Handler\r\n */\r\nclass PrioritasFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdproy\", \"prioritas\", {\r\n      schema: \"dbref\",\r\n      table: \"t_priproy\",\r\n      alias: \"pri\",\r\n      nameField: \"nmproy\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdproy=pri.kdproy\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      KdPRI: isEnabled,\r\n      PRI: pilihValue,\r\n      PRIkondisi: kondisiValue,\r\n      opsikataPRI: kataValue,\r\n      priradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Tema Filter Handler\r\n */\r\nclass TemaFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdtema\", \"tema\", {\r\n      schema: \"dbref\",\r\n      table: \"t_tema\",\r\n      alias: \"tm\",\r\n      nameField: \"nmtema\",\r\n      hasYear: true,\r\n      joinCondition: \"a.kdtema=tm.kdtema\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      KdTema: isEnabled,\r\n      Tema: pilihValue,\r\n      Temakondisi: kondisiValue,\r\n      opsikataTema: kataValue,\r\n      temaradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Mega Project (MP) Filter Handler\r\n */\r\nclass MegaProjectFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kdmp\", \"megaproject\", {\r\n      schema: \"dbref\",\r\n      table: \"t_mp\",\r\n      alias: \"mp\",\r\n      nameField: \"nmmp\",\r\n      hasYear: false, // Exception: t_mp table doesn't use year suffix\r\n      joinCondition: \"a.kdmp=mp.kdmp\",\r\n    });\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const {\r\n      KdMP: isEnabled,\r\n      MP: pilihValue,\r\n      MPkondisi: kondisiValue,\r\n      opsikataMP: kataValue,\r\n      mpradio: radio,\r\n      thang,\r\n    } = inquiryState;\r\n\r\n    return this.build(\r\n      {\r\n        isEnabled,\r\n        radio,\r\n        pilihValue,\r\n        kondisiValue,\r\n        kataValue,\r\n      },\r\n      thang\r\n    );\r\n  }\r\n}\r\n\r\nexport {\r\n  PronasFilter,\r\n  PropresFilter,\r\n  KegiatanPrioritasFilter,\r\n  PrioritasFilter,\r\n  TemaFilter,\r\n  MegaProjectFilter,\r\n};\r\nexport default PronasFilter;\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAYnC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,MAAM,SAAS,EACf,IAAI,UAAU,EACd,WAAW,YAAY,EACvB,YAAY,SAAS,EACrB,SAAS,KAAK,EACd,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,QAAQ,UAAU;YACtB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,sBAAsB,8KAAA,CAAA,UAAU;IAYpC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,MAAM,SAAS,EACf,IAAI,UAAU,EACd,WAAW,YAAY,EACvB,YAAY,SAAS,EACrB,SAAS,KAAK,EACd,KAAK,EACN,GAAG;QAEJ,+DAA+D;QAC/D,IAAI,sBAAsB;QAC1B,IAAI,cAAc,WAAW,QAAQ,CAAC,MAAM;YAC1C,sBAAsB,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,8BAA8B;YAC9E,QAAQ,GAAG,CACT,AAAC,iEAAmF,OAAnB,YAAW,UAA4B,OAApB,qBAAoB;QAE5G;QAEA,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA,YAAY;YACZ;YACA;QACF,GACA;IAEJ;IAxCA,aAAc;QACZ,KAAK,CAAC,QAAQ,WAAW;YACvB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAgCF;AAEA;;CAEC,GACD,MAAM,gCAAgC,8KAAA,CAAA,UAAU;IAY9C,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,SAAS,SAAS,EAClB,mBAAmB,UAAU,EAC7B,wBAAwB,KAAK,EAC7B,KAAK,EACN,GAAG;QACJ,MAAM,eAAe;QACrB,MAAM,YAAY;QAElB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,qCAAqC;YAC/C;YACA;YACA;YACA;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,SAAS,IAAI,CAAC,KAAK,CACvB;YACE;YACA;YACA;YACA;YACA;QACF,GACA;QAGF,wBAAwB;QACxB,QAAQ,GAAG,CAAC,sCAAsC;QAElD,yFAAyF;QACzF,yEAAyE;QACzE,iFAAiF;QACjF,IAAI,aAAa,CAAC,OAAO,UAAU,EAAE;YACnC,OAAO,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;YACzC,QAAQ,GAAG,CAAC,2CAA2C,OAAO,UAAU;QAC1E;QAEA,OAAO;IACT;IArDA,aAAc;QACZ,KAAK,CAAC,QAAQ,qBAAqB;YACjC,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AA6CF;AAEA;;CAEC,GACD,MAAM,wBAAwB,8KAAA,CAAA,UAAU;IAYtC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,OAAO,SAAS,EAChB,KAAK,UAAU,EACf,YAAY,YAAY,EACxB,aAAa,SAAS,EACtB,UAAU,KAAK,EACf,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,UAAU,aAAa;YAC3B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,mBAAmB,8KAAA,CAAA,UAAU;IAYjC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,QAAQ,SAAS,EACjB,MAAM,UAAU,EAChB,aAAa,YAAY,EACzB,cAAc,SAAS,EACvB,WAAW,KAAK,EAChB,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,UAAU,QAAQ;YACtB,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;AAEA;;CAEC,GACD,MAAM,0BAA0B,8KAAA,CAAA,UAAU;IAYxC,eAAe,YAAY,EAAE;QAC3B,MAAM,EACJ,MAAM,SAAS,EACf,IAAI,UAAU,EACd,WAAW,YAAY,EACvB,YAAY,SAAS,EACrB,SAAS,KAAK,EACd,KAAK,EACN,GAAG;QAEJ,OAAO,IAAI,CAAC,KAAK,CACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;IAEJ;IA/BA,aAAc;QACZ,KAAK,CAAC,QAAQ,eAAe;YAC3B,QAAQ;YACR,OAAO;YACP,OAAO;YACP,WAAW;YACX,SAAS;YACT,eAAe;QACjB;IACF;AAuBF;;uCAUe", "debugId": null}}, {"offset": {"line": 2013, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/SpecialFilter.js"], "sourcesContent": ["/**\r\n * Special Filters for jenlap = 7 (Priority Programs)\r\n * Handles: Inflas<PERSON>, <PERSON>, Kemiskinan, Pemilu, IKN, Pangan\r\n * Based on original SQL.jsx jenlap = 6 logic\r\n */\r\n\r\nimport BaseFilter from \"./BaseFilter\";\r\n\r\n/**\r\n * Inflasi Filter - Handles inflation intervention filters\r\n */\r\nexport class InflasiFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"inflasi\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, Inflasi, inflasiradio, opsiInflasi } = inquiryState;\r\n\r\n    // Only activate this filter for jenlap 6, never for jenlap 7\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // FUNGSI KDINFLASI - Based on original jenlap = 6 logic\r\n    if (inflasiradio === \"1\" && Inflasi !== \"XX\") {\r\n      result.columns.push(\"a.inf_intervensi\", \"a.inf_pengeluaran\");\r\n      result.groupBy.push(\"a.inf_intervensi\", \"a.inf_pengeluaran\");\r\n    }\r\n\r\n    if (inflasiradio === \"2\" && Inflasi !== \"XX\") {\r\n      result.columns.push(\r\n        \"a.inf_intervensi\",\r\n        \"bb.ur_inf_intervensi\",\r\n        \"a.inf_pengeluaran\",\r\n        \"inf.ur_inf_pengeluaran\"\r\n      );\r\n      result.joinClause =\r\n        \" LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi\" +\r\n        \" LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran\";\r\n      result.groupBy.push(\"a.inf_intervensi\", \"a.inf_pengeluaran\");\r\n    }\r\n\r\n    if (inflasiradio === \"3\" && Inflasi !== \"XX\") {\r\n      result.columns.push(\"bb.ur_inf_intervensi\", \"inf.ur_inf_pengeluaran\");\r\n      result.joinClause =\r\n        \" LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi\" +\r\n        \" LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran\";\r\n      result.groupBy.push(\"a.inf_intervensi\", \"a.inf_pengeluaran\");\r\n    }\r\n\r\n    if (inflasiradio === \"4\") {\r\n      result.columns = [];\r\n    }\r\n\r\n    // Filter condition when opsiInflasi is set\r\n    if (opsiInflasi === \"pilihInflasi\" && Inflasi !== \"XX\") {\r\n      if (Inflasi && Inflasi !== \"00\") {\r\n        result.whereConditions.push(\r\n          \"(a.inf_intervensi <> 'NULL' OR a.inf_pengeluaran <> 'NULL')\"\r\n        );\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Stunting Filter - Handles stunting intervention filters\r\n */\r\nexport class StuntingFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"stunting\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, Stunting, stuntingradio } = inquiryState;\r\n\r\n    // Only activate this filter for jenlap 6, never for jenlap 7\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // FUNGSI KDSTUNTING - Based on original jenlap = 6 logic\r\n    if (stuntingradio === \"1\" && Stunting !== \"XX\") {\r\n      result.columns.push(\"a.stun_intervensi\");\r\n      result.groupBy.push(\"a.stun_intervensi\");\r\n    }\r\n\r\n    if (stuntingradio === \"2\" && Stunting !== \"XX\") {\r\n      result.columns.push(\"a.stun_intervensi\", \"stun.ur_stun_intervensi\");\r\n      result.joinClause =\r\n        \" LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi\";\r\n      result.groupBy.push(\"a.stun_intervensi\");\r\n    }\r\n\r\n    if (stuntingradio === \"3\" && Stunting !== \"XX\") {\r\n      result.columns.push(\"stun.ur_stun_intervensi\");\r\n      result.joinClause =\r\n        \" LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi\";\r\n      result.groupBy.push(\"a.stun_intervensi\");\r\n    }\r\n\r\n    if (stuntingradio === \"4\") {\r\n      result.columns = [];\r\n    }\r\n\r\n    // Note: opsiStunting field doesn't exist in state, removing filter condition\r\n    // TODO: Add opsiStunting to state if needed for filtering by specific stunting intervention\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Kemiskinan Filter - Handles extreme poverty filters\r\n */\r\nexport class KemiskinanFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"kemiskinan\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, Miskin, kemiskinanradio, opsiKemiskinan } = inquiryState;\r\n\r\n    // Only activate this filter for jenlap 6, never for jenlap 7\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // FUNGSI KEMISKINAN EKSTRIM - Based on original jenlap = 6 logic\r\n    if (kemiskinanradio === \"1\" && Miskin !== \"XX\") {\r\n      result.columns.push(\"a.kemiskinan_ekstrim\");\r\n      result.groupBy.push(\"a.kemiskinan_ekstrim\");\r\n    }\r\n\r\n    if (kemiskinanradio === \"2\" && Miskin !== \"XX\") {\r\n      result.columns.push(\r\n        \"a.kemiskinan_ekstrim\",\r\n        \"(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim\"\r\n      );\r\n      result.groupBy.push(\"a.kemiskinan_ekstrim\");\r\n    }\r\n\r\n    if (kemiskinanradio === \"3\" && Miskin !== \"XX\") {\r\n      result.columns.push(\r\n        \"(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim\"\r\n      );\r\n      result.groupBy.push(\"a.kemiskinan_ekstrim\");\r\n    }\r\n\r\n    if (kemiskinanradio === \"4\") {\r\n      result.columns = [];\r\n    }\r\n\r\n    // Filter condition when opsiKemiskinan is set\r\n    if (opsiKemiskinan === \"pilihKemiskinan\" && Miskin !== \"XX\") {\r\n      if (Miskin && Miskin !== \"00\") {\r\n        result.whereConditions.push(`a.kemiskinan_ekstrim = '${Miskin}'`);\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Pemilu Filter - Handles election budget filters\r\n */\r\nexport class PemiluFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"pemilu\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, Pemilu, pemiluradio } = inquiryState;\r\n\r\n    // Only activate this filter for jenlap 6, never for jenlap 7\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // FUNGSI BELANJA PEMILU - Based on original jenlap = 6 logic\r\n    if (pemiluradio === \"1\" && Pemilu !== \"XX\") {\r\n      result.columns.push(\"a.pemilu\");\r\n      result.groupBy.push(\"a.pemilu\");\r\n    }\r\n\r\n    if (pemiluradio === \"2\" && Pemilu !== \"XX\") {\r\n      result.columns.push(\r\n        \"a.pemilu\",\r\n        \"(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu\"\r\n      );\r\n      result.groupBy.push(\"a.pemilu\");\r\n    }\r\n\r\n    if (pemiluradio === \"3\" && Pemilu !== \"XX\") {\r\n      result.columns.push(\r\n        \"(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu\"\r\n      );\r\n      result.groupBy.push(\"a.pemilu\");\r\n    }\r\n\r\n    if (pemiluradio === \"4\") {\r\n      result.columns = [];\r\n    }\r\n\r\n    // Note: opsiPemilu field doesn't exist in state, removing filter condition\r\n    // TODO: Add opsiPemilu to state if needed for filtering by specific election type\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * IKN Filter - Handles IKN (Ibu Kota Nusantara) budget filters\r\n */\r\nexport class IknFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"ikn\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, Ikn, iknradio, opsiIkn } = inquiryState;\r\n\r\n    // Only activate this filter for jenlap 6, never for jenlap 7\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // FUNGSI BELANJA IKN - Based on original jenlap = 6 logic\r\n    if (iknradio === \"1\" && Ikn !== \"XX\") {\r\n      result.columns.push(\"a.ikn\");\r\n      result.groupBy.push(\"a.ikn\");\r\n    }\r\n\r\n    if (iknradio === \"2\" && Ikn !== \"XX\") {\r\n      result.columns.push(\r\n        \"a.ikn\",\r\n        \"(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn\"\r\n      );\r\n      result.groupBy.push(\"a.ikn\");\r\n    }\r\n\r\n    if (iknradio === \"3\" && Ikn !== \"XX\") {\r\n      result.columns.push(\r\n        \"(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn\"\r\n      );\r\n      result.groupBy.push(\"a.ikn\");\r\n    }\r\n\r\n    if (iknradio === \"4\") {\r\n      result.columns = [];\r\n    }\r\n\r\n    // Filter condition when opsiIkn is set\r\n    if (opsiIkn === \"pilihikn\" && Ikn !== \"XX\") {\r\n      if (Ikn && Ikn !== \"00\") {\r\n        result.whereConditions.push(`a.ikn = '${Ikn}'`);\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Pangan Filter - Handles food security budget filters\r\n */\r\nexport class PanganFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"pangan\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, Pangan, panganradio } = inquiryState;\r\n\r\n    // Only activate this filter for jenlap 6, never for jenlap 7\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // FUNGSI BELANJA KETAHANAN PANGAN - Based on original jenlap = 6 logic\r\n    if (panganradio === \"1\" && Pangan !== \"XX\") {\r\n      result.columns.push(\"a.pangan\");\r\n      result.groupBy.push(\"a.pangan\");\r\n    }\r\n\r\n    if (panganradio === \"2\" && Pangan !== \"XX\") {\r\n      result.columns.push(\r\n        \"a.pangan\",\r\n        \"(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan\"\r\n      );\r\n      result.groupBy.push(\"a.pangan\");\r\n    }\r\n\r\n    if (panganradio === \"3\" && Pangan !== \"XX\") {\r\n      result.columns.push(\r\n        \"(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan\"\r\n      );\r\n      result.groupBy.push(\"a.pangan\");\r\n    }\r\n\r\n    if (panganradio === \"4\") {\r\n      result.columns = [];\r\n    }\r\n\r\n    // Note: opsiPangan field doesn't exist in state, removing filter condition\r\n    // TODO: Add opsiPangan to state if needed for filtering by specific food security type\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Blokir Filter - Handles block type filters for jenlap = 6\r\n */\r\nexport class BlokirFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"blokir\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, thang } = inquiryState;\r\n\r\n    if (jenlap !== \"6\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // KODE JENIS BLOKIR - Based on original jenlap = 7 logic\r\n    result.columns.push(\"a.kdblokir\", \"a.nmblokir\");\r\n    result.groupBy.push(\"a.kdblokir\");\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * Special grouping logic for jenlap = 7 (original jenlap = 6)\r\n */\r\nexport class SpecialGroupingFilter extends BaseFilter {\r\n  constructor() {\r\n    super(\"specialgrouping\");\r\n  }\r\n\r\n  buildFromState(inquiryState) {\r\n    const { jenlap, thang } = inquiryState;\r\n\r\n    if (jenlap !== \"7\") {\r\n      return this.getEmptyResult();\r\n    }\r\n\r\n    const result = this.getEmptyResult();\r\n\r\n    // PENAMBAHAN GROUPING DATA CAPAIAN OUTPUT\r\n    if (thang >= \"2021\") {\r\n      result.groupBy.push(\"a.sat\", \"a.os\", \"a.ket\");\r\n    } else {\r\n      result.groupBy.push(\"a.sat\");\r\n    }\r\n\r\n    return result;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;AAED;;AAKO,MAAM,sBAAsB,8KAAA,CAAA,UAAU;IAK3C,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG;QAEvD,6DAA6D;QAC7D,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,wDAAwD;QACxD,IAAI,iBAAiB,OAAO,YAAY,MAAM;YAC5C,OAAO,OAAO,CAAC,IAAI,CAAC,oBAAoB;YACxC,OAAO,OAAO,CAAC,IAAI,CAAC,oBAAoB;QAC1C;QAEA,IAAI,iBAAiB,OAAO,YAAY,MAAM;YAC5C,OAAO,OAAO,CAAC,IAAI,CACjB,oBACA,wBACA,qBACA;YAEF,OAAO,UAAU,GACf,iFACA;YACF,OAAO,OAAO,CAAC,IAAI,CAAC,oBAAoB;QAC1C;QAEA,IAAI,iBAAiB,OAAO,YAAY,MAAM;YAC5C,OAAO,OAAO,CAAC,IAAI,CAAC,wBAAwB;YAC5C,OAAO,UAAU,GACf,iFACA;YACF,OAAO,OAAO,CAAC,IAAI,CAAC,oBAAoB;QAC1C;QAEA,IAAI,iBAAiB,KAAK;YACxB,OAAO,OAAO,GAAG,EAAE;QACrB;QAEA,2CAA2C;QAC3C,IAAI,gBAAgB,kBAAkB,YAAY,MAAM;YACtD,IAAI,WAAW,YAAY,MAAM;gBAC/B,OAAO,eAAe,CAAC,IAAI,CACzB;YAEJ;QACF;QAEA,OAAO;IACT;IAvDA,aAAc;QACZ,KAAK,CAAC;IACR;AAsDF;AAKO,MAAM,uBAAuB,8KAAA,CAAA,UAAU;IAK5C,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG;QAE5C,6DAA6D;QAC7D,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,yDAAyD;QACzD,IAAI,kBAAkB,OAAO,aAAa,MAAM;YAC9C,OAAO,OAAO,CAAC,IAAI,CAAC;YACpB,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,kBAAkB,OAAO,aAAa,MAAM;YAC9C,OAAO,OAAO,CAAC,IAAI,CAAC,qBAAqB;YACzC,OAAO,UAAU,GACf;YACF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,kBAAkB,OAAO,aAAa,MAAM;YAC9C,OAAO,OAAO,CAAC,IAAI,CAAC;YACpB,OAAO,UAAU,GACf;YACF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,kBAAkB,KAAK;YACzB,OAAO,OAAO,GAAG,EAAE;QACrB;QAEA,6EAA6E;QAC7E,4FAA4F;QAE5F,OAAO;IACT;IA1CA,aAAc;QACZ,KAAK,CAAC;IACR;AAyCF;AAKO,MAAM,yBAAyB,8KAAA,CAAA,UAAU;IAK9C,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG;QAE5D,6DAA6D;QAC7D,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,iEAAiE;QACjE,IAAI,oBAAoB,OAAO,WAAW,MAAM;YAC9C,OAAO,OAAO,CAAC,IAAI,CAAC;YACpB,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,oBAAoB,OAAO,WAAW,MAAM;YAC9C,OAAO,OAAO,CAAC,IAAI,CACjB,wBACA;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,oBAAoB,OAAO,WAAW,MAAM;YAC9C,OAAO,OAAO,CAAC,IAAI,CACjB;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,oBAAoB,KAAK;YAC3B,OAAO,OAAO,GAAG,EAAE;QACrB;QAEA,8CAA8C;QAC9C,IAAI,mBAAmB,qBAAqB,WAAW,MAAM;YAC3D,IAAI,UAAU,WAAW,MAAM;gBAC7B,OAAO,eAAe,CAAC,IAAI,CAAC,AAAC,2BAAiC,OAAP,QAAO;YAChE;QACF;QAEA,OAAO;IACT;IA/CA,aAAc;QACZ,KAAK,CAAC;IACR;AA8CF;AAKO,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAK1C,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;QAExC,6DAA6D;QAC7D,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,6DAA6D;QAC7D,IAAI,gBAAgB,OAAO,WAAW,MAAM;YAC1C,OAAO,OAAO,CAAC,IAAI,CAAC;YACpB,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,gBAAgB,OAAO,WAAW,MAAM;YAC1C,OAAO,OAAO,CAAC,IAAI,CACjB,YACA;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,gBAAgB,OAAO,WAAW,MAAM;YAC1C,OAAO,OAAO,CAAC,IAAI,CACjB;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,gBAAgB,KAAK;YACvB,OAAO,OAAO,GAAG,EAAE;QACrB;QAEA,2EAA2E;QAC3E,kFAAkF;QAElF,OAAO;IACT;IA3CA,aAAc;QACZ,KAAK,CAAC;IACR;AA0CF;AAKO,MAAM,kBAAkB,8KAAA,CAAA,UAAU;IAKvC,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAE3C,6DAA6D;QAC7D,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,0DAA0D;QAC1D,IAAI,aAAa,OAAO,QAAQ,MAAM;YACpC,OAAO,OAAO,CAAC,IAAI,CAAC;YACpB,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,aAAa,OAAO,QAAQ,MAAM;YACpC,OAAO,OAAO,CAAC,IAAI,CACjB,SACA;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,aAAa,OAAO,QAAQ,MAAM;YACpC,OAAO,OAAO,CAAC,IAAI,CACjB;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,aAAa,KAAK;YACpB,OAAO,OAAO,GAAG,EAAE;QACrB;QAEA,uCAAuC;QACvC,IAAI,YAAY,cAAc,QAAQ,MAAM;YAC1C,IAAI,OAAO,QAAQ,MAAM;gBACvB,OAAO,eAAe,CAAC,IAAI,CAAC,AAAC,YAAe,OAAJ,KAAI;YAC9C;QACF;QAEA,OAAO;IACT;IA/CA,aAAc;QACZ,KAAK,CAAC;IACR;AA8CF;AAKO,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAK1C,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;QAExC,6DAA6D;QAC7D,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,uEAAuE;QACvE,IAAI,gBAAgB,OAAO,WAAW,MAAM;YAC1C,OAAO,OAAO,CAAC,IAAI,CAAC;YACpB,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,gBAAgB,OAAO,WAAW,MAAM;YAC1C,OAAO,OAAO,CAAC,IAAI,CACjB,YACA;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,gBAAgB,OAAO,WAAW,MAAM;YAC1C,OAAO,OAAO,CAAC,IAAI,CACjB;YAEF,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,IAAI,gBAAgB,KAAK;YACvB,OAAO,OAAO,GAAG,EAAE;QACrB;QAEA,2EAA2E;QAC3E,uFAAuF;QAEvF,OAAO;IACT;IA3CA,aAAc;QACZ,KAAK,CAAC;IACR;AA0CF;AAKO,MAAM,qBAAqB,8KAAA,CAAA,UAAU;IAK1C,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QAE1B,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,yDAAyD;QACzD,OAAO,OAAO,CAAC,IAAI,CAAC,cAAc;QAClC,OAAO,OAAO,CAAC,IAAI,CAAC;QAEpB,OAAO;IACT;IAlBA,aAAc;QACZ,KAAK,CAAC;IACR;AAiBF;AAKO,MAAM,8BAA8B,8KAAA,CAAA,UAAU;IAKnD,eAAe,YAAY,EAAE;QAC3B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;QAE1B,IAAI,WAAW,KAAK;YAClB,OAAO,IAAI,CAAC,cAAc;QAC5B;QAEA,MAAM,SAAS,IAAI,CAAC,cAAc;QAElC,0CAA0C;QAC1C,IAAI,SAAS,QAAQ;YACnB,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS,QAAQ;QACvC,OAAO;YACL,OAAO,OAAO,CAAC,IAAI,CAAC;QACtB;QAEA,OAAO;IACT;IArBA,aAAc;QACZ,KAAK,CAAC;IACR;AAoBF", "debugId": null}}, {"offset": {"line": 2281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/FilterBuilder.js"], "sourcesContent": ["/**\r\n * Filter Builder - Orchestrates all filter modules\r\n * Provides a unified interface to build all filters\r\n */\r\n\r\n// Import all filter modules\r\nimport {\r\n  DepartmentFilter,\r\n  UnitFilter,\r\n  DekonFilter,\r\n  SatkerFilter,\r\n} from \"./DepartmentFilter\";\r\nimport {\r\n  <PERSON>vinsiFilter,\r\n  KabkotaFilter,\r\n  Ka<PERSON><PERSON><PERSON><PERSON>ilter,\r\n  KppnFilter,\r\n} from \"./LocationFilter\";\r\nimport {\r\n  FungsiFilter,\r\n  SubFungsiFilter,\r\n  ProgramFilter,\r\n  KegiatanFilter,\r\n  OutputFilter,\r\n  SubOutputFilter,\r\n} from \"./ProgramFilter\";\r\nimport { AkunFilter, SumberDanaFilter, RegisterFilter } from \"./AccountFilter\";\r\nimport {\r\n  PronasFilter,\r\n  PropresFilter,\r\n  KegiatanPrioritasFilter,\r\n  PrioritasFilter,\r\n  TemaFilter,\r\n  MegaProjectFilter,\r\n} from \"./PriorityFilter\";\r\nimport {\r\n  InflasiFilter,\r\n  StuntingFilter,\r\n  KemiskinanFilter,\r\n  PemiluFilter,\r\n  Ikn<PERSON>ilter,\r\n  <PERSON>gan<PERSON><PERSON><PERSON>,\r\n  B<PERSON>kir<PERSON><PERSON>er,\r\n  SpecialGroupingFilter,\r\n} from \"./SpecialFilter\";\r\n\r\nclass FilterBuilder {\r\n  constructor() {\r\n    // Initialize all filter instances\r\n    this.filters = {\r\n      // Department & Organization\r\n      department: new DepartmentFilter(),\r\n      unit: new UnitFilter(),\r\n      dekon: new DekonFilter(),\r\n      satker: new SatkerFilter(),\r\n\r\n      // Location\r\n      provinsi: new ProvinsiFilter(),\r\n      kabkota: new KabkotaFilter(),\r\n      kanwil: new KanwilFilter(),\r\n      kppn: new KppnFilter(),\r\n\r\n      // Program Structure\r\n      fungsi: new FungsiFilter(),\r\n      subfungsi: new SubFungsiFilter(),\r\n      program: new ProgramFilter(),\r\n      kegiatan: new KegiatanFilter(),\r\n      output: new OutputFilter(),\r\n      suboutput: new SubOutputFilter(),\r\n\r\n      // Account\r\n      akun: new AkunFilter(),\r\n      sdana: new SumberDanaFilter(),\r\n      register: new RegisterFilter(),\r\n\r\n      // Priority Programs\r\n      pronas: new PronasFilter(),\r\n      propres: new PropresFilter(),\r\n      kegiatanprioritas: new KegiatanPrioritasFilter(),\r\n      prioritas: new PrioritasFilter(),\r\n      tema: new TemaFilter(),\r\n      megaproject: new MegaProjectFilter(),\r\n\r\n      // Special Filters (jenlap-specific)\r\n      inflasi: new InflasiFilter(),\r\n      stunting: new StuntingFilter(),\r\n      kemiskinan: new KemiskinanFilter(),\r\n      pemilu: new PemiluFilter(),\r\n      ikn: new IknFilter(),\r\n      pangan: new PanganFilter(),\r\n      blokir: new BlokirFilter(),\r\n      specialgrouping: new SpecialGroupingFilter(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Build all enabled filters from inquiry state\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {object} - Aggregated filter results\r\n   */\r\n  buildAllFilters(inquiryState) {\r\n    const result = {\r\n      columns: [],\r\n      joinClauses: [],\r\n      groupBy: [],\r\n      whereConditions: [],\r\n    };\r\n\r\n    // Only build filters that are enabled (except blokir and specialgrouping, which are jenlap-specific)\r\n    Object.entries(this.filters).forEach(([key, filter]) => {\r\n      let enabled = false;\r\n      if (key === \"blokir\") {\r\n        // Only enable blokir for jenlap 7 (Pergerakan Blokir Bulanan per Jenis)\r\n        enabled = inquiryState.jenlap === \"7\";\r\n      } else if (key === \"specialgrouping\") {\r\n        // Always enable specialgrouping for jenlap 6 (Volume Output Kegiatan - Data Caput)\r\n        enabled = inquiryState.jenlap === \"6\";\r\n      } else {\r\n        enabled = this.isFilterEnabled(key, inquiryState);\r\n      }\r\n\r\n      // Debug special filters\r\n      if (\r\n        [\r\n          \"inflasi\",\r\n          \"stunting\",\r\n          \"kemiskinan\",\r\n          \"pemilu\",\r\n          \"ikn\",\r\n          \"pangan\",\r\n        ].includes(key)\r\n      ) {\r\n        console.log(`🔍 Special Filter Debug - ${key}:`, {\r\n          enabled,\r\n          jenlap: inquiryState.jenlap,\r\n          filterSwitch: this.getFilterSwitchValue(key, inquiryState),\r\n          radioValue: this.getFilterRadioValue(key, inquiryState),\r\n          optionValue: this.getFilterOptionValue(key, inquiryState),\r\n        });\r\n      }\r\n\r\n      if (!enabled) return;\r\n      try {\r\n        const filterResult = filter.buildFromState(inquiryState);\r\n\r\n        if (filterResult.columns.length > 0) {\r\n          result.columns.push(...filterResult.columns);\r\n        }\r\n        if (filterResult.joinClause) {\r\n          result.joinClauses.push(filterResult.joinClause);\r\n        }\r\n        if (filterResult.groupBy.length > 0) {\r\n          result.groupBy.push(...filterResult.groupBy);\r\n        }\r\n        if (filterResult.whereConditions.length > 0) {\r\n          result.whereConditions.push(...filterResult.whereConditions);\r\n        }\r\n      } catch (error) {\r\n        console.warn(`Error building ${key} filter:`, error);\r\n      }\r\n    });\r\n\r\n    // Remove duplicates and optimize\r\n    result.columns = [...new Set(result.columns)];\r\n    result.joinClauses = this.optimizeJoins(result.joinClauses);\r\n    result.groupBy = [...new Set(result.groupBy)];\r\n    result.whereConditions = result.whereConditions.filter(\r\n      (condition) => condition && condition.trim() !== \"\"\r\n    );\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Build specific filter by name\r\n   * @param {string} filterName - Name of the filter\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {object} - Filter result\r\n   */\r\n  buildFilter(filterName, inquiryState) {\r\n    const filter = this.filters[filterName];\r\n    if (!filter) {\r\n      throw new Error(`Filter '${filterName}' not found`);\r\n    }\r\n\r\n    return filter.buildFromState(inquiryState);\r\n  }\r\n\r\n  /**\r\n   * Get list of available filters\r\n   * @returns {string[]} - Array of filter names\r\n   */\r\n  getAvailableFilters() {\r\n    return Object.keys(this.filters);\r\n  }\r\n\r\n  /**\r\n   * Check if a specific filter is enabled in the inquiry state\r\n   * @param {string} filterName - Name of the filter\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {boolean} - Whether the filter is enabled\r\n   */\r\n  isFilterEnabled(filterName, inquiryState) {\r\n    const enabledFields = {\r\n      department: \"kddept\",\r\n      unit: \"unit\",\r\n      dekon: \"kddekon\",\r\n      satker: \"kdsatker\",\r\n      provinsi: \"kdlokasi\",\r\n      kabkota: \"kdkabkota\",\r\n      kanwil: \"kdkanwil\",\r\n      kppn: \"kdkppn\",\r\n      fungsi: \"kdfungsi\",\r\n      subfungsi: \"kdsfungsi\",\r\n      program: \"kdprogram\",\r\n      kegiatan: \"kdgiat\",\r\n      output: \"kdoutput\",\r\n      suboutput: \"kdsoutput\",\r\n      akun: \"kdakun\",\r\n      sdana: \"kdsdana\",\r\n      register: \"kdregister\",\r\n      pronas: \"KdPN\",\r\n      propres: \"KdPP\",\r\n      kegiatanprioritas: \"KdKegPP\",\r\n      prioritas: \"KdPRI\",\r\n      tema: \"KdTema\",\r\n      megaproject: \"KdMP\",\r\n      // Special filters\r\n      inflasi: \"kdInflasi\",\r\n      stunting: \"KdStunting\",\r\n      kemiskinan: \"kdKemiskinan\",\r\n      pemilu: \"KdPemilu\",\r\n      ikn: \"kdIkn\",\r\n      pangan: \"KdPangan\",\r\n    };\r\n\r\n    const enabledField = enabledFields[filterName];\r\n    return enabledField ? Boolean(inquiryState[enabledField]) : false;\r\n  }\r\n\r\n  /**\r\n   * Optimize JOIN clauses by removing duplicates\r\n   * @param {string[]} joinClauses - Array of JOIN clauses\r\n   * @returns {string[]} - Optimized JOIN clauses\r\n   */\r\n  optimizeJoins(joinClauses) {\r\n    // Remove duplicates and empty joins\r\n    const uniqueJoins = [...new Set(joinClauses)].filter(\r\n      (join) => join && join.trim() !== \"\"\r\n    );\r\n\r\n    // Sort joins for consistent ordering\r\n    return uniqueJoins.sort();\r\n  }\r\n\r\n  /**\r\n   * Build role-based access control conditions\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {string} - Access control WHERE clause\r\n   */\r\n  buildAccessControl(inquiryState) {\r\n    const { role, kodekppn, kodekanwil } = inquiryState;\r\n\r\n    if (role === \"3\" && kodekppn) {\r\n      return `a.kdkppn = '${kodekppn}'`;\r\n    } else if (role === \"2\" && kodekanwil) {\r\n      return `a.kdkanwil = '${kodekanwil}'`;\r\n    }\r\n\r\n    return \"\";\r\n  }\r\n\r\n  /**\r\n   * Build complete WHERE clause including access control\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {string} - Complete WHERE clause\r\n   */\r\n  buildWhereClause(inquiryState) {\r\n    const filterResult = this.buildAllFilters(inquiryState);\r\n    const accessControl = this.buildAccessControl(inquiryState);\r\n\r\n    const conditions = [...filterResult.whereConditions];\r\n    if (accessControl) {\r\n      conditions.push(accessControl);\r\n    }\r\n\r\n    if (conditions.length === 0) {\r\n      return \"\";\r\n    }\r\n\r\n    return `WHERE ${conditions.join(\" AND \")}`;\r\n  }\r\n\r\n  /**\r\n   * Validate filter configuration\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {object} - Validation result\r\n   */\r\n  validateFilters(inquiryState) {\r\n    const errors = [];\r\n    const warnings = [];\r\n\r\n    // Check for conflicting filter combinations\r\n    const enabledFilters = this.getAvailableFilters().filter((name) =>\r\n      this.isFilterEnabled(name, inquiryState)\r\n    );\r\n\r\n    // Performance warnings for too many filters\r\n    if (enabledFilters.length > 10) {\r\n      warnings.push(\r\n        `High number of filters enabled (${enabledFilters.length}). Consider reducing for better performance.`\r\n      );\r\n    }\r\n\r\n    // Check for missing required dependencies\r\n    if (\r\n      this.isFilterEnabled(\"unit\", inquiryState) &&\r\n      !this.isFilterEnabled(\"department\", inquiryState)\r\n    ) {\r\n      warnings.push(\r\n        \"Unit filter is enabled but Department filter is not. Consider enabling Department filter for better context.\"\r\n      );\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n      warnings,\r\n      enabledFilters,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get filter statistics\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {object} - Filter statistics\r\n   */\r\n  getFilterStats(inquiryState) {\r\n    const filterResult = this.buildAllFilters(inquiryState);\r\n    const validation = this.validateFilters(inquiryState);\r\n\r\n    return {\r\n      totalFilters: Object.keys(this.filters).length,\r\n      enabledFilters: validation.enabledFilters.length,\r\n      enabledFilterNames: validation.enabledFilters,\r\n      columnsCount: filterResult.columns.length,\r\n      joinsCount: filterResult.joinClauses.length,\r\n      whereConditionsCount: filterResult.whereConditions.length,\r\n      groupByCount: filterResult.groupBy.length,\r\n      validation,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get filter switch value for debugging\r\n   * @param {string} filterName - Name of the filter\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {any} - Filter switch value\r\n   */\r\n  getFilterSwitchValue(filterName, inquiryState) {\r\n    const enabledFields = {\r\n      inflasi: \"kdInflasi\",\r\n      stunting: \"KdStunting\",\r\n      kemiskinan: \"kdKemiskinan\",\r\n      pemilu: \"KdPemilu\",\r\n      ikn: \"kdIkn\",\r\n      pangan: \"KdPangan\",\r\n    };\r\n    const field = enabledFields[filterName];\r\n    return field ? inquiryState[field] : undefined;\r\n  }\r\n\r\n  /**\r\n   * Get filter radio value for debugging\r\n   * @param {string} filterName - Name of the filter\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {any} - Filter radio value\r\n   */\r\n  getFilterRadioValue(filterName, inquiryState) {\r\n    const radioFields = {\r\n      inflasi: \"inflasiradio\",\r\n      stunting: \"stuntingradio\",\r\n      kemiskinan: \"kemiskinanradio\",\r\n      pemilu: \"pemiluradio\",\r\n      ikn: \"iknradio\",\r\n      pangan: \"panganradio\",\r\n    };\r\n    const field = radioFields[filterName];\r\n    return field ? inquiryState[field] : undefined;\r\n  }\r\n\r\n  /**\r\n   * Get filter option value for debugging\r\n   * @param {string} filterName - Name of the filter\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {any} - Filter option value\r\n   */\r\n  getFilterOptionValue(filterName, inquiryState) {\r\n    const optionFields = {\r\n      inflasi: \"Inflasi\",\r\n      stunting: \"Stunting\",\r\n      kemiskinan: \"Miskin\",\r\n      pemilu: \"Pemilu\",\r\n      ikn: \"Ikn\",\r\n      pangan: \"Pangan\",\r\n    };\r\n    const field = optionFields[filterName];\r\n    return field ? inquiryState[field] : undefined;\r\n  }\r\n}\r\n\r\nexport default FilterBuilder;\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,4BAA4B;;;;AAC5B;AAMA;AAMA;AAQA;AACA;AAQA;;;;;;;AAWA,MAAM;IAiDJ;;;;GAIC,GACD,gBAAgB,YAAY,EAAE;QAC5B,MAAM,SAAS;YACb,SAAS,EAAE;YACX,aAAa,EAAE;YACf,SAAS,EAAE;YACX,iBAAiB,EAAE;QACrB;QAEA,qGAAqG;QACrG,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,OAAO;YACjD,IAAI,UAAU;YACd,IAAI,QAAQ,UAAU;gBACpB,wEAAwE;gBACxE,UAAU,aAAa,MAAM,KAAK;YACpC,OAAO,IAAI,QAAQ,mBAAmB;gBACpC,mFAAmF;gBACnF,UAAU,aAAa,MAAM,KAAK;YACpC,OAAO;gBACL,UAAU,IAAI,CAAC,eAAe,CAAC,KAAK;YACtC;YAEA,wBAAwB;YACxB,IACE;gBACE;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAAC,QAAQ,CAAC,MACX;gBACA,QAAQ,GAAG,CAAC,AAAC,6BAAgC,OAAJ,KAAI,MAAI;oBAC/C;oBACA,QAAQ,aAAa,MAAM;oBAC3B,cAAc,IAAI,CAAC,oBAAoB,CAAC,KAAK;oBAC7C,YAAY,IAAI,CAAC,mBAAmB,CAAC,KAAK;oBAC1C,aAAa,IAAI,CAAC,oBAAoB,CAAC,KAAK;gBAC9C;YACF;YAEA,IAAI,CAAC,SAAS;YACd,IAAI;gBACF,MAAM,eAAe,OAAO,cAAc,CAAC;gBAE3C,IAAI,aAAa,OAAO,CAAC,MAAM,GAAG,GAAG;oBACnC,OAAO,OAAO,CAAC,IAAI,IAAI,aAAa,OAAO;gBAC7C;gBACA,IAAI,aAAa,UAAU,EAAE;oBAC3B,OAAO,WAAW,CAAC,IAAI,CAAC,aAAa,UAAU;gBACjD;gBACA,IAAI,aAAa,OAAO,CAAC,MAAM,GAAG,GAAG;oBACnC,OAAO,OAAO,CAAC,IAAI,IAAI,aAAa,OAAO;gBAC7C;gBACA,IAAI,aAAa,eAAe,CAAC,MAAM,GAAG,GAAG;oBAC3C,OAAO,eAAe,CAAC,IAAI,IAAI,aAAa,eAAe;gBAC7D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,AAAC,kBAAqB,OAAJ,KAAI,aAAW;YAChD;QACF;QAEA,iCAAiC;QACjC,OAAO,OAAO,GAAG;eAAI,IAAI,IAAI,OAAO,OAAO;SAAE;QAC7C,OAAO,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,WAAW;QAC1D,OAAO,OAAO,GAAG;eAAI,IAAI,IAAI,OAAO,OAAO;SAAE;QAC7C,OAAO,eAAe,GAAG,OAAO,eAAe,CAAC,MAAM,CACpD,CAAC,YAAc,aAAa,UAAU,IAAI,OAAO;QAGnD,OAAO;IACT;IAEA;;;;;GAKC,GACD,YAAY,UAAU,EAAE,YAAY,EAAE;QACpC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,WAAW;QACvC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM,AAAC,WAAqB,OAAX,YAAW;QACxC;QAEA,OAAO,OAAO,cAAc,CAAC;IAC/B;IAEA;;;GAGC,GACD,sBAAsB;QACpB,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;IACjC;IAEA;;;;;GAKC,GACD,gBAAgB,UAAU,EAAE,YAAY,EAAE;QACxC,MAAM,gBAAgB;YACpB,YAAY;YACZ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;YACV,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,WAAW;YACX,SAAS;YACT,UAAU;YACV,QAAQ;YACR,WAAW;YACX,MAAM;YACN,OAAO;YACP,UAAU;YACV,QAAQ;YACR,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,MAAM;YACN,aAAa;YACb,kBAAkB;YAClB,SAAS;YACT,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,KAAK;YACL,QAAQ;QACV;QAEA,MAAM,eAAe,aAAa,CAAC,WAAW;QAC9C,OAAO,eAAe,QAAQ,YAAY,CAAC,aAAa,IAAI;IAC9D;IAEA;;;;GAIC,GACD,cAAc,WAAW,EAAE;QACzB,oCAAoC;QACpC,MAAM,cAAc;eAAI,IAAI,IAAI;SAAa,CAAC,MAAM,CAClD,CAAC,OAAS,QAAQ,KAAK,IAAI,OAAO;QAGpC,qCAAqC;QACrC,OAAO,YAAY,IAAI;IACzB;IAEA;;;;GAIC,GACD,mBAAmB,YAAY,EAAE;QAC/B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;QAEvC,IAAI,SAAS,OAAO,UAAU;YAC5B,OAAO,AAAC,eAAuB,OAAT,UAAS;QACjC,OAAO,IAAI,SAAS,OAAO,YAAY;YACrC,OAAO,AAAC,iBAA2B,OAAX,YAAW;QACrC;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,iBAAiB,YAAY,EAAE;QAC7B,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;QAC1C,MAAM,gBAAgB,IAAI,CAAC,kBAAkB,CAAC;QAE9C,MAAM,aAAa;eAAI,aAAa,eAAe;SAAC;QACpD,IAAI,eAAe;YACjB,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,OAAO;QACT;QAEA,OAAO,AAAC,SAAiC,OAAzB,WAAW,IAAI,CAAC;IAClC;IAEA;;;;GAIC,GACD,gBAAgB,YAAY,EAAE;QAC5B,MAAM,SAAS,EAAE;QACjB,MAAM,WAAW,EAAE;QAEnB,4CAA4C;QAC5C,MAAM,iBAAiB,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,CAAC,OACxD,IAAI,CAAC,eAAe,CAAC,MAAM;QAG7B,4CAA4C;QAC5C,IAAI,eAAe,MAAM,GAAG,IAAI;YAC9B,SAAS,IAAI,CACX,AAAC,mCAAwD,OAAtB,eAAe,MAAM,EAAC;QAE7D;QAEA,0CAA0C;QAC1C,IACE,IAAI,CAAC,eAAe,CAAC,QAAQ,iBAC7B,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,eACpC;YACA,SAAS,IAAI,CACX;QAEJ;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;YACA;QACF;IACF;IAEA;;;;GAIC,GACD,eAAe,YAAY,EAAE;QAC3B,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;QAC1C,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC;QAExC,OAAO;YACL,cAAc,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;YAC9C,gBAAgB,WAAW,cAAc,CAAC,MAAM;YAChD,oBAAoB,WAAW,cAAc;YAC7C,cAAc,aAAa,OAAO,CAAC,MAAM;YACzC,YAAY,aAAa,WAAW,CAAC,MAAM;YAC3C,sBAAsB,aAAa,eAAe,CAAC,MAAM;YACzD,cAAc,aAAa,OAAO,CAAC,MAAM;YACzC;QACF;IACF;IAEA;;;;;GAKC,GACD,qBAAqB,UAAU,EAAE,YAAY,EAAE;QAC7C,MAAM,gBAAgB;YACpB,SAAS;YACT,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,KAAK;YACL,QAAQ;QACV;QACA,MAAM,QAAQ,aAAa,CAAC,WAAW;QACvC,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;IACvC;IAEA;;;;;GAKC,GACD,oBAAoB,UAAU,EAAE,YAAY,EAAE;QAC5C,MAAM,cAAc;YAClB,SAAS;YACT,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,KAAK;YACL,QAAQ;QACV;QACA,MAAM,QAAQ,WAAW,CAAC,WAAW;QACrC,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;IACvC;IAEA;;;;;GAKC,GACD,qBAAqB,UAAU,EAAE,YAAY,EAAE;QAC7C,MAAM,eAAe;YACnB,SAAS;YACT,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,KAAK;YACL,QAAQ;QACV;QACA,MAAM,QAAQ,YAAY,CAAC,WAAW;QACtC,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;IACvC;IAzWA,aAAc;QACZ,kCAAkC;QAClC,IAAI,CAAC,OAAO,GAAG;YACb,4BAA4B;YAC5B,YAAY,IAAI,oLAAA,CAAA,mBAAgB;YAChC,MAAM,IAAI,oLAAA,CAAA,aAAU;YACpB,OAAO,IAAI,oLAAA,CAAA,cAAW;YACtB,QAAQ,IAAI,oLAAA,CAAA,eAAY;YAExB,WAAW;YACX,UAAU,IAAI,kLAAA,CAAA,iBAAc;YAC5B,SAAS,IAAI,kLAAA,CAAA,gBAAa;YAC1B,QAAQ,IAAI,kLAAA,CAAA,eAAY;YACxB,MAAM,IAAI,kLAAA,CAAA,aAAU;YAEpB,oBAAoB;YACpB,QAAQ,IAAI,iLAAA,CAAA,eAAY;YACxB,WAAW,IAAI,iLAAA,CAAA,kBAAe;YAC9B,SAAS,IAAI,iLAAA,CAAA,gBAAa;YAC1B,UAAU,IAAI,iLAAA,CAAA,iBAAc;YAC5B,QAAQ,IAAI,iLAAA,CAAA,eAAY;YACxB,WAAW,IAAI,iLAAA,CAAA,kBAAe;YAE9B,UAAU;YACV,MAAM,IAAI,iLAAA,CAAA,aAAU;YACpB,OAAO,IAAI,iLAAA,CAAA,mBAAgB;YAC3B,UAAU,IAAI,iLAAA,CAAA,iBAAc;YAE5B,oBAAoB;YACpB,QAAQ,IAAI,kLAAA,CAAA,eAAY;YACxB,SAAS,IAAI,kLAAA,CAAA,gBAAa;YAC1B,mBAAmB,IAAI,kLAAA,CAAA,0BAAuB;YAC9C,WAAW,IAAI,kLAAA,CAAA,kBAAe;YAC9B,MAAM,IAAI,kLAAA,CAAA,aAAU;YACpB,aAAa,IAAI,kLAAA,CAAA,oBAAiB;YAElC,oCAAoC;YACpC,SAAS,IAAI,iLAAA,CAAA,gBAAa;YAC1B,UAAU,IAAI,iLAAA,CAAA,iBAAc;YAC5B,YAAY,IAAI,iLAAA,CAAA,mBAAgB;YAChC,QAAQ,IAAI,iLAAA,CAAA,eAAY;YACxB,KAAK,IAAI,iLAAA,CAAA,YAAS;YAClB,QAAQ,IAAI,iLAAA,CAAA,eAAY;YACxB,QAAQ,IAAI,iLAAA,CAAA,eAAY;YACxB,iBAAiB,IAAI,iLAAA,CAAA,wBAAqB;QAC5C;IACF;AA4TF;uCAEe", "debugId": null}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/QueryBuilder.js"], "sourcesContent": ["/**\r\n * Modular Query Builder\r\n * Replaces the monolithic useQueryBuilder with a clean, modular approach\r\n */\r\n\r\nimport FilterBuilder from \"./FilterBuilder\";\r\n\r\nclass QueryBuilder {\r\n  constructor() {\r\n    this.filterBuilder = new FilterBuilder();\r\n  }\r\n\r\n  /**\r\n   * Build dynamic FROM and SELECT clauses based on jenlap and other parameters\r\n   * @param {object} params - Query parameters\r\n   * @returns {object} - { dynamicFrom, dynamicSelect }\r\n   */\r\n  buildDynamicFromAndSelect(params) {\r\n    const { thang, jenlap, cutoff, tanggal, akumulatif, pembulatan } = params;\r\n\r\n    // Base table names (from original logic)\r\n    const fromapbn = `monev${thang}.pagu_real_detail_harian_dipa_apbn_${thang} a`;\r\n    const fromBulanan = `monev${thang}.pagu_real_detail_bulan_${thang} a`;\r\n    const fromcaput =\r\n      thang >= \"2021\"\r\n        ? `monev${thang}.pagu_output_${thang}_new a`\r\n        : `monev${thang}.pagu_output_${thang}_new a`;\r\n    const fromJnsblokir = `monev${thang}.pa_pagu_blokir_akun_${thang}_bulanan a`;\r\n\r\n    // Generate cutoff-based realizations\r\n    const validCutoff =\r\n      parseInt(cutoff) >= 1 && parseInt(cutoff) <= 12 ? parseInt(cutoff) : 12;\r\n    let realColumns = \"\";\r\n    for (let i = 1; i <= validCutoff; i++) {\r\n      realColumns += `real${i}`;\r\n      if (i !== validCutoff) realColumns += \"+ \";\r\n    }\r\n\r\n    // Build SELECT clauses - use pagu_apbn for jenlap=1, pagu for others\r\n    const paguField = jenlap === \"1\" ? \"a.pagu_apbn\" : \"a.pagu\";\r\n    const pagu = `, ROUND(SUM(${paguField})/${pembulatan},0) AS PAGU`;\r\n    const paguapbn = `, ROUND(SUM(${paguField})/${pembulatan},0) AS PAGU_APBN`;\r\n    const pagudipa = `, ROUND(SUM(a.pagu_dipa)/${pembulatan},0) AS PAGU_DIPA`;\r\n    const blokir = `, ROUND(SUM(a.blokir)/${pembulatan},0) AS BLOKIR`;\r\n    const selectClause = `, ROUND(SUM(${realColumns})/${pembulatan},0) AS REALISASI`;\r\n    const realapbn = `, ROUND(SUM(real1+real2+real3+real4+real5+real6+real7+real8+real9+real10+real11+real12)/${pembulatan},0) AS REALISASI`;\r\n\r\n    // Monthly columns for jenlap=3\r\n    const monthLabels = [\r\n      \"JAN\",\r\n      \"FEB\",\r\n      \"MAR\",\r\n      \"APR\",\r\n      \"MEI\",\r\n      \"JUN\",\r\n      \"JUL\",\r\n      \"AGS\",\r\n      \"SEP\",\r\n      \"OKT\",\r\n      \"NOV\",\r\n      \"DES\",\r\n    ];\r\n    let realbulanan = \"\";\r\n    let realbulananakumulatif = \"\";\r\n    let realBulanan = \"\";\r\n    let blokirBulanan = \"\";\r\n\r\n    for (let i = 1; i <= 12; i++) {\r\n      const monthName = monthLabels[i - 1];\r\n      if (i <= validCutoff) {\r\n        // Monthly realization\r\n        realbulanan += `, ROUND(SUM(real${i})/${pembulatan},0) AS ${monthName}`;\r\n\r\n        // Accumulated monthly\r\n        let accumulatedRealCols = \"\";\r\n        for (let j = 1; j <= i; j++) {\r\n          accumulatedRealCols += `real${j}`;\r\n          if (j < i) accumulatedRealCols += \"+\";\r\n        }\r\n        realbulananakumulatif += `, ROUND(SUM(${accumulatedRealCols})/${pembulatan},0) AS ${monthName}`;\r\n\r\n        // Pagu Bulanan\r\n        realBulanan += `, ROUND(sum(pagu${i})/${pembulatan}, 0) AS ${monthName}`;\r\n\r\n        // Blokir Bulanan\r\n        blokirBulanan += `, ROUND(sum(blokir${i})/${pembulatan}, 0) AS ${monthName}`;\r\n      } else {\r\n        realbulanan += `, 0 AS ${monthName}`;\r\n        realbulananakumulatif += `, 0 AS ${monthName}`;\r\n        realBulanan += `, 0 AS ${monthName}`;\r\n        blokirBulanan += `, 0 AS ${monthName}`;\r\n      }\r\n    }\r\n\r\n    // Special cases\r\n    const selectcaput = `, ROUND(SUM(${paguField})/${pembulatan},0) AS PAGU, ROUND(SUM(${realColumns})/${pembulatan},0) AS REALISASI`;\r\n    const blokircaput = `, ROUND(SUM(a.blokir)/${pembulatan},0) AS BLOKIR`;\r\n    const jnsblokirBulanan = `, a.kdblokir, ROUND(SUM(a.blokir)/${pembulatan},0) AS BLOKIR`;\r\n\r\n    // jenlap = 6: Volume Output Kegiatan (PN) - Data Caput (uses pagu_output table)\r\n    const jenlap6Select = `, a.sat as satuan, SUM(vol) AS vol, sum(${paguField}) as pagu, sum(real1) as rjan, sum(persen1) as pjan, sum(realfisik1) as rpjan, sum(real2) as rfeb, sum(persen2) as pfeb, sum(realfisik2) as rpfeb, sum(real3) as rmar, sum(persen3) as pmar, sum(realfisik3) as rpmar, sum(real4) as rapr, sum(persen4) as papr, sum(realfisik4) as rpapr, sum(real5) as rmei, sum(persen5) as pmei, sum(realfisik5) as rpmei, sum(real6) as rjun, sum(persen6) as pjun, sum(realfisik6) as rpjun, sum(real7) as rjul, sum(persen7) as pjul, sum(realfisik7) as rpjul, sum(real8) as rags, sum(persen8) as pags, sum(realfisik8) as rpags, sum(real9) as rsep, sum(persen9) as psep, sum(realfisik9) as rpsep, sum(real10) as rokt, sum(persen10) as pokt, sum(realfisik10) as rpokt, sum(real11) as rnov, sum(persen11) as pnov, sum(realfisik11) as rpnov, sum(real12) as rdes, sum(persen12) as pdes, sum(realfisik12) as rpdes, os, a.ket`;\r\n\r\n    // jenlap = 7: Pergerakan Blokir Bulanan per Jenis (kdblokir, nmblokir + monthly blokir breakdown)\r\n    const blokirBulananSelect = `, a.kdblokir, a.nmblokir${blokirBulanan}`;\r\n\r\n    // Historical tables\r\n    const monthNames = [\r\n      \"\",\r\n      \"januari\",\r\n      \"februari\",\r\n      \"maret\",\r\n      \"april\",\r\n      \"mei\",\r\n      \"juni\",\r\n      \"juli\",\r\n      \"agustus\",\r\n      \"september\",\r\n      \"oktober\",\r\n      \"november\",\r\n      \"desember\",\r\n    ];\r\n    const historicalTable = `dbhistori.pagu_real_detail_harian_${\r\n      monthNames[parseInt(cutoff)]\r\n    }_${thang} a`;\r\n    const currentTable = `monev${thang}.pagu_real_detail_harian_${thang} a`;\r\n\r\n    // Main switch logic\r\n    let dynamicFrom = \"\";\r\n    let dynamicSelect = \"\";\r\n\r\n    // Debug logging to check jenlap value\r\n    console.log(\"🔍 QueryBuilder Debug:\", {\r\n      jenlap,\r\n      jenlapType: typeof jenlap,\r\n    });\r\n\r\n    switch (jenlap) {\r\n      case \"1\":\r\n        console.log(\"📊 Using jenlap 1 (DIPA APBN)\");\r\n        dynamicFrom = fromapbn;\r\n        dynamicSelect = paguapbn + pagudipa + realapbn + blokir;\r\n        break;\r\n      case \"2\":\r\n        console.log(\"📊 Using jenlap 2 (Pagu Realisasi Blokir)\");\r\n        dynamicFrom = tanggal ? historicalTable : currentTable;\r\n        dynamicSelect = pagu + selectClause + blokir;\r\n        break;\r\n      case \"3\":\r\n        console.log(\"📊 Using jenlap 3 (Realisasi Bulanan)\");\r\n        dynamicFrom = tanggal ? historicalTable : currentTable;\r\n        dynamicSelect =\r\n          jenlap === \"3\" && akumulatif === \"1\"\r\n            ? pagu + realbulananakumulatif + blokir\r\n            : pagu + realbulanan + blokir;\r\n        break;\r\n      case \"4\":\r\n        console.log(\"📊 Using jenlap 4 (Pagu Bulanan)\");\r\n        dynamicFrom = fromBulanan;\r\n        dynamicSelect = realBulanan;\r\n        break;\r\n      case \"5\":\r\n        console.log(\"📊 Using jenlap 5 (Blokir Bulanan)\");\r\n        dynamicFrom = fromBulanan;\r\n        dynamicSelect = blokirBulanan;\r\n        break;\r\n      case \"6\":\r\n        console.log(\"📊 Using jenlap 6 (Volume Output Kegiatan - Data Caput)\");\r\n        dynamicFrom = fromcaput;\r\n        dynamicSelect = jenlap6Select;\r\n        break;\r\n      case \"7\":\r\n        console.log(\"📊 Using jenlap 7 (Pergerakan Blokir Bulanan per Jenis)\");\r\n        dynamicFrom = fromJnsblokir;\r\n        dynamicSelect = blokirBulananSelect;\r\n        break;\r\n      default:\r\n        console.log(\"📊 Using default jenlap\");\r\n        dynamicFrom = currentTable;\r\n        dynamicSelect = pagu + blokir;\r\n        break;\r\n    }\r\n\r\n    return { dynamicFrom, dynamicSelect };\r\n  }\r\n\r\n  /**\r\n   * Build complete SQL query from inquiry state\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {string} - Complete SQL query\r\n   */\r\n  buildQuery(inquiryState) {\r\n    // Build dynamic FROM and SELECT\r\n    const { dynamicFrom, dynamicSelect } =\r\n      this.buildDynamicFromAndSelect(inquiryState);\r\n\r\n    // Build all filters\r\n    const filterResult = this.filterBuilder.buildAllFilters(inquiryState);\r\n\r\n    // Build WHERE clause with access control\r\n    const whereClause = this.filterBuilder.buildWhereClause(inquiryState);\r\n\r\n    // Build final SELECT clause\r\n    let finalSelectClause = \"\";\r\n    if (filterResult.columns.length > 0) {\r\n      finalSelectClause = filterResult.columns.join(\", \") + dynamicSelect;\r\n    } else {\r\n      finalSelectClause = dynamicSelect.substring(1); // Remove leading comma\r\n    }\r\n\r\n    // Build GROUP BY clause\r\n    let groupByClause = \"\";\r\n    const groupByFields = [...filterResult.groupBy];\r\n\r\n    console.log(\"🔍 Initial groupByFields:\", groupByFields);\r\n\r\n    // Special handling for jenlap = 6: Add sat, os, ket to GROUP BY (Volume Output Kegiatan - Data Caput)\r\n    if (inquiryState.jenlap === \"6\") {\r\n      console.log(\"📊 Adding sat, os, ket to GROUP BY for jenlap 6\");\r\n      groupByFields.push(\"a.sat\", \"a.os\", \"a.ket\");\r\n    }\r\n\r\n    // Special handling for jenlap = 7: Add kdblokir to GROUP BY for blokir breakdown (Pergerakan Blokir Bulanan per Jenis)\r\n    if (inquiryState.jenlap === \"7\") {\r\n      console.log(\"📊 Adding kdblokir to GROUP BY for jenlap 7\");\r\n      groupByFields.push(\"a.kdblokir\");\r\n    }\r\n\r\n    console.log(\"🔍 Final groupByFields:\", groupByFields);\r\n\r\n    if (groupByFields.length > 0) {\r\n      groupByClause = `GROUP BY ${groupByFields.join(\", \")}`;\r\n    }\r\n\r\n    // Combine JOIN clauses\r\n    const joinClause = filterResult.joinClauses.join(\"\");\r\n\r\n    // Build final query\r\n    const finalQuery = `\r\n      SELECT ${finalSelectClause}\r\n      FROM ${dynamicFrom}${joinClause}\r\n      ${whereClause}\r\n      ${groupByClause}\r\n    `.trim();\r\n\r\n    return finalQuery;\r\n  }\r\n\r\n  /**\r\n   * Validate query before execution\r\n   * @param {string} query - SQL query to validate\r\n   * @returns {object} - Validation result\r\n   */\r\n  validateQuery(query) {\r\n    const errors = [];\r\n    const warnings = [];\r\n\r\n    if (!query || query.trim() === \"\") {\r\n      errors.push(\"Query is empty\");\r\n    }\r\n\r\n    if (!query.includes(\"FROM\")) {\r\n      errors.push(\"Query missing FROM clause\");\r\n    }\r\n\r\n    if (!query.includes(\"SELECT\")) {\r\n      errors.push(\"Query missing SELECT clause\");\r\n    }\r\n\r\n    // Check for potentially dangerous patterns\r\n    const dangerousPatterns = [\r\n      /;\\s*drop\\s+table/i,\r\n      /;\\s*delete\\s+from/i,\r\n      /;\\s*update\\s+.*\\s+set/i,\r\n      /union\\s+select/i,\r\n    ];\r\n\r\n    dangerousPatterns.forEach((pattern) => {\r\n      if (pattern.test(query)) {\r\n        errors.push(\"Potentially dangerous SQL pattern detected\");\r\n      }\r\n    });\r\n\r\n    // Performance warnings\r\n    const joinCount = (query.match(/LEFT JOIN/gi) || []).length;\r\n    if (joinCount > 10) {\r\n      warnings.push(`High number of JOINs (${joinCount}). Query may be slow.`);\r\n    }\r\n\r\n    const whereConditions = (query.match(/AND|OR/gi) || []).length;\r\n    if (whereConditions > 15) {\r\n      warnings.push(\r\n        `High number of WHERE conditions (${whereConditions}). Query may be slow.`\r\n      );\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors,\r\n      warnings,\r\n      stats: {\r\n        queryLength: query.length,\r\n        joinCount,\r\n        whereConditions,\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get query performance metrics\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {object} - Performance metrics\r\n   */\r\n  getQueryPerformanceMetrics(inquiryState) {\r\n    const startTime = performance.now();\r\n    const query = this.buildQuery(inquiryState);\r\n    const endTime = performance.now();\r\n\r\n    const validation = this.validateQuery(query);\r\n    const filterStats = this.filterBuilder.getFilterStats(inquiryState);\r\n\r\n    return {\r\n      query,\r\n      buildTime: endTime - startTime,\r\n      validation,\r\n      filterStats,\r\n      recommendations: this.generatePerformanceRecommendations(\r\n        validation,\r\n        filterStats\r\n      ),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Generate performance recommendations\r\n   * @param {object} validation - Query validation result\r\n   * @param {object} filterStats - Filter statistics\r\n   * @returns {string[]} - Array of recommendations\r\n   */\r\n  generatePerformanceRecommendations(validation, filterStats) {\r\n    const recommendations = [];\r\n\r\n    if (filterStats.enabledFilters > 8) {\r\n      recommendations.push(\r\n        \"Consider reducing the number of active filters for better performance\"\r\n      );\r\n    }\r\n\r\n    if (validation.stats.joinCount > 8) {\r\n      recommendations.push(\r\n        \"High number of table JOINs detected. Consider using indexed columns\"\r\n      );\r\n    }\r\n\r\n    if (validation.stats.queryLength > 5000) {\r\n      recommendations.push(\r\n        \"Query is very long. Consider breaking it into smaller queries\"\r\n      );\r\n    }\r\n\r\n    if (filterStats.whereConditionsCount > 12) {\r\n      recommendations.push(\r\n        \"Many WHERE conditions detected. Ensure proper indexing on filtered columns\"\r\n      );\r\n    }\r\n\r\n    return recommendations;\r\n  }\r\n\r\n  /**\r\n   * Generate SQL preview without building full query\r\n   * @param {object} inquiryState - Complete inquiry state\r\n   * @returns {object} - SQL preview components\r\n   */\r\n  generateSqlPreview(inquiryState) {\r\n    const { dynamicFrom, dynamicSelect } =\r\n      this.buildDynamicFromAndSelect(inquiryState);\r\n    const filterResult = this.filterBuilder.buildAllFilters(inquiryState);\r\n    const whereClause = this.filterBuilder.buildWhereClause(inquiryState);\r\n\r\n    return {\r\n      fromClause: dynamicFrom,\r\n      selectClause: dynamicSelect,\r\n      columns: filterResult.columns,\r\n      joinClauses: filterResult.joinClauses,\r\n      whereClause: whereClause,\r\n      groupBy: filterResult.groupBy,\r\n      filterStats: this.filterBuilder.getFilterStats(inquiryState),\r\n    };\r\n  }\r\n}\r\n\r\nexport default QueryBuilder;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAEA,MAAM;IAKJ;;;;GAIC,GACD,0BAA0B,MAAM,EAAE;QAChC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG;QAEnE,yCAAyC;QACzC,MAAM,WAAW,AAAC,QAAkD,OAA3C,OAAM,uCAA2C,OAAN,OAAM;QAC1E,MAAM,cAAc,AAAC,QAAuC,OAAhC,OAAM,4BAAgC,OAAN,OAAM;QAClE,MAAM,YACJ,SAAS,SACL,AAAC,QAA4B,OAArB,OAAM,iBAAqB,OAAN,OAAM,YACnC,AAAC,QAA4B,OAArB,OAAM,iBAAqB,OAAN,OAAM;QACzC,MAAM,gBAAgB,AAAC,QAAoC,OAA7B,OAAM,yBAA6B,OAAN,OAAM;QAEjE,qCAAqC;QACrC,MAAM,cACJ,SAAS,WAAW,KAAK,SAAS,WAAW,KAAK,SAAS,UAAU;QACvE,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,IAAK;YACrC,eAAe,AAAC,OAAQ,OAAF;YACtB,IAAI,MAAM,aAAa,eAAe;QACxC;QAEA,qEAAqE;QACrE,MAAM,YAAY,WAAW,MAAM,gBAAgB;QACnD,MAAM,OAAO,AAAC,eAA4B,OAAd,WAAU,MAAe,OAAX,YAAW;QACrD,MAAM,WAAW,AAAC,eAA4B,OAAd,WAAU,MAAe,OAAX,YAAW;QACzD,MAAM,WAAW,AAAC,4BAAsC,OAAX,YAAW;QACxD,MAAM,SAAS,AAAC,yBAAmC,OAAX,YAAW;QACnD,MAAM,eAAe,AAAC,eAA8B,OAAhB,aAAY,MAAe,OAAX,YAAW;QAC/D,MAAM,WAAW,AAAC,2FAAqG,OAAX,YAAW;QAEvH,+BAA+B;QAC/B,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,IAAI,cAAc;QAClB,IAAI,wBAAwB;QAC5B,IAAI,cAAc;QAClB,IAAI,gBAAgB;QAEpB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,MAAM,YAAY,WAAW,CAAC,IAAI,EAAE;YACpC,IAAI,KAAK,aAAa;gBACpB,sBAAsB;gBACtB,eAAe,AAAC,mBAAwB,OAAN,GAAE,MAAwB,OAApB,YAAW,WAAmB,OAAV;gBAE5D,sBAAsB;gBACtB,IAAI,sBAAsB;gBAC1B,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBAC3B,uBAAuB,AAAC,OAAQ,OAAF;oBAC9B,IAAI,IAAI,GAAG,uBAAuB;gBACpC;gBACA,yBAAyB,AAAC,eAAsC,OAAxB,qBAAoB,MAAwB,OAApB,YAAW,WAAmB,OAAV;gBAEpF,eAAe;gBACf,eAAe,AAAC,mBAAwB,OAAN,GAAE,MAAyB,OAArB,YAAW,YAAoB,OAAV;gBAE7D,iBAAiB;gBACjB,iBAAiB,AAAC,qBAA0B,OAAN,GAAE,MAAyB,OAArB,YAAW,YAAoB,OAAV;YACnE,OAAO;gBACL,eAAe,AAAC,UAAmB,OAAV;gBACzB,yBAAyB,AAAC,UAAmB,OAAV;gBACnC,eAAe,AAAC,UAAmB,OAAV;gBACzB,iBAAiB,AAAC,UAAmB,OAAV;YAC7B;QACF;QAEA,gBAAgB;QAChB,MAAM,cAAc,AAAC,eAA4B,OAAd,WAAU,MAAwC,OAApC,YAAW,2BAAyC,OAAhB,aAAY,MAAe,OAAX,YAAW;QAChH,MAAM,cAAc,AAAC,yBAAmC,OAAX,YAAW;QACxD,MAAM,mBAAmB,AAAC,qCAA+C,OAAX,YAAW;QAEzE,gFAAgF;QAChF,MAAM,gBAAgB,AAAC,2CAAoD,OAAV,WAAU;QAE3E,kGAAkG;QAClG,MAAM,sBAAsB,AAAC,2BAAwC,OAAd;QAEvD,oBAAoB;QACpB,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,kBAAkB,AAAC,qCAErB,OADF,UAAU,CAAC,SAAS,QAAQ,EAC7B,KAAS,OAAN,OAAM;QACV,MAAM,eAAe,AAAC,QAAwC,OAAjC,OAAM,6BAAiC,OAAN,OAAM;QAEpE,oBAAoB;QACpB,IAAI,cAAc;QAClB,IAAI,gBAAgB;QAEpB,sCAAsC;QACtC,QAAQ,GAAG,CAAC,0BAA0B;YACpC;YACA,YAAY,OAAO;QACrB;QAEA,OAAQ;YACN,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc;gBACd,gBAAgB,WAAW,WAAW,WAAW;gBACjD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc,UAAU,kBAAkB;gBAC1C,gBAAgB,OAAO,eAAe;gBACtC;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc,UAAU,kBAAkB;gBAC1C,gBACE,WAAW,OAAO,eAAe,MAC7B,OAAO,wBAAwB,SAC/B,OAAO,cAAc;gBAC3B;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc;gBACd,gBAAgB;gBAChB;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc;gBACd,gBAAgB;gBAChB;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc;gBACd,gBAAgB;gBAChB;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC;gBACZ,cAAc;gBACd,gBAAgB;gBAChB;YACF;gBACE,QAAQ,GAAG,CAAC;gBACZ,cAAc;gBACd,gBAAgB,OAAO;gBACvB;QACJ;QAEA,OAAO;YAAE;YAAa;QAAc;IACtC;IAEA;;;;GAIC,GACD,WAAW,YAAY,EAAE;QACvB,gCAAgC;QAChC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAClC,IAAI,CAAC,yBAAyB,CAAC;QAEjC,oBAAoB;QACpB,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;QAExD,yCAAyC;QACzC,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;QAExD,4BAA4B;QAC5B,IAAI,oBAAoB;QACxB,IAAI,aAAa,OAAO,CAAC,MAAM,GAAG,GAAG;YACnC,oBAAoB,aAAa,OAAO,CAAC,IAAI,CAAC,QAAQ;QACxD,OAAO;YACL,oBAAoB,cAAc,SAAS,CAAC,IAAI,uBAAuB;QACzE;QAEA,wBAAwB;QACxB,IAAI,gBAAgB;QACpB,MAAM,gBAAgB;eAAI,aAAa,OAAO;SAAC;QAE/C,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,sGAAsG;QACtG,IAAI,aAAa,MAAM,KAAK,KAAK;YAC/B,QAAQ,GAAG,CAAC;YACZ,cAAc,IAAI,CAAC,SAAS,QAAQ;QACtC;QAEA,uHAAuH;QACvH,IAAI,aAAa,MAAM,KAAK,KAAK;YAC/B,QAAQ,GAAG,CAAC;YACZ,cAAc,IAAI,CAAC;QACrB;QAEA,QAAQ,GAAG,CAAC,2BAA2B;QAEvC,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,gBAAgB,AAAC,YAAoC,OAAzB,cAAc,IAAI,CAAC;QACjD;QAEA,uBAAuB;QACvB,MAAM,aAAa,aAAa,WAAW,CAAC,IAAI,CAAC;QAEjD,oBAAoB;QACpB,MAAM,aAAa,AAAC,kBAEX,OADE,mBAAkB,iBACN,OAAd,aACL,OADmB,YAAW,YAE9B,OADA,aAAY,YACE,OAAd,eAAc,UAChB,IAAI;QAEN,OAAO;IACT;IAEA;;;;GAIC,GACD,cAAc,KAAK,EAAE;QACnB,MAAM,SAAS,EAAE;QACjB,MAAM,WAAW,EAAE;QAEnB,IAAI,CAAC,SAAS,MAAM,IAAI,OAAO,IAAI;YACjC,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,MAAM,QAAQ,CAAC,SAAS;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,MAAM,QAAQ,CAAC,WAAW;YAC7B,OAAO,IAAI,CAAC;QACd;QAEA,2CAA2C;QAC3C,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,kBAAkB,OAAO,CAAC,CAAC;YACzB,IAAI,QAAQ,IAAI,CAAC,QAAQ;gBACvB,OAAO,IAAI,CAAC;YACd;QACF;QAEA,uBAAuB;QACvB,MAAM,YAAY,CAAC,MAAM,KAAK,CAAC,kBAAkB,EAAE,EAAE,MAAM;QAC3D,IAAI,YAAY,IAAI;YAClB,SAAS,IAAI,CAAC,AAAC,yBAAkC,OAAV,WAAU;QACnD;QAEA,MAAM,kBAAkB,CAAC,MAAM,KAAK,CAAC,eAAe,EAAE,EAAE,MAAM;QAC9D,IAAI,kBAAkB,IAAI;YACxB,SAAS,IAAI,CACX,AAAC,oCAAmD,OAAhB,iBAAgB;QAExD;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;YACA,OAAO;gBACL,aAAa,MAAM,MAAM;gBACzB;gBACA;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,2BAA2B,YAAY,EAAE;QACvC,MAAM,YAAY,YAAY,GAAG;QACjC,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC;QAC9B,MAAM,UAAU,YAAY,GAAG;QAE/B,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC;QACtC,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QAEtD,OAAO;YACL;YACA,WAAW,UAAU;YACrB;YACA;YACA,iBAAiB,IAAI,CAAC,kCAAkC,CACtD,YACA;QAEJ;IACF;IAEA;;;;;GAKC,GACD,mCAAmC,UAAU,EAAE,WAAW,EAAE;QAC1D,MAAM,kBAAkB,EAAE;QAE1B,IAAI,YAAY,cAAc,GAAG,GAAG;YAClC,gBAAgB,IAAI,CAClB;QAEJ;QAEA,IAAI,WAAW,KAAK,CAAC,SAAS,GAAG,GAAG;YAClC,gBAAgB,IAAI,CAClB;QAEJ;QAEA,IAAI,WAAW,KAAK,CAAC,WAAW,GAAG,MAAM;YACvC,gBAAgB,IAAI,CAClB;QAEJ;QAEA,IAAI,YAAY,oBAAoB,GAAG,IAAI;YACzC,gBAAgB,IAAI,CAClB;QAEJ;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,mBAAmB,YAAY,EAAE;QAC/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAClC,IAAI,CAAC,yBAAyB,CAAC;QACjC,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;QACxD,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;QAExD,OAAO;YACL,YAAY;YACZ,cAAc;YACd,SAAS,aAAa,OAAO;YAC7B,aAAa,aAAa,WAAW;YACrC,aAAa;YACb,SAAS,aAAa,OAAO;YAC7B,aAAa,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QACjD;IACF;IA5XA,aAAc;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,iLAAA,CAAA,UAAa;IACxC;AA2XF;uCAEe", "debugId": null}}, {"offset": {"line": 2941, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/filters/index.js"], "sourcesContent": ["// Filter modules index\r\nexport { default as BaseFilter } from \"./BaseFilter\";\r\nexport { default as DepartmentFilter } from \"./DepartmentFilter\";\r\nexport { default as LocationFilter } from \"./LocationFilter\";\r\nexport { default as ProgramFilter } from \"./ProgramFilter\";\r\nexport { default as AccountFilter } from \"./AccountFilter\";\r\nexport { default as PriorityFilter } from \"./PriorityFilter\";\r\nexport { default as SpecialFilter } from \"./SpecialFilter\";\r\nexport { default as FilterBuilder } from \"./FilterBuilder\";\r\nexport { default as QueryBuilder } from \"./QueryBuilder\";\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2993, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/formInquiryMod.jsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/feedback/Omspan\";\r\nimport React, { useContext } from \"react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport SqlPreviewModal from \"./components/Modals/SqlPreviewModal\";\r\nimport SaveQueryModal from \"./components/Modals/SaveQueryModal\";\r\nimport ExportModal from \"./components/Modals/ExportModal\";\r\nimport InquiryModal from \"./components/Modals/InquiryModal\";\r\nimport FilterSection from \"./components/FilterSelector\";\r\nimport QueryButtons from \"./components/QueryButtons\";\r\nimport ReportTypeSelector from \"./components/LaporanSelector\";\r\nimport useInquiryState from \"./hooks/useInquiryState\";\r\nimport useQueryBuilder from \"./hooks/useQueryBuilderModular\";\r\nimport {\r\n  exportToCSV,\r\n  exportToExcel,\r\n  exportToJSON,\r\n  exportToText,\r\n} from \"./utils/exportUtils\";\r\n\r\nconst InquiryMod = () => {\r\n  // Use modular inquiry state hook\r\n  const inquiry = useInquiryState();\r\n\r\n  const context = useContext(MyContext);\r\n  const { statusLogin, token, axiosJWT } = context;\r\n  // Use modular query builder hook\r\n  const { buildQuery } = useQueryBuilder(inquiry);\r\n  // Destructure all needed state and setters from inquiry\r\n  const {\r\n    role,\r\n    telp,\r\n    verified,\r\n    loadingExcell,\r\n    setloadingExcell,\r\n    kodekppn,\r\n    kodekanwil,\r\n    settampilAI,\r\n    showModal,\r\n    setShowModal,\r\n    showModalKedua,\r\n    setShowModalKedua,\r\n    showModalsql,\r\n    setShowModalsql,\r\n    showModalPDF,\r\n    setShowModalPDF,\r\n    showModalsimpan,\r\n    setShowModalsimpan,\r\n    jenlap,\r\n    setJenlap,\r\n    thang,\r\n    setThang,\r\n    tanggal,\r\n    setTanggal,\r\n    cutoff,\r\n    setCutoff,\r\n    pembulatan,\r\n    setPembulatan,\r\n    akumulatif,\r\n    setAkumulatif,\r\n    selectedFormat,\r\n    setSelectedFormat,\r\n    export2,\r\n    setExport2,\r\n    loadingStatus,\r\n    setLoadingStatus,\r\n    showFormatDropdown,\r\n    setShowFormatDropdown,\r\n    kddept,\r\n    setKddept,\r\n    unit,\r\n    setUnit,\r\n    kddekon,\r\n    setKddekon,\r\n    kdlokasi,\r\n    setKdlokasi,\r\n    kdkabkota,\r\n    setKdkabkota,\r\n    kdkanwil,\r\n    setKdkanwil,\r\n    kdkppn,\r\n    setKdkppn,\r\n    kdsatker,\r\n    setKdsatker,\r\n    kdfungsi,\r\n    setKdfungsi,\r\n    kdsfungsi,\r\n    setKdsfungsi,\r\n    kdprogram,\r\n    setKdprogram,\r\n    kdgiat,\r\n    setKdgiat,\r\n    kdoutput,\r\n    setKdoutput,\r\n    kdsoutput,\r\n    setKdsoutput,\r\n    kdkomponen,\r\n    setKdkomponen,\r\n    kdskomponen,\r\n    setKdskomponen,\r\n    kdakun,\r\n    setKdakun,\r\n    kdsdana,\r\n    setKdsdana,\r\n    kdregister,\r\n    setKdregister,\r\n    kdInflasi,\r\n    setKdInflasi,\r\n    kdIkn,\r\n    setKdIkn,\r\n    kdKemiskinan,\r\n    setKdKemiskinan,\r\n    KdPRI,\r\n    setKdPRI,\r\n    KdPangan,\r\n    setKdPangan,\r\n    KdPemilu,\r\n    setKdPemilu,\r\n    KdStunting,\r\n    setKdStunting,\r\n    KdTema,\r\n    setKdTema,\r\n    KdPN,\r\n    setKdPN,\r\n    KdPP,\r\n    setKdPP,\r\n    KdMP,\r\n    setKdMP,\r\n    KdKegPP,\r\n    setKdKegPP,\r\n    Pangan,\r\n    setPangan,\r\n    Pemilu,\r\n    setPemilu,\r\n    dept,\r\n    setDept,\r\n    deptkondisi,\r\n    setDeptkondisi,\r\n    katadept,\r\n    setKatadept,\r\n    kdunit,\r\n    setKdunit,\r\n    unitkondisi,\r\n    setUnitkondisi,\r\n    kataunit,\r\n    setKataunit,\r\n    dekon,\r\n    setDekon,\r\n    dekonkondisi,\r\n    setDekonkondisi,\r\n    katadekon,\r\n    setKatadekon,\r\n    prov,\r\n    setProv,\r\n    lokasikondisi,\r\n    setLokasikondisi,\r\n    katalokasi,\r\n    setKatalokasi,\r\n    kabkota,\r\n    setKabkota,\r\n    kabkotakondisi,\r\n    setKabkotakondisi,\r\n    katakabkota,\r\n    setKatakabkota,\r\n    kanwil,\r\n    setKanwil,\r\n    kanwilkondisi,\r\n    setKanwilkondisi,\r\n    katakanwil,\r\n    setKatakanwil,\r\n    kppn,\r\n    setKppn,\r\n    kppnkondisi,\r\n    setKppnkondisi,\r\n    katakppn,\r\n    setKatakppn,\r\n    satker,\r\n    setSatker,\r\n    satkerkondisi,\r\n    setSatkerkondisi,\r\n    katasatker,\r\n    setKatasatker,\r\n    fungsi,\r\n    setFungsi,\r\n    fungsikondisi,\r\n    setFungsikondisi,\r\n    katafungsi,\r\n    setKatafungsi,\r\n    sfungsi,\r\n    setSfungsi,\r\n    subfungsikondisi,\r\n    setSubfungsikondisi,\r\n    katasubfungsi,\r\n    setKatasubfungsi,\r\n    program,\r\n    setProgram,\r\n    programkondisi,\r\n    setProgramkondisi,\r\n    kataprogram,\r\n    setKataprogram,\r\n    giat,\r\n    setGiat,\r\n    giatkondisi,\r\n    setGiatkondisi,\r\n    katagiat,\r\n    setKatagiat,\r\n    output,\r\n    setOutput,\r\n    outputkondisi,\r\n    setOutputkondisi,\r\n    kataoutput,\r\n    setKataoutput,\r\n    soutput,\r\n    setsOutput,\r\n    soutputkondisi,\r\n    setSoutputkondisi,\r\n    katasoutput,\r\n    setKatasoutput,\r\n    komponen,\r\n    setKomponen,\r\n    komponenkondisi,\r\n    setKomponenkondisi,\r\n    katakomponen,\r\n    setKatakomponen,\r\n    skomponen,\r\n    setSkomponen,\r\n    skomponenkondisi,\r\n    setSkomponenkondisi,\r\n    kataskomponen,\r\n    setKataskomponen,\r\n    akun,\r\n    setAkun,\r\n    akunkondisi,\r\n    setAkunkondisi,\r\n    kataakun,\r\n    setKataakun,\r\n    sdana,\r\n    setSdana,\r\n    sdanakondisi,\r\n    setSdanakondisi,\r\n    katasdana,\r\n    setKatasdana,\r\n    register,\r\n    setRegister,\r\n    registerkondisi,\r\n    setRegisterkondisi,\r\n    kataregister,\r\n    setKataregister,\r\n    PN,\r\n    setPN,\r\n    PP,\r\n    setPP,\r\n    PRI,\r\n    setPRI,\r\n    MP,\r\n    setMP,\r\n    Tema,\r\n    setTema,\r\n    Inflasi,\r\n    setInflasi,\r\n    Stunting,\r\n    setStunting,\r\n    Miskin,\r\n    setMiskin,\r\n    Ikn,\r\n    setIkn,\r\n    deptradio,\r\n    setDeptradio,\r\n    unitradio,\r\n    setUnitradio,\r\n    dekonradio,\r\n    setDekonradio,\r\n    locradio,\r\n    setLocradio,\r\n    kabkotaradio,\r\n    setKabkotaradio,\r\n    kanwilradio,\r\n    setKanwilradio,\r\n    kppnradio,\r\n    setKppnradio,\r\n    satkerradio,\r\n    setSatkerradio,\r\n    fungsiradio,\r\n    setFungsiradio,\r\n    subfungsiradio,\r\n    setSubfungsiradio,\r\n    programradio,\r\n    setProgramradio,\r\n    kegiatanradio,\r\n    setKegiatanradio,\r\n    outputradio,\r\n    setOutputradio,\r\n    soutputradio,\r\n    setsOutputradio,\r\n    komponenradio,\r\n    setKomponenradio,\r\n    skomponenradio,\r\n    setSkomponenradio,\r\n    akunradio,\r\n    setAkunradio,\r\n    sdanaradio,\r\n    setSdanaradio,\r\n    registerradio,\r\n    setRegisterradio,\r\n    inflasiradio,\r\n    setInflasiradio,\r\n    iknradio,\r\n    setIknradio,\r\n    kemiskinanradio,\r\n    setKemiskinanradio,\r\n    pnradio,\r\n    setPnradio,\r\n    ppradio,\r\n    setPpradio,\r\n    mpradio,\r\n    setMpradio,\r\n    temaradio,\r\n    setTemaradio,\r\n    panganradio,\r\n    setPanganradio,\r\n    stuntingradio,\r\n    setStuntingradio,\r\n    pemiluradio,\r\n    setPemiluradio,\r\n    priradio,\r\n    setPriradio,\r\n    opsiInflasi,\r\n    setOpsiInflasi,\r\n    opsiIkn,\r\n    setOpsiIkn,\r\n    opsiKemiskinan,\r\n    setOpsiKemiskinan,\r\n    kegiatanprioritas,\r\n    setKegiatanPrioritas,\r\n    kegiatanprioritasradio,\r\n    setKegiatanPrioritasRadio,\r\n    sql,\r\n    setSql,\r\n    from,\r\n    setFrom,\r\n    select,\r\n    setSelect,\r\n    akunType,\r\n    akunValue,\r\n    akunSql,\r\n  } = inquiry;\r\n\r\n  // Modal handlers\r\n  const openModalKedua = () => {\r\n    setShowModalKedua(true);\r\n    settampilAI(true);\r\n  };\r\n\r\n  const closeModalKedua = () => {\r\n    setShowModalKedua(false);\r\n    settampilAI(false);\r\n  };\r\n\r\n  // Replace generateSql with buildQuery in all handlers\r\n  const handleGenerateExcel = () => {\r\n    buildQuery(); // Call the function to build the query\r\n    setloadingExcell(true);\r\n  };\r\n\r\n  const handleDataFetchComplete = (total) => {\r\n    if (total > 0) {\r\n      Pesan(`${total} data berhasil diexport`);\r\n    } else {\r\n      Pesan(\"Tidak Ada Data\");\r\n    }\r\n    setloadingExcell(false);\r\n  };\r\n\r\n  // **UNIFIED QUERY GENERATION** - All functions now use the same query builder\r\n  const generateUnifiedQuery = () => {\r\n    const sql = buildQuery(); // Use buildQuery() to get the complete SQL string\r\n    return sql;\r\n  };\r\n\r\n  // **DEBUGGING HELPER** - Get current query for monitoring\r\n  const getCurrentQuery = () => {\r\n    return generateUnifiedQuery();\r\n  };\r\n\r\n  const handlegetQuery = async () => {\r\n    const sql = generateUnifiedQuery();\r\n    inquiry.setSql(sql);\r\n    setShowModal(true); // Always open InquiryModal\r\n  };\r\n  // **UPDATED** - SQL preview handler now uses unified query generation\r\n  const handlegetQuerySQL = () => {\r\n    const latestSql = generateUnifiedQuery(); // Same query as execute\r\n    inquiry.setSql(latestSql); // update global state\r\n    setShowModalsql(true); // open modal\r\n  };\r\n\r\n  // Modal close handlers\r\n  const closeModal = () => {\r\n    setShowModal(false);\r\n    setShowModalsimpan(false);\r\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n  };\r\n\r\n  const closeModalsql = () => {\r\n    setShowModalsql(false);\r\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n  };\r\n\r\n  const closeModalsimpan = () => {\r\n    setShowModalsimpan(false);\r\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\r\n  };\r\n\r\n  // Add useEffect for handling cutoff changes\r\n  React.useEffect(() => {\r\n    // Update SQL or other dependent values when these parameters change\r\n    const updateDependentValues = () => {\r\n      // Call buildQuery to rebuild when parameters change\r\n      buildQuery();\r\n    };\r\n\r\n    updateDependentValues();\r\n  }, [thang, cutoff, pembulatan, akumulatif]); // Remove buildQuery from dependencies to prevent infinite loops\r\n\r\n  // Add ref to track if component has mounted (to avoid resetting on initial load)\r\n  const hasMountedRef = React.useRef(false);\r\n\r\n  // Add useEffect for handling fungsi-subfungsi parent-child relationship\r\n  React.useEffect(() => {\r\n    // Skip the reset on initial mount\r\n    if (!hasMountedRef.current) {\r\n      hasMountedRef.current = true;\r\n      return;\r\n    }\r\n\r\n    // Reset subfungsi to default when fungsi changes (after initial mount)\r\n    // This ensures when user selects a different fungsi, subfungsi goes back to \"Semua Sub Fungsi\"\r\n    // Also clear related advanced filtering states\r\n    setSfungsi(\"XX\");\r\n    setSubfungsikondisi(\"\");\r\n    setKatasubfungsi(\"\");\r\n  }, [fungsi]);\r\n\r\n  // Handler to turn all switches on/off\r\n  const handlePilihSemua = (isOn) => {\r\n    setTanggal(isOn);\r\n    setKddept(isOn);\r\n    setKddekon(isOn);\r\n    setKdlokasi(isOn);\r\n    setKdkabkota(isOn);\r\n    setKdkanwil(isOn);\r\n    setKdkppn(isOn);\r\n    setKdsatker(isOn);\r\n    setKdfungsi(isOn);\r\n    setKdsfungsi(isOn);\r\n    setKdprogram(isOn);\r\n    setKdgiat(isOn);\r\n    setKdoutput(isOn);\r\n    setKdsoutput(isOn);\r\n    setKdkomponen(isOn);\r\n    setKdskomponen(isOn);\r\n    setKdakun(isOn);\r\n    setKdsdana(isOn);\r\n    setKdregister(isOn);\r\n    // Only set boolean switches, do not set 'unit' or other radio/select values\r\n    setCutoff(isOn ? \"12\" : \"0\");\r\n    setShowCutoffSelector(isOn);\r\n  };\r\n\r\n  // Handler to reset all filters and parameters to their initial state\r\n  const handleReset = () => {\r\n    setJenlap(\"2\");\r\n    setThang(new Date().getFullYear().toString());\r\n    setTanggal(false);\r\n    setKddept(true);\r\n    setUnit(false);\r\n    setKddekon(false);\r\n    setKdlokasi(false);\r\n    setKdkabkota(false);\r\n    setKdkanwil(false);\r\n    setKdkppn(false);\r\n    setKdsatker(false);\r\n    setKdfungsi(false);\r\n    setKdsfungsi(false);\r\n    setKdprogram(false);\r\n    setKdgiat(false);\r\n    setKdoutput(false);\r\n    setKdsoutput(false);\r\n    setKdkomponen(false);\r\n    setKdskomponen(false);\r\n    setKdakun(false);\r\n    setKdsdana(false);\r\n    setKdregister(false);\r\n    setKdInflasi(false);\r\n    setKdIkn(false);\r\n    setKdKemiskinan(false);\r\n    setKdPRI(false);\r\n    setKdPangan(false);\r\n    setKdPemilu(false);\r\n    setKdStunting(false);\r\n    setKdTema(false);\r\n    setKdPN(false);\r\n    setKdPP(false);\r\n    setKdMP(false);\r\n    setKdKegPP(false);\r\n    setAkumulatif(\"0\");\r\n    setCutoff(\"0\");\r\n    setShowCutoffSelector(false);\r\n    setPN(\"XX\");\r\n    setPP(\"XX\");\r\n    setPRI(\"XX\");\r\n    setMP(\"XX\");\r\n    setTema(\"XX\");\r\n    setInflasi(\"XX\");\r\n    setStunting(\"XX\");\r\n    setMiskin(\"XX\");\r\n    setPemilu(\"XX\");\r\n    setIkn(\"XX\");\r\n    setPangan(\"XX\");\r\n    setKegiatanPrioritas(\"XX\");\r\n    setDept(\"000\");\r\n    setKdunit(\"XX\");\r\n    setDekon(\"XX\");\r\n    setProv(\"XX\");\r\n    setKabkota(\"XX\");\r\n    setKabkotakondisi(\"\");\r\n    setKatakabkota(\"\");\r\n    setKanwil(\"XX\");\r\n    setKppn(\"XX\");\r\n    setKppnkondisi(\"\");\r\n    setKatakppn(\"\");\r\n    setSatker(\"XX\");\r\n    setSatkerkondisi(\"\");\r\n    setKatasatker(\"\");\r\n    setFungsi(\"XX\");\r\n    setFungsikondisi(\"\");\r\n    setKatafungsi(\"\");\r\n    setSfungsi(\"XX\");\r\n    setSubfungsikondisi(\"\");\r\n    setKatasubfungsi(\"\");\r\n    setProgram(\"XX\");\r\n    setGiat(\"XX\");\r\n    setOutput(\"XX\");\r\n    setsOutput(\"XX\");\r\n    setKomponen(\"XX\");\r\n    setSkomponen(\"XX\");\r\n    setAkun(\"XX\");\r\n    setSdana(\"XX\");\r\n    setRegister(\"XX\");\r\n    setPembulatan(\"1\");\r\n    setDeptradio(\"1\");\r\n    setUnitradio(\"1\");\r\n    setDekonradio(\"1\");\r\n    setLocradio(\"1\");\r\n    setKabkotaradio(\"1\");\r\n    setKanwilradio(\"1\");\r\n    setKppnradio(\"1\");\r\n    setSatkerradio(\"1\");\r\n    setFungsiradio(\"1\");\r\n    setSubfungsiradio(\"1\");\r\n    setProgramradio(\"1\");\r\n    setKegiatanradio(\"1\");\r\n    setOutputradio(\"1\");\r\n    setsOutputradio(\"1\");\r\n    setKomponenradio(\"1\");\r\n    setSkomponenradio(\"1\");\r\n    setAkunradio(\"1\");\r\n    setSdanaradio(\"1\");\r\n    setRegisterradio(\"1\");\r\n    setInflasiradio(\"1\");\r\n    setIknradio(\"1\");\r\n    setKemiskinanradio(\"1\");\r\n    setPnradio(\"1\");\r\n    setPpradio(\"1\");\r\n    setMpradio(\"1\");\r\n    setTemaradio(\"1\");\r\n    setPanganradio(\"1\");\r\n    setStuntingradio(\"1\");\r\n    setPemiluradio(\"1\");\r\n    setPriradio(\"1\");\r\n    setKegiatanPrioritasRadio(\"1\");\r\n    setOpsiInflasi(\"pilihInflasi\");\r\n    setOpsiIkn(\"pilihikn\");\r\n    setOpsiKemiskinan(\"pilihKemiskinan\");\r\n    setSql(\"\");\r\n    setFrom(\"\");\r\n    setSelect(\r\n      \", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR\"\r\n    );\r\n  };\r\n\r\n  // Add MP state if not present\r\n  const [opsiMP, setOpsiMP] = React.useState(\"pilihmp\");\r\n\r\n  // Handler for MpRadio\r\n  const handleRadioMP = (val) => setMPradio(val);\r\n\r\n  // Add handlePDF for Export PDF modal (matches old form)\r\n  const handlePDF = () => {\r\n    setShowModalPDF(true);\r\n  };\r\n\r\n  // Add state to control visibility of CutoffMonthSelector\r\n  const [showCutoffSelector, setShowCutoffSelector] = React.useState(\r\n    cutoff !== \"0\"\r\n  );\r\n\r\n  // Add state for SaveQueryModal\r\n  const [showSaveQueryModal, setShowSaveQueryModal] = React.useState(false);\r\n\r\n  // Helper to fetch data from backend using current filters/query\r\n  // **UPDATED** - Export data fetcher now uses unified query generation\r\n  async function fetchExportData() {\r\n    // Use the same query builder as execute and show SQL\r\n    const sql = generateUnifiedQuery(); // Consistent with all other operations\r\n    if (!sql || typeof sql !== \"string\" || sql.trim() === \"\") {\r\n      Pesan(\"Query tidak valid, silakan cek filter dan parameter.\");\r\n      return [];\r\n    }\r\n    // If not logged in, return empty array\r\n    if (!statusLogin) {\r\n      return [];\r\n    }\r\n\r\n    try {\r\n      // Use the same backend URL as in InquiryModal\r\n      const backendUrl = \"http://localhost:88\";\r\n      const response = await axiosJWT.post(\r\n        `${backendUrl}/next/inquiry`,\r\n        {\r\n          sql,\r\n          page: 1, // Export all data from first page (adjust if backend supports full export)\r\n        },\r\n        {\r\n          headers: {\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n      // If backend supports returning all data for export, use that.\r\n      // Otherwise, you may need to adjust API/backend to support full export.\r\n      if (response.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      }\r\n      return [];\r\n    } catch (e) {\r\n      console.error(\"Export API error:\", e);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // Robust Excel export handler (fetches fresh data)\r\n  const handleExportExcel = async () => {\r\n    setloadingExcell(true);\r\n    try {\r\n      const exportData = await fetchExportData();\r\n      if (!exportData || !exportData.length) {\r\n        Pesan(\"Tidak ada data untuk diexport\");\r\n        setloadingExcell(false);\r\n        return;\r\n      }\r\n      await exportToExcel(exportData, \"inquiry_data.xlsx\");\r\n      Pesan(\"Data berhasil diexport ke Excel\");\r\n    } catch (e) {\r\n      Pesan(\"Gagal export Excel\");\r\n    }\r\n    setloadingExcell(false);\r\n  };\r\n\r\n  // Robust CSV export handler (fetches fresh data)\r\n  const handleExportCSV = async () => {\r\n    setloadingExcell(true);\r\n    try {\r\n      const exportData = await fetchExportData();\r\n      if (!exportData || !exportData.length) {\r\n        Pesan(\"Tidak ada data untuk diexport\");\r\n        setloadingExcell(false);\r\n        return;\r\n      }\r\n      exportToCSV(exportData, \"inquiry_data.csv\");\r\n      Pesan(\"Data berhasil diexport ke CSV\");\r\n    } catch (e) {\r\n      Pesan(\"Gagal export CSV\");\r\n    }\r\n    setloadingExcell(false);\r\n  };\r\n\r\n  // Robust JSON export handler (fetches fresh data)\r\n  const handleExportJSON = async () => {\r\n    setloadingExcell(true);\r\n    try {\r\n      const exportData = await fetchExportData();\r\n      if (!exportData || !exportData.length) {\r\n        Pesan(\"Tidak ada data untuk diexport\");\r\n        setloadingExcell(false);\r\n        return;\r\n      }\r\n      exportToJSON(exportData, \"inquiry_data.json\");\r\n      Pesan(\"Data berhasil diexport ke JSON\");\r\n    } catch (e) {\r\n      Pesan(\"Gagal export JSON\");\r\n    }\r\n    setloadingExcell(false);\r\n  };\r\n\r\n  // Robust Text export handler (fetches fresh data)\r\n  const handleExportText = async () => {\r\n    setloadingExcell(true);\r\n    try {\r\n      const exportData = await fetchExportData();\r\n      if (!exportData || !exportData.length) {\r\n        Pesan(\"Tidak ada data untuk diexport\");\r\n        setloadingExcell(false);\r\n        return;\r\n      }\r\n      exportToText(exportData, \"inquiry_data.txt\");\r\n      Pesan(\"Data berhasil diexport ke Text\");\r\n    } catch (e) {\r\n      Pesan(\"Gagal export Text\");\r\n    }\r\n    setloadingExcell(false);\r\n  };\r\n\r\n  // Add useEffect for handling Akun filter changes\r\n  React.useEffect(() => {\r\n    // Update SQL or other dependent values when Akun filter changes\r\n    buildQuery();\r\n  }, [akunType, akunValue, akunSql]);\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className=\"xl:px-8 p-6\">\r\n        <h2 className=\"text-2xl font-bold mb-6\">Inquiry Data Belanja</h2>\r\n\r\n        {/* Report Settings Card */}\r\n        <ReportTypeSelector\r\n          inquiryState={{\r\n            jenlap,\r\n            setJenlap,\r\n            pembulatan,\r\n            setPembulatan,\r\n            akumulatif,\r\n            setAkumulatif,\r\n            thang,\r\n            setThang,\r\n          }}\r\n        />\r\n\r\n        {/* Filter Section Card (new row) */}\r\n\r\n        <FilterSection\r\n          inquiryState={{\r\n            // Report type for determining default switches\r\n            jenlap,\r\n            // Basic form states\r\n            tanggal,\r\n            setTanggal,\r\n            cutoff,\r\n            setCutoff,\r\n            showCutoffSelector,\r\n            setShowCutoffSelector,\r\n            akumulatif,\r\n            setAkumulatif,\r\n            // Filter visibility states\r\n            kddept,\r\n            setKddept,\r\n            unit,\r\n            setUnit,\r\n            kddekon,\r\n            setKddekon,\r\n            kdlokasi,\r\n            setKdlokasi,\r\n            kdkabkota,\r\n            setKdkabkota,\r\n            kdkanwil,\r\n            setKdkanwil,\r\n            kdkppn,\r\n            setKdkppn,\r\n            kdsatker,\r\n            setKdsatker,\r\n            kdfungsi,\r\n            setKdfungsi,\r\n            kdsfungsi,\r\n            setKdsfungsi,\r\n            kdprogram,\r\n            setKdprogram,\r\n            kdgiat,\r\n            setKdgiat,\r\n            kdoutput,\r\n            setKdoutput,\r\n            kdsoutput,\r\n            setKdsoutput,\r\n            kdkomponen,\r\n            setKdkomponen,\r\n            kdskomponen,\r\n            setKdskomponen,\r\n            kdakun,\r\n            setKdakun,\r\n            kdsdana,\r\n            setKdsdana,\r\n            kdregister,\r\n            setKdregister,\r\n            // Department filter values\r\n            dept,\r\n            setDept,\r\n            deptkondisi,\r\n            setDeptkondisi,\r\n            katadept,\r\n            setKatadept,\r\n            deptradio,\r\n            setDeptradio,\r\n            // Unit filter values\r\n            kdunit,\r\n            setKdunit,\r\n            unitkondisi,\r\n            setUnitkondisi,\r\n            kataunit,\r\n            setKataunit,\r\n            unitradio,\r\n            setUnitradio,\r\n            // Location filter values\r\n            prov,\r\n            setProv,\r\n            lokasikondisi,\r\n            setLokasikondisi,\r\n            katalokasi,\r\n            setKatalokasi,\r\n            locradio,\r\n            setLocradio,\r\n            // Dekon filter values\r\n            dekon,\r\n            setDekon,\r\n            dekonkondisi,\r\n            setDekonkondisi,\r\n            katadekon,\r\n            setKatadekon,\r\n            dekonradio,\r\n            setDekonradio,\r\n            // Kabkota filter values\r\n            kabkota,\r\n            setKabkota,\r\n            kabkotakondisi,\r\n            setKabkotakondisi,\r\n            katakabkota,\r\n            setKatakabkota,\r\n            kabkotaradio,\r\n            setKabkotaradio,\r\n            // Kanwil filter values\r\n            kanwil,\r\n            setKanwil,\r\n            kanwilkondisi,\r\n            setKanwilkondisi,\r\n            katakanwil,\r\n            setKatakanwil,\r\n            kanwilradio,\r\n            setKanwilradio,\r\n            // KPPN filter values\r\n            kppn,\r\n            setKppn,\r\n            kppnkondisi,\r\n            setKppnkondisi,\r\n            katakppn,\r\n            setKatakppn,\r\n            kppnradio,\r\n            setKppnradio,\r\n            // Satker filter values\r\n            satker,\r\n            setSatker,\r\n            satkerkondisi,\r\n            setSatkerkondisi,\r\n            katasatker,\r\n            setKatasatker,\r\n            satkerradio,\r\n            setSatkerradio,\r\n            // Fungsi filter values\r\n            fungsi,\r\n            setFungsi,\r\n            fungsikondisi,\r\n            setFungsikondisi,\r\n            katafungsi,\r\n            setKatafungsi,\r\n            fungsiradio,\r\n            setFungsiradio,\r\n            // Sub-fungsi filter values\r\n            sfungsi,\r\n            setSfungsi,\r\n            subfungsikondisi,\r\n            setSubfungsikondisi,\r\n            katasubfungsi,\r\n            setKatasubfungsi,\r\n            subfungsiradio,\r\n            setSubfungsiradio,\r\n            // Special states needed by FilterSelector\r\n            KdPRI,\r\n            setKdPRI,\r\n            KdPangan,\r\n            setKdPangan,\r\n            KdPemilu,\r\n            setKdPemilu,\r\n            KdStunting,\r\n            setKdStunting,\r\n            KdTema,\r\n            setKdTema,\r\n            KdPN,\r\n            setKdPN,\r\n            KdPP,\r\n            setKdPP,\r\n            KdMP,\r\n            setKdMP,\r\n            KdKegPP,\r\n            setKdKegPP,\r\n            // Kegiatan Prioritas filter values\r\n            kegiatanprioritas,\r\n            setKegiatanPrioritas,\r\n            kegiatanprioritasradio,\r\n            setKegiatanPrioritasRadio,\r\n            // Program filter values\r\n            program,\r\n            setProgram,\r\n            programkondisi,\r\n            setProgramkondisi,\r\n            kataprogram,\r\n            setKataprogram,\r\n            programradio,\r\n            setProgramradio,\r\n            // Kegiatan filter values\r\n            giat,\r\n            setGiat,\r\n            giatkondisi,\r\n            setGiatkondisi,\r\n            katagiat,\r\n            setKatagiat,\r\n            kegiatanradio,\r\n            setKegiatanradio,\r\n            // Output filter values\r\n            output,\r\n            setOutput,\r\n            outputkondisi,\r\n            setOutputkondisi,\r\n            kataoutput,\r\n            setKataoutput,\r\n            outputradio,\r\n            setOutputradio,\r\n            // Sub-output filter values\r\n            soutput,\r\n            setsOutput,\r\n            soutputkondisi,\r\n            setSoutputkondisi,\r\n            katasoutput,\r\n            setKatasoutput,\r\n            soutputradio,\r\n            setsOutputradio,\r\n            // Komponen filter values\r\n            komponen,\r\n            setKomponen,\r\n            komponenkondisi,\r\n            setKomponenkondisi,\r\n            katakomponen,\r\n            setKatakomponen,\r\n            komponenradio,\r\n            setKomponenradio,\r\n            // Subkomponen filter values\r\n            skomponen,\r\n            setSkomponen,\r\n            skomponenkondisi,\r\n            setSkomponenkondisi,\r\n            kataskomponen,\r\n            setKataskomponen,\r\n            skomponenradio,\r\n            setSkomponenradio,\r\n            // Akun filter values\r\n            akun,\r\n            setAkun,\r\n            akunkondisi,\r\n            setAkunkondisi,\r\n            kataakun,\r\n            setKataakun,\r\n            akunradio,\r\n            setAkunradio,\r\n            // Sumber Dana filter values\r\n            sdana,\r\n            setSdana,\r\n            sdanakondisi,\r\n            setSdanakondisi,\r\n            katasdana,\r\n            setKatasdana,\r\n            sdanaradio,\r\n            setSdanaradio,\r\n            // Register filter values\r\n            register,\r\n            setRegister,\r\n            registerkondisi,\r\n            setRegisterkondisi,\r\n            kataregister,\r\n            setKataregister,\r\n            registerradio,\r\n            setRegisterradio,\r\n            // New modularized filter states\r\n            kdInflasi,\r\n            setKdInflasi,\r\n            Inflasi,\r\n            setInflasi,\r\n            inflasiradio,\r\n            setInflasiradio,\r\n            opsiInflasi,\r\n            setOpsiInflasi,\r\n            kdIkn,\r\n            setKdIkn,\r\n            Ikn,\r\n            setIkn,\r\n            iknradio,\r\n            setIknradio,\r\n            opsiIkn,\r\n            setOpsiIkn,\r\n            kdKemiskinan,\r\n            setKdKemiskinan,\r\n            Miskin,\r\n            setMiskin,\r\n            kemiskinanradio,\r\n            setKemiskinanradio,\r\n            opsiKemiskinan,\r\n            setOpsiKemiskinan,\r\n            // Add new special filter states\r\n            Pangan,\r\n            setPangan,\r\n            panganradio,\r\n            setPanganradio,\r\n            Stunting,\r\n            setStunting,\r\n            stuntingradio,\r\n            setStuntingradio,\r\n            Pemilu,\r\n            setPemilu,\r\n            pemiluradio,\r\n            setPemiluradio,\r\n            PN,\r\n            setPN,\r\n            pnradio,\r\n            setPnradio,\r\n            PP,\r\n            setPP,\r\n            ppradio,\r\n            setPpradio,\r\n            MP,\r\n            setMP,\r\n            mpradio,\r\n            setMpradio,\r\n            Tema,\r\n            setTema,\r\n            temaradio,\r\n            setTemaradio,\r\n            PRI,\r\n            setPRI,\r\n            priradio,\r\n            setPriradio,\r\n            // Add other state/handlers as needed\r\n          }}\r\n        />\r\n\r\n        {/* Add SwitchesGrid for parameter toggles */}\r\n        <div className=\"my-3 sm:px-16\">\r\n          <div className=\"flex flex-col md:flex-row md:flex-wrap lg:flex-nowrap gap-2 border-2 dark:border-zinc-600 rounded-xl shadow-sm py-2 px-4 font-mono tracking-wide bg-zinc-100 dark:bg-black\">\r\n            <div className=\"text-xs\">\r\n              <span className=\"font-semibold text-blue-600 ml-4\">\r\n                Tahun Anggaran:\r\n              </span>\r\n              <span className=\"ml-2\">{thang}</span>\r\n            </div>\r\n            <div className=\"text-xs\">\r\n              <span className=\"font-semibold text-green-600 ml-4\">\r\n                Jenis Laporan:\r\n              </span>\r\n              <span className=\"ml-2\">\r\n                {jenlap === \"1\"\r\n                  ? \"Pagu APBN\"\r\n                  : jenlap === \"2\"\r\n                  ? \"Pagu Realisasi\"\r\n                  : jenlap === \"3\"\r\n                  ? \"Pagu Realisasi Bulanan\"\r\n                  : jenlap === \"4\"\r\n                  ? \"Pergerakan Pagu Bulanan\"\r\n                  : jenlap === \"5\"\r\n                  ? \"Pergerakan Blokir Bulanan\"\r\n                  : jenlap === \"7\"\r\n                  ? \"Pergerakan Blokir Bulanan per Jenis\"\r\n                  : \"Volume Output Kegiatan (PN) - Data Caput\"}\r\n              </span>\r\n            </div>\r\n            <div className=\"text-xs\">\r\n              <span className=\"font-semibold text-purple-600 ml-4\">\r\n                Pembulatan:\r\n              </span>\r\n              <span className=\"ml-2\">\r\n                {pembulatan === \"1\"\r\n                  ? \"Rupiah\"\r\n                  : pembulatan === \"1000\"\r\n                  ? \"Ribuan\"\r\n                  : pembulatan === \"1000000\"\r\n                  ? \"Jutaan\"\r\n                  : pembulatan === \"1000000000\"\r\n                  ? \"Miliaran\"\r\n                  : \"Triliunan\"}\r\n              </span>\r\n            </div>\r\n            <div className=\"text-xs\">\r\n              <span className=\"font-semibold text-orange-600 ml-4\">\r\n                Filter Aktif:\r\n              </span>\r\n              <span className=\"ml-2\">\r\n                {\r\n                  [\r\n                    tanggal,\r\n                    kddept,\r\n                    unit,\r\n                    kddekon,\r\n                    kdlokasi,\r\n                    kdkabkota,\r\n                    kdkanwil,\r\n                    kdkppn,\r\n                    kdsatker,\r\n                    kdfungsi,\r\n                    kdsfungsi,\r\n                    kdprogram,\r\n                    kdgiat,\r\n                    kdoutput,\r\n                    kdsoutput,\r\n                    kdkomponen,\r\n                    kdskomponen,\r\n                    kdakun,\r\n                    kdsdana,\r\n                    kdregister,\r\n                    kdInflasi,\r\n                    kdIkn,\r\n                    kdKemiskinan,\r\n                    KdPRI,\r\n                    KdPangan,\r\n                    KdPemilu,\r\n                    KdStunting,\r\n                    KdTema,\r\n                    KdPN,\r\n                    KdPP,\r\n                    KdMP,\r\n                    KdKegPP,\r\n                  ].filter(Boolean).length\r\n                }{\" \"}\r\n                dari{\" \"}\r\n                {\r\n                  [\r\n                    tanggal,\r\n                    kddept,\r\n                    unit,\r\n                    kddekon,\r\n                    kdlokasi,\r\n                    kdkabkota,\r\n                    kdkanwil,\r\n                    kdkppn,\r\n                    kdsatker,\r\n                    kdfungsi,\r\n                    kdsfungsi,\r\n                    kdprogram,\r\n                    kdgiat,\r\n                    kdoutput,\r\n                    kdsoutput,\r\n                    kdkomponen,\r\n                    kdskomponen,\r\n                    kdakun,\r\n                    kdsdana,\r\n                    kdregister,\r\n                    kdInflasi,\r\n                    kdIkn,\r\n                    kdKemiskinan,\r\n                    KdPRI,\r\n                    KdPangan,\r\n                    KdPemilu,\r\n                    KdStunting,\r\n                    KdTema,\r\n                    KdPN,\r\n                    KdPP,\r\n                    KdMP,\r\n                    KdKegPP,\r\n                  ].length\r\n                }\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Action Buttons */}\r\n        <QueryButtons\r\n          onExecuteQuery={handlegetQuery}\r\n          onExportExcel={handleExportExcel}\r\n          onExportCSV={handleExportCSV}\r\n          onExportPDF={handlePDF}\r\n          onReset={handleReset}\r\n          isLoading={loadingExcell}\r\n          onSaveQuery={() => setShowSaveQueryModal(true)}\r\n          onShowSQL={handlegetQuerySQL}\r\n        />\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      {showModalsql && (\r\n        <SqlPreviewModal\r\n          isOpen={showModalsql}\r\n          onClose={closeModalsql}\r\n          query={sql}\r\n        />\r\n      )}\r\n\r\n      {showModal && (\r\n        <InquiryModal\r\n          isOpen={showModal}\r\n          onClose={closeModal}\r\n          sql={sql}\r\n          from={from}\r\n          thang={thang}\r\n          pembulatan={pembulatan}\r\n        />\r\n      )}\r\n\r\n      {showModalsimpan && (\r\n        <SaveQueryModal\r\n          isOpen={showModalsimpan}\r\n          onClose={closeModalsimpan}\r\n          sql={sql}\r\n        />\r\n      )}\r\n\r\n      {showModalPDF && (\r\n        <ExportModal\r\n          showModalPDF={showModalPDF}\r\n          setShowModalPDF={setShowModalPDF}\r\n          selectedFormat={selectedFormat}\r\n          setSelectedFormat={setSelectedFormat}\r\n          fetchExportData={fetchExportData}\r\n          filename=\"inquiry_data\"\r\n          loading={loadingExcell}\r\n        />\r\n      )}\r\n\r\n      {/* Save Query Modal */}\r\n      {showSaveQueryModal && (\r\n        <SaveQueryModal\r\n          isOpen={showSaveQueryModal}\r\n          onClose={() => setShowSaveQueryModal(false)}\r\n          query={sql}\r\n          thang={thang}\r\n          queryType={\"INQUIRY\"}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InquiryMod;\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAOA,MAAM,aAAa;;IACjB,iCAAiC;IACjC,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,UAAe,AAAD;IAE9B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IACzC,iCAAiC;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wLAAA,CAAA,UAAe,AAAD,EAAE;IACvC,wDAAwD;IACxD,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,UAAU,EACV,WAAW,EACX,SAAS,EACT,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,MAAM,EACN,SAAS,EACT,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,UAAU,EACV,aAAa,EACb,UAAU,EACV,aAAa,EACb,cAAc,EACd,iBAAiB,EACjB,OAAO,EACP,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EACrB,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,OAAO,EACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,IAAI,EACJ,OAAO,EACP,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,EAAE,EACF,KAAK,EACL,EAAE,EACF,KAAK,EACL,GAAG,EACH,MAAM,EACN,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,GAAG,EACH,MAAM,EACN,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,WAAW,EACX,cAAc,EACd,cAAc,EACd,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,sBAAsB,EACtB,yBAAyB,EACzB,GAAG,EACH,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,EACR,GAAG;IAEJ,iBAAiB;IACjB,MAAM,iBAAiB;QACrB,kBAAkB;QAClB,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,YAAY;IACd;IAEA,sDAAsD;IACtD,MAAM,sBAAsB;QAC1B,cAAc,uCAAuC;QACrD,iBAAiB;IACnB;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,QAAQ,GAAG;YACb,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,AAAC,GAAQ,OAAN,OAAM;QACjB,OAAO;YACL,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR;QACA,iBAAiB;IACnB;IAEA,8EAA8E;IAC9E,MAAM,uBAAuB;QAC3B,MAAM,MAAM,cAAc,kDAAkD;QAC5E,OAAO;IACT;IAEA,0DAA0D;IAC1D,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,MAAM,MAAM;QACZ,QAAQ,MAAM,CAAC;QACf,aAAa,OAAO,2BAA2B;IACjD;IACA,sEAAsE;IACtE,MAAM,oBAAoB;QACxB,MAAM,YAAY,wBAAwB,wBAAwB;QAClE,QAAQ,MAAM,CAAC,YAAY,sBAAsB;QACjD,gBAAgB,OAAO,aAAa;IACtC;IAEA,uBAAuB;IACvB,MAAM,aAAa;QACjB,aAAa;QACb,mBAAmB;QACnB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,4CAA4C;IAC5C,6JAAA,CAAA,UAAK,CAAC,SAAS;gCAAC;YACd,oEAAoE;YACpE,MAAM;8DAAwB;oBAC5B,oDAAoD;oBACpD;gBACF;;YAEA;QACF;+BAAG;QAAC;QAAO;QAAQ;QAAY;KAAW,GAAG,gEAAgE;IAE7G,iFAAiF;IACjF,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAEnC,wEAAwE;IACxE,6JAAA,CAAA,UAAK,CAAC,SAAS;gCAAC;YACd,kCAAkC;YAClC,IAAI,CAAC,cAAc,OAAO,EAAE;gBAC1B,cAAc,OAAO,GAAG;gBACxB;YACF;YAEA,uEAAuE;YACvE,+FAA+F;YAC/F,+CAA+C;YAC/C,WAAW;YACX,oBAAoB;YACpB,iBAAiB;QACnB;+BAAG;QAAC;KAAO;IAEX,sCAAsC;IACtC,MAAM,mBAAmB,CAAC;QACxB,WAAW;QACX,UAAU;QACV,WAAW;QACX,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,aAAa;QACb,cAAc;QACd,eAAe;QACf,UAAU;QACV,WAAW;QACX,cAAc;QACd,4EAA4E;QAC5E,UAAU,OAAO,OAAO;QACxB,sBAAsB;IACxB;IAEA,qEAAqE;IACrE,MAAM,cAAc;QAClB,UAAU;QACV,SAAS,IAAI,OAAO,WAAW,GAAG,QAAQ;QAC1C,WAAW;QACX,UAAU;QACV,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,aAAa;QACb,cAAc;QACd,eAAe;QACf,UAAU;QACV,WAAW;QACX,cAAc;QACd,aAAa;QACb,SAAS;QACT,gBAAgB;QAChB,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,cAAc;QACd,UAAU;QACV,sBAAsB;QACtB,MAAM;QACN,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;QACV,qBAAqB;QACrB,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,kBAAkB;QAClB,eAAe;QACf,UAAU;QACV,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,UAAU;QACV,iBAAiB;QACjB,cAAc;QACd,WAAW;QACX,oBAAoB;QACpB,iBAAiB;QACjB,WAAW;QACX,QAAQ;QACR,UAAU;QACV,WAAW;QACX,YAAY;QACZ,aAAa;QACb,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,cAAc;QACd,aAAa;QACb,aAAa;QACb,cAAc;QACd,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,eAAe;QACf,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,kBAAkB;QAClB,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,mBAAmB;QACnB,WAAW;QACX,WAAW;QACX,WAAW;QACX,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,YAAY;QACZ,0BAA0B;QAC1B,eAAe;QACf,WAAW;QACX,kBAAkB;QAClB,OAAO;QACP,QAAQ;QACR,UACE;IAEJ;IAEA,8BAA8B;IAC9B,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3C,sBAAsB;IACtB,MAAM,gBAAgB,CAAC,MAAQ,WAAW;IAE1C,wDAAwD;IACxD,MAAM,YAAY;QAChB,gBAAgB;IAClB;IAEA,yDAAyD;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAChE,WAAW;IAGb,+BAA+B;IAC/B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnE,gEAAgE;IAChE,sEAAsE;IACtE,eAAe;QACb,qDAAqD;QACrD,MAAM,MAAM,wBAAwB,uCAAuC;QAC3E,IAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,IAAI,IAAI,OAAO,IAAI;YACxD,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;YACN,OAAO,EAAE;QACX;QACA,uCAAuC;QACvC,IAAI,CAAC,aAAa;YAChB,OAAO,EAAE;QACX;QAEA,IAAI;YACF,8CAA8C;YAC9C,MAAM,aAAa;YACnB,MAAM,WAAW,MAAM,SAAS,IAAI,CAClC,AAAC,GAAa,OAAX,YAAW,kBACd;gBACE;gBACA,MAAM;YACR,GACA;gBACE,SAAS;oBACP,eAAe,AAAC,UAAe,OAAN;gBAC3B;YACF;YAEF,+DAA+D;YAC/D,wEAAwE;YACxE,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBACtD,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B;YACA,OAAO,EAAE;QACX,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,EAAE;QACX;IACF;IAEA,mDAAmD;IACnD,MAAM,oBAAoB;QACxB,iBAAiB;QACjB,IAAI;YACF,MAAM,aAAa,MAAM;YACzB,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,EAAE;gBACrC,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;gBACN,iBAAiB;gBACjB;YACF;YACA,MAAM,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,YAAY;YAChC,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR,EAAE,OAAO,GAAG;YACV,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR;QACA,iBAAiB;IACnB;IAEA,iDAAiD;IACjD,MAAM,kBAAkB;QACtB,iBAAiB;QACjB,IAAI;YACF,MAAM,aAAa,MAAM;YACzB,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,EAAE;gBACrC,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;gBACN,iBAAiB;gBACjB;YACF;YACA,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,YAAY;YACxB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR,EAAE,OAAO,GAAG;YACV,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR;QACA,iBAAiB;IACnB;IAEA,kDAAkD;IAClD,MAAM,mBAAmB;QACvB,iBAAiB;QACjB,IAAI;YACF,MAAM,aAAa,MAAM;YACzB,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,EAAE;gBACrC,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;gBACN,iBAAiB;gBACjB;YACF;YACA,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,YAAY;YACzB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR,EAAE,OAAO,GAAG;YACV,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR;QACA,iBAAiB;IACnB;IAEA,kDAAkD;IAClD,MAAM,mBAAmB;QACvB,iBAAiB;QACjB,IAAI;YACF,MAAM,aAAa,MAAM;YACzB,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,EAAE;gBACrC,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;gBACN,iBAAiB;gBACjB;YACF;YACA,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,YAAY;YACzB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR,EAAE,OAAO,GAAG;YACV,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACR;QACA,iBAAiB;IACnB;IAEA,iDAAiD;IACjD,6JAAA,CAAA,UAAK,CAAC,SAAS;gCAAC;YACd,gEAAgE;YAChE;QACF;+BAAG;QAAC;QAAU;QAAW;KAAQ;IAEjC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCAGxC,6LAAC,uLAAA,CAAA,UAAkB;wBACjB,cAAc;4BACZ;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;wBACF;;;;;;kCAKF,6LAAC,sLAAA,CAAA,UAAa;wBACZ,cAAc;4BACZ,+CAA+C;4BAC/C;4BACA,oBAAoB;4BACpB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,2BAA2B;4BAC3B;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,2BAA2B;4BAC3B;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,qBAAqB;4BACrB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,yBAAyB;4BACzB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,sBAAsB;4BACtB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,wBAAwB;4BACxB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,uBAAuB;4BACvB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,qBAAqB;4BACrB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,uBAAuB;4BACvB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,uBAAuB;4BACvB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,2BAA2B;4BAC3B;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,0CAA0C;4BAC1C;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,mCAAmC;4BACnC;4BACA;4BACA;4BACA;4BACA,wBAAwB;4BACxB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,yBAAyB;4BACzB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,uBAAuB;4BACvB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,2BAA2B;4BAC3B;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,yBAAyB;4BACzB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,4BAA4B;4BAC5B;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,qBAAqB;4BACrB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,4BAA4B;4BAC5B;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,yBAAyB;4BACzB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,gCAAgC;4BAChC;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA,gCAAgC;4BAChC;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;wBAEF;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDAGnD,6LAAC;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;8CAE1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;sDAGpD,6LAAC;4CAAK,WAAU;sDACb,WAAW,MACR,cACA,WAAW,MACX,mBACA,WAAW,MACX,2BACA,WAAW,MACX,4BACA,WAAW,MACX,8BACA,WAAW,MACX,wCACA;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAqC;;;;;;sDAGrD,6LAAC;4CAAK,WAAU;sDACb,eAAe,MACZ,WACA,eAAe,SACf,WACA,eAAe,YACf,WACA,eAAe,eACf,aACA;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAqC;;;;;;sDAGrD,6LAAC;4CAAK,WAAU;;gDAEZ;oDACE;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,MAAM,CAAC,SAAS,MAAM;gDACxB;gDAAI;gDACD;gDAEH;oDACE;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,6LAAC,oLAAA,CAAA,UAAY;wBACX,gBAAgB;wBAChB,eAAe;wBACf,aAAa;wBACb,aAAa;wBACb,SAAS;wBACT,WAAW;wBACX,aAAa,IAAM,sBAAsB;wBACzC,WAAW;;;;;;;;;;;;YAKd,8BACC,6LAAC,iMAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS;gBACT,OAAO;;;;;;YAIV,2BACC,6LAAC,8LAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,YAAY;;;;;;YAIf,iCACC,6LAAC,gMAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS;gBACT,KAAK;;;;;;YAIR,8BACC,6LAAC,6LAAA,CAAA,UAAW;gBACV,cAAc;gBACd,iBAAiB;gBACjB,gBAAgB;gBAChB,mBAAmB;gBACnB,iBAAiB;gBACjB,UAAS;gBACT,SAAS;;;;;;YAKZ,oCACC,6LAAC,gMAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,OAAO;gBACP,OAAO;gBACP,WAAW;;;;;;;;;;;;AAKrB;GAhtCM;;QAEY,iLAAA,CAAA,UAAe;QAKR,wLAAA,CAAA,UAAe;;;KAPlC;uCAktCS", "debugId": null}}]}