{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterSwitch.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Switch } from \"@heroui/react\";\r\n\r\nconst FilterSwitch = ({\r\n  id,\r\n  checked,\r\n  onChange,\r\n  label,\r\n  size = \"sm\",\r\n  disabled = false,\r\n}) => {\r\n  return (\r\n    <div\r\n      className={`flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${\r\n        disabled ? \"opacity-50\" : \"\"\r\n      }`}\r\n    >\r\n      <Switch\r\n        id={id}\r\n        isSelected={checked}\r\n        onValueChange={disabled ? undefined : onChange}\r\n        size={size}\r\n        isDisabled={disabled}\r\n        aria-label={label}\r\n        aria-labelledby={`${id}-label`}\r\n        classNames={{\r\n          wrapper:\r\n            \"group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400\",\r\n          thumb: \"group-data-[selected=true]:bg-white shadow-lg\",\r\n        }}\r\n      />\r\n      <label\r\n        id={`${id}-label`}\r\n        htmlFor={id}\r\n        className={`text-sm font-medium transition-colors duration-200 flex-1 ${\r\n          disabled\r\n            ? \"text-gray-400 cursor-not-allowed\"\r\n            : \"text-gray-700 group-hover:text-purple-600 cursor-pointer\"\r\n        }`}\r\n      >\r\n        {label}\r\n      </label>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FilterSwitch;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,eAAe,CAAC,EACpB,EAAE,EACF,OAAO,EACP,QAAQ,EACR,KAAK,EACL,OAAO,IAAI,EACX,WAAW,KAAK,EACjB;IACC,qBACE,8OAAC;QACC,WAAW,CAAC,0FAA0F,EACpG,WAAW,eAAe,IAC1B;;0BAEF,8OAAC,4MAAA,CAAA,SAAM;gBACL,IAAI;gBACJ,YAAY;gBACZ,eAAe,WAAW,YAAY;gBACtC,MAAM;gBACN,YAAY;gBACZ,cAAY;gBACZ,mBAAiB,GAAG,GAAG,MAAM,CAAC;gBAC9B,YAAY;oBACV,SACE;oBACF,OAAO;gBACT;;;;;;0BAEF,8OAAC;gBACC,IAAI,GAAG,GAAG,MAAM,CAAC;gBACjB,SAAS;gBACT,WAAW,CAAC,0DAA0D,EACpE,WACI,qCACA,4DACJ;0BAED;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterSelector.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card, CardBody } from \"@heroui/react\";\r\nimport FilterSwitch from \"./FilterSwitch\";\r\n\r\n// Import filter group components\r\nimport KementerianFilter from \"./FilterGroups/KementerianFilter\";\r\nimport UnitFilter from \"./FilterGroups/UnitFilter\";\r\nimport LokasiFilter from \"./FilterGroups/LokasiFilter\";\r\nimport FungsiFilter from \"./FilterGroups/FungsiFilter\";\r\nimport SubfungsiFilter from \"./FilterGroups/SubfungsiFilter\";\r\nimport ProgramFilter from \"./FilterGroups/ProgramFilter\";\r\nimport KegiatanFilter from \"./FilterGroups/KegiatanFilter\";\r\nimport OutputFilter from \"./FilterGroups/OutputFilter\";\r\nimport SuboutputFilter from \"./FilterGroups/SuboutputFilter\";\r\nimport KomponenFilter from \"./FilterGroups/KomponenFilter\";\r\nimport SubkomponenFilter from \"./FilterGroups/SubkomponenFilter\";\r\nimport AkunFilter from \"./FilterGroups/AkunFilter\";\r\nimport SumberdanaFilter from \"./FilterGroups/SumberDanaFilter\";\r\nimport RegisterFilter from \"./FilterGroups/RegisterFilter\";\r\nimport InflasiFilter from \"./FilterGroups/InflasiFilter\";\r\nimport IknFilter from \"./FilterGroups/IknFilter\";\r\nimport KemiskinanFilter from \"./FilterGroups/KemiskinanFilter\";\r\nimport PanganFilter from \"./FilterGroups/PanganFilter\";\r\nimport StuntingFilter from \"./FilterGroups/StuntingFilter\";\r\nimport PemiluFilter from \"./FilterGroups/PemiluFilter\";\r\nimport PrinasFilter from \"./FilterGroups/PrinasFilter\";\r\nimport ProgrampriFilter from \"./FilterGroups/ProgrampriFilter\";\r\nimport KegiatanpriFilter from \"./FilterGroups/KegiatanpriFilter\";\r\nimport ProyekprioritasFilter from \"./FilterGroups/ProyekpriFilter\";\r\nimport MajorprFilter from \"./FilterGroups/MajorprFilter\";\r\nimport TematikFilter from \"./FilterGroups/TematikFilter\";\r\nimport DekonFilter from \"./FilterGroups/DekonFilter\";\r\nimport KabkotaFilter from \"./FilterGroups/KabkotaFilter\";\r\nimport KanwilFilter from \"./FilterGroups/KanwilFilter\";\r\nimport KppnFilter from \"./FilterGroups/KppnFilter\";\r\nimport SatkerFilter from \"./FilterGroups/SatkerFilter\";\r\nimport CutoffFilter from \"./FilterGroups/CutoffFilter\";\r\n\r\nconst FilterSection = ({ inquiryState }) => {\r\n  const {\r\n    // Report type for determining default switches\r\n    jenlap,\r\n    // Filter visibility states\r\n    tanggal,\r\n    setTanggal,\r\n    cutoff,\r\n    setCutoff,\r\n    showCutoffSelector,\r\n    setShowCutoffSelector,\r\n    akumulatif,\r\n    setAkumulatif,\r\n    kddept,\r\n    setKddept,\r\n    unit,\r\n    setUnit,\r\n    kddekon,\r\n    setKddekon,\r\n    kdlokasi,\r\n    setKdlokasi,\r\n    kdkabkota,\r\n    setKdkabkota,\r\n    kdkanwil,\r\n    setKdkanwil,\r\n    kdkppn,\r\n    setKdkppn,\r\n    kdsatker,\r\n    setKdsatker,\r\n    kdfungsi,\r\n    setKdfungsi,\r\n    kdsfungsi,\r\n    setKdsfungsi,\r\n    kdprogram,\r\n    setKdprogram,\r\n    kdgiat,\r\n    setKdgiat,\r\n    kdoutput,\r\n    setKdoutput,\r\n    kdsoutput,\r\n    setKdsoutput,\r\n    kdkomponen,\r\n    setKdkomponen,\r\n    kdskomponen,\r\n    setKdskomponen,\r\n    kdakun,\r\n    setKdakun,\r\n    kdsdana,\r\n    setKdsdana,\r\n    kdregister,\r\n    setKdregister,\r\n    kdInflasi,\r\n    setKdInflasi,\r\n    kdIkn,\r\n    setKdIkn,\r\n    kdKemiskinan,\r\n    setKdKemiskinan,\r\n    KdPRI,\r\n    setKdPRI,\r\n    KdPangan,\r\n    setKdPangan,\r\n    KdPemilu,\r\n    setKdPemilu,\r\n    KdStunting,\r\n    setKdStunting,\r\n    KdTema,\r\n    setKdTema,\r\n    KdPN,\r\n    setKdPN,\r\n    KdPP,\r\n    setKdPP,\r\n    KdKegPP,\r\n    setKdKegPP,\r\n    KdMP,\r\n    setKdMP,\r\n\r\n    // Filter values and conditions\r\n    dept,\r\n    setDept,\r\n    deptkondisi,\r\n    setDeptkondisi,\r\n    katadept,\r\n    setKatadept,\r\n    deptradio,\r\n    setDeptradio,\r\n    kdunit,\r\n    setKdunit,\r\n    unitkondisi,\r\n    setUnitkondisi,\r\n    kataunit,\r\n    setKataunit,\r\n    unitradio,\r\n    setUnitradio,\r\n    dekon,\r\n    setDekon,\r\n    dekonkondisi,\r\n    setDekonkondisi,\r\n    katadekon,\r\n    setKatadekon,\r\n    dekonradio,\r\n    setDekonradio,\r\n    prov,\r\n    setProv,\r\n    lokasikondisi,\r\n    setLokasikondisi,\r\n    katalokasi,\r\n    setKatalokasi,\r\n    locradio,\r\n    setLocradio,\r\n    kabkota,\r\n    setKabkota,\r\n    kabkotakondisi,\r\n    setKabkotakondisi,\r\n    katakabkota,\r\n    setKatakabkota,\r\n    kabkotaradio,\r\n    setKabkotaradio,\r\n    kanwil,\r\n    setKanwil,\r\n    kanwilkondisi,\r\n    setKanwilkondisi,\r\n    katakanwil,\r\n    setKatakanwil,\r\n    kanwilradio,\r\n    setKanwilradio,\r\n    kppn,\r\n    setKppn,\r\n    kppnkondisi,\r\n    setKppnkondisi,\r\n    katakppn,\r\n    setKatakppn,\r\n    kppnradio,\r\n    setKppnradio,\r\n    satker,\r\n    setSatker,\r\n    satkerkondisi,\r\n    setSatkerkondisi,\r\n    katasatker,\r\n    setKatasatker,\r\n    satkerradio,\r\n    setSatkerradio,\r\n    fungsi,\r\n    setFungsi,\r\n    fungsikondisi,\r\n    setFungsikondisi,\r\n    katafungsi,\r\n    setKatafungsi,\r\n    fungsiradio,\r\n    setFungsiradio,\r\n    sfungsi,\r\n    setSfungsi,\r\n    subfungsikondisi,\r\n    setSubfungsikondisi,\r\n    katasubfungsi,\r\n    setKatasubfungsi,\r\n    subfungsiradio,\r\n    setSubfungsiradio,\r\n    program,\r\n    setProgram,\r\n    programkondisi,\r\n    setProgramkondisi,\r\n    kataprogram,\r\n    setKataprogram,\r\n    programradio,\r\n    setProgramradio,\r\n    giat,\r\n    setGiat,\r\n    giatkondisi,\r\n    setGiatkondisi,\r\n    katagiat,\r\n    setKatagiat,\r\n    kegiatanradio,\r\n    setKegiatanradio,\r\n    output,\r\n    setOutput,\r\n    outputkondisi,\r\n    setOutputkondisi,\r\n    kataoutput,\r\n    setKataoutput,\r\n    outputradio,\r\n    setOutputradio,\r\n    soutput,\r\n    setsOutput,\r\n    soutputkondisi,\r\n    setSoutputkondisi,\r\n    katasoutput,\r\n    setKatasoutput,\r\n    soutputradio,\r\n    setsOutputradio,\r\n    komponen,\r\n    setKomponen,\r\n    komponenkondisi,\r\n    setKomponenkondisi,\r\n    katakomponen,\r\n    setKatakomponen,\r\n    komponenradio,\r\n    setKomponenradio,\r\n    skomponen,\r\n    setSkomponen,\r\n    skomponenkondisi,\r\n    setSkomponenkondisi,\r\n    kataskomponen,\r\n    setKataskomponen,\r\n    skomponenradio,\r\n    setSkomponenradio,\r\n    akun,\r\n    setAkun,\r\n    akunkondisi,\r\n    setAkunkondisi,\r\n    kataakun,\r\n    setKataakun,\r\n    akunradio,\r\n    setAkunradio,\r\n    sdana,\r\n    setSdana,\r\n    sdanakondisi,\r\n    setSdanakondisi,\r\n    katasdana,\r\n    setKatasdana,\r\n    sdanaradio,\r\n    setSdanaradio,\r\n    register,\r\n    setRegister,\r\n    registerkondisi,\r\n    setRegisterkondisi,\r\n    kataregister,\r\n    setKataregister,\r\n    registerradio,\r\n    setRegisterradio,\r\n    Inflasi,\r\n    setInflasi,\r\n    inflasiradio,\r\n    setInflasiradio,\r\n    opsiInflasi,\r\n    setOpsiInflasi,\r\n    Ikn,\r\n    setIkn,\r\n    iknradio,\r\n    setIknradio,\r\n    opsiIkn,\r\n    setOpsiIkn,\r\n    Miskin,\r\n    setMiskin,\r\n    kemiskinanradio,\r\n    setKemiskinanradio,\r\n    opsiKemiskinan,\r\n    setOpsiKemiskinan,\r\n    Pangan,\r\n    setPangan,\r\n    panganradio,\r\n    setPanganradio,\r\n    opsiPangan,\r\n    setOpsiPangan,\r\n    Stunting,\r\n    setStunting,\r\n    stuntingradio,\r\n    setStuntingradio,\r\n    opsiStunting,\r\n    setOpsiStunting,\r\n    PN,\r\n    setPN,\r\n    pnradio,\r\n    setPnradio,\r\n    PP,\r\n    setPP,\r\n    ppradio,\r\n    setPpradio,\r\n    kegiatanprioritas,\r\n    setKegiatanPrioritas,\r\n    kegiatanprioritasradio,\r\n    setKegiatanPrioritasRadio,\r\n    MP,\r\n    setMP,\r\n    mpradio,\r\n    setMpradio,\r\n    Tema,\r\n    setTema,\r\n    temaradio,\r\n    setTemaradio,\r\n    Pemilu,\r\n    setPemilu,\r\n    pemiluradio,\r\n    setPemiluradio,\r\n    PRI,\r\n    setPRI,\r\n    priradio,\r\n    setPriradio,\r\n  } = inquiryState;\r\n\r\n  // Remove local state - use the actual filter switch states from inquiryState instead\r\n  // const [showKementerian, setShowKementerian] = React.useState(false);\r\n  // const [showUnit, setShowUnit] = React.useState(false);\r\n  // const [showDekon, setShowDekon] = React.useState(false);\r\n  // const [showKabkota, setShowKabkota] = React.useState(false);\r\n  // const [showKanwil, setShowKanwil] = React.useState(false);\r\n  // const [showLokasi, setShowLokasi] = React.useState(false);\r\n\r\n  // Use the actual filter switch states from inquiryState\r\n  const showKementerian = kddept;\r\n  const setShowKementerian = setKddept;\r\n  const showUnit = unit;\r\n  const setShowUnit = setUnit;\r\n  const showDekon = kddekon;\r\n  const setShowDekon = setKddekon;\r\n  const showKabkota = kdkabkota;\r\n  const setShowKabkota = setKdkabkota;\r\n  const showKanwil = kdkanwil;\r\n  const setShowKanwil = setKdkanwil;\r\n  const showLokasi = kdlokasi;\r\n  const setShowLokasi = setKdlokasi;\r\n\r\n  // Determine which switches should be disabled based on report type\r\n  const getDisabledSwitches = (reportType) => {\r\n    // For Volume Output Kegiatan (6), disable cutoff, akun, register, sumber dana\r\n    if (reportType === \"6\") {\r\n      return [\r\n        \"cutoff\", // Cutoff\r\n        \"kdakun\", // Akun\r\n        \"kdregister\", // Register\r\n        \"kdsdana\", // Sumber Dana\r\n      ];\r\n    }\r\n\r\n    // Base disabled switches for most report types (same as Pagu Realisasi)\r\n    const baseDisabledSwitches = [\r\n      \"kdsoutput\", // Sub-output\r\n      \"KdPN\", // Prioritas Nasional\r\n      \"KdPP\", // Program Prioritas\r\n      \"KdKegPP\", // Kegiatan Prioritas\r\n      \"KdPRI\", // Proyek Prioritas\r\n      \"KdMP\", // Major Project\r\n      \"KdTema\", // Tematik\r\n      \"kdInflasi\", // Inflasi\r\n      \"KdStunting\", // Stunting\r\n      \"kdKemiskinan\", // Kemiskinan\r\n      \"KdPemilu\", // Pemilu\r\n      \"kdIkn\", // IKN\r\n      \"KdPangan\", // Pangan\r\n    ];\r\n\r\n    // For Pagu APBN (1), disable the same switches as other types plus cutoff\r\n    if (reportType === \"1\") {\r\n      return [...baseDisabledSwitches, \"cutoff\"];\r\n    }\r\n\r\n    // For all other report types (including Pagu Realisasi \"2\"), disable base switches\r\n    return baseDisabledSwitches;\r\n  };\r\n\r\n  const disabledSwitches = getDisabledSwitches(jenlap);\r\n\r\n  // Automatically turn off disabled switches when report type changes\r\n  // This effect runs when jenlap changes to manage switch states\r\n  React.useEffect(() => {\r\n    if (!jenlap) return;\r\n\r\n    // If switching to a report type that disables certain switches, turn them off\r\n    if (disabledSwitches.length > 0) {\r\n      if (disabledSwitches.includes(\"kdsoutput\") && kdsoutput) {\r\n        setKdsoutput && setKdsoutput(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPN\") && KdPN) {\r\n        setKdPN && setKdPN(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPP\") && KdPP) {\r\n        setKdPP && setKdPP(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdKegPP\") && KdKegPP) {\r\n        setKdKegPP && setKdKegPP(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPRI\") && KdPRI) {\r\n        setKdPRI && setKdPRI(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdMP\") && KdMP) {\r\n        setKdMP && setKdMP(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdTema\") && KdTema) {\r\n        setKdTema && setKdTema(false);\r\n      }\r\n      if (disabledSwitches.includes(\"kdInflasi\") && kdInflasi) {\r\n        setKdInflasi && setKdInflasi(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdStunting\") && KdStunting) {\r\n        setKdStunting && setKdStunting(false);\r\n      }\r\n      if (disabledSwitches.includes(\"kdKemiskinan\") && kdKemiskinan) {\r\n        setKdKemiskinan && setKdKemiskinan(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPemilu\") && KdPemilu) {\r\n        setKdPemilu && setKdPemilu(false);\r\n      }\r\n      if (disabledSwitches.includes(\"kdIkn\") && kdIkn) {\r\n        setKdIkn && setKdIkn(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPangan\") && KdPangan) {\r\n        setKdPangan && setKdPangan(false);\r\n      }\r\n      if (disabledSwitches.includes(\"cutoff\") && cutoff !== \"0\") {\r\n        setCutoff && setCutoff(\"0\");\r\n        setShowCutoffSelector && setShowCutoffSelector(false);\r\n      }\r\n    }\r\n  }, [jenlap]); // Only depend on jenlap to avoid circular dependencies\r\n\r\n  // Reset filter values when switches are turned off\r\n  React.useEffect(() => {\r\n    if (!kddept) {\r\n      // Reset Kementerian filter state\r\n      setDept && setDept(\"000\"); // Default dept value\r\n      setDeptkondisi && setDeptkondisi(\"\");\r\n      setKatadept && setKatadept(\"\");\r\n      setDeptradio && setDeptradio(\"1\");\r\n    }\r\n  }, [kddept, setDept, setDeptkondisi, setKatadept, setDeptradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!unit) {\r\n      // Reset Unit filter state\r\n      setKdunit && setKdunit(\"XX\");\r\n      setUnitkondisi && setUnitkondisi(\"\");\r\n      setKataunit && setKataunit(\"\");\r\n      setUnitradio && setUnitradio(\"1\");\r\n    }\r\n  }, [unit, setKdunit, setUnitkondisi, setKataunit, setUnitradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kddekon) {\r\n      // Reset Dekon filter state\r\n      setDekon && setDekon(\"XX\");\r\n      setDekonkondisi && setDekonkondisi(\"\");\r\n      setKatadekon && setKatadekon(\"\");\r\n      setDekonradio && setDekonradio(\"1\");\r\n    }\r\n  }, [kddekon, setDekon, setDekonkondisi, setKatadekon, setDekonradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdlokasi) {\r\n      // Reset Provinsi filter state\r\n      setProv && setProv(\"XX\");\r\n      setLokasikondisi && setLokasikondisi(\"\");\r\n      setKatalokasi && setKatalokasi(\"\");\r\n      setLocradio && setLocradio(\"1\");\r\n    }\r\n  }, [kdlokasi, setProv, setLokasikondisi, setKatalokasi, setLocradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkabkota) {\r\n      // Reset Kabkota filter state\r\n      setKabkota && setKabkota(\"XX\");\r\n      setKabkotakondisi && setKabkotakondisi(\"\");\r\n      setKatakabkota && setKatakabkota(\"\");\r\n      setKabkotaradio && setKabkotaradio(\"1\");\r\n    }\r\n  }, [\r\n    kdkabkota,\r\n    setKabkota,\r\n    setKabkotakondisi,\r\n    setKatakabkota,\r\n    setKabkotaradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkanwil) {\r\n      // Reset Kanwil filter state\r\n      setKanwil && setKanwil(\"XX\");\r\n      setKanwilkondisi && setKanwilkondisi(\"\");\r\n      setKatakanwil && setKatakanwil(\"\");\r\n      setKanwilradio && setKanwilradio(\"1\");\r\n    }\r\n  }, [kdkanwil, setKanwil, setKanwilkondisi, setKatakanwil, setKanwilradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkppn) {\r\n      // Reset KPPN filter state\r\n      setKppn && setKppn(\"XX\");\r\n      setKppnkondisi && setKppnkondisi(\"\");\r\n      setKatakppn && setKatakppn(\"\");\r\n      setKppnradio && setKppnradio(\"1\");\r\n    }\r\n  }, [kdkppn, setKppn, setKppnkondisi, setKatakppn, setKppnradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsatker) {\r\n      // Reset Satker filter state\r\n      setSatker && setSatker(\"XX\");\r\n      setSatkerkondisi && setSatkerkondisi(\"\");\r\n      setKatasatker && setKatasatker(\"\");\r\n      setSatkerradio && setSatkerradio(\"1\");\r\n    }\r\n  }, [kdsatker, setSatker, setSatkerkondisi, setKatasatker, setSatkerradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdfungsi) {\r\n      // Reset Fungsi filter state\r\n      setFungsi && setFungsi(\"XX\");\r\n      setFungsikondisi && setFungsikondisi(\"\");\r\n      setKatafungsi && setKatafungsi(\"\");\r\n      setFungsiradio && setFungsiradio(\"1\");\r\n    }\r\n  }, [kdfungsi, setFungsi, setFungsikondisi, setKatafungsi, setFungsiradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsfungsi) {\r\n      // Reset Sub-fungsi filter state\r\n      setSfungsi && setSfungsi(\"XX\");\r\n      setSubfungsikondisi && setSubfungsikondisi(\"\");\r\n      setKatasubfungsi && setKatasubfungsi(\"\");\r\n      setSubfungsiradio && setSubfungsiradio(\"1\");\r\n    }\r\n  }, [\r\n    kdsfungsi,\r\n    setSfungsi,\r\n    setSubfungsikondisi,\r\n    setKatasubfungsi,\r\n    setSubfungsiradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdprogram) {\r\n      // Reset Program filter state\r\n      setProgram && setProgram(\"XX\");\r\n      setProgramkondisi && setProgramkondisi(\"\");\r\n      setKataprogram && setKataprogram(\"\");\r\n      setProgramradio && setProgramradio(\"1\");\r\n    }\r\n  }, [\r\n    kdprogram,\r\n    setProgram,\r\n    setProgramkondisi,\r\n    setKataprogram,\r\n    setProgramradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdgiat) {\r\n      // Reset Kegiatan filter state\r\n      setGiat && setGiat(\"XX\");\r\n      setGiatkondisi && setGiatkondisi(\"\");\r\n      setKatagiat && setKatagiat(\"\");\r\n      setKegiatanradio && setKegiatanradio(\"1\");\r\n    }\r\n  }, [kdgiat, setGiat, setGiatkondisi, setKatagiat, setKegiatanradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdoutput) {\r\n      // Reset Output filter state\r\n      setOutput && setOutput(\"XX\");\r\n      setOutputkondisi && setOutputkondisi(\"\");\r\n      setKataoutput && setKataoutput(\"\");\r\n      setOutputradio && setOutputradio(\"1\");\r\n    }\r\n  }, [kdoutput, setOutput, setOutputkondisi, setKataoutput, setOutputradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsoutput) {\r\n      // Reset Sub-output filter state\r\n      setsOutput && setsOutput(\"XX\");\r\n      setSoutputkondisi && setSoutputkondisi(\"\");\r\n      setKatasoutput && setKatasoutput(\"\");\r\n      setsOutputradio && setsOutputradio(\"1\");\r\n    }\r\n  }, [\r\n    kdsoutput,\r\n    setsOutput,\r\n    setSoutputkondisi,\r\n    setKatasoutput,\r\n    setsOutputradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkomponen) {\r\n      // Reset Komponen filter state\r\n      setKomponen && setKomponen(\"XX\");\r\n      setKomponenkondisi && setKomponenkondisi(\"\");\r\n      setKatakomponen && setKatakomponen(\"\");\r\n      setKomponenradio && setKomponenradio(\"1\");\r\n    }\r\n  }, [\r\n    kdkomponen,\r\n    setKomponen,\r\n    setKomponenkondisi,\r\n    setKatakomponen,\r\n    setKomponenradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdskomponen) {\r\n      // Reset Sub-komponen filter state\r\n      setSkomponen && setSkomponen(\"XX\");\r\n      setSkomponenkondisi && setSkomponenkondisi(\"\");\r\n      setKataskomponen && setKataskomponen(\"\");\r\n      setSkomponenradio && setSkomponenradio(\"1\");\r\n    }\r\n  }, [\r\n    kdskomponen,\r\n    setSkomponen,\r\n    setSkomponenkondisi,\r\n    setKataskomponen,\r\n    setSkomponenradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdakun) {\r\n      // Reset Akun filter state\r\n      setAkun && setAkun(\"AKUN\");\r\n      setAkunkondisi && setAkunkondisi(\"\");\r\n      setKataakun && setKataakun(\"\");\r\n      setAkunradio && setAkunradio(\"1\");\r\n    }\r\n  }, [kdakun, setAkun, setAkunkondisi, setKataakun, setAkunradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsdana) {\r\n      // Reset Sumber Dana filter state\r\n      setSdana && setSdana(\"XX\");\r\n      setSdanakondisi && setSdanakondisi(\"\");\r\n      setKatasdana && setKatasdana(\"\");\r\n      setSdanaradio && setSdanaradio(\"1\");\r\n    }\r\n  }, [kdsdana, setSdana, setSdanakondisi, setKatasdana, setSdanaradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdregister) {\r\n      // Reset Register filter state\r\n      setRegister && setRegister(\"XX\");\r\n      setRegisterkondisi && setRegisterkondisi(\"\");\r\n      setKataregister && setKataregister(\"\");\r\n      setRegisterradio && setRegisterradio(\"1\");\r\n    }\r\n  }, [\r\n    kdregister,\r\n    setRegister,\r\n    setRegisterkondisi,\r\n    setKataregister,\r\n    setRegisterradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdInflasi) {\r\n      // Reset Inflasi filter state\r\n      setInflasi && setInflasi(\"00\");\r\n      setInflasiradio && setInflasiradio(\"1\");\r\n    }\r\n  }, [kdInflasi, setInflasi, setInflasiradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdIkn) {\r\n      // Reset IKN filter state\r\n      setIkn && setIkn(\"00\");\r\n      setIknradio && setIknradio(\"1\");\r\n    }\r\n  }, [kdIkn, setIkn, setIknradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdKemiskinan) {\r\n      // Reset Kemiskinan filter state\r\n      setMiskin && setMiskin(\"00\");\r\n      setKemiskinanradio && setKemiskinanradio(\"1\");\r\n    }\r\n  }, [kdKemiskinan, setMiskin, setKemiskinanradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPangan) {\r\n      // Reset Pangan filter state\r\n      setPangan && setPangan(\"00\");\r\n      setPanganradio && setPanganradio(\"1\");\r\n    }\r\n  }, [KdPangan, setPangan, setPanganradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdStunting) {\r\n      // Reset Stunting filter state\r\n      setStunting && setStunting(\"00\");\r\n      setStuntingradio && setStuntingradio(\"1\");\r\n    }\r\n  }, [KdStunting, setStunting, setStuntingradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPN) {\r\n      // Reset Prinas filter state\r\n      setPN && setPN(\"00\");\r\n      setPnradio && setPnradio(\"1\");\r\n    }\r\n  }, [KdPN, setPN, setPnradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPP) {\r\n      // Reset Programpri filter state\r\n      setPP && setPP(\"00\");\r\n      setPpradio && setPpradio(\"1\");\r\n    }\r\n  }, [KdPP, setPP, setPpradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdKegPP) {\r\n      // Reset Kegiatanpri filter state\r\n      setKegiatanPrioritas && setKegiatanPrioritas(\"XX\");\r\n      setKegiatanPrioritasRadio && setKegiatanPrioritasRadio(\"1\");\r\n    }\r\n  }, [KdKegPP, setKegiatanPrioritas, setKegiatanPrioritasRadio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdMP) {\r\n      // Reset Majorpr filter state\r\n      setMP && setMP(\"00\");\r\n      setMpradio && setMpradio(\"1\");\r\n    }\r\n  }, [KdMP, setMP, setMpradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdTema) {\r\n      // Reset Tematik filter state\r\n      setTema && setTema(\"00\");\r\n      setTemaradio && setTemaradio(\"1\");\r\n    }\r\n  }, [KdTema, setTema, setTemaradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPemilu) {\r\n      // Reset Pemilu filter state\r\n      setPemilu && setPemilu(\"00\");\r\n      setPemiluradio && setPemiluradio(\"1\");\r\n    }\r\n  }, [KdPemilu, setPemilu, setPemiluradio]);\r\n\r\n  // Set default filter switches based on report type (jenlap)\r\n  React.useEffect(() => {\r\n    if (!jenlap) return; // Don't do anything if jenlap is not set\r\n\r\n    // Define default switches for each report type\r\n    const getDefaultSwitches = (reportType) => {\r\n      // Base configuration: Only Kementerian is ON by default for all report types\r\n      const baseConfig = {\r\n        kddept: true, // Kementerian is always ON\r\n        unit: false,\r\n        kddekon: false,\r\n        kdlokasi: false,\r\n        kdkabkota: false,\r\n        kdkanwil: false,\r\n        kdkppn: false,\r\n        kdsatker: false,\r\n        kdfungsi: false,\r\n        kdsfungsi: false,\r\n        kdprogram: false,\r\n        kdgiat: false,\r\n        kdoutput: false,\r\n        kdsoutput: false,\r\n        kdakun: false,\r\n        kdsdana: false,\r\n        kdregister: false,\r\n        KdPN: false,\r\n        KdPP: false,\r\n        KdKegPP: false,\r\n        KdPRI: false,\r\n        KdMP: false,\r\n        KdTema: false,\r\n        kdInflasi: false,\r\n        KdStunting: false,\r\n        kdKemiskinan: false,\r\n        KdPemilu: false,\r\n        kdIkn: false,\r\n        KdPangan: false,\r\n      };\r\n\r\n      // Return base config for all report types\r\n      // Special cases (Pagu APBN and Volume Output Kegiatan) will have all switches available\r\n      // Other report types will have certain switches disabled (handled in the UI)\r\n      return baseConfig;\r\n    };\r\n\r\n    const defaultSwitches = getDefaultSwitches(jenlap);\r\n\r\n    // Apply the default switches\r\n    setKddept && setKddept(defaultSwitches.kddept);\r\n    setUnit && setUnit(defaultSwitches.unit);\r\n    setKddekon && setKddekon(defaultSwitches.kddekon);\r\n    setKdlokasi && setKdlokasi(defaultSwitches.kdlokasi);\r\n    setKdkabkota && setKdkabkota(defaultSwitches.kdkabkota);\r\n    setKdkanwil && setKdkanwil(defaultSwitches.kdkanwil);\r\n    setKdkppn && setKdkppn(defaultSwitches.kdkppn);\r\n    setKdsatker && setKdsatker(defaultSwitches.kdsatker);\r\n    setKdfungsi && setKdfungsi(defaultSwitches.kdfungsi);\r\n    setKdsfungsi && setKdsfungsi(defaultSwitches.kdsfungsi);\r\n    setKdprogram && setKdprogram(defaultSwitches.kdprogram);\r\n    setKdgiat && setKdgiat(defaultSwitches.kdgiat);\r\n    setKdoutput && setKdoutput(defaultSwitches.kdoutput);\r\n    setKdsoutput && setKdsoutput(defaultSwitches.kdsoutput);\r\n    setKdakun && setKdakun(defaultSwitches.kdakun);\r\n    setKdsdana && setKdsdana(defaultSwitches.kdsdana);\r\n    setKdregister && setKdregister(defaultSwitches.kdregister);\r\n    setKdPN && setKdPN(defaultSwitches.KdPN);\r\n    setKdPP && setKdPP(defaultSwitches.KdPP);\r\n    setKdKegPP && setKdKegPP(defaultSwitches.KdKegPP);\r\n    setKdPRI && setKdPRI(defaultSwitches.KdPRI);\r\n    setKdMP && setKdMP(defaultSwitches.KdMP);\r\n    setKdTema && setKdTema(defaultSwitches.KdTema);\r\n    setKdInflasi && setKdInflasi(defaultSwitches.kdInflasi);\r\n    setKdStunting && setKdStunting(defaultSwitches.KdStunting);\r\n    setKdKemiskinan && setKdKemiskinan(defaultSwitches.kdKemiskinan);\r\n    setKdPemilu && setKdPemilu(defaultSwitches.KdPemilu);\r\n    setKdIkn && setKdIkn(defaultSwitches.kdIkn);\r\n    setKdPangan && setKdPangan(defaultSwitches.KdPangan);\r\n  }, [jenlap]); // Only run when jenlap changes\r\n\r\n  return (\r\n    <>\r\n      {/* Filter Options Card - Modern design with gradient and rounded corners */}\r\n      <div className=\"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl\">\r\n        {/* <div className=\"flex items-center gap-3 mb-4\">\r\n          <h5 className=\"text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">\r\n            Filter Options\r\n          </h5>\r\n        </div> */}\r\n        <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3\">\r\n          {/* <FilterSwitch\r\n            id=\"tanggal-filter\"\r\n            checked={tanggal}\r\n            onChange={setTanggal}\r\n            label=\"Tanggal\"\r\n          /> */}\r\n          <FilterSwitch\r\n            id=\"cutoff-filter\"\r\n            checked={cutoff !== \"0\"}\r\n            onChange={(val) => {\r\n              if (val) {\r\n                // When enabled, reset to January\r\n                setCutoff(\"1\");\r\n              } else {\r\n                // When disabled, set to \"0\" (disabled state)\r\n                setCutoff(\"0\");\r\n              }\r\n              setShowCutoffSelector(val);\r\n            }}\r\n            label=\"Cutoff\"\r\n            disabled={disabledSwitches.includes(\"cutoff\")}\r\n          />\r\n          {/* <FilterSwitch\r\n            id=\"akumulatif-filter\"\r\n            checked={akumulatif}\r\n            onChange={setAkumulatif}\r\n            label=\"Akumulatif\"\r\n          /> */}\r\n          <FilterSwitch\r\n            id=\"kddept-filter\"\r\n            checked={Boolean(showKementerian)}\r\n            onChange={setShowKementerian}\r\n            label=\"Kementerian\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"unit-filter\"\r\n            checked={showUnit}\r\n            onChange={setShowUnit}\r\n            label=\"Eselon I\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"dekon-filter\"\r\n            checked={showDekon}\r\n            onChange={setShowDekon}\r\n            label=\"Kewenangan\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"lokasi-filter\"\r\n            checked={showLokasi}\r\n            onChange={setShowLokasi}\r\n            label=\"Provinsi\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kabkota-filter\"\r\n            checked={showKabkota}\r\n            onChange={setShowKabkota}\r\n            label=\"Kabupaten/Kota\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kanwil-filter\"\r\n            checked={showKanwil}\r\n            onChange={setShowKanwil}\r\n            label=\"Kanwil\"\r\n          />\r\n\r\n          <FilterSwitch\r\n            id=\"kdkppn-filter\"\r\n            checked={kdkppn}\r\n            onChange={setKdkppn}\r\n            label=\"KPPN\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdsatker-filter\"\r\n            checked={kdsatker}\r\n            onChange={setKdsatker}\r\n            label=\"Satker\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdfungsi-filter\"\r\n            checked={kdfungsi}\r\n            onChange={setKdfungsi}\r\n            label=\"Fungsi\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdsfungsi-filter\"\r\n            checked={kdsfungsi}\r\n            onChange={setKdsfungsi}\r\n            label=\"Sub-fungsi\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdprogram-filter\"\r\n            checked={kdprogram}\r\n            onChange={setKdprogram}\r\n            label=\"Program\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdgiat-filter\"\r\n            checked={kdgiat}\r\n            onChange={setKdgiat}\r\n            label=\"Kegiatan\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdoutput-filter\"\r\n            checked={kdoutput}\r\n            onChange={setKdoutput}\r\n            label=\"Output\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdsoutput-filter\"\r\n            checked={kdsoutput}\r\n            onChange={setKdsoutput}\r\n            label=\"Sub-output\"\r\n            disabled={disabledSwitches.includes(\"kdsoutput\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdakun-filter\"\r\n            checked={kdakun}\r\n            onChange={setKdakun}\r\n            label=\"Akun\"\r\n            disabled={disabledSwitches.includes(\"kdakun\")}\r\n          />\r\n          {/* <FilterSwitch\r\n            id=\"kdkomponen-filter\"\r\n            checked={kdkomponen}\r\n            onChange={setKdkomponen}\r\n            label=\"Komponen\"\r\n          /> */}\r\n          {/* <FilterSwitch\r\n            id=\"kdskomponen-filter\"\r\n            checked={kdskomponen}\r\n            onChange={setKdskomponen}\r\n            label=\"Sub-komponen\"\r\n          /> */}\r\n\r\n          <FilterSwitch\r\n            id=\"kdsdana-filter\"\r\n            checked={kdsdana}\r\n            onChange={setKdsdana}\r\n            label=\"Sumber Dana\"\r\n            disabled={disabledSwitches.includes(\"kdsdana\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdregister-filter\"\r\n            checked={kdregister}\r\n            onChange={setKdregister}\r\n            label=\"Register\"\r\n            disabled={disabledSwitches.includes(\"kdregister\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"prinas-filter\"\r\n            checked={KdPN}\r\n            onChange={setKdPN}\r\n            label=\"Prioritas Nasional\"\r\n            disabled={disabledSwitches.includes(\"KdPN\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"programpri-filter\"\r\n            checked={KdPP}\r\n            onChange={setKdPP}\r\n            label=\"Program Prioritas\"\r\n            disabled={disabledSwitches.includes(\"KdPP\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kegiatanpri-filter\"\r\n            checked={KdKegPP}\r\n            onChange={setKdKegPP}\r\n            label=\"Kegiatan Prioritas\"\r\n            disabled={disabledSwitches.includes(\"KdKegPP\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"proyek-prioritas-filter\"\r\n            checked={KdPRI}\r\n            onChange={setKdPRI}\r\n            label=\"Proyek Prioritas\"\r\n            disabled={disabledSwitches.includes(\"KdPRI\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"majorpr-filter\"\r\n            checked={KdMP}\r\n            onChange={setKdMP}\r\n            label=\"Major Project\"\r\n            disabled={disabledSwitches.includes(\"KdMP\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"tematik-filter\"\r\n            checked={KdTema}\r\n            onChange={setKdTema}\r\n            label=\"Tematik\"\r\n            disabled={disabledSwitches.includes(\"KdTema\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"inflasi-filter\"\r\n            checked={kdInflasi}\r\n            onChange={setKdInflasi}\r\n            label=\"Inflasi\"\r\n            disabled={disabledSwitches.includes(\"kdInflasi\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"stunting-filter\"\r\n            checked={KdStunting}\r\n            onChange={setKdStunting}\r\n            label=\"Stunting\"\r\n            disabled={disabledSwitches.includes(\"KdStunting\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kemiskinan-filter\"\r\n            checked={kdKemiskinan}\r\n            onChange={setKdKemiskinan}\r\n            label=\"Kemiskinan Extrem\"\r\n            disabled={disabledSwitches.includes(\"kdKemiskinan\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"pemilu-filter\"\r\n            checked={KdPemilu}\r\n            onChange={setKdPemilu}\r\n            label=\"Pemilu\"\r\n            disabled={disabledSwitches.includes(\"KdPemilu\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"ikn-filter\"\r\n            checked={kdIkn}\r\n            onChange={setKdIkn}\r\n            label=\"IKN\"\r\n            disabled={disabledSwitches.includes(\"kdIkn\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"pangan-filter\"\r\n            checked={KdPangan}\r\n            onChange={setKdPangan}\r\n            label=\"Ketahanan Pangan\"\r\n            disabled={disabledSwitches.includes(\"KdPangan\")}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filter Components Row - Modern spacing and styling */}\r\n      <div className=\"space-y-4 mb-4\">\r\n        {/* Always show CutoffFilter card, but disable select when switch is OFF */}\r\n        <CutoffFilter inquiryState={inquiryState} />\r\n        {showKementerian && (\r\n          <KementerianFilter\r\n            inquiryState={inquiryState}\r\n            status={showKementerian ? \"pilihdept\" : \"\"}\r\n          />\r\n        )}\r\n        {showUnit && <UnitFilter inquiryState={inquiryState} />}\r\n        {showDekon && <DekonFilter inquiryState={inquiryState} />}\r\n        {showLokasi && <LokasiFilter inquiryState={inquiryState} />}\r\n        {showKabkota && <KabkotaFilter inquiryState={inquiryState} />}\r\n        {showKanwil && <KanwilFilter inquiryState={inquiryState} />}\r\n        {kdkppn && <KppnFilter inquiryState={inquiryState} />}\r\n        {kdsatker && <SatkerFilter inquiryState={inquiryState} />}\r\n        {kdfungsi && <FungsiFilter inquiryState={inquiryState} />}\r\n        {kdsfungsi && <SubfungsiFilter inquiryState={inquiryState} />}\r\n        {kdprogram && <ProgramFilter inquiryState={inquiryState} />}\r\n        {kdgiat && <KegiatanFilter inquiryState={inquiryState} />}\r\n        {kdoutput && <OutputFilter type=\"output\" inquiryState={inquiryState} />}\r\n        {kdsoutput && <SuboutputFilter inquiryState={inquiryState} />}\r\n        {kdakun && <AkunFilter inquiryState={inquiryState} />}\r\n        {kdkomponen && <KomponenFilter inquiryState={inquiryState} />}\r\n        {kdskomponen && <SubkomponenFilter inquiryState={inquiryState} />}\r\n        {kdsdana && (\r\n          <SumberdanaFilter type=\"source\" inquiryState={inquiryState} />\r\n        )}\r\n        {kdregister && (\r\n          <RegisterFilter type=\"register\" inquiryState={inquiryState} />\r\n        )}\r\n        {KdPN && <PrinasFilter inquiryState={inquiryState} />}\r\n        {KdPP && <ProgrampriFilter inquiryState={inquiryState} />}\r\n        {KdKegPP && <KegiatanpriFilter inquiryState={inquiryState} />}\r\n        {KdPRI && <ProyekprioritasFilter inquiryState={inquiryState} />}\r\n        {KdMP && <MajorprFilter inquiryState={inquiryState} />}\r\n        {KdTema && <TematikFilter inquiryState={inquiryState} />}\r\n        {kdInflasi && <InflasiFilter inquiryState={inquiryState} />}\r\n        {KdStunting && <StuntingFilter inquiryState={inquiryState} />}\r\n        {kdKemiskinan && <KemiskinanFilter inquiryState={inquiryState} />}\r\n        {KdPemilu && <PemiluFilter inquiryState={inquiryState} />}\r\n        {kdIkn && <IknFilter inquiryState={inquiryState} />}\r\n        {KdPangan && <PanganFilter inquiryState={inquiryState} />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FilterSection;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE;IACrC,MAAM,EACJ,+CAA+C;IAC/C,MAAM,EACN,2BAA2B;IAC3B,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,kBAAkB,EAClB,qBAAqB,EACrB,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,OAAO,EACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,IAAI,EACJ,OAAO,EAEP,+BAA+B;IAC/B,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,IAAI,EACJ,OAAO,EACP,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,GAAG,EACH,MAAM,EACN,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,eAAe,EACf,kBAAkB,EAClB,cAAc,EACd,iBAAiB,EACjB,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,EAAE,EACF,KAAK,EACL,OAAO,EACP,UAAU,EACV,EAAE,EACF,KAAK,EACL,OAAO,EACP,UAAU,EACV,iBAAiB,EACjB,oBAAoB,EACpB,sBAAsB,EACtB,yBAAyB,EACzB,EAAE,EACF,KAAK,EACL,OAAO,EACP,UAAU,EACV,IAAI,EACJ,OAAO,EACP,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,GAAG,EACH,MAAM,EACN,QAAQ,EACR,WAAW,EACZ,GAAG;IAEJ,qFAAqF;IACrF,uEAAuE;IACvE,yDAAyD;IACzD,2DAA2D;IAC3D,+DAA+D;IAC/D,6DAA6D;IAC7D,6DAA6D;IAE7D,wDAAwD;IACxD,MAAM,kBAAkB;IACxB,MAAM,qBAAqB;IAC3B,MAAM,WAAW;IACjB,MAAM,cAAc;IACpB,MAAM,YAAY;IAClB,MAAM,eAAe;IACrB,MAAM,cAAc;IACpB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IACtB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IAEtB,mEAAmE;IACnE,MAAM,sBAAsB,CAAC;QAC3B,8EAA8E;QAC9E,IAAI,eAAe,KAAK;YACtB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;QAEA,wEAAwE;QACxE,MAAM,uBAAuB;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,0EAA0E;QAC1E,IAAI,eAAe,KAAK;YACtB,OAAO;mBAAI;gBAAsB;aAAS;QAC5C;QAEA,mFAAmF;QACnF,OAAO;IACT;IAEA,MAAM,mBAAmB,oBAAoB;IAE7C,oEAAoE;IACpE,+DAA+D;IAC/D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;QAEb,8EAA8E;QAC9E,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,IAAI,iBAAiB,QAAQ,CAAC,gBAAgB,WAAW;gBACvD,gBAAgB,aAAa;YAC/B;YACA,IAAI,iBAAiB,QAAQ,CAAC,WAAW,MAAM;gBAC7C,WAAW,QAAQ;YACrB;YACA,IAAI,iBAAiB,QAAQ,CAAC,WAAW,MAAM;gBAC7C,WAAW,QAAQ;YACrB;YACA,IAAI,iBAAiB,QAAQ,CAAC,cAAc,SAAS;gBACnD,cAAc,WAAW;YAC3B;YACA,IAAI,iBAAiB,QAAQ,CAAC,YAAY,OAAO;gBAC/C,YAAY,SAAS;YACvB;YACA,IAAI,iBAAiB,QAAQ,CAAC,WAAW,MAAM;gBAC7C,WAAW,QAAQ;YACrB;YACA,IAAI,iBAAiB,QAAQ,CAAC,aAAa,QAAQ;gBACjD,aAAa,UAAU;YACzB;YACA,IAAI,iBAAiB,QAAQ,CAAC,gBAAgB,WAAW;gBACvD,gBAAgB,aAAa;YAC/B;YACA,IAAI,iBAAiB,QAAQ,CAAC,iBAAiB,YAAY;gBACzD,iBAAiB,cAAc;YACjC;YACA,IAAI,iBAAiB,QAAQ,CAAC,mBAAmB,cAAc;gBAC7D,mBAAmB,gBAAgB;YACrC;YACA,IAAI,iBAAiB,QAAQ,CAAC,eAAe,UAAU;gBACrD,eAAe,YAAY;YAC7B;YACA,IAAI,iBAAiB,QAAQ,CAAC,YAAY,OAAO;gBAC/C,YAAY,SAAS;YACvB;YACA,IAAI,iBAAiB,QAAQ,CAAC,eAAe,UAAU;gBACrD,eAAe,YAAY;YAC7B;YACA,IAAI,iBAAiB,QAAQ,CAAC,aAAa,WAAW,KAAK;gBACzD,aAAa,UAAU;gBACvB,yBAAyB,sBAAsB;YACjD;QACF;IACF,GAAG;QAAC;KAAO,GAAG,uDAAuD;IAErE,mDAAmD;IACnD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;YACX,iCAAiC;YACjC,WAAW,QAAQ,QAAQ,qBAAqB;YAChD,kBAAkB,eAAe;YACjC,eAAe,YAAY;YAC3B,gBAAgB,aAAa;QAC/B;IACF,GAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAa;IAE/D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,MAAM;YACT,0BAA0B;YAC1B,aAAa,UAAU;YACvB,kBAAkB,eAAe;YACjC,eAAe,YAAY;YAC3B,gBAAgB,aAAa;QAC/B;IACF,GAAG;QAAC;QAAM;QAAW;QAAgB;QAAa;KAAa;IAE/D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;YACZ,2BAA2B;YAC3B,YAAY,SAAS;YACrB,mBAAmB,gBAAgB;YACnC,gBAAgB,aAAa;YAC7B,iBAAiB,cAAc;QACjC;IACF,GAAG;QAAC;QAAS;QAAU;QAAiB;QAAc;KAAc;IAEpE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,8BAA8B;YAC9B,WAAW,QAAQ;YACnB,oBAAoB,iBAAiB;YACrC,iBAAiB,cAAc;YAC/B,eAAe,YAAY;QAC7B;IACF,GAAG;QAAC;QAAU;QAAS;QAAkB;QAAe;KAAY;IAEpE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW;YACd,6BAA6B;YAC7B,cAAc,WAAW;YACzB,qBAAqB,kBAAkB;YACvC,kBAAkB,eAAe;YACjC,mBAAmB,gBAAgB;QACrC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,4BAA4B;YAC5B,aAAa,UAAU;YACvB,oBAAoB,iBAAiB;YACrC,iBAAiB,cAAc;YAC/B,kBAAkB,eAAe;QACnC;IACF,GAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;YACX,0BAA0B;YAC1B,WAAW,QAAQ;YACnB,kBAAkB,eAAe;YACjC,eAAe,YAAY;YAC3B,gBAAgB,aAAa;QAC/B;IACF,GAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAa;IAE/D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,4BAA4B;YAC5B,aAAa,UAAU;YACvB,oBAAoB,iBAAiB;YACrC,iBAAiB,cAAc;YAC/B,kBAAkB,eAAe;QACnC;IACF,GAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,4BAA4B;YAC5B,aAAa,UAAU;YACvB,oBAAoB,iBAAiB;YACrC,iBAAiB,cAAc;YAC/B,kBAAkB,eAAe;QACnC;IACF,GAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW;YACd,gCAAgC;YAChC,cAAc,WAAW;YACzB,uBAAuB,oBAAoB;YAC3C,oBAAoB,iBAAiB;YACrC,qBAAqB,kBAAkB;QACzC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW;YACd,6BAA6B;YAC7B,cAAc,WAAW;YACzB,qBAAqB,kBAAkB;YACvC,kBAAkB,eAAe;YACjC,mBAAmB,gBAAgB;QACrC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;YACX,8BAA8B;YAC9B,WAAW,QAAQ;YACnB,kBAAkB,eAAe;YACjC,eAAe,YAAY;YAC3B,oBAAoB,iBAAiB;QACvC;IACF,GAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAiB;IAEnE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,4BAA4B;YAC5B,aAAa,UAAU;YACvB,oBAAoB,iBAAiB;YACrC,iBAAiB,cAAc;YAC/B,kBAAkB,eAAe;QACnC;IACF,GAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW;YACd,gCAAgC;YAChC,cAAc,WAAW;YACzB,qBAAqB,kBAAkB;YACvC,kBAAkB,eAAe;YACjC,mBAAmB,gBAAgB;QACrC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,YAAY;YACf,8BAA8B;YAC9B,eAAe,YAAY;YAC3B,sBAAsB,mBAAmB;YACzC,mBAAmB,gBAAgB;YACnC,oBAAoB,iBAAiB;QACvC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,aAAa;YAChB,kCAAkC;YAClC,gBAAgB,aAAa;YAC7B,uBAAuB,oBAAoB;YAC3C,oBAAoB,iBAAiB;YACrC,qBAAqB,kBAAkB;QACzC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;YACX,0BAA0B;YAC1B,WAAW,QAAQ;YACnB,kBAAkB,eAAe;YACjC,eAAe,YAAY;YAC3B,gBAAgB,aAAa;QAC/B;IACF,GAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAa;IAE/D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;YACZ,iCAAiC;YACjC,YAAY,SAAS;YACrB,mBAAmB,gBAAgB;YACnC,gBAAgB,aAAa;YAC7B,iBAAiB,cAAc;QACjC;IACF,GAAG;QAAC;QAAS;QAAU;QAAiB;QAAc;KAAc;IAEpE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,YAAY;YACf,8BAA8B;YAC9B,eAAe,YAAY;YAC3B,sBAAsB,mBAAmB;YACzC,mBAAmB,gBAAgB;YACnC,oBAAoB,iBAAiB;QACvC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,WAAW;YACd,6BAA6B;YAC7B,cAAc,WAAW;YACzB,mBAAmB,gBAAgB;QACrC;IACF,GAAG;QAAC;QAAW;QAAY;KAAgB;IAE3C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,OAAO;YACV,yBAAyB;YACzB,UAAU,OAAO;YACjB,eAAe,YAAY;QAC7B;IACF,GAAG;QAAC;QAAO;QAAQ;KAAY;IAE/B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,cAAc;YACjB,gCAAgC;YAChC,aAAa,UAAU;YACvB,sBAAsB,mBAAmB;QAC3C;IACF,GAAG;QAAC;QAAc;QAAW;KAAmB;IAEhD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,4BAA4B;YAC5B,aAAa,UAAU;YACvB,kBAAkB,eAAe;QACnC;IACF,GAAG;QAAC;QAAU;QAAW;KAAe;IAExC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,YAAY;YACf,8BAA8B;YAC9B,eAAe,YAAY;YAC3B,oBAAoB,iBAAiB;QACvC;IACF,GAAG;QAAC;QAAY;QAAa;KAAiB;IAE9C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,MAAM;YACT,4BAA4B;YAC5B,SAAS,MAAM;YACf,cAAc,WAAW;QAC3B;IACF,GAAG;QAAC;QAAM;QAAO;KAAW;IAE5B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,MAAM;YACT,gCAAgC;YAChC,SAAS,MAAM;YACf,cAAc,WAAW;QAC3B;IACF,GAAG;QAAC;QAAM;QAAO;KAAW;IAE5B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,SAAS;YACZ,iCAAiC;YACjC,wBAAwB,qBAAqB;YAC7C,6BAA6B,0BAA0B;QACzD;IACF,GAAG;QAAC;QAAS;QAAsB;KAA0B;IAE7D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,MAAM;YACT,6BAA6B;YAC7B,SAAS,MAAM;YACf,cAAc,WAAW;QAC3B;IACF,GAAG;QAAC;QAAM;QAAO;KAAW;IAE5B,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ;YACX,6BAA6B;YAC7B,WAAW,QAAQ;YACnB,gBAAgB,aAAa;QAC/B;IACF,GAAG;QAAC;QAAQ;QAAS;KAAa;IAElC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,UAAU;YACb,4BAA4B;YAC5B,aAAa,UAAU;YACvB,kBAAkB,eAAe;QACnC;IACF,GAAG;QAAC;QAAU;QAAW;KAAe;IAExC,4DAA4D;IAC5D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,QAAQ,QAAQ,yCAAyC;QAE9D,+CAA+C;QAC/C,MAAM,qBAAqB,CAAC;YAC1B,6EAA6E;YAC7E,MAAM,aAAa;gBACjB,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,OAAO;gBACP,UAAU;YACZ;YAEA,0CAA0C;YAC1C,wFAAwF;YACxF,6EAA6E;YAC7E,OAAO;QACT;QAEA,MAAM,kBAAkB,mBAAmB;QAE3C,6BAA6B;QAC7B,aAAa,UAAU,gBAAgB,MAAM;QAC7C,WAAW,QAAQ,gBAAgB,IAAI;QACvC,cAAc,WAAW,gBAAgB,OAAO;QAChD,eAAe,YAAY,gBAAgB,QAAQ;QACnD,gBAAgB,aAAa,gBAAgB,SAAS;QACtD,eAAe,YAAY,gBAAgB,QAAQ;QACnD,aAAa,UAAU,gBAAgB,MAAM;QAC7C,eAAe,YAAY,gBAAgB,QAAQ;QACnD,eAAe,YAAY,gBAAgB,QAAQ;QACnD,gBAAgB,aAAa,gBAAgB,SAAS;QACtD,gBAAgB,aAAa,gBAAgB,SAAS;QACtD,aAAa,UAAU,gBAAgB,MAAM;QAC7C,eAAe,YAAY,gBAAgB,QAAQ;QACnD,gBAAgB,aAAa,gBAAgB,SAAS;QACtD,aAAa,UAAU,gBAAgB,MAAM;QAC7C,cAAc,WAAW,gBAAgB,OAAO;QAChD,iBAAiB,cAAc,gBAAgB,UAAU;QACzD,WAAW,QAAQ,gBAAgB,IAAI;QACvC,WAAW,QAAQ,gBAAgB,IAAI;QACvC,cAAc,WAAW,gBAAgB,OAAO;QAChD,YAAY,SAAS,gBAAgB,KAAK;QAC1C,WAAW,QAAQ,gBAAgB,IAAI;QACvC,aAAa,UAAU,gBAAgB,MAAM;QAC7C,gBAAgB,aAAa,gBAAgB,SAAS;QACtD,iBAAiB,cAAc,gBAAgB,UAAU;QACzD,mBAAmB,gBAAgB,gBAAgB,YAAY;QAC/D,eAAe,YAAY,gBAAgB,QAAQ;QACnD,YAAY,SAAS,gBAAgB,KAAK;QAC1C,eAAe,YAAY,gBAAgB,QAAQ;IACrD,GAAG;QAAC;KAAO,GAAG,+BAA+B;IAE7C,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BAMb,cAAA,8OAAC;oBAAI,WAAU;;sCAOb,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS,WAAW;4BACpB,UAAU,CAAC;gCACT,IAAI,KAAK;oCACP,iCAAiC;oCACjC,UAAU;gCACZ,OAAO;oCACL,6CAA6C;oCAC7C,UAAU;gCACZ;gCACA,sBAAsB;4BACxB;4BACA,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAQtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS,QAAQ;4BACjB,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAGR,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAetC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,8OAAC,iLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;;;;;;;;;;;;0BAM1C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC3B,iCACC,8OAAC,sMAAA,CAAA,UAAiB;wBAChB,cAAc;wBACd,QAAQ,kBAAkB,cAAc;;;;;;oBAG3C,0BAAY,8OAAC,+LAAA,CAAA,UAAU;wBAAC,cAAc;;;;;;oBACtC,2BAAa,8OAAC,gMAAA,CAAA,UAAW;wBAAC,cAAc;;;;;;oBACxC,4BAAc,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC1C,6BAAe,8OAAC,kMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBAC5C,4BAAc,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC1C,wBAAU,8OAAC,+LAAA,CAAA,UAAU;wBAAC,cAAc;;;;;;oBACpC,0BAAY,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACxC,0BAAY,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACxC,2BAAa,8OAAC,oMAAA,CAAA,UAAe;wBAAC,cAAc;;;;;;oBAC5C,2BAAa,8OAAC,kMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBAC1C,wBAAU,8OAAC,mMAAA,CAAA,UAAc;wBAAC,cAAc;;;;;;oBACxC,0BAAY,8OAAC,iMAAA,CAAA,UAAY;wBAAC,MAAK;wBAAS,cAAc;;;;;;oBACtD,2BAAa,8OAAC,oMAAA,CAAA,UAAe;wBAAC,cAAc;;;;;;oBAC5C,wBAAU,8OAAC,+LAAA,CAAA,UAAU;wBAAC,cAAc;;;;;;oBACpC,4BAAc,8OAAC,mMAAA,CAAA,UAAc;wBAAC,cAAc;;;;;;oBAC5C,6BAAe,8OAAC,sMAAA,CAAA,UAAiB;wBAAC,cAAc;;;;;;oBAChD,yBACC,8OAAC,qMAAA,CAAA,UAAgB;wBAAC,MAAK;wBAAS,cAAc;;;;;;oBAE/C,4BACC,8OAAC,mMAAA,CAAA,UAAc;wBAAC,MAAK;wBAAW,cAAc;;;;;;oBAE/C,sBAAQ,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACpC,sBAAQ,8OAAC,qMAAA,CAAA,UAAgB;wBAAC,cAAc;;;;;;oBACxC,yBAAW,8OAAC,sMAAA,CAAA,UAAiB;wBAAC,cAAc;;;;;;oBAC5C,uBAAS,8OAAC,oMAAA,CAAA,UAAqB;wBAAC,cAAc;;;;;;oBAC9C,sBAAQ,8OAAC,kMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBACrC,wBAAU,8OAAC,kMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBACvC,2BAAa,8OAAC,kMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBAC1C,4BAAc,8OAAC,mMAAA,CAAA,UAAc;wBAAC,cAAc;;;;;;oBAC5C,8BAAgB,8OAAC,qMAAA,CAAA,UAAgB;wBAAC,cAAc;;;;;;oBAChD,0BAAY,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACxC,uBAAS,8OAAC,8LAAA,CAAA,UAAS;wBAAC,cAAc;;;;;;oBAClC,0BAAY,8OAAC,iMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;;;;;;;;;AAIjD;uCAEe", "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/QueryButtons.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, ButtonGroup, Card, CardBody } from \"@heroui/react\";\r\nimport {\r\n  Play,\r\n  Download,\r\n  RefreshCw,\r\n  FileText,\r\n  Save,\r\n  MessageCircleHeart,\r\n} from \"lucide-react\";\r\n\r\nconst QueryButtons = ({\r\n  onExecuteQuery,\r\n  onExportExcel,\r\n  onExportCSV,\r\n  onExportPDF,\r\n  onReset,\r\n  onSaveQuery,\r\n  onShowSQL,\r\n  isLoading,\r\n}) => {\r\n  return (\r\n    <Card className=\"mb-4 shadow-none bg-transparent\">\r\n      <CardBody>\r\n        <div className=\"flex flex-wrap gap-6 justify-center md:justify-center\">\r\n          <Button\r\n            color=\"primary\"\r\n            startContent={<Play size={16} />}\r\n            onClick={onExecuteQuery}\r\n            isLoading={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Tayang Data\r\n          </Button>\r\n          <Button\r\n            color=\"danger\"\r\n            variant=\"ghost\"\r\n            startContent={<RefreshCw size={16} />}\r\n            onClick={onReset}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Reset Filter\r\n          </Button>\r\n          <ButtonGroup>\r\n            <Button\r\n              color=\"success\"\r\n              variant=\"flat\"\r\n              startContent={<Download size={16} />}\r\n              onClick={onExportExcel}\r\n              isDisabled={isLoading}\r\n              className=\"w-[120px] h-[50px]\"\r\n            >\r\n              Excel\r\n            </Button>\r\n\r\n            <Button\r\n              color=\"secondary\"\r\n              variant=\"flat\"\r\n              startContent={<Download size={16} />}\r\n              onClick={onExportCSV}\r\n              isDisabled={isLoading}\r\n              className=\"w-[120px] h-[50px]\"\r\n            >\r\n              CSV\r\n            </Button>\r\n          </ButtonGroup>\r\n          <Button\r\n            color=\"success\"\r\n            variant=\"flat\"\r\n            startContent={<MessageCircleHeart size={16} />}\r\n            onClick={onExportPDF}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Kirim WA\r\n          </Button>\r\n\r\n          <Button\r\n            color=\"warning\"\r\n            variant=\"flat\"\r\n            startContent={<Save size={16} />}\r\n            onClick={onSaveQuery}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Simpan Query\r\n          </Button>\r\n\r\n          <Button\r\n            color=\"default\"\r\n            variant=\"flat\"\r\n            startContent={<FileText size={16} />} // You can use a different icon if desired\r\n            onClick={onShowSQL}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Tayang SQL\r\n          </Button>\r\n        </div>\r\n      </CardBody>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default QueryButtons;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AASA,MAAM,eAAe,CAAC,EACpB,cAAc,EACd,aAAa,EACb,WAAW,EACX,WAAW,EACX,OAAO,EACP,WAAW,EACX,SAAS,EACT,SAAS,EACV;IACC,qBACE,8OAAC,sMAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,+MAAA,CAAA,WAAQ;sBACP,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,4BAAc,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAC1B,SAAS;wBACT,WAAW;wBACX,WAAU;kCACX;;;;;;kCAGD,8OAAC,4MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,8OAAC,gNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;wBAC/B,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;kCAGD,8OAAC,uNAAA,CAAA,cAAW;;0CACV,8OAAC,4MAAA,CAAA,SAAM;gCACL,OAAM;gCACN,SAAQ;gCACR,4BAAc,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;gCAC9B,SAAS;gCACT,YAAY;gCACZ,WAAU;0CACX;;;;;;0CAID,8OAAC,4MAAA,CAAA,SAAM;gCACL,OAAM;gCACN,SAAQ;gCACR,4BAAc,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;gCAC9B,SAAS;gCACT,YAAY;gCACZ,WAAU;0CACX;;;;;;;;;;;;kCAIH,8OAAC,4MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,8OAAC,sOAAA,CAAA,qBAAkB;4BAAC,MAAM;;;;;;wBACxC,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;kCAID,8OAAC,4MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAC1B,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;kCAID,8OAAC,4MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;wBAC9B,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/LaporanSelector.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card, CardBody, Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst ReportTypeSelector = ({ inquiryState, onFilterChange }) => {\r\n  // --- DESTRUCTURE STATE FROM INQUIRY STATE ---\r\n  const {\r\n    thang,\r\n    setThang,\r\n    jenlap,\r\n    setJenlap,\r\n    pembulatan,\r\n    setPembulatan,\r\n    akumulatif,\r\n    setAkumulatif,\r\n  } = inquiryState || {};\r\n\r\n  // --- FALLBACK STATE FOR BACKWARDS COMPATIBILITY ---\r\n  const [localThang, setLocalThang] = React.useState(\"2025\");\r\n  const [localJenlap, setLocalJenlap] = React.useState(\"2\");\r\n  const [localPembulatan, setLocalPembulatan] = React.useState(\"1\");\r\n  const [localAkumulatif, setLocalAkumulatif] = React.useState(\"0\");\r\n\r\n  // Use inquiryState values if available, otherwise use local state\r\n  const currentThang =\r\n    thang !== undefined && thang !== null ? thang : localThang;\r\n  const currentJenlap =\r\n    jenlap !== undefined && jenlap !== null ? jenlap : localJenlap;\r\n  const currentPembulatan =\r\n    pembulatan !== undefined && pembulatan !== null\r\n      ? pembulatan\r\n      : localPembulatan;\r\n  const currentAkumulatif =\r\n    akumulatif !== undefined && akumulatif !== null\r\n      ? akumulatif\r\n      : localAkumulatif;\r\n\r\n  const currentSetThang = setThang || setLocalThang;\r\n  const currentSetJenlap = setJenlap || setLocalJenlap;\r\n  const currentSetPembulatan = setPembulatan || setLocalPembulatan;\r\n  const currentSetAkumulatif = setAkumulatif || setLocalAkumulatif;\r\n\r\n  // Debug akumulatif value changes\r\n  React.useEffect(() => {\r\n    console.log(\r\n      \"LaporanSelector - currentAkumulatif changed to:\",\r\n      currentAkumulatif\r\n    );\r\n  }, [currentAkumulatif]);\r\n\r\n  // This effect reports changes up to the parent component if callback is provided\r\n  React.useEffect(() => {\r\n    if (onFilterChange) {\r\n      onFilterChange({\r\n        thang: currentThang,\r\n        jenlap: currentJenlap,\r\n        pembulatan: currentPembulatan,\r\n        akumulatif: currentAkumulatif,\r\n      });\r\n    }\r\n  }, [\r\n    currentThang,\r\n    currentJenlap,\r\n    currentPembulatan,\r\n    currentAkumulatif,\r\n    onFilterChange,\r\n  ]);\r\n\r\n  // --- LOGIC FOR CONDITIONAL \"AKUMULATIF\" ---\r\n  const isAkumulatifActive = currentJenlap === \"3\";\r\n\r\n  React.useEffect(() => {\r\n    console.log(\"LaporanSelector - akumulatif useEffect triggered:\", {\r\n      currentJenlap,\r\n      isAkumulatifActive,\r\n      currentAkumulatif,\r\n    });\r\n\r\n    if (!isAkumulatifActive) {\r\n      // When akumulatif is not active (jenlap !== \"3\"), set to \"0\" (Non-Akumulatif)\r\n\r\n      currentSetAkumulatif(\"0\");\r\n    } else {\r\n      // When akumulatif becomes active (jenlap === \"3\"), ensure it has a valid value\r\n      // If currentAkumulatif is empty or undefined, default to \"0\" (Non-Akumulatif)\r\n      if (\r\n        !currentAkumulatif ||\r\n        (currentAkumulatif !== \"0\" && currentAkumulatif !== \"1\")\r\n      ) {\r\n        currentSetAkumulatif(\"0\");\r\n      } else {\r\n      }\r\n    }\r\n  }, [currentJenlap, isAkumulatifActive]); // Remove currentSetAkumulatif from dependencies\r\n\r\n  // Available years for selection\r\n  const Tahun = [\r\n    \"2025\",\r\n    \"2024\",\r\n    \"2023\",\r\n    \"2022\",\r\n    \"2021\",\r\n    \"2020\",\r\n    \"2019\",\r\n    \"2018\",\r\n    \"2017\",\r\n    \"2016\",\r\n  ];\r\n\r\n  // Report types (match old form)\r\n  const jenlapOpt = [\r\n    { value: \"1\", label: \"Pagu APBN\" },\r\n    { value: \"2\", label: \"Pagu Realisasi\" },\r\n    { value: \"3\", label: \"Pagu Realisasi Bulanan\" },\r\n    { value: \"4\", label: \"Pergerakan Pagu Bulanan\" },\r\n    { value: \"5\", label: \"Pergerakan Blokir Bulanan\" },\r\n    { value: \"7\", label: \"Pergerakan Blokir Bulanan per Jenis\" },\r\n    { value: \"6\", label: \"Volume Output Kegiatan (PN) - Data Caput\" },\r\n  ];\r\n\r\n  // Akumulatif options\r\n  const akumulatifOpt = [\r\n    { value: \"1\", label: \"Akumulatif\" },\r\n    { value: \"0\", label: \"Non-Akumulatif\" },\r\n  ];\r\n\r\n  // Rounding options\r\n  const pembulatanOpt = [\r\n    { value: \"1\", label: \"Rupiah\" },\r\n    { value: \"1000\", label: \"Ribuan\" },\r\n    { value: \"1000000\", label: \"Jutaan\" },\r\n    { value: \"1000000000\", label: \"Miliar\" },\r\n    { value: \"1000000000000\", label: \"Triliun\" },\r\n  ];\r\n\r\n  const handleSelectionChange = (setter) => (keys) => {\r\n    const value = Array.from(keys)[0];\r\n\r\n    if (setter && value !== undefined) setter(value);\r\n  };\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <div className=\"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl\">\r\n        {/* <h5 className=\"text-lg font-semibold mb-4\">Report Settings</h5> */}\r\n\r\n        <div className=\"flex flex-col md:flex-row gap-6 w-full\">\r\n          {/* Year Selection */}\r\n          <div className=\"flex-1\">\r\n            <label id=\"thang-label\" className=\"block text-sm font-medium mb-2\">\r\n              Tahun Anggaran\r\n            </label>\r\n            <Select\r\n              selectedKeys={[currentThang]}\r\n              onSelectionChange={handleSelectionChange(currentSetThang)}\r\n              className=\"w-full\"\r\n              placeholder=\"Pilih Tahun\"\r\n              disallowEmptySelection // Prevent unselecting\r\n              aria-labelledby=\"thang-label\"\r\n              aria-label=\"Pilih Tahun Anggaran\"\r\n            >\r\n              {Tahun.map((year) => (\r\n                <SelectItem key={year} textValue={year}>\r\n                  {year}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Report Type Selection */}\r\n          <div className=\"flex-[1.5]\">\r\n            <label id=\"jenlap-label\" className=\"block text-sm font-medium mb-2\">\r\n              Jenis Laporan\r\n            </label>\r\n            <Select\r\n              selectedKeys={[currentJenlap]}\r\n              onSelectionChange={handleSelectionChange(currentSetJenlap)}\r\n              className=\"w-full\"\r\n              placeholder=\"Pilih Jenis Laporan\"\r\n              disallowEmptySelection // Prevent unselecting\r\n              aria-labelledby=\"jenlap-label\"\r\n              aria-label=\"Pilih Jenis Laporan\"\r\n            >\r\n              {jenlapOpt.map((type) => (\r\n                <SelectItem key={type.value} textValue={type.label}>\r\n                  {type.label}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Akumulatif Selection */}\r\n          <div className=\"flex-[0.5] min-w-[120px]\">\r\n            <label\r\n              id=\"akumulatif-label\"\r\n              className=\"block text-sm font-medium mb-2\"\r\n            >\r\n              Akumulatif\r\n            </label>\r\n            <Select\r\n              selectedKeys={isAkumulatifActive ? [currentAkumulatif] : []}\r\n              onSelectionChange={handleSelectionChange(currentSetAkumulatif)}\r\n              className=\"w-full\"\r\n              placeholder={isAkumulatifActive ? \"Pilih Akumulatif\" : \"Disabled\"}\r\n              isDisabled={!isAkumulatifActive}\r\n              disallowEmptySelection // This is now always true when active\r\n              aria-labelledby=\"akumulatif-label\"\r\n              aria-label=\"Pilih Akumulatif\"\r\n            >\r\n              {akumulatifOpt.map((option) => (\r\n                <SelectItem key={option.value} textValue={option.label}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Rounding Options as Dropdown */}\r\n          <div className=\"flex-1\">\r\n            <label\r\n              id=\"pembulatan-label\"\r\n              className=\"block text-sm font-medium mb-2\"\r\n            >\r\n              Pembulatan\r\n            </label>\r\n            <Select\r\n              selectedKeys={[currentPembulatan]}\r\n              onSelectionChange={handleSelectionChange(currentSetPembulatan)}\r\n              className=\"w-full\"\r\n              placeholder=\"Pilih Pembulatan\"\r\n              disallowEmptySelection // Prevent unselecting\r\n              aria-labelledby=\"pembulatan-label\"\r\n              aria-label=\"Pilih Pembulatan\"\r\n            >\r\n              {pembulatanOpt.map((option) => (\r\n                <SelectItem key={option.value} textValue={option.label}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportTypeSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEA,MAAM,qBAAqB,CAAC,EAAE,YAAY,EAAE,cAAc,EAAE;IAC1D,+CAA+C;IAC/C,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,MAAM,EACN,SAAS,EACT,UAAU,EACV,aAAa,EACb,UAAU,EACV,aAAa,EACd,GAAG,gBAAgB,CAAC;IAErB,qDAAqD;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7D,kEAAkE;IAClE,MAAM,eACJ,UAAU,aAAa,UAAU,OAAO,QAAQ;IAClD,MAAM,gBACJ,WAAW,aAAa,WAAW,OAAO,SAAS;IACrD,MAAM,oBACJ,eAAe,aAAa,eAAe,OACvC,aACA;IACN,MAAM,oBACJ,eAAe,aAAa,eAAe,OACvC,aACA;IAEN,MAAM,kBAAkB,YAAY;IACpC,MAAM,mBAAmB,aAAa;IACtC,MAAM,uBAAuB,iBAAiB;IAC9C,MAAM,uBAAuB,iBAAiB;IAE9C,iCAAiC;IACjC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,QAAQ,GAAG,CACT,mDACA;IAEJ,GAAG;QAAC;KAAkB;IAEtB,iFAAiF;IACjF,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,gBAAgB;YAClB,eAAe;gBACb,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,YAAY;YACd;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6CAA6C;IAC7C,MAAM,qBAAqB,kBAAkB;IAE7C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,QAAQ,GAAG,CAAC,qDAAqD;YAC/D;YACA;YACA;QACF;QAEA,IAAI,CAAC,oBAAoB;YACvB,8EAA8E;YAE9E,qBAAqB;QACvB,OAAO;YACL,+EAA+E;YAC/E,8EAA8E;YAC9E,IACE,CAAC,qBACA,sBAAsB,OAAO,sBAAsB,KACpD;gBACA,qBAAqB;YACvB,OAAO,CACP;QACF;IACF,GAAG;QAAC;QAAe;KAAmB,GAAG,gDAAgD;IAEzF,gCAAgC;IAChC,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gCAAgC;IAChC,MAAM,YAAY;QAChB;YAAE,OAAO;YAAK,OAAO;QAAY;QACjC;YAAE,OAAO;YAAK,OAAO;QAAiB;QACtC;YAAE,OAAO;YAAK,OAAO;QAAyB;QAC9C;YAAE,OAAO;YAAK,OAAO;QAA0B;QAC/C;YAAE,OAAO;YAAK,OAAO;QAA4B;QACjD;YAAE,OAAO;YAAK,OAAO;QAAsC;QAC3D;YAAE,OAAO;YAAK,OAAO;QAA2C;KACjE;IAED,qBAAqB;IACrB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAa;QAClC;YAAE,OAAO;YAAK,OAAO;QAAiB;KACvC;IAED,mBAAmB;IACnB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAQ,OAAO;QAAS;QACjC;YAAE,OAAO;YAAW,OAAO;QAAS;QACpC;YAAE,OAAO;YAAc,OAAO;QAAS;QACvC;YAAE,OAAO;YAAiB,OAAO;QAAU;KAC5C;IAED,MAAM,wBAAwB,CAAC,SAAW,CAAC;YACzC,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YAEjC,IAAI,UAAU,UAAU,WAAW,OAAO;QAC5C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBAGb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,IAAG;gCAAc,WAAU;0CAAiC;;;;;;0CAGnE,8OAAC,4MAAA,CAAA,SAAM;gCACL,cAAc;oCAAC;iCAAa;gCAC5B,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAY;gCACZ,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4NAAA,CAAA,aAAU;wCAAY,WAAW;kDAC/B;uCADc;;;;;;;;;;;;;;;;kCAQvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,IAAG;gCAAe,WAAU;0CAAiC;;;;;;0CAGpE,8OAAC,4MAAA,CAAA,SAAM;gCACL,cAAc;oCAAC;iCAAc;gCAC7B,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAY;gCACZ,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,4NAAA,CAAA,aAAU;wCAAkB,WAAW,KAAK,KAAK;kDAC/C,KAAK,KAAK;uCADI,KAAK,KAAK;;;;;;;;;;;;;;;;kCAQjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,IAAG;gCACH,WAAU;0CACX;;;;;;0CAGD,8OAAC,4MAAA,CAAA,SAAM;gCACL,cAAc,qBAAqB;oCAAC;iCAAkB,GAAG,EAAE;gCAC3D,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAa,qBAAqB,qBAAqB;gCACvD,YAAY,CAAC;gCACb,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,4NAAA,CAAA,aAAU;wCAAoB,WAAW,OAAO,KAAK;kDACnD,OAAO,KAAK;uCADE,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,IAAG;gCACH,WAAU;0CACX;;;;;;0CAGD,8OAAC,4MAAA,CAAA,SAAM;gCACL,cAAc;oCAAC;iCAAkB;gCACjC,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAY;gCACZ,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,4NAAA,CAAA,aAAU;wCAAoB,WAAW,OAAO,KAAK;kDACnD,OAAO,KAAK;uCADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C;uCAEe", "debugId": null}}]}