/* [project]/src/components/features/reference/style.css [app-client] (css) */
.jenis-laporan-option input[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  border: 2px solid #fff;
  border-radius: 50%;
  outline: none;
  width: 15px;
  height: 15px;
  margin: 0;
}

.jenis-laporan-option input[type="radio"]:checked {
  background-color: #fff;
}

.jenis-laporan-option input[type="radio"]:disabled {
  cursor: not-allowed;
  background-color: #eb0606;
  border: 2px solid #fff;
}

.jenis-laporan-option-tematik input[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  border: 2px solid #fff;
  border-radius: 50%;
  outline: none;
  width: 15px;
  height: 15px;
  margin: 0;
}

.jenis-laporan-option-tematik input[type="radio"]:checked {
  background-color: #fff;
}

.jenis-laporan-option-tematik input[type="radio"]:disabled {
  cursor: not-allowed;
  background-color: #eb0606;
  border: 2px solid #fff;
}

.jenis-laporan-option p, .jenis-laporan-option-tematik p {
  color: #fff;
}

/*# sourceMappingURL=src_components_features_reference_style_aa52066a.css.map*/