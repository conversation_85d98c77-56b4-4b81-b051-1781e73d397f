{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/Omspan.jsx"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\n\r\nconst showToast = (pesan) => {\r\n  Swal.close(); // Menutup notifikasi yang sedang ditampilkan\r\n\r\n  setTimeout(() => {\r\n    Swal.fire({\r\n      text: `${pesan} `,\r\n      position: \"top-end\",\r\n      showConfirmButton: false,\r\n      toast: true,\r\n      timer: 5000,\r\n      background: \"black\",\r\n      color: \"#ffffff\",\r\n      showClass: {\r\n        popup: \"animate__animated \",\r\n      },\r\n    });\r\n  }, 500); // Menunggu 500ms sebelum menampilkan notifikasi baru\r\n};\r\nconst NotifikasiToast = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} 👋`,\r\n    position: \"top-end\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 3000,\r\n    background: \"#C16DFA\",\r\n    color: \"#ffffff\",\r\n  });\r\n};\r\n\r\nconst NotifikasiToastEPA = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} `,\r\n    position: \"top-start\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 3000,\r\n    background: \"#17c3fa\",\r\n    color: \"#ffffff\",\r\n  });\r\n};\r\n\r\nexport const Toast = Swal.mixin({\r\n  toast: true,\r\n  position: \"top-start\",\r\n  showConfirmButton: false,\r\n  timer: 3000,\r\n  timerProgressBar: true,\r\n  customClass: {\r\n    popup: \"custom-toast-font custom-toast-primary-light\",\r\n  },\r\n  didOpen: (toast) => {\r\n    toast.onmouseenter = Swal.stopTimer;\r\n    toast.onmouseleave = Swal.resumeTimer;\r\n  },\r\n});\r\n\r\nconst NotifikasiDisclaimer = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} `,\r\n    position: \"top-start\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 5000,\r\n    background: \"#FF5733\",\r\n    color: \"#ffffff\",\r\n    showCloseButton: true,\r\n  });\r\n};\r\n\r\nconst UserLogin = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} `,\r\n    position: \"bottom-start\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 3000,\r\n    background: \"#FF5733\",\r\n    color: \"#ffffff\",\r\n    showCloseButton: true,\r\n    animation: false,\r\n  });\r\n};\r\n\r\nexport const Omspan = (username, message) => {\r\n  showToast(username, message);\r\n};\r\n\r\nexport const Tunda = (username) => {\r\n  showToast(username, \"Tunda OMSPAN berhasil\");\r\n};\r\n\r\nexport const Pesan = (pesan) => {\r\n  showToast(pesan);\r\n};\r\nexport const NotifPesan = (pesan) => {\r\n  NotifikasiToast(pesan);\r\n};\r\n\r\nexport const NotifDisclaimer = (pesan) => {\r\n  NotifikasiDisclaimer(pesan);\r\n};\r\n\r\n// export const ToastUserLogin = (pesan) => {\r\n//   UserLogin(pesan);\r\n// };\r\n\r\nexport const EPANOTIF = (pesan) => {\r\n  NotifikasiToastEPA(pesan);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,MAAM,YAAY,CAAC;IACjB,4JAAA,CAAA,UAAI,CAAC,KAAK,IAAI,6CAA6C;IAE3D,WAAW;QACT,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACR,MAAM,AAAC,GAAQ,OAAN,OAAM;YACf,UAAU;YACV,mBAAmB;YACnB,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,WAAW;gBACT,OAAO;YACT;QACF;IACF,GAAG,MAAM,qDAAqD;AAChE;AACA,MAAM,kBAAkB,OAAO;IAC7B,MAAM,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,AAAC,GAAQ,OAAN,OAAM;QACf,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;AACF;KAVM;AAYN,MAAM,qBAAqB,OAAO;IAChC,MAAM,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,AAAC,GAAQ,OAAN,OAAM;QACf,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;AACF;MAVM;AAYC,MAAM,QAAQ,4JAAA,CAAA,UAAI,CAAC,KAAK,CAAC;IAC9B,OAAO;IACP,UAAU;IACV,mBAAmB;IACnB,OAAO;IACP,kBAAkB;IAClB,aAAa;QACX,OAAO;IACT;IACA,SAAS,CAAC;QACR,MAAM,YAAY,GAAG,4JAAA,CAAA,UAAI,CAAC,SAAS;QACnC,MAAM,YAAY,GAAG,4JAAA,CAAA,UAAI,CAAC,WAAW;IACvC;AACF;AAEA,MAAM,uBAAuB,OAAO;IAClC,MAAM,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,AAAC,GAAQ,OAAN,OAAM;QACf,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;QACP,iBAAiB;IACnB;AACF;MAXM;AAaN,MAAM,YAAY,OAAO;IACvB,MAAM,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,AAAC,GAAQ,OAAN,OAAM;QACf,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;QACP,iBAAiB;QACjB,WAAW;IACb;AACF;MAZM;AAcC,MAAM,SAAS,CAAC,UAAU;IAC/B,UAAU,UAAU;AACtB;MAFa;AAIN,MAAM,QAAQ,CAAC;IACpB,UAAU,UAAU;AACtB;MAFa;AAIN,MAAM,QAAQ,CAAC;IACpB,UAAU;AACZ;MAFa;AAGN,MAAM,aAAa,CAAC;IACzB,gBAAgB;AAClB;MAFa;AAIN,MAAM,kBAAkB,CAAC;IAC9B,qBAAqB;AACvB;MAFa;AAQN,MAAM,WAAW,CAAC;IACvB,mBAAmB;AACrB;MAFa", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/toastError.jsx"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\n\r\n// Fungsi untuk menampilkan pesan toast dengan SweetAlert2\r\nconst ToastError = (title, text, icon = \"error\") => {\r\n  Swal.fire({\r\n    title,\r\n    text,\r\n    icon,\r\n    position: \"top-end\", // Menentukan posisi di atas sebelah kanan\r\n    toast: true,\r\n    showConfirmButton: false, // Tidak menampilkan tombol OK\r\n    timer: 5000,\r\n    showCloseButton: true,\r\n    background: \"red\",\r\n    color: \"white\",\r\n    // color: \"#716add\",\r\n    // customClass: {\r\n    //   container: \"toast-container\",\r\n    //   popup: \"colored-toast\",\r\n    // },\r\n    timerProgressBar: true,\r\n  });\r\n};\r\n\r\n// Fungsi untuk menampilkan pesan error berdasarkan kode status HTTP\r\nconst handleHttpError = (status, text) => {\r\n  switch (status) {\r\n    case 400:\r\n      ToastError(`<PERSON><PERSON>ahan <PERSON>, Permintaan tidak valid. (${text})`);\r\n      break;\r\n    case 401:\r\n      ToastError(\r\n        `Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (${text})`\r\n      );\r\n      break;\r\n    case 403:\r\n      ToastError(`<PERSON><PERSON><PERSON>, <PERSON>ks<PERSON> ke sumber daya dilarang. (${text})`);\r\n      break;\r\n    case 404:\r\n      ToastError(`Error Refresh Token. Silahkan Login Ulang... (${text})`);\r\n      break;\r\n    case 429:\r\n      ToastError(\r\n        `Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${text})`\r\n      );\r\n      break;\r\n    case 422:\r\n      ToastError(\r\n        `Unprocessable Entity, Permintaan tidak dapat diolah. (${text})`\r\n      );\r\n      break;\r\n    case 500:\r\n      ToastError(\"Kesalahan Pada Query\", text);\r\n      break;\r\n    case 503:\r\n      ToastError(\r\n        `Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${text})`\r\n      );\r\n      break;\r\n    case 504:\r\n      ToastError(`Waktu Habis, Permintaan waktu habis. (${text})`);\r\n      break;\r\n    case 505:\r\n      ToastError(\r\n        `Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${text})`\r\n      );\r\n      break;\r\n    case 507:\r\n      ToastError(\r\n        `Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${text})`\r\n      );\r\n      break;\r\n    case 511:\r\n      ToastError(`Autentikasi Diperlukan, Autentikasi diperlukan. (${text})`);\r\n      break;\r\n    default:\r\n      ToastError(`Kesalahan Server, ${text} `);\r\n      break;\r\n  }\r\n};\r\n\r\nexport { ToastError, handleHttpError };\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,0DAA0D;AAC1D,MAAM,aAAa,SAAC,OAAO;QAAM,wEAAO;IACtC,4JAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACR;QACA;QACA;QACA,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,OAAO;QACP,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,oBAAoB;QACpB,iBAAiB;QACjB,kCAAkC;QAClC,4BAA4B;QAC5B,KAAK;QACL,kBAAkB;IACpB;AACF;KAnBM;AAqBN,oEAAoE;AACpE,MAAM,kBAAkB,CAAC,QAAQ;IAC/B,OAAQ;QACN,KAAK;YACH,WAAW,AAAC,kDAAsD,OAAL,MAAK;YAClE;QACF,KAAK;YACH,WACE,AAAC,6DAAiE,OAAL,MAAK;YAEpE;QACF,KAAK;YACH,WAAW,AAAC,kDAAsD,OAAL,MAAK;YAClE;QACF,KAAK;YACH,WAAW,AAAC,iDAAqD,OAAL,MAAK;YACjE;QACF,KAAK;YACH,WACE,AAAC,qEAAyE,OAAL,MAAK;YAE5E;QACF,KAAK;YACH,WACE,AAAC,yDAA6D,OAAL,MAAK;YAEhE;QACF,KAAK;YACH,WAAW,wBAAwB;YACnC;QACF,KAAK;YACH,WACE,AAAC,6DAAiE,OAAL,MAAK;YAEpE;QACF,KAAK;YACH,WAAW,AAAC,yCAA6C,OAAL,MAAK;YACzD;QACF,KAAK;YACH,WACE,AAAC,0DAA8D,OAAL,MAAK;YAEjE;QACF,KAAK;YACH,WACE,AAAC,0DAA8D,OAAL,MAAK;YAEjE;QACF,KAAK;YACH,WAAW,AAAC,oDAAwD,OAAL,MAAK;YACpE;QACF;YACE,WAAW,AAAC,qBAAyB,OAAL,MAAK;YACrC;IACJ;AACF", "debugId": null}}]}