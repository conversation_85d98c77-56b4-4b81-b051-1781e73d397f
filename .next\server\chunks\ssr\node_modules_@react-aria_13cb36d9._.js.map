{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/ariaHideOutside.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/ariaHideOutside.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerWindow} from '@react-aria/utils';\nconst supportsInert = typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;\n\ninterface AriaHideOutsideOptions {\n  root?: Element,\n  shouldUseInert?: boolean\n}\n\n// Keeps a ref count of all hidden elements. Added to when hiding an element, and\n// subtracted from when showing it again. When it reaches zero, aria-hidden is removed.\nlet refCountMap = new WeakMap<Element, number>();\ninterface ObserverWrapper {\n  visibleNodes: Set<Element>,\n  hiddenNodes: Set<Element>,\n  observe: () => void,\n  disconnect: () => void\n}\nlet observerStack: Array<ObserverWrapper> = [];\n\n/**\n * Hides all elements in the DOM outside the given targets from screen readers using aria-hidden,\n * and returns a function to revert these changes. In addition, changes to the DOM are watched\n * and new elements outside the targets are automatically hidden.\n * @param targets - The elements that should remain visible.\n * @param root - Nothing will be hidden above this element.\n * @returns - A function to restore all hidden elements.\n */\nexport function ariaHideOutside(targets: Element[], options?: AriaHideOutsideOptions | Element) {\n  let windowObj = getOwnerWindow(targets?.[0]);\n  let opts = options instanceof windowObj.Element ? {root: options} : options;\n  let root = opts?.root ?? document.body;\n  let shouldUseInert = opts?.shouldUseInert && supportsInert;\n  let visibleNodes = new Set<Element>(targets);\n  let hiddenNodes = new Set<Element>();\n\n  let getHidden = (element: Element) => {\n    return shouldUseInert && element instanceof windowObj.HTMLElement ? element.inert : element.getAttribute('aria-hidden') === 'true';\n  };\n\n  let setHidden = (element: Element, hidden: boolean) => {\n    if (shouldUseInert && element instanceof windowObj.HTMLElement) {\n      element.inert = hidden;\n    } else if (hidden) {\n      element.setAttribute('aria-hidden', 'true');\n    } else {\n      element.removeAttribute('aria-hidden');\n    }\n  };\n\n  let walk = (root: Element) => {\n    // Keep live announcer and top layer elements (e.g. toasts) visible.\n    for (let element of root.querySelectorAll('[data-live-announcer], [data-react-aria-top-layer]')) {\n      visibleNodes.add(element);\n    }\n\n    let acceptNode = (node: Element) => {\n      // Skip this node and its children if it is one of the target nodes, or a live announcer.\n      // Also skip children of already hidden nodes, as aria-hidden is recursive. An exception is\n      // made for elements with role=\"row\" since VoiceOver on iOS has issues hiding elements with role=\"row\".\n      // For that case we want to hide the cells inside as well (https://bugs.webkit.org/show_bug.cgi?id=222623).\n      if (\n        hiddenNodes.has(node) ||\n        visibleNodes.has(node) ||\n        (node.parentElement && hiddenNodes.has(node.parentElement) && node.parentElement.getAttribute('role') !== 'row')\n      ) {\n        return NodeFilter.FILTER_REJECT;\n      }\n\n      // Skip this node but continue to children if one of the targets is inside the node.\n      for (let target of visibleNodes) {\n        if (node.contains(target)) {\n          return NodeFilter.FILTER_SKIP;\n        }\n      }\n\n      return NodeFilter.FILTER_ACCEPT;\n    };\n\n    let walker = document.createTreeWalker(\n      root,\n      NodeFilter.SHOW_ELEMENT,\n      {acceptNode}\n    );\n\n    // TreeWalker does not include the root.\n    let acceptRoot = acceptNode(root);\n    if (acceptRoot === NodeFilter.FILTER_ACCEPT) {\n      hide(root);\n    }\n\n    if (acceptRoot !== NodeFilter.FILTER_REJECT) {\n      let node = walker.nextNode() as Element;\n      while (node != null) {\n        hide(node);\n        node = walker.nextNode() as Element;\n      }\n    }\n  };\n\n  let hide = (node: Element) => {\n    let refCount = refCountMap.get(node) ?? 0;\n\n    // If already aria-hidden, and the ref count is zero, then this element\n    // was already hidden and there's nothing for us to do.\n    if (getHidden(node) && refCount === 0) {\n      return;\n    }\n\n    if (refCount === 0) {\n      setHidden(node, true);\n    }\n\n    hiddenNodes.add(node);\n    refCountMap.set(node, refCount + 1);\n  };\n\n  // If there is already a MutationObserver listening from a previous call,\n  // disconnect it so the new on takes over.\n  if (observerStack.length) {\n    observerStack[observerStack.length - 1].disconnect();\n  }\n\n  walk(root);\n\n  let observer = new MutationObserver(changes => {\n    for (let change of changes) {\n      if (change.type !== 'childList') {\n        continue;\n      }\n\n      // If the parent element of the added nodes is not within one of the targets,\n      // and not already inside a hidden node, hide all of the new children.\n      if (![...visibleNodes, ...hiddenNodes].some(node => node.contains(change.target))) {\n        for (let node of change.addedNodes) {\n          if (\n            (node instanceof HTMLElement || node instanceof SVGElement) &&\n            (node.dataset.liveAnnouncer === 'true' || node.dataset.reactAriaTopLayer === 'true')\n          ) {\n            visibleNodes.add(node);\n          } else if (node instanceof Element) {\n            walk(node);\n          }\n        }\n      }\n    }\n  });\n\n  observer.observe(root, {childList: true, subtree: true});\n\n  let observerWrapper: ObserverWrapper = {\n    visibleNodes,\n    hiddenNodes,\n    observe() {\n      observer.observe(root, {childList: true, subtree: true});\n    },\n    disconnect() {\n      observer.disconnect();\n    }\n  };\n\n  observerStack.push(observerWrapper);\n\n  return (): void => {\n    observer.disconnect();\n\n    for (let node of hiddenNodes) {\n      let count = refCountMap.get(node);\n      if (count == null) {\n        continue;\n      }\n      if (count === 1) {\n        setHidden(node, false);\n        refCountMap.delete(node);\n      } else {\n        refCountMap.set(node, count - 1);\n      }\n    }\n\n    // Remove this observer from the stack, and start the previous one.\n    if (observerWrapper === observerStack[observerStack.length - 1]) {\n      observerStack.pop();\n      if (observerStack.length) {\n        observerStack[observerStack.length - 1].observe();\n      }\n    } else {\n      observerStack.splice(observerStack.indexOf(observerWrapper), 1);\n    }\n  };\n}\n\nexport function keepVisible(element: Element): (() => void) | undefined {\n  let observer = observerStack[observerStack.length - 1];\n  if (observer && !observer.visibleNodes.has(element)) {\n    observer.visibleNodes.add(element);\n    return () => {\n      observer.visibleNodes.delete(element);\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAGD,MAAM,sCAAgB,OAAO,gBAAgB,eAAe,WAAW,YAAY,SAAS;AAO5F,iFAAiF;AACjF,uFAAuF;AACvF,IAAI,oCAAc,IAAI;AAOtB,IAAI,sCAAwC,EAAE;AAUvC,SAAS,0CAAgB,OAAkB,EAAE,OAA0C;IAC5F,IAAI,YAAY,CAAA,mKAAA,iBAAa,EAAE,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,OAAS,CAAC,EAAE;IAC3C,IAAI,OAAO,mBAAmB,UAAU,OAAO,GAAG;QAAC,MAAM;IAAO,IAAI;QACzD;IAAX,IAAI,OAAO,CAAA,aAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,IAAI,MAAA,QAAV,eAAA,KAAA,IAAA,aAAc,SAAS,IAAI;IACtC,IAAI,iBAAiB,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,cAAc,KAAI;IAC7C,IAAI,eAAe,IAAI,IAAa;IACpC,IAAI,cAAc,IAAI;IAEtB,IAAI,YAAY,CAAC;QACf,OAAO,kBAAkB,mBAAmB,UAAU,WAAW,GAAG,QAAQ,KAAK,GAAG,QAAQ,YAAY,CAAC,mBAAmB;IAC9H;IAEA,IAAI,YAAY,CAAC,SAAkB;QACjC,IAAI,kBAAkB,mBAAmB,UAAU,WAAW,EAC5D,QAAQ,KAAK,GAAG;aACX,IAAI,QACT,QAAQ,YAAY,CAAC,eAAe;aAEpC,QAAQ,eAAe,CAAC;IAE5B;IAEA,IAAI,OAAO,CAAC;QACV,oEAAoE;QACpE,KAAK,IAAI,WAAW,KAAK,gBAAgB,CAAC,sDACxC,aAAa,GAAG,CAAC;QAGnB,IAAI,aAAa,CAAC;YAChB,yFAAyF;YACzF,2FAA2F;YAC3F,uGAAuG;YACvG,2GAA2G;YAC3G,IACE,YAAY,GAAG,CAAC,SAChB,aAAa,GAAG,CAAC,SAChB,KAAK,aAAa,IAAI,YAAY,GAAG,CAAC,KAAK,aAAa,KAAK,KAAK,aAAa,CAAC,YAAY,CAAC,YAAY,OAE1G,OAAO,WAAW,aAAa;YAGjC,oFAAoF;YACpF,KAAK,IAAI,UAAU,aAAc;gBAC/B,IAAI,KAAK,QAAQ,CAAC,SAChB,OAAO,WAAW,WAAW;YAEjC;YAEA,OAAO,WAAW,aAAa;QACjC;QAEA,IAAI,SAAS,SAAS,gBAAgB,CACpC,MACA,WAAW,YAAY,EACvB;wBAAC;QAAU;QAGb,wCAAwC;QACxC,IAAI,aAAa,WAAW;QAC5B,IAAI,eAAe,WAAW,aAAa,EACzC,KAAK;QAGP,IAAI,eAAe,WAAW,aAAa,EAAE;YAC3C,IAAI,OAAO,OAAO,QAAQ;YAC1B,MAAO,QAAQ,KAAM;gBACnB,KAAK;gBACL,OAAO,OAAO,QAAQ;YACxB;QACF;IACF;IAEA,IAAI,OAAO,CAAC;YACK;QAAf,IAAI,WAAW,CAAA,mBAAA,kCAAY,GAAG,CAAC,KAAA,MAAA,QAAhB,qBAAA,KAAA,IAAA,mBAAyB;QAExC,uEAAuE;QACvE,uDAAuD;QACvD,IAAI,UAAU,SAAS,aAAa,GAClC;QAGF,IAAI,aAAa,GACf,UAAU,MAAM;QAGlB,YAAY,GAAG,CAAC;QAChB,kCAAY,GAAG,CAAC,MAAM,WAAW;IACnC;IAEA,yEAAyE;IACzE,0CAA0C;IAC1C,IAAI,oCAAc,MAAM,EACtB,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE,CAAC,UAAU;IAGpD,KAAK;IAEL,IAAI,WAAW,IAAI,iBAAiB,CAAA;QAClC,KAAK,IAAI,UAAU,QAAS;YAC1B,IAAI,OAAO,IAAI,KAAK,aAClB;YAGF,6EAA6E;YAC7E,sEAAsE;YACtE,IAAI,CAAC;mBAAI;mBAAiB;aAAY,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,OAAO,MAAM,IAC7E,KAAK,IAAI,QAAQ,OAAO,UAAU,CAAE;gBAClC,IACG,CAAA,gBAAgB,eAAe,gBAAgB,UAAS,KACxD,CAAA,KAAK,OAAO,CAAC,aAAa,KAAK,UAAU,KAAK,OAAO,CAAC,iBAAiB,KAAK,MAAK,GAElF,aAAa,GAAG,CAAC;qBACZ,IAAI,gBAAgB,SACzB,KAAK;YAET;QAEJ;IACF;IAEA,SAAS,OAAO,CAAC,MAAM;QAAC,WAAW;QAAM,SAAS;IAAI;IAEtD,IAAI,kBAAmC;sBACrC;qBACA;QACA;YACE,SAAS,OAAO,CAAC,MAAM;gBAAC,WAAW;gBAAM,SAAS;YAAI;QACxD;QACA;YACE,SAAS,UAAU;QACrB;IACF;IAEA,oCAAc,IAAI,CAAC;IAEnB,OAAO;QACL,SAAS,UAAU;QAEnB,KAAK,IAAI,QAAQ,YAAa;YAC5B,IAAI,QAAQ,kCAAY,GAAG,CAAC;YAC5B,IAAI,SAAS,MACX;YAEF,IAAI,UAAU,GAAG;gBACf,UAAU,MAAM;gBAChB,kCAAY,MAAM,CAAC;YACrB,OACE,kCAAY,GAAG,CAAC,MAAM,QAAQ;QAElC;QAEA,mEAAmE;QACnE,IAAI,oBAAoB,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE,EAAE;YAC/D,oCAAc,GAAG;YACjB,IAAI,oCAAc,MAAM,EACtB,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE,CAAC,OAAO;QAEnD,OACE,oCAAc,MAAM,CAAC,oCAAc,OAAO,CAAC,kBAAkB;IAEjE;AACF;AAEO,SAAS,0CAAY,OAAgB;IAC1C,IAAI,WAAW,mCAAa,CAAC,oCAAc,MAAM,GAAG,EAAE;IACtD,IAAI,YAAY,CAAC,SAAS,YAAY,CAAC,GAAG,CAAC,UAAU;QACnD,SAAS,YAAY,CAAC,GAAG,CAAC;QAC1B,OAAO;YACL,SAAS,YAAY,CAAC,MAAM,CAAC;QAC/B;IACF;AACF", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/listbox/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/listbox/dist/packages/%40react-aria/listbox/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {ListState} from '@react-stately/list';\n\ninterface ListData {\n  id?: string,\n  shouldSelectOnPressUp?: boolean,\n  shouldFocusOnHover?: boolean,\n  shouldUseVirtualFocus?: boolean,\n  isVirtualized?: boolean,\n  onAction?: (key: Key) => void,\n  linkBehavior?: 'action' | 'selection' | 'override'\n}\n\nexport const listData: WeakMap<ListState<unknown>, ListData> = new WeakMap<ListState<unknown>, ListData>();\n\nfunction normalizeKey(key: Key): string {\n  if (typeof key === 'string') {\n    return key.replace(/\\s*/g, '');\n  }\n\n  return '' + key;\n}\n\nexport function getItemId<T>(state: ListState<T>, itemKey: Key): string {\n  let data = listData.get(state);\n\n  if (!data) {\n    throw new Error('Unknown list');\n  }\n\n  return `${data.id}-option-${normalizeKey(itemKey)}`;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;AAeM,MAAM,4CAAkD,IAAI;AAEnE,SAAS,mCAAa,GAAQ;IAC5B,IAAI,OAAO,QAAQ,UACjB,OAAO,IAAI,OAAO,CAAC,QAAQ;IAG7B,OAAO,KAAK;AACd;AAEO,SAAS,0CAAa,KAAmB,EAAE,OAAY;IAC5D,IAAI,OAAO,0CAAS,GAAG,CAAC;IAExB,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,mCAAa,UAAU;AACrD", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/listbox/dist/useListBox.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/listbox/dist/packages/%40react-aria/listbox/src/useListBox.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaListBoxProps} from '@react-types/listbox';\nimport {DOMAttributes, KeyboardDelegate, LayoutDelegate, RefObject} from '@react-types/shared';\nimport {filterDOMProps, mergeProps, useId} from '@react-aria/utils';\nimport {listData} from './utils';\nimport {ListState} from '@react-stately/list';\nimport {useFocusWithin} from '@react-aria/interactions';\nimport {useLabel} from '@react-aria/label';\nimport {useSelectableList} from '@react-aria/selection';\n\nexport interface ListBoxAria {\n  /** Props for the listbox element. */\n  listBoxProps: DOMAttributes,\n  /** Props for the listbox's visual label element (if any). */\n  labelProps: DOMAttributes\n}\n\nexport interface AriaListBoxOptions<T> extends Omit<AriaListBoxProps<T>, 'children'> {\n  /** Whether the listbox uses virtual scrolling. */\n  isVirtualized?: boolean,\n\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n\n  /**\n   * A delegate object that provides layout information for items in the collection.\n   * By default this uses the DOM, but this can be overridden to implement things like\n   * virtualized scrolling.\n   */\n  layoutDelegate?: LayoutDelegate,\n\n  /**\n   * Whether the listbox items should use virtual focus instead of being focused directly.\n   */\n  shouldUseVirtualFocus?: boolean,\n\n  /**\n   * The behavior of links in the collection.\n   * - 'action': link behaves like onAction.\n   * - 'selection': link follows selection interactions (e.g. if URL drives selection).\n   * - 'override': links override all other interactions (link items are not selectable).\n   * @default 'override'\n   */\n  linkBehavior?: 'action' | 'selection' | 'override'\n}\n\n/**\n * Provides the behavior and accessibility implementation for a listbox component.\n * A listbox displays a list of options and allows a user to select one or more of them.\n * @param props - Props for the listbox.\n * @param state - State for the listbox, as returned by `useListState`.\n */\nexport function useListBox<T>(props: AriaListBoxOptions<T>, state: ListState<T>, ref: RefObject<HTMLElement | null>): ListBoxAria {\n  let domProps = filterDOMProps(props, {labelable: true});\n  // Use props instead of state here. We don't want this to change due to long press.\n  let selectionBehavior = props.selectionBehavior || 'toggle';\n  let linkBehavior = props.linkBehavior || (selectionBehavior === 'replace' ? 'action' : 'override');\n  if (selectionBehavior === 'toggle' && linkBehavior === 'action') {\n    // linkBehavior=\"action\" does not work with selectionBehavior=\"toggle\" because there is no way\n    // to initiate selection (checkboxes are not allowed inside a listbox). Link items will not be\n    // selectable in this configuration.\n    linkBehavior = 'override';\n  }\n\n  let {listProps} = useSelectableList({\n    ...props,\n    ref,\n    selectionManager: state.selectionManager,\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    linkBehavior\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    onFocusWithin: props.onFocus,\n    onBlurWithin: props.onBlur,\n    onFocusWithinChange: props.onFocusChange\n  });\n\n  // Share list id and some props with child options.\n  let id = useId(props.id);\n  listData.set(state, {\n    id,\n    shouldUseVirtualFocus: props.shouldUseVirtualFocus,\n    shouldSelectOnPressUp: props.shouldSelectOnPressUp,\n    shouldFocusOnHover: props.shouldFocusOnHover,\n    isVirtualized: props.isVirtualized,\n    onAction: props.onAction,\n    linkBehavior\n  });\n\n  let {labelProps, fieldProps} = useLabel({\n    ...props,\n    id,\n    // listbox is not an HTML input element so it\n    // shouldn't be labeled by a <label> element.\n    labelElementType: 'span'\n  });\n\n  return {\n    labelProps,\n    listBoxProps: mergeProps(domProps, focusWithinProps, state.selectionManager.selectionMode === 'multiple' ? {\n      'aria-multiselectable': 'true'\n    } : {}, {\n      role: 'listbox',\n      ...mergeProps(fieldProps, listProps)\n    })\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAwDM,SAAS,0CAAc,KAA4B,EAAE,KAAmB,EAAE,GAAkC;IACjH,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,mFAAmF;IACnF,IAAI,oBAAoB,MAAM,iBAAiB,IAAI;IACnD,IAAI,eAAe,MAAM,YAAY,IAAK,CAAA,sBAAsB,YAAY,WAAW,UAAS;IAChG,IAAI,sBAAsB,YAAY,iBAAiB,UACrD,AACA,8FAD8F,AACA;IAC9F,oCAAoC;IACpC,eAAe;IAGjB,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,8KAAA,oBAAgB,EAAE;QAClC,GAAG,KAAK;aACR;QACA,kBAAkB,MAAM,gBAAgB;QACxC,YAAY,MAAM,UAAU;QAC5B,cAAc,MAAM,YAAY;sBAChC;IACF;IAEA,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,8KAAA,iBAAa,EAAE;QACtC,eAAe,MAAM,OAAO;QAC5B,cAAc,MAAM,MAAM;QAC1B,qBAAqB,MAAM,aAAa;IAC1C;IAEA,mDAAmD;IACnD,IAAI,KAAK,CAAA,8JAAA,QAAI,EAAE,MAAM,EAAE;IACvB,CAAA,gKAAA,WAAO,EAAE,GAAG,CAAC,OAAO;YAClB;QACA,uBAAuB,MAAM,qBAAqB;QAClD,uBAAuB,MAAM,qBAAqB;QAClD,oBAAoB,MAAM,kBAAkB;QAC5C,eAAe,MAAM,aAAa;QAClC,UAAU,MAAM,QAAQ;sBACxB;IACF;IAEA,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,iKAAA,WAAO,EAAE;QACtC,GAAG,KAAK;YACR;QACA,6CAA6C;QAC7C,6CAA6C;QAC7C,kBAAkB;IACpB;IAEA,OAAO;oBACL;QACA,cAAc,CAAA,mKAAA,aAAS,EAAE,UAAU,kBAAkB,MAAM,gBAAgB,CAAC,aAAa,KAAK,aAAa;YACzG,wBAAwB;QAC1B,IAAI,CAAC,GAAG;YACN,MAAM;YACN,GAAG,CAAA,mKAAA,aAAS,EAAE,YAAY,UAAU;QACtC;IACF;AACF", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/listbox/dist/useOption.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/listbox/dist/packages/%40react-aria/listbox/src/useOption.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain, filterDOMProps, isMac, isWebKit, mergeProps, useLinkProps, useSlotId} from '@react-aria/utils';\nimport {DOMAttributes, FocusableElement, Key, RefObject} from '@react-types/shared';\nimport {getItemCount} from '@react-stately/collections';\nimport {getItemId, listData} from './utils';\nimport {isFocusVisible, useHover} from '@react-aria/interactions';\nimport {ListState} from '@react-stately/list';\nimport {SelectableItemStates, useSelectableItem} from '@react-aria/selection';\n\nexport interface OptionAria extends SelectableItemStates {\n  /** Props for the option element. */\n  optionProps: DOMAttributes,\n\n  /** Props for the main text element inside the option. */\n  labelProps: DOMAttributes,\n\n  /** Props for the description text element inside the option, if any. */\n  descriptionProps: DOMAttributes,\n\n  /** Whether the option is currently focused. */\n  isFocused: boolean,\n\n  /** Whether the option is keyboard focused. */\n  isFocusVisible: boolean\n}\n\nexport interface AriaOptionProps {\n  /**\n   * Whether the option is disabled.\n   * @deprecated\n   */\n  isDisabled?: boolean,\n\n  /**\n   * Whether the option is selected.\n   * @deprecated\n   */\n  isSelected?: boolean,\n\n  /** A screen reader only label for the option. */\n  'aria-label'?: string,\n\n  /** The unique key for the option. */\n  key: Key,\n\n  /**\n   * Whether selection should occur on press up instead of press down.\n   * @deprecated\n   */\n  shouldSelectOnPressUp?: boolean,\n\n  /**\n   * Whether the option should be focused when the user hovers over it.\n   * @deprecated\n   */\n  shouldFocusOnHover?: boolean,\n\n  /**\n   * Whether the option is contained in a virtual scrolling listbox.\n   * @deprecated\n   */\n  isVirtualized?: boolean,\n\n  /**\n   * Whether the option should use virtual focus instead of being focused directly.\n   * @deprecated\n   */\n  shouldUseVirtualFocus?: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for an option in a listbox.\n * See `useListBox` for more details about listboxes.\n * @param props - Props for the option.\n * @param state - State for the listbox, as returned by `useListState`.\n */\nexport function useOption<T>(props: AriaOptionProps, state: ListState<T>, ref: RefObject<FocusableElement | null>): OptionAria {\n  let {\n    key\n  } = props;\n\n  let data = listData.get(state);\n\n  let isDisabled = props.isDisabled ?? state.selectionManager.isDisabled(key);\n  let isSelected = props.isSelected ?? state.selectionManager.isSelected(key);\n  let shouldSelectOnPressUp = props.shouldSelectOnPressUp ?? data?.shouldSelectOnPressUp;\n  let shouldFocusOnHover = props.shouldFocusOnHover ?? data?.shouldFocusOnHover;\n  let shouldUseVirtualFocus = props.shouldUseVirtualFocus ?? data?.shouldUseVirtualFocus;\n  let isVirtualized = props.isVirtualized ?? data?.isVirtualized;\n\n  let labelId = useSlotId();\n  let descriptionId = useSlotId();\n\n  let optionProps = {\n    role: 'option',\n    'aria-disabled': isDisabled || undefined,\n    'aria-selected': state.selectionManager.selectionMode !== 'none' ? isSelected : undefined\n  };\n\n  // Safari with VoiceOver on macOS misreads options with aria-labelledby or aria-label as simply \"text\".\n  // We should not map slots to the label and description on Safari and instead just have VoiceOver read the textContent.\n  // https://bugs.webkit.org/show_bug.cgi?id=209279\n  if (!(isMac() && isWebKit())) {\n    optionProps['aria-label'] = props['aria-label'];\n    optionProps['aria-labelledby'] = labelId;\n    optionProps['aria-describedby'] = descriptionId;\n  }\n\n  let item = state.collection.getItem(key);\n  if (isVirtualized) {\n    let index = Number(item?.index);\n    optionProps['aria-posinset'] = Number.isNaN(index) ? undefined : index + 1;\n    optionProps['aria-setsize'] = getItemCount(state.collection);\n  }\n\n  let onAction = data?.onAction ? () => data?.onAction?.(key) : undefined;\n  let id = getItemId(state, key);\n  let {itemProps, isPressed, isFocused, hasAction, allowsSelection} = useSelectableItem({\n    selectionManager: state.selectionManager,\n    key,\n    ref,\n    shouldSelectOnPressUp,\n    allowsDifferentPressOrigin: shouldSelectOnPressUp && shouldFocusOnHover,\n    isVirtualized,\n    shouldUseVirtualFocus,\n    isDisabled,\n    onAction: onAction || item?.props?.onAction ? chain(item?.props?.onAction, onAction) : undefined,\n    linkBehavior: data?.linkBehavior,\n    id\n  });\n\n  let {hoverProps} = useHover({\n    isDisabled: isDisabled || !shouldFocusOnHover,\n    onHoverStart() {\n      if (!isFocusVisible()) {\n        state.selectionManager.setFocused(true);\n        state.selectionManager.setFocusedKey(key);\n      }\n    }\n  });\n\n  let domProps = filterDOMProps(item?.props);\n  delete domProps.id;\n  let linkProps = useLinkProps(item?.props);\n\n  return {\n    optionProps: {\n      ...optionProps,\n      ...mergeProps(domProps, itemProps, hoverProps, linkProps),\n      id\n    },\n    labelProps: {\n      id: labelId\n    },\n    descriptionProps: {\n      id: descriptionId\n    },\n    isFocused,\n    isFocusVisible: isFocused && state.selectionManager.isFocused && isFocusVisible(),\n    isSelected,\n    isDisabled,\n    isPressed,\n    allowsSelection,\n    hasAction\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA6EM,SAAS,0CAAa,KAAsB,EAAE,KAAmB,EAAE,GAAuC;QAkDvF,aAA8B;IAjDtD,IAAI,EAAA,KACF,GAAG,EACJ,GAAG;IAEJ,IAAI,OAAO,CAAA,gKAAA,WAAO,EAAE,GAAG,CAAC;QAEP;IAAjB,IAAI,aAAa,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB,MAAM,gBAAgB,CAAC,UAAU,CAAC;QACtD;IAAjB,IAAI,aAAa,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB,MAAM,gBAAgB,CAAC,UAAU,CAAC;QAC3C;IAA5B,IAAI,wBAAwB,CAAA,+BAAA,MAAM,qBAAqB,MAAA,QAA3B,iCAAA,KAAA,IAAA,+BAA+B,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,qBAAqB;QAC7D;IAAzB,IAAI,qBAAqB,CAAA,4BAAA,MAAM,kBAAkB,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAA4B,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,kBAAkB;QACjD;IAA5B,IAAI,wBAAwB,CAAA,+BAAA,MAAM,qBAAqB,MAAA,QAA3B,iCAAA,KAAA,IAAA,+BAA+B,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,qBAAqB;QAClE;IAApB,IAAI,gBAAgB,CAAA,uBAAA,MAAM,aAAa,MAAA,QAAnB,yBAAA,KAAA,IAAA,uBAAuB,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,aAAa;IAE9D,IAAI,UAAU,CAAA,8JAAA,YAAQ;IACtB,IAAI,gBAAgB,CAAA,8JAAA,YAAQ;IAE5B,IAAI,cAAc;QAChB,MAAM;QACN,iBAAiB,cAAc;QAC/B,iBAAiB,MAAM,gBAAgB,CAAC,aAAa,KAAK,SAAS,aAAa;IAClF;IAEA,uGAAuG;IACvG,uHAAuH;IACvH,iDAAiD;IACjD,IAAI,CAAE,CAAA,CAAA,iKAAA,QAAI,OAAO,CAAA,iKAAA,WAAO,GAAE,GAAI;QAC5B,WAAW,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa;QAC/C,WAAW,CAAC,kBAAkB,GAAG;QACjC,WAAW,CAAC,mBAAmB,GAAG;IACpC;IAEA,IAAI,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;IACpC,IAAI,eAAe;QACjB,IAAI,QAAQ,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;QAC9B,WAAW,CAAC,gBAAgB,GAAG,OAAO,KAAK,CAAC,SAAS,YAAY,QAAQ;QACzE,WAAW,CAAC,eAAe,GAAG,CAAA,8KAAA,eAAW,EAAE,MAAM,UAAU;IAC7D;IAEA,IAAI,WAAW,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,IAAG;YAAM;eAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,iBAAA,KAAM,QAAQ,MAAA,QAAd,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAA,IAAA,CAAA,MAAiB;QAAO;IAC9D,IAAI,KAAK,CAAA,gKAAA,YAAQ,EAAE,OAAO;IAC1B,IAAI,EAAA,WAAC,SAAS,EAAA,WAAE,SAAS,EAAA,WAAE,SAAS,EAAA,WAAE,SAAS,EAAA,iBAAE,eAAe,EAAC,GAAG,CAAA,8KAAA,oBAAgB,EAAE;QACpF,kBAAkB,MAAM,gBAAgB;aACxC;aACA;+BACA;QACA,4BAA4B,yBAAyB;uBACrD;+BACA;oBACA;QACA,UAAU,YAAA,CAAY,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,cAAA,KAAM,KAAK,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAa,QAAQ,IAAG,CAAA,8JAAA,QAAI,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,eAAA,KAAM,KAAK,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,QAAQ,EAAE,YAAY;QACvF,YAAY,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,YAAY;YAChC;IACF;IAEA,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;QAC1B,YAAY,cAAc,CAAC;QAC3B;YACE,IAAI,CAAC,CAAA,+KAAA,iBAAa,KAAK;gBACrB,MAAM,gBAAgB,CAAC,UAAU,CAAC;gBAClC,MAAM,gBAAgB,CAAC,aAAa,CAAC;YACvC;QACF;IACF;IAEA,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;IACzC,OAAO,SAAS,EAAE;IAClB,IAAI,YAAY,CAAA,iKAAA,eAAW,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;IAExC,OAAO;QACL,aAAa;YACX,GAAG,WAAW;YACd,GAAG,CAAA,mKAAA,aAAS,EAAE,UAAU,WAAW,YAAY,UAAU;gBACzD;QACF;QACA,YAAY;YACV,IAAI;QACN;QACA,kBAAkB;YAChB,IAAI;QACN;mBACA;QACA,gBAAgB,aAAa,MAAM,gBAAgB,CAAC,SAAS,IAAI,CAAA,+KAAA,iBAAa;oBAC9E;oBACA;mBACA;yBACA;mBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/listbox/dist/useListBoxSection.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/listbox/dist/packages/%40react-aria/listbox/src/useListBoxSection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport {ReactNode} from 'react';\nimport {useId} from '@react-aria/utils';\n\nexport interface AriaListBoxSectionProps {\n  /** The heading for the section. */\n  heading?: ReactNode,\n  /** An accessibility label for the section. Required if `heading` is not present. */\n  'aria-label'?: string\n}\n\nexport interface ListBoxSectionAria {\n  /** Props for the wrapper list item. */\n  itemProps: DOMAttributes,\n\n  /** Props for the heading element, if any. */\n  headingProps: DOMAttributes,\n\n  /** Props for the group element. */\n  groupProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a section in a listbox.\n * See `useListBox` for more details about listboxes.\n * @param props - Props for the section.\n */\nexport function useListBoxSection(props: AriaListBoxSectionProps): ListBoxSectionAria {\n  let {heading, 'aria-label': ariaLabel} = props;\n  let headingId = useId();\n\n  return {\n    itemProps: {\n      role: 'presentation'\n    },\n    headingProps: heading ? {\n      // Techincally, listbox cannot contain headings according to ARIA.\n      // We hide the heading from assistive technology, using role=\"presentation\",\n      // and only use it as a visual label for the nested group.\n      id: headingId,\n      role: 'presentation'\n    } : {},\n    groupProps: {\n      role: 'group',\n      'aria-label': ariaLabel,\n      'aria-labelledby': heading ? headingId : undefined\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GA6BM,SAAS,yCAAkB,KAA8B;IAC9D,IAAI,EAAA,SAAC,OAAO,EAAE,cAAc,SAAS,EAAC,GAAG;IACzC,IAAI,YAAY,CAAA,8JAAA,QAAI;IAEpB,OAAO;QACL,WAAW;YACT,MAAM;QACR;QACA,cAAc,UAAU;YACtB,kEAAkE;YAClE,4EAA4E;YAC5E,0DAA0D;YAC1D,IAAI;YACJ,MAAM;QACR,IAAI,CAAC;QACL,YAAY;YACV,MAAM;YACN,cAAc;YACd,mBAAmB,UAAU,YAAY;QAC3C;IACF;AACF", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/radio/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/radio/dist/packages/%40react-aria/radio/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RadioGroupState} from '@react-stately/radio';\n\ninterface RadioGroupData {\n  name: string,\n  form: string | undefined,\n  descriptionId: string | undefined,\n  errorMessageId: string | undefined,\n  validationBehavior: 'aria' | 'native'\n}\n\nexport const radioGroupData: WeakMap<RadioGroupState, RadioGroupData> = new WeakMap<RadioGroupState, RadioGroupData>();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAYM,MAAM,4CAA2D,IAAI", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/radio/dist/useRadioGroup.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/radio/dist/packages/%40react-aria/radio/src/useRadioGroup.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaRadioGroupProps} from '@react-types/radio';\nimport {DOMAttributes, ValidationResult} from '@react-types/shared';\nimport {filterDOMProps, getOwnerWindow, mergeProps, useId} from '@react-aria/utils';\nimport {getFocusableTreeWalker} from '@react-aria/focus';\nimport {radioGroupData} from './utils';\nimport {RadioGroupState} from '@react-stately/radio';\nimport {useField} from '@react-aria/label';\nimport {useFocusWithin} from '@react-aria/interactions';\nimport {useLocale} from '@react-aria/i18n';\n\nexport interface RadioGroupAria extends ValidationResult {\n  /** Props for the radio group wrapper element. */\n  radioGroupProps: DOMAttributes,\n  /** Props for the radio group's visible label (if any). */\n  labelProps: DOMAttributes,\n  /** Props for the radio group description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the radio group error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a radio group component.\n * Radio groups allow users to select a single item from a list of mutually exclusive options.\n * @param props - Props for the radio group.\n * @param state - State for the radio group, as returned by `useRadioGroupState`.\n */\nexport function useRadioGroup(props: AriaRadioGroupProps, state: RadioGroupState): RadioGroupAria {\n  let {\n    name,\n    form,\n    isReadOnly,\n    isRequired,\n    isDisabled,\n    orientation = 'vertical',\n    validationBehavior = 'aria'\n  } = props;\n  let {direction} = useLocale();\n\n  let {isInvalid, validationErrors, validationDetails} = state.displayValidation;\n  let {labelProps, fieldProps, descriptionProps, errorMessageProps} = useField({\n    ...props,\n    // Radio group is not an HTML input element so it\n    // shouldn't be labeled by a <label> element.\n    labelElementType: 'span',\n    isInvalid: state.isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  // When the radio group loses focus, reset the focusable radio to null if\n  // there is no selection. This allows tabbing into the group from either\n  // direction to go to the first or last radio.\n  let {focusWithinProps} = useFocusWithin({\n    onBlurWithin(e) {\n      props.onBlur?.(e);\n      if (!state.selectedValue) {\n        state.setLastFocusedValue(null);\n      }\n    },\n    onFocusWithin: props.onFocus,\n    onFocusWithinChange: props.onFocusChange\n  });\n\n  let onKeyDown = (e) => {\n    let nextDir;\n    switch (e.key) {\n      case 'ArrowRight':\n        if (direction === 'rtl' && orientation !== 'vertical') {\n          nextDir = 'prev';\n        } else {\n          nextDir = 'next';\n        }\n        break;\n      case 'ArrowLeft':\n        if (direction === 'rtl' && orientation !== 'vertical') {\n          nextDir = 'next';\n        } else {\n          nextDir = 'prev';\n        }\n        break;\n      case 'ArrowDown':\n        nextDir = 'next';\n        break;\n      case 'ArrowUp':\n        nextDir = 'prev';\n        break;\n      default:\n        return;\n    }\n    e.preventDefault();\n    let walker = getFocusableTreeWalker(e.currentTarget, {\n      from: e.target,\n      accept: (node) => node instanceof getOwnerWindow(node).HTMLInputElement && node.type === 'radio'\n    });\n    let nextElem;\n    if (nextDir === 'next') {\n      nextElem = walker.nextNode();\n      if (!nextElem) {\n        walker.currentNode = e.currentTarget;\n        nextElem = walker.firstChild();\n      }\n    } else {\n      nextElem = walker.previousNode();\n      if (!nextElem) {\n        walker.currentNode = e.currentTarget;\n        nextElem = walker.lastChild();\n      }\n    }\n    if (nextElem) {\n      // Call focus on nextElem so that keyboard navigation scrolls the radio into view\n      nextElem.focus();\n      state.setSelectedValue(nextElem.value);\n    }\n  };\n\n  let groupName = useId(name);\n  radioGroupData.set(state, {\n    name: groupName,\n    form,\n    descriptionId: descriptionProps.id,\n    errorMessageId: errorMessageProps.id,\n    validationBehavior\n  });\n\n  return {\n    radioGroupProps: mergeProps(domProps, {\n      // https://www.w3.org/TR/wai-aria-1.2/#radiogroup\n      role: 'radiogroup',\n      onKeyDown,\n      'aria-invalid': state.isInvalid || undefined,\n      'aria-errormessage': props['aria-errormessage'],\n      'aria-readonly': isReadOnly || undefined,\n      'aria-required': isRequired || undefined,\n      'aria-disabled': isDisabled || undefined,\n      'aria-orientation': orientation,\n      ...fieldProps,\n      ...focusWithinProps\n    }),\n    labelProps,\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA6BM,SAAS,0CAAc,KAA0B,EAAE,KAAsB;IAC9E,IAAI,EAAA,MACF,IAAI,EAAA,MACJ,IAAI,EAAA,YACJ,UAAU,EAAA,YACV,UAAU,EAAA,YACV,UAAU,EAAA,aACV,cAAc,UAAA,EAAA,oBACd,qBAAqB,MAAA,EACtB,GAAG;IACJ,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,+JAAA,YAAQ;IAE1B,IAAI,EAAA,WAAC,SAAS,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAC,GAAG,MAAM,iBAAiB;IAC9E,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAC,GAAG,CAAA,iKAAA,WAAO,EAAE;QAC3E,GAAG,KAAK;QACR,iDAAiD;QACjD,6CAA6C;QAC7C,kBAAkB;QAClB,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY,IAAI;IACtC;IAEA,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,yEAAyE;IACzE,wEAAwE;IACxE,8CAA8C;IAC9C,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,8KAAA,iBAAa,EAAE;QACtC,cAAa,CAAC;gBACZ;aAAA,gBAAA,MAAM,MAAM,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAA,IAAA,CAAA,OAAe;YACf,IAAI,CAAC,MAAM,aAAa,EACtB,MAAM,mBAAmB,CAAC;QAE9B;QACA,eAAe,MAAM,OAAO;QAC5B,qBAAqB,MAAM,aAAa;IAC1C;IAEA,IAAI,YAAY,CAAC;QACf,IAAI;QACJ,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,IAAI,cAAc,SAAS,gBAAgB,YACzC,UAAU;qBAEV,UAAU;gBAEZ;YACF,KAAK;gBACH,IAAI,cAAc,SAAS,gBAAgB,YACzC,UAAU;qBAEV,UAAU;gBAEZ;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE;QACJ;QACA,EAAE,cAAc;QAChB,IAAI,SAAS,CAAA,mKAAA,yBAAqB,EAAE,EAAE,aAAa,EAAE;YACnD,MAAM,EAAE,MAAM;YACd,QAAQ,CAAC,OAAS,gBAAgB,CAAA,mKAAA,iBAAa,EAAE,MAAM,gBAAgB,IAAI,KAAK,IAAI,KAAK;QAC3F;QACA,IAAI;QACJ,IAAI,YAAY,QAAQ;YACtB,WAAW,OAAO,QAAQ;YAC1B,IAAI,CAAC,UAAU;gBACb,OAAO,WAAW,GAAG,EAAE,aAAa;gBACpC,WAAW,OAAO,UAAU;YAC9B;QACF,OAAO;YACL,WAAW,OAAO,YAAY;YAC9B,IAAI,CAAC,UAAU;gBACb,OAAO,WAAW,GAAG,EAAE,aAAa;gBACpC,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,IAAI,UAAU;YACZ,iFAAiF;YACjF,SAAS,KAAK;YACd,MAAM,gBAAgB,CAAC,SAAS,KAAK;QACvC;IACF;IAEA,IAAI,YAAY,CAAA,8JAAA,QAAI,EAAE;IACtB,CAAA,8JAAA,iBAAa,EAAE,GAAG,CAAC,OAAO;QACxB,MAAM;cACN;QACA,eAAe,iBAAiB,EAAE;QAClC,gBAAgB,kBAAkB,EAAE;4BACpC;IACF;IAEA,OAAO;QACL,iBAAiB,CAAA,mKAAA,aAAS,EAAE,UAAU;YACpC,iDAAiD;YACjD,MAAM;uBACN;YACA,gBAAgB,MAAM,SAAS,IAAI;YACnC,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,iBAAiB,cAAc;YAC/B,iBAAiB,cAAc;YAC/B,iBAAiB,cAAc;YAC/B,oBAAoB;YACpB,GAAG,UAAU;YACb,GAAG,gBAAgB;QACrB;oBACA;0BACA;2BACA;mBACA;0BACA;2BACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/radio/dist/useRadio.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/radio/dist/packages/%40react-aria/radio/src/useRadio.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaRadioProps} from '@react-types/radio';\nimport {filterDOMProps, mergeProps, useFormReset} from '@react-aria/utils';\nimport {InputHTMLAttributes, LabelHTMLAttributes} from 'react';\nimport {radioGroupData} from './utils';\nimport {RadioGroupState} from '@react-stately/radio';\nimport {RefObject} from '@react-types/shared';\nimport {useFocusable, usePress} from '@react-aria/interactions';\nimport {useFormValidation} from '@react-aria/form';\n\nexport interface RadioAria {\n  /** Props for the label wrapper element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Whether the radio is disabled. */\n  isDisabled: boolean,\n  /** Whether the radio is currently selected. */\n  isSelected: boolean,\n  /** Whether the radio is in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for an individual\n * radio button in a radio group.\n * @param props - Props for the radio.\n * @param state - State for the radio group, as returned by `useRadioGroupState`.\n * @param ref - Ref to the HTML input element.\n */\nexport function useRadio(props: AriaRadioProps, state: RadioGroupState, ref: RefObject<HTMLInputElement | null>): RadioAria {\n  let {\n    value,\n    children,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick\n  } = props;\n\n  const isDisabled = props.isDisabled || state.isDisabled;\n\n  let hasChildren = children != null;\n  let hasAriaLabel = ariaLabel != null || ariaLabelledby != null;\n  if (!hasChildren && !hasAriaLabel && process.env.NODE_ENV !== 'production') {\n    console.warn('If you do not provide children, you must specify an aria-label for accessibility');\n  }\n\n  let checked = state.selectedValue === value;\n\n  let onChange = (e) => {\n    e.stopPropagation();\n    state.setSelectedValue(value);\n  };\n\n  // Handle press state for keyboard interactions and cases where labelProps is not used.\n  let {pressProps, isPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick,\n    isDisabled\n  });\n\n  // Handle press state on the label.\n  let {pressProps: labelProps, isPressed: isLabelPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPressUp,\n    onClick,\n    isDisabled,\n    onPress(e) {\n      onPress?.(e);\n      state.setSelectedValue(value);\n      ref.current?.focus();\n    }\n  });\n\n  let {focusableProps} = useFocusable(mergeProps(props, {\n    onFocus: () => state.setLastFocusedValue(value)\n  }), ref);\n  let interactions = mergeProps(pressProps, focusableProps);\n  let domProps = filterDOMProps(props, {labelable: true});\n  let tabIndex: number | undefined = -1;\n  if (state.selectedValue != null) {\n    if (state.selectedValue === value) {\n      tabIndex = 0;\n    }\n  } else if (state.lastFocusedValue === value || state.lastFocusedValue == null) {\n    tabIndex = 0;\n  }\n  if (isDisabled) {\n    tabIndex = undefined;\n  }\n\n  let {name, form, descriptionId, errorMessageId, validationBehavior} = radioGroupData.get(state)!;\n  useFormReset(ref, state.defaultSelectedValue, state.setSelectedValue);\n  useFormValidation({validationBehavior}, state, ref);\n\n  return {\n    labelProps: mergeProps(labelProps, {onClick: e => e.preventDefault()}),\n    inputProps: mergeProps(domProps, {\n      ...interactions,\n      type: 'radio',\n      name,\n      form,\n      tabIndex,\n      disabled: isDisabled,\n      required: state.isRequired && validationBehavior === 'native',\n      checked,\n      value,\n      onChange,\n      'aria-describedby': [\n        props['aria-describedby'],\n        state.isInvalid ? errorMessageId : null,\n        descriptionId\n      ].filter(Boolean).join(' ') || undefined\n    }),\n    isDisabled,\n    isSelected: checked,\n    isPressed: isPressed || isLabelPressed\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA+BM,SAAS,0CAAS,KAAqB,EAAE,KAAsB,EAAE,GAAuC;IAC7G,IAAI,EAAA,OACF,KAAK,EAAA,UACL,QAAQ,EACR,cAAc,SAAS,EACvB,mBAAmB,cAAc,EAAA,cACjC,YAAY,EAAA,YACZ,UAAU,EAAA,eACV,aAAa,EAAA,SACb,OAAO,EAAA,WACP,SAAS,EAAA,SACT,OAAO,EACR,GAAG;IAEJ,MAAM,aAAa,MAAM,UAAU,IAAI,MAAM,UAAU;IAEvD,IAAI,cAAc,YAAY;IAC9B,IAAI,eAAe,aAAa,QAAQ,kBAAkB;IAC1D,IAAI,CAAC,eAAe,CAAC,gBAAgB,QAAQ,GAAG,CAAC,QAAQ,gCAAK,cAC5D,QAAQ,IAAI,CAAC;IAGf,IAAI,UAAU,MAAM,aAAa,KAAK;IAEtC,IAAI,WAAW,CAAC;QACd,EAAE,eAAe;QACjB,MAAM,gBAAgB,CAAC;IACzB;IAEA,uFAAuF;IACvF,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;sBACrC;oBACA;uBACA;iBACA;mBACA;iBACA;oBACA;IACF;IAEA,mCAAmC;IACnC,IAAI,EAAC,YAAY,UAAU,EAAE,WAAW,cAAc,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;sBACjE;oBACA;uBACA;mBACA;iBACA;oBACA;QACA,SAAQ,CAAC;gBAGP;YAFA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAU;YACV,MAAM,gBAAgB,CAAC;aACvB,eAAA,IAAI,OAAO,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,KAAK;QACpB;IACF;IAEA,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,4KAAA,eAAW,EAAE,CAAA,mKAAA,aAAS,EAAE,OAAO;QACpD,SAAS,IAAM,MAAM,mBAAmB,CAAC;IAC3C,IAAI;IACJ,IAAI,eAAe,CAAA,mKAAA,aAAS,EAAE,YAAY;IAC1C,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,WAA+B,CAAA;IACnC,IAAI,MAAM,aAAa,IAAI,MACzB;QAAA,IAAI,MAAM,aAAa,KAAK,OAC1B,WAAW;IACb,OACK,IAAI,MAAM,gBAAgB,KAAK,SAAS,MAAM,gBAAgB,IAAI,MACvE,WAAW;IAEb,IAAI,YACF,WAAW;IAGb,IAAI,EAAA,MAAC,IAAI,EAAA,MAAE,IAAI,EAAA,eAAE,aAAa,EAAA,gBAAE,cAAc,EAAA,oBAAE,kBAAkB,EAAC,GAAG,CAAA,8JAAA,iBAAa,EAAE,GAAG,CAAC;IACzF,CAAA,qKAAA,eAAW,EAAE,KAAK,MAAM,oBAAoB,EAAE,MAAM,gBAAgB;IACpE,CAAA,yKAAA,oBAAgB,EAAE;4BAAC;IAAkB,GAAG,OAAO;IAE/C,OAAO;QACL,YAAY,CAAA,mKAAA,aAAS,EAAE,YAAY;YAAC,SAAS,CAAA,IAAK,EAAE,cAAc;QAAE;QACpE,YAAY,CAAA,mKAAA,aAAS,EAAE,UAAU;YAC/B,GAAG,YAAY;YACf,MAAM;kBACN;kBACA;sBACA;YACA,UAAU;YACV,UAAU,MAAM,UAAU,IAAI,uBAAuB;qBACrD;mBACA;sBACA;YACA,oBAAoB;gBAClB,KAAK,CAAC,mBAAmB;gBACzB,MAAM,SAAS,GAAG,iBAAiB;gBACnC;aACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;QACjC;oBACA;QACA,YAAY;QACZ,WAAW,aAAa;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/checkbox/dist/useCheckbox.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/checkbox/dist/packages/%40react-aria/checkbox/src/useCheckbox.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaCheckboxProps} from '@react-types/checkbox';\nimport {InputHTMLAttributes, LabelHTMLAttributes, useEffect} from 'react';\nimport {mergeProps} from '@react-aria/utils';\nimport {privateValidationStateProp, useFormValidationState} from '@react-stately/form';\nimport {RefObject, ValidationResult} from '@react-types/shared';\nimport {ToggleState} from '@react-stately/toggle';\nimport {useFormValidation} from '@react-aria/form';\nimport {usePress} from '@react-aria/interactions';\nimport {useToggle} from '@react-aria/toggle';\n\nexport interface CheckboxAria extends ValidationResult {\n  /** Props for the label wrapper element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Whether the checkbox is selected. */\n  isSelected: boolean,\n  /** Whether the checkbox is in a pressed state. */\n  isPressed: boolean,\n  /** Whether the checkbox is disabled. */\n  isDisabled: boolean,\n  /** Whether the checkbox is read only. */\n  isReadOnly: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a checkbox component.\n * Checkboxes allow users to select multiple items from a list of individual items, or\n * to mark one individual item as selected.\n * @param props - Props for the checkbox.\n * @param state - State for the checkbox, as returned by `useToggleState`.\n * @param inputRef - A ref for the HTML input element.\n */\nexport function useCheckbox(props: AriaCheckboxProps, state: ToggleState, inputRef: RefObject<HTMLInputElement | null>): CheckboxAria {\n  // Create validation state here because it doesn't make sense to add to general useToggleState.\n  let validationState = useFormValidationState({...props, value: state.isSelected});\n  let {isInvalid, validationErrors, validationDetails} = validationState.displayValidation;\n  let {labelProps, inputProps, isSelected, isPressed, isDisabled, isReadOnly} = useToggle({\n    ...props,\n    isInvalid\n  }, state, inputRef);\n\n  useFormValidation(props, validationState, inputRef);\n\n  let {isIndeterminate, isRequired, validationBehavior = 'aria'} = props;\n  useEffect(() => {\n    // indeterminate is a property, but it can only be set via javascript\n    // https://css-tricks.com/indeterminate-checkboxes/\n    if (inputRef.current) {\n      inputRef.current.indeterminate = !!isIndeterminate;\n    }\n  });\n\n  // Reset validation state on label press for checkbox with a hidden input.\n  let {pressProps} = usePress({\n    isDisabled: isDisabled || isReadOnly,\n    onPress() {\n      // @ts-expect-error\n      let {[privateValidationStateProp]: groupValidationState} = props;\n  \n      let {commitValidation} = groupValidationState\n      ? groupValidationState\n      : validationState;\n      \n      commitValidation();\n    }\n  });\n\n  return {\n    labelProps: mergeProps(labelProps, pressProps),\n    inputProps: {\n      ...inputProps,\n      checked: isSelected,\n      'aria-required': (isRequired && validationBehavior === 'aria') || undefined,\n      required: isRequired && validationBehavior === 'native'\n    },\n    isSelected,\n    isPressed,\n    isDisabled,\n    isReadOnly,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAmCM,SAAS,0CAAY,KAAwB,EAAE,KAAkB,EAAE,QAA4C;IACpH,+FAA+F;IAC/F,IAAI,kBAAkB,CAAA,iLAAA,yBAAqB,EAAE;QAAC,GAAG,KAAK;QAAE,OAAO,MAAM,UAAU;IAAA;IAC/E,IAAI,EAAA,WAAC,SAAS,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAC,GAAG,gBAAgB,iBAAiB;IACxF,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAA,YAAE,UAAU,EAAA,WAAE,SAAS,EAAA,YAAE,UAAU,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,mKAAA,YAAQ,EAAE;QACtF,GAAG,KAAK;mBACR;IACF,GAAG,OAAO;IAEV,CAAA,yKAAA,oBAAgB,EAAE,OAAO,iBAAiB;IAE1C,IAAI,EAAA,iBAAC,eAAe,EAAA,YAAE,UAAU,EAAA,oBAAE,qBAAqB,MAAA,EAAO,GAAG;IACjE,CAAA,yMAAA,YAAQ,EAAE;QACR,qEAAqE;QACrE,mDAAmD;QACnD,IAAI,SAAS,OAAO,EAClB,SAAS,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;IAEvC;IAEA,0EAA0E;IAC1E,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;QAC1B,YAAY,cAAc;QAC1B;YACE,mBAAmB;YACnB,IAAI,EAAC,CAAC,CAAA,iLAAA,6BAAyB,EAAE,EAAE,oBAAoB,EAAC,GAAG;YAE3D,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,uBACvB,uBACA;YAEF;QACF;IACF;IAEA,OAAO;QACL,YAAY,CAAA,mKAAA,aAAS,EAAE,YAAY;QACnC,YAAY;YACV,GAAG,UAAU;YACb,SAAS;YACT,iBAAkB,cAAc,uBAAuB,UAAW;YAClE,UAAU,cAAc,uBAAuB;QACjD;oBACA;mBACA;oBACA;oBACA;mBACA;0BACA;2BACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/checkbox/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/checkbox/dist/packages/%40react-aria/checkbox/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CheckboxGroupState} from '@react-stately/checkbox';\n\ninterface CheckboxGroupData {\n  name?: string,\n  form?: string,\n  descriptionId?: string,\n  errorMessageId?: string,\n  validationBehavior: 'aria' | 'native'\n}\n\nexport const checkboxGroupData: WeakMap<CheckboxGroupState, CheckboxGroupData> = new WeakMap<CheckboxGroupState, CheckboxGroupData>();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAYM,MAAM,4CAAoE,IAAI", "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/checkbox/dist/useCheckboxGroupItem.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/checkbox/dist/packages/%40react-aria/checkbox/src/useCheckboxGroupItem.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaCheckboxGroupItemProps} from '@react-types/checkbox';\nimport {CheckboxAria, useCheckbox} from './useCheckbox';\nimport {checkboxGroupData} from './utils';\nimport {CheckboxGroupState} from '@react-stately/checkbox';\nimport {DEFAULT_VALIDATION_RESULT, privateValidationStateProp, useFormValidationState} from '@react-stately/form';\nimport {RefObject, ValidationResult} from '@react-types/shared';\nimport {useEffect, useRef} from 'react';\nimport {useToggleState} from '@react-stately/toggle';\n\n/**\n * Provides the behavior and accessibility implementation for a checkbox component contained within a checkbox group.\n * Checkbox groups allow users to select multiple items from a list of options.\n * @param props - Props for the checkbox.\n * @param state - State for the checkbox, as returned by `useCheckboxGroupState`.\n * @param inputRef - A ref for the HTML input element.\n */\nexport function useCheckboxGroupItem(props: AriaCheckboxGroupItemProps, state: CheckboxGroupState, inputRef: RefObject<HTMLInputElement | null>): CheckboxAria {\n  const toggleState = useToggleState({\n    isReadOnly: props.isReadOnly || state.isReadOnly,\n    isSelected: state.isSelected(props.value),\n    defaultSelected: state.defaultValue.includes(props.value),\n    onChange(isSelected) {\n      if (isSelected) {\n        state.addValue(props.value);\n      } else {\n        state.removeValue(props.value);\n      }\n\n      if (props.onChange) {\n        props.onChange(isSelected);\n      }\n    }\n  });\n\n  let {name, form, descriptionId, errorMessageId, validationBehavior} = checkboxGroupData.get(state)!;\n  validationBehavior = props.validationBehavior ?? validationBehavior;\n\n  // Local validation for this checkbox.\n  let {realtimeValidation} = useFormValidationState({\n    ...props,\n    value: toggleState.isSelected,\n    // Server validation is handled at the group level.\n    name: undefined,\n    validationBehavior: 'aria'\n  });\n\n  // Update the checkbox group state when realtime validation changes.\n  let nativeValidation = useRef(DEFAULT_VALIDATION_RESULT);\n  let updateValidation = () => {\n    state.setInvalid(props.value, realtimeValidation.isInvalid ? realtimeValidation : nativeValidation.current);\n  };\n\n  useEffect(updateValidation);\n\n  // Combine group and checkbox level validation.\n  let combinedRealtimeValidation = state.realtimeValidation.isInvalid ? state.realtimeValidation : realtimeValidation;\n  let displayValidation = validationBehavior === 'native' ? state.displayValidation : combinedRealtimeValidation;\n\n  let res = useCheckbox({\n    ...props,\n    isReadOnly: props.isReadOnly || state.isReadOnly,\n    isDisabled: props.isDisabled || state.isDisabled,\n    name: props.name || name,\n    form: props.form || form,\n    isRequired: props.isRequired ?? state.isRequired,\n    validationBehavior,\n    [privateValidationStateProp]: {\n      realtimeValidation: combinedRealtimeValidation,\n      displayValidation,\n      resetValidation: state.resetValidation,\n      commitValidation: state.commitValidation,\n      updateValidation(v: ValidationResult) {\n        nativeValidation.current = v;\n        updateValidation();\n      }\n    }\n  }, toggleState, inputRef);\n\n  return {\n    ...res,\n    inputProps: {\n      ...res.inputProps,\n      'aria-describedby': [\n        props['aria-describedby'],\n        state.isInvalid ? errorMessageId : null,\n        descriptionId\n      ].filter(Boolean).join(' ') || undefined\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAkBM,SAAS,0CAAqB,KAAiC,EAAE,KAAyB,EAAE,QAA4C;IAC7I,MAAM,cAAc,CAAA,2KAAA,iBAAa,EAAE;QACjC,YAAY,MAAM,UAAU,IAAI,MAAM,UAAU;QAChD,YAAY,MAAM,UAAU,CAAC,MAAM,KAAK;QACxC,iBAAiB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,KAAK;QACxD,UAAS,UAAU;YACjB,IAAI,YACF,MAAM,QAAQ,CAAC,MAAM,KAAK;iBAE1B,MAAM,WAAW,CAAC,MAAM,KAAK;YAG/B,IAAI,MAAM,QAAQ,EAChB,MAAM,QAAQ,CAAC;QAEnB;IACF;IAEA,IAAI,EAAA,MAAC,IAAI,EAAA,MAAE,IAAI,EAAA,eAAE,aAAa,EAAA,gBAAE,cAAc,EAAA,oBAAE,kBAAkB,EAAC,GAAG,CAAA,iKAAA,oBAAgB,EAAE,GAAG,CAAC;QACvE;IAArB,qBAAqB,CAAA,4BAAA,MAAM,kBAAkB,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAA4B;IAEjD,sCAAsC;IACtC,IAAI,EAAA,oBAAC,kBAAkB,EAAC,GAAG,CAAA,iLAAA,yBAAqB,EAAE;QAChD,GAAG,KAAK;QACR,OAAO,YAAY,UAAU;QAC7B,mDAAmD;QACnD,MAAM;QACN,oBAAoB;IACtB;IAEA,oEAAoE;IACpE,IAAI,mBAAmB,CAAA,yMAAA,SAAK,EAAE,CAAA,iLAAA,4BAAwB;IACtD,IAAI,mBAAmB;QACrB,MAAM,UAAU,CAAC,MAAM,KAAK,EAAE,mBAAmB,SAAS,GAAG,qBAAqB,iBAAiB,OAAO;IAC5G;IAEA,CAAA,yMAAA,YAAQ,EAAE;IAEV,+CAA+C;IAC/C,IAAI,6BAA6B,MAAM,kBAAkB,CAAC,SAAS,GAAG,MAAM,kBAAkB,GAAG;IACjG,IAAI,oBAAoB,uBAAuB,WAAW,MAAM,iBAAiB,GAAG;QAQtE;IANd,IAAI,MAAM,CAAA,uKAAA,cAAU,EAAE;QACpB,GAAG,KAAK;QACR,YAAY,MAAM,UAAU,IAAI,MAAM,UAAU;QAChD,YAAY,MAAM,UAAU,IAAI,MAAM,UAAU;QAChD,MAAM,MAAM,IAAI,IAAI;QACpB,MAAM,MAAM,IAAI,IAAI;QACpB,YAAY,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB,MAAM,UAAU;4BAChD;QACA,CAAC,CAAA,iLAAA,6BAAyB,EAAE,EAAE;YAC5B,oBAAoB;+BACpB;YACA,iBAAiB,MAAM,eAAe;YACtC,kBAAkB,MAAM,gBAAgB;YACxC,kBAAiB,CAAmB;gBAClC,iBAAiB,OAAO,GAAG;gBAC3B;YACF;QACF;IACF,GAAG,aAAa;IAEhB,OAAO;QACL,GAAG,GAAG;QACN,YAAY;YACV,GAAG,IAAI,UAAU;YACjB,oBAAoB;gBAClB,KAAK,CAAC,mBAAmB;gBACzB,MAAM,SAAS,GAAG,iBAAiB;gBACnC;aACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;QACjC;IACF;AACF", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {TableState} from '@react-stately/table';\n\nexport const gridIds: WeakMap<TableState<unknown>, string> = new WeakMap<TableState<unknown>, string>();\n\nfunction normalizeKey(key: Key): string {\n  if (typeof key === 'string') {\n    return key.replace(/\\s*/g, '');\n  }\n\n  return '' + key;\n}\n\nexport function getColumnHeaderId<T>(state: TableState<T>, columnKey: Key): string {\n  let gridId = gridIds.get(state);\n  if (!gridId) {\n    throw new Error('Unknown grid');\n  }\n\n  return `${gridId}-${normalizeKey(columnKey)}`;\n}\n\nexport function getCellId<T>(state: TableState<T>, rowKey: Key, columnKey: Key): string {\n  let gridId = gridIds.get(state);\n  if (!gridId) {\n    throw new Error('Unknown grid');\n  }\n\n  return `${gridId}-${normalizeKey(rowKey)}-${normalizeKey(columnKey)}`;\n}\n\nexport function getRowLabelledBy<T>(state: TableState<T>, rowKey: Key): string {\n  // A row is labelled by it's row headers.\n  return [...state.collection.rowHeaderColumnKeys].map(columnKey =>\n    getCellId(state, rowKey, columnKey)\n  ).join(' ');\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;AAKM,MAAM,4CAAgD,IAAI;AAEjE,SAAS,mCAAa,GAAQ;IAC5B,IAAI,OAAO,QAAQ,UACjB,OAAO,IAAI,OAAO,CAAC,QAAQ;IAG7B,OAAO,KAAK;AACd;AAEO,SAAS,0CAAqB,KAAoB,EAAE,SAAc;IACvE,IAAI,SAAS,0CAAQ,GAAG,CAAC;IACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,OAAO,CAAC,EAAE,mCAAa,YAAY;AAC/C;AAEO,SAAS,0CAAa,KAAoB,EAAE,MAAW,EAAE,SAAc;IAC5E,IAAI,SAAS,0CAAQ,GAAG,CAAC;IACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM;IAGlB,OAAO,GAAG,OAAO,CAAC,EAAE,mCAAa,QAAQ,CAAC,EAAE,mCAAa,YAAY;AACvE;AAEO,SAAS,yCAAoB,KAAoB,EAAE,MAAW;IACnE,yCAAyC;IACzC,OAAO;WAAI,MAAM,UAAU,CAAC,mBAAmB;KAAC,CAAC,GAAG,CAAC,CAAA,YACnD,0CAAU,OAAO,QAAQ,YACzB,IAAI,CAAC;AACT", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/ar-AE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/ar-AE.json"], "sourcesContent": ["{\n  \"ascending\": \"تصاعدي\",\n  \"ascendingSort\": \"ترتيب حسب العمود {columnName} بترتيب تصاعدي\",\n  \"columnSize\": \"{value} بالبكسل\",\n  \"descending\": \"تنازلي\",\n  \"descendingSort\": \"ترتيب حسب العمود {columnName} بترتيب تنازلي\",\n  \"resizerDescription\": \"اضغط على مفتاح Enter لبدء تغيير الحجم\",\n  \"select\": \"تحديد\",\n  \"selectAll\": \"تحديد الكل\",\n  \"sortable\": \"عمود قابل للترتيب\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,0CAAM,CAAC;IACvC,iBAAiB,CAAC,OAAS,CAAC,qGAAiB,EAAE,KAAK,UAAU,CAAC,sFAAc,CAAC;IAC9E,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,kDAAQ,CAAC;IAC/C,cAAc,CAAC,0CAAM,CAAC;IACtB,kBAAkB,CAAC,OAAS,CAAC,qGAAiB,EAAE,KAAK,UAAU,CAAC,sFAAc,CAAC;IAC/E,sBAAsB,CAAC,iMAAqC,CAAC;IAC7D,UAAU,CAAC,mCAAK,CAAC;IACjB,aAAa,CAAC,gEAAU,CAAC;IACzB,YAAY,CAAC,2GAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/bg-BG.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/bg-BG.json"], "sourcesContent": ["{\n  \"ascending\": \"възходящ\",\n  \"ascendingSort\": \"сортирано по колона {columnName} във възходящ ред\",\n  \"columnSize\": \"{value} пиксела\",\n  \"descending\": \"низходящ\",\n  \"descendingSort\": \"сортирано по колона {columnName} в низходящ ред\",\n  \"resizerDescription\": \"Натиснете „Enter“, за да започнете да преоразмерявате\",\n  \"select\": \"Изберете\",\n  \"selectAll\": \"Изберете всичко\",\n  \"sortable\": \"сортираща колона\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,wDAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,0HAAoB,EAAE,KAAK,UAAU,CAAC,qGAAiB,CAAC;IACpF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,kDAAQ,CAAC;IAC/C,cAAc,CAAC,wDAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,0HAAoB,EAAE,KAAK,UAAU,CAAC,uFAAe,CAAC;IACnF,sBAAsB,CAAC,6SAAqD,CAAC;IAC7E,UAAU,CAAC,wDAAQ,CAAC;IACpB,aAAa,CAAC,mGAAe,CAAC;IAC9B,YAAY,CAAC,0GAAgB,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/cs-CZ.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"ascending\": \"vzestupně\",\n  \"ascendingSort\": \"řazeno vzestupně podle sloupce {columnName}\",\n  \"columnSize\": \"{value} pixelů\",\n  \"descending\": \"sestupně\",\n  \"descendingSort\": \"řazeno sestupně podle sloupce {columnName}\",\n  \"resizerDescription\": \"Stisknutím klávesy Enter začnete měnit velikost\",\n  \"select\": \"Vybrat\",\n  \"selectAll\": \"Vybrat vše\",\n  \"sortable\": \"sloupec s možností řazení\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,eAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,2CAA+B,EAAE,KAAK,UAAU,EAAE;IAC9E,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,aAAO,CAAC;IAC9C,cAAc,CAAC,cAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,0CAA8B,EAAE,KAAK,UAAU,EAAE;IAC9E,sBAAsB,CAAC,iEAA+C,CAAC;IACvE,UAAU,CAAC,MAAM,CAAC;IAClB,aAAa,CAAC,gBAAU,CAAC;IACzB,YAAY,CAAC,2CAAyB,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/da-DK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/da-DK.json"], "sourcesContent": ["{\n  \"ascending\": \"stigende\",\n  \"ascendingSort\": \"sorteret efter kolonne {columnName} i stigende rækkefølge\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"faldende\",\n  \"descendingSort\": \"sorteret efter kolonne {columnName} i faldende rækkefølge\",\n  \"resizerDescription\": \"Tryk på Enter for at ændre størrelse\",\n  \"select\": \"Vælg\",\n  \"selectAll\": \"Vælg alle\",\n  \"sortable\": \"sorterbar kolonne\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,QAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,uBAAuB,EAAE,KAAK,UAAU,CAAC,4BAAsB,CAAC;IAC5F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,uBAAuB,EAAE,KAAK,UAAU,CAAC,4BAAsB,CAAC;IAC7F,sBAAsB,CAAC,6CAAoC,CAAC;IAC5D,UAAU,CAAC,OAAI,CAAC;IAChB,aAAa,CAAC,YAAS,CAAC;IACxB,YAAY,CAAC,iBAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/de-DE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/de-DE.json"], "sourcesContent": ["{\n  \"ascending\": \"aufsteigend\",\n  \"ascendingSort\": \"sortiert nach Spalte {columnName} in aufsteigender Reihenfolge\",\n  \"columnSize\": \"{value} Pixel\",\n  \"descending\": \"absteigend\",\n  \"descendingSort\": \"sortiert nach Spalte {columnName} in absteigender Reihenfolge\",\n  \"resizerDescription\": \"Eingabetaste zum Starten der Größenänderung drücken\",\n  \"select\": \"Auswählen\",\n  \"selectAll\": \"Alles auswählen\",\n  \"sortable\": \"sortierbare Spalte\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,WAAW,CAAC;IAC5C,iBAAiB,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,UAAU,CAAC,6BAA6B,CAAC;IACjG,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC;IAC7C,cAAc,CAAC,UAAU,CAAC;IAC1B,kBAAkB,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,UAAU,CAAC,4BAA4B,CAAC;IACjG,sBAAsB,CAAC,+DAAmD,CAAC;IAC3E,UAAU,CAAC,YAAS,CAAC;IACrB,aAAa,CAAC,kBAAe,CAAC;IAC9B,YAAY,CAAC,kBAAkB,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/el-GR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/el-GR.json"], "sourcesContent": ["{\n  \"ascending\": \"αύξουσα\",\n  \"ascendingSort\": \"διαλογή ανά στήλη {columnName} σε αύξουσα σειρά\",\n  \"columnSize\": \"{value} pixel\",\n  \"descending\": \"φθίνουσα\",\n  \"descendingSort\": \"διαλογή ανά στήλη {columnName} σε φθίνουσα σειρά\",\n  \"resizerDescription\": \"Πατήστε Enter για έναρξη της αλλαγής μεγέθους\",\n  \"select\": \"Επιλογή\",\n  \"selectAll\": \"Επιλογή όλων\",\n  \"sortable\": \"Στήλη διαλογής\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,iDAAO,CAAC;IACxC,iBAAiB,CAAC,OAAS,CAAC,4GAAkB,EAAE,KAAK,UAAU,CAAC,qGAAiB,CAAC;IAClF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC;IAC7C,cAAc,CAAC,wDAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,4GAAkB,EAAE,KAAK,UAAU,CAAC,4GAAkB,CAAC;IACpF,sBAAsB,CAAC,yPAA6C,CAAC;IACrE,UAAU,CAAC,iDAAO,CAAC;IACnB,aAAa,CAAC,8EAAY,CAAC;IAC3B,YAAY,CAAC,4FAAc,CAAC;AAC9B", "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/en-US.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/en-US.json"], "sourcesContent": ["{\n  \"select\": \"Select\",\n  \"selectAll\": \"Select All\",\n  \"sortable\": \"sortable column\",\n  \"ascending\": \"ascending\",\n  \"descending\": \"descending\",\n  \"ascendingSort\": \"sorted by column {columnName} in ascending order\",\n  \"descendingSort\": \"sorted by column {columnName} in descending order\",\n  \"columnSize\": \"{value} pixels\",\n  \"resizerDescription\": \"Press Enter to start resizing\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,UAAU,CAAC,MAAM,CAAC;IACpC,aAAa,CAAC,UAAU,CAAC;IACzB,YAAY,CAAC,eAAe,CAAC;IAC7B,aAAa,CAAC,SAAS,CAAC;IACxB,cAAc,CAAC,UAAU,CAAC;IAC1B,iBAAiB,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,mBAAmB,CAAC;IACnF,kBAAkB,CAAC,OAAS,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,oBAAoB,CAAC;IACrF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,sBAAsB,CAAC,6BAA6B,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/es-ES.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/es-ES.json"], "sourcesContent": ["{\n  \"ascending\": \"ascendente\",\n  \"ascendingSort\": \"ordenado por columna {columnName} en sentido ascendente\",\n  \"columnSize\": \"{value} píxeles\",\n  \"descending\": \"descendente\",\n  \"descendingSort\": \"ordenado por columna {columnName} en orden descendente\",\n  \"resizerDescription\": \"Pulse Intro para empezar a redimensionar\",\n  \"select\": \"Seleccionar\",\n  \"selectAll\": \"Seleccionar todos\",\n  \"sortable\": \"columna ordenable\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,UAAU,CAAC;IAC3C,iBAAiB,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,UAAU,CAAC,sBAAsB,CAAC;IAC1F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,WAAQ,CAAC;IAC/C,cAAc,CAAC,WAAW,CAAC;IAC3B,kBAAkB,CAAC,OAAS,CAAC,qBAAqB,EAAE,KAAK,UAAU,CAAC,qBAAqB,CAAC;IAC1F,sBAAsB,CAAC,wCAAwC,CAAC;IAChE,UAAU,CAAC,WAAW,CAAC;IACvB,aAAa,CAAC,iBAAiB,CAAC;IAChC,YAAY,CAAC,iBAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/et-EE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/et-EE.json"], "sourcesContent": ["{\n  \"ascending\": \"tõusev järjestus\",\n  \"ascendingSort\": \"sorditud veeru järgi {columnName} tõusvas järjestuses\",\n  \"columnSize\": \"{value} pikslit\",\n  \"descending\": \"laskuv järjestus\",\n  \"descendingSort\": \"sorditud veeru järgi {columnName} laskuvas järjestuses\",\n  \"resizerDescription\": \"Suuruse muutmise alustamiseks vajutage klahvi Enter\",\n  \"select\": \"Vali\",\n  \"selectAll\": \"Vali kõik\",\n  \"sortable\": \"sorditav veerg\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,sBAAgB,CAAC;IACjD,iBAAiB,CAAC,OAAS,CAAC,wBAAqB,EAAE,KAAK,UAAU,CAAC,0BAAoB,CAAC;IACxF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC/C,cAAc,CAAC,mBAAgB,CAAC;IAChC,kBAAkB,CAAC,OAAS,CAAC,wBAAqB,EAAE,KAAK,UAAU,CAAC,wBAAqB,CAAC;IAC1F,sBAAsB,CAAC,mDAAmD,CAAC;IAC3E,UAAU,CAAC,IAAI,CAAC;IAChB,aAAa,CAAC,YAAS,CAAC;IACxB,YAAY,CAAC,cAAc,CAAC;AAC9B", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/fi-FI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/fi-FI.json"], "sourcesContent": ["{\n  \"ascending\": \"nouseva\",\n  \"ascendingSort\": \"lajiteltu sarakkeen {columnName} mukaan nousevassa järjestyksessä\",\n  \"columnSize\": \"{value} pikseliä\",\n  \"descending\": \"laskeva\",\n  \"descendingSort\": \"lajiteltu sarakkeen {columnName} mukaan laskevassa järjestyksessä\",\n  \"resizerDescription\": \"Aloita koon muutos painamalla Enter-näppäintä\",\n  \"select\": \"Valitse\",\n  \"selectAll\": \"Valitse kaikki\",\n  \"sortable\": \"lajiteltava sarake\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,OAAO,CAAC;IACxC,iBAAiB,CAAC,OAAS,CAAC,oBAAoB,EAAE,KAAK,UAAU,CAAC,uCAAiC,CAAC;IACpG,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,YAAS,CAAC;IAChD,cAAc,CAAC,OAAO,CAAC;IACvB,kBAAkB,CAAC,OAAS,CAAC,oBAAoB,EAAE,KAAK,UAAU,CAAC,uCAAiC,CAAC;IACrG,sBAAsB,CAAC,sDAA6C,CAAC;IACrE,UAAU,CAAC,OAAO,CAAC;IACnB,aAAa,CAAC,cAAc,CAAC;IAC7B,YAAY,CAAC,kBAAkB,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/fr-FR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/fr-FR.json"], "sourcesContent": ["{\n  \"ascending\": \"croissant\",\n  \"ascendingSort\": \"trié en fonction de la colonne {columnName} par ordre croissant\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"décroissant\",\n  \"descendingSort\": \"trié en fonction de la colonne {columnName} par ordre décroissant\",\n  \"resizerDescription\": \"Appuyez sur Entrée pour commencer le redimensionnement.\",\n  \"select\": \"Sélectionner\",\n  \"selectAll\": \"Sélectionner tout\",\n  \"sortable\": \"colonne triable\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,SAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,qCAA+B,EAAE,KAAK,UAAU,CAAC,oBAAoB,CAAC;IAClG,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,UAAO,CAAC;IAC9C,cAAc,CAAC,cAAW,CAAC;IAC3B,kBAAkB,CAAC,OAAS,CAAC,qCAA+B,EAAE,KAAK,UAAU,CAAC,yBAAsB,CAAC;IACrG,sBAAsB,CAAC,0DAAuD,CAAC;IAC/E,UAAU,CAAC,eAAY,CAAC;IACxB,aAAa,CAAC,oBAAiB,CAAC;IAChC,YAAY,CAAC,eAAe,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/he-IL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/he-IL.json"], "sourcesContent": ["{\n  \"ascending\": \"עולה\",\n  \"ascendingSort\": \"מוין לפי עמודה {columnName} בסדר עולה\",\n  \"columnSize\": \"{value} פיקסלים\",\n  \"descending\": \"יורד\",\n  \"descendingSort\": \"מוין לפי עמודה {columnName} בסדר יורד\",\n  \"resizerDescription\": \"הקש Enter כדי לשנות את הגודל\",\n  \"select\": \"בחר\",\n  \"selectAll\": \"בחר הכול\",\n  \"sortable\": \"עמודה שניתן למיין\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,4BAAI,CAAC;IACrC,iBAAiB,CAAC,OAAS,CAAC,uFAAe,EAAE,KAAK,UAAU,CAAC,0DAAU,CAAC;IACxE,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,kDAAQ,CAAC;IAC/C,cAAc,CAAC,4BAAI,CAAC;IACpB,kBAAkB,CAAC,OAAS,CAAC,uFAAe,EAAE,KAAK,UAAU,CAAC,0DAAU,CAAC;IACzE,sBAAsB,CAAC,wIAA4B,CAAC;IACpD,UAAU,CAAC,qBAAG,CAAC;IACf,aAAa,CAAC,kDAAQ,CAAC;IACvB,YAAY,CAAC,2GAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/hr-HR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/hr-HR.json"], "sourcesContent": ["{\n  \"ascending\": \"rastući\",\n  \"ascendingSort\": \"razvrstano po stupcima {columnName} rastućem redoslijedom\",\n  \"columnSize\": \"{value} piksela\",\n  \"descending\": \"padaju<PERSON>i\",\n  \"descendingSort\": \"razvrstano po stupcima {columnName} padajućim redoslijedom\",\n  \"resizerDescription\": \"Pritisnite Enter da biste započeli promenu veličine\",\n  \"select\": \"Odaberite\",\n  \"selectAll\": \"Odaberite sve\",\n  \"sortable\": \"stupac koji se može razvrstati\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,aAAO,CAAC;IACxC,iBAAiB,CAAC,OAAS,CAAC,uBAAuB,EAAE,KAAK,UAAU,CAAC,4BAAsB,CAAC;IAC5F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC/C,cAAc,CAAC,cAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,uBAAuB,EAAE,KAAK,UAAU,CAAC,6BAAuB,CAAC;IAC9F,sBAAsB,CAAC,+DAAmD,CAAC;IAC3E,UAAU,CAAC,SAAS,CAAC;IACrB,aAAa,CAAC,aAAa,CAAC;IAC5B,YAAY,CAAC,oCAA8B,CAAC;AAC9C", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/hu-HU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/hu-HU.json"], "sourcesContent": ["{\n  \"ascending\": \"növekv<PERSON>\",\n  \"ascendingSort\": \"rendezve a(z) {columnName} oszlop szerint, növekvő sorrendben\",\n  \"columnSize\": \"{value} képpont\",\n  \"descending\": \"cs<PERSON><PERSON><PERSON><PERSON>\",\n  \"descendingSort\": \"rendezve a(z) {columnName} oszlop szerint, csökken<PERSON> sorrendben\",\n  \"resizerDescription\": \"Nyomja le az Enter billentyűt az átméretezés megkezdéséhez\",\n  \"select\": \"Kijelölés\",\n  \"selectAll\": \"Összes kijelölése\",\n  \"sortable\": \"rendezend<PERSON> oszlop\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,gBAAO,CAAC;IACxC,iBAAiB,CAAC,OAAS,CAAC,cAAc,EAAE,KAAK,UAAU,CAAC,4CAAmC,CAAC;IAChG,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,WAAQ,CAAC;IAC/C,cAAc,CAAC,iBAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,cAAc,EAAE,KAAK,UAAU,CAAC,6CAAoC,CAAC;IAClG,sBAAsB,CAAC,+EAA0D,CAAC;IAClF,UAAU,CAAC,eAAS,CAAC;IACrB,aAAa,CAAC,0BAAiB,CAAC;IAChC,YAAY,CAAC,uBAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/it-IT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/it-IT.json"], "sourcesContent": ["{\n  \"ascending\": \"crescente\",\n  \"ascendingSort\": \"in ordine crescente in base alla colonna {columnName}\",\n  \"columnSize\": \"{value} pixel\",\n  \"descending\": \"decrescente\",\n  \"descendingSort\": \"in ordine decrescente in base alla colonna {columnName}\",\n  \"resizerDescription\": \"Premi Invio per iniziare a ridimensionare\",\n  \"select\": \"Seleziona\",\n  \"selectAll\": \"Seleziona tutto\",\n  \"sortable\": \"colonna ordinabile\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,SAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,yCAAyC,EAAE,KAAK,UAAU,EAAE;IACxF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC;IAC7C,cAAc,CAAC,WAAW,CAAC;IAC3B,kBAAkB,CAAC,OAAS,CAAC,2CAA2C,EAAE,KAAK,UAAU,EAAE;IAC3F,sBAAsB,CAAC,yCAAyC,CAAC;IACjE,UAAU,CAAC,SAAS,CAAC;IACrB,aAAa,CAAC,eAAe,CAAC;IAC9B,YAAY,CAAC,kBAAkB,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/ja-JP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/ja-JP.json"], "sourcesContent": ["{\n  \"ascending\": \"昇順\",\n  \"ascendingSort\": \"列 {columnName} を昇順で並べ替え\",\n  \"columnSize\": \"{value} ピクセル\",\n  \"descending\": \"降順\",\n  \"descendingSort\": \"列 {columnName} を降順で並べ替え\",\n  \"resizerDescription\": \"Enter キーを押してサイズ変更を開始\",\n  \"select\": \"選択\",\n  \"selectAll\": \"すべて選択\",\n  \"sortable\": \"並べ替え可能な列\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,gBAAE,CAAC;IACnC,iBAAiB,CAAC,OAAS,CAAC,SAAE,EAAE,KAAK,UAAU,CAAC,iEAAS,CAAC;IAC1D,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,iCAAK,CAAC;IAC5C,cAAc,CAAC,gBAAE,CAAC;IAClB,kBAAkB,CAAC,OAAS,CAAC,SAAE,EAAE,KAAK,UAAU,CAAC,iEAAS,CAAC;IAC3D,sBAAsB,CAAC,sHAAoB,CAAC;IAC5C,UAAU,CAAC,gBAAE,CAAC;IACd,aAAa,CAAC,wCAAK,CAAC;IACpB,YAAY,CAAC,gEAAQ,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/ko-KR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/ko-KR.json"], "sourcesContent": ["{\n  \"ascending\": \"오름차순\",\n  \"ascendingSort\": \"{columnName} 열을 기준으로 오름차순으로 정렬됨\",\n  \"columnSize\": \"{value} 픽셀\",\n  \"descending\": \"내림차순\",\n  \"descendingSort\": \"{columnName} 열을 기준으로 내림차순으로 정렬됨\",\n  \"resizerDescription\": \"크기 조정을 시작하려면 Enter를 누르세요.\",\n  \"select\": \"선택\",\n  \"selectAll\": \"모두 선택\",\n  \"sortable\": \"정렬 가능한 열\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,gCAAI,CAAC;IACrC,iBAAiB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,4HAAmB,CAAC;IAClE,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,iBAAG,CAAC;IAC1C,cAAc,CAAC,gCAAI,CAAC;IACpB,kBAAkB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,4HAAmB,CAAC;IACnE,sBAAsB,CAAC,kIAAyB,CAAC;IACjD,UAAU,CAAC,gBAAE,CAAC;IACd,aAAa,CAAC,iCAAK,CAAC;IACpB,YAAY,CAAC,kDAAQ,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/lt-LT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/lt-LT.json"], "sourcesContent": ["{\n  \"ascending\": \"did<PERSON><PERSON><PERSON>ia tvarka\",\n  \"ascendingSort\": \"surikiuota pagal stulpelį {columnName} did<PERSON><PERSON><PERSON>ia tvarka\",\n  \"columnSize\": \"{value} piks.\",\n  \"descending\": \"mažėjančia tvarka\",\n  \"descendingSort\": \"surikiuota pagal stulpelį {columnName} mažėjančia tvarka\",\n  \"resizerDescription\": \"Paspauskite „Enter“, kad pradėtumėte keisti dydį\",\n  \"select\": \"Pasirinkti\",\n  \"selectAll\": \"Pasirinkti viską\",\n  \"sortable\": \"rikiuo<PERSON><PERSON> stulpelis\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,6BAAiB,CAAC;IAClD,iBAAiB,CAAC,OAAS,CAAC,gCAA0B,EAAE,KAAK,UAAU,CAAC,8BAAkB,CAAC;IAC3F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC;IAC7C,cAAc,CAAC,mCAAiB,CAAC;IACjC,kBAAkB,CAAC,OAAS,CAAC,gCAA0B,EAAE,KAAK,UAAU,CAAC,oCAAkB,CAAC;IAC5F,sBAAsB,CAAC,gFAAgD,CAAC;IACxE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,sBAAgB,CAAC;IAC/B,YAAY,CAAC,qBAAqB,CAAC;AACrC", "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/lv-LV.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/lv-LV.json"], "sourcesContent": ["{\n  \"ascending\": \"augošā secībā\",\n  \"ascendingSort\": \"kārtots pēc kolonnas {columnName} augošā secībā\",\n  \"columnSize\": \"{value} pikseļi\",\n  \"descending\": \"dilsto<PERSON>ā secībā\",\n  \"descendingSort\": \"kārtots pēc kolonnas {columnName} dilstošā secībā\",\n  \"resizerDescription\": \"Nospiediet Enter, lai sāktu izmēru mainīšanu\",\n  \"select\": \"Atlasīt\",\n  \"selectAll\": \"Atlasīt visu\",\n  \"sortable\": \"kārtojamā kolonna\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,qCAAa,CAAC;IAC9C,iBAAiB,CAAC,OAAS,CAAC,iCAAqB,EAAE,KAAK,UAAU,CAAC,sCAAc,CAAC;IAClF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,cAAQ,CAAC;IAC/C,cAAc,CAAC,uCAAe,CAAC;IAC/B,kBAAkB,CAAC,OAAS,CAAC,iCAAqB,EAAE,KAAK,UAAU,CAAC,wCAAgB,CAAC;IACrF,sBAAsB,CAAC,oEAA4C,CAAC;IACpE,UAAU,CAAC,aAAO,CAAC;IACnB,aAAa,CAAC,kBAAY,CAAC;IAC3B,YAAY,CAAC,6BAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/nb-NO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/nb-NO.json"], "sourcesContent": ["{\n  \"ascending\": \"stigende\",\n  \"ascendingSort\": \"sortert etter kolonne {columnName} i stigende rekkefølge\",\n  \"columnSize\": \"{value} piksler\",\n  \"descending\": \"synkende\",\n  \"descendingSort\": \"sortert etter kolonne {columnName} i synkende rekkefølge\",\n  \"resizerDescription\": \"Trykk på Enter for å starte størrelsesendring\",\n  \"select\": \"Velg\",\n  \"selectAll\": \"Velg alle\",\n  \"sortable\": \"kolonne som kan sorteres\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,QAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,sBAAsB,EAAE,KAAK,UAAU,CAAC,yBAAsB,CAAC;IAC3F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC/C,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,sBAAsB,EAAE,KAAK,UAAU,CAAC,yBAAsB,CAAC;IAC5F,sBAAsB,CAAC,sDAA6C,CAAC;IACrE,UAAU,CAAC,IAAI,CAAC;IAChB,aAAa,CAAC,SAAS,CAAC;IACxB,YAAY,CAAC,wBAAwB,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/nl-NL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/nl-NL.json"], "sourcesContent": ["{\n  \"ascending\": \"oplopend\",\n  \"ascendingSort\": \"gesorteerd in oplopende volgorde in kolom {columnName}\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"aflopend\",\n  \"descendingSort\": \"gesorteerd in aflopende volgorde in kolom {columnName}\",\n  \"resizerDescription\": \"Druk op Enter om het formaat te wijzigen\",\n  \"select\": \"Selecteren\",\n  \"selectAll\": \"Alles selecteren\",\n  \"sortable\": \"sorteerbare kolom\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,QAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,0CAA0C,EAAE,KAAK,UAAU,EAAE;IACzF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,0CAA0C,EAAE,KAAK,UAAU,EAAE;IAC1F,sBAAsB,CAAC,wCAAwC,CAAC;IAChE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,gBAAgB,CAAC;IAC/B,YAAY,CAAC,iBAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1365, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/pl-PL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/pl-PL.json"], "sourcesContent": ["{\n  \"ascending\": \"rosnąco\",\n  \"ascendingSort\": \"posortowano według kolumny {columnName} w porządku rosnącym\",\n  \"columnSize\": \"Liczba pikseli: {value}\",\n  \"descending\": \"malejąco\",\n  \"descendingSort\": \"posortowano według kolumny {columnName} w porządku malejącym\",\n  \"resizerDescription\": \"Naciśnij Enter, aby rozpocząć zmienianie rozmiaru\",\n  \"select\": \"Zaznacz\",\n  \"selectAll\": \"Zaznacz wszystko\",\n  \"sortable\": \"kolumna z możliwością sortowania\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,aAAO,CAAC;IACxC,iBAAiB,CAAC,OAAS,CAAC,iCAA2B,EAAE,KAAK,UAAU,CAAC,gCAAoB,CAAC;IAC9F,cAAc,CAAC,OAAS,CAAC,gBAAgB,EAAE,KAAK,KAAK,EAAE;IACvD,cAAc,CAAC,cAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,iCAA2B,EAAE,KAAK,UAAU,CAAC,iCAAqB,CAAC;IAChG,sBAAsB,CAAC,mEAAiD,CAAC;IACzE,UAAU,CAAC,OAAO,CAAC;IACnB,aAAa,CAAC,gBAAgB,CAAC;IAC/B,YAAY,CAAC,kDAAgC,CAAC;AAChD", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/pt-BR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/pt-BR.json"], "sourcesContent": ["{\n  \"ascending\": \"crescente\",\n  \"ascendingSort\": \"classificado pela coluna {columnName} em ordem crescente\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"decrescente\",\n  \"descendingSort\": \"classificado pela coluna {columnName} em ordem decrescente\",\n  \"resizerDescription\": \"Pressione Enter para começar a redimensionar\",\n  \"select\": \"Selecionar\",\n  \"selectAll\": \"Selecionar tudo\",\n  \"sortable\": \"coluna classificável\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,SAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,yBAAyB,EAAE,KAAK,UAAU,CAAC,mBAAmB,CAAC;IAC3F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,WAAW,CAAC;IAC3B,kBAAkB,CAAC,OAAS,CAAC,yBAAyB,EAAE,KAAK,UAAU,CAAC,qBAAqB,CAAC;IAC9F,sBAAsB,CAAC,+CAA4C,CAAC;IACpE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,eAAe,CAAC;IAC9B,YAAY,CAAC,uBAAoB,CAAC;AACpC", "debugId": null}}, {"offset": {"line": 1407, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/pt-PT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/pt-PT.json"], "sourcesContent": ["{\n  \"ascending\": \"ascendente\",\n  \"ascendingSort\": \"Ordenar por coluna {columnName} em ordem ascendente\",\n  \"columnSize\": \"{value} pixels\",\n  \"descending\": \"descendente\",\n  \"descendingSort\": \"Ordenar por coluna {columnName} em ordem descendente\",\n  \"resizerDescription\": \"Prima Enter para iniciar o redimensionamento\",\n  \"select\": \"Selecionar\",\n  \"selectAll\": \"Selecionar tudo\",\n  \"sortable\": \"Coluna ordenável\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,UAAU,CAAC;IAC3C,iBAAiB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,UAAU,CAAC,oBAAoB,CAAC;IACtF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,WAAW,CAAC;IAC3B,kBAAkB,CAAC,OAAS,CAAC,mBAAmB,EAAE,KAAK,UAAU,CAAC,qBAAqB,CAAC;IACxF,sBAAsB,CAAC,4CAA4C,CAAC;IACpE,UAAU,CAAC,UAAU,CAAC;IACtB,aAAa,CAAC,eAAe,CAAC;IAC9B,YAAY,CAAC,mBAAgB,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/ro-RO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/ro-RO.json"], "sourcesContent": ["{\n  \"ascending\": \"crescătoare\",\n  \"ascendingSort\": \"sortate după coloana {columnName} în ordine crescătoare\",\n  \"columnSize\": \"{value} pixeli\",\n  \"descending\": \"descrescătoare\",\n  \"descendingSort\": \"sortate după coloana {columnName} în ordine descrescătoare\",\n  \"resizerDescription\": \"Apăsați pe Enter pentru a începe redimensionarea\",\n  \"select\": \"Selectare\",\n  \"selectAll\": \"Selectare totală\",\n  \"sortable\": \"coloană sortabilă\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,iBAAW,CAAC;IAC5C,iBAAiB,CAAC,OAAS,CAAC,2BAAqB,EAAE,KAAK,UAAU,CAAC,+BAAsB,CAAC;IAC1F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,oBAAc,CAAC;IAC9B,kBAAkB,CAAC,OAAS,CAAC,2BAAqB,EAAE,KAAK,UAAU,CAAC,kCAAyB,CAAC;IAC9F,sBAAsB,CAAC,+DAAgD,CAAC;IACxE,UAAU,CAAC,SAAS,CAAC;IACrB,aAAa,CAAC,sBAAgB,CAAC;IAC/B,YAAY,CAAC,6BAAiB,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 1449, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/ru-RU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/ru-RU.json"], "sourcesContent": ["{\n  \"ascending\": \"возрастание\",\n  \"ascendingSort\": \"сортировать столбец {columnName} в порядке возрастания\",\n  \"columnSize\": \"{value} пикс.\",\n  \"descending\": \"убывание\",\n  \"descendingSort\": \"сортировать столбец {columnName} в порядке убывания\",\n  \"resizerDescription\": \"Нажмите клавишу Enter для начала изменения размеров\",\n  \"select\": \"Выбрать\",\n  \"selectAll\": \"Выбрать все\",\n  \"sortable\": \"сортируемый столбец\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,6EAAW,CAAC;IAC5C,iBAAiB,CAAC,OAAS,CAAC,gIAAoB,EAAE,KAAK,UAAU,CAAC,wIAAsB,CAAC;IACzF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,8BAAM,CAAC;IAC7C,cAAc,CAAC,wDAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,gIAAoB,EAAE,KAAK,UAAU,CAAC,mHAAmB,CAAC;IACvF,sBAAsB,CAAC,mSAAmD,CAAC;IAC3E,UAAU,CAAC,iDAAO,CAAC;IACnB,aAAa,CAAC,uEAAW,CAAC;IAC1B,YAAY,CAAC,+HAAmB,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/sk-SK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/sk-SK.json"], "sourcesContent": ["{\n  \"ascending\": \"vzostupne\",\n  \"ascendingSort\": \"zoradené zostupne podľa stĺpca {columnName}\",\n  \"columnSize\": \"Počet pixelov: {value}\",\n  \"descending\": \"zostupne\",\n  \"descendingSort\": \"zoradené zostupne podľa stĺpca {columnName}\",\n  \"resizerDescription\": \"Stlačením klávesu Enter začnete zmenu veľkosti\",\n  \"select\": \"Vybrať\",\n  \"selectAll\": \"Vybrať všetko\",\n  \"sortable\": \"zoraditeľný stĺpec\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,SAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,8CAA+B,EAAE,KAAK,UAAU,EAAE;IAC9E,cAAc,CAAC,OAAS,CAAC,qBAAe,EAAE,KAAK,KAAK,EAAE;IACtD,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,8CAA+B,EAAE,KAAK,UAAU,EAAE;IAC/E,sBAAsB,CAAC,sEAA8C,CAAC;IACtE,UAAU,CAAC,YAAM,CAAC;IAClB,aAAa,CAAC,yBAAa,CAAC;IAC5B,YAAY,CAAC,iCAAkB,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/sl-SI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/sl-SI.json"], "sourcesContent": ["{\n  \"ascending\": \"nara<PERSON><PERSON><PERSON>o<PERSON><PERSON>\",\n  \"ascendingSort\": \"razvr<PERSON>č<PERSON> po stolpcu {columnName} v naraščajočem vrstnem redu\",\n  \"columnSize\": \"{value} slikovnih pik\",\n  \"descending\": \"padajoče\",\n  \"descendingSort\": \"razvrščeno po stolpcu {columnName} v padajočem vrstnem redu\",\n  \"resizerDescription\": \"Pritisnite tipko Enter da začnete spreminjati velikost\",\n  \"select\": \"Izberite\",\n  \"selectAll\": \"Izberite vse\",\n  \"sortable\": \"razvrstljivi stolpec\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,6BAAW,CAAC;IAC5C,iBAAiB,CAAC,OAAS,CAAC,kCAAsB,EAAE,KAAK,UAAU,CAAC,8CAA4B,CAAC;IACjG,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,cAAc,CAAC;IACrD,cAAc,CAAC,cAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,kCAAsB,EAAE,KAAK,UAAU,CAAC,+BAAyB,CAAC;IAC/F,sBAAsB,CAAC,4DAAsD,CAAC;IAC9E,UAAU,CAAC,QAAQ,CAAC;IACpB,aAAa,CAAC,YAAY,CAAC;IAC3B,YAAY,CAAC,oBAAoB,CAAC;AACpC", "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/sr-SP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/sr-SP.json"], "sourcesContent": ["{\n  \"ascending\": \"rastući\",\n  \"ascendingSort\": \"sortirano po kolonama {columnName} rastu<PERSON><PERSON> redosledom\",\n  \"columnSize\": \"{value} piksela\",\n  \"descending\": \"padaju<PERSON>i\",\n  \"descendingSort\": \"sortirano po kolonama {columnName} padaju<PERSON><PERSON> redosledom\",\n  \"resizerDescription\": \"Pritisnite Enter da biste započeli promenu veličine\",\n  \"select\": \"Izaberite\",\n  \"selectAll\": \"Izaberite sve\",\n  \"sortable\": \"kolona koja se može sortirati\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,aAAO,CAAC;IACxC,iBAAiB,CAAC,OAAS,CAAC,sBAAsB,EAAE,KAAK,UAAU,CAAC,0BAAoB,CAAC;IACzF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC;IAC/C,cAAc,CAAC,cAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,sBAAsB,EAAE,KAAK,UAAU,CAAC,2BAAqB,CAAC;IAC3F,sBAAsB,CAAC,+DAAmD,CAAC;IAC3E,UAAU,CAAC,SAAS,CAAC;IACrB,aAAa,CAAC,aAAa,CAAC;IAC5B,YAAY,CAAC,mCAA6B,CAAC;AAC7C", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/sv-SE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/sv-SE.json"], "sourcesContent": ["{\n  \"ascending\": \"stigande\",\n  \"ascendingSort\": \"sorterat på kolumn {columnName} i stigande ordning\",\n  \"columnSize\": \"{value} pixlar\",\n  \"descending\": \"fallande\",\n  \"descendingSort\": \"sorterat på kolumn {columnName} i fallande ordning\",\n  \"resizerDescription\": \"Tryck på Retur för att börja ä<PERSON> storlek\",\n  \"select\": \"Markera\",\n  \"selectAll\": \"Markera allt\",\n  \"sortable\": \"sorterbar kolumn\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,QAAQ,CAAC;IACzC,iBAAiB,CAAC,OAAS,CAAC,sBAAmB,EAAE,KAAK,UAAU,CAAC,mBAAmB,CAAC;IACrF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,QAAQ,CAAC;IACxB,kBAAkB,CAAC,OAAS,CAAC,sBAAmB,EAAE,KAAK,UAAU,CAAC,mBAAmB,CAAC;IACtF,sBAAsB,CAAC,sDAA0C,CAAC;IAClE,UAAU,CAAC,OAAO,CAAC;IACnB,aAAa,CAAC,YAAY,CAAC;IAC3B,YAAY,CAAC,gBAAgB,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 1554, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/tr-TR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/tr-TR.json"], "sourcesContent": ["{\n  \"ascending\": \"artan sırada\",\n  \"ascendingSort\": \"{columnName} sütuna göre artan düzende sırala\",\n  \"columnSize\": \"{value} piksel\",\n  \"descending\": \"azalan sırada\",\n  \"descendingSort\": \"{columnName} sütuna göre azalan düzende sırala\",\n  \"resizerDescription\": \"Yeniden boyutlandırmak için Enter'a basın\",\n  \"select\": \"Seç\",\n  \"selectAll\": \"Tü<PERSON>ün<PERSON> Seç\",\n  \"sortable\": \"Sıralanabilir sütun\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,kBAAY,CAAC;IAC7C,iBAAiB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,gDAAiC,CAAC;IAChF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,mBAAa,CAAC;IAC7B,kBAAkB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,iDAAkC,CAAC;IAClF,sBAAsB,CAAC,wDAAyC,CAAC;IACjE,UAAU,CAAC,MAAG,CAAC;IACf,aAAa,CAAC,sBAAU,CAAC;IACzB,YAAY,CAAC,4BAAmB,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/uk-UA.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/uk-UA.json"], "sourcesContent": ["{\n  \"ascending\": \"висхідний\",\n  \"ascendingSort\": \"відсортовано за стовпцем {columnName} у висхідному порядку\",\n  \"columnSize\": \"{value} пікс.\",\n  \"descending\": \"низхідний\",\n  \"descendingSort\": \"відсортовано за стовпцем {columnName} у низхідному порядку\",\n  \"resizerDescription\": \"Натисніть Enter, щоб почати зміну розміру\",\n  \"select\": \"Вибрати\",\n  \"selectAll\": \"Вибрати все\",\n  \"sortable\": \"сортувальний стовпець\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,+DAAS,CAAC;IAC1C,iBAAiB,CAAC,OAAS,CAAC,6JAAyB,EAAE,KAAK,UAAU,CAAC,iIAAqB,CAAC;IAC7F,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,8BAAM,CAAC;IAC7C,cAAc,CAAC,+DAAS,CAAC;IACzB,kBAAkB,CAAC,OAAS,CAAC,6JAAyB,EAAE,KAAK,UAAU,CAAC,iIAAqB,CAAC;IAC9F,sBAAsB,CAAC,6NAAyC,CAAC;IACjE,UAAU,CAAC,iDAAO,CAAC;IACnB,aAAa,CAAC,uEAAW,CAAC;IAC1B,YAAY,CAAC,6IAAqB,CAAC;AACrC", "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/zh-CN.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/zh-CN.json"], "sourcesContent": ["{\n  \"ascending\": \"升序\",\n  \"ascendingSort\": \"按列 {columnName} 升序排序\",\n  \"columnSize\": \"{value} 像素\",\n  \"descending\": \"降序\",\n  \"descendingSort\": \"按列 {columnName} 降序排序\",\n  \"resizerDescription\": \"按“输入”键开始调整大小。\",\n  \"select\": \"选择\",\n  \"selectAll\": \"全选\",\n  \"sortable\": \"可排序的列\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,gBAAE,CAAC;IACnC,iBAAiB,CAAC,OAAS,CAAC,iBAAG,EAAE,KAAK,UAAU,CAAC,iCAAK,CAAC;IACvD,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,iBAAG,CAAC;IAC1C,cAAc,CAAC,gBAAE,CAAC;IAClB,kBAAkB,CAAC,OAAS,CAAC,iBAAG,EAAE,KAAK,UAAU,CAAC,iCAAK,CAAC;IACxD,sBAAsB,CAAC,wGAAa,CAAC;IACrC,UAAU,CAAC,gBAAE,CAAC;IACd,aAAa,CAAC,gBAAE,CAAC;IACjB,YAAY,CAAC,wCAAK,CAAC;AACrB", "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/zh-TW.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/intl/zh-TW.json"], "sourcesContent": ["{\n  \"ascending\": \"遞增\",\n  \"ascendingSort\": \"已依據「{columnName}」欄遞增排序\",\n  \"columnSize\": \"{value} 像素\",\n  \"descending\": \"遞減\",\n  \"descendingSort\": \"已依據「{columnName}」欄遞減排序\",\n  \"resizerDescription\": \"按 Enter 鍵以開始調整大小\",\n  \"select\": \"選取\",\n  \"selectAll\": \"全選\",\n  \"sortable\": \"可排序的欄\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,aAAa,CAAC,gBAAE,CAAC;IACnC,iBAAiB,CAAC,OAAS,CAAC,gCAAI,EAAE,KAAK,UAAU,CAAC,gDAAM,CAAC;IACzD,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,iBAAG,CAAC;IAC1C,cAAc,CAAC,gBAAE,CAAC;IAClB,kBAAkB,CAAC,OAAS,CAAC,gCAAI,EAAE,KAAK,UAAU,CAAC,gDAAM,CAAC;IAC1D,sBAAsB,CAAC,+EAAgB,CAAC;IACxC,UAAU,CAAC,gBAAE,CAAC;IACd,aAAa,CAAC,gBAAE,CAAC;IACjB,YAAY,CAAC,wCAAK,CAAC;AACrB", "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/intlStrings.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/%2A.js"], "sourcesContent": ["const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4BAAiB;IACf,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;IACT,uKAAS,UAAA;AACX", "debugId": null}}, {"offset": {"line": 1752, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/useTableColumnHeader.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/useTableColumnHeader.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {getColumnHeaderId} from './utils';\nimport {GridNode} from '@react-types/grid';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {isAndroid, mergeProps, useDescription} from '@react-aria/utils';\nimport {TableState} from '@react-stately/table';\nimport {useEffect} from 'react';\nimport {useFocusable, usePress} from '@react-aria/interactions';\nimport {useGridCell} from '@react-aria/grid';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaTableColumnHeaderProps<T> {\n  /** An object representing the [column header](https://www.w3.org/TR/wai-aria-1.1/#columnheader). Contains all the relevant information that makes up the column header. */\n  node: GridNode<T>,\n  /** Whether the [column header](https://www.w3.org/TR/wai-aria-1.1/#columnheader) is contained in a virtual scroller. */\n  isVirtualized?: boolean\n}\n\nexport interface TableColumnHeaderAria {\n  /** Props for the [column header](https://www.w3.org/TR/wai-aria-1.1/#columnheader) element. */\n  columnHeaderProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a column header in a table.\n * @param props - Props for the column header.\n * @param state - State of the table, as returned by `useTableState`.\n * @param ref - The ref attached to the column header element.\n */\nexport function useTableColumnHeader<T>(props: AriaTableColumnHeaderProps<T>, state: TableState<T>, ref: RefObject<FocusableElement | null>): TableColumnHeaderAria {\n  let {node} = props;\n  let allowsSorting = node.props.allowsSorting;\n  // if there are no focusable children, the column header will focus the cell\n  let {gridCellProps} = useGridCell({...props, focusMode: 'child'}, state, ref);\n\n  let isSelectionCellDisabled = node.props.isSelectionCell && state.selectionManager.selectionMode === 'single';\n\n  let {pressProps} = usePress({\n    isDisabled: !allowsSorting || isSelectionCellDisabled,\n    onPress() {\n      state.sort(node.key);\n    },\n    ref\n  });\n\n  // Needed to pick up the focusable context, enabling things like Tooltips for example\n  let {focusableProps} = useFocusable({}, ref);\n\n  let ariaSort: DOMAttributes['aria-sort'] | undefined = undefined;\n  let isSortedColumn = state.sortDescriptor?.column === node.key;\n  let sortDirection = state.sortDescriptor?.direction;\n  // aria-sort not supported in Android Talkback\n  if (node.props.allowsSorting && !isAndroid()) {\n    ariaSort = isSortedColumn ? sortDirection : 'none';\n  }\n\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/table');\n  let sortDescription;\n  if (allowsSorting) {\n    sortDescription = `${stringFormatter.format('sortable')}`;\n    // Android Talkback doesn't support aria-sort so we add sort order details to the aria-described by here\n    if (isSortedColumn && sortDirection && isAndroid()) {\n      sortDescription = `${sortDescription}, ${stringFormatter.format(sortDirection)}`;\n    }\n  }\n\n  let descriptionProps = useDescription(sortDescription);\n\n  let shouldDisableFocus = state.collection.size === 0;\n  useEffect(() => {\n    if (shouldDisableFocus && state.selectionManager.focusedKey === node.key) {\n      state.selectionManager.setFocusedKey(null);\n    }\n  }, [shouldDisableFocus, state.selectionManager, node.key]);\n\n  return {\n    columnHeaderProps: {\n      ...mergeProps(\n        focusableProps,\n        gridCellProps,\n        pressProps,\n        descriptionProps,\n        // If the table is empty, make all column headers untabbable\n        shouldDisableFocus ? {tabIndex: -1} : null\n      ),\n      role: 'columnheader',\n      id: getColumnHeaderId(state, node.key),\n      'aria-colspan': node.colSpan && node.colSpan > 1 ? node.colSpan : undefined,\n      'aria-sort': ariaSort\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAgCM,SAAS,0CAAwB,KAAoC,EAAE,KAAoB,EAAE,GAAuC;QAoBpH,uBACD;IApBpB,IAAI,EAAA,MAAC,IAAI,EAAC,GAAG;IACb,IAAI,gBAAgB,KAAK,KAAK,CAAC,aAAa;IAC5C,4EAA4E;IAC5E,IAAI,EAAA,eAAC,aAAa,EAAC,GAAG,CAAA,mKAAA,cAAU,EAAE;QAAC,GAAG,KAAK;QAAE,WAAW;IAAO,GAAG,OAAO;IAEzE,IAAI,0BAA0B,KAAK,KAAK,CAAC,eAAe,IAAI,MAAM,gBAAgB,CAAC,aAAa,KAAK;IAErG,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;QAC1B,YAAY,CAAC,iBAAiB;QAC9B;YACE,MAAM,IAAI,CAAC,KAAK,GAAG;QACrB;aACA;IACF;IAEA,qFAAqF;IACrF,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,4KAAA,eAAW,EAAE,CAAC,GAAG;IAExC,IAAI,WAAmD;IACvD,IAAI,iBAAiB,CAAA,CAAA,wBAAA,MAAM,cAAc,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAsB,MAAM,MAAK,KAAK,GAAG;IAC9D,IAAI,gBAAA,CAAgB,yBAAA,MAAM,cAAc,MAAA,QAApB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAsB,SAAS;IACnD,8CAA8C;IAC9C,IAAI,KAAK,KAAK,CAAC,aAAa,IAAI,CAAC,CAAA,iKAAA,YAAQ,KACvC,WAAW,iBAAiB,gBAAgB;IAG9C,IAAI,kBAAkB,CAAA,mLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,gKAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI;IACJ,IAAI,eAAe;QACjB,kBAAkB,GAAG,gBAAgB,MAAM,CAAC,aAAa;QACzD,wGAAwG;QACxG,IAAI,kBAAkB,iBAAiB,CAAA,iKAAA,YAAQ,KAC7C,kBAAkB,GAAG,gBAAgB,EAAE,EAAE,gBAAgB,MAAM,CAAC,gBAAgB;IAEpF;IAEA,IAAI,mBAAmB,CAAA,uKAAA,iBAAa,EAAE;IAEtC,IAAI,qBAAqB,MAAM,UAAU,CAAC,IAAI,KAAK;IACnD,CAAA,yMAAA,YAAQ,EAAE;QACR,IAAI,sBAAsB,MAAM,gBAAgB,CAAC,UAAU,KAAK,KAAK,GAAG,EACtE,MAAM,gBAAgB,CAAC,aAAa,CAAC;IAEzC,GAAG;QAAC;QAAoB,MAAM,gBAAgB;QAAE,KAAK,GAAG;KAAC;IAEzD,OAAO;QACL,mBAAmB;YACjB,GAAG,CAAA,mKAAA,aAAS,EACV,gBACA,eACA,YACA,kBACA,AACA,qBAAqB,uCADuC;gBACtC,UAAU,CAAA;YAAE,IAAI,KACvC;YACD,MAAM;YACN,IAAI,CAAA,8JAAA,oBAAgB,EAAE,OAAO,KAAK,GAAG;YACrC,gBAAgB,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,OAAO,GAAG;YAClE,aAAa;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/useTableSelectionCheckbox.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/useTableSelectionCheckbox.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaCheckboxProps} from '@react-types/checkbox';\nimport {getRowLabelledBy} from './utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {Key} from '@react-types/shared';\nimport {TableState} from '@react-stately/table';\nimport {useGridSelectionCheckbox} from '@react-aria/grid';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaTableSelectionCheckboxProps {\n  /** A unique key for the checkbox. */\n  key: Key\n}\n\nexport interface TableSelectionCheckboxAria {\n  /** Props for the row selection checkbox element. */\n  checkboxProps: AriaCheckboxProps\n}\n\nexport interface TableSelectAllCheckboxAria {\n  /** Props for the select all checkbox element. */\n  checkboxProps: AriaCheckboxProps\n}\n\n/**\n * Provides the behavior and accessibility implementation for a selection checkbox in a table.\n * @param props - Props for the selection checkbox.\n * @param state - State of the table, as returned by `useTableState`.\n */\nexport function useTableSelectionCheckbox<T>(props: AriaTableSelectionCheckboxProps, state: TableState<T>): TableSelectionCheckboxAria {\n  let {key} = props;\n  const {checkboxProps} = useGridSelectionCheckbox(props, state);\n\n  return {\n    checkboxProps: {\n      ...checkboxProps,\n      'aria-labelledby': `${checkboxProps.id} ${getRowLabelledBy(state, key)}`\n    }\n  };\n}\n\n/**\n * Provides the behavior and accessibility implementation for the select all checkbox in a table.\n * @param props - Props for the select all checkbox.\n * @param state - State of the table, as returned by `useTableState`.\n */\nexport function useTableSelectAllCheckbox<T>(state: TableState<T>): TableSelectAllCheckboxAria {\n  let {isEmpty, isSelectAll, selectionMode} = state.selectionManager;\n  const stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/table');\n\n  return {\n    checkboxProps: {\n      'aria-label': stringFormatter.format(selectionMode === 'single' ? 'select' : 'selectAll'),\n      isSelected: isSelectAll,\n      isDisabled: selectionMode !== 'multiple' || (state.collection.size === 0 || (state.collection.rows.length === 1 && state.collection.rows[0].type === 'loader')),\n      isIndeterminate: !isEmpty && !isSelectAll,\n      onChange: () => state.selectionManager.toggleSelectAll()\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA+BM,SAAS,0CAA6B,KAAsC,EAAE,KAAoB;IACvG,IAAI,EAAA,KAAC,GAAG,EAAC,GAAG;IACZ,MAAM,EAAA,eAAC,aAAa,EAAC,GAAG,CAAA,gLAAA,2BAAuB,EAAE,OAAO;IAExD,OAAO;QACL,eAAe;YACb,GAAG,aAAa;YAChB,mBAAmB,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE,CAAA,8JAAA,mBAAe,EAAE,OAAO,MAAM;QAC1E;IACF;AACF;AAOO,SAAS,0CAA6B,KAAoB;IAC/D,IAAI,EAAA,SAAC,OAAO,EAAA,aAAE,WAAW,EAAA,eAAE,aAAa,EAAC,GAAG,MAAM,gBAAgB;IAClE,MAAM,kBAAkB,CAAA,mLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,gKAAA,CAAA,UAAA,CAAW,GAAG;IAElE,OAAO;QACL,eAAe;YACb,cAAc,gBAAgB,MAAM,CAAC,kBAAkB,WAAW,WAAW;YAC7E,YAAY;YACZ,YAAY,kBAAkB,cAAe,MAAM,UAAU,CAAC,IAAI,KAAK,KAAM,MAAM,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK;YACrJ,iBAAiB,CAAC,WAAW,CAAC;YAC9B,UAAU,IAAM,MAAM,gBAAgB,CAAC,eAAe;QACxD;IACF;AACF", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/TableKeyboardDelegate.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/TableKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getChildNodes, getFirstItem} from '@react-stately/collections';\nimport {GridKeyboardDelegate} from '@react-aria/grid';\nimport {Key, Node} from '@react-types/shared';\nimport {TableCollection} from '@react-types/table';\n\nexport class TableKeyboardDelegate<T> extends GridKeyboardDelegate<T, TableCollection<T>> {\n\n  protected isCell(node: Node<T>): boolean {\n    return node.type === 'cell' || node.type === 'rowheader' || node.type === 'column';\n  }\n\n  getKeyBelow(key: Key): Key | null {\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus was on a column, then focus the first child column if any,\n    // or find the corresponding cell in the first row.\n    if (startItem.type === 'column') {\n      let child = getFirstItem(getChildNodes(startItem, this.collection));\n      if (child) {\n        return child.key;\n      }\n\n      let firstKey = this.getFirstKey();\n      if (firstKey == null) {\n        return null;\n      }\n\n      let firstItem = this.collection.getItem(firstKey);\n      if (!firstItem) {\n        return null;\n      }\n\n      return super.getKeyForItemInRowByIndex(firstKey, startItem.index);\n    }\n\n    return super.getKeyBelow(key);\n  }\n\n  getKeyAbove(key: Key): Key | null {\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus was on a column, focus the parent column if any\n    if (startItem.type === 'column') {\n      let parent = startItem.parentKey != null ? this.collection.getItem(startItem.parentKey) : null;\n      if (parent && parent.type === 'column') {\n        return parent.key;\n      }\n\n      return null;\n    }\n\n    // only return above row key if not header row\n    let superKey = super.getKeyAbove(key);\n    let superItem = superKey != null ? this.collection.getItem(superKey) : null;\n    if (superItem && superItem.type !== 'headerrow') {\n      return superKey;\n    }\n\n    // If no item was found, and focus was on a cell, then focus the\n    // corresponding column header.\n    if (this.isCell(startItem)) {\n      return this.collection.columns[startItem.index].key;\n    }\n\n    // If focus was on a row, then focus the first column header.\n    return this.collection.columns[0].key;\n  }\n\n  private findNextColumnKey(column: Node<T>): Key | null {\n    // Search following columns\n    let key = this.findNextKey(column.key, item => item.type === 'column');\n    if (key != null) {\n      return key;\n    }\n\n    // Wrap around to the first column\n    let row = this.collection.headerRows[column.level];\n    for (let item of getChildNodes(row, this.collection)) {\n      if (item.type === 'column') {\n        return item.key;\n      }\n    }\n\n    return null;\n  }\n\n  private findPreviousColumnKey(column: Node<T>): Key | null {\n    // Search previous columns\n    let key = this.findPreviousKey(column.key, item => item.type === 'column');\n    if (key != null) {\n      return key;\n    }\n\n    // Wrap around to the last column\n    let row = this.collection.headerRows[column.level];\n    let childNodes = [...getChildNodes(row, this.collection)];\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      let item = childNodes[i];\n      if (item.type === 'column') {\n        return item.key;\n      }\n    }\n\n    return null;\n  }\n\n  getKeyRightOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus was on a column, then focus the next column\n    if (item.type === 'column') {\n      return this.direction === 'rtl'\n        ? this.findPreviousColumnKey(item)\n        : this.findNextColumnKey(item);\n    }\n\n    return super.getKeyRightOf(key);\n  }\n\n  getKeyLeftOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus was on a column, then focus the previous column\n    if (item.type === 'column') {\n      return this.direction === 'rtl'\n        ? this.findNextColumnKey(item)\n        : this.findPreviousColumnKey(item);\n    }\n\n    return super.getKeyLeftOf(key);\n  }\n\n  getKeyForSearch(search: string, fromKey?: Key): Key | null {\n    if (!this.collator) {\n      return null;\n    }\n\n    let collection = this.collection;\n    let key = fromKey ?? this.getFirstKey();\n    if (key == null) {\n      return null;\n    }\n\n    // If the starting key is a cell, search from its parent row.\n    let startItem = collection.getItem(key);\n    if (startItem?.type === 'cell') {\n      key = startItem.parentKey ?? null;\n    }\n\n    let hasWrapped = false;\n    while (key != null) {\n      let item = collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      if (item.textValue) {\n        let substring = item.textValue.slice(0, search.length);\n        if (this.collator.compare(substring, search) === 0) {\n          return item.key;\n        }\n      }\n\n      // Check each of the row header cells in this row for a match\n      for (let cell of getChildNodes(item, this.collection)) {\n        let column = collection.columns[cell.index];\n        if (collection.rowHeaderColumnKeys.has(column.key) && cell.textValue) {\n          let substring = cell.textValue.slice(0, search.length);\n          if (this.collator.compare(substring, search) === 0) {\n            // If we started on a cell, end on the matching cell. Otherwise, end on the row.\n            let fromItem = fromKey != null ? collection.getItem(fromKey) : startItem;\n            return fromItem?.type === 'cell'\n              ? cell.key\n              : item.key;\n          }\n        }\n      }\n\n      key = this.getKeyBelow(key);\n\n      // Wrap around when reaching the end of the collection\n      if (key == null && !hasWrapped) {\n        key = this.getFirstKey();\n        hasWrapped = true;\n      }\n    }\n\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAOM,MAAM,kDAAiC,CAAA,4KAAA,uBAAmB;IAErD,OAAO,IAAa,EAAW;QACvC,OAAO,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK;IAC5E;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;QAGT,sEAAsE;QACtE,mDAAmD;QACnD,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,IAAI,QAAQ,CAAA,+KAAA,eAAW,EAAE,CAAA,+KAAA,gBAAY,EAAE,WAAW,IAAI,CAAC,UAAU;YACjE,IAAI,OACF,OAAO,MAAM,GAAG;YAGlB,IAAI,WAAW,IAAI,CAAC,WAAW;YAC/B,IAAI,YAAY,MACd,OAAO;YAGT,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACxC,IAAI,CAAC,WACH,OAAO;YAGT,OAAO,KAAK,CAAC,0BAA0B,UAAU,UAAU,KAAK;QAClE;QAEA,OAAO,KAAK,CAAC,YAAY;IAC3B;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;QAGT,2DAA2D;QAC3D,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,IAAI,SAAS,UAAU,SAAS,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,IAAI;YAC1F,IAAI,UAAU,OAAO,IAAI,KAAK,UAC5B,OAAO,OAAO,GAAG;YAGnB,OAAO;QACT;QAEA,8CAA8C;QAC9C,IAAI,WAAW,KAAK,CAAC,YAAY;QACjC,IAAI,YAAY,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY;QACvE,IAAI,aAAa,UAAU,IAAI,KAAK,aAClC,OAAO;QAGT,gEAAgE;QAChE,+BAA+B;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,YACd,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG;QAGrD,6DAA6D;QAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG;IACvC;IAEQ,kBAAkB,MAAe,EAAc;QACrD,2BAA2B;QAC3B,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,CAAA,OAAQ,KAAK,IAAI,KAAK;QAC7D,IAAI,OAAO,MACT,OAAO;QAGT,kCAAkC;QAClC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC;QAClD,KAAK,IAAI,QAAQ,CAAA,+KAAA,gBAAY,EAAE,KAAK,IAAI,CAAC,UAAU,EAAG;YACpD,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,KAAK,GAAG;QAEnB;QAEA,OAAO;IACT;IAEQ,sBAAsB,MAAe,EAAc;QACzD,0BAA0B;QAC1B,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,EAAE,CAAA,OAAQ,KAAK,IAAI,KAAK;QACjE,IAAI,OAAO,MACT,OAAO;QAGT,iCAAiC;QACjC,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,KAAK,CAAC;QAClD,IAAI,aAAa;eAAI,CAAA,+KAAA,gBAAY,EAAE,KAAK,IAAI,CAAC,UAAU;SAAE;QACzD,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC/C,IAAI,OAAO,UAAU,CAAC,EAAE;YACxB,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,KAAK,GAAG;QAEnB;QAEA,OAAO;IACT;IAEA,cAAc,GAAQ,EAAc;QAClC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,uDAAuD;QACvD,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,IAAI,CAAC,SAAS,KAAK,QACtB,IAAI,CAAC,qBAAqB,CAAC,QAC3B,IAAI,CAAC,iBAAiB,CAAC;QAG7B,OAAO,KAAK,CAAC,cAAc;IAC7B;IAEA,aAAa,GAAQ,EAAc;QACjC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,2DAA2D;QAC3D,IAAI,KAAK,IAAI,KAAK,UAChB,OAAO,IAAI,CAAC,SAAS,KAAK,QACtB,IAAI,CAAC,iBAAiB,CAAC,QACvB,IAAI,CAAC,qBAAqB,CAAC;QAGjC,OAAO,KAAK,CAAC,aAAa;IAC5B;IAEA,gBAAgB,MAAc,EAAE,OAAa,EAAc;QACzD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAChB,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,MAAM,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW,IAAI,CAAC,WAAW;QACrC,IAAI,OAAO,MACT,OAAO;QAGT,6DAA6D;QAC7D,IAAI,YAAY,WAAW,OAAO,CAAC;YAE3B;QADR,IAAI,CAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,IAAI,MAAK,QACtB,MAAM,CAAA,uBAAA,UAAU,SAAS,MAAA,QAAnB,yBAAA,KAAA,IAAA,uBAAuB;QAG/B,IAAI,aAAa;QACjB,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,WAAW,OAAO,CAAC;YAC9B,IAAI,CAAC,MACH,OAAO;YAGT,IAAI,KAAK,SAAS,EAAE;gBAClB,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;gBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GAC/C,OAAO,KAAK,GAAG;YAEnB;YAEA,6DAA6D;YAC7D,KAAK,IAAI,QAAQ,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAAG;gBACrD,IAAI,SAAS,WAAW,OAAO,CAAC,KAAK,KAAK,CAAC;gBAC3C,IAAI,WAAW,mBAAmB,CAAC,GAAG,CAAC,OAAO,GAAG,KAAK,KAAK,SAAS,EAAE;oBACpE,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;oBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GAAG;wBAClD,gFAAgF;wBAChF,IAAI,WAAW,WAAW,OAAO,WAAW,OAAO,CAAC,WAAW;wBAC/D,OAAO,CAAA,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,IAAI,MAAK,SACtB,KAAK,GAAG,GACR,KAAK,GAAG;oBACd;gBACF;YACF;YAEA,MAAM,IAAI,CAAC,WAAW,CAAC;YAEvB,sDAAsD;YACtD,IAAI,OAAO,QAAQ,CAAC,YAAY;gBAC9B,MAAM,IAAI,CAAC,WAAW;gBACtB,aAAa;YACf;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2038, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/useTable.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/useTable.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {announce} from '@react-aria/live-announcer';\nimport {GridAria, GridProps, useGrid} from '@react-aria/grid';\nimport {gridIds} from './utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {Key, LayoutDelegate, Rect, RefObject, Size} from '@react-types/shared';\nimport {mergeProps, useDescription, useId, useUpdateEffect} from '@react-aria/utils';\nimport {TableKeyboardDelegate} from './TableKeyboardDelegate';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState, TreeGridState} from '@react-stately/table';\nimport {useCollator, useLocale, useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\nexport interface AriaTableProps extends GridProps {\n  /** The layout object for the table. Computes what content is visible and how to position and style them. */\n  layoutDelegate?: LayoutDelegate,\n  /** @deprecated - Use layoutDelegate instead. */\n  layout?: DeprecatedLayout\n}\n\ninterface DeprecatedLayout {\n  getLayoutInfo(key: Key): DeprecatedLayoutInfo,\n  getContentSize(): Size,\n  virtualizer: DeprecatedVirtualizer\n}\n\ninterface DeprecatedLayoutInfo {\n  rect: Rect\n}\n\ninterface DeprecatedVirtualizer {\n  visibleRect: Rect\n}\n\n/**\n * Provides the behavior and accessibility implementation for a table component.\n * A table displays data in rows and columns and enables a user to navigate its contents via directional navigation keys,\n * and optionally supports row selection and sorting.\n * @param props - Props for the table.\n * @param state - State for the table, as returned by `useTableState`.\n * @param ref - The ref attached to the table element.\n */\nexport function useTable<T>(props: AriaTableProps, state: TableState<T> | TreeGridState<T>, ref: RefObject<HTMLElement | null>): GridAria {\n  let {\n    keyboardDelegate,\n    isVirtualized,\n    layoutDelegate,\n    layout\n  } = props;\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let {direction} = useLocale();\n  let disabledBehavior = state.selectionManager.disabledBehavior;\n  let delegate = useMemo(() => keyboardDelegate || new TableKeyboardDelegate({\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    disabledBehavior,\n    ref,\n    direction,\n    collator,\n    layoutDelegate,\n    layout\n  }), [keyboardDelegate, state.collection, state.disabledKeys, disabledBehavior, ref, direction, collator, layoutDelegate, layout]);\n  let id = useId(props.id);\n  gridIds.set(state, id);\n\n  let {gridProps} = useGrid({\n    ...props,\n    id,\n    keyboardDelegate: delegate\n  }, state, ref);\n\n  // Override to include header rows\n  if (isVirtualized) {\n    gridProps['aria-rowcount'] = state.collection.size + state.collection.headerRows.length;\n  }\n\n  if (tableNestedRows() && 'expandedKeys' in state) {\n    gridProps.role = 'treegrid';\n  }\n\n  let {column, direction: sortDirection} = state.sortDescriptor || {};\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/table');\n  let sortDescription = useMemo(() => {\n    let columnName = state.collection.columns.find(c => c.key === column)?.textValue ?? '';\n    return sortDirection && column ? stringFormatter.format(`${sortDirection}Sort`, {columnName}) : undefined;\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [sortDirection, column, state.collection.columns]);\n\n  let descriptionProps = useDescription(sortDescription);\n\n  // Only announce after initial render, tabbing to the table will tell you the initial sort info already\n  useUpdateEffect(() => {\n    if (sortDescription) {\n      announce(sortDescription, 'assertive', 500);\n    }\n  }, [sortDescription]);\n\n  return {\n    gridProps: mergeProps(\n      gridProps,\n      descriptionProps,\n      {\n        // merge sort description with long press information\n        'aria-describedby': [descriptionProps['aria-describedby'], gridProps['aria-describedby']].filter(Boolean).join(' ')\n      }\n    )\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA4CM,SAAS,0CAAY,KAAqB,EAAE,KAAuC,EAAE,GAAkC;IAC5H,IAAI,EAAA,kBACF,gBAAgB,EAAA,eAChB,aAAa,EAAA,gBACb,cAAc,EAAA,QACd,MAAM,EACP,GAAG;IAEJ,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,mKAAA,cAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,+JAAA,YAAQ;IAC1B,IAAI,mBAAmB,MAAM,gBAAgB,CAAC,gBAAgB;IAC9D,IAAI,WAAW,CAAA,yMAAA,UAAM,EAAE,IAAM,oBAAoB,IAAI,CAAA,8KAAA,wBAAoB,EAAE;YACzE,YAAY,MAAM,UAAU;YAC5B,cAAc,MAAM,YAAY;8BAChC;iBACA;uBACA;sBACA;4BACA;oBACA;QACF,IAAI;QAAC;QAAkB,MAAM,UAAU;QAAE,MAAM,YAAY;QAAE;QAAkB;QAAK;QAAW;QAAU;QAAgB;KAAO;IAChI,IAAI,KAAK,CAAA,8JAAA,QAAI,EAAE,MAAM,EAAE;IACvB,CAAA,8JAAA,UAAM,EAAE,GAAG,CAAC,OAAO;IAEnB,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,+JAAA,UAAM,EAAE;QACxB,GAAG,KAAK;YACR;QACA,kBAAkB;IACpB,GAAG,OAAO;IAEV,kCAAkC;IAClC,IAAI,eACF,SAAS,CAAC,gBAAgB,GAAG,MAAM,UAAU,CAAC,IAAI,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM;IAGzF,IAAI,CAAA,kKAAA,kBAAc,OAAO,kBAAkB,OACzC,UAAU,IAAI,GAAG;IAGnB,IAAI,EAAA,QAAC,MAAM,EAAE,WAAW,aAAa,EAAC,GAAG,MAAM,cAAc,IAAI,CAAC;IAClE,IAAI,kBAAkB,CAAA,mLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,gKAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI,kBAAkB,CAAA,yMAAA,UAAM,EAAE;YACX;YAAA;QAAjB,IAAI,aAAa,CAAA,2CAAA,CAAA,iCAAA,MAAM,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,OAAA,MAAA,QAA7C,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAsD,SAAS,MAAA,QAA/D,6CAAA,KAAA,IAAA,2CAAmE;QACpF,OAAO,iBAAiB,SAAS,gBAAgB,MAAM,CAAC,GAAG,cAAc,IAAI,CAAC,EAAE;wBAAC;QAAU,KAAK;IAClG,uDAAuD;IACvD,GAAG;QAAC;QAAe;QAAQ,MAAM,UAAU,CAAC,OAAO;KAAC;IAEpD,IAAI,mBAAmB,CAAA,uKAAA,iBAAa,EAAE;IAEtC,uGAAuG;IACvG,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,iBACF,CAAA,kLAAA,WAAO,EAAE,iBAAiB,aAAa;IAE3C,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,WAAW,CAAA,mKAAA,aAAS,EAClB,WACA,kBACA;YACE,qDAAqD;YACrD,oBAAoB;gBAAC,gBAAgB,CAAC,mBAAmB;gBAAE,SAAS,CAAC,mBAAmB;aAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACjH;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 2155, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/useTableCell.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/useTableCell.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {getCellId} from './utils';\nimport {GridNode} from '@react-types/grid';\nimport {TableState} from '@react-stately/table';\nimport {useGridCell} from '@react-aria/grid';\n\nexport interface AriaTableCellProps {\n  /** An object representing the table cell. Contains all the relevant information that makes up the row header. */\n  node: GridNode<unknown>,\n  /** Whether the cell is contained in a virtual scroller. */\n  isVirtualized?: boolean,\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean,\n  /**\n   * Handler that is called when a user performs an action on the cell.\n   * Please use onCellAction at the collection level instead.\n   * @deprecated\n   **/\n  onAction?: () => void\n}\n\nexport interface TableCellAria {\n  /** Props for the table cell element. */\n  gridCellProps: DOMAttributes,\n  /** Whether the cell is currently in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a cell in a table.\n * @param props - Props for the cell.\n * @param state - State of the table, as returned by `useTableState`.\n * @param ref - The ref attached to the cell element.\n */\nexport function useTableCell<T>(props: AriaTableCellProps, state: TableState<T>, ref: RefObject<FocusableElement | null>): TableCellAria {\n  let {gridCellProps, isPressed} = useGridCell(props, state, ref);\n  let columnKey = props.node.column?.key;\n  if (columnKey != null && state.collection.rowHeaderColumnKeys.has(columnKey)) {\n    gridCellProps.role = 'rowheader';\n    gridCellProps.id = getCellId(state, props.node.parentKey!, columnKey);\n  }\n\n  return {\n    gridCellProps,\n    isPressed\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAoCM,SAAS,0CAAgB,KAAyB,EAAE,KAAoB,EAAE,GAAuC;QAEtG;IADhB,IAAI,EAAA,eAAC,aAAa,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,mKAAA,cAAU,EAAE,OAAO,OAAO;IAC3D,IAAI,YAAA,CAAY,qBAAA,MAAM,IAAI,CAAC,MAAM,MAAA,QAAjB,uBAAA,KAAA,IAAA,KAAA,IAAA,mBAAmB,GAAG;IACtC,IAAI,aAAa,QAAQ,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,YAAY;QAC5E,cAAc,IAAI,GAAG;QACrB,cAAc,EAAE,GAAG,CAAA,8JAAA,YAAQ,EAAE,OAAO,MAAM,IAAI,CAAC,SAAS,EAAG;IAC7D;IAEA,OAAO;uBACL;mBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2191, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/useTableRow.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/useTableRow.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {getLastItem} from '@react-stately/collections';\nimport {getRowLabelledBy} from './utils';\nimport type {GridNode} from '@react-types/grid';\nimport {GridRowAria, GridRowProps, useGridRow} from '@react-aria/grid';\nimport {HTMLAttributes} from 'react';\nimport {mergeProps, useSyntheticLinkProps} from '@react-aria/utils';\nimport {TableCollection} from '@react-types/table';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState, TreeGridState} from '@react-stately/table';\nimport {useLocale} from '@react-aria/i18n';\n\nconst EXPANSION_KEYS = {\n  expand: {\n    ltr: 'ArrowRight',\n    rtl: 'ArrowLeft'\n  },\n  'collapse': {\n    ltr: 'ArrowLeft',\n    rtl: 'ArrowRight'\n  }\n};\n\n/**\n * Provides the behavior and accessibility implementation for a row in a table.\n * @param props - Props for the row.\n * @param state - State of the table, as returned by `useTableState`.\n */\nexport function useTableRow<T>(props: GridRowProps<T>, state: TableState<T> | TreeGridState<T>, ref: RefObject<FocusableElement | null>): GridRowAria {\n  let {node, isVirtualized} = props;\n  let {rowProps, ...states} = useGridRow<T, TableCollection<T>, TableState<T>>(props, state, ref);\n  let {direction} = useLocale();\n\n  if (isVirtualized && !(tableNestedRows() && 'expandedKeys' in state)) {\n    rowProps['aria-rowindex'] = node.index + 1 + state.collection.headerRows.length; // aria-rowindex is 1 based\n  } else {\n    delete rowProps['aria-rowindex'];\n  }\n\n  let treeGridRowProps: HTMLAttributes<HTMLElement> = {};\n  if (tableNestedRows() && 'expandedKeys' in state) {\n    let treeNode = state.keyMap.get(node.key);\n    if (treeNode != null) {\n      let hasChildRows = treeNode.props?.UNSTABLE_childItems || treeNode.props?.children?.length > state.userColumnCount;\n      treeGridRowProps = {\n        onKeyDown: (e) => {\n          if ((e.key === EXPANSION_KEYS['expand'][direction]) && state.selectionManager.focusedKey === treeNode.key && hasChildRows && state.expandedKeys !== 'all' && !state.expandedKeys.has(treeNode.key)) {\n            state.toggleKey(treeNode.key);\n            e.stopPropagation();\n          } else if ((e.key === EXPANSION_KEYS['collapse'][direction]) && state.selectionManager.focusedKey === treeNode.key && hasChildRows && (state.expandedKeys === 'all' || state.expandedKeys.has(treeNode.key))) {\n            state.toggleKey(treeNode.key);\n            e.stopPropagation();\n          }\n        },\n        'aria-expanded': hasChildRows ? state.expandedKeys === 'all' || state.expandedKeys.has(node.key) : undefined,\n        'aria-level': treeNode.level,\n        'aria-posinset': (treeNode.indexOfType ?? 0) + 1,\n        'aria-setsize': treeNode.level > 1 ?\n          ((getLastItem(state.keyMap.get(treeNode.parentKey!)?.childNodes ?? []) as GridNode<T>)?.indexOfType ?? 0) + 1 :\n          ((getLastItem(state.collection.body.childNodes) as GridNode<T>)?.indexOfType ?? 0) + 1\n      };\n    }\n  }\n\n  let syntheticLinkProps = useSyntheticLinkProps(node.props);\n  let linkProps = states.hasAction ? syntheticLinkProps : {};\n  return {\n    rowProps: {\n      ...mergeProps(rowProps, treeGridRowProps, linkProps),\n      'aria-labelledby': getRowLabelledBy(state, node.key)\n    },\n    ...states\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAcD,MAAM,uCAAiB;IACrB,QAAQ;QACN,KAAK;QACL,KAAK;IACP;IACA,YAAY;QACV,KAAK;QACL,KAAK;IACP;AACF;AAOO,SAAS,0CAAe,KAAsB,EAAE,KAAuC,EAAE,GAAuC;IACrI,IAAI,EAAA,MAAC,IAAI,EAAA,eAAE,aAAa,EAAC,GAAG;IAC5B,IAAI,EAAA,UAAC,QAAQ,EAAE,GAAG,QAAO,GAAG,CAAA,kKAAA,aAAS,EAAwC,OAAO,OAAO;IAC3F,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,+JAAA,YAAQ;IAE1B,IAAI,iBAAiB,CAAE,CAAA,CAAA,kKAAA,kBAAc,OAAO,kBAAkB,KAAI,GAChE,QAAQ,CAAC,gBAAgB,GAAG,KAAK,KAAK,GAAG,IAAI,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,2BAA2B;SAE5G,OAAO,QAAQ,CAAC,gBAAgB;IAGlC,IAAI,mBAAgD,CAAC;IACrD,IAAI,CAAA,kKAAA,kBAAc,OAAO,kBAAkB,OAAO;QAChD,IAAI,WAAW,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG;QACxC,IAAI,YAAY,MAAM;gBACD,iBAAuC,0BAAA,kBAepD,cAAY,mBACZ;YAhBN,IAAI,eAAe,CAAA,CAAA,kBAAA,SAAS,KAAK,MAAA,QAAd,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAgB,mBAAmB,KAAI,CAAA,CAAA,mBAAA,SAAS,KAAK,MAAA,QAAd,qBAAA,KAAA,IAAA,KAAA,IAAA,CAAA,2BAAA,iBAAgB,QAAQ,MAAA,QAAxB,6BAAA,KAAA,IAAA,KAAA,IAAA,yBAA0B,MAAM,IAAG,MAAM,eAAe;gBAa9F,uBAEF,8BAAb,0BACA;YAfL,mBAAmB;gBACjB,WAAW,CAAC;oBACV,IAAK,EAAE,GAAG,KAAK,oCAAc,CAAC,SAAS,CAAC,UAAU,IAAK,MAAM,gBAAgB,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,gBAAgB,MAAM,YAAY,KAAK,SAAS,CAAC,MAAM,YAAY,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG;wBAClM,MAAM,SAAS,CAAC,SAAS,GAAG;wBAC5B,EAAE,eAAe;oBACnB,OAAO,IAAK,EAAE,GAAG,KAAK,oCAAc,CAAC,WAAW,CAAC,UAAU,IAAK,MAAM,gBAAgB,CAAC,UAAU,KAAK,SAAS,GAAG,IAAI,gBAAiB,CAAA,MAAM,YAAY,KAAK,SAAS,MAAM,YAAY,CAAC,GAAG,CAAC,SAAS,GAAG,CAAA,GAAI;wBAC5M,MAAM,SAAS,CAAC,SAAS,GAAG;wBAC5B,EAAE,eAAe;oBACnB;gBACF;gBACA,iBAAiB,eAAe,MAAM,YAAY,KAAK,SAAS,MAAM,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI;gBACnG,cAAc,SAAS,KAAK;gBAC5B,iBAAkB,CAAA,CAAA,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,wBAAwB,CAAA,IAAK;gBAC/C,gBAAgB,SAAS,KAAK,GAAG,IAC9B,CAAA,CAAA,2BAAA,CAAC,eAAA,CAAA,+KAAA,cAAU,EAAE,CAAA,+BAAA,CAAA,oBAAA,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,SAAS,CAAA,MAAA,QAAnC,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAuC,UAAU,MAAA,QAAjD,iCAAA,KAAA,IAAA,+BAAqD,EAAE,CAAA,MAAA,QAAnE,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAsF,WAAW,MAAA,QAAlG,6BAAA,KAAA,IAAA,2BAAsG,CAAA,IAAK,IAC3G,CAAA,CAAA,4BAAA,CAAC,gBAAA,CAAA,+KAAA,cAAU,EAAE,MAAM,UAAU,CAAC,IAAI,CAAC,UAAU,CAAA,MAAA,QAA5C,kBAAA,KAAA,IAAA,KAAA,IAAA,cAA+D,WAAW,MAAA,QAA3E,8BAAA,KAAA,IAAA,4BAA+E,CAAA,IAAK;YACzF;QACF;IACF;IAEA,IAAI,qBAAqB,CAAA,iKAAA,wBAAoB,EAAE,KAAK,KAAK;IACzD,IAAI,YAAY,OAAO,SAAS,GAAG,qBAAqB,CAAC;IACzD,OAAO;QACL,UAAU;YACR,GAAG,CAAA,mKAAA,aAAS,EAAE,UAAU,kBAAkB,UAAU;YACpD,mBAAmB,CAAA,8JAAA,mBAAe,EAAE,OAAO,KAAK,GAAG;QACrD;QACA,GAAG,MAAM;IACX;AACF", "debugId": null}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/index.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport {useTable} from './useTable';\nexport {useTableColumnHeader} from './useTableColumnHeader';\nexport {useTableRow} from './useTableRow';\nexport {useTableHeaderRow} from './useTableHeaderRow';\nexport {useTableCell} from './useTableCell';\nexport {useTableSelectionCheckbox, useTableSelectAllCheckbox} from './useTableSelectionCheckbox';\nexport {useTableColumnResize} from './useTableColumnResize';\n\n// Workaround for a Parcel bug where re-exports don't work in the CommonJS output format...\n// export {useGridRowGroup as useTableRowGroup} from '@react-aria/grid';\nimport {GridRowGroupAria, useGridRowGroup} from '@react-aria/grid';\nexport function useTableRowGroup(): GridRowGroupAria {\n  return useGridRowGroup();\n}\n\nexport type {AriaTableProps} from './useTable';\nexport type {GridAria, GridRowAria, GridRowProps} from '@react-aria/grid';\nexport type {AriaTableColumnHeaderProps, TableColumnHeaderAria} from './useTableColumnHeader';\nexport type {AriaTableCellProps, TableCellAria} from './useTableCell';\nexport type {TableHeaderRowAria} from './useTableHeaderRow';\nexport type {AriaTableSelectionCheckboxProps, TableSelectionCheckboxAria, TableSelectAllCheckboxAria} from './useTableSelectionCheckbox';\nexport type {AriaTableColumnResizeProps, TableColumnResizeAria} from './useTableColumnResize';\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS;IACd,OAAO,CAAA,uKAAA,kBAAc;AACvB", "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/table/dist/useTableHeaderRow.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/table/dist/packages/%40react-aria/table/src/useTableHeaderRow.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {GridRowProps} from '@react-aria/grid';\nimport {tableNestedRows} from '@react-stately/flags';\nimport {TableState} from '@react-stately/table';\n\nexport interface TableHeaderRowAria {\n  /** Props for the grid row element. */\n  rowProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a header row in a table.\n * @param props - Props for the row.\n * @param state - State of the table, as returned by `useTableState`.\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function useTableHeaderRow<T>(props: GridRowProps<T>, state: TableState<T>, ref: RefObject<Element | null>): TableHeaderRowAria {\n  let {node, isVirtualized} = props;\n  let rowProps = {\n    role: 'row'\n  };\n\n  if (isVirtualized && !(tableNestedRows() && 'expandedKeys' in state)) {\n    rowProps['aria-rowindex'] = node.index + 1; // aria-rowindex is 1 based\n  }\n\n  return {\n    rowProps\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAkBM,SAAS,0CAAqB,KAAsB,EAAE,KAAoB,EAAE,GAA8B;IAC/G,IAAI,EAAA,MAAC,IAAI,EAAA,eAAE,aAAa,EAAC,GAAG;IAC5B,IAAI,WAAW;QACb,MAAM;IACR;IAEA,IAAI,iBAAiB,CAAE,CAAA,CAAA,kKAAA,kBAAc,OAAO,kBAAkB,KAAI,GAChE,QAAQ,CAAC,gBAAgB,GAAG,KAAK,KAAK,GAAG,GAAG,2BAA2B;IAGzE,OAAO;kBACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 2335, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {GridCollection} from '@react-types/grid';\nimport type {GridState} from '@react-stately/grid';\nimport type {Key, KeyboardDelegate} from '@react-types/shared';\n\ninterface GridMapShared {\n  keyboardDelegate: KeyboardDelegate,\n  actions: {\n    onRowAction?: (key: Key) => void,\n    onCellAction?: (key: Key) => void\n  },\n  shouldSelectOnPressUp?: boolean\n}\n\n// Used to share:\n// keyboard delegate between useGrid and useGridCell\n// onRowAction/onCellAction across hooks\nexport const gridMap: WeakMap<GridState<unknown, GridCollection<unknown>>, GridMapShared> = new WeakMap<GridState<unknown, GridCollection<unknown>>, GridMapShared>();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAkBM,MAAM,4CAA+E,IAAI", "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useGridCell.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useGridCell.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, Key, RefObject} from '@react-types/shared';\nimport {focusSafely, isFocusVisible} from '@react-aria/interactions';\nimport {getFocusableTreeWalker} from '@react-aria/focus';\nimport {getScrollParent, mergeProps, scrollIntoViewport} from '@react-aria/utils';\nimport {GridCollection, GridNode} from '@react-types/grid';\nimport {gridMap} from './utils';\nimport {GridState} from '@react-stately/grid';\nimport {KeyboardEvent as ReactKeyboardEvent, useRef} from 'react';\nimport {useLocale} from '@react-aria/i18n';\nimport {useSelectableItem} from '@react-aria/selection';\n\nexport interface GridCellProps {\n  /** An object representing the grid cell. Contains all the relevant information that makes up the grid cell. */\n  node: GridNode<unknown>,\n  /** Whether the grid cell is contained in a virtual scroller. */\n  isVirtualized?: boolean,\n  /** Whether the cell or its first focusable child element should be focused when the grid cell is focused. */\n  focusMode?: 'child' | 'cell',\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean,\n  /** Indicates how many columns the data cell spans. */\n  colSpan?: number,\n  /**\n   * Handler that is called when a user performs an action on the cell.\n   * Please use onCellAction at the collection level instead.\n   * @deprecated\n   **/\n  onAction?: () => void\n}\n\nexport interface GridCellAria {\n  /** Props for the grid cell element. */\n  gridCellProps: DOMAttributes,\n  /** Whether the cell is currently in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a cell in a grid.\n * @param props - Props for the cell.\n * @param state - State of the parent grid, as returned by `useGridState`.\n */\nexport function useGridCell<T, C extends GridCollection<T>>(props: GridCellProps, state: GridState<T, C>, ref: RefObject<FocusableElement | null>): GridCellAria {\n  let {\n    node,\n    isVirtualized,\n    focusMode = 'child',\n    shouldSelectOnPressUp,\n    onAction\n  } = props;\n\n  let {direction} = useLocale();\n  let {keyboardDelegate, actions: {onCellAction}} = gridMap.get(state)!;\n\n  // We need to track the key of the item at the time it was last focused so that we force\n  // focus to go to the item when the DOM node is reused for a different item in a virtualizer.\n  let keyWhenFocused = useRef<Key | null>(null);\n\n  // Handles focusing the cell. If there is a focusable child,\n  // it is focused, otherwise the cell itself is focused.\n  let focus = () => {\n    if (ref.current) {\n      let treeWalker = getFocusableTreeWalker(ref.current);\n      if (focusMode === 'child') {\n        // If focus is already on a focusable child within the cell, early return so we don't shift focus\n        if (ref.current.contains(document.activeElement) && ref.current !== document.activeElement) {\n          return;\n        }\n\n        let focusable = state.selectionManager.childFocusStrategy === 'last'\n          ? last(treeWalker)\n          : treeWalker.firstChild() as FocusableElement;\n        if (focusable) {\n          focusSafely(focusable);\n          return;\n        }\n      }\n\n      if (\n        (keyWhenFocused.current != null && node.key !== keyWhenFocused.current) ||\n        !ref.current.contains(document.activeElement)\n      ) {\n        focusSafely(ref.current);\n      }\n    }\n  };\n\n  let {itemProps, isPressed} = useSelectableItem({\n    selectionManager: state.selectionManager,\n    key: node.key,\n    ref,\n    isVirtualized,\n    focus,\n    shouldSelectOnPressUp,\n    onAction: onCellAction ? () => onCellAction(node.key) : onAction,\n    isDisabled: state.collection.size === 0\n  });\n\n  let onKeyDownCapture = (e: ReactKeyboardEvent) => {\n    if (!e.currentTarget.contains(e.target as Element) || state.isKeyboardNavigationDisabled || !ref.current || !document.activeElement) {\n      return;\n    }\n\n    let walker = getFocusableTreeWalker(ref.current);\n    walker.currentNode = document.activeElement;\n\n    switch (e.key) {\n      case 'ArrowLeft': {\n        // Find the next focusable element within the cell.\n        let focusable: FocusableElement | null = direction === 'rtl'\n          ? walker.nextNode() as FocusableElement\n          : walker.previousNode() as FocusableElement;\n\n        // Don't focus the cell itself if focusMode is \"child\"\n        if (focusMode === 'child' && focusable === ref.current) {\n          focusable = null;\n        }\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (focusable) {\n          focusSafely(focusable);\n          scrollIntoViewport(focusable, {containingElement: getScrollParent(ref.current)});\n        } else {\n          // If there is no next focusable child, then move to the next cell to the left of this one.\n          // This will be handled by useSelectableCollection. However, if there is no cell to the left\n          // of this one, only one column, and the grid doesn't focus rows, then the next key will be the\n          // same as this one. In that case we need to handle focusing either the cell or the first/last\n          // child, depending on the focus mode.\n          let prev = keyboardDelegate.getKeyLeftOf?.(node.key);\n          if (prev !== node.key) {\n            // We prevent the capturing event from reaching children of the cell, e.g. pickers.\n            // We want arrow keys to navigate to the next cell instead. We need to re-dispatch\n            // the event from a higher parent so it still bubbles and gets handled by useSelectableCollection.\n            ref.current.parentElement?.dispatchEvent(\n              new KeyboardEvent(e.nativeEvent.type, e.nativeEvent)\n            );\n            break;\n          }\n\n          if (focusMode === 'cell' && direction === 'rtl') {\n            focusSafely(ref.current);\n            scrollIntoViewport(ref.current, {containingElement: getScrollParent(ref.current)});\n          } else {\n            walker.currentNode = ref.current;\n            focusable = direction === 'rtl'\n              ? walker.firstChild() as FocusableElement\n              : last(walker);\n            if (focusable) {\n              focusSafely(focusable);\n              scrollIntoViewport(focusable, {containingElement: getScrollParent(ref.current)});\n            }\n          }\n        }\n        break;\n      }\n      case 'ArrowRight': {\n        let focusable: FocusableElement | null = direction === 'rtl'\n          ? walker.previousNode() as FocusableElement\n          : walker.nextNode() as FocusableElement;\n\n        if (focusMode === 'child' && focusable === ref.current) {\n          focusable = null;\n        }\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (focusable) {\n          focusSafely(focusable);\n          scrollIntoViewport(focusable, {containingElement: getScrollParent(ref.current)});\n        } else {\n          let next = keyboardDelegate.getKeyRightOf?.(node.key);\n          if (next !== node.key) {\n            // We prevent the capturing event from reaching children of the cell, e.g. pickers.\n            // We want arrow keys to navigate to the next cell instead. We need to re-dispatch\n            // the event from a higher parent so it still bubbles and gets handled by useSelectableCollection.\n            ref.current.parentElement?.dispatchEvent(\n              new KeyboardEvent(e.nativeEvent.type, e.nativeEvent)\n            );\n            break;\n          }\n\n          if (focusMode === 'cell' && direction === 'ltr') {\n            focusSafely(ref.current);\n            scrollIntoViewport(ref.current, {containingElement: getScrollParent(ref.current)});\n          } else {\n            walker.currentNode = ref.current;\n            focusable = direction === 'rtl'\n              ? last(walker)\n              : walker.firstChild() as FocusableElement;\n            if (focusable) {\n              focusSafely(focusable);\n              scrollIntoViewport(focusable, {containingElement: getScrollParent(ref.current)});\n            }\n          }\n        }\n        break;\n      }\n      case 'ArrowUp':\n      case 'ArrowDown':\n        // Prevent this event from reaching cell children, e.g. menu buttons. We want arrow keys to navigate\n        // to the cell above/below instead. We need to re-dispatch the event from a higher parent so it still\n        // bubbles and gets handled by useSelectableCollection.\n        if (!e.altKey && ref.current.contains(e.target as Element)) {\n          e.stopPropagation();\n          e.preventDefault();\n          ref.current.parentElement?.dispatchEvent(\n            new KeyboardEvent(e.nativeEvent.type, e.nativeEvent)\n          );\n        }\n        break;\n    }\n  };\n\n  // Grid cells can have focusable elements inside them. In this case, focus should\n  // be marshalled to that element rather than focusing the cell itself.\n  let onFocus = (e) => {\n    keyWhenFocused.current = node.key;\n    if (e.target !== ref.current) {\n      // useSelectableItem only handles setting the focused key when\n      // the focused element is the gridcell itself. We also want to\n      // set the focused key when a child element receives focus.\n      // If focus is currently visible (e.g. the user is navigating with the keyboard),\n      // then skip this. We want to restore focus to the previously focused row/cell\n      // in that case since the table should act like a single tab stop.\n      if (!isFocusVisible()) {\n        state.selectionManager.setFocusedKey(node.key);\n      }\n      return;\n    }\n\n    // If the cell itself is focused, wait a frame so that focus finishes propagatating\n    // up to the tree, and move focus to a focusable child if possible.\n    requestAnimationFrame(() => {\n      if (focusMode === 'child' && document.activeElement === ref.current) {\n        focus();\n      }\n    });\n  };\n\n  let gridCellProps: DOMAttributes = mergeProps(itemProps, {\n    role: 'gridcell',\n    onKeyDownCapture,\n    'aria-colspan': node.colSpan,\n    'aria-colindex': node.colIndex != null ? node.colIndex + 1 : undefined, // aria-colindex is 1-based\n    colSpan: isVirtualized ? undefined : node.colSpan,\n    onFocus\n  });\n\n  if (isVirtualized) {\n    gridCellProps['aria-colindex'] = (node.colIndex ?? node.index) + 1; // aria-colindex is 1-based\n  }\n\n  // When pressing with a pointer and cell selection is not enabled, usePress will be applied to the\n  // row rather than the cell. However, when the row is draggable, usePress cannot preventDefault\n  // on pointer down, so the browser will try to focus the cell which has a tabIndex applied.\n  // To avoid this, remove the tabIndex from the cell briefly on pointer down.\n  if (shouldSelectOnPressUp && gridCellProps.tabIndex != null && gridCellProps.onPointerDown == null) {\n    gridCellProps.onPointerDown = (e) => {\n      let el = e.currentTarget;\n      let tabindex = el.getAttribute('tabindex');\n      el.removeAttribute('tabindex');\n      requestAnimationFrame(() => {\n        if (tabindex != null) {\n          el.setAttribute('tabindex', tabindex);\n        }\n      });\n    };\n  }\n\n  return {\n    gridCellProps,\n    isPressed\n  };\n}\n\nfunction last(walker: TreeWalker) {\n  let next: FocusableElement | null = null;\n  let last: FocusableElement | null = null;\n  do {\n    last = walker.lastChild() as FocusableElement | null;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA4CM,SAAS,0CAA4C,KAAoB,EAAE,KAAsB,EAAE,GAAuC;IAC/I,IAAI,EAAA,MACF,IAAI,EAAA,eACJ,aAAa,EAAA,WACb,YAAY,OAAA,EAAA,uBACZ,qBAAqB,EAAA,UACrB,QAAQ,EACT,GAAG;IAEJ,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,+JAAA,YAAQ;IAC1B,IAAI,EAAA,kBAAC,gBAAgB,EAAE,SAAS,EAAA,cAAC,YAAY,EAAC,EAAC,GAAG,CAAA,6JAAA,UAAM,EAAE,GAAG,CAAC;IAE9D,wFAAwF;IACxF,6FAA6F;IAC7F,IAAI,iBAAiB,CAAA,yMAAA,SAAK,EAAc;IAExC,4DAA4D;IAC5D,uDAAuD;IACvD,IAAI,QAAQ;QACV,IAAI,IAAI,OAAO,EAAE;YACf,IAAI,aAAa,CAAA,mKAAA,yBAAqB,EAAE,IAAI,OAAO;YACnD,IAAI,cAAc,SAAS;gBACzB,iGAAiG;gBACjG,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,aAAa,KAAK,IAAI,OAAO,KAAK,SAAS,aAAa,EACxF;gBAGF,IAAI,YAAY,MAAM,gBAAgB,CAAC,kBAAkB,KAAK,SAC1D,2BAAK,cACL,WAAW,UAAU;gBACzB,IAAI,WAAW;oBACb,CAAA,2KAAA,cAAU,EAAE;oBACZ;gBACF;YACF;YAEA,IACG,eAAe,OAAO,IAAI,QAAQ,KAAK,GAAG,KAAK,eAAe,OAAO,IACtE,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,aAAa,GAE5C,CAAA,2KAAA,cAAU,EAAE,IAAI,OAAO;QAE3B;IACF;IAEA,IAAI,EAAA,WAAC,SAAS,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,8KAAA,oBAAgB,EAAE;QAC7C,kBAAkB,MAAM,gBAAgB;QACxC,KAAK,KAAK,GAAG;aACb;uBACA;eACA;+BACA;QACA,UAAU,eAAe,IAAM,aAAa,KAAK,GAAG,IAAI;QACxD,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK;IACxC;IAEA,IAAI,mBAAmB,CAAC;QACtB,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,KAAgB,MAAM,4BAA4B,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,SAAS,aAAa,EACjI;QAGF,IAAI,SAAS,CAAA,mKAAA,yBAAqB,EAAE,IAAI,OAAO;QAC/C,OAAO,WAAW,GAAG,SAAS,aAAa;QAE3C,OAAQ,EAAE,GAAG;YACX,KAAK;gBAAa;oBAChB,mDAAmD;oBACnD,IAAI,YAAqC,cAAc,QACnD,OAAO,QAAQ,KACf,OAAO,YAAY;oBAEvB,sDAAsD;oBACtD,IAAI,cAAc,WAAW,cAAc,IAAI,OAAO,EACpD,YAAY;oBAGd,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,IAAI,WAAW;wBACb,CAAA,2KAAA,cAAU,EAAE;wBACZ,CAAA,uKAAA,qBAAiB,EAAE,WAAW;4BAAC,mBAAmB,CAAA,wKAAA,kBAAc,EAAE,IAAI,OAAO;wBAAC;oBAChF,OAAO;4BAMM;wBALX,2FAA2F;wBAC3F,4FAA4F;wBAC5F,+FAA+F;wBAC/F,8FAA8F;wBAC9F,sCAAsC;wBACtC,IAAI,OAAA,CAAO,iCAAA,iBAAiB,YAAY,MAAA,QAA7B,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAA,IAAA,CAAA,kBAAgC,KAAK,GAAG;wBACnD,IAAI,SAAS,KAAK,GAAG,EAAE;gCACrB,AACA,kFAAkF,CADC;4BAEnF,kGAAkG;4BAClG;6BAAA,6BAAA,IAAI,OAAO,CAAC,aAAa,MAAA,QAAzB,+BAAA,KAAA,IAAA,KAAA,IAAA,2BAA2B,aAAa,CACtC,IAAI,cAAc,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,WAAW;4BAErD;wBACF;wBAEA,IAAI,cAAc,UAAU,cAAc,OAAO;4BAC/C,CAAA,2KAAA,cAAU,EAAE,IAAI,OAAO;4BACvB,CAAA,uKAAA,qBAAiB,EAAE,IAAI,OAAO,EAAE;gCAAC,mBAAmB,CAAA,wKAAA,kBAAc,EAAE,IAAI,OAAO;4BAAC;wBAClF,OAAO;4BACL,OAAO,WAAW,GAAG,IAAI,OAAO;4BAChC,YAAY,cAAc,QACtB,OAAO,UAAU,KACjB,2BAAK;4BACT,IAAI,WAAW;gCACb,CAAA,2KAAA,cAAU,EAAE;gCACZ,CAAA,uKAAA,qBAAiB,EAAE,WAAW;oCAAC,mBAAmB,CAAA,wKAAA,kBAAc,EAAE,IAAI,OAAO;gCAAC;4BAChF;wBACF;oBACF;oBACA;gBACF;YACA,KAAK;gBAAc;oBACjB,IAAI,YAAqC,cAAc,QACnD,OAAO,YAAY,KACnB,OAAO,QAAQ;oBAEnB,IAAI,cAAc,WAAW,cAAc,IAAI,OAAO,EACpD,YAAY;oBAGd,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,IAAI,WAAW;wBACb,CAAA,2KAAA,cAAU,EAAE;wBACZ,CAAA,uKAAA,qBAAiB,EAAE,WAAW;4BAAC,mBAAmB,CAAA,wKAAA,kBAAc,EAAE,IAAI,OAAO;wBAAC;oBAChF,OAAO;4BACM;wBAAX,IAAI,OAAA,CAAO,kCAAA,iBAAiB,aAAa,MAAA,QAA9B,oCAAA,KAAA,IAAA,KAAA,IAAA,gCAAA,IAAA,CAAA,kBAAiC,KAAK,GAAG;wBACpD,IAAI,SAAS,KAAK,GAAG,EAAE;gCACrB,AACA,kFAAkF,CADC;4BAEnF,kGAAkG;4BAClG;6BAAA,8BAAA,IAAI,OAAO,CAAC,aAAa,MAAA,QAAzB,gCAAA,KAAA,IAAA,KAAA,IAAA,4BAA2B,aAAa,CACtC,IAAI,cAAc,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,WAAW;4BAErD;wBACF;wBAEA,IAAI,cAAc,UAAU,cAAc,OAAO;4BAC/C,CAAA,2KAAA,cAAU,EAAE,IAAI,OAAO;4BACvB,CAAA,uKAAA,qBAAiB,EAAE,IAAI,OAAO,EAAE;gCAAC,mBAAmB,CAAA,wKAAA,kBAAc,EAAE,IAAI,OAAO;4BAAC;wBAClF,OAAO;4BACL,OAAO,WAAW,GAAG,IAAI,OAAO;4BAChC,YAAY,cAAc,QACtB,2BAAK,UACL,OAAO,UAAU;4BACrB,IAAI,WAAW;gCACb,CAAA,2KAAA,cAAU,EAAE;gCACZ,CAAA,uKAAA,qBAAiB,EAAE,WAAW;oCAAC,mBAAmB,CAAA,wKAAA,kBAAc,EAAE,IAAI,OAAO;gCAAC;4BAChF;wBACF;oBACF;oBACA;gBACF;YACA,KAAK;YACL,KAAK;gBACH,oGAAoG;gBACpG,qGAAqG;gBACrG,uDAAuD;gBACvD,IAAI,CAAC,EAAE,MAAM,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAc;wBAG1D;oBAFA,EAAE,eAAe;oBACjB,EAAE,cAAc;qBAChB,8BAAA,IAAI,OAAO,CAAC,aAAa,MAAA,QAAzB,gCAAA,KAAA,IAAA,KAAA,IAAA,4BAA2B,aAAa,CACtC,IAAI,cAAc,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,WAAW;gBAEvD;gBACA;QACJ;IACF;IAEA,iFAAiF;IACjF,sEAAsE;IACtE,IAAI,UAAU,CAAC;QACb,eAAe,OAAO,GAAG,KAAK,GAAG;QACjC,IAAI,EAAE,MAAM,KAAK,IAAI,OAAO,EAAE;YAC5B,8DAA8D;YAC9D,8DAA8D;YAC9D,2DAA2D;YAC3D,iFAAiF;YACjF,8EAA8E;YAC9E,kEAAkE;YAClE,IAAI,CAAC,CAAA,+KAAA,iBAAa,KAChB,MAAM,gBAAgB,CAAC,aAAa,CAAC,KAAK,GAAG;YAE/C;QACF;QAEA,mFAAmF;QACnF,mEAAmE;QACnE,sBAAsB;YACpB,IAAI,cAAc,WAAW,SAAS,aAAa,KAAK,IAAI,OAAO,EACjE;QAEJ;IACF;IAEA,IAAI,gBAA+B,CAAA,mKAAA,aAAS,EAAE,WAAW;QACvD,MAAM;0BACN;QACA,gBAAgB,KAAK,OAAO;QAC5B,iBAAiB,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ,GAAG,IAAI;QAC7D,SAAS,gBAAgB,YAAY,KAAK,OAAO;iBACjD;IACF;QAGoC;IADpC,IAAI,eACF,aAAa,CAAC,gBAAgB,GAAI,CAAA,CAAA,iBAAA,KAAK,QAAQ,MAAA,QAAb,mBAAA,KAAA,IAAA,iBAAiB,KAAK,KAAI,IAAK,GAAG,2BAA2B;IAGjG,kGAAkG;IAClG,+FAA+F;IAC/F,2FAA2F;IAC3F,4EAA4E;IAC5E,IAAI,yBAAyB,cAAc,QAAQ,IAAI,QAAQ,cAAc,aAAa,IAAI,MAC5F,cAAc,aAAa,GAAG,CAAC;QAC7B,IAAI,KAAK,EAAE,aAAa;QACxB,IAAI,WAAW,GAAG,YAAY,CAAC;QAC/B,GAAG,eAAe,CAAC;QACnB,sBAAsB;YACpB,IAAI,YAAY,MACd,GAAG,YAAY,CAAC,YAAY;QAEhC;IACF;IAGF,OAAO;uBACL;mBACA;IACF;AACF;AAEA,SAAS,2BAAK,MAAkB;IAC9B,IAAI,OAAgC;IACpC,IAAI,OAAgC;IACpC,GAAG;QACD,OAAO,OAAO,SAAS;QACvB,IAAI,MACF,OAAO;IAEX,QAAS,KAAM;IACf,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2585, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/ar-AE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/ar-AE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} غير المحدد\",\n  \"longPressToSelect\": \"اضغط مطولًا للدخول إلى وضع التحديد.\",\n  \"select\": \"تحديد\",\n  \"selectedAll\": \"جميع العناصر المحددة.\",\n  \"selectedCount\": \"{count, plural, =0 {لم يتم تحديد عناصر} one {# عنصر محدد} other {# عنصر محدد}}.\",\n  \"selectedItem\": \"{item} المحدد\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iEAAW,CAAC;IACvE,qBAAqB,CAAC,iNAAmC,CAAC;IAC1D,UAAU,CAAC,mCAAK,CAAC;IACjB,eAAe,CAAC,iIAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4GAAkB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0DAAU,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0DAAU,CAAC;QAAA,GAAG,CAAC,CAAC;IACxN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,2CAAO,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 2607, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/bg-BG.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/bg-BG.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} не е избран.\",\n  \"longPressToSelect\": \"Натиснете и задръжте за да влезете в избирателен режим.\",\n  \"select\": \"Изберете\",\n  \"selectedAll\": \"Всички елементи са избрани.\",\n  \"selectedCount\": \"{count, plural, =0 {Няма избрани елементи} one {# избран елемент} other {# избрани елементи}}.\",\n  \"selectedItem\": \"{item} избран.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mEAAa,CAAC;IACzE,qBAAqB,CAAC,2UAAuD,CAAC;IAC9E,UAAU,CAAC,wDAAQ,CAAC;IACpB,eAAe,CAAC,qKAA2B,CAAC;IAC5C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uIAAqB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,2GAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IACvO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,4CAAQ,CAAC;AAClD", "debugId": null}}, {"offset": {"line": 2629, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/cs-CZ.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Polo<PERSON><PERSON> {item} není v<PERSON>.\",\n  \"longPressToSelect\": \"Dlouhým stisknutím přejdete do režimu výběru.\",\n  \"select\": \"Vybrat\",\n  \"selectedAll\": \"Vybrány všechny položky.\",\n  \"selectedCount\": \"{count, plural, =0 {Nevybrány žádné <PERSON>} one {Vybrána # položka} other {Vybráno # položek}}.\",\n  \"selectedItem\": \"Vybrána polož<PERSON> {item}.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,cAAQ,EAAE,KAAK,IAAI,CAAC,oBAAc,CAAC;IAClF,qBAAqB,CAAC,wEAA6C,CAAC;IACpE,UAAU,CAAC,MAAM,CAAC;IAClB,eAAe,CAAC,uCAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4CAAuB,CAAC;YAAE,KAAK,IAAM,CAAC,WAAQ,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAQ,CAAC;YAAE,OAAO,IAAM,CAAC,WAAQ,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAQ,CAAC;QAAA,GAAG,CAAC,CAAC;IACzO,gBAAgB,CAAC,OAAS,CAAC,yBAAgB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC3D", "debugId": null}}, {"offset": {"line": 2651, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/da-DK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/da-DK.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} ikke valgt.\",\n  \"longPressToSelect\": \"Lav et langt tryk for at aktivere valgtilstand.\",\n  \"select\": \"Vælg\",\n  \"selectedAll\": \"Alle elementer valgt.\",\n  \"selectedCount\": \"{count, plural, =0 {Ingen elementer valgt} one {# element valgt} other {# elementer valgt}}.\",\n  \"selectedItem\": \"{item} valgt.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACxE,qBAAqB,CAAC,+CAA+C,CAAC;IACtE,UAAU,CAAC,OAAI,CAAC;IAChB,eAAe,CAAC,qBAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qBAAqB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,gBAAgB,CAAC;QAAA,GAAG,CAAC,CAAC;IACrO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 2673, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/de-DE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/de-DE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nicht ausgewählt.\",\n  \"longPressToSelect\": \"Gedr<PERSON><PERSON> halten, um Auswahlmodus zu öffnen.\",\n  \"select\": \"Auswählen\",\n  \"selectedAll\": \"Alle Elemente ausgewählt.\",\n  \"selectedCount\": \"{count, plural, =0 {Keine Elemente ausgewählt} one {# Element ausgewählt} other {# Elemente ausgewählt}}.\",\n  \"selectedItem\": \"{item} ausgewählt.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,qBAAkB,CAAC;IAC9E,qBAAqB,CAAC,iDAA2C,CAAC;IAClE,UAAU,CAAC,YAAS,CAAC;IACrB,eAAe,CAAC,4BAAyB,CAAC;IAC1C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4BAAyB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,sBAAmB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,uBAAoB,CAAC;QAAA,GAAG,CAAC,CAAC;IAClP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAY,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 2695, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/el-GR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/el-GR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Δεν επιλέχθηκε το στοιχείο {item}.\",\n  \"longPressToSelect\": \"Πατήστε παρατεταμένα για να μπείτε σε λειτουργία επιλογής.\",\n  \"select\": \"Επιλογή\",\n  \"selectedAll\": \"Επιλέχθηκαν όλα τα στοιχεία.\",\n  \"selectedCount\": \"{count, plural, =0 {Δεν επιλέχθηκαν στοιχεία} one {Επιλέχθηκε # στοιχείο} other {Επιλέχθηκαν # στοιχεία}}.\",\n  \"selectedItem\": \"Επιλέχθηκε το στοιχείο {item}.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,qKAA2B,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IACxF,qBAAqB,CAAC,sWAA0D,CAAC;IACjF,UAAU,CAAC,iDAAO,CAAC;IACnB,eAAe,CAAC,4KAA4B,CAAC;IAC7C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4JAAwB,CAAC;YAAE,KAAK,IAAM,CAAC,uEAAW,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yDAAS,CAAC;YAAE,OAAO,IAAM,CAAC,8EAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yDAAS,CAAC;QAAA,GAAG,CAAC,CAAC;IACnP,gBAAgB,CAAC,OAAS,CAAC,+IAAuB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAClE", "debugId": null}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/en-US.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/en-US.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} not selected.\",\n  \"select\": \"Select\",\n  \"selectedCount\": \"{count, plural, =0 {No items selected} one {# item selected} other {# items selected}}.\",\n  \"selectedAll\": \"All items selected.\",\n  \"selectedItem\": \"{item} selected.\",\n  \"longPressToSelect\": \"Long press to enter selection mode.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;IAC1E,UAAU,CAAC,MAAM,CAAC;IAClB,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,iBAAiB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,eAAe,CAAC;QAAA,GAAG,CAAC,CAAC;IAChO,eAAe,CAAC,mBAAmB,CAAC;IACpC,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;IAClD,qBAAqB,CAAC,mCAAmC,CAAC;AAC5D", "debugId": null}}, {"offset": {"line": 2739, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/es-ES.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/es-ES.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} no seleccionado.\",\n  \"longPressToSelect\": \"Mantenga pulsado para abrir el modo de selección.\",\n  \"select\": \"Seleccionar\",\n  \"selectedAll\": \"Todos los elementos seleccionados.\",\n  \"selectedCount\": \"{count, plural, =0 {Ningún elemento seleccionado} one {# elemento seleccionado} other {# elementos seleccionados}}.\",\n  \"selectedItem\": \"{item} seleccionado.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC;IAC7E,qBAAqB,CAAC,oDAAiD,CAAC;IACxE,UAAU,CAAC,WAAW,CAAC;IACvB,eAAe,CAAC,kCAAkC,CAAC;IACnD,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,+BAA4B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,sBAAsB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,wBAAwB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC5P,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;AACxD", "debugId": null}}, {"offset": {"line": 2761, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/et-EE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/et-EE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} pole valitud.\",\n  \"longPressToSelect\": \"Valikurežiimi sisenemiseks vajutage pikalt.\",\n  \"select\": \"Vali\",\n  \"selectedAll\": \"Kõik üksused valitud.\",\n  \"selectedCount\": \"{count, plural, =0 {Üksusi pole valitud} one {# üksus valitud} other {# üksust valitud}}.\",\n  \"selectedItem\": \"{item} valitud.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;IAC1E,qBAAqB,CAAC,iDAA2C,CAAC;IAClE,UAAU,CAAC,IAAI,CAAC;IAChB,eAAe,CAAC,2BAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,sBAAmB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAe,CAAC;QAAA,GAAG,CAAC,CAAC;IAClO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/fi-FI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/fi-FI.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"<PERSON><PERSON><PERSON><PERSON> {item} ei valittu.\",\n  \"longPressToSelect\": \"<PERSON><PERSON><PERSON> valintatilaan painamalla pitkään.\",\n  \"select\": \"<PERSON><PERSON><PERSON>\",\n  \"selectedAll\": \"<PERSON><PERSON><PERSON> kohteet valittu.\",\n  \"selectedCount\": \"{count, plural, =0 {Ei yhtään kohdetta valittu} one {# kohde valittu} other {# kohdetta valittu}}.\",\n  \"selectedItem\": \"{item} valittu.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC;IACjF,qBAAqB,CAAC,8CAAwC,CAAC;IAC/D,UAAU,CAAC,OAAO,CAAC;IACnB,eAAe,CAAC,uBAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,gCAA0B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,cAAc,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC3O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 2805, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/fr-FR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/fr-FR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} non sélectionné.\",\n  \"longPressToSelect\": \"Appuyez de manière prolongée pour passer en mode de sélection.\",\n  \"select\": \"Sélectionner\",\n  \"selectedAll\": \"Tous les éléments sélectionnés.\",\n  \"selectedCount\": \"{count, plural, =0 {Aucun élément sélectionné} one {# élément sélectionné} other {# éléments sélectionnés}}.\",\n  \"selectedItem\": \"{item} sélectionné.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,uBAAiB,CAAC;IAC7E,qBAAqB,CAAC,uEAA8D,CAAC;IACrF,UAAU,CAAC,eAAY,CAAC;IACxB,eAAe,CAAC,2CAA+B,CAAC;IAChD,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qCAAyB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,gCAAoB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kCAAsB,CAAC;QAAA,GAAG,CAAC,CAAC;IACrP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mBAAa,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 2827, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/he-IL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/he-IL.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} לא נבחר.\",\n  \"longPressToSelect\": \"הקשה ארוכה לכניסה למצב בחירה.\",\n  \"select\": \"בחר\",\n  \"selectedAll\": \"כל הפריטים נבחרו.\",\n  \"selectedCount\": \"{count, plural, =0 {לא נבחרו פריטים} one {פריט # נבחר} other {# פריטים נבחרו}}.\",\n  \"selectedItem\": \"{item} נבחר.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,6CAAS,CAAC;IACrE,qBAAqB,CAAC,6KAA6B,CAAC;IACpD,UAAU,CAAC,qBAAG,CAAC;IACf,eAAe,CAAC,qGAAiB,CAAC;IAClC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,6FAAe,CAAC;YAAE,KAAK,IAAM,CAAC,6BAAK,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6BAAK,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,+EAAa,CAAC;QAAA,GAAG,CAAC,CAAC;IACxN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,8BAAM,CAAC;AAChD", "debugId": null}}, {"offset": {"line": 2849, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/hr-HR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/hr-HR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Stavka {item} nije odabrana.\",\n  \"longPressToSelect\": \"Dugo pritisnite za ulazak u način odabira.\",\n  \"select\": \"Odaberite\",\n  \"selectedAll\": \"Odabrane su sve stavke.\",\n  \"selectedCount\": \"{count, plural, =0 {Nije odabrana nijedna stavka} one {Odabrana je # stavka} other {Odabrano je # stavki}}.\",\n  \"selectedItem\": \"Stavka {item} je odabrana.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC;IAClF,qBAAqB,CAAC,gDAA0C,CAAC;IACjE,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,uBAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4BAA4B,CAAC;YAAE,KAAK,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;YAAE,OAAO,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;QAAA,GAAG,CAAC,CAAC;IACpP,gBAAgB,CAAC,OAAS,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,aAAa,CAAC;AAC9D", "debugId": null}}, {"offset": {"line": 2871, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/hu-HU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/hu-HU.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nincs kijelölve.\",\n  \"longPressToSelect\": \"Nyomja hosszan a kijelöléshez.\",\n  \"select\": \"Ki<PERSON>lölés\",\n  \"selectedAll\": \"Az összes elem kijelölve.\",\n  \"selectedCount\": \"{count, plural, =0 {Egy elem sincs kijelölve} one {# elem kijelölve} other {# elem kijelölve}}.\",\n  \"selectedItem\": \"{item} kijelölve.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,oBAAiB,CAAC;IAC7E,qBAAqB,CAAC,oCAA8B,CAAC;IACrD,UAAU,CAAC,eAAS,CAAC;IACrB,eAAe,CAAC,+BAAyB,CAAC;IAC1C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,2BAAwB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAe,CAAC;QAAA,GAAG,CAAC,CAAC;IACxO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAW,CAAC;AACrD", "debugId": null}}, {"offset": {"line": 2893, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/it-IT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/it-IT.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} non selezionato.\",\n  \"longPressToSelect\": \"Premi a lungo per passare alla modalità di selezione.\",\n  \"select\": \"Seleziona\",\n  \"selectedAll\": \"Tutti gli elementi selezionati.\",\n  \"selectedCount\": \"{count, plural, =0 {Nessun elemento selezionato} one {# elemento selezionato} other {# elementi selezionati}}.\",\n  \"selectedItem\": \"{item} selezionato.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC;IAC7E,qBAAqB,CAAC,wDAAqD,CAAC;IAC5E,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,+BAA+B,CAAC;IAChD,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,2BAA2B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,qBAAqB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,qBAAqB,CAAC;QAAA,GAAG,CAAC,CAAC;IACvP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 2915, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/ja-JP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/ja-JP.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} が選択されていません。\",\n  \"longPressToSelect\": \"長押しして選択モードを開きます。\",\n  \"select\": \"選択\",\n  \"selectedAll\": \"すべての項目を選択しました。\",\n  \"selectedCount\": \"{count, plural, =0 {項目が選択されていません} one {# 項目を選択しました} other {# 項目を選択しました}}。\",\n  \"selectedItem\": \"{item} を選択しました。\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,yFAAY,CAAC;IACxE,qBAAqB,CAAC,gIAAgB,CAAC;IACvC,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,gHAAc,CAAC;IAC/B,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,gGAAY,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yEAAU,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yEAAU,CAAC;QAAA,GAAG,QAAC,CAAC;IAClN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,iEAAS,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 2937, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/ko-KR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/ko-KR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item}이(가) 선택되지 않았습니다.\",\n  \"longPressToSelect\": \"선택 모드로 들어가려면 길게 누르십시오.\",\n  \"select\": \"선택\",\n  \"selectedAll\": \"모든 항목이 선택되었습니다.\",\n  \"selectedCount\": \"{count, plural, =0 {선택된 항목이 없습니다} one {#개 항목이 선택되었습니다} other {#개 항목이 선택되었습니다}}.\",\n  \"selectedItem\": \"{item}이(가) 선택되었습니다.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,6FAAgB,CAAC;IAC5E,qBAAqB,CAAC,6IAAsB,CAAC;IAC7C,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,mGAAe,CAAC;IAChC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,kFAAY,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0FAAa,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0FAAa,CAAC;QAAA,GAAG,CAAC,CAAC;IACxN,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,4EAAa,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 2959, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/lt-LT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/lt-LT.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nepasirinkta.\",\n  \"longPressToSelect\": \"Norėdami įjungti pasirinkimo re<PERSON>, paspauskite ir palaikykite.\",\n  \"select\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"selectedAll\": \"Pasirinkti visi elementai.\",\n  \"selectedCount\": \"{count, plural, =0 {Nepasirinktas nė vienas elementas} one {Pasirinktas # elementas} other {Pasirinkta elementų: #}}.\",\n  \"selectedItem\": \"Pasirinkta: {item}.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;IAC1E,qBAAqB,CAAC,wFAAgE,CAAC;IACvF,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,0BAA0B,CAAC;IAC3C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uCAAiC,CAAC;YAAE,KAAK,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,UAAU,CAAC;YAAE,OAAO,IAAM,CAAC,2BAAqB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IAC9P,gBAAgB,CAAC,OAAS,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 2981, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/lv-LV.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/lv-LV.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Vienums {item} nav atlasīts.\",\n  \"longPressToSelect\": \"Ilgi turiet nospiestu. lai ieslēgtu atlases režīmu.\",\n  \"select\": \"Atlasīt\",\n  \"selectedAll\": \"Atlasīti visi vienumi.\",\n  \"selectedCount\": \"{count, plural, =0 {Nav atlasīts neviens vienums} one {Atlasīto vienumu skaits: #} other {Atlasīto vienumu skaits: #}}.\",\n  \"selectedItem\": \"Atlasīts vienums {item}.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,oBAAc,CAAC;IAClF,qBAAqB,CAAC,qEAAmD,CAAC;IAC1E,UAAU,CAAC,aAAO,CAAC;IACnB,eAAe,CAAC,4BAAsB,CAAC;IACvC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,kCAA4B,CAAC;YAAE,KAAK,IAAM,CAAC,+BAAyB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;YAAE,OAAO,IAAM,CAAC,+BAAyB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IAChQ,gBAAgB,CAAC,OAAS,CAAC,uBAAiB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC5D", "debugId": null}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/nb-NO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/nb-NO.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} er ikke valgt.\",\n  \"longPressToSelect\": \"Bruk et langt trykk for å gå inn i valgmodus.\",\n  \"select\": \"Velg\",\n  \"selectedAll\": \"Alle elementer er valgt.\",\n  \"selectedCount\": \"{count, plural, =0 {Ingen elementer er valgt} one {# element er valgt} other {# elementer er valgt}}.\",\n  \"selectedItem\": \"{item} er valgt.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAe,CAAC;IAC3E,qBAAqB,CAAC,mDAA6C,CAAC;IACpE,UAAU,CAAC,IAAI,CAAC;IAChB,eAAe,CAAC,wBAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,wBAAwB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC9O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 3025, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/nl-NL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/nl-NL.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} niet geselecteerd.\",\n  \"longPressToSelect\": \"Druk lang om de selectiemodus te openen.\",\n  \"select\": \"Selecteren\",\n  \"selectedAll\": \"Alle items geselecteerd.\",\n  \"selectedCount\": \"{count, plural, =0 {Geen items geselecteerd} one {# item geselecteerd} other {# items geselecteerd}}.\",\n  \"selectedItem\": \"{item} geselecteerd.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mBAAmB,CAAC;IAC/E,qBAAqB,CAAC,wCAAwC,CAAC;IAC/D,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,wBAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAkB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC9O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAc,CAAC;AACxD", "debugId": null}}, {"offset": {"line": 3047, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/pl-PL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/pl-PL.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"<PERSON>e zaznaczono {item}.\",\n  \"longPressToSelect\": \"Naciśnij i przytrzymaj, aby wejść do trybu wyboru.\",\n  \"select\": \"Zaznacz\",\n  \"selectedAll\": \"Wszystkie zaznaczone elementy.\",\n  \"selectedCount\": \"{count, plural, =0 {Nie zaznaczono żadnych elementów} one {# zaznaczony element} other {# zaznaczonych elementów}}.\",\n  \"selectedItem\": \"Zaznaczono {item}.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IAC5E,qBAAqB,CAAC,oEAAkD,CAAC;IACzE,UAAU,CAAC,OAAO,CAAC;IACnB,eAAe,CAAC,8BAA8B,CAAC;IAC/C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,yCAAgC,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0BAAuB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC5P,gBAAgB,CAAC,OAAS,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 3069, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/pt-BR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/pt-BR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} não selecionado.\",\n  \"longPressToSelect\": \"Mantenha pressionado para entrar no modo de seleção.\",\n  \"select\": \"Selecionar\",\n  \"selectedAll\": \"Todos os itens selecionados.\",\n  \"selectedCount\": \"{count, plural, =0 {Nenhum item selecionado} one {# item selecionado} other {# itens selecionados}}.\",\n  \"selectedItem\": \"{item} selecionado.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,oBAAiB,CAAC;IAC7E,qBAAqB,CAAC,0DAAoD,CAAC;IAC3E,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,4BAA4B,CAAC;IAC7C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC7O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 3091, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/pt-PT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/pt-PT.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} não selecionado.\",\n  \"longPressToSelect\": \"Prima continuamente para entrar no modo de seleção.\",\n  \"select\": \"Selecionar\",\n  \"selectedAll\": \"Todos os itens selecionados.\",\n  \"selectedCount\": \"{count, plural, =0 {Nenhum item selecionado} one {# item selecionado} other {# itens selecionados}}.\",\n  \"selectedItem\": \"{item} selecionado.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,oBAAiB,CAAC;IAC7E,qBAAqB,CAAC,yDAAmD,CAAC;IAC1E,UAAU,CAAC,UAAU,CAAC;IACtB,eAAe,CAAC,4BAA4B,CAAC;IAC7C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC7O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 3113, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/ro-RO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/ro-RO.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} neselectat.\",\n  \"longPressToSelect\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lung pentru a intra în modul de selectare.\",\n  \"select\": \"Selectare\",\n  \"selectedAll\": \"Toate elementele selectate.\",\n  \"selectedCount\": \"{count, plural, =0 {Niciun element selectat} one {# element selectat} other {# elemente selectate}}.\",\n  \"selectedItem\": \"{item} selectat.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;IACxE,qBAAqB,CAAC,iEAAkD,CAAC;IACzE,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,2BAA2B,CAAC;IAC5C,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,mBAAmB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC7O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/ru-RU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/ru-RU.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} не выбрано.\",\n  \"longPressToSelect\": \"Нажмите и удерживайте для входа в режим выбора.\",\n  \"select\": \"Выбрать\",\n  \"selectedAll\": \"Выбраны все элементы.\",\n  \"selectedCount\": \"{count, plural, =0 {Нет выбранных элементов} one {# элемент выбран} other {# элементов выбрано}}.\",\n  \"selectedItem\": \"{item} выбрано.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,kEAAY,CAAC;IACxE,qBAAqB,CAAC,yRAA+C,CAAC;IACtE,UAAU,CAAC,iDAAO,CAAC;IACnB,eAAe,CAAC,iIAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qJAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kHAAkB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC1O,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mDAAS,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 3157, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/sk-SK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/sk-SK.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Nevybra<PERSON> položky: {item}.\",\n  \"longPressToSelect\": \"Dlhším stlačením prejdite do režimu výberu.\",\n  \"select\": \"Vybrať\",\n  \"selectedAll\": \"Všetky vybraté položky.\",\n  \"selectedCount\": \"{count, plural, =0 {Žiadne vybraté položky} one {# vybratá položka} other {Počet vybratých položiek:#}}.\",\n  \"selectedItem\": \"Vybraté položky: {item}.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,4BAAmB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;IAChF,qBAAqB,CAAC,sEAA2C,CAAC;IAClE,UAAU,CAAC,YAAM,CAAC;IAClB,eAAe,CAAC,sCAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qCAAsB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAgB,CAAC;YAAE,OAAO,IAAM,CAAC,wCAAyB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IACjP,gBAAgB,CAAC,OAAS,CAAC,0BAAiB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC5D", "debugId": null}}, {"offset": {"line": 3179, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/sl-SI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/sl-SI.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Element {item} ni izbran.\",\n  \"longPressToSelect\": \"Za izbirni način pritisnite in dlje časa držite.\",\n  \"select\": \"Izberite\",\n  \"selectedAll\": \"Vsi elementi so izbrani.\",\n  \"selectedCount\": \"{count, plural, =0 {Noben element ni izbran} one {# element je izbran} other {# elementov je izbranih}}.\",\n  \"selectedItem\": \"Element {item} je izbran.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;IAC/E,qBAAqB,CAAC,kEAAgD,CAAC;IACvE,UAAU,CAAC,QAAQ,CAAC;IACpB,eAAe,CAAC,wBAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAkB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,sBAAsB,CAAC;QAAA,GAAG,CAAC,CAAC;IACjP,gBAAgB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;AAC7D", "debugId": null}}, {"offset": {"line": 3201, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/sr-SP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/sr-SP.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} nije izabrano.\",\n  \"longPressToSelect\": \"Dugo pritisnite za ulazak u režim biranja.\",\n  \"select\": \"Izaberite\",\n  \"selectedAll\": \"<PERSON>zabrane su sve stavke.\",\n  \"selectedCount\": \"{count, plural, =0 {Nije izabrana nijedna stavka} one {Izabrana je # stavka} other {Izabrano je # stavki}}.\",\n  \"selectedItem\": \"{item} je izabrano.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,eAAe,CAAC;IAC3E,qBAAqB,CAAC,gDAA0C,CAAC;IACjE,UAAU,CAAC,SAAS,CAAC;IACrB,eAAe,CAAC,uBAAuB,CAAC;IACxC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,4BAA4B,CAAC;YAAE,KAAK,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;YAAE,OAAO,IAAM,CAAC,YAAY,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC;QAAA,GAAG,CAAC,CAAC;IACpP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;AACvD", "debugId": null}}, {"offset": {"line": 3223, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/sv-SE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/sv-SE.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} ej markerat.\",\n  \"longPressToSelect\": \"Tryck länge när du vill öppna väljarläge.\",\n  \"select\": \"Markera\",\n  \"selectedAll\": \"Alla markerade objekt.\",\n  \"selectedCount\": \"{count, plural, =0 {Inga markerade objekt} one {# markerat objekt} other {# markerade objekt}}.\",\n  \"selectedItem\": \"{item} markerat.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC;IACzE,qBAAqB,CAAC,wDAAyC,CAAC;IAChE,UAAU,CAAC,OAAO,CAAC;IACnB,eAAe,CAAC,sBAAsB,CAAC;IACvC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,qBAAqB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,gBAAgB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IACxO,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 3245, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/tr-TR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/tr-TR.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} seçilmedi.\",\n  \"longPressToSelect\": \"Seçim moduna girmek için uzun basın.\",\n  \"select\": \"Seç\",\n  \"selectedAll\": \"Tüm ögeler seçildi.\",\n  \"selectedCount\": \"{count, plural, =0 {Hiçbir öge seçilmedi} one {# öge seçildi} other {# öge seçildi}}.\",\n  \"selectedItem\": \"{item} seçildi.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,cAAW,CAAC;IACvE,qBAAqB,CAAC,gDAAoC,CAAC;IAC3D,UAAU,CAAC,MAAG,CAAC;IACf,eAAe,CAAC,4BAAmB,CAAC;IACpC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,6BAAoB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAY,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAY,CAAC;QAAA,GAAG,CAAC,CAAC;IAC9N,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,YAAS,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 3267, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/uk-UA.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/uk-UA.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"{item} не вибрано.\",\n  \"longPressToSelect\": \"Виконайте довге натиснення, щоб перейти в режим вибору.\",\n  \"select\": \"Вибрати\",\n  \"selectedAll\": \"Усі елементи вибрано.\",\n  \"selectedCount\": \"{count, plural, =0 {Жодних елементів не вибрано} one {# елемент вибрано} other {Вибрано елементів: #}}.\",\n  \"selectedItem\": \"{item} вибрано.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,kEAAY,CAAC;IACxE,qBAAqB,CAAC,2UAAuD,CAAC;IAC9E,UAAU,CAAC,iDAAO,CAAC;IACnB,eAAe,CAAC,iIAAqB,CAAC;IACtC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,2KAA2B,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,oGAAgB,CAAC;YAAE,OAAO,IAAM,CAAC,mHAAmB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,GAAG;QAAA,GAAG,CAAC,CAAC;IAChP,gBAAgB,CAAC,OAAS,GAAG,KAAK,IAAI,CAAC,mDAAS,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 3289, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/zh-CN.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/zh-CN.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"未选择 {item}。\",\n  \"longPressToSelect\": \"长按以进入选择模式。\",\n  \"select\": \"选择\",\n  \"selectedAll\": \"已选择所有项目。\",\n  \"selectedCount\": \"{count, plural, =0 {未选择项目} one {已选择 # 个项目} other {已选择 # 个项目}}。\",\n  \"selectedItem\": \"已选择 {item}。\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,yBAAI,EAAE,KAAK,IAAI,CAAC,QAAC,CAAC;IACjE,qBAAqB,CAAC,gFAAU,CAAC;IACjC,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,gEAAQ,CAAC;IACzB,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,wCAAK,CAAC;YAAE,KAAK,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;YAAE,OAAO,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;QAAA,GAAG,QAAC,CAAC;IACvM,gBAAgB,CAAC,OAAS,CAAC,yBAAI,EAAE,KAAK,IAAI,CAAC,QAAC,CAAC;AAC/C", "debugId": null}}, {"offset": {"line": 3311, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/zh-TW.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/intl/zh-TW.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"未選取「{item}」。\",\n  \"longPressToSelect\": \"長按以進入選擇模式。\",\n  \"select\": \"選取\",\n  \"selectedAll\": \"已選取所有項目。\",\n  \"selectedCount\": \"{count, plural, =0 {未選取任何項目} one {已選取 # 個項目} other {已選取 # 個項目}}。\",\n  \"selectedItem\": \"已選取「{item}」。\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,gCAAI,EAAE,KAAK,IAAI,CAAC,gBAAE,CAAC;IAClE,qBAAqB,CAAC,gFAAU,CAAC;IACjC,UAAU,CAAC,gBAAE,CAAC;IACd,eAAe,CAAC,gEAAQ,CAAC;IACzB,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,wDAAO,CAAC;YAAE,KAAK,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;YAAE,OAAO,IAAM,CAAC,yBAAI,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,yBAAI,CAAC;QAAA,GAAG,QAAC,CAAC;IACzM,gBAAgB,CAAC,OAAS,CAAC,gCAAI,EAAE,KAAK,IAAI,CAAC,gBAAE,CAAC;AAChD", "debugId": null}}, {"offset": {"line": 3333, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/intlStrings.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/%2A.js"], "sourcesContent": ["const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4BAAiB;IACf,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;IACT,sKAAS,UAAA;AACX", "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useGridSelectionCheckbox.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useGridSelectionCheckbox.ts"], "sourcesContent": ["import {AriaCheckboxProps} from '@react-types/checkbox';\nimport {GridCollection} from '@react-types/grid';\nimport {GridState} from '@react-stately/grid';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {Key} from '@react-types/shared';\nimport {useId} from '@react-aria/utils';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaGridSelectionCheckboxProps {\n  /** A unique key for the checkbox. */\n  key: Key\n}\n\nexport interface GridSelectionCheckboxAria {\n  /** Props for the row selection checkbox element. */\n  checkboxProps: AriaCheckboxProps\n}\n\n\n/**\n * Provides the behavior and accessibility implementation for a selection checkbox in a grid.\n * @param props - Props for the selection checkbox.\n * @param state - State of the grid, as returned by `useGridState`.\n */\nexport function useGridSelectionCheckbox<T, C extends GridCollection<T>>(props: AriaGridSelectionCheckboxProps, state: GridState<T, C>): GridSelectionCheckboxAria {\n  let {key} = props;\n\n  let manager = state.selectionManager;\n  let checkboxId = useId();\n  let isDisabled = !state.selectionManager.canSelectItem(key);\n  let isSelected = state.selectionManager.isSelected(key);\n\n  // Checkbox should always toggle selection, regardless of selectionBehavior.\n  let onChange = () => manager.toggleSelection(key);\n\n  const stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/grid');\n\n  return {\n    checkboxProps: {\n      id: checkboxId,\n      'aria-label': stringFormatter.format('select'),\n      isSelected,\n      isDisabled,\n      onChange\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAyBO,SAAS,0CAAyD,KAAqC,EAAE,KAAsB;IACpI,IAAI,EAAA,KAAC,GAAG,EAAC,GAAG;IAEZ,IAAI,UAAU,MAAM,gBAAgB;IACpC,IAAI,aAAa,CAAA,8JAAA,QAAI;IACrB,IAAI,aAAa,CAAC,MAAM,gBAAgB,CAAC,aAAa,CAAC;IACvD,IAAI,aAAa,MAAM,gBAAgB,CAAC,UAAU,CAAC;IAEnD,4EAA4E;IAC5E,IAAI,WAAW,IAAM,QAAQ,eAAe,CAAC;IAE7C,MAAM,kBAAkB,CAAA,mLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,+JAAA,CAAA,UAAA,CAAW,GAAG;IAElE,OAAO;QACL,eAAe;YACb,IAAI;YACJ,cAAc,gBAAgB,MAAM,CAAC;wBACrC;wBACA;sBACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3484, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/GridKeyboardDelegate.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/GridKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Direction, DisabledBehavior, Key, KeyboardDelegate, LayoutDelegate, Node, Rect, RefObject, Size} from '@react-types/shared';\nimport {DOMLayoutDelegate} from '@react-aria/selection';\nimport {getChildNodes, getFirstItem, getLastItem, getNthItem} from '@react-stately/collections';\nimport {GridCollection, GridNode} from '@react-types/grid';\n\nexport interface GridKeyboardDelegateOptions<C> {\n  collection: C,\n  disabledKeys: Set<Key>,\n  disabledBehavior?: DisabledBehavior,\n  ref?: RefObject<HTMLElement | null>,\n  direction: Direction,\n  collator?: Intl.Collator,\n  layoutDelegate?: LayoutDelegate,\n  /** @deprecated - Use layoutDelegate instead. */\n  layout?: DeprecatedLayout,\n  focusMode?: 'row' | 'cell'\n}\n\nexport class GridKeyboardDelegate<T, C extends GridCollection<T>> implements KeyboardDelegate {\n  collection: C;\n  protected disabledKeys: Set<Key>;\n  protected disabledBehavior: DisabledBehavior;\n  protected direction: Direction;\n  protected collator: Intl.Collator | undefined;\n  protected layoutDelegate: LayoutDelegate;\n  protected focusMode: 'row' | 'cell';\n\n  constructor(options: GridKeyboardDelegateOptions<C>) {\n    this.collection = options.collection;\n    this.disabledKeys = options.disabledKeys;\n    this.disabledBehavior = options.disabledBehavior || 'all';\n    this.direction = options.direction;\n    this.collator = options.collator;\n    if (!options.layout && !options.ref) {\n      throw new Error('Either a layout or a ref must be specified.');\n    }\n    this.layoutDelegate = options.layoutDelegate || (options.layout ? new DeprecatedLayoutDelegate(options.layout) : new DOMLayoutDelegate(options.ref!));\n    this.focusMode = options.focusMode ?? 'row';\n  }\n\n  protected isCell(node: Node<T>): boolean {\n    return node.type === 'cell';\n  }\n\n  protected isRow(node: Node<T>): boolean {\n    return node.type === 'row' || node.type === 'item';\n  }\n\n  private isDisabled(item: Node<unknown>) {\n    return this.disabledBehavior === 'all' && (item.props?.isDisabled || this.disabledKeys.has(item.key));\n  }\n\n  protected findPreviousKey(fromKey?: Key, pred?: (item: Node<T>) => boolean): Key | null {\n    let key = fromKey != null\n      ? this.collection.getKeyBefore(fromKey)\n      : this.collection.getLastKey();\n\n    while (key != null) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      if (!this.isDisabled(item) && (!pred || pred(item))) {\n        return key;\n      }\n\n      key = this.collection.getKeyBefore(key);\n    }\n    return null;\n  }\n\n  protected findNextKey(fromKey?: Key, pred?: (item: Node<T>) => boolean): Key | null {\n    let key = fromKey != null\n      ? this.collection.getKeyAfter(fromKey)\n      : this.collection.getFirstKey();\n\n    while (key != null) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      if (!this.isDisabled(item) && (!pred || pred(item))) {\n        return key;\n      }\n\n      key = this.collection.getKeyAfter(key);\n      if (key == null) {\n        return null;\n      }\n    }\n    return null;\n  }\n\n  protected getKeyForItemInRowByIndex(key: Key, index: number = 0): Key | null {\n    if (index < 0) {\n      return null;\n    }\n\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    let i = 0;\n    for (let child of getChildNodes(item, this.collection) as Iterable<GridNode<T>>) {\n      if (child.colSpan && child.colSpan + i > index) {\n        return child.key ?? null;\n      }\n\n      if (child.colSpan) {\n        i = i + child.colSpan - 1;\n      }\n\n      if (i === index) {\n        return child.key ?? null;\n      }\n\n      i++;\n    }\n    return null;\n  }\n\n  getKeyBelow(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus was on a cell, start searching from the parent row\n    if (this.isCell(startItem)) {\n      key = startItem.parentKey ?? null;\n    }\n    if (key == null) {\n      return null;\n    }\n\n    // Find the next item\n    key = this.findNextKey(key, (item => item.type === 'item'));\n    if (key != null) {\n      // If focus was on a cell, focus the cell with the same index in the next row.\n      if (this.isCell(startItem)) {\n        let startIndex = startItem.colIndex ? startItem.colIndex : startItem.index;\n        return this.getKeyForItemInRowByIndex(key, startIndex);\n      }\n\n      // Otherwise, focus the next row\n      if (this.focusMode === 'row') {\n        return key;\n      }\n    }\n    return null;\n  }\n\n  getKeyAbove(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let startItem = this.collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n\n    // If focus is on a cell, start searching from the parent row\n    if (this.isCell(startItem)) {\n      key = startItem.parentKey ?? null;\n    }\n    if (key == null) {\n      return null;\n    }\n\n    // Find the previous item\n    key = this.findPreviousKey(key, item => item.type === 'item');\n    if (key != null) {\n      // If focus was on a cell, focus the cell with the same index in the previous row.\n      if (this.isCell(startItem)) {\n        let startIndex = startItem.colIndex ? startItem.colIndex : startItem.index;\n        return this.getKeyForItemInRowByIndex(key, startIndex);\n      }\n\n      // Otherwise, focus the previous row\n      if (this.focusMode === 'row') {\n        return key;\n      }\n    }\n    return null;\n  }\n\n  getKeyRightOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus is on a row, focus the first child cell.\n    if (this.isRow(item)) {\n      let children = getChildNodes(item, this.collection);\n      return (this.direction === 'rtl'\n        ? getLastItem(children)?.key\n        : getFirstItem(children)?.key) ?? null;\n    }\n\n    // If focus is on a cell, focus the next cell if any,\n    // otherwise focus the parent row.\n    if (this.isCell(item) && item.parentKey != null) {\n      let parent = this.collection.getItem(item.parentKey);\n      if (!parent) {\n        return null;\n      }\n      let children = getChildNodes(parent, this.collection);\n      let next = (this.direction === 'rtl'\n        ? getNthItem(children, item.index - 1)\n        : getNthItem(children, item.index + 1)) ?? null;\n\n      if (next) {\n        return next.key ?? null;\n      }\n\n      // focus row only if focusMode is set to row\n      if (this.focusMode === 'row') {\n        return item.parentKey ?? null;\n      }\n\n      return (this.direction === 'rtl' ? this.getFirstKey(key) : this.getLastKey(key)) ?? null;\n    }\n    return null;\n  }\n\n  getKeyLeftOf(key: Key): Key | null {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      return null;\n    }\n\n    // If focus is on a row, focus the last child cell.\n    if (this.isRow(item)) {\n      let children = getChildNodes(item, this.collection);\n      return (this.direction === 'rtl'\n        ? getFirstItem(children)?.key\n        : getLastItem(children)?.key) ?? null;\n    }\n\n    // If focus is on a cell, focus the previous cell if any,\n    // otherwise focus the parent row.\n    if (this.isCell(item) && item.parentKey != null) {\n      let parent = this.collection.getItem(item.parentKey);\n      if (!parent) {\n        return null;\n      }\n      let children = getChildNodes(parent, this.collection);\n      let prev = (this.direction === 'rtl'\n        ? getNthItem(children, item.index + 1)\n        : getNthItem(children, item.index - 1)) ?? null;\n\n      if (prev) {\n        return prev.key ?? null;\n      }\n\n      // focus row only if focusMode is set to row\n      if (this.focusMode === 'row') {\n        return item.parentKey ?? null;\n      }\n\n      return (this.direction === 'rtl' ? this.getLastKey(key) : this.getFirstKey(key)) ?? null;\n    }\n    return null;\n  }\n\n  getFirstKey(fromKey?: Key, global?: boolean): Key | null {\n    let key: Key | null = fromKey ?? null;\n    let item: Node<T> | undefined | null;\n    if (key != null) {\n      item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      // If global flag is not set, and a cell is currently focused,\n      // move focus to the first cell in the parent row.\n      if (this.isCell(item) && !global && item.parentKey != null) {\n        let parent = this.collection.getItem(item.parentKey);\n        if (!parent) {\n          return null;\n        }\n        return getFirstItem(getChildNodes(parent, this.collection))?.key ?? null;\n      }\n    }\n\n    // Find the first row\n    key = this.findNextKey(undefined, item => item.type === 'item');\n\n    // If global flag is set (or if focus mode is cell), focus the first cell in the first row.\n    if (key != null && ((item && this.isCell(item) && global) || this.focusMode === 'cell')) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      key = getFirstItem(getChildNodes(item, this.collection))?.key ?? null;\n    }\n\n    // Otherwise, focus the row itself.\n    return key;\n  }\n\n  getLastKey(fromKey?: Key, global?: boolean): Key | null {\n    let key: Key | null = fromKey ?? null;\n    let item: Node<T> | undefined | null;\n    if (key != null) {\n      item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      // If global flag is not set, and a cell is currently focused,\n      // move focus to the last cell in the parent row.\n      if (this.isCell(item) && !global && item.parentKey != null) {\n        let parent = this.collection.getItem(item.parentKey);\n        if (!parent) {\n          return null;\n        }\n        let children = getChildNodes(parent, this.collection);\n        return getLastItem(children)?.key ?? null;\n      }\n    }\n\n    // Find the last row\n    key = this.findPreviousKey(undefined, item => item.type === 'item');\n\n    // If global flag is set (or if focus mode is cell), focus the last cell in the last row.\n    if (key != null && ((item && this.isCell(item) && global) || this.focusMode === 'cell')) {\n      let item = this.collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      let children = getChildNodes(item, this.collection);\n      key = getLastItem(children)?.key ?? null;\n    }\n\n    // Otherwise, focus the row itself.\n    return key;\n  }\n\n  getKeyPageAbove(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n    if (!itemRect) {\n      return null;\n    }\n\n    let pageY = Math.max(0, itemRect.y + itemRect.height - this.layoutDelegate.getVisibleRect().height);\n\n    while (itemRect && itemRect.y > pageY && key != null) {\n      key = this.getKeyAbove(key) ?? null;\n      if (key == null) {\n        break;\n      }\n      itemRect = this.layoutDelegate.getItemRect(key);\n    }\n\n    return key;\n  }\n\n  getKeyPageBelow(fromKey: Key): Key | null {\n    let key: Key | null = fromKey;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n\n    if (!itemRect) {\n      return null;\n    }\n\n    let pageHeight = this.layoutDelegate.getVisibleRect().height;\n    let pageY = Math.min(this.layoutDelegate.getContentSize().height, itemRect.y + pageHeight);\n\n    while (itemRect && (itemRect.y + itemRect.height) < pageY) {\n      let nextKey = this.getKeyBelow(key);\n      // If nextKey is undefined, we've reached the last row already\n      if (nextKey == null) {\n        break;\n      }\n\n      itemRect = this.layoutDelegate.getItemRect(nextKey);\n      key = nextKey;\n    }\n\n    return key;\n  }\n\n  getKeyForSearch(search: string, fromKey?: Key): Key | null {\n    let key: Key | null = fromKey ?? null;\n    if (!this.collator) {\n      return null;\n    }\n\n    let collection = this.collection;\n    key = fromKey ?? this.getFirstKey();\n    if (key == null) {\n      return null;\n    }\n\n    // If the starting key is a cell, search from its parent row.\n    let startItem = collection.getItem(key);\n    if (!startItem) {\n      return null;\n    }\n    if (startItem.type === 'cell') {\n      key = startItem.parentKey ?? null;\n    }\n\n    let hasWrapped = false;\n    while (key != null) {\n      let item = collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n\n      // check row text value for match\n      if (item.textValue) {\n        let substring = item.textValue.slice(0, search.length);\n        if (this.collator.compare(substring, search) === 0) {\n          if (this.isRow(item) && this.focusMode === 'cell') {\n            return getFirstItem(getChildNodes(item, this.collection))?.key ?? null;\n          }\n\n          return item.key;\n        }\n      }\n\n      key = this.findNextKey(key, item => item.type === 'item');\n\n      // Wrap around when reaching the end of the collection\n      if (key == null && !hasWrapped) {\n        key = this.getFirstKey();\n        hasWrapped = true;\n      }\n    }\n\n    return null;\n  }\n}\n\n/* Backward compatibility for old Virtualizer Layout interface. */\ninterface DeprecatedLayout {\n  getLayoutInfo(key: Key): DeprecatedLayoutInfo,\n  getContentSize(): Size,\n  virtualizer: DeprecatedVirtualizer\n}\n\ninterface DeprecatedLayoutInfo {\n  rect: Rect\n}\n\ninterface DeprecatedVirtualizer {\n  visibleRect: Rect\n}\n\nclass DeprecatedLayoutDelegate implements LayoutDelegate {\n  layout: DeprecatedLayout;\n\n  constructor(layout: DeprecatedLayout) {\n    this.layout = layout;\n  }\n\n  getContentSize(): Size {\n    return this.layout.getContentSize();\n  }\n\n  getItemRect(key: Key): Rect | null {\n    return this.layout.getLayoutInfo(key)?.rect || null;\n  }\n\n  getVisibleRect(): Rect {\n    return this.layout.virtualizer.visibleRect;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,MAAM;IAsBD,OAAO,IAAa,EAAW;QACvC,OAAO,KAAK,IAAI,KAAK;IACvB;IAEU,MAAM,IAAa,EAAW;QACtC,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK;IAC9C;IAEQ,WAAW,IAAmB,EAAE;YACK;QAA3C,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAU,CAAA,CAAA,CAAA,cAAA,KAAK,KAAK,MAAA,QAAV,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAY,UAAU,KAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,CAAA;IACrG;IAEU,gBAAgB,OAAa,EAAE,IAAiC,EAAc;QACtF,IAAI,MAAM,WAAW,OACjB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,WAC7B,IAAI,CAAC,UAAU,CAAC,UAAU;QAE9B,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;YAET,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAU,CAAA,CAAC,QAAQ,KAAK,KAAI,GAC/C,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACrC;QACA,OAAO;IACT;IAEU,YAAY,OAAa,EAAE,IAAiC,EAAc;QAClF,IAAI,MAAM,WAAW,OACjB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAC5B,IAAI,CAAC,UAAU,CAAC,WAAW;QAE/B,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;YAET,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAU,CAAA,CAAC,QAAQ,KAAK,KAAI,GAC/C,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;YAClC,IAAI,OAAO,MACT,OAAO;QAEX;QACA,OAAO;IACT;IAEU,0BAA0B,GAAQ,EAAE,QAAgB,CAAC,EAAc;QAC3E,IAAI,QAAQ,GACV,OAAO;QAGT,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,IAAI,IAAI;QACR,KAAK,IAAI,SAAS,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAA4B;gBAEtE;YADT,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,GAAG,IAAI,OACvC,OAAO,CAAA,aAAA,MAAM,GAAG,MAAA,QAAT,eAAA,KAAA,IAAA,aAAa;YAGtB,IAAI,MAAM,OAAO,EACf,IAAI,IAAI,MAAM,OAAO,GAAG;gBAIjB;YADT,IAAI,MAAM,OACR,OAAO,CAAA,cAAA,MAAM,GAAG,MAAA,QAAT,gBAAA,KAAA,IAAA,cAAa;YAGtB;QACF;QACA,OAAO;IACT;IAEA,YAAY,OAAY,EAAc;QACpC,IAAI,MAAkB;QACtB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;YAKD;QAFR,8DAA8D;QAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,YACd,MAAM,CAAA,uBAAA,UAAU,SAAS,MAAA,QAAnB,yBAAA,KAAA,IAAA,uBAAuB;QAE/B,IAAI,OAAO,MACT,OAAO;QAGT,qBAAqB;QACrB,MAAM,IAAI,CAAC,WAAW,CAAC,KAAM,CAAA,OAAQ,KAAK,IAAI,KAAK;QACnD,IAAI,OAAO,MAAM;YACf,8EAA8E;YAC9E,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBAC1B,IAAI,aAAa,UAAU,QAAQ,GAAG,UAAU,QAAQ,GAAG,UAAU,KAAK;gBAC1E,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK;YAC7C;YAEA,gCAAgC;YAChC,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO;QAEX;QACA,OAAO;IACT;IAEA,YAAY,OAAY,EAAc;QACpC,IAAI,MAAkB;QACtB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxC,IAAI,CAAC,WACH,OAAO;YAKD;QAFR,6DAA6D;QAC7D,IAAI,IAAI,CAAC,MAAM,CAAC,YACd,MAAM,CAAA,uBAAA,UAAU,SAAS,MAAA,QAAnB,yBAAA,KAAA,IAAA,uBAAuB;QAE/B,IAAI,OAAO,MACT,OAAO;QAGT,yBAAyB;QACzB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAA,OAAQ,KAAK,IAAI,KAAK;QACtD,IAAI,OAAO,MAAM;YACf,kFAAkF;YAClF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;gBAC1B,IAAI,aAAa,UAAU,QAAQ,GAAG,UAAU,QAAQ,GAAG,UAAU,KAAK;gBAC1E,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK;YAC7C;YAEA,oCAAoC;YACpC,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO;QAEX;QACA,OAAO;IACT;IAEA,cAAc,GAAQ,EAAc;QAClC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,oDAAoD;QACpD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAGhB,cACA;YAHJ,IAAI,WAAW,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU;gBAC1C;YAAR,OAAO,CAAC,OAAA,IAAI,CAAC,SAAS,KAAK,QAAA,CACvB,eAAA,CAAA,+KAAA,cAAU,EAAE,SAAA,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAuB,GAAG,GAAA,CAC1B,gBAAA,CAAA,+KAAA,eAAW,EAAE,SAAA,MAAA,QAAb,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAwB,GAAG,MAAA,QAFvB,SAAA,KAAA,IAAA,OAE4B;QACtC;QAEA,qDAAqD;QACrD,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM;YAC/C,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;YACnD,IAAI,CAAC,QACH,OAAO;YAET,IAAI,WAAW,CAAA,+KAAA,gBAAY,EAAE,QAAQ,IAAI,CAAC,UAAU;gBACxC;YAAZ,IAAI,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAC3B,CAAA,+KAAA,aAAS,EAAE,UAAU,KAAK,KAAK,GAAG,KAClC,CAAA,+KAAA,aAAS,EAAE,UAAU,KAAK,KAAK,GAAG,EAAA,MAAA,QAF1B,UAAA,KAAA,IAAA,QAEiC;gBAGpC;YADT,IAAI,MACF,OAAO,CAAA,YAAA,KAAK,GAAG,MAAA,QAAR,cAAA,KAAA,IAAA,YAAY;gBAKZ;YAFT,4CAA4C;YAC5C,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO,CAAA,kBAAA,KAAK,SAAS,MAAA,QAAd,oBAAA,KAAA,IAAA,kBAAkB;gBAGnB;YAAR,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAA,MAAA,QAAnE,UAAA,KAAA,IAAA,QAA4E;QACtF;QACA,OAAO;IACT;IAEA,aAAa,GAAQ,EAAc;QACjC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MACH,OAAO;QAGT,mDAAmD;QACnD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAGhB,eACA;YAHJ,IAAI,WAAW,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU;gBAC1C;YAAR,OAAO,CAAC,OAAA,IAAI,CAAC,SAAS,KAAK,QAAA,CACvB,gBAAA,CAAA,+KAAA,eAAW,EAAE,SAAA,MAAA,QAAb,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAwB,GAAG,GAAA,CAC3B,eAAA,CAAA,+KAAA,cAAU,EAAE,SAAA,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAuB,GAAG,MAAA,QAFtB,SAAA,KAAA,IAAA,OAE2B;QACrC;QAEA,yDAAyD;QACzD,kCAAkC;QAClC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM;YAC/C,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;YACnD,IAAI,CAAC,QACH,OAAO;YAET,IAAI,WAAW,CAAA,+KAAA,gBAAY,EAAE,QAAQ,IAAI,CAAC,UAAU;gBACxC;YAAZ,IAAI,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAC3B,CAAA,+KAAA,aAAS,EAAE,UAAU,KAAK,KAAK,GAAG,KAClC,CAAA,+KAAA,aAAS,EAAE,UAAU,KAAK,KAAK,GAAG,EAAA,MAAA,QAF1B,UAAA,KAAA,IAAA,QAEiC;gBAGpC;YADT,IAAI,MACF,OAAO,CAAA,YAAA,KAAK,GAAG,MAAA,QAAR,cAAA,KAAA,IAAA,YAAY;gBAKZ;YAFT,4CAA4C;YAC5C,IAAI,IAAI,CAAC,SAAS,KAAK,OACrB,OAAO,CAAA,kBAAA,KAAK,SAAS,MAAA,QAAd,oBAAA,KAAA,IAAA,kBAAkB;gBAGnB;YAAR,OAAO,CAAC,QAAA,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAA,MAAA,QAAnE,UAAA,KAAA,IAAA,QAA4E;QACtF;QACA,OAAO;IACT;IAEA,YAAY,OAAa,EAAE,MAAgB,EAAc;QACvD,IAAI,MAAkB,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW;QACjC,IAAI;QACJ,IAAI,OAAO,MAAM;YACf,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,MACH,OAAO;YAGT,8DAA8D;YAC9D,kDAAkD;YAClD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM;oBAKnD;gBAJP,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;gBACnD,IAAI,CAAC,QACH,OAAO;oBAEF;gBAAP,OAAO,CAAA,oBAAA,CAAA,gBAAA,CAAA,+KAAA,eAAW,EAAE,CAAA,+KAAA,gBAAY,EAAE,QAAQ,IAAI,CAAC,UAAU,EAAA,MAAA,QAAlD,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAsD,GAAG,MAAA,QAAzD,sBAAA,KAAA,IAAA,oBAA6D;YACtE;QACF;QAEA,qBAAqB;QACrB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAA,OAAQ,KAAK,IAAI,KAAK;QAExD,2FAA2F;QAC3F,IAAI,OAAO,QAAS,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,UAAW,IAAI,CAAC,SAAS,KAAK,MAAK,GAAI;gBAKjF;YAJN,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;gBAEH;YAAN,MAAM,CAAA,qBAAA,CAAA,iBAAA,CAAA,+KAAA,eAAW,EAAE,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAAA,MAAA,QAAhD,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAoD,GAAG,MAAA,QAAvD,uBAAA,KAAA,IAAA,qBAA2D;QACnE;QAEA,mCAAmC;QACnC,OAAO;IACT;IAEA,WAAW,OAAa,EAAE,MAAgB,EAAc;QACtD,IAAI,MAAkB,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW;QACjC,IAAI;QACJ,IAAI,OAAO,MAAM;YACf,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,MACH,OAAO;YAGT,8DAA8D;YAC9D,iDAAiD;YACjD,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM;oBAMnD;gBALP,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;gBACnD,IAAI,CAAC,QACH,OAAO;gBAET,IAAI,WAAW,CAAA,+KAAA,gBAAY,EAAE,QAAQ,IAAI,CAAC,UAAU;oBAC7C;gBAAP,OAAO,CAAA,mBAAA,CAAA,eAAA,CAAA,+KAAA,cAAU,EAAE,SAAA,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAuB,GAAG,MAAA,QAA1B,qBAAA,KAAA,IAAA,mBAA8B;YACvC;QACF;QAEA,oBAAoB;QACpB,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,CAAA,OAAQ,KAAK,IAAI,KAAK;QAE5D,yFAAyF;QACzF,IAAI,OAAO,QAAS,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,UAAW,IAAI,CAAC,SAAS,KAAK,MAAK,GAAI;gBAMjF;YALN,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,MACH,OAAO;YAET,IAAI,WAAW,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU;gBAC5C;YAAN,MAAM,CAAA,oBAAA,CAAA,gBAAA,CAAA,+KAAA,cAAU,EAAE,SAAA,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAuB,GAAG,MAAA,QAA1B,sBAAA,KAAA,IAAA,oBAA8B;QACtC;QAEA,mCAAmC;QACnC,OAAO;IACT;IAEA,gBAAgB,OAAY,EAAc;QACxC,IAAI,MAAkB;QACtB,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;QAElG,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,OAAO,KAAM;gBAC9C;YAAN,MAAM,CAAA,oBAAA,IAAI,CAAC,WAAW,CAAC,IAAA,MAAA,QAAjB,sBAAA,KAAA,IAAA,oBAAyB;YAC/B,IAAI,OAAO,MACT;YAEF,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC7C;QAEA,OAAO;IACT;IAEA,gBAAgB,OAAY,EAAc;QACxC,IAAI,MAAkB;QACtB,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAE/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;QAC5D,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,EAAE,SAAS,CAAC,GAAG;QAE/E,MAAO,YAAa,SAAS,CAAC,GAAG,SAAS,MAAM,GAAI,MAAO;YACzD,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;YAC/B,8DAA8D;YAC9D,IAAI,WAAW,MACb;YAGF,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YAC3C,MAAM;QACR;QAEA,OAAO;IACT;IAEA,gBAAgB,MAAc,EAAE,OAAa,EAAc;QACzD,IAAI,MAAkB,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW;QACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAChB,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,MAAM,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW,IAAI,CAAC,WAAW;QACjC,IAAI,OAAO,MACT,OAAO;QAGT,6DAA6D;QAC7D,IAAI,YAAY,WAAW,OAAO,CAAC;QACnC,IAAI,CAAC,WACH,OAAO;YAGD;QADR,IAAI,UAAU,IAAI,KAAK,QACrB,MAAM,CAAA,uBAAA,UAAU,SAAS,MAAA,QAAnB,yBAAA,KAAA,IAAA,uBAAuB;QAG/B,IAAI,aAAa;QACjB,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,WAAW,OAAO,CAAC;YAC9B,IAAI,CAAC,MACH,OAAO;YAGT,iCAAiC;YACjC,IAAI,KAAK,SAAS,EAAE;gBAClB,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;gBACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GAAG;wBAEzC;wBAAA;oBADT,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,SAAS,KAAK,QACzC,OAAO,CAAA,oBAAA,CAAA,gBAAA,CAAA,+KAAA,eAAW,EAAE,CAAA,+KAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAAA,MAAA,QAAhD,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAoD,GAAG,MAAA,QAAvD,sBAAA,KAAA,IAAA,oBAA2D;oBAGpE,OAAO,KAAK,GAAG;gBACjB;YACF;YAEA,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAA,OAAQ,KAAK,IAAI,KAAK;YAElD,sDAAsD;YACtD,IAAI,OAAO,QAAQ,CAAC,YAAY;gBAC9B,MAAM,IAAI,CAAC,WAAW;gBACtB,aAAa;YACf;QACF;QAEA,OAAO;IACT;IAxZA,YAAY,OAAuC,CAAE;QACnD,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;QACpC,IAAI,CAAC,YAAY,GAAG,QAAQ,YAAY;QACxC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,gBAAgB,IAAI;QACpD,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS;QAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ;QAChC,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,GAAG,EACjC,MAAM,IAAI,MAAM;QAElB,IAAI,CAAC,cAAc,GAAG,QAAQ,cAAc,IAAK,CAAA,QAAQ,MAAM,GAAG,IAAI,+CAAyB,QAAQ,MAAM,IAAI,IAAI,CAAA,8KAAA,oBAAgB,EAAE,QAAQ,GAAG,CAAC;YAClI;QAAjB,IAAI,CAAC,SAAS,GAAG,CAAA,qBAAA,QAAQ,SAAS,MAAA,QAAjB,uBAAA,KAAA,IAAA,qBAAqB;IACxC;AA8YF;AAiBA,MAAM;IAOJ,iBAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc;IACnC;IAEA,YAAY,GAAQ,EAAe;YAC1B;QAAP,OAAO,CAAA,CAAA,6BAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAA,MAAA,QAA1B,+BAAA,KAAA,IAAA,KAAA,IAAA,2BAAgC,IAAI,KAAI;IACjD;IAEA,iBAAuB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW;IAC5C;IAdA,YAAY,MAAwB,CAAE;QACpC,IAAI,CAAC,MAAM,GAAG;IAChB;AAaF", "debugId": null}}, {"offset": {"line": 3801, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useGridSelectionAnnouncement.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useGridSelectionAnnouncement.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {announce} from '@react-aria/live-announcer';\nimport {Collection, Key, Node, Selection} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {SelectionManager} from '@react-stately/selection';\nimport {useEffectEvent, useUpdateEffect} from '@react-aria/utils';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useRef} from 'react';\n\nexport interface GridSelectionAnnouncementProps {\n  /**\n   * A function that returns the text that should be announced by assistive technology when a row is added or removed from selection.\n   * @default (key) => state.collection.getItem(key)?.textValue\n   */\n  getRowText?: (key: Key) => string\n}\n\ninterface GridSelectionState<T> {\n  /** A collection of items in the grid. */\n  collection: Collection<Node<T>>,\n  /** A set of items that are disabled. */\n  disabledKeys: Set<Key>,\n  /** A selection manager to read and update multiple selection state. */\n  selectionManager: SelectionManager\n}\n\nexport function useGridSelectionAnnouncement<T>(props: GridSelectionAnnouncementProps, state: GridSelectionState<T>): void {\n  let {\n    getRowText = (key) => state.collection.getTextValue?.(key) ?? state.collection.getItem(key)?.textValue\n  } = props;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/grid');\n\n  // Many screen readers do not announce when items in a grid are selected/deselected.\n  // We do this using an ARIA live region.\n  let selection = state.selectionManager.rawSelection;\n  let lastSelection = useRef(selection);\n  let announceSelectionChange = useEffectEvent(() => {\n    if (!state.selectionManager.isFocused || selection === lastSelection.current) {\n      lastSelection.current = selection;\n\n      return;\n    }\n\n    let addedKeys = diffSelection(selection, lastSelection.current);\n    let removedKeys = diffSelection(lastSelection.current, selection);\n\n    // If adding or removing a single row from the selection, announce the name of that item.\n    let isReplace = state.selectionManager.selectionBehavior === 'replace';\n    let messages: string[] = [];\n\n    if ((state.selectionManager.selectedKeys.size === 1 && isReplace)) {\n      let firstKey = state.selectionManager.selectedKeys.keys().next().value;\n      if (firstKey != null && state.collection.getItem(firstKey)) {\n        let currentSelectionText = getRowText(firstKey);\n        if (currentSelectionText) {\n          messages.push(stringFormatter.format('selectedItem', {item: currentSelectionText}));\n        }\n      }\n    } else if (addedKeys.size === 1 && removedKeys.size === 0) {\n      let firstKey = addedKeys.keys().next().value;\n      if (firstKey != null) {\n        let addedText = getRowText(firstKey);\n        if (addedText) {\n          messages.push(stringFormatter.format('selectedItem', {item: addedText}));\n        }\n      }\n    } else if (removedKeys.size === 1 && addedKeys.size === 0) {\n      let firstKey = removedKeys.keys().next().value;\n      if (firstKey != null && state.collection.getItem(firstKey)) {\n        let removedText = getRowText(firstKey);\n        if (removedText) {\n          messages.push(stringFormatter.format('deselectedItem', {item: removedText}));\n        }\n      }\n    }\n\n    // Announce how many items are selected, except when selecting the first item.\n    if (state.selectionManager.selectionMode === 'multiple') {\n      if (messages.length === 0 || selection === 'all' || selection.size > 1 || lastSelection.current === 'all' || lastSelection.current?.size > 1) {\n        messages.push(selection === 'all'\n          ? stringFormatter.format('selectedAll')\n          : stringFormatter.format('selectedCount', {count: selection.size})\n        );\n      }\n    }\n\n    if (messages.length > 0) {\n      announce(messages.join(' '));\n    }\n\n    lastSelection.current = selection;\n  });\n\n  useUpdateEffect(() => {\n    if (state.selectionManager.isFocused) {\n      announceSelectionChange();\n    } else {\n      // Wait a frame in case the collection is about to become focused (e.g. on mouse down).\n      let raf = requestAnimationFrame(announceSelectionChange);\n      return () => cancelAnimationFrame(raf);\n    }\n  }, [selection, state.selectionManager.isFocused]);\n}\n\nfunction diffSelection(a: Selection, b: Selection): Set<Key> {\n  let res = new Set<Key>();\n  if (a === 'all' || b === 'all') {\n    return res;\n  }\n\n  for (let key of a.keys()) {\n    if (!b.has(key)) {\n      res.add(key);\n    }\n  }\n\n  return res;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GA4BM,SAAS,0CAAgC,KAAqC,EAAE,KAA4B;IACjH,IAAI,EAAA,YACF,aAAa,CAAC;YAAQ,gCAAA,mBAAwC;YAAxC;eAAA,CAAA,kCAAA,CAAA,iCAAA,CAAA,oBAAA,MAAM,UAAU,EAAC,YAAY,MAAA,QAA7B,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAA,IAAA,CAAA,mBAAgC,IAAA,MAAA,QAAhC,oCAAA,KAAA,IAAA,kCAAA,CAAwC,4BAAA,MAAM,UAAU,CAAC,OAAO,CAAC,IAAA,MAAA,QAAzB,8BAAA,KAAA,IAAA,KAAA,IAAA,0BAA+B,SAAS;OACvG,GAAG;IACJ,IAAI,kBAAkB,CAAA,mLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,+JAAA,CAAA,UAAA,CAAW,GAAG;IAEhE,oFAAoF;IACpF,wCAAwC;IACxC,IAAI,YAAY,MAAM,gBAAgB,CAAC,YAAY;IACnD,IAAI,gBAAgB,CAAA,yMAAA,SAAK,EAAE;IAC3B,IAAI,0BAA0B,CAAA,uKAAA,iBAAa,EAAE;YA0CoE;QAzC/G,IAAI,CAAC,MAAM,gBAAgB,CAAC,SAAS,IAAI,cAAc,cAAc,OAAO,EAAE;YAC5E,cAAc,OAAO,GAAG;YAExB;QACF;QAEA,IAAI,YAAY,oCAAc,WAAW,cAAc,OAAO;QAC9D,IAAI,cAAc,oCAAc,cAAc,OAAO,EAAE;QAEvD,yFAAyF;QACzF,IAAI,YAAY,MAAM,gBAAgB,CAAC,iBAAiB,KAAK;QAC7D,IAAI,WAAqB,EAAE;QAE3B,IAAK,MAAM,gBAAgB,CAAC,YAAY,CAAC,IAAI,KAAK,KAAK,WAAY;YACjE,IAAI,WAAW,MAAM,gBAAgB,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK;YACtE,IAAI,YAAY,QAAQ,MAAM,UAAU,CAAC,OAAO,CAAC,WAAW;gBAC1D,IAAI,uBAAuB,WAAW;gBACtC,IAAI,sBACF,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,gBAAgB;oBAAC,MAAM;gBAAoB;YAEpF;QACF,OAAO,IAAI,UAAU,IAAI,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG;YACzD,IAAI,WAAW,UAAU,IAAI,GAAG,IAAI,GAAG,KAAK;YAC5C,IAAI,YAAY,MAAM;gBACpB,IAAI,YAAY,WAAW;gBAC3B,IAAI,WACF,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,gBAAgB;oBAAC,MAAM;gBAAS;YAEzE;QACF,OAAO,IAAI,YAAY,IAAI,KAAK,KAAK,UAAU,IAAI,KAAK,GAAG;YACzD,IAAI,WAAW,YAAY,IAAI,GAAG,IAAI,GAAG,KAAK;YAC9C,IAAI,YAAY,QAAQ,MAAM,UAAU,CAAC,OAAO,CAAC,WAAW;gBAC1D,IAAI,cAAc,WAAW;gBAC7B,IAAI,aACF,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,kBAAkB;oBAAC,MAAM;gBAAW;YAE7E;QACF;QAEA,8EAA8E;QAC9E,IAAI,MAAM,gBAAgB,CAAC,aAAa,KAAK,YAC3C;YAAA,IAAI,SAAS,MAAM,KAAK,KAAK,cAAc,SAAS,UAAU,IAAI,GAAG,KAAK,cAAc,OAAO,KAAK,SAAS,CAAA,CAAA,yBAAA,cAAc,OAAO,MAAA,QAArB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAuB,IAAI,IAAG,GACzI,SAAS,IAAI,CAAC,cAAc,QACxB,gBAAgB,MAAM,CAAC,iBACvB,gBAAgB,MAAM,CAAC,iBAAiB;gBAAC,OAAO,UAAU,IAAI;YAAA;QAEpE;QAGF,IAAI,SAAS,MAAM,GAAG,GACpB,CAAA,kLAAA,WAAO,EAAE,SAAS,IAAI,CAAC;QAGzB,cAAc,OAAO,GAAG;IAC1B;IAEA,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,MAAM,gBAAgB,CAAC,SAAS,EAClC;aACK;YACL,uFAAuF;YACvF,IAAI,MAAM,sBAAsB;YAChC,OAAO,IAAM,qBAAqB;QACpC;IACF,GAAG;QAAC;QAAW,MAAM,gBAAgB,CAAC,SAAS;KAAC;AAClD;AAEA,SAAS,oCAAc,CAAY,EAAE,CAAY;IAC/C,IAAI,MAAM,IAAI;IACd,IAAI,MAAM,SAAS,MAAM,OACvB,OAAO;IAGT,KAAK,IAAI,OAAO,EAAE,IAAI,GACpB,IAAI,CAAC,EAAE,GAAG,CAAC,MACT,IAAI,GAAG,CAAC;IAIZ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3908, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useHighlightSelectionDescription.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useHighlightSelectionDescription.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {MultipleSelectionManager} from '@react-stately/selection';\nimport {useDescription} from '@react-aria/utils';\nimport {useInteractionModality} from '@react-aria/interactions';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\nexport interface HighlightSelectionDescriptionProps {\n  selectionManager: MultipleSelectionManager,\n  hasItemActions?: boolean\n}\n\n/**\n * Computes the description for a grid selectable collection.\n * @param props\n */\nexport function useHighlightSelectionDescription(props: HighlightSelectionDescriptionProps): AriaLabelingProps {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/grid');\n  let modality = useInteractionModality();\n  // null is the default if the user hasn't interacted with the table at all yet or the rest of the page\n  let shouldLongPress = (modality === 'pointer' || modality === 'virtual' || modality == null)\n    && typeof window !== 'undefined' && 'ontouchstart' in window;\n\n  let interactionDescription = useMemo(() => {\n    let selectionMode = props.selectionManager.selectionMode;\n    let selectionBehavior = props.selectionManager.selectionBehavior;\n\n    let message: string | undefined;\n    if (shouldLongPress) {\n      message = stringFormatter.format('longPressToSelect');\n    }\n\n    return selectionBehavior === 'replace' && selectionMode !== 'none' && props.hasItemActions ? message : undefined;\n  }, [props.selectionManager.selectionMode, props.selectionManager.selectionBehavior, props.hasItemActions, stringFormatter, shouldLongPress]);\n\n  let descriptionProps = useDescription(interactionDescription);\n  return descriptionProps;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,SAAS,0CAAiC,KAAyC;IACxF,IAAI,kBAAkB,CAAA,mLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,+JAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI,WAAW,CAAA,+KAAA,yBAAqB;IACpC,sGAAsG;IACtG,IAAI,kBAAmB,CAAA,aAAa,aAAa,aAAa,aAAa,YAAY,IAAG,KACrF,OAAO,SAAW,eAAe,kBAAkB;IAExD,IAAI,yBAAyB,CAAA,yMAAA,UAAM,EAAE;QACnC,IAAI,gBAAgB,MAAM,gBAAgB,CAAC,aAAa;QACxD,IAAI,oBAAoB,MAAM,gBAAgB,CAAC,iBAAiB;QAEhE,IAAI;QACJ,IAAI,iBACF,UAAU,gBAAgB,MAAM,CAAC;;QAGnC,OAAO,sBAAsB,aAAa,kBAAkB,UAAU,MAAM,cAAc,GAAG,UAAU;IACzG,GAAG;QAAC,MAAM,gBAAgB,CAAC,aAAa;QAAE,MAAM,gBAAgB,CAAC,iBAAiB;QAAE,MAAM,cAAc;QAAE;QAAiB;KAAgB;IAE3I,IAAI,mBAAmB,CAAA,uKAAA,iBAAa,EAAE;IACtC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3962, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useGrid.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useGrid.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMAttributes, DOMProps, Key, KeyboardDelegate, RefObject} from '@react-types/shared';\nimport {filterDOMProps, mergeProps, useId} from '@react-aria/utils';\nimport {GridCollection} from '@react-types/grid';\nimport {GridKeyboardDelegate} from './GridKeyboardDelegate';\nimport {gridMap} from './utils';\nimport {GridState} from '@react-stately/grid';\nimport {useCallback, useMemo} from 'react';\nimport {useCollator, useLocale} from '@react-aria/i18n';\nimport {useGridSelectionAnnouncement} from './useGridSelectionAnnouncement';\nimport {useHasTabbableChild} from '@react-aria/focus';\nimport {useHighlightSelectionDescription} from './useHighlightSelectionDescription';\nimport {useSelectableCollection} from '@react-aria/selection';\n\nexport interface GridProps extends DOMProps, AriaLabelingProps {\n  /** Whether the grid uses virtual scrolling. */\n  isVirtualized?: boolean,\n  /**\n   * Whether typeahead navigation is disabled.\n   * @default false\n   */\n  disallowTypeAhead?: boolean,\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n  /**\n   * Whether initial grid focus should be placed on the grid row or grid cell.\n   * @default 'row'\n   */\n  focusMode?: 'row' | 'cell',\n  /**\n   * A function that returns the text that should be announced by assistive technology when a row is added or removed from selection.\n   * @default (key) => state.collection.getItem(key)?.textValue\n   */\n  getRowText?: (key: Key) => string,\n  /**\n   * The ref attached to the scrollable body. Used to provided automatic scrolling on item focus for non-virtualized grids.\n   */\n  scrollRef?: RefObject<HTMLElement | null>,\n  /** Handler that is called when a user performs an action on the row. */\n  onRowAction?: (key: Key) => void,\n  /** Handler that is called when a user performs an action on the cell. */\n  onCellAction?: (key: Key) => void,\n  /**\n   * Whether pressing the escape key should clear selection in the grid or not.\n   *\n   * Most experiences should not modify this option as it eliminates a keyboard user's ability to\n   * easily clear selection. Only use if the escape key is being handled externally or should not\n   * trigger selection clearing contextually.\n   * @default 'clearSelection'\n   */\n  escapeKeyBehavior?: 'clearSelection' | 'none',\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean\n}\n\nexport interface GridAria {\n  /** Props for the grid element. */\n  gridProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a grid component.\n * A grid displays data in one or more rows and columns and enables a user to navigate its contents via directional navigation keys.\n * @param props - Props for the grid.\n * @param state - State for the grid, as returned by `useGridState`.\n * @param ref - The ref attached to the grid element.\n */\nexport function useGrid<T>(props: GridProps, state: GridState<T, GridCollection<T>>, ref: RefObject<HTMLElement | null>): GridAria {\n  let {\n    isVirtualized,\n    disallowTypeAhead,\n    keyboardDelegate,\n    focusMode,\n    scrollRef,\n    getRowText,\n    onRowAction,\n    onCellAction,\n    escapeKeyBehavior = 'clearSelection',\n    shouldSelectOnPressUp\n  } = props;\n  let {selectionManager: manager} = state;\n\n  if (!props['aria-label'] && !props['aria-labelledby']) {\n    console.warn('An aria-label or aria-labelledby prop is required for accessibility.');\n  }\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let {direction} = useLocale();\n  let disabledBehavior = state.selectionManager.disabledBehavior;\n  let delegate = useMemo(() => keyboardDelegate || new GridKeyboardDelegate({\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    disabledBehavior,\n    ref,\n    direction,\n    collator,\n    focusMode\n  }), [keyboardDelegate, state.collection, state.disabledKeys, disabledBehavior, ref, direction, collator, focusMode]);\n\n  let {collectionProps} = useSelectableCollection({\n    ref,\n    selectionManager: manager,\n    keyboardDelegate: delegate,\n    isVirtualized,\n    scrollRef,\n    disallowTypeAhead,\n    escapeKeyBehavior\n  });\n\n  let id = useId(props.id);\n  gridMap.set(state, {keyboardDelegate: delegate, actions: {onRowAction, onCellAction}, shouldSelectOnPressUp});\n\n  let descriptionProps = useHighlightSelectionDescription({\n    selectionManager: manager,\n    hasItemActions: !!(onRowAction || onCellAction)\n  });\n\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  let onFocus = useCallback((e) => {\n    if (manager.isFocused) {\n      // If a focus event bubbled through a portal, reset focus state.\n      if (!e.currentTarget.contains(e.target)) {\n        manager.setFocused(false);\n      }\n\n      return;\n    }\n\n    // Focus events can bubble through portals. Ignore these events.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    manager.setFocused(true);\n  }, [manager]);\n\n  // Continue to track collection focused state even if keyboard navigation is disabled\n  let navDisabledHandlers = useMemo(() => ({\n    onBlur: collectionProps.onBlur,\n    onFocus\n  }), [onFocus, collectionProps.onBlur]);\n\n  let hasTabbableChild = useHasTabbableChild(ref, {\n    isDisabled: state.collection.size !== 0\n  });\n\n  let gridProps: DOMAttributes = mergeProps(\n    domProps,\n    {\n      role: 'grid',\n      id,\n      'aria-multiselectable': manager.selectionMode === 'multiple' ? 'true' : undefined\n    },\n    state.isKeyboardNavigationDisabled ? navDisabledHandlers : collectionProps,\n    // If collection is empty, make sure the grid is tabbable unless there is a child tabbable element.\n    (state.collection.size === 0 && {tabIndex: hasTabbableChild ? -1 : 0}) || undefined,\n    descriptionProps\n  );\n\n  if (isVirtualized) {\n    gridProps['aria-rowcount'] = state.collection.size;\n    gridProps['aria-colcount'] = state.collection.columnCount;\n  }\n\n  useGridSelectionAnnouncement({getRowText}, state);\n  return {\n    gridProps\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAuEM,SAAS,0CAAW,KAAgB,EAAE,KAAsC,EAAE,GAAkC;IACrH,IAAI,EAAA,eACF,aAAa,EAAA,mBACb,iBAAiB,EAAA,kBACjB,gBAAgB,EAAA,WAChB,SAAS,EAAA,WACT,SAAS,EAAA,YACT,UAAU,EAAA,aACV,WAAW,EAAA,cACX,YAAY,EAAA,mBACZ,oBAAoB,gBAAA,EAAA,uBACpB,qBAAqB,EACtB,GAAG;IACJ,IAAI,EAAC,kBAAkB,OAAO,EAAC,GAAG;IAElC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,kBAAkB,EACnD,QAAQ,IAAI,CAAC;IAGf,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,mKAAA,cAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,+JAAA,YAAQ;IAC1B,IAAI,mBAAmB,MAAM,gBAAgB,CAAC,gBAAgB;IAC9D,IAAI,WAAW,CAAA,yMAAA,UAAM,EAAE,IAAM,oBAAoB,IAAI,CAAA,4KAAA,uBAAmB,EAAE;YACxE,YAAY,MAAM,UAAU;YAC5B,cAAc,MAAM,YAAY;8BAChC;iBACA;uBACA;sBACA;uBACA;QACF,IAAI;QAAC;QAAkB,MAAM,UAAU;QAAE,MAAM,YAAY;QAAE;QAAkB;QAAK;QAAW;QAAU;KAAU;IAEnH,IAAI,EAAA,iBAAC,eAAe,EAAC,GAAG,CAAA,oLAAA,0BAAsB,EAAE;aAC9C;QACA,kBAAkB;QAClB,kBAAkB;uBAClB;mBACA;2BACA;2BACA;IACF;IAEA,IAAI,KAAK,CAAA,8JAAA,QAAI,EAAE,MAAM,EAAE;IACvB,CAAA,6JAAA,UAAM,EAAE,GAAG,CAAC,OAAO;QAAC,kBAAkB;QAAU,SAAS;yBAAC;0BAAa;QAAY;+BAAG;IAAqB;IAE3G,IAAI,mBAAmB,CAAA,wLAAA,mCAA+B,EAAE;QACtD,kBAAkB;QAClB,gBAAgB,CAAC,CAAE,CAAA,eAAe,YAAW;IAC/C;IAEA,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,IAAI,UAAU,CAAA,yMAAA,cAAU,EAAE,CAAC;QACzB,IAAI,QAAQ,SAAS,EAAE;YACrB,gEAAgE;YAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC,QAAQ,UAAU,CAAC;YAGrB;QACF;QAEA,gEAAgE;QAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,QAAQ,UAAU,CAAC;IACrB,GAAG;QAAC;KAAQ;IAEZ,qFAAqF;IACrF,IAAI,sBAAsB,CAAA,yMAAA,UAAM,EAAE,IAAO,CAAA;YACvC,QAAQ,gBAAgB,MAAM;qBAC9B;QACF,CAAA,GAAI;QAAC;QAAS,gBAAgB,MAAM;KAAC;IAErC,IAAI,mBAAmB,CAAA,4KAAA,sBAAkB,EAAE,KAAK;QAC9C,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK;IACxC;IAEA,IAAI,YAA2B,CAAA,mKAAA,aAAS,EACtC,UACA;QACE,MAAM;YACN;QACA,wBAAwB,QAAQ,aAAa,KAAK,aAAa,SAAS;IAC1E,GACA,MAAM,4BAA4B,GAAG,sBAAsB,iBAE3D,AADA,AACC,MAAM,UAAU,CAAC,IAAI,KAAK,KAAK,oEADmE;QAClE,UAAU,mBAAmB,CAAA,IAAK;IAAC,KAAM,WAC1E;IAGF,IAAI,eAAe;QACjB,SAAS,CAAC,gBAAgB,GAAG,MAAM,UAAU,CAAC,IAAI;QAClD,SAAS,CAAC,gBAAgB,GAAG,MAAM,UAAU,CAAC,WAAW;IAC3D;IAEA,CAAA,oLAAA,+BAA2B,EAAE;oBAAC;IAAU,GAAG;IAC3C,OAAO;mBACL;IACF;AACF", "debugId": null}}, {"offset": {"line": 4098, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useGridRow.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useGridRow.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from '@react-aria/utils';\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {GridCollection, GridNode} from '@react-types/grid';\nimport {gridMap} from './utils';\nimport {GridState} from '@react-stately/grid';\nimport {SelectableItemStates, useSelectableItem} from '@react-aria/selection';\n\nexport interface GridRowProps<T> {\n  /** An object representing the grid row. Contains all the relevant information that makes up the grid row. */\n  node: GridNode<T>,\n  /** Whether the grid row is contained in a virtual scroller. */\n  isVirtualized?: boolean,\n  /** Whether selection should occur on press up instead of press down. */\n  shouldSelectOnPressUp?: boolean,\n  /**\n   * Handler that is called when a user performs an action on the row.\n   * Please use onCellAction at the collection level instead.\n   * @deprecated\n   **/\n  onAction?: () => void\n}\n\nexport interface GridRowAria extends SelectableItemStates {\n  /** Props for the grid row element. */\n  rowProps: DOMAttributes,\n  /** Whether the row is currently in a pressed state. */\n  isPressed: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a row in a grid.\n * @param props - Props for the row.\n * @param state - State of the parent grid, as returned by `useGridState`.\n */\nexport function useGridRow<T, C extends GridCollection<T>, S extends GridState<T, C>>(props: GridRowProps<T>, state: S, ref: RefObject<FocusableElement | null>): GridRowAria {\n  let {\n    node,\n    isVirtualized,\n    shouldSelectOnPressUp,\n    onAction\n  } = props;\n\n  let {actions, shouldSelectOnPressUp: gridShouldSelectOnPressUp} = gridMap.get(state)!;\n  let onRowAction = actions.onRowAction ? () => actions.onRowAction?.(node.key) : onAction;\n  let {itemProps, ...states} = useSelectableItem({\n    selectionManager: state.selectionManager,\n    key: node.key,\n    ref,\n    isVirtualized,\n    shouldSelectOnPressUp: gridShouldSelectOnPressUp || shouldSelectOnPressUp,\n    onAction: onRowAction || node?.props?.onAction ? chain(node?.props?.onAction, onRowAction) : undefined,\n    isDisabled: state.collection.size === 0\n  });\n\n  let isSelected = state.selectionManager.isSelected(node.key);\n\n  let rowProps: DOMAttributes = {\n    role: 'row',\n    'aria-selected': state.selectionManager.selectionMode !== 'none' ? isSelected : undefined,\n    'aria-disabled': states.isDisabled || undefined,\n    ...itemProps\n  };\n\n  if (isVirtualized) {\n    rowProps['aria-rowindex'] = node.index + 1; // aria-rowindex is 1 based\n  }\n\n  return {\n    rowProps,\n    ...states\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAoCM,SAAS,0CAAsE,KAAsB,EAAE,KAAQ,EAAE,GAAuC;QAgBlI,aAA8B;IAfzD,IAAI,EAAA,MACF,IAAI,EAAA,eACJ,aAAa,EAAA,uBACb,qBAAqB,EAAA,UACrB,QAAQ,EACT,GAAG;IAEJ,IAAI,EAAA,SAAC,OAAO,EAAE,uBAAuB,yBAAyB,EAAC,GAAG,CAAA,6JAAA,UAAM,EAAE,GAAG,CAAC;IAC9E,IAAI,cAAc,QAAQ,WAAW,GAAG;YAAM;gBAAA,uBAAA,QAAQ,WAAW,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAA,IAAA,CAAA,SAAsB,KAAK,GAAG;QAAI;IAChF,IAAI,EAAA,WAAC,SAAS,EAAE,GAAG,QAAO,GAAG,CAAA,8KAAA,oBAAgB,EAAE;QAC7C,kBAAkB,MAAM,gBAAgB;QACxC,KAAK,KAAK,GAAG;aACb;uBACA;QACA,uBAAuB,6BAA6B;QACpD,UAAU,eAAA,CAAe,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,cAAA,KAAM,KAAK,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAa,QAAQ,IAAG,CAAA,8JAAA,QAAI,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,eAAA,KAAM,KAAK,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,QAAQ,EAAE,eAAe;QAC7F,YAAY,MAAM,UAAU,CAAC,IAAI,KAAK;IACxC;IAEA,IAAI,aAAa,MAAM,gBAAgB,CAAC,UAAU,CAAC,KAAK,GAAG;IAE3D,IAAI,WAA0B;QAC5B,MAAM;QACN,iBAAiB,MAAM,gBAAgB,CAAC,aAAa,KAAK,SAAS,aAAa;QAChF,iBAAiB,OAAO,UAAU,IAAI;QACtC,GAAG,SAAS;IACd;IAEA,IAAI,eACF,QAAQ,CAAC,gBAAgB,GAAG,KAAK,KAAK,GAAG,GAAG,2BAA2B;IAGzE,OAAO;kBACL;QACA,GAAG,MAAM;IACX;AACF", "debugId": null}}, {"offset": {"line": 4153, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/grid/dist/useGridRowGroup.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/grid/dist/packages/%40react-aria/grid/src/useGridRowGroup.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\n\nexport interface GridRowGroupAria {\n  /** Props for the row group element. */\n  rowGroupProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for a row group in a grid.\n */\nexport function useGridRowGroup(): GridRowGroupAria {\n  return {\n    rowGroupProps: {\n      role: 'rowgroup'\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAYM,SAAS;IACd,OAAO;QACL,eAAe;YACb,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 4179, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/live-announcer/dist/LiveAnnouncer.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/live-announcer/dist/packages/%40react-aria/live-announcer/src/LiveAnnouncer.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\ntype Assertiveness = 'assertive' | 'polite';\n\n/* Inspired by https://github.com/AlmeroSteyn/react-aria-live */\nconst LIVEREGION_TIMEOUT_DELAY = 7000;\n\nlet liveAnnouncer: LiveAnnouncer | null = null;\n\ntype Message = string | {'aria-labelledby': string};\n\n/**\n * Announces the message using screen reader technology.\n */\nexport function announce(\n  message: Message,\n  assertiveness: Assertiveness = 'assertive',\n  timeout: number = LIVEREGION_TIMEOUT_DELAY\n): void {\n  if (!liveAnnouncer) {\n    liveAnnouncer = new LiveAnnouncer();\n    // wait for the live announcer regions to be added to the dom, then announce\n    // otherwise <PERSON><PERSON> won't announce the message if it's added too quickly\n    // found most times less than 100ms were not consistent when announcing with Safari\n\n    // IS_REACT_ACT_ENVIRONMENT is used by React 18. Previous versions checked for the `jest` global.\n    // https://github.com/reactwg/react-18/discussions/102\n    // if we're in a test environment, announce without waiting\n    // @ts-ignore\n    if (!(typeof IS_REACT_ACT_ENVIRONMENT === 'boolean' ? IS_REACT_ACT_ENVIRONMENT : typeof jest !== 'undefined')) {\n      setTimeout(() => {\n        if (liveAnnouncer?.isAttached()) {\n          liveAnnouncer?.announce(message, assertiveness, timeout);\n        }\n      }, 100);\n    } else {\n      liveAnnouncer.announce(message, assertiveness, timeout);\n    }\n  } else {\n    liveAnnouncer.announce(message, assertiveness, timeout);\n  }\n}\n\n/**\n * Stops all queued announcements.\n */\nexport function clearAnnouncer(assertiveness: Assertiveness): void {\n  if (liveAnnouncer) {\n    liveAnnouncer.clear(assertiveness);\n  }\n}\n\n/**\n * Removes the announcer from the DOM.\n */\nexport function destroyAnnouncer(): void {\n  if (liveAnnouncer) {\n    liveAnnouncer.destroy();\n    liveAnnouncer = null;\n  }\n}\n\n// LiveAnnouncer is implemented using vanilla DOM, not React. That's because as of React 18\n// ReactDOM.render is deprecated, and the replacement, ReactDOM.createRoot is moved into a\n// subpath import `react-dom/client`. That makes it hard for us to support multiple React versions.\n// As a global API, we can't use portals without introducing a breaking API change. LiveAnnouncer\n// is simple enough to implement without React, so that's what we do here.\n// See this discussion for more details: https://github.com/reactwg/react-18/discussions/125#discussioncomment-2382638\nclass LiveAnnouncer {\n  node: HTMLElement | null = null;\n  assertiveLog: HTMLElement | null = null;\n  politeLog: HTMLElement | null = null;\n\n  constructor() {\n    if (typeof document !== 'undefined') {\n      this.node = document.createElement('div');\n      this.node.dataset.liveAnnouncer = 'true';\n      // copied from VisuallyHidden\n      Object.assign(this.node.style, {\n        border: 0,\n        clip: 'rect(0 0 0 0)',\n        clipPath: 'inset(50%)',\n        height: '1px',\n        margin: '-1px',\n        overflow: 'hidden',\n        padding: 0,\n        position: 'absolute',\n        width: '1px',\n        whiteSpace: 'nowrap'\n      });\n\n      this.assertiveLog = this.createLog('assertive');\n      this.node.appendChild(this.assertiveLog);\n\n      this.politeLog = this.createLog('polite');\n      this.node.appendChild(this.politeLog);\n\n      document.body.prepend(this.node);\n    }\n  }\n\n  isAttached() {\n    return this.node?.isConnected;\n  }\n\n  createLog(ariaLive: string) {\n    let node = document.createElement('div');\n    node.setAttribute('role', 'log');\n    node.setAttribute('aria-live', ariaLive);\n    node.setAttribute('aria-relevant', 'additions');\n    return node;\n  }\n\n  destroy() {\n    if (!this.node) {\n      return;\n    }\n\n    document.body.removeChild(this.node);\n    this.node = null;\n  }\n\n  announce(message: Message, assertiveness = 'assertive', timeout = LIVEREGION_TIMEOUT_DELAY) {\n    if (!this.node) {\n      return;\n    }\n\n    let node = document.createElement('div');\n    if (typeof message === 'object') {\n      // To read an aria-labelledby, the element must have an appropriate role, such as img.\n      node.setAttribute('role', 'img');\n      node.setAttribute('aria-labelledby', message['aria-labelledby']);\n    } else {\n      node.textContent = message;\n    }\n\n    if (assertiveness === 'assertive') {\n      this.assertiveLog?.appendChild(node);\n    } else {\n      this.politeLog?.appendChild(node);\n    }\n\n    if (message !== '') {\n      setTimeout(() => {\n        node.remove();\n      }, timeout);\n    }\n  }\n\n  clear(assertiveness: Assertiveness) {\n    if (!this.node) {\n      return;\n    }\n\n    if ((!assertiveness || assertiveness === 'assertive') && this.assertiveLog) {\n      this.assertiveLog.innerHTML = '';\n    }\n\n    if ((!assertiveness || assertiveness === 'polite') && this.politeLog) {\n      this.politeLog.innerHTML = '';\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAID,8DAA8D;;;;;AAC9D,MAAM,iDAA2B;AAEjC,IAAI,sCAAsC;AAOnC,SAAS,0CACd,OAAgB,EAChB,gBAA+B,WAAW,EAC1C,UAAkB,8CAAwB;IAE1C,IAAI,CAAC,qCAAe;QAClB,sCAAgB,IAAI;QACpB,4EAA4E;QAC5E,wEAAwE;QACxE,mFAAmF;QAEnF,iGAAiG;QACjG,sDAAsD;QACtD,2DAA2D;QAC3D,aAAa;QACb,IAAI,CAAE,CAAA,OAAO,6BAA6B,YAAY,2BAA2B,OAAO,SAAS,WAAU,GACzG,WAAW;YACT,IAAI,wCAAA,QAAA,wCAAA,KAAA,IAAA,KAAA,IAAA,oCAAe,UAAU,IAC3B,wCAAA,QAAA,wCAAA,KAAA,IAAA,KAAA,IAAA,oCAAe,QAAQ,CAAC,SAAS,eAAe;QAEpD,GAAG;aAEH,oCAAc,QAAQ,CAAC,SAAS,eAAe;IAEnD,OACE,oCAAc,QAAQ,CAAC,SAAS,eAAe;AAEnD;AAKO,SAAS,0CAAe,aAA4B;IACzD,IAAI,qCACF,oCAAc,KAAK,CAAC;AAExB;AAKO,SAAS;IACd,IAAI,qCAAe;QACjB,oCAAc,OAAO;QACrB,sCAAgB;IAClB;AACF;AAEA,2FAA2F;AAC3F,0FAA0F;AAC1F,mGAAmG;AACnG,iGAAiG;AACjG,0EAA0E;AAC1E,sHAAsH;AACtH,MAAM;IAiCJ,aAAa;YACJ;QAAP,OAAA,CAAO,aAAA,IAAI,CAAC,IAAI,MAAA,QAAT,eAAA,KAAA,IAAA,KAAA,IAAA,WAAW,WAAW;IAC/B;IAEA,UAAU,QAAgB,EAAE;QAC1B,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,KAAK,YAAY,CAAC,QAAQ;QAC1B,KAAK,YAAY,CAAC,aAAa;QAC/B,KAAK,YAAY,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,IAAI,EACZ;QAGF,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QACnC,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,SAAS,OAAgB,EAAE,gBAAgB,WAAW,EAAE,UAAU,8CAAwB,EAAE;YAexF,oBAEA;QAhBF,IAAI,CAAC,IAAI,CAAC,IAAI,EACZ;QAGF,IAAI,OAAO,SAAS,aAAa,CAAC;QAClC,IAAI,OAAO,YAAY,UAAU;YAC/B,sFAAsF;YACtF,KAAK,YAAY,CAAC,QAAQ;YAC1B,KAAK,YAAY,CAAC,mBAAmB,OAAO,CAAC,kBAAkB;QACjE,OACE,KAAK,WAAW,GAAG;QAGrB,IAAI,kBAAkB,aAAA,CACpB,qBAAA,IAAI,CAAC,YAAY,MAAA,QAAjB,uBAAA,KAAA,IAAA,KAAA,IAAA,mBAAmB,WAAW,CAAC;cAE/B,kBAAA,IAAI,CAAC,SAAS,MAAA,QAAd,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAgB,WAAW,CAAC;QAG9B,IAAI,YAAY,IACd,WAAW;YACT,KAAK,MAAM;QACb,GAAG;IAEP;IAEA,MAAM,aAA4B,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,IAAI,EACZ;QAGF,IAAK,CAAA,CAAC,iBAAiB,kBAAkB,WAAU,KAAM,IAAI,CAAC,YAAY,EACxE,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG;QAGhC,IAAK,CAAA,CAAC,iBAAiB,kBAAkB,QAAO,KAAM,IAAI,CAAC,SAAS,EAClE,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IAE/B;IAxFA,aAAc;aAJd,IAAA,GAA2B;aAC3B,YAAA,GAAmC;aACnC,SAAA,GAAgC;QAG9B,IAAI,OAAO,aAAa,aAAa;YACnC,IAAI,CAAC,IAAI,GAAG,SAAS,aAAa,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG;YAClC,6BAA6B;YAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBAC7B,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;YAEA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY;YAEvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS;YAEpC,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QACjC;IACF;AA+DF", "debugId": null}}, {"offset": {"line": 4298, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useUpdateEffect.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useUpdateEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {EffectCallback, useEffect, useRef} from 'react';\n\n// Like useEffect, but only called for updates after the initial render.\nexport function useUpdateEffect(effect: EffectCallback, dependencies: any[]): void {\n  const isInitialMount = useRef(true);\n  const lastDeps = useRef<any[] | null>(null);\n\n  useEffect(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n\n  useEffect(() => {\n    let prevDeps = lastDeps.current;\n    if (isInitialMount.current) {\n      isInitialMount.current = false;\n    } else if (!prevDeps || dependencies.some((dep, i) => !Object.is(dep, prevDeps[i]))) {\n      effect();\n    }\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAKM,SAAS,0CAAgB,MAAsB,EAAE,YAAmB;IACzE,MAAM,iBAAiB,CAAA,yMAAA,SAAK,EAAE;IAC9B,MAAM,WAAW,CAAA,yMAAA,SAAK,EAAgB;IAEtC,CAAA,yMAAA,YAAQ,EAAE;QACR,eAAe,OAAO,GAAG;QACzB,OAAO;YACL,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG,EAAE;IAEL,CAAA,yMAAA,YAAQ,EAAE;QACR,IAAI,WAAW,SAAS,OAAO;QAC/B,IAAI,eAAe,OAAO,EACxB,eAAe,OAAO,GAAG;aACpB,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,CAAC,KAAK,IAAM,CAAC,OAAO,EAAE,CAAC,KAAK,QAAQ,CAAC,EAAE,IAC/E;QAEF,SAAS,OAAO,GAAG;IACnB,uDAAuD;IACzD,GAAG;AACL", "debugId": null}}, {"offset": {"line": 4336, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/focus/dist/useHasTabbableChild.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/useHasTabbableChild.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getFocusableTreeWalker} from './FocusScope';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {useState} from 'react';\n\ninterface AriaHasTabbableChildOptions {\n  isDisabled?: boolean\n}\n\n// This was created for a special empty case of a component that can have child or\n// be empty, like Collection/Virtualizer/Table/ListView/etc. When these components\n// are empty they can have a message with a tabbable element, which is like them\n// being not empty, when it comes to focus and tab order.\n\n/**\n * Returns whether an element has a tabbable child, and updates as children change.\n * @private\n */\nexport function useHasTabbableChild(ref: RefObject<Element | null>, options?: AriaHasTabbableChildOptions): boolean {\n  let isDisabled = options?.isDisabled;\n  let [hasTabbableChild, setHasTabbableChild] = useState(false);\n\n  useLayoutEffect(() => {\n    if (ref?.current && !isDisabled) {\n      let update = () => {\n        if (ref.current) {\n          let walker = getFocusableTreeWalker(ref.current, {tabbable: true});\n          setHasTabbableChild(!!walker.nextNode());\n        }\n      };\n\n      update();\n\n      // Update when new elements are inserted, or the tabIndex/disabled attribute updates.\n      let observer = new MutationObserver(update);\n      observer.observe(ref.current, {\n        subtree: true,\n        childList: true,\n        attributes: true,\n        attributeFilter: ['tabIndex', 'disabled']\n      });\n\n      return () => {\n        // Disconnect mutation observer when a React update occurs on the top-level component\n        // so we update synchronously after re-rendering. Otherwise React will emit act warnings\n        // in tests since mutation observers fire asynchronously. The mutation observer is necessary\n        // so we also update if a child component re-renders and adds/removes something tabbable.\n        observer.disconnect();\n      };\n    }\n  });\n\n  return isDisabled ? false : hasTabbableChild;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,SAAS,0CAAoB,GAA8B,EAAE,OAAqC;IACvG,IAAI,aAAa,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,UAAU;IACpC,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,yMAAA,WAAO,EAAE;IAEvD,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,CAAA,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,OAAO,KAAI,CAAC,YAAY;YAC/B,IAAI,SAAS;gBACX,IAAI,IAAI,OAAO,EAAE;oBACf,IAAI,SAAS,CAAA,mKAAA,yBAAqB,EAAE,IAAI,OAAO,EAAE;wBAAC,UAAU;oBAAI;oBAChE,oBAAoB,CAAC,CAAC,OAAO,QAAQ;gBACvC;YACF;YAEA;YAEA,qFAAqF;YACrF,IAAI,WAAW,IAAI,iBAAiB;YACpC,SAAS,OAAO,CAAC,IAAI,OAAO,EAAE;gBAC5B,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,iBAAiB;oBAAC;oBAAY;iBAAW;YAC3C;YAEA,OAAO;gBACL,qFAAqF;gBACrF,wFAAwF;gBACxF,4FAA4F;gBAC5F,yFAAyF;gBACzF,SAAS,UAAU;YACrB;QACF;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B", "debugId": null}}]}