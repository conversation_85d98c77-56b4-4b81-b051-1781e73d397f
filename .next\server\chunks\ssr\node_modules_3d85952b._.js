module.exports = {

"[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_8949b27e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/xlsx/xlsx.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_xlsx_xlsx_mjs_105cf573._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/xlsx/xlsx.mjs [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_71ed62e5._.js",
  "server/chunks/ssr/node_modules_ccb71a4f._.js",
  "server/chunks/ssr/[externals]_module_74f3e68c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_jspdf-autotable_dist_jspdf_plugin_autotable_mjs_fe341469._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-ssr] (ecmascript)");
    });
});
}),

};