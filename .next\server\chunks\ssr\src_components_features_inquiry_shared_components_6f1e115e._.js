module.exports = {

"[project]/src/components/features/inquiry/shared/components/FilterSwitch.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$switch$2f$dist$2f$chunk$2d$TQNYOUFX$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__switch_default__as__Switch$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/switch/dist/chunk-TQNYOUFX.mjs [app-ssr] (ecmascript) <export switch_default as Switch>");
;
;
;
const FilterSwitch = ({ id, checked, onChange, label, size = "sm", disabled = false })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${disabled ? "opacity-50" : ""}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$switch$2f$dist$2f$chunk$2d$TQNYOUFX$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__switch_default__as__Switch$3e$__["Switch"], {
                id: id,
                isSelected: checked,
                onValueChange: disabled ? undefined : onChange,
                size: size,
                isDisabled: disabled,
                "aria-label": label,
                "aria-labelledby": `${id}-label`,
                classNames: {
                    wrapper: "group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400",
                    thumb: "group-data-[selected=true]:bg-white shadow-lg"
                }
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/components/FilterSwitch.jsx",
                lineNumber: 18,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                id: `${id}-label`,
                htmlFor: id,
                className: `text-sm font-medium transition-colors duration-200 flex-1 ${disabled ? "text-gray-400 cursor-not-allowed" : "text-gray-700 group-hover:text-purple-600 cursor-pointer"}`,
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/components/FilterSwitch.jsx",
                lineNumber: 32,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSwitch.jsx",
        lineNumber: 13,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = FilterSwitch;
}),
"[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterSwitch.jsx [app-ssr] (ecmascript)");
// Import filter group components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KementerianFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KementerianFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$UnitFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/UnitFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$LokasiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/LokasiFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$FungsiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/FungsiFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SubfungsiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/SubfungsiFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$ProgramFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/ProgramFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KegiatanFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KegiatanFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$OutputFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/OutputFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SuboutputFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/SuboutputFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KomponenFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KomponenFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SubkomponenFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/SubkomponenFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$AkunFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/AkunFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SumberDanaFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/SumberDanaFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$RegisterFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/RegisterFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$InflasiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/InflasiFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$IknFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/IknFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KemiskinanFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KemiskinanFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$PanganFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/PanganFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$StuntingFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/StuntingFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$PemiluFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/PemiluFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$PrinasFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/PrinasFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$ProgrampriFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/ProgrampriFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KegiatanpriFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KegiatanpriFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$ProyekpriFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/ProyekpriFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$MajorprFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/MajorprFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$TematikFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/TematikFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$DekonFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/DekonFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KabkotaFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KabkotaFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KanwilFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KanwilFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KppnFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/KppnFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SatkerFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/SatkerFilter.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$CutoffFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterGroups/CutoffFilter.jsx [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const FilterSection = ({ inquiryState })=>{
    const { // Report type for determining default switches
    jenlap, // Filter visibility states
    tanggal, setTanggal, cutoff, setCutoff, showCutoffSelector, setShowCutoffSelector, akumulatif, setAkumulatif, kddept, setKddept, unit, setUnit, kddekon, setKddekon, kdlokasi, setKdlokasi, kdkabkota, setKdkabkota, kdkanwil, setKdkanwil, kdkppn, setKdkppn, kdsatker, setKdsatker, kdfungsi, setKdfungsi, kdsfungsi, setKdsfungsi, kdprogram, setKdprogram, kdgiat, setKdgiat, kdoutput, setKdoutput, kdsoutput, setKdsoutput, kdkomponen, setKdkomponen, kdskomponen, setKdskomponen, kdakun, setKdakun, kdsdana, setKdsdana, kdregister, setKdregister, kdInflasi, setKdInflasi, kdIkn, setKdIkn, kdKemiskinan, setKdKemiskinan, KdPRI, setKdPRI, KdPangan, setKdPangan, KdPemilu, setKdPemilu, KdStunting, setKdStunting, KdTema, setKdTema, KdPN, setKdPN, KdPP, setKdPP, KdKegPP, setKdKegPP, KdMP, setKdMP, // Filter values and conditions
    dept, setDept, deptkondisi, setDeptkondisi, katadept, setKatadept, deptradio, setDeptradio, kdunit, setKdunit, unitkondisi, setUnitkondisi, kataunit, setKataunit, unitradio, setUnitradio, dekon, setDekon, dekonkondisi, setDekonkondisi, katadekon, setKatadekon, dekonradio, setDekonradio, prov, setProv, lokasikondisi, setLokasikondisi, katalokasi, setKatalokasi, locradio, setLocradio, kabkota, setKabkota, kabkotakondisi, setKabkotakondisi, katakabkota, setKatakabkota, kabkotaradio, setKabkotaradio, kanwil, setKanwil, kanwilkondisi, setKanwilkondisi, katakanwil, setKatakanwil, kanwilradio, setKanwilradio, kppn, setKppn, kppnkondisi, setKppnkondisi, katakppn, setKatakppn, kppnradio, setKppnradio, satker, setSatker, satkerkondisi, setSatkerkondisi, katasatker, setKatasatker, satkerradio, setSatkerradio, fungsi, setFungsi, fungsikondisi, setFungsikondisi, katafungsi, setKatafungsi, fungsiradio, setFungsiradio, sfungsi, setSfungsi, subfungsikondisi, setSubfungsikondisi, katasubfungsi, setKatasubfungsi, subfungsiradio, setSubfungsiradio, program, setProgram, programkondisi, setProgramkondisi, kataprogram, setKataprogram, programradio, setProgramradio, giat, setGiat, giatkondisi, setGiatkondisi, katagiat, setKatagiat, kegiatanradio, setKegiatanradio, output, setOutput, outputkondisi, setOutputkondisi, kataoutput, setKataoutput, outputradio, setOutputradio, soutput, setsOutput, soutputkondisi, setSoutputkondisi, katasoutput, setKatasoutput, soutputradio, setsOutputradio, komponen, setKomponen, komponenkondisi, setKomponenkondisi, katakomponen, setKatakomponen, komponenradio, setKomponenradio, skomponen, setSkomponen, skomponenkondisi, setSkomponenkondisi, kataskomponen, setKataskomponen, skomponenradio, setSkomponenradio, akun, setAkun, akunkondisi, setAkunkondisi, kataakun, setKataakun, akunradio, setAkunradio, sdana, setSdana, sdanakondisi, setSdanakondisi, katasdana, setKatasdana, sdanaradio, setSdanaradio, register, setRegister, registerkondisi, setRegisterkondisi, kataregister, setKataregister, registerradio, setRegisterradio, Inflasi, setInflasi, inflasiradio, setInflasiradio, opsiInflasi, setOpsiInflasi, Ikn, setIkn, iknradio, setIknradio, opsiIkn, setOpsiIkn, Miskin, setMiskin, kemiskinanradio, setKemiskinanradio, opsiKemiskinan, setOpsiKemiskinan, Pangan, setPangan, panganradio, setPanganradio, opsiPangan, setOpsiPangan, Stunting, setStunting, stuntingradio, setStuntingradio, opsiStunting, setOpsiStunting, PN, setPN, pnradio, setPnradio, PP, setPP, ppradio, setPpradio, kegiatanprioritas, setKegiatanPrioritas, kegiatanprioritasradio, setKegiatanPrioritasRadio, MP, setMP, mpradio, setMpradio, Tema, setTema, temaradio, setTemaradio, Pemilu, setPemilu, pemiluradio, setPemiluradio, PRI, setPRI, priradio, setPriradio } = inquiryState;
    // Remove local state - use the actual filter switch states from inquiryState instead
    // const [showKementerian, setShowKementerian] = React.useState(false);
    // const [showUnit, setShowUnit] = React.useState(false);
    // const [showDekon, setShowDekon] = React.useState(false);
    // const [showKabkota, setShowKabkota] = React.useState(false);
    // const [showKanwil, setShowKanwil] = React.useState(false);
    // const [showLokasi, setShowLokasi] = React.useState(false);
    // Use the actual filter switch states from inquiryState
    const showKementerian = kddept;
    const setShowKementerian = setKddept;
    const showUnit = unit;
    const setShowUnit = setUnit;
    const showDekon = kddekon;
    const setShowDekon = setKddekon;
    const showKabkota = kdkabkota;
    const setShowKabkota = setKdkabkota;
    const showKanwil = kdkanwil;
    const setShowKanwil = setKdkanwil;
    const showLokasi = kdlokasi;
    const setShowLokasi = setKdlokasi;
    // Determine which switches should be disabled based on report type
    const getDisabledSwitches = (reportType)=>{
        // For Volume Output Kegiatan (6), disable cutoff, akun, register, sumber dana
        if (reportType === "6") {
            return [
                "cutoff",
                "kdakun",
                "kdregister",
                "kdsdana"
            ];
        }
        // Base disabled switches for most report types (same as Pagu Realisasi)
        const baseDisabledSwitches = [
            "kdsoutput",
            "KdPN",
            "KdPP",
            "KdKegPP",
            "KdPRI",
            "KdMP",
            "KdTema",
            "kdInflasi",
            "KdStunting",
            "kdKemiskinan",
            "KdPemilu",
            "kdIkn",
            "KdPangan"
        ];
        // For Pagu APBN (1), disable the same switches as other types plus cutoff
        if (reportType === "1") {
            return [
                ...baseDisabledSwitches,
                "cutoff"
            ];
        }
        // For all other report types (including Pagu Realisasi "2"), disable base switches
        return baseDisabledSwitches;
    };
    const disabledSwitches = getDisabledSwitches(jenlap);
    // Automatically turn off disabled switches when report type changes
    // This effect runs when jenlap changes to manage switch states
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!jenlap) return;
        // If switching to a report type that disables certain switches, turn them off
        if (disabledSwitches.length > 0) {
            if (disabledSwitches.includes("kdsoutput") && kdsoutput) {
                setKdsoutput && setKdsoutput(false);
            }
            if (disabledSwitches.includes("KdPN") && KdPN) {
                setKdPN && setKdPN(false);
            }
            if (disabledSwitches.includes("KdPP") && KdPP) {
                setKdPP && setKdPP(false);
            }
            if (disabledSwitches.includes("KdKegPP") && KdKegPP) {
                setKdKegPP && setKdKegPP(false);
            }
            if (disabledSwitches.includes("KdPRI") && KdPRI) {
                setKdPRI && setKdPRI(false);
            }
            if (disabledSwitches.includes("KdMP") && KdMP) {
                setKdMP && setKdMP(false);
            }
            if (disabledSwitches.includes("KdTema") && KdTema) {
                setKdTema && setKdTema(false);
            }
            if (disabledSwitches.includes("kdInflasi") && kdInflasi) {
                setKdInflasi && setKdInflasi(false);
            }
            if (disabledSwitches.includes("KdStunting") && KdStunting) {
                setKdStunting && setKdStunting(false);
            }
            if (disabledSwitches.includes("kdKemiskinan") && kdKemiskinan) {
                setKdKemiskinan && setKdKemiskinan(false);
            }
            if (disabledSwitches.includes("KdPemilu") && KdPemilu) {
                setKdPemilu && setKdPemilu(false);
            }
            if (disabledSwitches.includes("kdIkn") && kdIkn) {
                setKdIkn && setKdIkn(false);
            }
            if (disabledSwitches.includes("KdPangan") && KdPangan) {
                setKdPangan && setKdPangan(false);
            }
            if (disabledSwitches.includes("cutoff") && cutoff !== "0") {
                setCutoff && setCutoff("0");
                setShowCutoffSelector && setShowCutoffSelector(false);
            }
        }
    }, [
        jenlap
    ]); // Only depend on jenlap to avoid circular dependencies
    // Reset filter values when switches are turned off
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kddept) {
            // Reset Kementerian filter state
            setDept && setDept("000"); // Default dept value
            setDeptkondisi && setDeptkondisi("");
            setKatadept && setKatadept("");
            setDeptradio && setDeptradio("1");
        }
    }, [
        kddept,
        setDept,
        setDeptkondisi,
        setKatadept,
        setDeptradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!unit) {
            // Reset Unit filter state
            setKdunit && setKdunit("XX");
            setUnitkondisi && setUnitkondisi("");
            setKataunit && setKataunit("");
            setUnitradio && setUnitradio("1");
        }
    }, [
        unit,
        setKdunit,
        setUnitkondisi,
        setKataunit,
        setUnitradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kddekon) {
            // Reset Dekon filter state
            setDekon && setDekon("XX");
            setDekonkondisi && setDekonkondisi("");
            setKatadekon && setKatadekon("");
            setDekonradio && setDekonradio("1");
        }
    }, [
        kddekon,
        setDekon,
        setDekonkondisi,
        setKatadekon,
        setDekonradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdlokasi) {
            // Reset Provinsi filter state
            setProv && setProv("XX");
            setLokasikondisi && setLokasikondisi("");
            setKatalokasi && setKatalokasi("");
            setLocradio && setLocradio("1");
        }
    }, [
        kdlokasi,
        setProv,
        setLokasikondisi,
        setKatalokasi,
        setLocradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdkabkota) {
            // Reset Kabkota filter state
            setKabkota && setKabkota("XX");
            setKabkotakondisi && setKabkotakondisi("");
            setKatakabkota && setKatakabkota("");
            setKabkotaradio && setKabkotaradio("1");
        }
    }, [
        kdkabkota,
        setKabkota,
        setKabkotakondisi,
        setKatakabkota,
        setKabkotaradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdkanwil) {
            // Reset Kanwil filter state
            setKanwil && setKanwil("XX");
            setKanwilkondisi && setKanwilkondisi("");
            setKatakanwil && setKatakanwil("");
            setKanwilradio && setKanwilradio("1");
        }
    }, [
        kdkanwil,
        setKanwil,
        setKanwilkondisi,
        setKatakanwil,
        setKanwilradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdkppn) {
            // Reset KPPN filter state
            setKppn && setKppn("XX");
            setKppnkondisi && setKppnkondisi("");
            setKatakppn && setKatakppn("");
            setKppnradio && setKppnradio("1");
        }
    }, [
        kdkppn,
        setKppn,
        setKppnkondisi,
        setKatakppn,
        setKppnradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdsatker) {
            // Reset Satker filter state
            setSatker && setSatker("XX");
            setSatkerkondisi && setSatkerkondisi("");
            setKatasatker && setKatasatker("");
            setSatkerradio && setSatkerradio("1");
        }
    }, [
        kdsatker,
        setSatker,
        setSatkerkondisi,
        setKatasatker,
        setSatkerradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdfungsi) {
            // Reset Fungsi filter state
            setFungsi && setFungsi("XX");
            setFungsikondisi && setFungsikondisi("");
            setKatafungsi && setKatafungsi("");
            setFungsiradio && setFungsiradio("1");
        }
    }, [
        kdfungsi,
        setFungsi,
        setFungsikondisi,
        setKatafungsi,
        setFungsiradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdsfungsi) {
            // Reset Sub-fungsi filter state
            setSfungsi && setSfungsi("XX");
            setSubfungsikondisi && setSubfungsikondisi("");
            setKatasubfungsi && setKatasubfungsi("");
            setSubfungsiradio && setSubfungsiradio("1");
        }
    }, [
        kdsfungsi,
        setSfungsi,
        setSubfungsikondisi,
        setKatasubfungsi,
        setSubfungsiradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdprogram) {
            // Reset Program filter state
            setProgram && setProgram("XX");
            setProgramkondisi && setProgramkondisi("");
            setKataprogram && setKataprogram("");
            setProgramradio && setProgramradio("1");
        }
    }, [
        kdprogram,
        setProgram,
        setProgramkondisi,
        setKataprogram,
        setProgramradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdgiat) {
            // Reset Kegiatan filter state
            setGiat && setGiat("XX");
            setGiatkondisi && setGiatkondisi("");
            setKatagiat && setKatagiat("");
            setKegiatanradio && setKegiatanradio("1");
        }
    }, [
        kdgiat,
        setGiat,
        setGiatkondisi,
        setKatagiat,
        setKegiatanradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdoutput) {
            // Reset Output filter state
            setOutput && setOutput("XX");
            setOutputkondisi && setOutputkondisi("");
            setKataoutput && setKataoutput("");
            setOutputradio && setOutputradio("1");
        }
    }, [
        kdoutput,
        setOutput,
        setOutputkondisi,
        setKataoutput,
        setOutputradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdsoutput) {
            // Reset Sub-output filter state
            setsOutput && setsOutput("XX");
            setSoutputkondisi && setSoutputkondisi("");
            setKatasoutput && setKatasoutput("");
            setsOutputradio && setsOutputradio("1");
        }
    }, [
        kdsoutput,
        setsOutput,
        setSoutputkondisi,
        setKatasoutput,
        setsOutputradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdkomponen) {
            // Reset Komponen filter state
            setKomponen && setKomponen("XX");
            setKomponenkondisi && setKomponenkondisi("");
            setKatakomponen && setKatakomponen("");
            setKomponenradio && setKomponenradio("1");
        }
    }, [
        kdkomponen,
        setKomponen,
        setKomponenkondisi,
        setKatakomponen,
        setKomponenradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdskomponen) {
            // Reset Sub-komponen filter state
            setSkomponen && setSkomponen("XX");
            setSkomponenkondisi && setSkomponenkondisi("");
            setKataskomponen && setKataskomponen("");
            setSkomponenradio && setSkomponenradio("1");
        }
    }, [
        kdskomponen,
        setSkomponen,
        setSkomponenkondisi,
        setKataskomponen,
        setSkomponenradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdakun) {
            // Reset Akun filter state
            setAkun && setAkun("AKUN");
            setAkunkondisi && setAkunkondisi("");
            setKataakun && setKataakun("");
            setAkunradio && setAkunradio("1");
        }
    }, [
        kdakun,
        setAkun,
        setAkunkondisi,
        setKataakun,
        setAkunradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdsdana) {
            // Reset Sumber Dana filter state
            setSdana && setSdana("XX");
            setSdanakondisi && setSdanakondisi("");
            setKatasdana && setKatasdana("");
            setSdanaradio && setSdanaradio("1");
        }
    }, [
        kdsdana,
        setSdana,
        setSdanakondisi,
        setKatasdana,
        setSdanaradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdregister) {
            // Reset Register filter state
            setRegister && setRegister("XX");
            setRegisterkondisi && setRegisterkondisi("");
            setKataregister && setKataregister("");
            setRegisterradio && setRegisterradio("1");
        }
    }, [
        kdregister,
        setRegister,
        setRegisterkondisi,
        setKataregister,
        setRegisterradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdInflasi) {
            // Reset Inflasi filter state
            setInflasi && setInflasi("00");
            setInflasiradio && setInflasiradio("1");
        }
    }, [
        kdInflasi,
        setInflasi,
        setInflasiradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdIkn) {
            // Reset IKN filter state
            setIkn && setIkn("00");
            setIknradio && setIknradio("1");
        }
    }, [
        kdIkn,
        setIkn,
        setIknradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!kdKemiskinan) {
            // Reset Kemiskinan filter state
            setMiskin && setMiskin("00");
            setKemiskinanradio && setKemiskinanradio("1");
        }
    }, [
        kdKemiskinan,
        setMiskin,
        setKemiskinanradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdPangan) {
            // Reset Pangan filter state
            setPangan && setPangan("00");
            setPanganradio && setPanganradio("1");
        }
    }, [
        KdPangan,
        setPangan,
        setPanganradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdStunting) {
            // Reset Stunting filter state
            setStunting && setStunting("00");
            setStuntingradio && setStuntingradio("1");
        }
    }, [
        KdStunting,
        setStunting,
        setStuntingradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdPN) {
            // Reset Prinas filter state
            setPN && setPN("00");
            setPnradio && setPnradio("1");
        }
    }, [
        KdPN,
        setPN,
        setPnradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdPP) {
            // Reset Programpri filter state
            setPP && setPP("00");
            setPpradio && setPpradio("1");
        }
    }, [
        KdPP,
        setPP,
        setPpradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdKegPP) {
            // Reset Kegiatanpri filter state
            setKegiatanPrioritas && setKegiatanPrioritas("XX");
            setKegiatanPrioritasRadio && setKegiatanPrioritasRadio("1");
        }
    }, [
        KdKegPP,
        setKegiatanPrioritas,
        setKegiatanPrioritasRadio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdMP) {
            // Reset Majorpr filter state
            setMP && setMP("00");
            setMpradio && setMpradio("1");
        }
    }, [
        KdMP,
        setMP,
        setMpradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdTema) {
            // Reset Tematik filter state
            setTema && setTema("00");
            setTemaradio && setTemaradio("1");
        }
    }, [
        KdTema,
        setTema,
        setTemaradio
    ]);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!KdPemilu) {
            // Reset Pemilu filter state
            setPemilu && setPemilu("00");
            setPemiluradio && setPemiluradio("1");
        }
    }, [
        KdPemilu,
        setPemilu,
        setPemiluradio
    ]);
    // Set default filter switches based on report type (jenlap)
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!jenlap) return; // Don't do anything if jenlap is not set
        // Define default switches for each report type
        const getDefaultSwitches = (reportType)=>{
            // Base configuration: Only Kementerian is ON by default for all report types
            const baseConfig = {
                kddept: true,
                unit: false,
                kddekon: false,
                kdlokasi: false,
                kdkabkota: false,
                kdkanwil: false,
                kdkppn: false,
                kdsatker: false,
                kdfungsi: false,
                kdsfungsi: false,
                kdprogram: false,
                kdgiat: false,
                kdoutput: false,
                kdsoutput: false,
                kdakun: false,
                kdsdana: false,
                kdregister: false,
                KdPN: false,
                KdPP: false,
                KdKegPP: false,
                KdPRI: false,
                KdMP: false,
                KdTema: false,
                kdInflasi: false,
                KdStunting: false,
                kdKemiskinan: false,
                KdPemilu: false,
                kdIkn: false,
                KdPangan: false
            };
            // Return base config for all report types
            // Special cases (Pagu APBN and Volume Output Kegiatan) will have all switches available
            // Other report types will have certain switches disabled (handled in the UI)
            return baseConfig;
        };
        const defaultSwitches = getDefaultSwitches(jenlap);
        // Apply the default switches
        setKddept && setKddept(defaultSwitches.kddept);
        setUnit && setUnit(defaultSwitches.unit);
        setKddekon && setKddekon(defaultSwitches.kddekon);
        setKdlokasi && setKdlokasi(defaultSwitches.kdlokasi);
        setKdkabkota && setKdkabkota(defaultSwitches.kdkabkota);
        setKdkanwil && setKdkanwil(defaultSwitches.kdkanwil);
        setKdkppn && setKdkppn(defaultSwitches.kdkppn);
        setKdsatker && setKdsatker(defaultSwitches.kdsatker);
        setKdfungsi && setKdfungsi(defaultSwitches.kdfungsi);
        setKdsfungsi && setKdsfungsi(defaultSwitches.kdsfungsi);
        setKdprogram && setKdprogram(defaultSwitches.kdprogram);
        setKdgiat && setKdgiat(defaultSwitches.kdgiat);
        setKdoutput && setKdoutput(defaultSwitches.kdoutput);
        setKdsoutput && setKdsoutput(defaultSwitches.kdsoutput);
        setKdakun && setKdakun(defaultSwitches.kdakun);
        setKdsdana && setKdsdana(defaultSwitches.kdsdana);
        setKdregister && setKdregister(defaultSwitches.kdregister);
        setKdPN && setKdPN(defaultSwitches.KdPN);
        setKdPP && setKdPP(defaultSwitches.KdPP);
        setKdKegPP && setKdKegPP(defaultSwitches.KdKegPP);
        setKdPRI && setKdPRI(defaultSwitches.KdPRI);
        setKdMP && setKdMP(defaultSwitches.KdMP);
        setKdTema && setKdTema(defaultSwitches.KdTema);
        setKdInflasi && setKdInflasi(defaultSwitches.kdInflasi);
        setKdStunting && setKdStunting(defaultSwitches.KdStunting);
        setKdKemiskinan && setKdKemiskinan(defaultSwitches.kdKemiskinan);
        setKdPemilu && setKdPemilu(defaultSwitches.KdPemilu);
        setKdIkn && setKdIkn(defaultSwitches.kdIkn);
        setKdPangan && setKdPangan(defaultSwitches.KdPangan);
    }, [
        jenlap
    ]); // Only run when jenlap changes
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "cutoff-filter",
                            checked: cutoff !== "0",
                            onChange: (val)=>{
                                if (val) {
                                    // When enabled, reset to January
                                    setCutoff("1");
                                } else {
                                    // When disabled, set to "0" (disabled state)
                                    setCutoff("0");
                                }
                                setShowCutoffSelector(val);
                            },
                            label: "Cutoff",
                            disabled: disabledSwitches.includes("cutoff")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 859,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kddept-filter",
                            checked: Boolean(showKementerian),
                            onChange: setShowKementerian,
                            label: "Kementerian"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 881,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "unit-filter",
                            checked: showUnit,
                            onChange: setShowUnit,
                            label: "Eselon I"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 887,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "dekon-filter",
                            checked: showDekon,
                            onChange: setShowDekon,
                            label: "Kewenangan"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 893,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "lokasi-filter",
                            checked: showLokasi,
                            onChange: setShowLokasi,
                            label: "Provinsi"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 899,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kabkota-filter",
                            checked: showKabkota,
                            onChange: setShowKabkota,
                            label: "Kabupaten/Kota"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 905,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kanwil-filter",
                            checked: showKanwil,
                            onChange: setShowKanwil,
                            label: "Kanwil"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 911,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdkppn-filter",
                            checked: kdkppn,
                            onChange: setKdkppn,
                            label: "KPPN"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 918,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdsatker-filter",
                            checked: kdsatker,
                            onChange: setKdsatker,
                            label: "Satker"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 924,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdfungsi-filter",
                            checked: kdfungsi,
                            onChange: setKdfungsi,
                            label: "Fungsi"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 930,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdsfungsi-filter",
                            checked: kdsfungsi,
                            onChange: setKdsfungsi,
                            label: "Sub-fungsi"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 936,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdprogram-filter",
                            checked: kdprogram,
                            onChange: setKdprogram,
                            label: "Program"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 942,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdgiat-filter",
                            checked: kdgiat,
                            onChange: setKdgiat,
                            label: "Kegiatan"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 948,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdoutput-filter",
                            checked: kdoutput,
                            onChange: setKdoutput,
                            label: "Output"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 954,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdsoutput-filter",
                            checked: kdsoutput,
                            onChange: setKdsoutput,
                            label: "Sub-output",
                            disabled: disabledSwitches.includes("kdsoutput")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 960,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdakun-filter",
                            checked: kdakun,
                            onChange: setKdakun,
                            label: "Akun",
                            disabled: disabledSwitches.includes("kdakun")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 967,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdsdana-filter",
                            checked: kdsdana,
                            onChange: setKdsdana,
                            label: "Sumber Dana",
                            disabled: disabledSwitches.includes("kdsdana")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 987,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kdregister-filter",
                            checked: kdregister,
                            onChange: setKdregister,
                            label: "Register",
                            disabled: disabledSwitches.includes("kdregister")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 994,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "prinas-filter",
                            checked: KdPN,
                            onChange: setKdPN,
                            label: "Prioritas Nasional",
                            disabled: disabledSwitches.includes("KdPN")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1001,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "programpri-filter",
                            checked: KdPP,
                            onChange: setKdPP,
                            label: "Program Prioritas",
                            disabled: disabledSwitches.includes("KdPP")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1008,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kegiatanpri-filter",
                            checked: KdKegPP,
                            onChange: setKdKegPP,
                            label: "Kegiatan Prioritas",
                            disabled: disabledSwitches.includes("KdKegPP")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1015,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "proyek-prioritas-filter",
                            checked: KdPRI,
                            onChange: setKdPRI,
                            label: "Proyek Prioritas",
                            disabled: disabledSwitches.includes("KdPRI")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1022,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "majorpr-filter",
                            checked: KdMP,
                            onChange: setKdMP,
                            label: "Major Project",
                            disabled: disabledSwitches.includes("KdMP")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1029,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "tematik-filter",
                            checked: KdTema,
                            onChange: setKdTema,
                            label: "Tematik",
                            disabled: disabledSwitches.includes("KdTema")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1036,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "inflasi-filter",
                            checked: kdInflasi,
                            onChange: setKdInflasi,
                            label: "Inflasi",
                            disabled: disabledSwitches.includes("kdInflasi")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1043,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "stunting-filter",
                            checked: KdStunting,
                            onChange: setKdStunting,
                            label: "Stunting",
                            disabled: disabledSwitches.includes("KdStunting")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1050,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "kemiskinan-filter",
                            checked: kdKemiskinan,
                            onChange: setKdKemiskinan,
                            label: "Kemiskinan Extrem",
                            disabled: disabledSwitches.includes("kdKemiskinan")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1057,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "pemilu-filter",
                            checked: KdPemilu,
                            onChange: setKdPemilu,
                            label: "Pemilu",
                            disabled: disabledSwitches.includes("KdPemilu")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1064,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "ikn-filter",
                            checked: kdIkn,
                            onChange: setKdIkn,
                            label: "IKN",
                            disabled: disabledSwitches.includes("kdIkn")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1071,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSwitch$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            id: "pangan-filter",
                            checked: KdPangan,
                            onChange: setKdPangan,
                            label: "Ketahanan Pangan",
                            disabled: disabledSwitches.includes("KdPangan")
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                            lineNumber: 1078,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                    lineNumber: 852,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                lineNumber: 846,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4 mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$CutoffFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1091,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    showKementerian && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KementerianFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState,
                        status: showKementerian ? "pilihdept" : ""
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1093,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    showUnit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$UnitFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1098,
                        columnNumber: 22
                    }, ("TURBOPACK compile-time value", void 0)),
                    showDekon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$DekonFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1099,
                        columnNumber: 23
                    }, ("TURBOPACK compile-time value", void 0)),
                    showLokasi && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$LokasiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1100,
                        columnNumber: 24
                    }, ("TURBOPACK compile-time value", void 0)),
                    showKabkota && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KabkotaFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1101,
                        columnNumber: 25
                    }, ("TURBOPACK compile-time value", void 0)),
                    showKanwil && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KanwilFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1102,
                        columnNumber: 24
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdkppn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KppnFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1103,
                        columnNumber: 20
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdsatker && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SatkerFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1104,
                        columnNumber: 22
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdfungsi && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$FungsiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1105,
                        columnNumber: 22
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdsfungsi && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SubfungsiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1106,
                        columnNumber: 23
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdprogram && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$ProgramFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1107,
                        columnNumber: 23
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdgiat && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KegiatanFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1108,
                        columnNumber: 20
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdoutput && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$OutputFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        type: "output",
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1109,
                        columnNumber: 22
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdsoutput && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SuboutputFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1110,
                        columnNumber: 23
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdakun && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$AkunFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1111,
                        columnNumber: 20
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdkomponen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KomponenFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1112,
                        columnNumber: 24
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdskomponen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SubkomponenFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1113,
                        columnNumber: 25
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdsdana && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$SumberDanaFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        type: "source",
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1115,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdregister && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$RegisterFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        type: "register",
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1118,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdPN && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$PrinasFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1120,
                        columnNumber: 18
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdPP && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$ProgrampriFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1121,
                        columnNumber: 18
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdKegPP && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KegiatanpriFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1122,
                        columnNumber: 21
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdPRI && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$ProyekpriFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1123,
                        columnNumber: 19
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdMP && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$MajorprFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1124,
                        columnNumber: 18
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdTema && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$TematikFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1125,
                        columnNumber: 20
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdInflasi && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$InflasiFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1126,
                        columnNumber: 23
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdStunting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$StuntingFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1127,
                        columnNumber: 24
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdKemiskinan && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$KemiskinanFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1128,
                        columnNumber: 26
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdPemilu && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$PemiluFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1129,
                        columnNumber: 22
                    }, ("TURBOPACK compile-time value", void 0)),
                    kdIkn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$IknFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1130,
                        columnNumber: 19
                    }, ("TURBOPACK compile-time value", void 0)),
                    KdPangan && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterGroups$2f$PanganFilter$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: inquiryState
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                        lineNumber: 1131,
                        columnNumber: 22
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx",
                lineNumber: 1089,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = FilterSection;
}),
"[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/button/dist/chunk-WBUKVQRU.mjs [app-ssr] (ecmascript) <export button_default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$57V4RE7B$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_group_default__as__ButtonGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/button/dist/chunk-57V4RE7B.mjs [app-ssr] (ecmascript) <export button_group_default as ButtonGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$card$2f$dist$2f$chunk$2d$O24IAYCG$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__card_default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/card/dist/chunk-O24IAYCG.mjs [app-ssr] (ecmascript) <export card_default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$card$2f$dist$2f$chunk$2d$LGSBTEIA$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__card_body_default__as__CardBody$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/card/dist/chunk-LGSBTEIA.mjs [app-ssr] (ecmascript) <export card_body_default as CardBody>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/play.js [app-ssr] (ecmascript) <export default as Play>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-ssr] (ecmascript) <export default as Save>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2d$heart$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircleHeart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-circle-heart.js [app-ssr] (ecmascript) <export default as MessageCircleHeart>");
;
;
;
;
const QueryButtons = ({ onExecuteQuery, onExportExcel, onExportCSV, onExportPDF, onReset, onSaveQuery, onShowSQL, isLoading })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$card$2f$dist$2f$chunk$2d$O24IAYCG$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__card_default__as__Card$3e$__["Card"], {
        className: "mb-4 shadow-none bg-transparent",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$card$2f$dist$2f$chunk$2d$LGSBTEIA$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__card_body_default__as__CardBody$3e$__["CardBody"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-wrap gap-6 justify-center md:justify-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                        color: "primary",
                        startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$play$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Play$3e$__["Play"], {
                            size: 16
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                            lineNumber: 28,
                            columnNumber: 27
                        }, void 0),
                        onClick: onExecuteQuery,
                        isLoading: isLoading,
                        className: "w-[160px] h-[50px]",
                        children: "Tayang Data"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                        lineNumber: 26,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                        color: "danger",
                        variant: "ghost",
                        startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                            size: 16
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                            lineNumber: 38,
                            columnNumber: 27
                        }, void 0),
                        onClick: onReset,
                        isDisabled: isLoading,
                        className: "w-[160px] h-[50px]",
                        children: "Reset Filter"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                        lineNumber: 35,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$57V4RE7B$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_group_default__as__ButtonGroup$3e$__["ButtonGroup"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                                color: "success",
                                variant: "flat",
                                startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                    size: 16
                                }, void 0, false, {
                                    fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                                    lineNumber: 49,
                                    columnNumber: 29
                                }, void 0),
                                onClick: onExportExcel,
                                isDisabled: isLoading,
                                className: "w-[120px] h-[50px]",
                                children: "Excel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                                lineNumber: 46,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                                color: "secondary",
                                variant: "flat",
                                startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                    size: 16
                                }, void 0, false, {
                                    fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                                    lineNumber: 60,
                                    columnNumber: 29
                                }, void 0),
                                onClick: onExportCSV,
                                isDisabled: isLoading,
                                className: "w-[120px] h-[50px]",
                                children: "CSV"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                                lineNumber: 57,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                        lineNumber: 45,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                        color: "success",
                        variant: "flat",
                        startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2d$heart$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircleHeart$3e$__["MessageCircleHeart"], {
                            size: 16
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                            lineNumber: 71,
                            columnNumber: 27
                        }, void 0),
                        onClick: onExportPDF,
                        isDisabled: isLoading,
                        className: "w-[160px] h-[50px]",
                        children: "Kirim WA"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                        lineNumber: 68,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                        color: "warning",
                        variant: "flat",
                        startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                            size: 16
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                            lineNumber: 82,
                            columnNumber: 27
                        }, void 0),
                        onClick: onSaveQuery,
                        isDisabled: isLoading,
                        className: "w-[160px] h-[50px]",
                        children: "Simpan Query"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                        lineNumber: 79,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$button$2f$dist$2f$chunk$2d$WBUKVQRU$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__button_default__as__Button$3e$__["Button"], {
                        color: "default",
                        variant: "flat",
                        startContent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                            size: 16
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                            lineNumber: 93,
                            columnNumber: 27
                        }, void 0),
                        onClick: onShowSQL,
                        isDisabled: isLoading,
                        className: "w-[160px] h-[50px]",
                        children: "Tayang SQL"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                        lineNumber: 90,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
                lineNumber: 25,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
            lineNumber: 24,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx",
        lineNumber: 23,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = QueryButtons;
}),
"[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$select$2f$dist$2f$chunk$2d$Y2AYO5NJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__select_default__as__Select$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/select/dist/chunk-Y2AYO5NJ.mjs [app-ssr] (ecmascript) <export select_default as Select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$listbox$2f$dist$2f$chunk$2d$BJFJ4DRR$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__listbox_item_base_default__as__SelectItem$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroui/listbox/dist/chunk-BJFJ4DRR.mjs [app-ssr] (ecmascript) <export listbox_item_base_default as SelectItem>");
;
;
;
const ReportTypeSelector = ({ inquiryState, onFilterChange })=>{
    // --- DESTRUCTURE STATE FROM INQUIRY STATE ---
    const { thang, setThang, jenlap, setJenlap, pembulatan, setPembulatan, akumulatif, setAkumulatif } = inquiryState || {};
    // --- FALLBACK STATE FOR BACKWARDS COMPATIBILITY ---
    const [localThang, setLocalThang] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState("2025");
    const [localJenlap, setLocalJenlap] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState("2");
    const [localPembulatan, setLocalPembulatan] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState("1");
    const [localAkumulatif, setLocalAkumulatif] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState("0");
    // Use inquiryState values if available, otherwise use local state
    const currentThang = thang !== undefined && thang !== null ? thang : localThang;
    const currentJenlap = jenlap !== undefined && jenlap !== null ? jenlap : localJenlap;
    const currentPembulatan = pembulatan !== undefined && pembulatan !== null ? pembulatan : localPembulatan;
    const currentAkumulatif = akumulatif !== undefined && akumulatif !== null ? akumulatif : localAkumulatif;
    const currentSetThang = setThang || setLocalThang;
    const currentSetJenlap = setJenlap || setLocalJenlap;
    const currentSetPembulatan = setPembulatan || setLocalPembulatan;
    const currentSetAkumulatif = setAkumulatif || setLocalAkumulatif;
    // Debug akumulatif value changes
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        console.log("LaporanSelector - currentAkumulatif changed to:", currentAkumulatif);
    }, [
        currentAkumulatif
    ]);
    // This effect reports changes up to the parent component if callback is provided
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (onFilterChange) {
            onFilterChange({
                thang: currentThang,
                jenlap: currentJenlap,
                pembulatan: currentPembulatan,
                akumulatif: currentAkumulatif
            });
        }
    }, [
        currentThang,
        currentJenlap,
        currentPembulatan,
        currentAkumulatif,
        onFilterChange
    ]);
    // --- LOGIC FOR CONDITIONAL "AKUMULATIF" ---
    const isAkumulatifActive = currentJenlap === "3";
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        console.log("LaporanSelector - akumulatif useEffect triggered:", {
            currentJenlap,
            isAkumulatifActive,
            currentAkumulatif
        });
        if (!isAkumulatifActive) {
            // When akumulatif is not active (jenlap !== "3"), set to "0" (Non-Akumulatif)
            currentSetAkumulatif("0");
        } else {
            // When akumulatif becomes active (jenlap === "3"), ensure it has a valid value
            // If currentAkumulatif is empty or undefined, default to "0" (Non-Akumulatif)
            if (!currentAkumulatif || currentAkumulatif !== "0" && currentAkumulatif !== "1") {
                currentSetAkumulatif("0");
            } else {}
        }
    }, [
        currentJenlap,
        isAkumulatifActive
    ]); // Remove currentSetAkumulatif from dependencies
    // Available years for selection
    const Tahun = [
        "2025",
        "2024",
        "2023",
        "2022",
        "2021",
        "2020",
        "2019",
        "2018",
        "2017",
        "2016"
    ];
    // Report types (match old form)
    const jenlapOpt = [
        {
            value: "1",
            label: "Pagu APBN"
        },
        {
            value: "2",
            label: "Pagu Realisasi"
        },
        {
            value: "3",
            label: "Pagu Realisasi Bulanan"
        },
        {
            value: "4",
            label: "Pergerakan Pagu Bulanan"
        },
        {
            value: "5",
            label: "Pergerakan Blokir Bulanan"
        },
        {
            value: "7",
            label: "Pergerakan Blokir Bulanan per Jenis"
        },
        {
            value: "6",
            label: "Volume Output Kegiatan (PN) - Data Caput"
        }
    ];
    // Akumulatif options
    const akumulatifOpt = [
        {
            value: "1",
            label: "Akumulatif"
        },
        {
            value: "0",
            label: "Non-Akumulatif"
        }
    ];
    // Rounding options
    const pembulatanOpt = [
        {
            value: "1",
            label: "Rupiah"
        },
        {
            value: "1000",
            label: "Ribuan"
        },
        {
            value: "1000000",
            label: "Jutaan"
        },
        {
            value: "1000000000",
            label: "Miliar"
        },
        {
            value: "1000000000000",
            label: "Triliun"
        }
    ];
    const handleSelectionChange = (setter)=>(keys)=>{
            const value = Array.from(keys)[0];
            if (setter && value !== undefined) setter(value);
        };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col md:flex-row gap-6 w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                id: "thang-label",
                                className: "block text-sm font-medium mb-2",
                                children: "Tahun Anggaran"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 149,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$select$2f$dist$2f$chunk$2d$Y2AYO5NJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__select_default__as__Select$3e$__["Select"], {
                                selectedKeys: [
                                    currentThang
                                ],
                                onSelectionChange: handleSelectionChange(currentSetThang),
                                className: "w-full",
                                placeholder: "Pilih Tahun",
                                disallowEmptySelection: true,
                                "aria-labelledby": "thang-label",
                                "aria-label": "Pilih Tahun Anggaran",
                                children: Tahun.map((year)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$listbox$2f$dist$2f$chunk$2d$BJFJ4DRR$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__listbox_item_base_default__as__SelectItem$3e$__["SelectItem"], {
                                        textValue: year,
                                        children: year
                                    }, year, false, {
                                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                        lineNumber: 162,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)))
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 152,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                        lineNumber: 148,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-[1.5]",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                id: "jenlap-label",
                                className: "block text-sm font-medium mb-2",
                                children: "Jenis Laporan"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 171,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$select$2f$dist$2f$chunk$2d$Y2AYO5NJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__select_default__as__Select$3e$__["Select"], {
                                selectedKeys: [
                                    currentJenlap
                                ],
                                onSelectionChange: handleSelectionChange(currentSetJenlap),
                                className: "w-full",
                                placeholder: "Pilih Jenis Laporan",
                                disallowEmptySelection: true,
                                "aria-labelledby": "jenlap-label",
                                "aria-label": "Pilih Jenis Laporan",
                                children: jenlapOpt.map((type)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$listbox$2f$dist$2f$chunk$2d$BJFJ4DRR$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__listbox_item_base_default__as__SelectItem$3e$__["SelectItem"], {
                                        textValue: type.label,
                                        children: type.label
                                    }, type.value, false, {
                                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                        lineNumber: 184,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)))
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 174,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                        lineNumber: 170,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-[0.5] min-w-[120px]",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                id: "akumulatif-label",
                                className: "block text-sm font-medium mb-2",
                                children: "Akumulatif"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$select$2f$dist$2f$chunk$2d$Y2AYO5NJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__select_default__as__Select$3e$__["Select"], {
                                selectedKeys: isAkumulatifActive ? [
                                    currentAkumulatif
                                ] : [],
                                onSelectionChange: handleSelectionChange(currentSetAkumulatif),
                                className: "w-full",
                                placeholder: isAkumulatifActive ? "Pilih Akumulatif" : "Disabled",
                                isDisabled: !isAkumulatifActive,
                                disallowEmptySelection: true,
                                "aria-labelledby": "akumulatif-label",
                                "aria-label": "Pilih Akumulatif",
                                children: akumulatifOpt.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$listbox$2f$dist$2f$chunk$2d$BJFJ4DRR$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__listbox_item_base_default__as__SelectItem$3e$__["SelectItem"], {
                                        textValue: option.label,
                                        children: option.label
                                    }, option.value, false, {
                                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                        lineNumber: 210,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)))
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 199,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                        lineNumber: 192,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                id: "pembulatan-label",
                                className: "block text-sm font-medium mb-2",
                                children: "Pembulatan"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 219,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$select$2f$dist$2f$chunk$2d$Y2AYO5NJ$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__select_default__as__Select$3e$__["Select"], {
                                selectedKeys: [
                                    currentPembulatan
                                ],
                                onSelectionChange: handleSelectionChange(currentSetPembulatan),
                                className: "w-full",
                                placeholder: "Pilih Pembulatan",
                                disallowEmptySelection: true,
                                "aria-labelledby": "pembulatan-label",
                                "aria-label": "Pilih Pembulatan",
                                children: pembulatanOpt.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroui$2f$listbox$2f$dist$2f$chunk$2d$BJFJ4DRR$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__listbox_item_base_default__as__SelectItem$3e$__["SelectItem"], {
                                        textValue: option.label,
                                        children: option.label
                                    }, option.value, false, {
                                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                        lineNumber: 235,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)))
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                                lineNumber: 225,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                        lineNumber: 218,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
                lineNumber: 146,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
            lineNumber: 143,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx",
        lineNumber: 142,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = ReportTypeSelector;
}),

};

//# sourceMappingURL=src_components_features_inquiry_shared_components_6f1e115e._.js.map