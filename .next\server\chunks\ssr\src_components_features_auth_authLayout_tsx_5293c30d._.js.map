{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/auth/authLayout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Image } from \"@heroui/react\";\r\n\r\ninterface Props {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const AuthLayoutWrapper = ({ children }: Props) => {\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      {/* Single section - Login form with background */}\r\n      <div className=\"flex-1 flex-col flex items-center justify-start md:justify-center p-6 overflow-y-auto md:overflow-hidden min-h-screen relative\">\r\n        <div className=\"absolute inset-0 z-0 overflow-hidden\">\r\n          <Image\r\n            className=\"w-full h-full object-cover object-center\"\r\n            src=\"https://nextui.org/gradients/docs-right.png\"\r\n            alt=\"gradient\"\r\n          />\r\n        </div>\r\n        <div className=\"relative z-10 w-full py-8 md:py-0\">{children}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAS;IACnD,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yMAAA,CAAA,QAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;;;;;;;;;;;8BAGR,8OAAC;oBAAI,WAAU;8BAAqC;;;;;;;;;;;;;;;;;AAI5D", "debugId": null}}]}