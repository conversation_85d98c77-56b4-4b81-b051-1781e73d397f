{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport const createAuthCookie = async (nama: string, nilai: string) => {\r\n  const cookieStore = await cookies();\r\n  const isProduction = process.env.NODE_ENV === \"production\";\r\n\r\n  cookieStore.set(\"userAuth\", \"myToken\", {\r\n    secure: isProduction,\r\n    httpOnly: false,\r\n    sameSite: \"lax\",\r\n  });\r\n  cookieStore.set(nama, nilai, {\r\n    secure: isProduction,\r\n    httpOnly: false,\r\n    sameSite: \"lax\",\r\n  });\r\n};\r\n\r\nexport const deleteAuthCookie = async () => {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(\"userAuth\");\r\n  cookieStore.delete(\"token\");\r\n  cookieStore.delete(\"nextToken\");\r\n};\r\n\r\nexport const checkAuthStatus = async (): Promise<{\r\n  userAuth: boolean;\r\n  token: boolean;\r\n  accessToken: boolean;\r\n  nextToken: boolean;\r\n}> => {\r\n  const cookieStore = await cookies();\r\n\r\n  return {\r\n    userAuth: cookieStore.has(\"userAuth\"),\r\n    token: cookieStore.has(\"token\"),\r\n    accessToken: cookieStore.has(\"accessToken\"),\r\n    nextToken: cookieStore.has(\"nextToken\"),\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;IAIa,mBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/validation/schemas.ts"], "sourcesContent": ["import { object, ref, string } from \"yup\";\r\n\r\nexport const LoginSchema = object().shape({\r\n  username: string().required(\"Username is required\"),\r\n  password: string().required(\"Password is required\"),\r\n  captcha: string().when(\"$chap\", {\r\n    is: \"0\",\r\n    then: (schema) => schema.required(\"Captcha is required\"),\r\n    otherwise: (schema) => schema.notRequired(),\r\n  }),\r\n});\r\n\r\nexport const RegisterSchema = object().shape({\r\n  name: string().required(\"Name is required\"),\r\n  email: string()\r\n    .email(\"This field must be an email\")\r\n    .required(\"Email is required\"),\r\n  password: string().required(\"Password is required\"),\r\n  confirmPassword: string()\r\n    .required(\"Confirm password is required\")\r\n    .oneOf([ref(\"password\")], \"Passwords must match\"),\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,KAAK,CAAC;IACxC,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,QAAQ,CAAC;IAC5B,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,QAAQ,CAAC;IAC5B,SAAS,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,IAAI,CAAC,SAAS;QAC9B,IAAI;QACJ,MAAM,CAAC,SAAW,OAAO,QAAQ,CAAC;QAClC,WAAW,CAAC,SAAW,OAAO,WAAW;IAC3C;AACF;AAEO,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,KAAK,CAAC;IAC3C,MAAM,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,QAAQ,CAAC;IACxB,OAAO,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IACT,KAAK,CAAC,+BACN,QAAQ,CAAC;IACZ,UAAU,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IAAI,QAAQ,CAAC;IAC5B,iBAAiB,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,IACnB,QAAQ,CAAC,gCACT,KAAK,CAAC;QAAC,CAAA,GAAA,sIAAA,CAAA,MAAG,AAAD,EAAE;KAAY,EAAE;AAC9B", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/logo/snext_dark.svg.js"], "sourcesContent": ["var _defs, _g;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nvar SvgSnextDark = function SvgSnextDark(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    id: \"snext_dark_svg__Layer_2\",\n    \"data-name\": \"Layer 2\",\n    viewBox: \"0 0 3399.57 600\"\n  }, props), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"style\", null, \".snext_dark_svg__cls-1{fill:#0f52ba}\"))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    id: \"snext_dark_svg__Layer_1-2\",\n    \"data-name\": \"Layer 1\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M525 150c-41.46 0-75-33.54-75-75S416.42 0 375 0H75C33.58 0 0 33.58 0 75v300c0 41.42 33.58 75 75 75s75 33.54 75 75 33.58 75 75 75h300c41.42 0 75-33.58 75-75V225c0-41.42-33.58-75-75-75m-75 37.5V375c0 41.42-33.58 75-75 75H112.5c20.71 0 37.5-16.79 37.5-37.5V225c0-41.42 33.58-75 75-75h262.5c-20.71 0-37.5 16.79-37.5 37.5M1031.72 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68L935.94 468c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08 26.22-22.17 63.94-33.25 113.14-33.25 24.86 0 47.71 3.65 68.54 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM1313.84 108.26c-14.88 0-27.69-5.37-38.43-16.12-10.74-10.74-16.12-23.55-16.12-38.43s5.37-27.54 16.12-38.02c10.74-10.46 23.55-15.7 38.43-15.7s27.69 5.24 38.43 15.7c10.74 10.47 16.12 23.14 16.12 38.02s-5.37 27.69-16.12 38.43c-10.74 10.74-23.55 16.12-38.43 16.12m-46.28 486.66V191.86a5.08 5.08 0 0 1 5.08-5.08h81.57a5.08 5.08 0 0 1 5.08 5.08v403.06a5.08 5.08 0 0 1-5.08 5.08h-81.57a5.08 5.08 0 0 1-5.08-5.08M1462.03 594.9V190.07c0-2.82 2.29-5.1 5.1-5.1h77.3c2.68 0 4.9 2.07 5.09 4.75l2.77 39.27c.33 4.74 6.39 6.48 9.21 2.65 11.96-16.25 26.53-29.18 43.72-38.78 21.3-11.89 45.78-17.85 73.46-17.85q49.8 0 85.08 19.92c23.51 13.28 41.5 33.9 53.96 61.84 12.45 27.95 18.68 64.33 18.68 109.16v228.98c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1V387.52c0-33.2-3.73-58.79-11.21-76.78-7.47-17.98-17.99-30.57-31.54-37.77-13.57-7.19-29.47-10.79-47.73-10.79-31-.54-55.07 9.69-72.22 30.71-17.16 21.04-25.73 51.19-25.73 90.48v211.55c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1ZM2086.2 600c-42.82 0-75.48-10.7-97.97-32.11-22.5-21.41-33.74-52.16-33.74-92.28V266.79c0-2.76-2.24-5-5-5h-57.48c-2.76 0-5-2.24-5-5v-68.05c0-2.76 2.24-5 5-5h57.48c2.76 0 5-2.24 5-5V61.91c0-2.76 2.24-5 5-5h80.24c2.76 0 5 2.24 5 5v116.83c0 2.76 2.24 5 5 5h99.76c2.76 0 5 2.24 5 5v68.05c0 2.76-2.24 5-5 5h-99.76c-2.76 0-5 2.24-5 5v191.75c0 18.98 4.47 33.33 13.41 43.09s22.08 14.63 39.43 14.63c5.41 0 11.38-1.08 17.89-3.25 5.21-1.73 10.95-4.69 17.21-8.86 2.53-1.69 5.98-.78 7.3 1.97l28.9 59.92c1.09 2.26.33 4.97-1.76 6.36-12.89 8.56-25.76 15.19-38.64 19.89-14.1 5.14-28.19 7.72-42.28 7.72ZM2420.33 600c-39.48 0-74.49-9.06-105.03-27.17-30.55-18.11-54.62-43.11-72.19-75.02-17.58-31.9-26.36-68.66-26.36-110.31s8.92-78.4 26.76-110.31c17.84-31.9 42.3-56.9 73.4-75.02C2348 184.06 2383.54 175 2423.57 175c36.22 0 68.94 9.33 98.14 27.98s52.31 45.56 69.35 80.7c16.44 33.93 24.94 74.13 25.52 120.64.03 2.79-2.2 5.07-4.99 5.07h-295.26c-2.97 0-5.3 2.58-4.96 5.53 3.57 31.44 15.97 56.37 37.18 74.77 22.43 19.47 49.07 29.2 79.89 29.2 24.86 0 45.42-5.54 61.64-16.63 15.01-10.25 27.01-23.41 36.01-39.46a4.98 4.98 0 0 1 6.29-2.16l71.06 30.15c2.74 1.16 3.86 4.44 2.41 7.04-10.93 19.61-24.52 36.9-40.75 51.88-17.58 16.22-38.4 28.67-62.45 37.31-24.07 8.64-51.5 12.98-82.32 12.98m-98.11-260.35h190.7c2.99 0 5.33-2.61 4.96-5.57-2.18-16.98-7.66-31.21-16.45-42.69-9.73-12.7-21.63-22.3-35.69-28.79-14.07-6.49-28.93-9.73-44.61-9.73s-30.42 3.12-45.83 9.33c-15.41 6.22-28.53 15.82-39.34 28.79-9.73 11.67-15.96 25.96-18.68 42.87a4.99 4.99 0 0 0 4.93 5.79ZM2834.96 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68l69.95-31.01c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08C2751.28 186.08 2789 175 2838.2 175c24.86 0 47.71 3.65 68.53 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM3196.81 600c-48.66 0-86.38-10.81-113.14-32.44-26.76-21.62-40.15-52.17-40.15-91.65 0-42.18 14.19-74.34 42.58-96.52 28.39-22.16 67.99-33.25 118.82-33.25h97.37c3.01 0 5.35-2.66 4.95-5.64-3.65-26.98-11.81-47.81-24.45-62.49-13.52-15.68-33.53-23.52-60.02-23.52-19.47 0-36.5 4.06-51.1 12.17-13.62 7.57-25.37 19.14-35.22 34.71a4.97 4.97 0 0 1-5.92 2.01l-70.4-25.86c-2.82-1.04-4.1-4.32-2.71-6.99 8.31-15.94 18.96-30.88 31.93-44.82 14.32-15.41 32.44-27.7 54.34-36.9 21.9-9.19 48.26-13.79 79.08-13.79 39.46 0 72.45 7.71 98.95 23.12 26.49 15.41 46.09 37.45 58.8 66.1 12.7 28.67 19.06 63.26 19.06 103.82l-2.38 217.3a4.985 4.985 0 0 1-4.99 4.93h-73.86c-2.66 0-4.85-2.09-4.98-4.74l-1.45-29.5c-.23-4.66-6.15-6.47-8.96-2.75-9.8 12.99-22.07 23.43-36.8 31.31-19.2 10.27-42.31 15.41-69.35 15.41Zm11.35-76.24q30 0 53.13-13.38c23.13-13.38 27.3-20.95 35.69-36.09 8.38-15.13 12.57-31.9 12.57-50.29v-3.12c0-2.75-2.23-4.99-4.99-4.99h-70.44c-36.23 0-61.64 5.01-76.24 15-14.6 10.01-21.9 24.21-21.9 42.58 0 15.69 6.35 27.98 19.06 36.9 12.7 8.92 30.42 13.38 53.12 13.38Z\",\n    className: \"snext_dark_svg__cls-1\"\n  }))));\n};\nexport default SvgSnextDark;"], "names": [], "mappings": ";;;AAEA;AAFA,IAAI,OAAO;AACX,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;AAEnR,IAAI,eAAe,SAAS,aAAa,KAAK;IAC5C,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,SAAS;QACtD,OAAO;QACP,IAAI;QACJ,aAAa;QACb,SAAS;IACX,GAAG,QAAQ,SAAS,CAAC,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,MAAM,wCAAwC,GAAG,MAAM,CAAC,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,KAAK;QACvN,IAAI;QACJ,aAAa;IACf,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC1C,GAAG;QACH,WAAW;IACb,GAAG;AACL;KAbI;uCAcW", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/auth/PageTransitionSuccess.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Card, CardBody } from \"@heroui/react\";\r\nimport { CheckCircle2, ArrowRight } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\ninterface PageTransitionSuccessProps {\r\n  isVisible: boolean;\r\n  onComplete?: () => void;\r\n  duration?: number;\r\n}\r\n\r\nexport const PageTransitionSuccess = ({\r\n  isVisible,\r\n  onComplete,\r\n  duration = 2500,\r\n}: PageTransitionSuccessProps) => {\r\n  const [phase, setPhase] = useState<\"hidden\" | \"showing\" | \"transitioning\">(\r\n    \"hidden\"\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (isVisible) {\r\n      setPhase(\"showing\");\r\n\r\n      // Start transition phase\r\n      const transitionTimer = setTimeout(() => {\r\n        setPhase(\"transitioning\");\r\n      }, 1500);\r\n\r\n      // Complete animation\r\n      const completeTimer = setTimeout(() => {\r\n        onComplete?.();\r\n      }, duration);\r\n\r\n      return () => {\r\n        clearTimeout(transitionTimer);\r\n        clearTimeout(completeTimer);\r\n      };\r\n    } else {\r\n      setPhase(\"hidden\");\r\n    }\r\n  }, [isVisible, onComplete, duration]);\r\n\r\n  if (phase === \"hidden\") return null;\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key=\"success-overlay\"\r\n        className=\"fixed inset-0 z-50 flex items-center justify-center\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n      >\r\n        {/* Background with gradient animation */}\r\n        <motion.div\r\n          className=\"absolute inset-0 bg-gradient-to-br from-primary-500/20 via-secondary-500/20 to-success-500/20\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.8 }}\r\n        />\r\n\r\n        {/* Animated background blur */}\r\n        <motion.div\r\n          className=\"absolute inset-0 backdrop-blur-lg\"\r\n          initial={{ backdropFilter: \"blur(0px)\" }}\r\n          animate={{ backdropFilter: \"blur(20px)\" }}\r\n          transition={{ duration: 0.6 }}\r\n        />\r\n\r\n        {/* Success content */}\r\n        <AnimatePresence mode=\"wait\">\r\n          {phase === \"showing\" && (\r\n            <motion.div\r\n              key=\"success-content\"\r\n              initial={{ scale: 0.5, opacity: 0, y: 50 }}\r\n              animate={{ scale: 1, opacity: 1, y: 0 }}\r\n              exit={{ scale: 1.1, opacity: 0, y: -20 }}\r\n              transition={{\r\n                type: \"spring\",\r\n                damping: 20,\r\n                stiffness: 300,\r\n                duration: 0.6,\r\n              }}\r\n              className=\"relative z-10\"\r\n            >\r\n              <Card className=\"w-96 bg-zinc-100/90 dark:bg-zinc-900/90 backdrop-blur-xl shadow-2xl border border-white/30\">\r\n                <CardBody className=\"flex flex-col items-center justify-center p-10 text-center\">\r\n                  {/* Large animated checkmark */}\r\n                  <motion.div\r\n                    className=\"relative mb-6\"\r\n                    initial={{ scale: 0 }}\r\n                    animate={{ scale: 1 }}\r\n                    transition={{\r\n                      delay: 0.2,\r\n                      type: \"spring\",\r\n                      damping: 12,\r\n                      stiffness: 150,\r\n                    }}\r\n                  >\r\n                    <motion.div\r\n                      className=\"w-20 h-20 bg-gradient-to-br from-success-400 to-success-600 rounded-full flex items-center justify-center shadow-lg\"\r\n                      initial={{ rotate: -180 }}\r\n                      animate={{ rotate: 0 }}\r\n                      transition={{\r\n                        delay: 0.3,\r\n                        duration: 0.8,\r\n                        ease: \"easeOut\",\r\n                      }}\r\n                    >\r\n                      <motion.div\r\n                        initial={{ scale: 0 }}\r\n                        animate={{ scale: 1 }}\r\n                        transition={{ delay: 0.6, duration: 0.4 }}\r\n                      >\r\n                        <CheckCircle2\r\n                          size={40}\r\n                          className=\"text-white\"\r\n                          strokeWidth={2.5}\r\n                        />\r\n                      </motion.div>\r\n                    </motion.div>\r\n\r\n                    {/* Expanding rings */}\r\n                    {[...Array(3)].map((_, i) => (\r\n                      <motion.div\r\n                        key={i}\r\n                        className=\"absolute inset-0 w-20 h-20 border-2 border-success/40 rounded-full\"\r\n                        initial={{ scale: 1, opacity: 0.8 }}\r\n                        animate={{ scale: 2 + i * 0.5, opacity: 0 }}\r\n                        transition={{\r\n                          delay: 0.8 + i * 0.2,\r\n                          duration: 1.5,\r\n                          ease: \"easeOut\",\r\n                        }}\r\n                      />\r\n                    ))}\r\n                  </motion.div>\r\n\r\n                  {/* Success text */}\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.8, duration: 0.5 }}\r\n                    className=\"space-y-3 mb-6\"\r\n                  >\r\n                    <h2 className=\"text-3xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent\">\r\n                      Login Berhasil!\r\n                    </h2>\r\n                    <p className=\"text-foreground/70 text-base\">\r\n                      Sedang mengkonfigurasi sistem...\r\n                    </p>\r\n                  </motion.div>\r\n\r\n                  {/* Progress bar */}\r\n                  <motion.div\r\n                    className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden\"\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    transition={{ delay: 1 }}\r\n                  >\r\n                    <motion.div\r\n                      className=\"h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full\"\r\n                      initial={{ width: \"0%\" }}\r\n                      animate={{ width: \"100%\" }}\r\n                      transition={{\r\n                        delay: 1.1,\r\n                        duration: 1.2,\r\n                        ease: \"easeInOut\",\r\n                      }}\r\n                    />\r\n                  </motion.div>\r\n                </CardBody>\r\n              </Card>\r\n            </motion.div>\r\n          )}\r\n\r\n          {phase === \"transitioning\" && (\r\n            <motion.div\r\n              key=\"transition-content\"\r\n              initial={{ scale: 0.8, opacity: 0 }}\r\n              animate={{ scale: 1, opacity: 1 }}\r\n              transition={{ duration: 0.4 }}\r\n              className=\"flex items-center space-x-3 text-white\"\r\n            >\r\n              {/* <motion.div\r\n                animate={{ x: [0, 10, 0] }}\r\n                transition={{ duration: 1, repeat: Infinity }}\r\n              >\r\n                <ArrowRight size={32} className=\"text-primary-400\" />\r\n              </motion.div> */}\r\n              <span className=\"text-xl font-medium\">\r\n                Welcome to sintesaNEXT\r\n              </span>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n\r\n        {/* Floating particles for extra polish */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n          {[...Array(15)].map((_, i) => (\r\n            <motion.div\r\n              key={i}\r\n              className=\"absolute w-1 h-1 bg-primary-400/60 rounded-full\"\r\n              style={{\r\n                left: `${Math.random() * 100}%`,\r\n                top: `${Math.random() * 100}%`,\r\n              }}\r\n              initial={{ scale: 0, opacity: 0 }}\r\n              animate={{\r\n                scale: [0, 1, 0],\r\n                opacity: [0, 1, 0],\r\n                y: [0, -50, -100],\r\n              }}\r\n              transition={{\r\n                duration: 3,\r\n                delay: Math.random() * 2,\r\n                repeat: Infinity,\r\n                ease: \"easeOut\",\r\n              }}\r\n            />\r\n          ))}\r\n        </div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAaO,MAAM,wBAAwB;QAAC,EACpC,SAAS,EACT,UAAU,EACV,WAAW,IAAI,EACY;;IAC3B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/B;IAGF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,WAAW;gBACb,SAAS;gBAET,yBAAyB;gBACzB,MAAM,kBAAkB;uEAAW;wBACjC,SAAS;oBACX;sEAAG;gBAEH,qBAAqB;gBACrB,MAAM,gBAAgB;qEAAW;wBAC/B,uBAAA,iCAAA;oBACF;oEAAG;gBAEH;uDAAO;wBACL,aAAa;wBACb,aAAa;oBACf;;YACF,OAAO;gBACL,SAAS;YACX;QACF;0CAAG;QAAC;QAAW;QAAY;KAAS;IAEpC,IAAI,UAAU,UAAU,OAAO;IAE/B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;;8BAGnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;oBAAI;;;;;;8BAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,gBAAgB;oBAAY;oBACvC,SAAS;wBAAE,gBAAgB;oBAAa;oBACxC,YAAY;wBAAE,UAAU;oBAAI;;;;;;8BAI9B,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,UAAU,2BACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,OAAO;gCAAK,SAAS;gCAAG,GAAG;4BAAG;4BACzC,SAAS;gCAAE,OAAO;gCAAG,SAAS;gCAAG,GAAG;4BAAE;4BACtC,MAAM;gCAAE,OAAO;gCAAK,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BACvC,YAAY;gCACV,MAAM;gCACN,SAAS;gCACT,WAAW;gCACX,UAAU;4BACZ;4BACA,WAAU;sCAEV,cAAA,6LAAC,yMAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDACV,OAAO;gDACP,MAAM;gDACN,SAAS;gDACT,WAAW;4CACb;;8DAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,QAAQ,CAAC;oDAAI;oDACxB,SAAS;wDAAE,QAAQ;oDAAE;oDACrB,YAAY;wDACV,OAAO;wDACP,UAAU;wDACV,MAAM;oDACR;8DAEA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,OAAO;4DAAK,UAAU;wDAAI;kEAExC,cAAA,6LAAC,wNAAA,CAAA,eAAY;4DACX,MAAM;4DACN,WAAU;4DACV,aAAa;;;;;;;;;;;;;;;;gDAMlB;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,WAAU;wDACV,SAAS;4DAAE,OAAO;4DAAG,SAAS;wDAAI;wDAClC,SAAS;4DAAE,OAAO,IAAI,IAAI;4DAAK,SAAS;wDAAE;wDAC1C,YAAY;4DACV,OAAO,MAAM,IAAI;4DACjB,UAAU;4DACV,MAAM;wDACR;uDARK;;;;;;;;;;;sDAcX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;8DAAsG;;;;;;8DAGpH,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;sDAM9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,YAAY;gDAAE,OAAO;4CAAE;sDAEvB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAK;gDACvB,SAAS;oDAAE,OAAO;gDAAO;gDACzB,YAAY;oDACV,OAAO;oDACP,UAAU;oDACV,MAAM;gDACR;;;;;;;;;;;;;;;;;;;;;;2BA/FJ;;;;;wBAuGP,UAAU,iCACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAQV,cAAA,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;2BAZlC;;;;;;;;;;;8BAoBV,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;gCAC7B,KAAK,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC9B;4BACA,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,SAAS;gCACP,OAAO;oCAAC;oCAAG;oCAAG;iCAAE;gCAChB,SAAS;oCAAC;oCAAG;oCAAG;iCAAE;gCAClB,GAAG;oCAAC;oCAAG,CAAC;oCAAI,CAAC;iCAAI;4BACnB;4BACA,YAAY;gCACV,UAAU;gCACV,OAAO,KAAK,MAAM,KAAK;gCACvB,QAAQ;gCACR,MAAM;4BACR;2BAjBK;;;;;;;;;;;WA1JP;;;;;;;;;;AAkLZ;GAvNa;KAAA", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/auth/login.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createAuth<PERSON><PERSON>ie } from \"@/actions/auth.action\";\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport { LoginSchema } from \"@/lib/validation/schemas\";\r\nimport { <PERSON><PERSON>, Card, CardBody, Divider, Input } from \"@heroui/react\";\r\nimport axios from \"axios\";\r\nimport { Formik } from \"formik\";\r\nimport { Copyright } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useCallback, useContext, useEffect, useState } from \"react\";\r\nimport SintesaLogoDark from \"../../layout/icons/logo/snext_dark.svg\";\r\n\r\nimport { decryptData } from \"@/lib/encryption/decrypt\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport { jwtDecode } from \"jwt-decode\";\r\nimport { PageTransitionSuccess } from \"./PageTransitionSuccess\";\r\n\r\nexport const Login = () => {\r\n  const context = useContext(MyContext);\r\n\r\n  if (!context) {\r\n    throw new Error(\"Login must be used within MyContextProvider\");\r\n  }\r\n\r\n  const {\r\n    setVerified,\r\n    setNmrole,\r\n    setRole,\r\n    setName,\r\n    setActive,\r\n    setKdlokasi,\r\n    setKdkanwil,\r\n    setDeptlimit,\r\n    setKdkppn,\r\n    setExpire,\r\n    setToken,\r\n    setIduser,\r\n    setUrl,\r\n    setstatusLogin,\r\n    setUsername,\r\n    setMode,\r\n    setTampil,\r\n    setTampilverify,\r\n    setStatus,\r\n    setPersentase,\r\n    setSession,\r\n    setNamelogin,\r\n    setLoggedInUser2,\r\n    setLoggedinUsers,\r\n    telp,\r\n    setTelp,\r\n    offline,\r\n    setOffline,\r\n    offlinest,\r\n    setOfflinest,\r\n  } = context;\r\n\r\n  const router = useRouter();\r\n  const { showToast } = useToast();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isPinLoading, setIsPinLoading] = useState(false);\r\n  const [pinValue, setPinValue] = useState(\"\");\r\n  const [chap, setChap] = useState(\"0\");\r\n  const [captcha, setCaptcha] = useState(\"\");\r\n  const [recaptchaValue, setRecaptchaValue] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const resetState = () => {\r\n    setName(\"\");\r\n    setRole(\"\");\r\n    setNmrole(\"\");\r\n    setVerified(\"\");\r\n    setActive(\"\");\r\n    setKdlokasi(\"\");\r\n    setKdkanwil(\"\");\r\n\r\n    setDeptlimit(\"\");\r\n    setKdkppn(\"\");\r\n    setExpire(\"\");\r\n    setToken(\"\");\r\n    setIduser(\"\");\r\n    setUrl(\"\");\r\n    setstatusLogin(\"\");\r\n    setUsername(\"\");\r\n    setMode(\"\");\r\n    setTampil(\"\");\r\n    setTampilverify(\"\");\r\n    setStatus(\"\");\r\n    setPersentase(\"\");\r\n    setSession(\"\");\r\n    setNamelogin(\"\");\r\n    setLoggedInUser2(\"\");\r\n    setLoggedinUsers(\"\");\r\n  };\r\n\r\n  const initialValues = {\r\n    username: \"\",\r\n    password: \"\",\r\n    captcha: \"\",\r\n  };\r\n\r\n  // Generate captcha for chap === \"0\" mode\r\n  const generateCaptcha = useCallback(() => {\r\n    const randomNum = Math.floor(Math.random() * 9000) + 1000;\r\n    const captchaString = randomNum.toString();\r\n    const formattedCaptcha = insertRandomSpaces(captchaString);\r\n    setCaptcha(formattedCaptcha);\r\n  }, []);\r\n\r\n  const insertRandomSpaces = (input) => {\r\n    const numberOfSpaces = Math.floor(Math.random() * (input.length - 1)) + 1;\r\n    let output = \"\";\r\n    for (let i = 0; i < input.length; i++) {\r\n      output += input[i];\r\n      if (i < input.length - 1 && i < numberOfSpaces) {\r\n        output += \" \";\r\n      }\r\n    }\r\n    return output;\r\n  };\r\n\r\n  // // Check captcha mode from backend\r\n  // const cekMode = async () => {\r\n  //   try {\r\n  //     const response = await axios.get(\r\n  //       process.env.NEXT_PUBLIC_LOCAL_CEKMODE || \"\"\r\n  //     );\r\n  //     setChap(response.data.capcay);\r\n  //     console.log(response.data.capcay);\r\n  //   } catch (error) {\r\n  //     console.log(error);\r\n  //     setError(\"Captcha mode check failed\");\r\n  //     setChap(\"0\");\r\n  //     setOffline(true);\r\n  //     window.location.href = \"/v3/next/offline\";\r\n  //     showToast(\"Mode Offline\", \"error\");\r\n  //   }\r\n  // };\r\n\r\n  // useEffect(() => {\r\n  //   cekMode();\r\n  // }, []);\r\n\r\n  useEffect(() => {\r\n    if (chap === \"0\") {\r\n      generateCaptcha();\r\n\r\n      const interval = setInterval(() => {\r\n        generateCaptcha();\r\n      }, 20000);\r\n\r\n      return () => {\r\n        clearInterval(interval);\r\n      };\r\n    }\r\n  }, [chap, generateCaptcha]);\r\n\r\n  const handleLogin = useCallback(\r\n    async (values) => {\r\n      // Validate captcha first\r\n      if (chap === \"1\" && recaptchaValue === \"\") {\r\n        setError(\"Captcha belum Diverifikasi\");\r\n        showToast(\"Please complete the captcha verification.\", \"warning\");\r\n        return;\r\n      } else if (chap === \"0\") {\r\n        const cleanedCaptcha = values.captcha?.replace(/\\s/g, \"\") || \"\";\r\n        if (cleanedCaptcha !== captcha.replace(/\\s/g, \"\")) {\r\n          showToast(\"Captcha code is incorrect.\", \"error\");\r\n          return;\r\n        }\r\n      } else if (chap === \"\") {\r\n        setError(\"Captcha Error\");\r\n        showToast(\"Captcha system error.\", \"error\");\r\n        return;\r\n      }\r\n\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        const response = await axios.post(\r\n          process.env.NEXT_PUBLIC_LOCAL_LOGIN ?? \"\",\r\n          values,\r\n          { withCredentials: true }\r\n        );\r\n        const data = response.data;\r\n\r\n        if (!data.success) {\r\n          if (data.msg === \"Password Anda Tidak Sesuai\") {\r\n            setError(\"Password Anda Tidak Sesuai\");\r\n            showToast(\"Password Anda Tidak Sesuai\", \"error\");\r\n          } else if (data.msg === \"User tidak ditemukan\") {\r\n            setError(\"User tidak ditemukan\");\r\n            showToast(\"User Tidak Ditemukan\", \"error\");\r\n          } else {\r\n            setError(\"Terjadi kesalahan saat login\");\r\n            showToast(\"Terjadi Kesalahan saat login\", \"error\");\r\n          }\r\n          setLoading(false);\r\n        } else {\r\n          resetState();\r\n\r\n          const decrypted = decryptData(data.data.token);\r\n          const decoded = jwtDecode(decrypted);\r\n          console.log(data.data.token);\r\n          setTelp(decoded.telp);\r\n          setToken(data.data.token);\r\n          setstatusLogin(true);\r\n          setLoading(false);\r\n          setName(decoded.name);\r\n          setExpire(decoded.exp.toString());\r\n          setRole(decoded.role);\r\n          setKdkanwil(decoded.kdkanwil);\r\n          setKdkppn(decoded.kdkppn);\r\n          setKdlokasi(decoded.kdlokasi);\r\n          setActive(decoded.active);\r\n          setDeptlimit(decoded.dept_limit);\r\n          setNmrole(decoded.namarole);\r\n          setIduser(decoded.userId);\r\n          setUrl(decoded.url);\r\n          setUsername(decoded.username);\r\n          setMode(decoded.mode);\r\n          setTampil(decoded.tampil);\r\n          setTampilverify(decoded.tampilverify);\r\n          setSession(decoded.session);\r\n          setVerified(decoded.verified);\r\n\r\n          setShowSuccessAnimation(true);\r\n          localStorage.setItem(\"status\", \"true\");\r\n          localStorage.setItem(\"token\", data.data.token); // Persist token for refresh\r\n          await createAuthCookie(\"token\", data.data.token); // Gunakan token sebenarnya\r\n        }\r\n      } catch (error) {\r\n        console.log(error);\r\n\r\n        showToast(\"Login Gagal\", \"error\");\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    },\r\n    [router, showToast, chap, recaptchaValue, captcha]\r\n  );\r\n\r\n  const handlePinLogin = useCallback(async () => {\r\n    if (!pinValue) {\r\n      showToast(\"Please enter your 6-digit PIN.\", \"warning\");\r\n      return;\r\n    }\r\n\r\n    if (pinValue.length !== 6) {\r\n      showToast(\"PIN must be exactly 6 digits.\", \"warning\");\r\n      return;\r\n    }\r\n\r\n    setIsPinLoading(true);\r\n\r\n    // Temporary hardcoded PIN for testing\r\n    const TEMP_PIN = \"123456\";\r\n\r\n    try {\r\n      // Check if PIN is correct\r\n      if (pinValue !== TEMP_PIN) {\r\n        showToast(\"Invalid PIN. Please try again.\", \"error\");\r\n        setPinValue(\"\");\r\n        return;\r\n      }\r\n\r\n      // PIN is correct, proceed with login\r\n      setShowSuccessAnimation(true);\r\n      localStorage.setItem(\"status\", \"true\");\r\n      // Untuk PIN login, kita buat token dummy atau gunakan sistem lain\r\n      await createAuthCookie(\"token\", \"pin_authenticated\");\r\n    } catch (error) {\r\n      showToast(\"PIN login failed. Please try again.\", \"error\");\r\n    } finally {\r\n      setIsPinLoading(false);\r\n    }\r\n  }, [pinValue, showToast]);\r\n\r\n  const handleSuccessAnimationComplete = useCallback(() => {\r\n    setShowSuccessAnimation(false);\r\n    router.replace(\"/\");\r\n  }, [router]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center w-full\">\r\n      <PageTransitionSuccess\r\n        isVisible={showSuccessAnimation}\r\n        onComplete={handleSuccessAnimationComplete}\r\n        duration={2500}\r\n      />\r\n\r\n      <div className=\"text-center text-[25px] font-bold mb-8\">\r\n        <SintesaLogoDark className=\"h-8 w-auto\" />\r\n      </div>\r\n\r\n      <Formik\r\n        initialValues={initialValues}\r\n        validationSchema={LoginSchema}\r\n        context={{ chap }}\r\n        onSubmit={handleLogin}\r\n      >\r\n        {({ values, errors, touched, handleChange, handleSubmit }) => (\r\n          <>\r\n            <div className=\"flex flex-col w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm gap-4 mb-4\">\r\n              <Input\r\n                autoFocus\r\n                variant=\"bordered\"\r\n                label=\"Username\"\r\n                type=\"text\"\r\n                value={values.username}\r\n                isInvalid={!!errors.username && !!touched.username}\r\n                errorMessage={errors.username}\r\n                onChange={handleChange(\"username\")}\r\n              />\r\n              <Input\r\n                variant=\"bordered\"\r\n                label=\"Password\"\r\n                type=\"password\"\r\n                value={values.password}\r\n                isInvalid={!!errors.password && !!touched.password}\r\n                errorMessage={errors.password}\r\n                onChange={handleChange(\"password\")}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === \"Enter\") {\r\n                    handleSubmit();\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            {chap === \"0\" && (\r\n              <div className=\"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm mb-4\">\r\n                <div className=\"flex flex-col sm:flex-row gap-3\">\r\n                  <Card className=\"flex-1 min-w-0 flex items-center justify-center h-12 sm:h-14 bg-gray-200 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600\">\r\n                    <CardBody className=\"p-2 flex items-center justify-center\">\r\n                      <div className=\"text-lg sm:text-2xl font-bold text-center text-slate-600 dark:text-white tracking-wider break-all\">\r\n                        {captcha}\r\n                      </div>\r\n                    </CardBody>\r\n                  </Card>\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <Input\r\n                      variant=\"bordered\"\r\n                      label=\"Kode Captcha\"\r\n                      type=\"text\"\r\n                      maxLength={4}\r\n                      value={values.captcha || \"\"}\r\n                      onChange={(e) => {\r\n                        // Only allow numbers\r\n                        const numericValue = e.target.value.replace(/\\D/g, \"\");\r\n                        // Create a new event with numeric value\r\n                        const syntheticEvent = {\r\n                          ...e,\r\n                          target: {\r\n                            ...e.target,\r\n                            value: numericValue,\r\n                          },\r\n                        };\r\n                        handleChange(\"captcha\")(syntheticEvent);\r\n                      }}\r\n                      onKeyDown={(e) => {\r\n                        if (e.key === \"Enter\") {\r\n                          handleSubmit();\r\n                        }\r\n                      }}\r\n                      isInvalid={!!errors.captcha && !!touched.captcha}\r\n                      errorMessage={errors.captcha}\r\n                      className=\"w-full\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {chap === \"1\" && (\r\n              <div className=\"flex w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm justify-center mb-4\">\r\n                {/* <ReCAPTCHA\r\n                  sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || \"\"}\r\n                  onChange={handleRecaptchaChange}\r\n                /> */}\r\n              </div>\r\n            )}\r\n\r\n            <Button\r\n              className=\"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm min-h-[36px] h-10 flex-shrink-0 login-button mt-2 font-semibold overflow-hidden\"\r\n              onPress={() => handleSubmit()}\r\n              color=\"primary\"\r\n              isLoading={isLoading}\r\n              isDisabled={isLoading}\r\n            >\r\n              <span className=\"truncate whitespace-nowrap\">\r\n                {isLoading ? \"Logging in...\" : \"Login\"}\r\n              </span>\r\n            </Button>\r\n            <Divider className=\"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm my-6\" />\r\n\r\n            {/* PIN Login Section */}\r\n            <div className=\"flex w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm flex-wrap md:flex-nowrap gap-4\">\r\n              <Input\r\n                variant=\"bordered\"\r\n                label=\"PIN 6 Digit\"\r\n                type=\"password\"\r\n                maxLength={6}\r\n                value={pinValue}\r\n                onChange={(e) => setPinValue(e.target.value.replace(/\\D/g, \"\"))}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === \"Enter\") {\r\n                    handlePinLogin();\r\n                  }\r\n                }}\r\n                classNames={{\r\n                  label: \"text-sm\",\r\n                }}\r\n              />\r\n              <Button\r\n                className=\"w-full flex h-14 font-semibold\"\r\n                onPress={handlePinLogin}\r\n                variant=\"ghost\"\r\n                color=\"danger\"\r\n                isLoading={isPinLoading}\r\n                isDisabled={isPinLoading}\r\n              >\r\n                {isPinLoading ? \"Logging in...\" : \"Login PIN\"}\r\n              </Button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </Formik>\r\n\r\n      <div className=\"text-slate-400 mt-6 text-sm tracking-wider font-sans\">\r\n        Belum Punya Akun ?{\" \"}\r\n        <Link href=\"/register\" className=\"font-bold\">\r\n          Hubungi Admin\r\n        </Link>\r\n      </div>\r\n      <Divider className=\"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm my-6\" />\r\n\r\n      <div className=\"font-semibold text-slate-400 text-xs tracking-wider flex items-center font-sans gap-1\">\r\n        <Copyright size={13} />\r\n        <label>2025 Direktorat PA | PDPSIPA</label>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AAwLU;;AAtLV;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;;AAmBO,MAAM,QAAQ;;IACnB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IAEpC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EACJ,WAAW,EACX,SAAS,EACT,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,EACX,WAAW,EACX,YAAY,EACZ,SAAS,EACT,SAAS,EACT,QAAQ,EACR,SAAS,EACT,MAAM,EACN,cAAc,EACd,WAAW,EACX,OAAO,EACP,SAAS,EACT,eAAe,EACf,SAAS,EACT,aAAa,EACb,UAAU,EACV,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,SAAS,EACT,YAAY,EACb,GAAG;IAEJ,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,aAAa;QACjB,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,YAAY;QAEZ,aAAa;QACb,UAAU;QACV,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,eAAe;QACf,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,gBAAgB;QAChB,UAAU;QACV,cAAc;QACd,WAAW;QACX,aAAa;QACb,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,gBAAgB;QACpB,UAAU;QACV,UAAU;QACV,SAAS;IACX;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAClC,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YACrD,MAAM,gBAAgB,UAAU,QAAQ;YACxC,MAAM,mBAAmB,mBAAmB;YAC5C,WAAW;QACb;6CAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,MAAM,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,GAAG,CAAC,KAAK;QACxE,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,UAAU,KAAK,CAAC,EAAE;YAClB,IAAI,IAAI,MAAM,MAAM,GAAG,KAAK,IAAI,gBAAgB;gBAC9C,UAAU;YACZ;QACF;QACA,OAAO;IACT;IAEA,qCAAqC;IACrC,gCAAgC;IAChC,UAAU;IACV,wCAAwC;IACxC,oDAAoD;IACpD,SAAS;IACT,qCAAqC;IACrC,yCAAyC;IACzC,sBAAsB;IACtB,0BAA0B;IAC1B,6CAA6C;IAC7C,oBAAoB;IACpB,wBAAwB;IACxB,iDAAiD;IACjD,0CAA0C;IAC1C,MAAM;IACN,KAAK;IAEL,oBAAoB;IACpB,eAAe;IACf,UAAU;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,SAAS,KAAK;gBAChB;gBAEA,MAAM,WAAW;gDAAY;wBAC3B;oBACF;+CAAG;gBAEH;uCAAO;wBACL,cAAc;oBAChB;;YACF;QACF;0BAAG;QAAC;QAAM;KAAgB;IAE1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAC5B,OAAO;YACL,yBAAyB;YACzB,IAAI,SAAS,OAAO,mBAAmB,IAAI;gBACzC,SAAS;gBACT,UAAU,6CAA6C;gBACvD;YACF,OAAO,IAAI,SAAS,KAAK;oBACA;gBAAvB,MAAM,iBAAiB,EAAA,kBAAA,OAAO,OAAO,cAAd,sCAAA,gBAAgB,OAAO,CAAC,OAAO,QAAO;gBAC7D,IAAI,mBAAmB,QAAQ,OAAO,CAAC,OAAO,KAAK;oBACjD,UAAU,8BAA8B;oBACxC;gBACF;YACF,OAAO,IAAI,SAAS,IAAI;gBACtB,SAAS;gBACT,UAAU,yBAAyB;gBACnC;YACF;YAEA,aAAa;YAEb,IAAI;oBAEA;gBADF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,CAAA,4HAAA,kDAAA,uCAAuC,IACvC,QACA;oBAAE,iBAAiB;gBAAK;gBAE1B,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,IAAI,KAAK,GAAG,KAAK,8BAA8B;wBAC7C,SAAS;wBACT,UAAU,8BAA8B;oBAC1C,OAAO,IAAI,KAAK,GAAG,KAAK,wBAAwB;wBAC9C,SAAS;wBACT,UAAU,wBAAwB;oBACpC,OAAO;wBACL,SAAS;wBACT,UAAU,gCAAgC;oBAC5C;oBACA,WAAW;gBACb,OAAO;oBACL;oBAEA,MAAM,YAAY,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD,EAAE,KAAK,IAAI,CAAC,KAAK;oBAC7C,MAAM,UAAU,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE;oBAC1B,QAAQ,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK;oBAC3B,QAAQ,QAAQ,IAAI;oBACpB,SAAS,KAAK,IAAI,CAAC,KAAK;oBACxB,eAAe;oBACf,WAAW;oBACX,QAAQ,QAAQ,IAAI;oBACpB,UAAU,QAAQ,GAAG,CAAC,QAAQ;oBAC9B,QAAQ,QAAQ,IAAI;oBACpB,YAAY,QAAQ,QAAQ;oBAC5B,UAAU,QAAQ,MAAM;oBACxB,YAAY,QAAQ,QAAQ;oBAC5B,UAAU,QAAQ,MAAM;oBACxB,aAAa,QAAQ,UAAU;oBAC/B,UAAU,QAAQ,QAAQ;oBAC1B,UAAU,QAAQ,MAAM;oBACxB,OAAO,QAAQ,GAAG;oBAClB,YAAY,QAAQ,QAAQ;oBAC5B,QAAQ,QAAQ,IAAI;oBACpB,UAAU,QAAQ,MAAM;oBACxB,gBAAgB,QAAQ,YAAY;oBACpC,WAAW,QAAQ,OAAO;oBAC1B,YAAY,QAAQ,QAAQ;oBAE5B,wBAAwB;oBACxB,aAAa,OAAO,CAAC,UAAU;oBAC/B,aAAa,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,4BAA4B;oBAC5E,MAAM,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,2BAA2B;gBAC/E;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;gBAEZ,UAAU,eAAe;YAC3B,SAAU;gBACR,aAAa;YACf;QACF;yCACA;QAAC;QAAQ;QAAW;QAAM;QAAgB;KAAQ;IAGpD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YACjC,IAAI,CAAC,UAAU;gBACb,UAAU,kCAAkC;gBAC5C;YACF;YAEA,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,UAAU,iCAAiC;gBAC3C;YACF;YAEA,gBAAgB;YAEhB,sCAAsC;YACtC,MAAM,WAAW;YAEjB,IAAI;gBACF,0BAA0B;gBAC1B,IAAI,aAAa,UAAU;oBACzB,UAAU,kCAAkC;oBAC5C,YAAY;oBACZ;gBACF;gBAEA,qCAAqC;gBACrC,wBAAwB;gBACxB,aAAa,OAAO,CAAC,UAAU;gBAC/B,kEAAkE;gBAClE,MAAM,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;YAClC,EAAE,OAAO,OAAO;gBACd,UAAU,uCAAuC;YACnD,SAAU;gBACR,gBAAgB;YAClB;QACF;4CAAG;QAAC;QAAU;KAAU;IAExB,MAAM,iCAAiC,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACjD,wBAAwB;YACxB,OAAO,OAAO,CAAC;QACjB;4DAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kKAAA,CAAA,wBAAqB;gBACpB,WAAW;gBACX,YAAY;gBACZ,UAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oKAAA,CAAA,UAAe;oBAAC,WAAU;;;;;;;;;;;0BAG7B,6LAAC,kJAAA,CAAA,SAAM;gBACL,eAAe;gBACf,kBAAkB,sIAAA,CAAA,cAAW;gBAC7B,SAAS;oBAAE;gBAAK;gBAChB,UAAU;0BAET;wBAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE;yCACvD;;0CACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4MAAA,CAAA,QAAK;wCACJ,SAAS;wCACT,SAAQ;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO,OAAO,QAAQ;wCACtB,WAAW,CAAC,CAAC,OAAO,QAAQ,IAAI,CAAC,CAAC,QAAQ,QAAQ;wCAClD,cAAc,OAAO,QAAQ;wCAC7B,UAAU,aAAa;;;;;;kDAEzB,6LAAC,4MAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO,OAAO,QAAQ;wCACtB,WAAW,CAAC,CAAC,OAAO,QAAQ,IAAI,CAAC,CAAC,QAAQ,QAAQ;wCAClD,cAAc,OAAO,QAAQ;wCAC7B,UAAU,aAAa;wCACvB,WAAW,CAAC;4CACV,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;wCACF;;;;;;;;;;;;4BAIH,SAAS,qBACR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;0DAClB,cAAA,6LAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;sDAIP,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4MAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAW;gDACX,OAAO,OAAO,OAAO,IAAI;gDACzB,UAAU,CAAC;oDACT,qBAAqB;oDACrB,MAAM,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;oDACnD,wCAAwC;oDACxC,MAAM,iBAAiB;wDACrB,GAAG,CAAC;wDACJ,QAAQ;4DACN,GAAG,EAAE,MAAM;4DACX,OAAO;wDACT;oDACF;oDACA,aAAa,WAAW;gDAC1B;gDACA,WAAW,CAAC;oDACV,IAAI,EAAE,GAAG,KAAK,SAAS;wDACrB;oDACF;gDACF;gDACA,WAAW,CAAC,CAAC,OAAO,OAAO,IAAI,CAAC,CAAC,QAAQ,OAAO;gDAChD,cAAc,OAAO,OAAO;gDAC5B,WAAU;;;;;;;;;;;;;;;;;;;;;;4BAOnB,SAAS,qBACR,6LAAC;gCAAI,WAAU;;;;;;0CAQjB,6LAAC,+MAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM;gCACf,OAAM;gCACN,WAAW;gCACX,YAAY;0CAEZ,cAAA,6LAAC;oCAAK,WAAU;8CACb,YAAY,kBAAkB;;;;;;;;;;;0CAGnC,6LAAC,kNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4MAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,OAAM;wCACN,MAAK;wCACL,WAAW;wCACX,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;wCAC3D,WAAW,CAAC;4CACV,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;wCACF;wCACA,YAAY;4CACV,OAAO;wCACT;;;;;;kDAEF,6LAAC,+MAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,SAAQ;wCACR,OAAM;wCACN,WAAW;wCACX,YAAY;kDAEX,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC;gBAAI,WAAU;;oBAAuD;oBACjD;kCACnB,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAU;kCAAY;;;;;;;;;;;;0BAI/C,6LAAC,kNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BAEnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+MAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;kCACjB,6LAAC;kCAAM;;;;;;;;;;;;;;;;;;AAIf;GA5aa;;QAwCI,qIAAA,CAAA,YAAS;QACF,uJAAA,CAAA,WAAQ;;;KAzCnB", "debugId": null}}]}