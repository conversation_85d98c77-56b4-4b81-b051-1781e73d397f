(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/actions/auth.action.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_93cfbb59._.js",
  "static/chunks/src_actions_auth_action_ts_5b3ea7fe._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/actions/auth.action.ts [app-client] (ecmascript)");
    });
});
}),
}]);