{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KementerianFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport Kddept from \"@/components/features/reference/referensi_inquiryMod/Kddept\";\r\nimport { Building, Info } from \"lucide-react\";\r\n\r\nconst KementerianFilter = ({ inquiryState, status }) => {\r\n  // Use inquiryState for dept, deptradio, deptkondisi, katadept\r\n  const {\r\n    dept,\r\n    setDept,\r\n    deptradio,\r\n    setDeptradio,\r\n    deptkondisi,\r\n    setDeptkondisi,\r\n    katadept,\r\n    setKatadept,\r\n  } = inquiryState || {};\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katadept && katadept.trim() !== \"\";\r\n  const hasKondisiFilter = deptkondisi && deptkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    dept && dept !== \"XXX\" && dept !== \"000\" && dept !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isKddeptDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  const KementerianOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Building size={20} className=\"ml-4 text-secondary\" />\r\n          Kementerian\r\n        </h6>{\" \"}\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {\" \"}\r\n            {/* Kddept */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKddeptDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Kementerian\r\n                </label>\r\n                {hasPilihFilter && !isKddeptDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setDept && setDept(\"000\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kddept\r\n                value={dept}\r\n                onChange={setDept}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status={status}\r\n                isDisabled={isKddeptDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setDeptkondisi && setDeptkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={deptkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) => {\r\n                  const value = e.target.value;\r\n                  setDeptkondisi && setDeptkondisi(value);\r\n                }}\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatadept && setKatadept(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: keuangan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katadept || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => {\r\n                  const value = e.target.value;\r\n                  setKatadept && setKatadept(value);\r\n                }}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>{\" \"}\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={[deptradio || \"1\"]}\r\n                onSelectionChange={(key) => {\r\n                  let selected = key;\r\n                  if (key && typeof key !== \"string\" && key.size) {\r\n                    selected = Array.from(key)[0];\r\n                  }\r\n                  if (!selected) {\r\n                    setDeptradio && setDeptradio(\"1\");\r\n                    return;\r\n                  }\r\n                  setDeptradio && setDeptradio(selected);\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {KementerianOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kddept */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KementerianFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,oBAAoB,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE;IACjD,8DAA8D;IAC9D,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,YAAY,EACZ,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACZ,GAAG,gBAAgB,CAAC;IAErB,mEAAmE;IACnE,MAAM,gBAAgB,YAAY,SAAS,IAAI,OAAO;IACtD,MAAM,mBAAmB,eAAe,YAAY,IAAI,OAAO;IAC/D,MAAM,iBACJ,QAAQ,SAAS,SAAS,SAAS,SAAS,SAAS;IAEvD,8CAA8C;IAC9C,MAAM,mBAAmB,iBAAiB;IAC1C,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,MAAM,qBAAqB;QACzB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;gBAElD;8BAEN,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,mBAAmB,kBAAkB,iBACrC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,kCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,WAAW,QAAQ;8DACnC;;;;;;;;;;;;sDAKL,8OAAC,6KAAA,CAAA,UAAM;4CACL,OAAO;4CACP,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,QAAQ;4CACR,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAKjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAML,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,YAAY;4CACZ,UAAU,CAAC;gDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAC5B,kBAAkB,eAAe;4CACnC;;;;;;;;;;;;8CAIJ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,eAAe,YAAY;8DAC3C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,YAAY;4CACnB,YAAY;4CACZ,UAAU,CAAC;gDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAC5B,eAAe,YAAY;4CAC7B;;;;;;;;;;;;8CAIJ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;wCAE5C;sDACT,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc;gDAAC,aAAa;6CAAI;4CAChC,mBAAmB,CAAC;gDAClB,IAAI,WAAW;gDACf,IAAI,OAAO,OAAO,QAAQ,YAAY,IAAI,IAAI,EAAE;oDAC9C,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gDAC/B;gDACA,IAAI,CAAC,UAAU;oDACb,gBAAgB,aAAa;oDAC7B;gDACF;gDACA,gBAAgB,aAAa;4CAC/B;4CACA,sBAAsB;sDAErB,mBAAmB,GAAG,CAAC,CAAC,oBACvB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAQlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/UnitFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Layers, Info } from \"lucide-react\";\r\nimport Kdunit from \"@/components/features/reference/referensi_inquiryMod/Kdunit\";\r\n\r\nconst UnitFilter = ({ inquiryState }) => {\r\n  // Use inquiryState for unit values and dept dependency\r\n  const {\r\n    dept, // Get dept to filter unit list\r\n    kdunit: unit, // Use kdunit from shared state\r\n    setKdunit: setUnit,\r\n    unitkondisi,\r\n    setUnitkondisi,\r\n    kataunit,\r\n    setKataunit,\r\n    unitradio,\r\n    setUnitradio,\r\n  } = inquiryState || {};\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = kataunit && kataunit.trim() !== \"\";\r\n  const hasKondisiFilter = unitkondisi && unitkondisi.trim() !== \"\";\r\n  const hasPilihFilter = unit && unit !== \"XXX\" && unit !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isKdunitDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  // 4 display options, matching KementerianFilter\r\n  const UnitOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  // When the department changes, reset the selected unit to \"Semua Unit\"\r\n  React.useEffect(() => {\r\n    if (setUnit) {\r\n      setUnit(\"XX\");\r\n    }\r\n  }, [dept, setUnit]);\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Layers size={20} className=\"ml-4 text-secondary\" />\r\n          Eselon I\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdunit */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKdunitDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Eselon I\r\n                </label>\r\n                {hasPilihFilter && !isKdunitDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setUnit && setUnit(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdunit\r\n                value={unit}\r\n                onChange={setUnit} // The refactored Kdunit passes the value directly\r\n                kddept={dept}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status=\"pilihunit\"\r\n                isDisabled={isKdunitDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"ml-1 cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setUnitkondisi && setUnitkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 01,02,03, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={unitkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setUnitkondisi && setUnitkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKataunit && setKataunit(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: sekretariat\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kataunit || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKataunit && setKataunit(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={new Set([unitradio || \"1\"])}\r\n                onSelectionChange={(keys) => {\r\n                  // HeroUI Select passes a Set object\r\n                  const selected = Array.from(keys)[0];\r\n                  if (selected && setUnitradio) {\r\n                    setUnitradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {UnitOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdunit */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UnitFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE;IAClC,uDAAuD;IACvD,MAAM,EACJ,IAAI,EACJ,QAAQ,IAAI,EACZ,WAAW,OAAO,EAClB,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACb,GAAG,gBAAgB,CAAC;IAErB,mEAAmE;IACnE,MAAM,gBAAgB,YAAY,SAAS,IAAI,OAAO;IACtD,MAAM,mBAAmB,eAAe,YAAY,IAAI,OAAO;IAC/D,MAAM,iBAAiB,QAAQ,SAAS,SAAS,SAAS;IAE1D,8CAA8C;IAC9C,MAAM,mBAAmB,iBAAiB;IAC1C,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,gDAAgD;IAChD,MAAM,cAAc;QAClB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,uEAAuE;IACvE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS;YACX,QAAQ;QACV;IACF,GAAG;QAAC;QAAM;KAAQ;IAElB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,mBAAmB,kBAAkB,iBACrC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,kCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,WAAW,QAAQ;8DACnC;;;;;;;;;;;;sDAKL,8OAAC,6KAAA,CAAA,UAAM;4CACL,OAAO;4CACP,UAAU;4CACV,QAAQ;4CACR,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,YAAY;4CACZ,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAMrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,eAAe,YAAY;8DAC3C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,YAAY;4CACnB,YAAY;4CACZ,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc,IAAI,IAAI;gDAAC,aAAa;6CAAI;4CACxC,mBAAmB,CAAC;gDAClB,oCAAoC;gDACpC,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,YAAY,cAAc;oDAC5B,aAAa;gDACf;4CACF;4CACA,sBAAsB;sDAErB,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/LokasiFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { MapPin, Info } from \"lucide-react\";\r\nimport Kdlokasi from \"@/components/features/reference/referensi_inquiryMod/Kdlokasi\";\r\n\r\nconst LokasiFilter = ({ inquiryState }) => {\r\n  // Use inquiryState for prov, locradio, lokasikondisi, katalokasi\r\n  const {\r\n    prov,\r\n    setProv,\r\n    locradio,\r\n    setLocradio,\r\n    lokasikondisi,\r\n    setLokasikondisi,\r\n    katalokasi,\r\n    setKatalokasi,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katalokasi && katalokasi.trim() !== \"\";\r\n  const hasKondisiFilter = lokasikondisi && lokasikondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    prov && prov !== \"XXX\" && prov !== \"XX\" && prov !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  const LocOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Uraian\" },\r\n    { value: \"3\", label: \"Kode Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <MapPin size={20} className=\"ml-4 text-secondary\" />\r\n          Provinsi\r\n        </h6>\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdlokasi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isPilihDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Provinsi\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setProv && setProv(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdlokasi\r\n                value={prov}\r\n                onChange={setProv}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status=\"pilihprov\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setLokasikondisi && setLokasikondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 31,32,33, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={lokasikondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setLokasikondisi && setLokasikondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatalokasi && setKatalokasi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: jawa\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katalokasi || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKatalokasi && setKatalokasi(e.target.value)}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={new Set([locradio || \"1\"])}\r\n                onSelectionChange={(keys) => {\r\n                  // HeroUI Select passes a Set object\r\n                  const selected = Array.from(keys)[0];\r\n                  if (selected && setLocradio) {\r\n                    setLocradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {LocOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdlokasi */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Helper text under Kondisi */}\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LokasiFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,iEAAiE;IACjE,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACd,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,cAAc,WAAW,IAAI,OAAO;IAC1D,MAAM,mBAAmB,iBAAiB,cAAc,IAAI,OAAO;IACnE,MAAM,iBACJ,QAAQ,SAAS,SAAS,SAAS,QAAQ,SAAS;IAEtD,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,MAAM,aAAa;QACjB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAItD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,kBAAkB,kBAAkB,iBACpC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,WAAW,QAAQ;8DACnC;;;;;;;;;;;;sDAKL,8OAAC,+KAAA,CAAA,UAAQ;4CACP,OAAO;4CACP,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,oBAAoB,iBAAiB;8DACrD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,iBAAiB;4CACxB,YAAY;4CACZ,UAAU,CAAC,IACT,oBAAoB,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,iBAAiB,cAAc;8DAC/C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,cAAc;4CACrB,YAAY;4CACZ,UAAU,CAAC,IAAM,iBAAiB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIlE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc,IAAI,IAAI;gDAAC,YAAY;6CAAI;4CACvC,mBAAmB,CAAC;gDAClB,oCAAoC;gDACpC,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,YAAY,aAAa;oDAC3B,YAAY;gDACd;4CACF;4CACA,sBAAsB;sDAErB,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAQlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAIf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/FungsiFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport Kdfungsi from \"@/components/features/reference/referensi_inquiryMod/Kdfungsi\";\r\nimport { BookText, Info } from \"lucide-react\";\r\n\r\nconst FungsiFilter = ({ inquiryState }) => {\r\n  const {\r\n    fungsi,\r\n    setFungsi,\r\n    fungsiradio,\r\n    setFungsiradio,\r\n    fungsikondisi,\r\n    setFungsikondisi,\r\n    katafungsi,\r\n    setKatafungsi,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katafungsi && katafungsi.trim() !== \"\";\r\n  const hasKondisiFilter = fungsikondisi && fungsikondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    fungsi && fungsi !== \"XXX\" && fungsi !== \"XX\" && fungsi !== \"00\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  const FungsiOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <BookText size={20} className=\"ml-4 text-secondary\" />\r\n          Fungsi\r\n        </h6>{\" \"}\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {\" \"}\r\n            {/* Kdfungsi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isPilihDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Fungsi\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setFungsi && setFungsi(\"00\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdfungsi\r\n                kdfungsi={fungsi}\r\n                onChange={setFungsi}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status=\"pilihfungsi\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setFungsikondisi && setFungsikondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"misalkan: 01,02, dst atau !03\"\r\n                value={fungsikondisi}\r\n                onChange={(e) => setFungsikondisi(e.target.value)}\r\n                className=\"w-full\"\r\n                size=\"sm\"\r\n                isDisabled={isKondisiDisabled}\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Kata Kunci\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatafungsi && setKatafungsi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"misalkan: ekonomi\"\r\n                value={katafungsi}\r\n                onChange={(e) => setKatafungsi(e.target.value)}\r\n                className=\"w-full\"\r\n                size=\"sm\"\r\n                isDisabled={isKataDisabled}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>{\" \"}\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={new Set([fungsiradio])}\r\n                onSelectionChange={(keys) => {\r\n                  // HeroUI Select passes a Set object\r\n                  const selected = Array.from(keys)[0];\r\n                  if (selected) {\r\n                    setFungsiradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {FungsiOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdfungsi */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FungsiFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,MAAM,EACJ,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACd,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,cAAc,WAAW,IAAI,OAAO;IAC1D,MAAM,mBAAmB,iBAAiB,cAAc,IAAI,OAAO;IACnE,MAAM,iBACJ,UAAU,WAAW,SAAS,WAAW,QAAQ,WAAW;IAE9D,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;gBAElD;8BAEN,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,kBAAkB,kBAAkB,iBACpC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,aAAa,UAAU;8DACvC;;;;;;;;;;;;sDAKL,8OAAC,+KAAA,CAAA,UAAQ;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,oBAAoB,iBAAiB;8DACrD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;4CACV,MAAK;4CACL,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,iBAAiB,cAAc;8DAC/C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;4CACV,MAAK;4CACL,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;wCAE5C;sDACT,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc,IAAI,IAAI;gDAAC;6CAAY;4CACnC,mBAAmB,CAAC;gDAClB,oCAAoC;gDACpC,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,UAAU;oDACZ,eAAe;gDACjB;4CACF;4CACA,sBAAsB;sDAErB,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/SubfungsiFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport Kdsfungsi from \"@/components/features/reference/referensi_inquiryMod/Kdsfungsi\";\r\nimport { Layers, Info } from \"lucide-react\";\r\n\r\nconst SubfungsiFilter = ({ inquiryState }) => {\r\n  const {\r\n    fungsi,\r\n    sfungsi,\r\n    setSfungsi,\r\n    subfungsiradio,\r\n    setSubfungsiradio,\r\n    subfungsikondisi,\r\n    setSubfungsikondisi,\r\n    katasubfungsi,\r\n    setKatasubfungsi,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katasubfungsi && katasubfungsi.trim() !== \"\";\r\n  const hasKondisiFilter = subfungsikondisi && subfungsikondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    sfungsi && sfungsi !== \"XXX\" && sfungsi !== \"XX\" && sfungsi !== \"00\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  const SubfungsiOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Layers size={20} className=\"ml-4 text-secondary\" />\r\n          Sub-Fungsi\r\n        </h6>{\" \"}\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {\" \"}\r\n            {/* Kdsfungsi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isPilihDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Sub-Fungsi\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setSfungsi && setSfungsi(\"00\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdsfungsi\r\n                kdsfungsi={sfungsi}\r\n                onChange={setSfungsi}\r\n                kdfungsi={fungsi}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status=\"pilihsubfungsi\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() =>\r\n                      setSubfungsikondisi && setSubfungsikondisi(\"\")\r\n                    }\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"misalkan: 01,02, dst atau !01\"\r\n                value={subfungsikondisi}\r\n                onChange={(e) => setSubfungsikondisi(e.target.value)}\r\n                className=\"w-full\"\r\n                size=\"sm\"\r\n                isDisabled={isKondisiDisabled}\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Kata Kunci\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatasubfungsi && setKatasubfungsi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"misalkan: industri\"\r\n                value={katasubfungsi}\r\n                onChange={(e) => setKatasubfungsi(e.target.value)}\r\n                className=\"w-full\"\r\n                size=\"sm\"\r\n                isDisabled={isKataDisabled}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>{\" \"}\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={new Set([subfungsiradio])}\r\n                onSelectionChange={(keys) => {\r\n                  // HeroUI Select passes a Set object\r\n                  const selected = Array.from(keys)[0];\r\n                  if (selected) {\r\n                    setSubfungsiradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {SubfungsiOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdsfungsi */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SubfungsiFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE;IACvC,MAAM,EACJ,MAAM,EACN,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EACjB,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,iBAAiB,cAAc,IAAI,OAAO;IAChE,MAAM,mBAAmB,oBAAoB,iBAAiB,IAAI,OAAO;IACzE,MAAM,iBACJ,WAAW,YAAY,SAAS,YAAY,QAAQ,YAAY;IAElE,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;gBAEhD;8BAEN,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,kBAAkB,kBAAkB,iBACpC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,cAAc,WAAW;8DACzC;;;;;;;;;;;;sDAKL,8OAAC,gLAAA,CAAA,UAAS;4CACR,WAAW;4CACX,UAAU;4CACV,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IACP,uBAAuB,oBAAoB;8DAE9C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;4CACV,MAAK;4CACL,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,oBAAoB,iBAAiB;8DACrD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;4CACV,MAAK;4CACL,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;wCAE5C;sDACT,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc,IAAI,IAAI;gDAAC;6CAAe;4CACtC,mBAAmB,CAAC;gDAClB,oCAAoC;gDACpC,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,UAAU;oDACZ,kBAAkB;gDACpB;4CACF;4CACA,sBAAsB;sDAErB,iBAAiB,GAAG,CAAC,CAAC,oBACrB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 1924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/ProgramFilter.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Settings, Info } from \"lucide-react\";\r\nimport Kdprogram from \"@/components/features/reference/referensi_inquiryMod/Kdprogram\";\r\nimport { getFilteredPrograms } from \"../../utils/filterUtils\";\r\n\r\nconst ProgramFilter = ({ inquiryState, type = \"program\" }) => {\r\n  // Get different states based on type (program vs activity)\r\n  const getFilterStates = () => {\r\n    if (type === \"activity\") {\r\n      return {\r\n        value: inquiryState?.giat,\r\n        setValue: inquiryState?.setGiat,\r\n        kondisi: inquiryState?.giatkondisi,\r\n        setKondisi: inquiryState?.setGiatkondisi,\r\n        kata: inquiryState?.katagiat,\r\n        setKata: inquiryState?.setKatagiat,\r\n        radio: inquiryState?.kegiatanradio,\r\n        setRadio: inquiryState?.setKegiatanradio,\r\n        filterProps: {\r\n          kddept: inquiryState?.dept,\r\n          kdunit: inquiryState?.kdunit,\r\n          kdprogram: inquiryState?.program,\r\n        },\r\n        title: \"Kegiatan\",\r\n        label: \"Pilih <PERSON>giatan\",\r\n      };\r\n    } else {\r\n      return {\r\n        value: inquiryState?.program,\r\n        setValue: inquiryState?.setProgram,\r\n        kondisi: inquiryState?.programkondisi,\r\n        setKondisi: inquiryState?.setProgramkondisi,\r\n        kata: inquiryState?.kataprogram,\r\n        setKata: inquiryState?.setKataprogram,\r\n        radio: inquiryState?.programradio,\r\n        setRadio: inquiryState?.setProgramradio,\r\n        filterProps: {\r\n          kddept: inquiryState?.dept,\r\n          kdunit: inquiryState?.kdunit,\r\n        },\r\n        title: \"Program\",\r\n        label: \"Pilih Program\",\r\n      };\r\n    }\r\n  };\r\n\r\n  const {\r\n    value,\r\n    setValue,\r\n    kondisi,\r\n    setKondisi,\r\n    kata,\r\n    setKata,\r\n    radio,\r\n    setRadio,\r\n    filterProps,\r\n    title,\r\n    label,\r\n  } = getFilterStates();\r\n\r\n  // State for filtered data\r\n  const [filteredData, setFilteredData] = useState([]);\r\n\r\n  // Update filtered data when parent filters change\r\n  useEffect(() => {\r\n    if (type === \"program\") {\r\n      const filtered = getFilteredPrograms(\r\n        filterProps.kddept,\r\n        filterProps.kdunit\r\n      );\r\n      setFilteredData(filtered);\r\n    }\r\n  }, [type, filterProps.kddept, filterProps.kdunit]);\r\n\r\n  // When parent filters change, reset child selection\r\n  React.useEffect(() => {\r\n    if (setValue) {\r\n      setValue(\"XX\");\r\n    }\r\n  }, [filterProps.kddept, filterProps.kdunit, filterProps.kdprogram, setValue]);\r\n\r\n  const ProgramOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  // Determine if any filter is active\r\n  const isFilterActive = () => {\r\n    return (\r\n      (kondisi && kondisi !== \"\") ||\r\n      (kata && kata !== \"\") ||\r\n      (radio && radio !== \"1\") // Assuming \"1\" is the default for no filter\r\n    );\r\n  };\r\n\r\n  // Disable states for each input based on filter logic\r\n  const isPilihDisabled = isFilterActive();\r\n  const isKondisiDisabled = isFilterActive() && !kondisi;\r\n  const isKataDisabled = isFilterActive() && !kata;\r\n  const hasKondisiFilter = kondisi && kondisi !== \"\";\r\n  const hasKataFilter = kata && kata !== \"\";\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Settings size={20} className=\"ml-4 text-secondary\" />\r\n          {title}\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                {label}\r\n              </label>\r\n              <Kdprogram\r\n                value={value}\r\n                onChange={setValue}\r\n                {...filterProps}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder={label}\r\n                status={type === \"activity\" ? \"pilihgiat\" : \"pilihprogram\"}\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKondisi && setKondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) => setKondisi && setKondisi(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKata && setKata(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: pendidikan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kata || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKata && setKata(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={radio ? [radio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setRadio) {\r\n                    setRadio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {ProgramOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Selection */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgramFilter;\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;;;;;AAEA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE,OAAO,SAAS,EAAE;IACvD,2DAA2D;IAC3D,MAAM,kBAAkB;QACtB,IAAI,SAAS,YAAY;YACvB,OAAO;gBACL,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,SAAS,cAAc;gBACvB,YAAY,cAAc;gBAC1B,MAAM,cAAc;gBACpB,SAAS,cAAc;gBACvB,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,aAAa;oBACX,QAAQ,cAAc;oBACtB,QAAQ,cAAc;oBACtB,WAAW,cAAc;gBAC3B;gBACA,OAAO;gBACP,OAAO;YACT;QACF,OAAO;YACL,OAAO;gBACL,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,SAAS,cAAc;gBACvB,YAAY,cAAc;gBAC1B,MAAM,cAAc;gBACpB,SAAS,cAAc;gBACvB,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,aAAa;oBACX,QAAQ,cAAc;oBACtB,QAAQ,cAAc;gBACxB;gBACA,OAAO;gBACP,OAAO;YACT;QACF;IACF;IAEA,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACX,KAAK,EACL,KAAK,EACN,GAAG;IAEJ,0BAA0B;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEnD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,WAAW;YACtB,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EACjC,YAAY,MAAM,EAClB,YAAY,MAAM;YAEpB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAM,YAAY,MAAM;QAAE,YAAY,MAAM;KAAC;IAEjD,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU;YACZ,SAAS;QACX;IACF,GAAG;QAAC,YAAY,MAAM;QAAE,YAAY,MAAM;QAAE,YAAY,SAAS;QAAE;KAAS;IAE5E,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,oCAAoC;IACpC,MAAM,iBAAiB;QACrB,OACE,AAAC,WAAW,YAAY,MACvB,QAAQ,SAAS,MACjB,SAAS,UAAU,IAAK,4CAA4C;;IAEzE;IAEA,sDAAsD;IACtD,MAAM,kBAAkB;IACxB,MAAM,oBAAoB,oBAAoB,CAAC;IAC/C,MAAM,iBAAiB,oBAAoB,CAAC;IAC5C,MAAM,mBAAmB,WAAW,YAAY;IAChD,MAAM,gBAAgB,QAAQ,SAAS;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAC7B;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDACd;;;;;;sDAEH,8OAAC,gLAAA,CAAA,UAAS;4CACR,OAAO;4CACP,UAAU;4CACT,GAAG,WAAW;4CACf,WAAU;4CACV,MAAK;4CACL,aAAa;4CACb,QAAQ,SAAS,aAAa,cAAc;4CAC5C,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,cAAc,WAAW;8DACzC;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,WAAW;4CAClB,YAAY;4CACZ,UAAU,CAAC,IAAM,cAAc,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAK5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,WAAW,QAAQ;8DACnC;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,QAAQ;4CACf,YAAY;4CACZ,UAAU,CAAC,IAAM,WAAW,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,QAAQ;gDAAC;6CAAM,GAAG;gDAAC;6CAAI;4CACrC,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,UAAU;oDACZ,SAAS;gDACX;4CACF;4CACA,sBAAsB;sDAErB,eAAe,GAAG,CAAC,CAAC,oBACnB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 2350, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KegiatanFilter.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Activity, Info } from \"lucide-react\";\r\nimport Kdgiat from \"@/components/features/reference/referensi_inquiryMod/Kdgiat\";\r\nimport { getFilteredKegiatan } from \"../../utils/filterUtils\";\r\n\r\nconst KegiatanFilter = ({ inquiryState }) => {\r\n  const {\r\n    giat,\r\n    setGiat,\r\n    giatkondisi,\r\n    setGiatkondisi,\r\n    katagiat,\r\n    setKatagiat,\r\n    kegiatanradio,\r\n    setKegiatanradio,\r\n    dept,\r\n    kdunit,\r\n    program,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katagiat && katagiat.trim() !== \"\";\r\n  const hasKondisiFilter = giatkondisi && giatkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    giat && giat !== \"XXX\" && giat !== \"XX\" && giat !== \"XXX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  // State for filtered data\r\n  const [filteredData, setFilteredData] = useState([]);\r\n\r\n  // Update filtered data when parent filters change\r\n  useEffect(() => {\r\n    const filtered = getFilteredKegiatan(dept, kdunit, program);\r\n    setFilteredData(filtered);\r\n  }, [dept, kdunit, program]);\r\n\r\n  // When parent filters change, reset child selection\r\n  React.useEffect(() => {\r\n    if (setGiat) {\r\n      setGiat(\"XX\");\r\n    }\r\n  }, [dept, kdunit, program, setGiat]);\r\n\r\n  const KegiatanOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Activity size={20} className=\"ml-4 text-secondary\" />\r\n          Kegiatan\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdgiat */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Kegiatan\r\n              </label>\r\n              <Kdgiat\r\n                value={giat}\r\n                onChange={setGiat}\r\n                kddept={dept}\r\n                kdunit={kdunit}\r\n                kdprogram={program}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Kegiatan\"\r\n                status=\"pilihgiat\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setGiatkondisi && setGiatkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 1001,1002,1003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={giatkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setGiatkondisi && setGiatkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatagiat && setKatagiat(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: layanan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katagiat || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKatagiat && setKatagiat(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={kegiatanradio ? [kegiatanradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setKegiatanradio) {\r\n                    setKegiatanradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {KegiatanOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdgiat */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KegiatanFilter;\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;;;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,YAAY,EAAE;IACtC,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,IAAI,EACJ,MAAM,EACN,OAAO,EACR,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,YAAY,SAAS,IAAI,OAAO;IACtD,MAAM,mBAAmB,eAAe,YAAY,IAAI,OAAO;IAC/D,MAAM,iBACJ,QAAQ,SAAS,SAAS,SAAS,QAAQ,SAAS;IAEtD,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,0BAA0B;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEnD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,QAAQ;QACnD,gBAAgB;IAClB,GAAG;QAAC;QAAM;QAAQ;KAAQ;IAE1B,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS;YACX,QAAQ;QACV;IACF,GAAG;QAAC;QAAM;QAAQ;QAAS;KAAQ;IAEnC,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,6KAAA,CAAA,UAAM;4CACL,OAAO;4CACP,UAAU;4CACV,QAAQ;4CACR,QAAQ;4CACR,WAAW;4CACX,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,YAAY;4CACZ,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAMrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,eAAe,YAAY;8DAC3C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,YAAY;4CACnB,YAAY;4CACZ,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,gBAAgB;gDAAC;6CAAc,GAAG;gDAAC;6CAAI;4CACrD,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,kBAAkB;oDACpB,iBAAiB;gDACnB;4CACF;4CACA,sBAAsB;sDAErB,gBAAgB,GAAG,CAAC,CAAC,oBACpB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 2734, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/OutputFilter.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Target, Info } from \"lucide-react\";\r\nimport Kdoutput from \"@/components/features/reference/referensi_inquiryMod/Kdoutput\";\r\nimport Kdsoutput from \"@/components/features/reference/referensi_inquiryMod/Kdsoutput\";\r\nimport { getFilteredOutputs } from \"../../utils/filterUtils\";\r\n\r\nconst OutputFilter = ({ inquiryState, type = \"output\" }) => {\r\n  // Get different states based on type (output vs suboutput)\r\n  const getFilterStates = () => {\r\n    if (type === \"suboutput\") {\r\n      return {\r\n        value: inquiryState?.soutput,\r\n        setValue: inquiryState?.setsOutput,\r\n        kondisi: inquiryState?.soutputkondisi,\r\n        setKondisi: inquiryState?.setSoutputkondisi,\r\n        kata: inquiryState?.katasoutput,\r\n        setKata: inquiryState?.setKatasoutput,\r\n        radio: inquiryState?.soutputradio,\r\n        setRadio: inquiryState?.setsOutputradio,\r\n        filterProps: {\r\n          kdgiat: inquiryState?.giat,\r\n          kdoutput: inquiryState?.output,\r\n        },\r\n        title: \"Sub-output\",\r\n        label: \"Pilih Sub-output\",\r\n        Component: Kdsoutput,\r\n      };\r\n    } else {\r\n      return {\r\n        value: inquiryState?.output,\r\n        setValue: inquiryState?.setOutput,\r\n        kondisi: inquiryState?.outputkondisi,\r\n        setKondisi: inquiryState?.setOutputkondisi,\r\n        kata: inquiryState?.kataoutput,\r\n        setKata: inquiryState?.setKataoutput,\r\n        radio: inquiryState?.outputradio,\r\n        setRadio: inquiryState?.setOutputradio,\r\n        filterProps: {\r\n          kddept: inquiryState?.dept,\r\n          kdunit: inquiryState?.kdunit,\r\n          kdprogram: inquiryState?.program,\r\n          kdgiat: inquiryState?.giat,\r\n        },\r\n        title: \"Output\",\r\n        label: \"Pilih Output\",\r\n        Component: Kdoutput,\r\n      };\r\n    }\r\n  };\r\n\r\n  const {\r\n    value,\r\n    setValue,\r\n    kondisi,\r\n    setKondisi,\r\n    kata,\r\n    setKata,\r\n    radio,\r\n    setRadio,\r\n    filterProps,\r\n    title,\r\n    label,\r\n    Component,\r\n  } = getFilterStates();\r\n\r\n  // State for filtered data - only for output type\r\n  const [filteredData, setFilteredData] = useState([]);\r\n\r\n  // Update filtered data when parent filters change (only for output)\r\n  useEffect(() => {\r\n    if (type === \"output\") {\r\n      const filtered = getFilteredOutputs(\r\n        filterProps.kddept || inquiryState?.dept,\r\n        filterProps.kdunit || inquiryState?.kdunit,\r\n        filterProps.kdprogram || inquiryState?.program,\r\n        filterProps.kdgiat || inquiryState?.giat\r\n      );\r\n      setFilteredData(filtered);\r\n    }\r\n    // For suboutput, no data-driven filtering since there's no child data\r\n  }, [\r\n    type,\r\n    inquiryState?.dept,\r\n    inquiryState?.kdunit,\r\n    inquiryState?.program,\r\n    inquiryState?.giat,\r\n  ]);\r\n\r\n  // When parent filters change, reset child selection\r\n  React.useEffect(() => {\r\n    if (setValue) {\r\n      setValue(\"XX\");\r\n    }\r\n  }, [\r\n    filterProps.kddept,\r\n    filterProps.kdunit,\r\n    filterProps.kdprogram,\r\n    filterProps.kdgiat,\r\n    filterProps.kdoutput,\r\n    setValue,\r\n  ]);\r\n\r\n  const OutputOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKondisiFilter = kondisi && kondisi.trim() !== \"\";\r\n  const hasKataFilter = kata && kata.trim() !== \"\";\r\n  const hasPilihFilter = value && value !== \"XX\" && value !== \"XXX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKondisiFilter || hasKataFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Target size={20} className=\"ml-4 text-secondary\" />\r\n          {title}\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Output Selection */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                {label}\r\n              </label>\r\n              <Component\r\n                value={value}\r\n                onChange={setValue}\r\n                {...filterProps}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder={label}\r\n                status={type === \"suboutput\" ? \"pilihsoutput\" : \"pilihoutput\"}\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKondisi && setKondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: EAA,EAB,EAC, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) => setKondisi && setKondisi(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKata && setKata(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: layanan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kata || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKata && setKata(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={radio ? [radio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setRadio) {\r\n                    setRadio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {OutputOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Output */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OutputFilter;\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE,OAAO,QAAQ,EAAE;IACrD,2DAA2D;IAC3D,MAAM,kBAAkB;QACtB,IAAI,SAAS,aAAa;YACxB,OAAO;gBACL,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,SAAS,cAAc;gBACvB,YAAY,cAAc;gBAC1B,MAAM,cAAc;gBACpB,SAAS,cAAc;gBACvB,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,aAAa;oBACX,QAAQ,cAAc;oBACtB,UAAU,cAAc;gBAC1B;gBACA,OAAO;gBACP,OAAO;gBACP,WAAW,gLAAA,CAAA,UAAS;YACtB;QACF,OAAO;YACL,OAAO;gBACL,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,SAAS,cAAc;gBACvB,YAAY,cAAc;gBAC1B,MAAM,cAAc;gBACpB,SAAS,cAAc;gBACvB,OAAO,cAAc;gBACrB,UAAU,cAAc;gBACxB,aAAa;oBACX,QAAQ,cAAc;oBACtB,QAAQ,cAAc;oBACtB,WAAW,cAAc;oBACzB,QAAQ,cAAc;gBACxB;gBACA,OAAO;gBACP,OAAO;gBACP,WAAW,+KAAA,CAAA,UAAQ;YACrB;QACF;IACF;IAEA,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACX,KAAK,EACL,KAAK,EACL,SAAS,EACV,GAAG;IAEJ,iDAAiD;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEnD,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,UAAU;YACrB,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,qBAAkB,AAAD,EAChC,YAAY,MAAM,IAAI,cAAc,MACpC,YAAY,MAAM,IAAI,cAAc,QACpC,YAAY,SAAS,IAAI,cAAc,SACvC,YAAY,MAAM,IAAI,cAAc;YAEtC,gBAAgB;QAClB;IACA,sEAAsE;IACxE,GAAG;QACD;QACA,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;KACf;IAED,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU;YACZ,SAAS;QACX;IACF,GAAG;QACD,YAAY,MAAM;QAClB,YAAY,MAAM;QAClB,YAAY,SAAS;QACrB,YAAY,MAAM;QAClB,YAAY,QAAQ;QACpB;KACD;IAED,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,mEAAmE;IACnE,MAAM,mBAAmB,WAAW,QAAQ,IAAI,OAAO;IACvD,MAAM,gBAAgB,QAAQ,KAAK,IAAI,OAAO;IAC9C,MAAM,iBAAiB,SAAS,UAAU,QAAQ,UAAU;IAE5D,8CAA8C;IAC9C,MAAM,kBAAkB,oBAAoB;IAC5C,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAC3B;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDACd;;;;;;sDAEH,8OAAC;4CACC,OAAO;4CACP,UAAU;4CACT,GAAG,WAAW;4CACf,WAAU;4CACV,MAAK;4CACL,aAAa;4CACb,QAAQ,SAAS,cAAc,iBAAiB;4CAChD,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,cAAc,WAAW;8DACzC;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,WAAW;4CAClB,YAAY;4CACZ,UAAU,CAAC,IAAM,cAAc,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAK5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,WAAW,QAAQ;8DACnC;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,QAAQ;4CACf,YAAY;4CACZ,UAAU,CAAC,IAAM,WAAW,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,QAAQ;gDAAC;6CAAM,GAAG;gDAAC;6CAAI;4CACrC,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,UAAU;oDACZ,SAAS;gDACX;4CACF;4CACA,sBAAsB;sDAErB,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 3167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/SuboutputFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Target, Info } from \"lucide-react\";\r\nimport Kdsoutput from \"@/components/features/reference/referensi_inquiryMod/Kdsoutput\";\r\n\r\nconst SuboutputFilter = ({ inquiryState }) => {\r\n  const {\r\n    soutput,\r\n    setsOutput,\r\n    soutputkondisi,\r\n    setSoutputkondisi,\r\n    katasoutput,\r\n    setKatasoutput,\r\n    soutputradio,\r\n    setsOutputradio,\r\n    giat,\r\n    output,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katasoutput && katasoutput.trim() !== \"\";\r\n  const hasKondisiFilter = soutputkondisi && soutputkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    soutput && soutput !== \"XXX\" && soutput !== \"XX\" && soutput !== \"XXX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  // When parent filters change, reset child selection\r\n  React.useEffect(() => {\r\n    if (setsOutput) {\r\n      setsOutput(\"XX\");\r\n    }\r\n  }, [giat, output, setsOutput]);\r\n\r\n  const SuboutputOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Target size={20} className=\"ml-4 text-secondary\" />\r\n          Sub-output\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdsoutput */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Pilih Sub-output\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setsOutput && setsOutput(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdsoutput\r\n                value={soutput}\r\n                onChange={setsOutput}\r\n                kdgiat={giat}\r\n                kdoutput={output}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Sub-output\"\r\n                status=\"pilihsoutput\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setSoutputkondisi && setSoutputkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={soutputkondisi || \"\"}\r\n                onChange={(e) =>\r\n                  setSoutputkondisi && setSoutputkondisi(e.target.value)\r\n                }\r\n                isDisabled={isKondisiDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setKatasoutput && setKatasoutput(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: layanan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katasoutput || \"\"}\r\n                onChange={(e) =>\r\n                  setKatasoutput && setKatasoutput(e.target.value)\r\n                }\r\n                isDisabled={isKataDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={soutputradio ? [soutputradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setsOutputradio) {\r\n                    setsOutputradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {SuboutputOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdsoutput */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuboutputFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE;IACvC,MAAM,EACJ,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,MAAM,EACP,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,eAAe,YAAY,IAAI,OAAO;IAC5D,MAAM,mBAAmB,kBAAkB,eAAe,IAAI,OAAO;IACrE,MAAM,iBACJ,WAAW,YAAY,SAAS,YAAY,QAAQ,YAAY;IAElE,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,YAAY;YACd,WAAW;QACb;IACF,GAAG;QAAC;QAAM;QAAQ;KAAW;IAE7B,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,cAAc,WAAW;8DACzC;;;;;;;;;;;;sDAKL,8OAAC,gLAAA,CAAA,UAAS;4CACR,OAAO;4CACP,UAAU;4CACV,QAAQ;4CACR,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,qBAAqB,kBAAkB;8DACvD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,kBAAkB;4CACzB,UAAU,CAAC,IACT,qBAAqB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CAEvD,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAEjD,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,eAAe;gDAAC;6CAAa,GAAG;gDAAC;6CAAI;4CACnD,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,iBAAiB;oDACnB,gBAAgB;gDAClB;4CACF;4CACA,sBAAsB;sDAErB,iBAAiB,GAAG,CAAC,CAAC,oBACrB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 3557, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KomponenFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Package } from \"lucide-react\";\r\nimport Kdkomponen from \"@/components/features/reference/referensi_inquiryMod/Kdkomponen\";\r\n\r\nconst KomponenFilter = ({ inquiryState }) => {\r\n  const {\r\n    komponen,\r\n    setKomponen,\r\n    komponenkondisi,\r\n    setKomponenkondisi,\r\n    katakomponen,\r\n    setKatakomponen,\r\n    komponenradio,\r\n    setKomponenradio,\r\n    dept,\r\n    kdunit,\r\n    program,\r\n    giat,\r\n    output,\r\n    soutput,\r\n  } = inquiryState || {};\r\n\r\n  // When parent filters change, reset child selection\r\n  React.useEffect(() => {\r\n    if (setKomponen) {\r\n      setKomponen(\"XX\");\r\n    }\r\n  }, [dept, kdunit, program, giat, output, soutput, setKomponen]);\r\n\r\n  const KomponenOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"<PERSON>raian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"w-full p-3 sm:p-4 rounded-2xl bg-gradient-to-r from-indigo-100 to-blue-100 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Package size={18} className=\"text-primary\" />\r\n          Komponen\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdkomponen */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Komponen\r\n              </label>\r\n              <Kdkomponen\r\n                value={komponen}\r\n                onChange={setKomponen}\r\n                kdoutput={output}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Komponen\"\r\n                status=\"pilihkomponen\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={komponenkondisi || \"\"}\r\n                onChange={(e) =>\r\n                  setKomponenkondisi && setKomponenkondisi(e.target.value)\r\n                }\r\n              />\r\n              {/* Helper text - show immediately below on mobile */}\r\n              <p className=\"text-xs text-gray-500 xl:hidden\">\r\n                untuk banyak kode pisahkan dengan koma, gunakan tanda ! di depan\r\n                untuk exclude\r\n              </p>\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"misalkan: belanja\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katakomponen || \"\"}\r\n                onChange={(e) =>\r\n                  setKatakomponen && setKatakomponen(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={komponenradio ? [komponenradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setKomponenradio) {\r\n                    setKomponenradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {KomponenOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdkomponen */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Helper text under Kondisi */}\r\n            <div className=\"flex-1\">\r\n              <p className=\"text-xs text-gray-500\">\r\n                untuk banyak kode pisahkan dengan koma, gunakan tanda ! di depan\r\n                untuk exclude\r\n              </p>\r\n            </div>\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KomponenFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,YAAY,EAAE;IACtC,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,IAAI,EACJ,MAAM,EACN,OAAO,EACP,IAAI,EACJ,MAAM,EACN,OAAO,EACR,GAAG,gBAAgB,CAAC;IAErB,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,aAAa;YACf,YAAY;QACd;IACF,GAAG;QAAC;QAAM;QAAQ;QAAS;QAAM;QAAQ;QAAS;KAAY;IAE9D,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,wMAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAiB;;;;;;;8BAKhD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,iLAAA,CAAA,UAAU;4CACT,OAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;;;;;;;;;;;;8CAKX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,mBAAmB;4CAC1B,UAAU,CAAC,IACT,sBAAsB,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;sDAI3D,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,gBAAgB;4CACvB,UAAU,CAAC,IACT,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAMvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,gBAAgB;gDAAC;6CAAc,GAAG;gDAAC;6CAAI;4CACrD,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,kBAAkB;oDACpB,iBAAiB;gDACnB;4CACF;4CACA,sBAAsB;sDAErB,gBAAgB,GAAG,CAAC,CAAC,oBACpB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 3846, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/SubkomponenFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Package } from \"lucide-react\";\r\nimport Kdsubkomponen from \"@/components/features/reference/referensi_inquiryMod/Kdsubkomponen\";\r\n\r\nconst SubkomponenFilter = ({ inquiryState }) => {\r\n  const {\r\n    skomponen,\r\n    setSkomponen,\r\n    skomponenkondisi,\r\n    setSkomponenkondisi,\r\n    kataskomponen,\r\n    setKataskomponen,\r\n    skomponenradio,\r\n    setSkomponenradio,\r\n    dept,\r\n    kdunit,\r\n    program,\r\n    giat,\r\n    output,\r\n    soutput,\r\n    komponen,\r\n  } = inquiryState || {};\r\n\r\n  // When parent filters change, reset child selection\r\n  React.useEffect(() => {\r\n    if (setSkomponen) {\r\n      setSkomponen(\"XX\");\r\n    }\r\n  }, [dept, kdunit, program, giat, output, soutput, komponen, setSkomponen]);\r\n\r\n  const SubkomponenOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"<PERSON><PERSON><PERSON>\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"w-full p-3 sm:p-4 rounded-2xl bg-gradient-to-r from-teal-100 to-cyan-100 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Package size={18} className=\"text-primary\" />\r\n          Sub-komponen\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdsubkomponen */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Sub-komponen\r\n              </label>\r\n              <Kdsubkomponen\r\n                value={skomponen}\r\n                onChange={setSkomponen}\r\n                kdkomponen={komponen}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Sub-komponen\"\r\n                status=\"pilihsubkomponen\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={skomponenkondisi || \"\"}\r\n                onChange={(e) =>\r\n                  setSkomponenkondisi && setSkomponenkondisi(e.target.value)\r\n                }\r\n              />\r\n              {/* Helper text - show immediately below on mobile */}\r\n              <p className=\"text-xs text-gray-500 xl:hidden\">\r\n                untuk banyak kode pisahkan dengan koma, gunakan tanda ! di depan\r\n                untuk exclude\r\n              </p>\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"misalkan: belanja\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kataskomponen || \"\"}\r\n                onChange={(e) =>\r\n                  setKataskomponen && setKataskomponen(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={skomponenradio ? [skomponenradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setSkomponenradio) {\r\n                    setSkomponenradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {SubkomponenOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdsubkomponen */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Helper text under Kondisi */}\r\n            <div className=\"flex-1\">\r\n              <p className=\"text-xs text-gray-500\">\r\n                untuk banyak kode pisahkan dengan koma, gunakan tanda ! di depan\r\n                untuk exclude\r\n              </p>\r\n            </div>\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SubkomponenFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,oBAAoB,CAAC,EAAE,YAAY,EAAE;IACzC,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,IAAI,EACJ,MAAM,EACN,OAAO,EACP,IAAI,EACJ,MAAM,EACN,OAAO,EACP,QAAQ,EACT,GAAG,gBAAgB,CAAC;IAErB,oDAAoD;IACpD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,cAAc;YAChB,aAAa;QACf;IACF,GAAG;QAAC;QAAM;QAAQ;QAAS;QAAM;QAAQ;QAAS;QAAU;KAAa;IAEzE,MAAM,qBAAqB;QACzB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,wMAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAiB;;;;;;;8BAKhD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,oLAAA,CAAA,UAAa;4CACZ,OAAO;4CACP,UAAU;4CACV,YAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;;;;;;;;;;;;8CAKX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,oBAAoB;4CAC3B,UAAU,CAAC,IACT,uBAAuB,oBAAoB,EAAE,MAAM,CAAC,KAAK;;;;;;sDAI7D,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAOjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,iBAAiB;4CACxB,UAAU,CAAC,IACT,oBAAoB,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAMzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,iBAAiB;gDAAC;6CAAe,GAAG;gDAAC;6CAAI;4CACvD,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,mBAAmB;oDACrB,kBAAkB;gDACpB;4CACF;4CACA,sBAAsB;sDAErB,mBAAmB,GAAG,CAAC,CAAC,oBACvB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 4136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/AkunFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Settings, Info } from \"lucide-react\";\r\nimport Kdakun from \"@/components/features/reference/referensi_inquiryMod/Kdakun\";\r\n\r\nconst AkunFilter = ({ inquiryState }) => {\r\n  const {\r\n    akun,\r\n    setAkun,\r\n    akunkondisi,\r\n    setAkunkondisi,\r\n    kataakun,\r\n    setKataakun,\r\n    akunradio,\r\n    setAkunradio,\r\n    jenlap,\r\n    jenis,\r\n    kdakun, // <-- get kdakun from inquiryState\r\n    setAkunType, // <-- add this to store type\r\n    setAkunValue, // <-- add this to store processed value\r\n    setAkunSql, // <-- add this to store SQL expr\r\n  } = inquiryState;\r\n\r\n  // Disable logic: kondisi disables kata, kata disables kondisi, pilih akun is never disabled\r\n  const hasKondisiFilter = akunkondisi && akunkondisi.trim() !== \"\";\r\n  const hasKataFilter = kataakun && kataakun.trim() !== \"\";\r\n  const isKondisiDisabled = hasKataFilter;\r\n  const isKataDisabled = hasKondisiFilter;\r\n\r\n  const AkunOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Settings size={20} className=\"ml-4 text-secondary\" />\r\n          Akun\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Pilih Akun\r\n                </label>\r\n              </div>\r\n              <Kdakun\r\n                value={akun && akun.type ? akun.type : akun}\r\n                onChange={(obj) => {\r\n                  setAkun(obj); // store the whole object for backward compatibility\r\n                  if (setAkunType) setAkunType(obj.type);\r\n                  if (setAkunValue) setAkunValue(obj.value);\r\n                  if (setAkunSql) setAkunSql(obj.sql);\r\n                }}\r\n                jenlap={jenlap}\r\n                jenis={jenis}\r\n                kdakun={kdakun}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Akun\"\r\n                status=\"pilihakun\"\r\n                isDisabled={false}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setAkunkondisi && setAkunkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={akunkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setAkunkondisi && setAkunkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKataakun && setKataakun(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: gaji\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kataakun || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKataakun && setKataakun(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={akunradio ? [akunradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setAkunradio) {\r\n                    setAkunradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {AkunOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Selection */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AkunFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAJA;;;;;;AAMA,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE;IAClC,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,MAAM,EACN,KAAK,EACL,MAAM,EACN,WAAW,EACX,YAAY,EACZ,UAAU,EACX,GAAG;IAEJ,4FAA4F;IAC5F,MAAM,mBAAmB,eAAe,YAAY,IAAI,OAAO;IAC/D,MAAM,gBAAgB,YAAY,SAAS,IAAI,OAAO;IACtD,MAAM,oBAAoB;IAC1B,MAAM,iBAAiB;IAEvB,MAAM,cAAc;QAClB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;;;;;;sDAIvD,8OAAC,6KAAA,CAAA,UAAM;4CACL,OAAO,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;4CACvC,UAAU,CAAC;gDACT,QAAQ,MAAM,oDAAoD;gDAClE,IAAI,aAAa,YAAY,IAAI,IAAI;gDACrC,IAAI,cAAc,aAAa,IAAI,KAAK;gDACxC,IAAI,YAAY,WAAW,IAAI,GAAG;4CACpC;4CACA,QAAQ;4CACR,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,kCACC,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,YAAY;4CACZ,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAMrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,+BACC,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,eAAe,YAAY;8DAC3C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,YAAY;4CACnB,YAAY;4CACZ,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,YAAY;gDAAC;6CAAU,GAAG;gDAAC;6CAAI;4CAC7C,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,cAAc;oDAChB,aAAa;gDACf;4CACF;4CACA,sBAAsB;sDAErB,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 4506, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/SumberDanaFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Settings, Info } from \"lucide-react\";\r\nimport Kdsdana from \"@/components/features/reference/referensi_inquiryMod/Kdsdana\";\r\n\r\nconst SumberdanaFilter = ({ inquiryState }) => {\r\n  const {\r\n    sdana,\r\n    setSdana,\r\n    sdanakondisi,\r\n    setSdanakondisi,\r\n    katasdana,\r\n    setKatasdana,\r\n    sdanaradio,\r\n    setSdanaradio,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katasdana && katasdana.trim() !== \"\";\r\n  const hasKondisiFilter = sdanakondisi && sdanakondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    sdana && sdana !== \"XXX\" && sdana !== \"XX\" && sdana !== \"XXX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  const SdanaOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Settings size={20} className=\"ml-4 text-secondary\" />\r\n          Sumber Dana\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Pilih Sumber Dana\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setSdana && setSdana(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdsdana\r\n                value={sdana}\r\n                onChange={setSdana}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Sumber Dana\"\r\n                status=\"pilihsdana\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setSdanakondisi && setSdanakondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={sdanakondisi || \"\"}\r\n                onChange={(e) =>\r\n                  setSdanakondisi && setSdanakondisi(e.target.value)\r\n                }\r\n                isDisabled={isKondisiDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setKatasdana && setKatasdana(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: rupiah\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katasdana || \"\"}\r\n                onChange={(e) => setKatasdana && setKatasdana(e.target.value)}\r\n                isDisabled={isKataDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={sdanaradio ? [sdanaradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setSdanaradio) {\r\n                    setSdanaradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {SdanaOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Selection */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SumberdanaFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAJA;;;;;;AAMA,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAE;IACxC,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACd,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,aAAa,UAAU,IAAI,OAAO;IACxD,MAAM,mBAAmB,gBAAgB,aAAa,IAAI,OAAO;IACjE,MAAM,iBACJ,SAAS,UAAU,SAAS,UAAU,QAAQ,UAAU;IAE1D,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,MAAM,eAAe;QACnB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,YAAY,SAAS;8DACrC;;;;;;;;;;;;sDAKL,8OAAC,8KAAA,CAAA,UAAO;4CACN,OAAO;4CACP,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,mBAAmB,gBAAgB;8DACnD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,gBAAgB;4CACvB,UAAU,CAAC,IACT,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAEnD,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,gBAAgB,aAAa;8DAC7C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,aAAa;4CACpB,UAAU,CAAC,IAAM,gBAAgB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5D,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,aAAa;gDAAC;6CAAW,GAAG;gDAAC;6CAAI;4CAC/C,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,eAAe;oDACjB,cAAc;gDAChB;4CACF;4CACA,sBAAsB;sDAErB,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 4885, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/RegisterFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Settings, Info } from \"lucide-react\";\r\nimport Kdregister from \"@/components/features/reference/referensi_inquiryMod/Kdregister\";\r\n\r\nconst RegisterFilter = ({ inquiryState }) => {\r\n  const {\r\n    register,\r\n    setRegister,\r\n    registerkondisi,\r\n    setRegisterkondisi,\r\n    kataregister,\r\n    setKataregister,\r\n    registerradio,\r\n    setRegisterradio,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = kataregister && kataregister.trim() !== \"\";\r\n  const hasKondisiFilter = registerkondisi && registerkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    register && register !== \"XXX\" && register !== \"XX\" && register !== \"XXX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  const RegisterOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Settings size={20} className=\"ml-4 text-secondary\" />\r\n          Register\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Pilih Register\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setRegister && setRegister(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdregister\r\n                value={register}\r\n                onChange={setRegister}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Register\"\r\n                status=\"pilihregister\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setRegisterkondisi && setRegisterkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={registerkondisi || \"\"}\r\n                onChange={(e) =>\r\n                  setRegisterkondisi && setRegisterkondisi(e.target.value)\r\n                }\r\n                isDisabled={isKondisiDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label className=\"text-sm font-medium text-gray-700\">\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onClick={() => setKataregister && setKataregister(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: register\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kataregister || \"\"}\r\n                onChange={(e) =>\r\n                  setKataregister && setKataregister(e.target.value)\r\n                }\r\n                isDisabled={isKataDisabled}\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={registerradio ? [registerradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setRegisterradio) {\r\n                    setRegisterradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {RegisterOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Selection */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RegisterFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAJA;;;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,YAAY,EAAE;IACtC,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EACjB,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,gBAAgB,aAAa,IAAI,OAAO;IAC9D,MAAM,mBAAmB,mBAAmB,gBAAgB,IAAI,OAAO;IACvE,MAAM,iBACJ,YAAY,aAAa,SAAS,aAAa,QAAQ,aAAa;IAEtE,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,eAAe,YAAY;8DAC3C;;;;;;;;;;;;sDAKL,8OAAC,iLAAA,CAAA,UAAU;4CACT,OAAO;4CACP,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,sBAAsB,mBAAmB;8DACzD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,mBAAmB;4CAC1B,UAAU,CAAC,IACT,sBAAsB,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CAEzD,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAoC;;;;;;gDAGpD,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,mBAAmB,gBAAgB;8DACnD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,gBAAgB;4CACvB,UAAU,CAAC,IACT,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAEnD,YAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAc,gBAAgB;gDAAC;6CAAc,GAAG;gDAAC;6CAAI;4CACrD,mBAAmB,CAAC;gDAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,kBAAkB;oDACpB,iBAAiB;gDACnB;4CACF;4CACA,sBAAsB;sDAErB,gBAAgB,GAAG,CAAC,CAAC,oBACpB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 5264, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/InflasiFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { TrendingUp } from \"lucide-react\";\r\nimport JenisInflasiInquiry from \"@/components/features/reference/referensi_inquiryMod/JenisInflasiInquiry\";\r\n\r\nconst InflasiFilter = ({ inquiryState }) => {\r\n  const { Inflasi, setInflasi, inflasiradio, setInflasiradio } = inquiryState;\r\n\r\n  const InflasiOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"<PERSON><PERSON><PERSON>\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <TrendingUp size={20} className=\"ml-4 text-secondary\" />\r\n          Jenis Inflasi\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Jenis Inflasi\r\n              </label>\r\n              <JenisInflasiInquiry\r\n                value={Inflasi}\r\n                onChange={setInflasi}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Jenis Inflasi\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Inflasi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Inflasi\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Inflasi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Inflasi\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={inflasiradio ? [inflasiradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setInflasiradio) {\r\n                    setInflasiradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {InflasiOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InflasiFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE;IACrC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG;IAE/D,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,kNAAA,CAAA,aAAU;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAK1D,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,0LAAA,CAAA,UAAmB;wCAClB,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,eAAe;4CAAC;yCAAa,GAAG;4CAAC;yCAAI;wCACnD,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,iBAAiB;gDACnB,gBAAgB;4CAClB;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,eAAe,GAAG,CAAC,CAAC,oBACnB,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 5485, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/IknFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Building } from \"lucide-react\";\r\nimport JenisIkn from \"@/components/features/reference/referensi_inquiryMod/JenisIkn\";\r\n\r\nconst IknFilter = ({ inquiryState }) => {\r\n  const { Ikn, setIkn, iknradio, setIknradio } = inquiryState;\r\n\r\n  const IknOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Building size={20} className=\"ml-4 text-secondary\" />\r\n          Jenis IKN\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Jenis IKN\r\n              </label>\r\n              <JenisIkn\r\n                value={Ikn}\r\n                onChange={setIkn}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Jenis IKN\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for IKN */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk IKN\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for IKN */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk IKN\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={iknradio ? [iknradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setIknradio) {\r\n                    setIknradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {IknOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default IknFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,YAAY,CAAC,EAAE,YAAY,EAAE;IACjC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAE/C,MAAM,aAAa;QACjB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,+KAAA,CAAA,UAAQ;wCACP,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,WAAW;4CAAC;yCAAS,GAAG;4CAAC;yCAAI;wCAC3C,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,aAAa;gDACf,YAAY;4CACd;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 5706, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KemiskinanFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Heart } from \"lucide-react\";\r\nimport JenisMiskin from \"@/components/features/reference/referensi_inquiryMod/JenisMiskin\";\r\n\r\nconst KemiskinanFilter = ({ inquiryState }) => {\r\n  const { Miskin, setMiskin, kemiskinanradio, setKemiskinanradio } =\r\n    inquiryState;\r\n\r\n  const MiskinOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Heart size={20} className=\"ml-4 text-secondary\" />\r\n          Jenis Kemiskinan\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Jenis Kemiskinan\r\n              </label>\r\n              <JenisMiskin\r\n                value={Miskin}\r\n                onChange={setMiskin}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Jenis Kemiskinan\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Kemiskinan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Kemiskinan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Kemiskinan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Kemiskinan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={kemiskinanradio ? [kemiskinanradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setKemiskinanradio) {\r\n                    setKemiskinanradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {MiskinOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KemiskinanFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAE;IACxC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAC9D;IAEF,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,oMAAA,CAAA,QAAK;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKrD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,kLAAA,CAAA,UAAW;wCACV,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,kBAAkB;4CAAC;yCAAgB,GAAG;4CAAC;yCAAI;wCACzD,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,oBAAoB;gDACtB,mBAAmB;4CACrB;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 5927, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/PanganFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Wheat } from \"lucide-react\";\r\nimport JenisPangan from \"@/components/features/reference/referensi_inquiryMod/JenisPangan\";\r\n\r\nconst PanganFilter = ({ inquiryState }) => {\r\n  const { Pangan, setPangan, panganradio, setPanganradio } = inquiryState;\r\n\r\n  const PanganOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Wheat size={20} className=\"ml-4 text-secondary\" />\r\n          Ketahanan Pangan\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Ketahanan Pangan\r\n              </label>\r\n              <JenisPangan\r\n                value={Pangan}\r\n                onChange={setPangan}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Ketahanan Pangan\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Pangan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Ketahanan Pangan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Pangan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Ketahanan Pangan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={panganradio ? [panganradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setPanganradio) {\r\n                    setPanganradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {PanganOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PanganFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG;IAE3D,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,oMAAA,CAAA,QAAK;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKrD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,kLAAA,CAAA,UAAW;wCACV,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,cAAc;4CAAC;yCAAY,GAAG;4CAAC;yCAAI;wCACjD,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,gBAAgB;gDAClB,eAAe;4CACjB;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 6148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/StuntingFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Baby } from \"lucide-react\";\r\nimport JenisStuntingInquiry from \"@/components/features/reference/referensi_inquiryMod/JenisStuntingInquiry\";\r\n\r\nconst StuntingFilter = ({ inquiryState }) => {\r\n  const { Stunting, setStunting, stuntingradio, setStuntingradio } =\r\n    inquiryState;\r\n\r\n  const StuntingOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Baby size={20} className=\"ml-4 text-secondary\" />\r\n          Tematik Stunting\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Tematik Stunting\r\n              </label>\r\n              <JenisStuntingInquiry\r\n                value={Stunting}\r\n                onChange={setStunting}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Tematik Stunting\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Stunting */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Tematik Stunting\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Stunting */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Tematik Stunting\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={stuntingradio ? [stuntingradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setStuntingradio) {\r\n                    setStuntingradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {StuntingOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StuntingFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,YAAY,EAAE;IACtC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAC9D;IAEF,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,2LAAA,CAAA,UAAoB;wCACnB,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,gBAAgB;4CAAC;yCAAc,GAAG;4CAAC;yCAAI;wCACrD,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,kBAAkB;gDACpB,iBAAiB;4CACnB;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,gBAAgB,GAAG,CAAC,CAAC,oBACpB,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 6369, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/PemiluFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Vote } from \"lucide-react\";\r\nimport JenisPemilu from \"@/components/features/reference/referensi_inquiryMod/JenisPemilu\";\r\n\r\nconst PemiluFilter = ({ inquiryState }) => {\r\n  const { Pemilu, setPemilu, pemiluradio, setPemiluradio } = inquiryState;\r\n\r\n  const PemiluOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Vote size={20} className=\"ml-4 text-secondary\" />\r\n          Belanja Pemilu\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Jenis Pemilu\r\n              </label>\r\n              <JenisPemilu\r\n                value={Pemilu}\r\n                onChange={setPemilu}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Jenis Pemilu\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Pemilu */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Belanja Pemilu\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Pemilu */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Belanja Pemilu\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={pemiluradio ? [pemiluradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setPemiluradio) {\r\n                    setPemiluradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {PemiluOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PemiluFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG;IAE3D,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKpD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,kLAAA,CAAA,UAAW;wCACV,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,cAAc;4CAAC;yCAAY,GAAG;4CAAC;yCAAI;wCACjD,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,gBAAgB;gDAClB,eAAe;4CACjB;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 6589, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/PrinasFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Crown } from \"lucide-react\";\r\nimport KodePN from \"@/components/features/reference/referensi_inquiryMod/KdPN\";\r\n\r\nconst PrinasFilter = ({ inquiryState }) => {\r\n  const { PN, setPN, pnradio, setPnradio } = inquiryState;\r\n\r\n  const PNOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Crown size={20} className=\"ml-4 text-secondary\" />\r\n          Prioritas Nasional\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Prioritas Nasional\r\n              </label>\r\n              <KodePN\r\n                value={PN}\r\n                onChange={setPN}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Prioritas Nasional\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for PRI */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Prioritas Nasional\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for PN */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Prioritas Nasional\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={pnradio ? [pnradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setPnradio) {\r\n                    setPnradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {PNOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PrinasFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG;IAE3C,MAAM,YAAY;QAChB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,oMAAA,CAAA,QAAK;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKrD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,2KAAA,CAAA,UAAM;wCACL,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,UAAU;4CAAC;yCAAQ,GAAG;4CAAC;yCAAI;wCACzC,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,YAAY;gDACd,WAAW;4CACb;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,UAAU,GAAG,CAAC,CAAC,oBACd,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 6810, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/ProgrampriFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Target } from \"lucide-react\";\r\nimport KodePP from \"@/components/features/reference/referensi_inquiryMod/KdPP\";\r\n\r\nconst ProgrampriFilter = ({ inquiryState }) => {\r\n  const { PP, setPP, ppradio, setPpradio, PN } = inquiryState;\r\n\r\n  const PPOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Target size={20} className=\"ml-4 text-secondary\" />\r\n          Program Prioritas\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Program Prioritas\r\n              </label>\r\n              <KodePP\r\n                value={PP}\r\n                onChange={setPP}\r\n                kdPN={PN}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Program Prioritas\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Program Prioritas */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Program Prioritas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Program Prioritas */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Program Prioritas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={ppradio ? [ppradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setPpradio) {\r\n                    setPpradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {PPOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgrampriFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAE;IACxC,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG;IAE/C,MAAM,YAAY;QAChB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,2KAAA,CAAA,UAAM;wCACL,OAAO;wCACP,UAAU;wCACV,MAAM;wCACN,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,UAAU;4CAAC;yCAAQ,GAAG;4CAAC;yCAAI;wCACzC,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,YAAY;gDACd,WAAW;4CACb;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,UAAU,GAAG,CAAC,CAAC,oBACd,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 7032, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KegiatanpriFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Target } from \"lucide-react\";\r\nimport KodeKegPP from \"@/components/features/reference/referensi_inquiryMod/KdKegPP\";\r\n\r\nconst KegiatanpriFilter = ({ inquiryState }) => {\r\n  const {\r\n    kegiatanprioritas,\r\n    setKegiatanPrioritas,\r\n    kegiatanprioritasradio,\r\n    setKegiatanPrioritasRadio,\r\n    PP,\r\n    PN,\r\n    thang,\r\n  } = inquiryState;\r\n\r\n  // Debug: log kegiatanprioritas to ensure it updates on selection\r\n  React.useEffect(() => {\r\n    console.log(\"[KegiatanpriFilter] kegiatanprioritas:\", kegiatanprioritas);\r\n  }, [kegiatanprioritas]);\r\n\r\n  const KegiatanPrioritasOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Target size={20} className=\"ml-4 text-secondary\" />\r\n          Kegiatan Prioritas\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Kegiatan Prioritas\r\n              </label>\r\n              <KodeKegPP\r\n                value={kegiatanprioritas}\r\n                onChange={(val) => {\r\n                  setKegiatanPrioritas(val);\r\n                  // Debug: log value selected\r\n                  console.log(\r\n                    \"[KegiatanpriFilter] setKegiatanPrioritas called with:\",\r\n                    val\r\n                  );\r\n                }}\r\n                kdPN={PN}\r\n                kdPP={PP}\r\n                thang={thang}\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Kegiatan Prioritas */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Kegiatan Prioritas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Kegiatan Prioritas */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Kegiatan Prioritas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={\r\n                  kegiatanprioritasradio ? [kegiatanprioritasradio] : [\"1\"]\r\n                }\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setKegiatanPrioritasRadio) {\r\n                    setKegiatanPrioritasRadio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {KegiatanPrioritasOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KegiatanpriFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,oBAAoB,CAAC,EAAE,YAAY,EAAE;IACzC,MAAM,EACJ,iBAAiB,EACjB,oBAAoB,EACpB,sBAAsB,EACtB,yBAAyB,EACzB,EAAE,EACF,EAAE,EACF,KAAK,EACN,GAAG;IAEJ,iEAAiE;IACjE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,QAAQ,GAAG,CAAC,0CAA0C;IACxD,GAAG;QAAC;KAAkB;IAEtB,MAAM,2BAA2B;QAC/B;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,8KAAA,CAAA,UAAS;wCACR,OAAO;wCACP,UAAU,CAAC;4CACT,qBAAqB;4CACrB,4BAA4B;4CAC5B,QAAQ,GAAG,CACT,yDACA;wCAEJ;wCACA,MAAM;wCACN,MAAM;wCACN,OAAO;;;;;;;;;;;;0CAKX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cACE,yBAAyB;4CAAC;yCAAuB,GAAG;4CAAC;yCAAI;wCAE3D,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,2BAA2B;gDAC7B,0BAA0B;4CAC5B;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,yBAAyB,GAAG,CAAC,CAAC,oBAC7B,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 7263, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/ProyekpriFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Target } from \"lucide-react\";\r\nimport KodePRI from \"@/components/features/reference/referensi_inquiryMod/KdPRI\";\r\n\r\nconst PRIFilter = ({ inquiryState }) => {\r\n  const { PRI, setPRI, priradio, setPriradio, PN, PP, KegPP, thang } =\r\n    inquiryState;\r\n\r\n  const PRIOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Target size={20} className=\"ml-4 text-secondary\" />\r\n          Proyek Prioritas\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Proyek Prioritas\r\n              </label>\r\n              <KodePRI\r\n                value={PRI}\r\n                onChange={setPRI}\r\n                kdPN={PN}\r\n                kdPP={PP}\r\n                KegPP={KegPP}\r\n                thang={thang}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Proyek Prioritas\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for PRI */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Proyek Prioritas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for PRI */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Proyek Prioritas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={priradio ? [priradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setPriradio) {\r\n                    setPriradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {PRIOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PRIFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,YAAY,CAAC,EAAE,YAAY,EAAE;IACjC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAChE;IAEF,MAAM,aAAa;QACjB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4KAAA,CAAA,UAAO;wCACN,OAAO;wCACP,UAAU;wCACV,MAAM;wCACN,MAAM;wCACN,OAAO;wCACP,OAAO;wCACP,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,WAAW;4CAAC;yCAAS,GAAG;4CAAC;yCAAI;wCAC3C,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,aAAa;gDACf,YAAY;4CACd;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 7488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/MajorprFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Briefcase } from \"lucide-react\";\r\nimport JenisMP from \"@/components/features/reference/referensi_inquiryMod/JenisMP\";\r\n\r\nconst MajorprFilter = ({ inquiryState }) => {\r\n  const { MP, setMP, mpradio, setMpradio } = inquiryState;\r\n\r\n  const MPOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Briefcase size={20} className=\"ml-4 text-secondary\" />\r\n          Major Project\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Major Project\r\n              </label>\r\n              <JenisMP\r\n                value={MP}\r\n                onChange={setMP}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Major Project\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Major Project */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Major Project\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Major Project */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Major Project\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={mpradio ? [mpradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setMpradio) {\r\n                    setMpradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {MPOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MajorprFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE;IACrC,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG;IAE3C,MAAM,YAAY;QAChB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,4MAAA,CAAA,YAAS;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,8KAAA,CAAA,UAAO;wCACN,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,UAAU;4CAAC;yCAAQ,GAAG;4CAAC;yCAAI;wCACzC,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,YAAY;gDACd,WAAW;4CACb;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,UAAU,GAAG,CAAC,CAAC,oBACd,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 7709, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/TematikFilter.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { Input, Select, SelectItem } from \"@heroui/react\";\r\nimport { Layers } from \"lucide-react\";\r\nimport JenisTEMA from \"@/components/features/reference/referensi_inquiryMod/JenisTEMA\";\r\n\r\nconst TematikFilter = ({ inquiryState }) => {\r\n  const { Tema, setTema, temaradio, setTemaradio } = inquiryState;\r\n\r\n  const TemaOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"<PERSON><PERSON>\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Layers size={20} className=\"ml-4 text-secondary\" />\r\n          Tematik\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Selection Component */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Tematik\r\n              </label>\r\n              <JenisTEMA\r\n                value={Tema}\r\n                onChange={setTema}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Tematik\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kondisi - Disabled for Tematik */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Masukkan Kondisi\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Tematik\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Kata - Disabled for Tematik */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-400\">\r\n                Mengandung Kata\r\n              </label>\r\n              <Input\r\n                placeholder=\"Tidak tersedia untuk Tematik\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                isDisabled\r\n                value=\"\"\r\n              />\r\n            </div>\r\n\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                selectedKeys={temaradio ? [temaradio] : [\"1\"]}\r\n                onSelectionChange={(keys) => {\r\n                  const selected = Array.from(keys)[0];\r\n                  if (setTemaradio) {\r\n                    setTemaradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n                size=\"sm\"\r\n                className=\"w-full min-w-0\"\r\n              >\r\n                {TemaOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TematikFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE;IACrC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;IAEnD,MAAM,cAAc;QAClB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKtD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,gLAAA,CAAA,UAAS;wCACR,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,MAAK;wCACL,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,yMAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,MAAK;wCACL,UAAU;wCACV,OAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDAGrD,8OAAC,4MAAA,CAAA,SAAM;wCACL,cAAc,YAAY;4CAAC;yCAAU,GAAG;4CAAC;yCAAI;wCAC7C,mBAAmB,CAAC;4CAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;4CACpC,IAAI,cAAc;gDAChB,aAAa;4CACf;wCACF;wCACA,sBAAsB;wCACtB,MAAK;wCACL,WAAU;kDAET,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,4NAAA,CAAA,aAAU;gDAAiB,WAAW,IAAI,KAAK;0DAC7C,IAAI,KAAK;+CADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe", "debugId": null}}, {"offset": {"line": 7930, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/DekonFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Landmark, Info } from \"lucide-react\";\r\nimport Kddekon from \"@/components/features/reference/referensi_inquiryMod/Kddekon\";\r\n\r\nconst DekonFilter = ({ inquiryState }) => {\r\n  // Use inquiryState for dekon, dekonkondisi, katadekon, dekonradio\r\n  const {\r\n    dekon,\r\n    setDekon,\r\n    dekonkondisi,\r\n    setDekonkondisi,\r\n    katadekon,\r\n    setKatadekon,\r\n    dekonradio,\r\n    setDekonradio,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katadekon && katadekon.trim() !== \"\";\r\n  const hasKondisiFilter = dekonkondisi && dekonkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    dekon &&\r\n    dekon !== \"XXX\" &&\r\n    dekon !== \"XX\" &&\r\n    dekon !== \"000\" &&\r\n    dekon.trim() !== \"\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isKddekonDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n  // 4 display options, matching the other filters\r\n  const DekonOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Landmark size={20} className=\"ml-4 text-secondary\" />\r\n          Kewenangan\r\n        </h6>{\" \"}\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kddekon */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKddekonDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Kewenangan\r\n                </label>\r\n\r\n                {hasPilihFilter && !isKddekonDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setDekon && setDekon(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kddekon\r\n                value={dekon}\r\n                onChange={setDekon}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status=\"pilihdekon\"\r\n                isDisabled={isKddekonDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setDekonkondisi && setDekonkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: DK,TP,UB, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={dekonkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setDekonkondisi && setDekonkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatadekon && setKatadekon(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: dekonsentrasi\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katadekon || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKatadekon && setKatadekon(e.target.value)}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>{\" \"}\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={new Set([dekonradio])}\r\n                onSelectionChange={(keys) => {\r\n                  // HeroUI Select passes a Set object\r\n                  const selected = Array.from(keys)[0];\r\n                  if (selected) {\r\n                    setDekonradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {DekonOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kddekon */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DekonFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,cAAc,CAAC,EAAE,YAAY,EAAE;IACnC,kEAAkE;IAClE,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACd,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,aAAa,UAAU,IAAI,OAAO;IACxD,MAAM,mBAAmB,gBAAgB,aAAa,IAAI,OAAO;IACjE,MAAM,iBACJ,SACA,UAAU,SACV,UAAU,QACV,UAAU,SACV,MAAM,IAAI,OAAO;IAEnB,8CAA8C;IAC9C,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAC3C,gDAAgD;IAChD,MAAM,eAAe;QACnB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;gBAElD;8BAEN,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;8DACH;;;;;;gDAIA,kBAAkB,CAAC,mCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,YAAY,SAAS;8DACrC;;;;;;;;;;;;sDAKL,8OAAC,8KAAA,CAAA,UAAO;4CACN,OAAO;4CACP,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,mBAAmB,gBAAgB;8DACnD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,gBAAgB;4CACvB,YAAY;4CACZ,UAAU,CAAC,IACT,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,gBAAgB,aAAa;8DAC7C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,aAAa;4CACpB,YAAY;4CACZ,UAAU,CAAC,IAAM,gBAAgB,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;wCAE5C;sDACT,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc,IAAI,IAAI;gDAAC;6CAAW;4CAClC,mBAAmB,CAAC;gDAClB,oCAAoC;gDACpC,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;gDACpC,IAAI,UAAU;oDACZ,cAAc;gDAChB;4CACF;4CACA,sBAAsB;sDAErB,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 8313, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KabkotaFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Map, Info } from \"lucide-react\";\r\nimport Kdkabkota from \"@/components/features/reference/referensi_inquiryMod/Kdkabkota\";\r\n\r\nconst KabkotaFilter = ({ inquiryState }) => {\r\n  const {\r\n    kabkota,\r\n    setKabkota,\r\n    prov,\r\n    kabkotakondisi,\r\n    setKabkotakondisi,\r\n    katakabkota,\r\n    setKatakabkota,\r\n    kabkotaradio,\r\n    setKabkotaradio,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katakabkota && katakabkota.trim() !== \"\";\r\n  const hasKondisiFilter = kabkotakondisi && kabkotakondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    kabkota && kabkota !== \"XXX\" && kabkota !== \"XX\" && kabkota !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter; // When the province changes, reset the selected kabkota to \"Semua Kabupaten/Kota\"\r\n  React.useEffect(() => {\r\n    if (setKabkota) {\r\n      setKabkota(\"XX\");\r\n    }\r\n  }, [prov, setKabkota]);\r\n\r\n  const KabkotaOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}{\" \"}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Map size={20} className=\"ml-4 text-secondary\" />\r\n          Kabupaten/Kota\r\n        </h6>\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdkabkota */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isPilihDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Kabupaten/Kota\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKabkota && setKabkota(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdkabkota\r\n                value={kabkota}\r\n                onChange={\r\n                  setKabkota || (() => console.warn(\"setKabkota is undefined\"))\r\n                }\r\n                kdlokasi={prov}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Kabupaten/Kota\"\r\n                status=\"pilihkdkabkota\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs flex-shrink-0\"\r\n                    onPress={() => setKabkotakondisi && setKabkotakondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n\r\n              <Input\r\n                placeholder=\"misalkan: 01,02,03, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kabkotakondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setKabkotakondisi && setKabkotakondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatakabkota && setKatakabkota(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: jakarta\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katakabkota || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) =>\r\n                  setKatakabkota && setKatakabkota(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>{\" \"}\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={[kabkotaradio || \"1\"]}\r\n                onSelectionChange={(key) => {\r\n                  let selected = key;\r\n                  if (key && typeof key !== \"string\" && key.size) {\r\n                    selected = Array.from(key)[0];\r\n                  }\r\n\r\n                  // Clean up the weird $.X format that HeroUI sometimes uses\r\n                  if (\r\n                    typeof selected === \"string\" &&\r\n                    selected.startsWith(\"$.\")\r\n                  ) {\r\n                    selected = selected.replace(\"$.\", \"\");\r\n                  }\r\n\r\n                  if (!selected) {\r\n                    setKabkotaradio && setKabkotaradio(\"1\");\r\n                    return;\r\n                  }\r\n\r\n                  if (setKabkotaradio) {\r\n                    setKabkotaradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {KabkotaOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kddept */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KabkotaFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE;IACrC,MAAM,EACJ,OAAO,EACP,UAAU,EACV,IAAI,EACJ,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EAChB,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,eAAe,YAAY,IAAI,OAAO;IAC5D,MAAM,mBAAmB,kBAAkB,eAAe,IAAI,OAAO;IACrE,MAAM,iBACJ,WAAW,YAAY,SAAS,YAAY,QAAQ,YAAY;IAElE,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB,gBAAgB,kFAAkF;IAC7I,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,YAAY;YACd,WAAW;QACb;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IACD,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;gBACgD;8BAC7D,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,gMAAA,CAAA,MAAG;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAInD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,kBAAkB,kBAAkB,iBACpC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,cAAc,WAAW;8DACzC;;;;;;;;;;;;sDAKL,8OAAC,gLAAA,CAAA,UAAS;4CACR,OAAO;4CACP,UACE,cAAc,CAAC,IAAM,QAAQ,IAAI,CAAC,0BAA0B;4CAE9D,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAKjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,qBAAqB,kBAAkB;8DACvD;;;;;;;;;;;;sDAML,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,kBAAkB;4CACzB,YAAY;4CACZ,UAAU,CAAC,IACT,qBAAqB,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,YAAY;4CACZ,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;wCAE5C;sDACT,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc;gDAAC,gBAAgB;6CAAI;4CACnC,mBAAmB,CAAC;gDAClB,IAAI,WAAW;gDACf,IAAI,OAAO,OAAO,QAAQ,YAAY,IAAI,IAAI,EAAE;oDAC9C,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gDAC/B;gDAEA,2DAA2D;gDAC3D,IACE,OAAO,aAAa,YACpB,SAAS,UAAU,CAAC,OACpB;oDACA,WAAW,SAAS,OAAO,CAAC,MAAM;gDACpC;gDAEA,IAAI,CAAC,UAAU;oDACb,mBAAmB,gBAAgB;oDACnC;gDACF;gDAEA,IAAI,iBAAiB;oDACnB,gBAAgB;gDAClB;4CACF;4CACA,sBAAsB;sDAErB,eAAe,GAAG,CAAC,CAAC,oBACnB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAQlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 8714, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KanwilFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Landmark, Info } from \"lucide-react\";\r\nimport Kdkanwil from \"@/components/features/reference/referensi_inquiryMod/Kdkanwil\";\r\n\r\nconst KanwilFilter = ({ inquiryState, status }) => {\r\n  // Use inquiryState for kanwil, kanwilradio, kanwilkondisi, katakanwil\r\n  const {\r\n    kanwil,\r\n    setKanwil,\r\n    prov, // Province selection for filtering kanwil\r\n    kanwilradio,\r\n    setKanwilradio,\r\n    kanwilkondisi,\r\n    setKanwilkondisi,\r\n    katakanwil,\r\n    setKatakanwil,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katakanwil && katakanwil.trim() !== \"\";\r\n  const hasKondisiFilter = kanwilkondisi && kanwilkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    kanwil && kanwil !== \"XXX\" && kanwil !== \"XX\" && kanwil !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  // When the province changes, reset the selected kanwil to \"Semua Kanwil\"\r\n  React.useEffect(() => {\r\n    if (setKanwil) {\r\n      setKanwil(\"XX\");\r\n    }\r\n  }, [prov, setKanwil]);\r\n\r\n  const KanwilOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Landmark size={20} className=\"ml-4 text-secondary\" />\r\n          Kanwil\r\n        </h6>\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdkanwil */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isPilihDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Kanwil\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKanwil && setKanwil(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdkanwil\r\n                value={kanwil}\r\n                onChange={setKanwil}\r\n                kdlokasi={prov} // Pass province selection for filtering\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                status=\"pilihkanwil\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKanwilkondisi && setKanwilkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kanwilkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setKanwilkondisi && setKanwilkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatakanwil && setKatakanwil(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: jakarta\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katakanwil || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKatakanwil && setKatakanwil(e.target.value)}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={\r\n                  kanwilradio ? new Set([kanwilradio]) : new Set([\"1\"])\r\n                }\r\n                onSelectionChange={(key) => {\r\n                  let selected = key;\r\n                  if (key && typeof key !== \"string\" && key.size) {\r\n                    selected = Array.from(key)[0];\r\n                  }\r\n                  if (!selected) {\r\n                    setKanwilradio && setKanwilradio(\"1\");\r\n                    return;\r\n                  }\r\n                  setKanwilradio && setKanwilradio(selected);\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {KanwilOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdkanwil */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KanwilFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE;IAC5C,sEAAsE;IACtE,MAAM,EACJ,MAAM,EACN,SAAS,EACT,IAAI,EACJ,WAAW,EACX,cAAc,EACd,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACd,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,cAAc,WAAW,IAAI,OAAO;IAC1D,MAAM,mBAAmB,iBAAiB,cAAc,IAAI,OAAO;IACnE,MAAM,iBACJ,UAAU,WAAW,SAAS,WAAW,QAAQ,WAAW;IAE9D,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,yEAAyE;IACzE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,WAAW;YACb,UAAU;QACZ;IACF,GAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAIxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,kBAAkB,kBAAkB,iBACpC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,aAAa,UAAU;8DACvC;;;;;;;;;;;;sDAKL,8OAAC,+KAAA,CAAA,UAAQ;4CACP,OAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,oBAAoB,iBAAiB;8DACrD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,iBAAiB;4CACxB,YAAY;4CACZ,UAAU,CAAC,IACT,oBAAoB,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,iBAAiB,cAAc;8DAC/C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,cAAc;4CACrB,YAAY;4CACZ,UAAU,CAAC,IAAM,iBAAiB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIlE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cACE,cAAc,IAAI,IAAI;gDAAC;6CAAY,IAAI,IAAI,IAAI;gDAAC;6CAAI;4CAEtD,mBAAmB,CAAC;gDAClB,IAAI,WAAW;gDACf,IAAI,OAAO,OAAO,QAAQ,YAAY,IAAI,IAAI,EAAE;oDAC9C,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gDAC/B;gDACA,IAAI,CAAC,UAAU;oDACb,kBAAkB,eAAe;oDACjC;gDACF;gDACA,kBAAkB,eAAe;4CACnC;4CACA,sBAAsB;sDAErB,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAQlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 9110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/KppnFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Banknote, Info } from \"lucide-react\";\r\nimport Kdkppn from \"@/components/features/reference/referensi_inquiryMod/Kdkppn\";\r\n\r\nconst KppnFilter = ({ inquiryState }) => {\r\n  const {\r\n    kppn,\r\n    setKppn,\r\n    kanwil,\r\n    kppnkondisi,\r\n    setKppnkondisi,\r\n    katakppn,\r\n    setKatakppn,\r\n    kppnradio,\r\n    setKppnradio,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katakppn && katakppn.trim() !== \"\";\r\n  const hasKondisiFilter = kppnkondisi && kppnkondisi.trim() !== \"\";\r\n  const hasPilihFilter =\r\n    kppn && kppn !== \"XXX\" && kppn !== \"XX\" && kppn !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isPilihDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  // When the kanwil changes, reset the selected kppn to \"Semua KPPN\"\r\n  React.useEffect(() => {\r\n    if (setKppn) {\r\n      setKppn(\"XX\");\r\n    }\r\n  }, [kanwil, setKppn]);\r\n\r\n  const KppnOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Banknote size={20} className=\"ml-4 text-secondary\" />\r\n          KPPN\r\n        </h6>\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdkppn */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isPilihDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih KPPN\r\n                </label>\r\n                {hasPilihFilter && !isPilihDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKppn && setKppn(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdkppn\r\n                value={kppn}\r\n                onChange={\r\n                  setKppn || (() => console.warn(\"setKppn is undefined\"))\r\n                }\r\n                kdkanwil={kanwil}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih KPPN\"\r\n                status=\"pilihkppn\"\r\n                isDisabled={isPilihDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKppnkondisi && setKppnkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 001,002,003, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={kppnkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setKppnkondisi && setKppnkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatakppn && setKatakppn(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: medan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katakppn || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKatakppn && setKatakppn(e.target.value)}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={[kppnradio || \"1\"]}\r\n                onSelectionChange={(key) => {\r\n                  let selected = key;\r\n                  if (key && typeof key !== \"string\" && key.size) {\r\n                    selected = Array.from(key)[0];\r\n                  }\r\n\r\n                  // Clean up the weird $.X format that HeroUI sometimes uses\r\n                  if (\r\n                    typeof selected === \"string\" &&\r\n                    selected.startsWith(\"$.\")\r\n                  ) {\r\n                    selected = selected.replace(\"$.\", \"\");\r\n                  }\r\n\r\n                  if (!selected) {\r\n                    setKppnradio && setKppnradio(\"1\");\r\n                    return;\r\n                  }\r\n\r\n                  if (setKppnradio) {\r\n                    setKppnradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {KppnOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdkppn */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default KppnFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE;IAClC,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,MAAM,EACN,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACb,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,YAAY,SAAS,IAAI,OAAO;IACtD,MAAM,mBAAmB,eAAe,YAAY,IAAI,OAAO;IAC/D,MAAM,iBACJ,QAAQ,SAAS,SAAS,SAAS,QAAQ,SAAS;IAEtD,8CAA8C;IAC9C,MAAM,kBAAkB,iBAAiB;IACzC,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,mEAAmE;IACnE,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS;YACX,QAAQ;QACV;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,cAAc;QAClB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAIxD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,kBAAkB,kBAAkB,iBACpC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,iCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,WAAW,QAAQ;8DACnC;;;;;;;;;;;;sDAKL,8OAAC,6KAAA,CAAA,UAAM;4CACL,OAAO;4CACP,UACE,WAAW,CAAC,IAAM,QAAQ,IAAI,CAAC,uBAAuB;4CAExD,UAAU;4CACV,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,kBAAkB,eAAe;8DACjD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,eAAe;4CACtB,YAAY;4CACZ,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,eAAe,YAAY;8DAC3C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,YAAY;4CACnB,YAAY;4CACZ,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc;gDAAC,aAAa;6CAAI;4CAChC,mBAAmB,CAAC;gDAClB,IAAI,WAAW;gDACf,IAAI,OAAO,OAAO,QAAQ,YAAY,IAAI,IAAI,EAAE;oDAC9C,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gDAC/B;gDAEA,2DAA2D;gDAC3D,IACE,OAAO,aAAa,YACpB,SAAS,UAAU,CAAC,OACpB;oDACA,WAAW,SAAS,OAAO,CAAC,MAAM;gDACpC;gDAEA,IAAI,CAAC,UAAU;oDACb,gBAAgB,aAAa;oDAC7B;gDACF;gDAEA,IAAI,cAAc;oDAChB,aAAa;gDACf;4CACF;4CACA,sBAAsB;sDAErB,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAQlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 9510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/SatkerFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, Input, Select, SelectItem, Tooltip } from \"@heroui/react\";\r\nimport { Building2, Info } from \"lucide-react\";\r\nimport Kdsatker from \"@/components/features/reference/referensi_inquiryMod/Kdsatker\";\r\n\r\nconst SatkerFilter = ({ inquiryState }) => {\r\n  const {\r\n    satker,\r\n    setSatker,\r\n    dept, // Kementerian selection for filtering satker\r\n    kdunit, // Unit selection for filtering satker\r\n    prov, // Province selection for filtering satker\r\n    kppn, // KPPN selection for filtering satker\r\n    satkerkondisi,\r\n    setSatkerkondisi,\r\n    katasatker,\r\n    setKatasatker,\r\n    satkerradio,\r\n    setSatkerradio,\r\n  } = inquiryState;\r\n\r\n  // Determine which filter type is currently active (priority order)\r\n  const hasKataFilter = katasatker && katasatker.trim() !== \"\";\r\n  const hasKondisiFilter = satkerkondisi && satkerkondisi.trim() !== \"\";\r\n  const hasPilihFilter = satker && satker !== \"XXX\" && satker !== \"XX\";\r\n\r\n  // Disable other inputs based on active filter\r\n  const isKdsatkerDisabled = hasKataFilter || hasKondisiFilter;\r\n  const isKondisiDisabled = hasKataFilter || hasPilihFilter;\r\n  const isKataDisabled = hasKondisiFilter || hasPilihFilter;\r\n\r\n  // When any parent filter changes, reset the selected satker to \"Semua Satker\"\r\n  React.useEffect(() => {\r\n    if (setSatker) {\r\n      setSatker(\"XX\");\r\n    }\r\n  }, [dept, kdunit, prov, kppn, setSatker]);\r\n\r\n  const SatkerOptions = [\r\n    { value: \"1\", label: \"Kode\" },\r\n    { value: \"2\", label: \"Kode Uraian\" },\r\n    { value: \"3\", label: \"Uraian\" },\r\n    { value: \"4\", label: \"Jangan Tampilkan\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Building2 size={20} className=\"text-secondary ml-4\" />\r\n          Satker\r\n        </h6>\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Kdsatker */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKdsatkerDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Pilih Satker\r\n                </label>\r\n                {hasPilihFilter && !isKdsatkerDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setSatker && setSatker(\"XX\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Kdsatker\r\n                value={satker}\r\n                onChange={\r\n                  setSatker || (() => console.warn(\"setSatker is undefined\"))\r\n                }\r\n                kddept={dept}\r\n                kdunit={kdunit}\r\n                kdlokasi={prov}\r\n                kdkppn={kppn}\r\n                className=\"w-full min-w-0 max-w-full\"\r\n                size=\"sm\"\r\n                placeholder=\"Pilih Satker\"\r\n                status=\"pilihsatker\"\r\n                isDisabled={isKdsatkerDisabled}\r\n              />\r\n            </div>\r\n            {/* Kondisi */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <label\r\n                    className={`text-sm font-medium ${\r\n                      isKondisiDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                    }`}\r\n                  >\r\n                    Masukkan Kondisi\r\n                  </label>\r\n                  <Tooltip\r\n                    content=\"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude\"\r\n                    showArrow={true}\r\n                    delay={1000}\r\n                    motionProps={{\r\n                      variants: {\r\n                        exit: {\r\n                          opacity: 0,\r\n                          transition: {\r\n                            duration: 0.1,\r\n                            ease: \"easeIn\",\r\n                          },\r\n                        },\r\n                        enter: {\r\n                          opacity: 1,\r\n                          transition: {\r\n                            duration: 0.15,\r\n                            ease: \"easeOut\",\r\n                          },\r\n                        },\r\n                      },\r\n                    }}\r\n                  >\r\n                    <span className=\"cursor-pointer text-gray-400 hover:text-gray-600\">\r\n                      <Info size={15} />\r\n                    </span>\r\n                  </Tooltip>\r\n                </div>\r\n                {hasKondisiFilter && !isKondisiDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setSatkerkondisi && setSatkerkondisi(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: 647321,647322, dst\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={satkerkondisi || \"\"}\r\n                isDisabled={isKondisiDisabled}\r\n                onChange={(e) =>\r\n                  setSatkerkondisi && setSatkerkondisi(e.target.value)\r\n                }\r\n              />\r\n            </div>\r\n            {/* Kata */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <label\r\n                  className={`text-sm font-medium ${\r\n                    isKataDisabled ? \"text-gray-400\" : \"text-gray-700\"\r\n                  }`}\r\n                >\r\n                  Mengandung Kata\r\n                </label>\r\n                {hasKataFilter && !isKataDisabled && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"light\"\r\n                    color=\"warning\"\r\n                    className=\"h-6 px-2 text-xs\"\r\n                    onPress={() => setKatasatker && setKatasatker(\"\")}\r\n                  >\r\n                    Clear\r\n                  </Button>\r\n                )}\r\n              </div>\r\n              <Input\r\n                placeholder=\"misalkan: universitas\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                value={katasatker || \"\"}\r\n                isDisabled={isKataDisabled}\r\n                onChange={(e) => setKatasatker && setKatasatker(e.target.value)}\r\n              />\r\n            </div>\r\n            {/* Jenis Tampilan */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Jenis Tampilan\r\n              </label>\r\n              <Select\r\n                aria-label=\"Pilih tampilan\"\r\n                className=\"w-full min-w-0\"\r\n                size=\"sm\"\r\n                selectedKeys={[satkerradio || \"1\"]}\r\n                onSelectionChange={(key) => {\r\n                  let selected = key;\r\n                  if (key && typeof key !== \"string\" && key.size) {\r\n                    selected = Array.from(key)[0];\r\n                  }\r\n\r\n                  // Clean up the weird $.X format that HeroUI sometimes uses\r\n                  if (\r\n                    typeof selected === \"string\" &&\r\n                    selected.startsWith(\"$.\")\r\n                  ) {\r\n                    selected = selected.replace(\"$.\", \"\");\r\n                  }\r\n\r\n                  if (!selected) {\r\n                    setSatkerradio && setSatkerradio(\"1\");\r\n                    return;\r\n                  }\r\n\r\n                  if (setSatkerradio) {\r\n                    setSatkerradio(selected);\r\n                  }\r\n                }}\r\n                disallowEmptySelection\r\n              >\r\n                {SatkerOptions.map((opt) => (\r\n                  <SelectItem key={opt.value} textValue={opt.label}>\r\n                    {opt.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n          </div>\r\n          {/* Helper text row - only show on extra large screens */}\r\n          <div className=\"hidden xl:flex xl:flex-row gap-4 w-full\">\r\n            {/* Spacer for Kdsatker */}\r\n            <div className=\"flex-1\"></div>\r\n\r\n            {/* Spacer for Kata */}\r\n            <div className=\"flex-1\"></div>\r\n            {/* Spacer for Jenis Tampilan */}\r\n            <div className=\"flex-1\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SatkerFilter;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;;AAEA,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,MAAM,EACJ,MAAM,EACN,SAAS,EACT,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACf,GAAG;IAEJ,mEAAmE;IACnE,MAAM,gBAAgB,cAAc,WAAW,IAAI,OAAO;IAC1D,MAAM,mBAAmB,iBAAiB,cAAc,IAAI,OAAO;IACnE,MAAM,iBAAiB,UAAU,WAAW,SAAS,WAAW;IAEhE,8CAA8C;IAC9C,MAAM,qBAAqB,iBAAiB;IAC5C,MAAM,oBAAoB,iBAAiB;IAC3C,MAAM,iBAAiB,oBAAoB;IAE3C,8EAA8E;IAC9E,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,WAAW;YACb,UAAU;QACZ;IACF,GAAG;QAAC;QAAM;QAAQ;QAAM;QAAM;KAAU;IAExC,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAO;QAC5B;YAAE,OAAO;YAAK,OAAO;QAAc;QACnC;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAK,OAAO;QAAmB;KACzC;IAED,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,gNAAA,CAAA,YAAS;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAIzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,qBAAqB,kBAAkB,iBACvC;8DACH;;;;;;gDAGA,kBAAkB,CAAC,oCAClB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,aAAa,UAAU;8DACvC;;;;;;;;;;;;sDAKL,8OAAC,+KAAA,CAAA,UAAQ;4CACP,OAAO;4CACP,UACE,aAAa,CAAC,IAAM,QAAQ,IAAI,CAAC,yBAAyB;4CAE5D,QAAQ;4CACR,QAAQ;4CACR,UAAU;4CACV,QAAQ;4CACR,WAAU;4CACV,MAAK;4CACL,aAAY;4CACZ,QAAO;4CACP,YAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,oBAAoB,EAC9B,oBAAoB,kBAAkB,iBACtC;sEACH;;;;;;sEAGD,8OAAC,+MAAA,CAAA,UAAO;4DACN,SAAQ;4DACR,WAAW;4DACX,OAAO;4DACP,aAAa;gEACX,UAAU;oEACR,MAAM;wEACJ,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;oEACA,OAAO;wEACL,SAAS;wEACT,YAAY;4EACV,UAAU;4EACV,MAAM;wEACR;oEACF;gEACF;4DACF;sEAEA,cAAA,8OAAC;gEAAK,WAAU;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gDAIjB,oBAAoB,CAAC,mCACpB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,oBAAoB,iBAAiB;8DACrD;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,iBAAiB;4CACxB,YAAY;4CACZ,UAAU,CAAC,IACT,oBAAoB,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,oBAAoB,EAC9B,iBAAiB,kBAAkB,iBACnC;8DACH;;;;;;gDAGA,iBAAiB,CAAC,gCACjB,8OAAC,4MAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,OAAM;oDACN,WAAU;oDACV,SAAS,IAAM,iBAAiB,cAAc;8DAC/C;;;;;;;;;;;;sDAKL,8OAAC,yMAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;4CACV,MAAK;4CACL,OAAO,cAAc;4CACrB,YAAY;4CACZ,UAAU,CAAC,IAAM,iBAAiB,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAIlE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAoC;;;;;;sDAGrD,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cAAc;gDAAC,eAAe;6CAAI;4CAClC,mBAAmB,CAAC;gDAClB,IAAI,WAAW;gDACf,IAAI,OAAO,OAAO,QAAQ,YAAY,IAAI,IAAI,EAAE;oDAC9C,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gDAC/B;gDAEA,2DAA2D;gDAC3D,IACE,OAAO,aAAa,YACpB,SAAS,UAAU,CAAC,OACpB;oDACA,WAAW,SAAS,OAAO,CAAC,MAAM;gDACpC;gDAEA,IAAI,CAAC,UAAU;oDACb,kBAAkB,eAAe;oDACjC;gDACF;gDAEA,IAAI,gBAAgB;oDAClB,eAAe;gDACjB;4CACF;4CACA,sBAAsB;sDAErB,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,4NAAA,CAAA,aAAU;oDAAiB,WAAW,IAAI,KAAK;8DAC7C,IAAI,KAAK;mDADK,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAQlC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe", "debugId": null}}, {"offset": {"line": 9916, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterGroups/CutoffFilter.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport { Calendar } from \"lucide-react\";\r\n\r\nconst months = [\r\n  { value: \"1\", label: \"<PERSON><PERSON><PERSON>\" },\r\n  { value: \"2\", label: \"<PERSON><PERSON><PERSON>\" },\r\n  { value: \"3\", label: \"<PERSON><PERSON>\" },\r\n  { value: \"4\", label: \"April\" },\r\n  { value: \"5\", label: \"<PERSON>\" },\r\n  { value: \"6\", label: \"<PERSON><PERSON>\" },\r\n  { value: \"7\", label: \"<PERSON><PERSON>\" },\r\n  { value: \"8\", label: \"Agustus\" },\r\n  { value: \"9\", label: \"September\" },\r\n  { value: \"10\", label: \"Oktober\" },\r\n  { value: \"11\", label: \"November\" },\r\n  { value: \"12\", label: \"Desember\" },\r\n];\r\n\r\nconst CutoffFilter = ({ inquiryState }) => {\r\n  const { cutoff, setCutoff } = inquiryState;\r\n\r\n  // Determine if cutoff is enabled (switch is ON)\r\n  const isCutoffEnabled = cutoff !== \"0\";\r\n\r\n  // Handle selection change\r\n  const handleSelectionChange = (keys) => {\r\n    const selectedKey = Array.from(keys)[0];\r\n    if (selectedKey) {\r\n      setCutoff(selectedKey);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm\">\r\n      {/* Mobile/Tablet: Stack vertically, Desktop: Row layout */}\r\n      <div className=\"flex flex-col lg:flex-row lg:items-center gap-4 w-full\">\r\n        {/* Title - Full width on mobile, fixed width on desktop */}\r\n        <h6 className=\"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]\">\r\n          <Calendar size={20} className=\"ml-4 text-secondary\" />\r\n          Cut-Off\r\n        </h6>\r\n\r\n        {/* Form fields container */}\r\n        <div className=\"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full\">\r\n          {/* Fields: Stack on mobile/tablet, row on large desktop */}\r\n          <div className=\"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full\">\r\n            {/* Month Selection */}\r\n            <div className=\"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden\">\r\n              <label className=\"text-sm font-medium text-gray-700\">\r\n                Pilih Bulan Cutoff\r\n              </label>\r\n              <div className=\"flex items-center\">\r\n                <Select\r\n                  aria-label=\"Select cutoff month\"\r\n                  className=\"w-full min-w-0 max-w-xs\"\r\n                  size=\"sm\"\r\n                  selectedKeys={\r\n                    isCutoffEnabled ? new Set([cutoff]) : new Set([\"12\"])\r\n                  }\r\n                  onSelectionChange={handleSelectionChange}\r\n                  isDisabled={!isCutoffEnabled}\r\n                  disallowEmptySelection\r\n                  placeholder=\"Choose month\"\r\n                >\r\n                  {months.map((month) => (\r\n                    <SelectItem key={month.value} value={month.value}>\r\n                      {month.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </Select>\r\n                {/* Helper text */}\r\n                {!isCutoffEnabled ? (\r\n                  <p className=\"text-xs text-gray-500 ml-3\">\r\n                    Aktifkan filter untuk memilih bulan cutoff\r\n                  </p>\r\n                ) : (\r\n                  <p className=\"text-xs text-gray-500 ml-3\">\r\n                    Silahkan pilih cutoff bulan\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CutoffFilter;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;;AAEA,MAAM,SAAS;IACb;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAW;IAChC;QAAE,OAAO;QAAK,OAAO;IAAQ;IAC7B;QAAE,OAAO;QAAK,OAAO;IAAQ;IAC7B;QAAE,OAAO;QAAK,OAAO;IAAM;IAC3B;QAAE,OAAO;QAAK,OAAO;IAAO;IAC5B;QAAE,OAAO;QAAK,OAAO;IAAO;IAC5B;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAY;IACjC;QAAE,OAAO;QAAM,OAAO;IAAU;IAChC;QAAE,OAAO;QAAM,OAAO;IAAW;IACjC;QAAE,OAAO;QAAM,OAAO;IAAW;CAClC;AAED,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE;IACpC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAE9B,gDAAgD;IAChD,MAAM,kBAAkB,WAAW;IAEnC,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;QAC7B,MAAM,cAAc,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;QACvC,IAAI,aAAa;YACf,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAAwB;;;;;;;8BAKxD,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAoC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4MAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,MAAK;4CACL,cACE,kBAAkB,IAAI,IAAI;gDAAC;6CAAO,IAAI,IAAI,IAAI;gDAAC;6CAAK;4CAEtD,mBAAmB;4CACnB,YAAY,CAAC;4CACb,sBAAsB;4CACtB,aAAY;sDAEX,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,4NAAA,CAAA,aAAU;oDAAmB,OAAO,MAAM,KAAK;8DAC7C,MAAM,KAAK;mDADG,MAAM,KAAK;;;;;;;;;;wCAM/B,CAAC,gCACA,8OAAC;4CAAE,WAAU;sDAA6B;;;;;qGAI1C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5D;uCAEe", "debugId": null}}]}