{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/%28dashboard%29/dashboard/page.jsx"], "sourcesContent": ["import { ScrollkeBawah } from \"@/components/home/<USER>\";\r\n// import { CekToken } from \"@/utils/cekToken\";\r\nimport DashboardLoading from \"./loading\";\r\n\r\nconst Dashboard = async () => {\r\n  return <DashboardLoading />;\r\n};\r\n\r\nexport default Dashboard;\r\n"], "names": [], "mappings": ";;;;;;;;;AACA,+CAA+C;AAC/C;;;;AAEA,MAAM,YAAY;IAChB,qBAAO,8OAAC,oJAAA,CAAA,UAAgB;;;;;AAC1B;uCAEe", "debugId": null}}]}