{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/%28dashboard%29/inquiry-data/belanja/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport InquiryMod from \"@/components/features/inquiry/shared/formInquiryMod\";\r\n\r\n// Simulate slow loading to test loading component\r\n// const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));\r\n\r\nconst InquiryBelanja = () => {\r\n  // Add 2 second delay to see loading animation\r\n  // await delay(2000);\r\n\r\n  return <InquiryMod />;\r\n};\r\n\r\nexport default InquiryBelanja;\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,kDAAkD;AAClD,mFAAmF;AAEnF,MAAM,iBAAiB;IACrB,8CAA8C;IAC9C,qBAAqB;IAErB,qBAAO,6LAAC,wKAAA,CAAA,UAAU;;;;;AACpB;KALM;uCAOS", "debugId": null}}]}