(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/feedback/Omspan.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EPANOTIF": ()=>EPANOTIF,
    "NotifDisclaimer": ()=>NotifDisclaimer,
    "NotifPesan": ()=>NotifPesan,
    "Omspan": ()=>Omspan,
    "Pesan": ()=><PERSON><PERSON>,
    "Toast": ()=>Toast,
    "Tunda": ()=>Tunda
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sweetalert2/dist/sweetalert2.all.js [app-client] (ecmascript)");
;
const showToast = (pesan)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].close(); // Menutup notifikasi yang sedang ditampilkan
    setTimeout(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire({
            text: "".concat(pesan, " "),
            position: "top-end",
            showConfirmButton: false,
            toast: true,
            timer: 5000,
            background: "black",
            color: "#ffffff",
            showClass: {
                popup: "animate__animated "
            }
        });
    }, 500); // Menunggu 500ms sebelum menampilkan notifikasi baru
};
const NotifikasiToast = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire({
        text: "".concat(pesan, " 👋"),
        position: "top-end",
        showConfirmButton: false,
        toast: true,
        timer: 3000,
        background: "#C16DFA",
        color: "#ffffff"
    });
};
_c = NotifikasiToast;
const NotifikasiToastEPA = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire({
        text: "".concat(pesan, " "),
        position: "top-start",
        showConfirmButton: false,
        toast: true,
        timer: 3000,
        background: "#17c3fa",
        color: "#ffffff"
    });
};
_c1 = NotifikasiToastEPA;
const Toast = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].mixin({
    toast: true,
    position: "top-start",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    customClass: {
        popup: "custom-toast-font custom-toast-primary-light"
    },
    didOpen: (toast)=>{
        toast.onmouseenter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].stopTimer;
        toast.onmouseleave = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resumeTimer;
    }
});
const NotifikasiDisclaimer = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire({
        text: "".concat(pesan, " "),
        position: "top-start",
        showConfirmButton: false,
        toast: true,
        timer: 5000,
        background: "#FF5733",
        color: "#ffffff",
        showCloseButton: true
    });
};
_c2 = NotifikasiDisclaimer;
const UserLogin = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire({
        text: "".concat(pesan, " "),
        position: "bottom-start",
        showConfirmButton: false,
        toast: true,
        timer: 3000,
        background: "#FF5733",
        color: "#ffffff",
        showCloseButton: true,
        animation: false
    });
};
_c3 = UserLogin;
const Omspan = (username, message)=>{
    showToast(username, message);
};
_c4 = Omspan;
const Tunda = (username)=>{
    showToast(username, "Tunda OMSPAN berhasil");
};
_c5 = Tunda;
const Pesan = (pesan)=>{
    showToast(pesan);
};
_c6 = Pesan;
const NotifPesan = (pesan)=>{
    NotifikasiToast(pesan);
};
_c7 = NotifPesan;
const NotifDisclaimer = (pesan)=>{
    NotifikasiDisclaimer(pesan);
};
_c8 = NotifDisclaimer;
const EPANOTIF = (pesan)=>{
    NotifikasiToastEPA(pesan);
};
_c9 = EPANOTIF;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;
__turbopack_context__.k.register(_c, "NotifikasiToast");
__turbopack_context__.k.register(_c1, "NotifikasiToastEPA");
__turbopack_context__.k.register(_c2, "NotifikasiDisclaimer");
__turbopack_context__.k.register(_c3, "UserLogin");
__turbopack_context__.k.register(_c4, "Omspan");
__turbopack_context__.k.register(_c5, "Tunda");
__turbopack_context__.k.register(_c6, "Pesan");
__turbopack_context__.k.register(_c7, "NotifPesan");
__turbopack_context__.k.register(_c8, "NotifDisclaimer");
__turbopack_context__.k.register(_c9, "EPANOTIF");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/feedback/toastError.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ToastError": ()=>ToastError,
    "handleHttpError": ()=>handleHttpError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sweetalert2/dist/sweetalert2.all.js [app-client] (ecmascript)");
;
// Fungsi untuk menampilkan pesan toast dengan SweetAlert2
const ToastError = function(title, text) {
    let icon = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "error";
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$all$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire({
        title,
        text,
        icon,
        position: "top-end",
        toast: true,
        showConfirmButton: false,
        timer: 5000,
        showCloseButton: true,
        background: "red",
        color: "white",
        // color: "#716add",
        // customClass: {
        //   container: "toast-container",
        //   popup: "colored-toast",
        // },
        timerProgressBar: true
    });
};
_c = ToastError;
// Fungsi untuk menampilkan pesan error berdasarkan kode status HTTP
const handleHttpError = (status, text)=>{
    switch(status){
        case 400:
            ToastError("Kesalahan Permintaan, Permintaan tidak valid. (".concat(text, ")"));
            break;
        case 401:
            ToastError("Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (".concat(text, ")"));
            break;
        case 403:
            ToastError("Akses Ditolak, Akses ke sumber daya dilarang. (".concat(text, ")"));
            break;
        case 404:
            ToastError("Error Refresh Token. Silahkan Login Ulang... (".concat(text, ")"));
            break;
        case 429:
            ToastError("Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (".concat(text, ")"));
            break;
        case 422:
            ToastError("Unprocessable Entity, Permintaan tidak dapat diolah. (".concat(text, ")"));
            break;
        case 500:
            ToastError("Kesalahan Pada Query", text);
            break;
        case 503:
            ToastError("Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (".concat(text, ")"));
            break;
        case 504:
            ToastError("Waktu Habis, Permintaan waktu habis. (".concat(text, ")"));
            break;
        case 505:
            ToastError("Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (".concat(text, ")"));
            break;
        case 507:
            ToastError("Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (".concat(text, ")"));
            break;
        case 511:
            ToastError("Autentikasi Diperlukan, Autentikasi diperlukan. (".concat(text, ")"));
            break;
        default:
            ToastError("Kesalahan Server, ".concat(text, " "));
            break;
    }
};
;
var _c;
__turbopack_context__.k.register(_c, "ToastError");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_ui_feedback_9c082b6e._.js.map