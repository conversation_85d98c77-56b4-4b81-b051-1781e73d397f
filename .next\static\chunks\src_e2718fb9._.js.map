{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/utils/random.ts"], "sourcesContent": ["import CryptoJS from \"crypto-js\";\r\n\r\nconst secretKey = \"mebe23\";\r\nconst Encrypt = (word, key = secretKey) => {\r\n  let encJson = CryptoJS.AES.encrypt(JSON.stringify(word), key).toString();\r\n  let encData = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(encJson));\r\n  return encData;\r\n};\r\n\r\nexport default Encrypt;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY;AAClB,MAAM,UAAU,SAAC;QAAM,uEAAM;IAC3B,IAAI,UAAU,wIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC,OAAO,KAAK,QAAQ;IACtE,IAAI,UAAU,wIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,wIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;IACpE,OAAO;AACT;KAJM;uCAMS", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/dashboard/modal/detailPerforma.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport {\r\n  Button,\r\n  Input,\r\n  Modal,\r\n  ModalBody,\r\n  ModalContent,\r\n  ModalFooter,\r\n  ModalHeader,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableColumn,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@heroui/react\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nconst ModalPerforma = ({ isOpen, onClose }) => {\r\n  const [dataDipa, setDataDipa] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const { showToast } = useToast();\r\n  const context = useContext(MyContext);\r\n  const { token, axiosJWT } = context;\r\n\r\n  const formatNumber = (value) =>\r\n    new Intl.NumberFormat(\"id-ID\", { maximumFractionDigits: 0 }).format(value);\r\n\r\n  const getData = async () => {\r\n    const query = `\r\n      SELECT \r\n        prk.kddept,\r\n        td.nmdept,\r\n        SUM(prk.pagu) AS pagu,\r\n        SUM(prk.realisasi) AS realisasi,\r\n        SUM(prk.realisasi) / NULLIF(SUM(prk.pagu), 0) * 100 AS persen\r\n      FROM dashboard.pagu_real_kl prk\r\n      LEFT JOIN dbref.t_dept_2025 td ON prk.kddept = td.kddept\r\n      WHERE prk.thang = '2022' \r\n      GROUP BY prk.kddept\r\n      ORDER BY pagu DESC\r\n    `\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n\r\n    try {\r\n      setLoading(true);\r\n      const encryptedQuery = Encrypt(query);\r\n      const response = await axiosJWT.post(\r\n        `${process.env.NEXT_PUBLIC_GET_REFERENSI}`,\r\n        { query: encryptedQuery }\r\n      );\r\n\r\n      const resultData = response.data.result || [];\r\n      const cleanedData = resultData.map((item) => ({\r\n        kddept: item.kddept || \"\",\r\n        nmdept: item.nmdept || \"\",\r\n        pagu: item.pagu || 0,\r\n        realisasi: item.realisasi || 0,\r\n        persen: item.persen || 0,\r\n      }));\r\n      setDataDipa(cleanedData);\r\n    } catch (err) {\r\n      const errorMessage =\r\n        err.response?.data?.error || \"Terjadi kesalahan saat memuat data\";\r\n      showToast(errorMessage, \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isOpen) getData();\r\n  }, [isOpen]);\r\n\r\n  const skeletonRowCount = 8;\r\n\r\n  const filteredData = dataDipa.filter((item) =>\r\n    `${item.kddept} ${item.nmdept}`\r\n      .toLowerCase()\r\n      .includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <Modal isOpen={isOpen} onClose={onClose} size=\"5xl\" scrollBehavior=\"inside\">\r\n      <ModalContent>\r\n        <ModalHeader>Detail Performa Kementerian/Lembaga</ModalHeader>\r\n        <ModalBody className=\"p-4\">\r\n          <div className=\"mb-4\">\r\n            <Input\r\n              placeholder=\"Cari Kementerian atau Kode...\"\r\n              value={searchTerm}\r\n              onValueChange={setSearchTerm}\r\n              startContent={<Search size={18} />}\r\n              variant=\"bordered\"\r\n              radius=\"sm\"\r\n              size=\"sm\"\r\n              className=\"w-full sm:w-1/2\"\r\n            />\r\n          </div>\r\n          <div className=\"relative min-h-[60vh] overflow-auto\">\r\n            <Table\r\n              isStriped\r\n              isHeaderSticky\r\n              aria-label=\"Tabel Performa KL\"\r\n              removeWrapper\r\n            >\r\n              <TableHeader>\r\n                {[\"Nama Kementerian\", \"Pagu\", \"Realisasi\", \"Persentase\"].map(\r\n                  (header, i) => (\r\n                    <TableColumn\r\n                      key={i}\r\n                      className=\"top-0 z-10 bg-gradient-to-r from-blue-150 to-indigo-50 text-center font-semibold text-sm text-gray-800 border-b-2 border-blue-200 shadow-sm\"\r\n                    >\r\n                      {header}\r\n                    </TableColumn>\r\n                  )\r\n                )}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {(loading\r\n                  ? Array.from({ length: skeletonRowCount }, (_, i) => ({\r\n                      kddept: `skeleton-${i}`,\r\n                      nmdept: \"\",\r\n                      pagu: 0,\r\n                      realisasi: 0,\r\n                      persen: 0,\r\n                    }))\r\n                  : filteredData\r\n                ).map((item) => (\r\n                  <TableRow key={item.kddept}>\r\n                    <TableCell className=\"text-left align-top\">\r\n                      {loading ? (\r\n                        <>\r\n                          <div className=\"h-4 w-40 bg-gray-200 rounded animate-pulse mb-1\"></div>\r\n                          <div className=\"h-3 w-24 bg-gray-100 rounded animate-pulse\"></div>\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <div className=\"font-medium\">{item.nmdept}</div>\r\n                          <div className=\"text-xs text-gray-500\">\r\n                            ({item.kddept})\r\n                          </div>\r\n                        </>\r\n                      )}\r\n                    </TableCell>\r\n                    <TableCell className=\"text-right align-top\">\r\n                      {loading ? (\r\n                        <div className=\"h-4 w-24 bg-gray-200 rounded animate-pulse ml-auto\"></div>\r\n                      ) : (\r\n                        formatNumber(item.pagu)\r\n                      )}\r\n                    </TableCell>\r\n                    <TableCell className=\"text-right align-top\">\r\n                      {loading ? (\r\n                        <div className=\"h-4 w-24 bg-gray-200 rounded animate-pulse ml-auto\"></div>\r\n                      ) : (\r\n                        formatNumber(item.realisasi)\r\n                      )}\r\n                    </TableCell>\r\n                    <TableCell className=\"text-right align-top\">\r\n                      {loading ? (\r\n                        <div className=\"h-4 w-16 bg-gray-200 rounded animate-pulse ml-auto\"></div>\r\n                      ) : (\r\n                        `${Number(item.persen).toFixed(2)}%`\r\n                      )}\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        </ModalBody>\r\n        <ModalFooter>\r\n          <Button\r\n            color=\"danger\"\r\n            onPress={onClose}\r\n            startContent={<X size={16} />}\r\n          >\r\n            Tutup\r\n          </Button>\r\n        </ModalFooter>\r\n      </ModalContent>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ModalPerforma;\r\n"], "names": [], "mappings": ";;;AAuDW;;AArDX;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AACA;;;AArBA;;;;;;;AAuBA,MAAM,gBAAgB;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IACxC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,eAAe,CAAC,QACpB,IAAI,KAAK,YAAY,CAAC,SAAS;YAAE,uBAAuB;QAAE,GAAG,MAAM,CAAC;IAEtE,MAAM,UAAU;QACd,MAAM,QAAQ,AAAC,+YAaZ,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,IAAI;YACF,WAAW;YACX,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;YAC/B,MAAM,WAAW,MAAM,SAAS,IAAI,CAClC,AAAC,GAAwC,gFACzC;gBAAE,OAAO;YAAe;YAG1B,MAAM,aAAa,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;YAC7C,MAAM,cAAc,WAAW,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC5C,QAAQ,KAAK,MAAM,IAAI;oBACvB,QAAQ,KAAK,MAAM,IAAI;oBACvB,MAAM,KAAK,IAAI,IAAI;oBACnB,WAAW,KAAK,SAAS,IAAI;oBAC7B,QAAQ,KAAK,MAAM,IAAI;gBACzB,CAAC;YACD,YAAY;QACd,EAAE,OAAO,KAAK;gBAEV,oBAAA;YADF,MAAM,eACJ,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,KAAK,KAAI;YAC/B,UAAU,cAAc;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ;QACd;kCAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB;IAEzB,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,OACpC,AAAC,GAAiB,OAAf,KAAK,MAAM,EAAC,KAAe,OAAZ,KAAK,MAAM,EAC1B,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW;IAGpC,qBACE,6LAAC,4MAAA,CAAA,QAAK;QAAC,QAAQ;QAAQ,SAAS;QAAS,MAAK;QAAM,gBAAe;kBACjE,cAAA,6LAAC,2NAAA,CAAA,eAAY;;8BACX,6LAAC,yNAAA,CAAA,cAAW;8BAAC;;;;;;8BACb,6LAAC,qNAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4MAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,eAAe;gCACf,4BAAc,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;gCAC5B,SAAQ;gCACR,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4MAAA,CAAA,QAAK;gCACJ,SAAS;gCACT,cAAc;gCACd,cAAW;gCACX,aAAa;;kDAEb,6LAAC,yNAAA,CAAA,cAAW;kDACT;4CAAC;4CAAoB;4CAAQ;4CAAa;yCAAa,CAAC,GAAG,CAC1D,CAAC,QAAQ,kBACP,6LAAC,yNAAA,CAAA,cAAW;gDAEV,WAAU;0DAET;+CAHI;;;;;;;;;;kDAQb,6LAAC,qNAAA,CAAA,YAAS;kDACP,CAAC,UACE,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAiB,GAAG,CAAC,GAAG,IAAM,CAAC;gDAClD,QAAQ,AAAC,YAAa,OAAF;gDACpB,QAAQ;gDACR,MAAM;gDACN,WAAW;gDACX,QAAQ;4CACV,CAAC,KACD,YACJ,EAAE,GAAG,CAAC,CAAC,qBACL,6LAAC,mNAAA,CAAA,WAAQ;;kEACP,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,wBACC;;8EACE,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;yFAGjB;;8EACE,6LAAC;oEAAI,WAAU;8EAAe,KAAK,MAAM;;;;;;8EACzC,6LAAC;oEAAI,WAAU;;wEAAwB;wEACnC,KAAK,MAAM;wEAAC;;;;;;;;;;;;;;kEAKtB,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,wBACC,6LAAC;4DAAI,WAAU;;;;;uGAEf,aAAa,KAAK,IAAI;;;;;;kEAG1B,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,wBACC,6LAAC;4DAAI,WAAU;;;;;uGAEf,aAAa,KAAK,SAAS;;;;;;kEAG/B,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,wBACC,6LAAC;4DAAI,WAAU;;;;;uGAEf,AAAC,GAAiC,OAA/B,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,IAAG;;;;;;;+CAlCzB,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;8BA2CpC,6LAAC,yNAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,+MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAS;wBACT,4BAAc,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;kCACxB;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxKM;;QAIkB,uJAAA,CAAA,WAAQ;;;KAJ1B;uCA0KS", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/dashboard/dataDipa/BelanjaTerbesar.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nimport { Card, CardBody, CardHeader, Chip, Progress } from \"@heroui/react\";\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport ModalPerforma from \"../modal/detailPerforma\";\r\n\r\nexport const BelanjaTerbesar = ({ selectedKanwil, selectedKddept }) => {\r\n  const [dataDipa, setDataDipa] = useState([]);\r\n  const [error, setError] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const { theme } = useTheme();\r\n  const { showToast } = useToast();\r\n  const [modal, setModal] = useState(false);\r\n  const context = useContext(MyContext);\r\n\r\n  const { token, axiosJWT } = context;\r\n\r\n  const getThemeClasses = () => {\r\n    const isDark = theme === \"dark\";\r\n    return {\r\n      cardBg: isDark\r\n        ? \"bg-gradient-to-br from-slate-800 to-slate-700\"\r\n        : \"bg-gradient-to-br from-slate-100 to-slate-200\",\r\n      skeletonCardBg: isDark\r\n        ? \"bg-gradient-to-br from-slate-800 to-slate-700\"\r\n        : \"bg-gradient-to-br from-default-50 to-default-100\",\r\n      skeletonItemBg: isDark ? \"bg-slate-700/50\" : \"bg-default-50\",\r\n      textPrimary: isDark ? \"text-slate-100\" : \"text-slate-900\",\r\n      textSecondary: isDark ? \"text-slate-300\" : \"text-slate-700\",\r\n      textMuted: isDark ? \"text-slate-400\" : \"text-slate-600\",\r\n    };\r\n  };\r\n\r\n  const formatTrillions = (amount) => {\r\n    return (\r\n      new Intl.NumberFormat(\"id-ID\", {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2,\r\n      }).format(amount) + \" T\"\r\n    );\r\n  };\r\n\r\n  const getData = async () => {\r\n    let kanwilFilter = \"\";\r\n    if (selectedKanwil && selectedKanwil !== \"00\") {\r\n      kanwilFilter = ` and kdkanwil='${selectedKanwil}'`;\r\n    }\r\n\r\n    let kddeptFilter = \"\";\r\n    if (selectedKddept && selectedKddept !== \"000\") {\r\n      kddeptFilter = ` and kddept='${selectedKddept}'`;\r\n    }\r\n\r\n    const encodedQuery = encodeURIComponent(\r\n      `SELECT \r\n  ${selectedKddept && selectedKddept !== \"000\" ? \"kddept,\" : \"\"}\r\n  jenbel,\r\n  CASE\r\n    WHEN jenbel = '51' THEN 'Pegawai'\r\n    WHEN jenbel = '52' THEN 'Barang'\r\n    WHEN jenbel = '53' THEN 'Modal'\r\n    WHEN jenbel = '57' THEN 'Sosial'\r\n    ELSE 'Lainnya'\r\n  END AS jenis_belanja,\r\n  SUM(pagu) AS pagu,\r\n  SUM(realisasi) AS realisasi,\r\n  SUM(realisasi) / NULLIF(SUM(pagu),0) * 100 AS persen\r\nFROM dashboard.pagu_real_jenbel \r\nWHERE thang = '2022' ${kanwilFilter}${kddeptFilter}\r\nGROUP BY jenbel;`\r\n    );\r\n    const cleanedQuery = decodeURIComponent(encodedQuery)\r\n      .replace(/\\n/g, \" \")\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n    const encryptedQuery = Encrypt(cleanedQuery);\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const response = await axiosJWT.post(\r\n        `${process.env.NEXT_PUBLIC_GET_REFERENSI}`,\r\n        { query: encryptedQuery }\r\n      );\r\n\r\n      const resultData = response.data.result || [];\r\n      setDataDipa(resultData);\r\n    } catch (err) {\r\n      const { data } = err.response || {};\r\n      showToast(data && data.error, \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, [selectedKanwil, selectedKddept]);\r\n\r\n  const renderMainContent = () => {\r\n    const jenbelData = dataDipa\r\n      .map((item) => {\r\n        const percentage =\r\n          item.pagu > 0 ? (item.realisasi / item.pagu) * 100 : 0;\r\n        const status =\r\n          percentage >= 80\r\n            ? \"excellent\"\r\n            : percentage >= 60\r\n            ? \"on-track\"\r\n            : \"warning\";\r\n        return {\r\n          name: item.jenis_belanja,\r\n          budget: formatTrillions(item.pagu / 1000000000000),\r\n          realized: formatTrillions(item.realisasi / 1000000000000),\r\n          percentage: Math.round(percentage),\r\n          status,\r\n        };\r\n      })\r\n      .sort((a, b) => {\r\n        const budgetA = parseFloat(a.budget.replace(/[^\\d.-]/g, \"\"));\r\n        const budgetB = parseFloat(b.budget.replace(/[^\\d.-]/g, \"\"));\r\n        return budgetB - budgetA;\r\n      });\r\n\r\n    return (\r\n      <CardBody className=\"pt-0 px-4 md:px-6\">\r\n        <div className=\"space-y-0\">\r\n          {jenbelData.slice(0, 4).map((jenbel, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"flex items-center justify-between p-0.5 md:p-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors duration-200\"\r\n            >\r\n              <div className=\"flex-1 min-w-0\">\r\n                <div className=\"mb-0.5\">\r\n                  <h4\r\n                    className={`font-medium text-xs truncate pr-2 ${\r\n                      getThemeClasses().textSecondary\r\n                    }`}\r\n                  >\r\n                    {jenbel.name}\r\n                  </h4>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <div className=\"relative flex-1\">\r\n                    <Progress\r\n                      value={jenbel.percentage}\r\n                      color={\r\n                        jenbel.status === \"excellent\"\r\n                          ? \"success\"\r\n                          : jenbel.status === \"on-track\"\r\n                          ? \"primary\"\r\n                          : \"warning\"\r\n                      }\r\n                      size=\"md\"\r\n                      aria-label=\"belanjaterbesar\"\r\n                      className=\"w-full h-6\"\r\n                    />\r\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                      <span\r\n                        className={`text-xs font-semibold drop-shadow-sm ${\r\n                          theme === \"dark\" ? \"text-white\" : \"text-slate-800\"\r\n                        }`}\r\n                      >\r\n                        Rp {jenbel.realized} / Rp {jenbel.budget}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <Chip\r\n                    size=\"sm\"\r\n                    variant=\"flat\"\r\n                    color={\r\n                      jenbel.status === \"excellent\"\r\n                        ? \"success\"\r\n                        : jenbel.status === \"on-track\"\r\n                        ? \"primary\"\r\n                        : \"warning\"\r\n                    }\r\n                    className=\"text-xs flex-shrink-0 font-semibold shadow-sm\"\r\n                  >\r\n                    {jenbel.percentage}%\r\n                  </Chip>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </CardBody>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className={`border-none shadow-sm ${\r\n          loading ? getThemeClasses().skeletonCardBg : getThemeClasses().cardBg\r\n        } lg:col-span-6 xl:col-span-3`}\r\n      >\r\n        <CardHeader className=\"pb-2 px-4 md:px-6\">\r\n          <div className=\"flex justify-between items-center w-full\">\r\n            <h3\r\n              className={`text-sm md:text-base font-semibold ${\r\n                getThemeClasses().textPrimary\r\n              }`}\r\n            >\r\n              Jenis Belanja Terbesar\r\n            </h3>\r\n            <Chip\r\n              color=\"primary\"\r\n              onClick={() => setModal(true)}\r\n              variant=\"flat\"\r\n              size=\"sm\"\r\n              className=\"w-fit cursor-pointer\"\r\n            >\r\n              Detail\r\n            </Chip>\r\n          </div>\r\n        </CardHeader>\r\n        {loading ? (\r\n          <CardBody className=\"pt-0 px-4 md:px-6\">Loading...</CardBody>\r\n        ) : dataDipa.length === 0 ? (\r\n          <CardBody className=\"pt-0 px-4 md:px-6\">Tidak ada data</CardBody>\r\n        ) : (\r\n          renderMainContent()\r\n        )}\r\n      </Card>\r\n      {modal && (\r\n        <ModalPerforma isOpen={modal} onClose={() => setModal(false)} />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AAwFW;;AAtFX;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;AAYO,MAAM,kBAAkB;QAAC,EAAE,cAAc,EAAE,cAAc,EAAE;;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IAEpC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,kBAAkB;QACtB,MAAM,SAAS,UAAU;QACzB,OAAO;YACL,QAAQ,SACJ,kDACA;YACJ,gBAAgB,SACZ,kDACA;YACJ,gBAAgB,SAAS,oBAAoB;YAC7C,aAAa,SAAS,mBAAmB;YACzC,eAAe,SAAS,mBAAmB;YAC3C,WAAW,SAAS,mBAAmB;QACzC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OACE,IAAI,KAAK,YAAY,CAAC,SAAS;YAC7B,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,UAAU;IAExB;IAEA,MAAM,UAAU;QACd,IAAI,eAAe;QACnB,IAAI,kBAAkB,mBAAmB,MAAM;YAC7C,eAAe,AAAC,kBAAgC,OAAf,gBAAe;QAClD;QAEA,IAAI,eAAe;QACnB,IAAI,kBAAkB,mBAAmB,OAAO;YAC9C,eAAe,AAAC,gBAA8B,OAAf,gBAAe;QAChD;QAEA,MAAM,eAAe,mBACnB,AAAC,cAcgB,OAbnB,kBAAkB,mBAAmB,QAAQ,YAAY,IAAG,mYAa1B,OAAf,cAA4B,OAAb,cAAa;QAG/C,MAAM,eAAe,mBAAmB,cACrC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,QAAQ,KAChB,IAAI;QACP,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,SAAS,IAAI,CAClC,AAAC,GAAwC,gFACzC;gBAAE,OAAO;YAAe;YAG1B,MAAM,aAAa,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;YAC7C,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC;YAClC,UAAU,QAAQ,KAAK,KAAK,EAAE;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,oBAAoB;QACxB,MAAM,aAAa,SAChB,GAAG,CAAC,CAAC;YACJ,MAAM,aACJ,KAAK,IAAI,GAAG,IAAI,AAAC,KAAK,SAAS,GAAG,KAAK,IAAI,GAAI,MAAM;YACvD,MAAM,SACJ,cAAc,KACV,cACA,cAAc,KACd,aACA;YACN,OAAO;gBACL,MAAM,KAAK,aAAa;gBACxB,QAAQ,gBAAgB,KAAK,IAAI,GAAG;gBACpC,UAAU,gBAAgB,KAAK,SAAS,GAAG;gBAC3C,YAAY,KAAK,KAAK,CAAC;gBACvB;YACF;QACF,GACC,IAAI,CAAC,CAAC,GAAG;YACR,MAAM,UAAU,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY;YACxD,MAAM,UAAU,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY;YACxD,OAAO,UAAU;QACnB;QAEF,qBACE,6LAAC,kNAAA,CAAA,WAAQ;YAAC,WAAU;sBAClB,cAAA,6LAAC;gBAAI,WAAU;0BACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACnC,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAW,AAAC,qCAEX,OADC,kBAAkB,aAAa;kDAGhC,OAAO,IAAI;;;;;;;;;;;8CAGhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,WAAQ;oDACP,OAAO,OAAO,UAAU;oDACxB,OACE,OAAO,MAAM,KAAK,cACd,YACA,OAAO,MAAM,KAAK,aAClB,YACA;oDAEN,MAAK;oDACL,cAAW;oDACX,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAW,AAAC,wCAEX,OADC,UAAU,SAAS,eAAe;;4DAErC;4DACK,OAAO,QAAQ;4DAAC;4DAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;sDAI9C,6LAAC,yMAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,OACE,OAAO,MAAM,KAAK,cACd,YACA,OAAO,MAAM,KAAK,aAClB,YACA;4CAEN,WAAU;;gDAET,OAAO,UAAU;gDAAC;;;;;;;;;;;;;;;;;;;uBAlDpB;;;;;;;;;;;;;;;IA2DjB;IAEA,qBACE;;0BACE,6LAAC,yMAAA,CAAA,OAAI;gBACH,WAAW,AAAC,yBAEX,OADC,UAAU,kBAAkB,cAAc,GAAG,kBAAkB,MAAM,EACtE;;kCAED,6LAAC,sNAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAW,AAAC,sCAEX,OADC,kBAAkB,WAAW;8CAEhC;;;;;;8CAGD,6LAAC,yMAAA,CAAA,OAAI;oCACH,OAAM;oCACN,SAAS,IAAM,SAAS;oCACxB,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;oBAKJ,wBACC,6LAAC,kNAAA,CAAA,WAAQ;wBAAC,WAAU;kCAAoB;;;;;mEACtC,SAAS,MAAM,KAAK,kBACtB,6LAAC,kNAAA,CAAA,WAAQ;wBAAC,WAAU;kCAAoB;;;;;mEAExC;;;;;;;YAGH,uBACC,6LAAC,yKAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAO,SAAS,IAAM,SAAS;;;;;;;;AAI9D;GAjOa;;QAIO,mJAAA,CAAA,WAAQ;QACJ,uJAAA,CAAA,WAAQ;;;KALnB", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/dashboard/dataDipa/getDipa.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport { Card, CardBody } from \"@heroui/react\";\r\nimport {\r\n  ArrowDownRight,\r\n  ArrowUpRight,\r\n  Building2,\r\n  DollarSign,\r\n  Target,\r\n  TrendingUp,\r\n  Wallet,\r\n} from \"lucide-react\";\r\n\r\nexport const GetDipa = ({ selectedKanwil, selectedKddept }) => {\r\n  const [dataDipa, setDataDipa] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const context = useContext(MyContext);\r\n  const { theme } = useTheme();\r\n  const { showToast } = useToast();\r\n  const { token, axiosJWT } = context || {};\r\n\r\n  const getThemeClasses = () => {\r\n    const isDark = theme === \"dark\";\r\n    return {\r\n      cardBg: isDark\r\n        ? \"bg-gradient-to-br from-slate-800/90 to-slate-700/90\"\r\n        : \"bg-gradient-to-br from-white/90 to-slate-50/90\",\r\n      cardBgPrimary: isDark\r\n        ? \"bg-gradient-to-br from-blue-900/80 to-blue-800/70\"\r\n        : \"bg-gradient-to-br from-blue-50 to-blue-100/90\",\r\n      cardBgSuccess: isDark\r\n        ? \"bg-gradient-to-br from-green-900/80 to-green-800/70\"\r\n        : \"bg-gradient-to-br from-green-50 to-green-100/90\",\r\n      cardBgWarning: isDark\r\n        ? \"bg-gradient-to-br from-amber-900/80 to-amber-800/70\"\r\n        : \"bg-gradient-to-br from-amber-50 to-amber-100/90\",\r\n      cardBgSecondary: isDark\r\n        ? \"bg-gradient-to-br from-purple-900/80 to-purple-800/70\"\r\n        : \"bg-gradient-to-br from-purple-50 to-purple-100/90\",\r\n      textPrimary: isDark ? \"text-slate-100\" : \"text-slate-900\",\r\n      textSecondary: isDark ? \"text-slate-300\" : \"text-slate-600\",\r\n    };\r\n  };\r\n\r\n  const getData = async () => {\r\n    let kanwilFilter =\r\n      selectedKanwil && selectedKanwil !== \"00\"\r\n        ? ` and kdkanwil='${selectedKanwil}'`\r\n        : \"\";\r\n    let kddeptFilter =\r\n      selectedKddept && selectedKddept !== \"000\"\r\n        ? ` and kddept='${selectedKddept}'`\r\n        : \"\";\r\n\r\n    const query = `\r\n      SELECT SUM(pagu)/1000000000000 AS pagu, SUM(realisasi)/1000000000000 AS realisasi,\r\n      (SUM(pagu) - SUM(realisasi))/1000000000000 AS sisa,\r\n      (SELECT SUM(jml) FROM dashboard.dipa_satker_rekap where thang='2022'${kanwilFilter}${kddeptFilter}) AS jumlah_dipa,\r\n      ROUND(SUM(persen_dipa) / 1000000, 2) AS persen\r\n      FROM dashboard.pagu_real_kl prk\r\n      WHERE thang='2022' and kddept<>'999'${kanwilFilter}${kddeptFilter} LIMIT 1;\r\n    `;\r\n    const cleanedQuery = decodeURIComponent(query)\r\n      .replace(/\\n/g, \" \")\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n\r\n    try {\r\n      setLoading(true);\r\n      const response = await axiosJWT.post(\r\n        process.env.NEXT_PUBLIC_GET_REFERENSI,\r\n        { query: Encrypt(cleanedQuery) }\r\n      );\r\n      setDataDipa(response.data.result?.[0] || null);\r\n    } catch (err) {\r\n      const { data } = err.response || {};\r\n      showToast(data && data.error, \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, [selectedKanwil, selectedKddept]);\r\n\r\n  const formatTrillions = (val) =>\r\n    `${new Intl.NumberFormat(\"id-ID\", {\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2,\r\n    }).format(val)} T`;\r\n  const formatNumber = (val) => new Intl.NumberFormat(\"id-ID\").format(val);\r\n\r\n  if (loading || !dataDipa) return null; // Simplified loading/null handling for this JSX conversion\r\n\r\n  const realisasiPercentage = (dataDipa.realisasi / dataDipa.pagu) * 100;\r\n  const sisaPercentage = (dataDipa.sisa / dataDipa.pagu) * 100;\r\n\r\n  const stats = [\r\n    {\r\n      title: \"Total Pagu\",\r\n      value: formatTrillions(dataDipa.pagu),\r\n      icon: Wallet,\r\n      color: \"primary\",\r\n      trend: \"up\",\r\n      change: \"+2.5%\",\r\n    },\r\n    {\r\n      title: \"Realisasi\",\r\n      value: formatTrillions(dataDipa.realisasi),\r\n      icon: TrendingUp,\r\n      color: \"success\",\r\n      trend: realisasiPercentage > 50 ? \"up\" : \"down\",\r\n      change: `${realisasiPercentage.toFixed(1)}%`,\r\n    },\r\n    {\r\n      title: \"Sisa Anggaran\",\r\n      value: formatTrillions(dataDipa.sisa),\r\n      icon: DollarSign,\r\n      color: \"warning\",\r\n      trend: sisaPercentage > 30 ? \"up\" : \"down\",\r\n      change: `${sisaPercentage.toFixed(1)}%`,\r\n    },\r\n    {\r\n      title: \"Jumlah DIPA\",\r\n      value: formatNumber(dataDipa.jumlah_dipa),\r\n      icon: Building2,\r\n      color: \"secondary\",\r\n      trend: \"up\",\r\n      change: `${formatNumber(dataDipa.jumlah_dipa)} unit`,\r\n    },\r\n    {\r\n      title: \"Efisiensi Anggaran\",\r\n      value: `${dataDipa.persen}%`,\r\n      icon: Target,\r\n      color: \"primary\",\r\n      trend: dataDipa.persen > 80 ? \"up\" : \"down\",\r\n      change: `${dataDipa.persen}%`,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-12 gap-2 md:gap-3 mt-2 md:mt-3\">\r\n      {stats.map((stat, index) => (\r\n        <Card\r\n          key={index}\r\n          className={`border-none shadow-sm hover:shadow-md transition-shadow ${\r\n            getThemeClasses()[\r\n              `cardBg${\r\n                stat.color.charAt(0).toUpperCase() + stat.color.slice(1)\r\n              }`\r\n            ] || getThemeClasses().cardBg\r\n          } ${\r\n            index === 0\r\n              ? \"lg:col-span-2\"\r\n              : index === 1\r\n              ? \"lg:col-span-2\"\r\n              : index === 2\r\n              ? \"lg:col-span-2\"\r\n              : index === 3\r\n              ? \"lg:col-span-3\"\r\n              : \"lg:col-span-3\"\r\n          }`}\r\n        >\r\n          <CardBody className=\"p-2 md:p-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-1 md:gap-2 min-w-0 flex-1\">\r\n                <div\r\n                  className={`p-1 md:p-1.5 rounded-lg bg-${stat.color}/20 border border-${stat.color}/30 flex-shrink-0`}\r\n                >\r\n                  <stat.icon\r\n                    className={`h-3 w-3 md:h-4 md:w-4 text-${stat.color}`}\r\n                  />\r\n                </div>\r\n                <div className=\"min-w-0 flex-1\">\r\n                  <p\r\n                    className={`text-xs ${\r\n                      getThemeClasses().textSecondary\r\n                    } truncate`}\r\n                  >\r\n                    {stat.title}\r\n                  </p>\r\n                  <p\r\n                    className={`text-sm md:text-lg font-semibold ${\r\n                      getThemeClasses().textPrimary\r\n                    } truncate`}\r\n                  >\r\n                    {stat.value}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center gap-1 flex-shrink-0\">\r\n                {stat.trend === \"up\" ? (\r\n                  <ArrowUpRight className=\"h-2 w-2 md:h-3 md:w-3 text-success-600\" />\r\n                ) : (\r\n                  <ArrowDownRight className=\"h-2 w-2 md:h-3 md:w-3 text-danger-600\" />\r\n                )}\r\n                <span\r\n                  className={`text-xs font-medium ${\r\n                    stat.trend === \"up\" ? \"text-success-600\" : \"text-danger-600\"\r\n                  }`}\r\n                >\r\n                  {stat.change}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </CardBody>\r\n        </Card>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AA4EQ;;AA1ER;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAmBO,MAAM,UAAU;QAAC,EAAE,cAAc,EAAE,cAAc,EAAE;;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;IAExC,MAAM,kBAAkB;QACtB,MAAM,SAAS,UAAU;QACzB,OAAO;YACL,QAAQ,SACJ,wDACA;YACJ,eAAe,SACX,sDACA;YACJ,eAAe,SACX,wDACA;YACJ,eAAe,SACX,wDACA;YACJ,iBAAiB,SACb,0DACA;YACJ,aAAa,SAAS,mBAAmB;YACzC,eAAe,SAAS,mBAAmB;QAC7C;IACF;IAEA,MAAM,UAAU;QACd,IAAI,eACF,kBAAkB,mBAAmB,OACjC,AAAC,kBAAgC,OAAf,gBAAe,OACjC;QACN,IAAI,eACF,kBAAkB,mBAAmB,QACjC,AAAC,gBAA8B,OAAf,gBAAe,OAC/B;QAEN,MAAM,QAAQ,AAAC,oOAGwE,OAAf,cAGhC,OAH+C,cAAa,8JAG7C,OAAf,cAA4B,OAAb,cAAa;QAEpE,MAAM,eAAe,mBAAmB,OACrC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,IAAI;gBAMU;YALZ,WAAW;YACX,MAAM,WAAW,MAAM,SAAS,IAAI,yEAElC;gBAAE,OAAO,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;YAAc;YAEjC,YAAY,EAAA,wBAAA,SAAS,IAAI,CAAC,MAAM,cAApB,4CAAA,qBAAsB,CAAC,EAAE,KAAI;QAC3C,EAAE,OAAO,KAAK;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC;YAClC,UAAU,QAAQ,KAAK,KAAK,EAAE;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR;QACF;4BAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,kBAAkB,CAAC,MACvB,AAAC,GAGc,OAHZ,IAAI,KAAK,YAAY,CAAC,SAAS;YAChC,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,MAAK;IACjB,MAAM,eAAe,CAAC,MAAQ,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAEpE,IAAI,WAAW,CAAC,UAAU,OAAO,MAAM,2DAA2D;IAElG,MAAM,sBAAsB,AAAC,SAAS,SAAS,GAAG,SAAS,IAAI,GAAI;IACnE,MAAM,iBAAiB,AAAC,SAAS,IAAI,GAAG,SAAS,IAAI,GAAI;IAEzD,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,OAAO,gBAAgB,SAAS,IAAI;YACpC,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA;YACE,OAAO;YACP,OAAO,gBAAgB,SAAS,SAAS;YACzC,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO,sBAAsB,KAAK,OAAO;YACzC,QAAQ,AAAC,GAAiC,OAA/B,oBAAoB,OAAO,CAAC,IAAG;QAC5C;QACA;YACE,OAAO;YACP,OAAO,gBAAgB,SAAS,IAAI;YACpC,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO,iBAAiB,KAAK,OAAO;YACpC,QAAQ,AAAC,GAA4B,OAA1B,eAAe,OAAO,CAAC,IAAG;QACvC;QACA;YACE,OAAO;YACP,OAAO,aAAa,SAAS,WAAW;YACxC,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;YACP,QAAQ,AAAC,GAAqC,OAAnC,aAAa,SAAS,WAAW,GAAE;QAChD;QACA;YACE,OAAO;YACP,OAAO,AAAC,GAAkB,OAAhB,SAAS,MAAM,EAAC;YAC1B,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO,SAAS,MAAM,GAAG,KAAK,OAAO;YACrC,QAAQ,AAAC,GAAkB,OAAhB,SAAS,MAAM,EAAC;QAC7B;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,yMAAA,CAAA,OAAI;gBAEH,WAAW,AAAC,2DAOV,OANA,iBAAiB,CACf,AAAC,SAEA,OADC,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,IAEzD,IAAI,kBAAkB,MAAM,EAC9B,KAUA,OATC,UAAU,IACN,kBACA,UAAU,IACV,kBACA,UAAU,IACV,kBACA,UAAU,IACV,kBACA;0BAGN,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;8BAClB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,AAAC,8BAA4D,OAA/B,KAAK,KAAK,EAAC,sBAA+B,OAAX,KAAK,KAAK,EAAC;kDAEnF,cAAA,6LAAC,KAAK,IAAI;4CACR,WAAW,AAAC,8BAAwC,OAAX,KAAK,KAAK;;;;;;;;;;;kDAGvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,AAAC,WAEX,OADC,kBAAkB,aAAa,EAChC;0DAEA,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDACC,WAAW,AAAC,oCAEX,OADC,kBAAkB,WAAW,EAC9B;0DAEA,KAAK,KAAK;;;;;;;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,KAAK,KAAK,qBACd,6LAAC,6NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;iGAExB,6LAAC,iOAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,6LAAC;wCACC,WAAW,AAAC,uBAEX,OADC,KAAK,KAAK,KAAK,OAAO,qBAAqB;kDAG5C,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;eAzDf;;;;;;;;;;AAkEf;GAtMa;;QAIO,mJAAA,CAAA,WAAQ;QACJ,uJAAA,CAAA,WAAQ;;;KALnB", "debugId": null}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/dashboard/dataDipa/PerformaTerbesar.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nimport ModalPerforma from \"@/components/features/dashboard/modal/detailPerforma\";\r\nimport { handleHttpError } from \"@/lib/utils/handleError\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport {\r\n  Card,\r\n  CardBody,\r\n  CardHeader,\r\n  Chip,\r\n  Progress,\r\n  Skeleton,\r\n} from \"@heroui/react\";\r\nimport { Database, FileX } from \"lucide-react\";\r\n\r\nconst PerformaTerbesar = ({ selectedKanwil, selectedKddept }) => {\r\n  const [dataDipa, setDataDipa] = useState([]);\r\n  const [error, setError] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const context = useContext(MyContext);\r\n  const { theme } = useTheme();\r\n  const [modalPerforma, setModalPerforma] = useState(false);\r\n  const { token, axiosJWT } = context;\r\n\r\n  const getThemeClasses = () => {\r\n    const isDark = theme === \"dark\";\r\n    return {\r\n      cardBg: isDark\r\n        ? \"bg-gradient-to-br from-slate-800 to-slate-700\"\r\n        : \"bg-gradient-to-br from-slate-100 to-slate-200\",\r\n      skeletonCardBg: isDark\r\n        ? \"bg-gradient-to-br from-slate-800 to-slate-700\"\r\n        : \"bg-gradient-to-br from-default-50 to-default-100\",\r\n      skeletonItemBg: isDark ? \"bg-slate-700/50\" : \"bg-default-50\",\r\n      textPrimary: isDark ? \"text-slate-100\" : \"text-slate-900\",\r\n      textSecondary: isDark ? \"text-slate-300\" : \"text-slate-700\",\r\n      textMuted: isDark ? \"text-slate-400\" : \"text-slate-600\",\r\n    };\r\n  };\r\n\r\n  const formatTrillions = (amount) => {\r\n    return (\r\n      new Intl.NumberFormat(\"id-ID\", {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2,\r\n      }).format(amount) + \" T\"\r\n    );\r\n  };\r\n\r\n  const getData = async () => {\r\n    let kanwilFilter =\r\n      selectedKanwil && selectedKanwil !== \"00\"\r\n        ? ` and prk.kdkanwil='${selectedKanwil}'`\r\n        : \"\";\r\n\r\n    let kddeptFilter =\r\n      selectedKddept && selectedKddept !== \"000\"\r\n        ? ` and prk.kddept='${selectedKddept}'`\r\n        : \"\";\r\n\r\n    const encodedQuery = encodeURIComponent(`SELECT \r\n      prk.kddept,\r\n      td.nmdept,\r\n      SUM(prk.pagu) AS pagu,\r\n      SUM(prk.realisasi) AS realisasi,\r\n      SUM(prk.realisasi) / NULLIF(SUM(prk.pagu), 0) * 100 AS persen\r\n    FROM dashboard.pagu_real_kl prk\r\n    LEFT JOIN dbref.t_dept_2025 td ON prk.kddept = td.kddept\r\n    WHERE prk.thang = '2022'${kanwilFilter}${kddeptFilter}\r\n    GROUP BY prk.kddept\r\n    ORDER BY pagu DESC\r\n    LIMIT 4;`);\r\n\r\n    const cleanedQuery = decodeURIComponent(encodedQuery)\r\n      .replace(/\\n/g, \" \")\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n\r\n    const encryptedQuery = Encrypt(cleanedQuery);\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await axiosJWT.post(\r\n        `${process.env.NEXT_PUBLIC_GET_REFERENSI}`,\r\n        { query: encryptedQuery }\r\n      );\r\n\r\n      const resultData = response.data.result || [];\r\n      setDataDipa(resultData);\r\n    } catch (err) {\r\n      const { status, data } = err.response || {};\r\n      setDataDipa([]);\r\n      handleHttpError(\r\n        status,\r\n        (data && data.error) ||\r\n          \"Terjadi Permasalahan Koneksi atau Server Backend\"\r\n      );\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  const CardHeaderComponent = () => (\r\n    <CardHeader className=\"pb-2 px-4 md:px-6\">\r\n      <div className=\"flex justify-between items-center w-full\">\r\n        <h3\r\n          className={`text-sm md:text-base font-semibold ${\r\n            getThemeClasses().textPrimary\r\n          }`}\r\n        >\r\n          Performa K/L Terbesar\r\n        </h3>\r\n        <Chip\r\n          color=\"primary\"\r\n          onClick={() => setModalPerforma(true)}\r\n          variant=\"flat\"\r\n          size=\"sm\"\r\n          className=\"w-fit cursor-pointer\"\r\n        >\r\n          Detail\r\n        </Chip>\r\n      </div>\r\n    </CardHeader>\r\n  );\r\n\r\n  const renderLoadingContent = () => (\r\n    <CardBody className=\"pt-0 px-4 md:px-6\">\r\n      <div className=\"space-y-2 md:space-y-3\">\r\n        {Array.from({ length: 2 }).map((_, index) => (\r\n          <div\r\n            key={index}\r\n            className={`p-2 md:p-3 ${\r\n              getThemeClasses().skeletonItemBg\r\n            } rounded-lg space-y-2`}\r\n          >\r\n            <div className=\"flex justify-between items-center\">\r\n              <Skeleton className=\"h-4 w-40 rounded\" />\r\n              <Skeleton className=\"h-6 w-12 rounded-full\" />\r\n            </div>\r\n            <Skeleton className=\"h-3 w-32 rounded\" />\r\n            <Skeleton className=\"h-2 w-full rounded-full\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </CardBody>\r\n  );\r\n\r\n  const renderEmptyContent = () => (\r\n    <CardBody className=\"pt-0 px-4 md:px-6\">\r\n      <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n        <FileX className=\"w-12 h-12 text-default-400 mb-4\" />\r\n        <div className=\"mt-4\">\r\n          <Chip\r\n            size=\"sm\"\r\n            variant=\"flat\"\r\n            color=\"warning\"\r\n            startContent={<Database className=\"w-3 h-3\" />}\r\n            className=\"text-xs\"\r\n          >\r\n            Data Tidak Tersedia\r\n          </Chip>\r\n        </div>\r\n      </div>\r\n    </CardBody>\r\n  );\r\n\r\n  const renderMainContent = () => {\r\n    const ministryData = dataDipa\r\n      .map((item) => {\r\n        const percentage =\r\n          item.pagu > 0 ? (item.realisasi / item.pagu) * 100 : 0;\r\n        const status =\r\n          percentage >= 90\r\n            ? \"excellent\"\r\n            : percentage >= 80\r\n            ? \"on-track\"\r\n            : \"warning\";\r\n        return {\r\n          name: item.nmdept || `K/L ${item.kddept}`,\r\n          budget: formatTrillions(item.pagu / 1000000000000),\r\n          realized: formatTrillions(item.realisasi / 1000000000000),\r\n          percentage: Math.round(percentage),\r\n          status,\r\n        };\r\n      })\r\n      .sort((a, b) => b.percentage - a.percentage);\r\n\r\n    return (\r\n      <CardBody className=\"pt-0 px-4 md:px-6\">\r\n        <div className=\"space-y-0\">\r\n          {ministryData.slice(0, 4).map((ministry, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"flex items-center justify-between p-0.5 md:p-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors duration-200\"\r\n            >\r\n              <div className=\"flex-1 min-w-0\">\r\n                <div className=\"mb-0.5\">\r\n                  <h4\r\n                    className={`font-medium text-xs truncate pr-2 ${\r\n                      getThemeClasses().textSecondary\r\n                    }`}\r\n                  >\r\n                    {ministry.name}\r\n                  </h4>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <div className=\"relative flex-1\">\r\n                    <Progress\r\n                      value={ministry.percentage}\r\n                      color={\r\n                        ministry.status === \"excellent\"\r\n                          ? \"success\"\r\n                          : ministry.status === \"on-track\"\r\n                          ? \"primary\"\r\n                          : \"warning\"\r\n                      }\r\n                      aria-label=\"performaterbesar\"\r\n                      size=\"md\"\r\n                      className=\"w-full h-6\"\r\n                      classNames={{\r\n                        track:\r\n                          \"h-6 bg-gradient-to-r from-default-100 to-default-200 dark:from-slate-700 dark:to-slate-600 shadow-inner rounded-full border border-default-200 dark:border-slate-600\",\r\n                        indicator: `h-6 rounded-full shadow-lg transition-all duration-500 ease-out ${\r\n                          ministry.status === \"excellent\"\r\n                            ? \"bg-gradient-to-r from-green-400 to-emerald-500 shadow-green-200 dark:shadow-green-900/50\"\r\n                            : ministry.status === \"on-track\"\r\n                            ? \"bg-gradient-to-r from-blue-400 to-indigo-500 shadow-blue-200 dark:shadow-blue-900/50\"\r\n                            : \"bg-gradient-to-r from-amber-400 to-orange-500 shadow-amber-200 dark:shadow-amber-900/50\"\r\n                        }`,\r\n                        label: \"hidden\",\r\n                      }}\r\n                    />\r\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                      <span\r\n                        className={`text-xs font-semibold drop-shadow-sm ${\r\n                          theme === \"dark\" ? \"text-white\" : \"text-slate-800\"\r\n                        }`}\r\n                      >\r\n                        Rp {ministry.realized} / Rp {ministry.budget}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <Chip\r\n                    size=\"sm\"\r\n                    variant=\"flat\"\r\n                    color={\r\n                      ministry.status === \"excellent\"\r\n                        ? \"success\"\r\n                        : ministry.status === \"on-track\"\r\n                        ? \"primary\"\r\n                        : \"warning\"\r\n                    }\r\n                    className=\"text-xs flex-shrink-0 font-semibold shadow-sm\"\r\n                  >\r\n                    {ministry.percentage}%\r\n                  </Chip>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </CardBody>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Card\r\n        className={`border-none shadow-sm ${\r\n          loading ? getThemeClasses().skeletonCardBg : getThemeClasses().cardBg\r\n        } lg:col-span-6 xl:col-span-3`}\r\n      >\r\n        <CardHeaderComponent />\r\n        {loading\r\n          ? renderLoadingContent()\r\n          : dataDipa.length === 0\r\n          ? renderEmptyContent()\r\n          : renderMainContent()}\r\n      </Card>\r\n      {modalPerforma && (\r\n        <ModalPerforma\r\n          isOpen={modalPerforma}\r\n          onClose={() => setModalPerforma(false)}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PerformaTerbesar;\r\n"], "names": [], "mappings": ";;;AAwFW;;AAtFX;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;;;AAjBA;;;;;;;;;AAmBA,MAAM,mBAAmB;QAAC,EAAE,cAAc,EAAE,cAAc,EAAE;;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,kBAAkB;QACtB,MAAM,SAAS,UAAU;QACzB,OAAO;YACL,QAAQ,SACJ,kDACA;YACJ,gBAAgB,SACZ,kDACA;YACJ,gBAAgB,SAAS,oBAAoB;YAC7C,aAAa,SAAS,mBAAmB;YACzC,eAAe,SAAS,mBAAmB;YAC3C,WAAW,SAAS,mBAAmB;QACzC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OACE,IAAI,KAAK,YAAY,CAAC,SAAS;YAC7B,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,UAAU;IAExB;IAEA,MAAM,UAAU;QACd,IAAI,eACF,kBAAkB,mBAAmB,OACjC,AAAC,sBAAoC,OAAf,gBAAe,OACrC;QAEN,IAAI,eACF,kBAAkB,mBAAmB,QACjC,AAAC,oBAAkC,OAAf,gBAAe,OACnC;QAEN,MAAM,eAAe,mBAAmB,AAAC,2TAQA,OAAf,cAA4B,OAAb,cAAa;QAKtD,MAAM,eAAe,mBAAmB,cACrC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,SAAS,IAAI,CAClC,AAAC,GAAwC,gFACzC;gBAAE,OAAO;YAAe;YAG1B,MAAM,aAAa,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;YAC7C,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC;YAC1C,YAAY,EAAE;YACd,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACZ,QACA,AAAC,QAAQ,KAAK,KAAK,IACjB;QAEN,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,sBAAsB,kBAC1B,6LAAC,sNAAA,CAAA,aAAU;YAAC,WAAU;sBACpB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAW,AAAC,sCAEX,OADC,kBAAkB,WAAW;kCAEhC;;;;;;kCAGD,6LAAC,yMAAA,CAAA,OAAI;wBACH,OAAM;wBACN,SAAS,IAAM,iBAAiB;wBAChC,SAAQ;wBACR,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAOP,MAAM,uBAAuB,kBAC3B,6LAAC,kNAAA,CAAA,WAAQ;YAAC,WAAU;sBAClB,cAAA,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;wBAEC,WAAW,AAAC,cAEX,OADC,kBAAkB,cAAc,EACjC;;0CAED,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,qNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,qNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,qNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;uBAVf;;;;;;;;;;;;;;;IAiBf,MAAM,qBAAqB,kBACzB,6LAAC,kNAAA,CAAA,WAAQ;YAAC,WAAU;sBAClB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,OAAM;4BACN,4BAAc,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAClC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAQT,MAAM,oBAAoB;QACxB,MAAM,eAAe,SAClB,GAAG,CAAC,CAAC;YACJ,MAAM,aACJ,KAAK,IAAI,GAAG,IAAI,AAAC,KAAK,SAAS,GAAG,KAAK,IAAI,GAAI,MAAM;YACvD,MAAM,SACJ,cAAc,KACV,cACA,cAAc,KACd,aACA;YACN,OAAO;gBACL,MAAM,KAAK,MAAM,IAAI,AAAC,OAAkB,OAAZ,KAAK,MAAM;gBACvC,QAAQ,gBAAgB,KAAK,IAAI,GAAG;gBACpC,UAAU,gBAAgB,KAAK,SAAS,GAAG;gBAC3C,YAAY,KAAK,KAAK,CAAC;gBACvB;YACF;QACF,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;QAE7C,qBACE,6LAAC,kNAAA,CAAA,WAAQ;YAAC,WAAU;sBAClB,cAAA,6LAAC;gBAAI,WAAU;0BACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACvC,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAW,AAAC,qCAEX,OADC,kBAAkB,aAAa;kDAGhC,SAAS,IAAI;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,WAAQ;oDACP,OAAO,SAAS,UAAU;oDAC1B,OACE,SAAS,MAAM,KAAK,cAChB,YACA,SAAS,MAAM,KAAK,aACpB,YACA;oDAEN,cAAW;oDACX,MAAK;oDACL,WAAU;oDACV,YAAY;wDACV,OACE;wDACF,WAAW,AAAC,mEAMX,OALC,SAAS,MAAM,KAAK,cAChB,6FACA,SAAS,MAAM,KAAK,aACpB,yFACA;wDAEN,OAAO;oDACT;;;;;;8DAEF,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAW,AAAC,wCAEX,OADC,UAAU,SAAS,eAAe;;4DAErC;4DACK,SAAS,QAAQ;4DAAC;4DAAO,SAAS,MAAM;;;;;;;;;;;;;;;;;;sDAIlD,6LAAC,yMAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,OACE,SAAS,MAAM,KAAK,cAChB,YACA,SAAS,MAAM,KAAK,aACpB,YACA;4CAEN,WAAU;;gDAET,SAAS,UAAU;gDAAC;;;;;;;;;;;;;;;;;;;uBA9DtB;;;;;;;;;;;;;;;IAuEjB;IAEA,qBACE;;0BACE,6LAAC,yMAAA,CAAA,OAAI;gBACH,WAAW,AAAC,yBAEX,OADC,UAAU,kBAAkB,cAAc,GAAG,kBAAkB,MAAM,EACtE;;kCAED,6LAAC;;;;;oBACA,UACG,yBACA,SAAS,MAAM,KAAK,IACpB,uBACA;;;;;;;YAEL,+BACC,6LAAC,yKAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;;;AAK1C;GArRM;;QAKc,mJAAA,CAAA,WAAQ;;;KALtB;uCAuRS", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/Kanwil.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Autocomplete, AutocompleteItem, Skeleton } from \"@heroui/react\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport MyContext from \"@/stores/data/Context\";\r\n\r\nexport const Kanwil = ({\r\n  onKanwilChange,\r\n  selectedKanwil: propSelectedKanwil,\r\n}) => {\r\n  const [dataKanwil, setDataKanwil] = useState([]);\r\n  const [error, setError] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedKey, setSelectedKey] = useState(propSelectedKanwil || \"00\");\r\n  const context = useContext(MyContext);\r\n  const { showToast } = useToast();\r\n\r\n  const { token, axiosJWT, statusLogin } = context;\r\n\r\n  const getData = async () => {\r\n    const encodedQuery = encodeURIComponent(\r\n      `SELECT kdkanwil, nmkanwil FROM dbref.t_kanwil_2025 where kdkanwil<>'00' order by kdkanwil asc;`\r\n    );\r\n    const cleanedQuery = decodeURIComponent(encodedQuery)\r\n      .replace(/\\n/g, \" \")\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n    const encryptedQuery = Encrypt(cleanedQuery);\r\n\r\n    try {\r\n      setLoading(true);\r\n\r\n      const response = await axiosJWT.post(\r\n        `${process.env.NEXT_PUBLIC_GET_REFERENSI}`,\r\n        { query: encryptedQuery },\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Accept: \"application/json, text/plain, */*\",\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      // Add \"Semua Kanwil\" option at the beginning\r\n      const allKanwilOption = { kdkanwil: \"00\", nmkanwil: \"SEMUA KANWIL\" };\r\n      const kanwilData = [allKanwilOption, ...(response.data.result || [])];\r\n      setDataKanwil(kanwilData);\r\n    } catch (err) {\r\n      const { data } = err.response || {};\r\n      showToast(data && data.error, \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  // Sync dengan prop selectedKanwil jika berubah\r\n  useEffect(() => {\r\n    if (propSelectedKanwil !== undefined) {\r\n      setSelectedKey(propSelectedKanwil);\r\n    }\r\n  }, [propSelectedKanwil]);\r\n\r\n  const handleSelectionChange = (key) => {\r\n    setSelectedKey(key);\r\n    // Kirim nilai ke parent component melalui props callback\r\n    if (onKanwilChange) {\r\n      onKanwilChange(key);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <Skeleton className=\"w-full sm:w-44 lg:w-52 h-10 rounded-lg\" />;\r\n  }\r\n\r\n  return (\r\n    <Autocomplete\r\n      placeholder=\"Pilih Kanwil\"\r\n      className=\"w-full sm:w-44 lg:w-52 h-10\"\r\n      aria-label=\"kanwil\"\r\n      size=\"md\"\r\n      variant=\"flat\"\r\n      color=\"default\"\r\n      selectedKey={selectedKey}\r\n      onSelectionChange={(key) => handleSelectionChange(key)}\r\n      classNames={{\r\n        base: \"rounded-lg bg-slate-50/80 dark:bg-slate-800/80\",\r\n        selectorButton:\r\n          \"rounded-lg bg-slate-50/80 dark:bg-slate-800/80 border-slate-200/60 dark:border-slate-700/60 hover:bg-slate-100/80 dark:hover:bg-slate-700/80 data-[hover=true]:bg-slate-100/80 dark:data-[hover=true]:bg-slate-700/80\",\r\n        listbox: \"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 p-2\",\r\n        popoverContent:\r\n          \"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 backdrop-blur-sm border-slate-200/60 dark:border-slate-700/60 !z-50 !mt-2 shadow-lg\",\r\n      }}\r\n      allowsCustomValue\r\n      defaultItems={dataKanwil}\r\n    >\r\n      {(item) => (\r\n        <AutocompleteItem\r\n          key={item.kdkanwil}\r\n          textValue={\r\n            item.kdkanwil === \"00\" ? item.nmkanwil : `${item.nmkanwil}`\r\n          }\r\n        >\r\n          {item.kdkanwil === \"00\" ? item.nmkanwil : `${item.nmkanwil}`}\r\n        </AutocompleteItem>\r\n      )}\r\n    </Autocomplete>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AAoCW;;AAlCX;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;AAPA;;;;;;AASO,MAAM,SAAS;QAAC,EACrB,cAAc,EACd,gBAAgB,kBAAkB,EACnC;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,sBAAsB;IACrE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAEzC,MAAM,UAAU;QACd,MAAM,eAAe,mBAClB;QAEH,MAAM,eAAe,mBAAmB,cACrC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,QAAQ,KAChB,IAAI;QACP,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,IAAI;YACF,WAAW;YAEX,MAAM,WAAW,MAAM,SAAS,IAAI,CAClC,AAAC,GAAwC,gFACzC;gBAAE,OAAO;YAAe,GACxB;gBACE,SAAS;oBACP,gBAAgB;oBAChB,QAAQ;oBACR,eAAe,AAAC,UAAe,OAAN;gBAC3B;YACF;YAGF,6CAA6C;YAC7C,MAAM,kBAAkB;gBAAE,UAAU;gBAAM,UAAU;YAAe;YACnE,MAAM,aAAa;gBAAC;mBAAqB,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;aAAE;YACrE,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC;YAClC,UAAU,QAAQ,KAAK,KAAK,EAAE;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,uBAAuB,WAAW;gBACpC,eAAe;YACjB;QACF;2BAAG;QAAC;KAAmB;IAEvB,MAAM,wBAAwB,CAAC;QAC7B,eAAe;QACf,yDAAyD;QACzD,IAAI,gBAAgB;YAClB,eAAe;QACjB;IACF;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,qBACE,6LAAC,iOAAA,CAAA,eAAY;QACX,aAAY;QACZ,WAAU;QACV,cAAW;QACX,MAAK;QACL,SAAQ;QACR,OAAM;QACN,aAAa;QACb,mBAAmB,CAAC,MAAQ,sBAAsB;QAClD,YAAY;YACV,MAAM;YACN,gBACE;YACF,SAAS;YACT,gBACE;QACJ;QACA,iBAAiB;QACjB,cAAc;kBAEb,CAAC,qBACA,6LAAC,qOAAA,CAAA,mBAAgB;gBAEf,WACE,KAAK,QAAQ,KAAK,OAAO,KAAK,QAAQ,GAAG,AAAC,GAAgB,OAAd,KAAK,QAAQ;0BAG1D,KAAK,QAAQ,KAAK,OAAO,KAAK,QAAQ,GAAG,AAAC,GAAgB,OAAd,KAAK,QAAQ;eALrD,KAAK,QAAQ;;;;;;;;;;AAU5B;GA1Ga;;QASW,uJAAA,CAAA,WAAQ;;;KATnB", "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/Kddept.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Autocomplete, AutocompleteItem, Skeleton } from \"@heroui/react\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport MyContext from \"@/stores/data/Context\";\r\n\r\nexport const Kddept = ({ onKddeptChange, selectedKddept }) => {\r\n  const [dataKddept, setDataKddept] = useState([]);\r\n  const [error, setError] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const context = useContext(MyContext);\r\n  const { showToast } = useToast();\r\n\r\n  const { token, axiosJWT, statusLogin } = context;\r\n\r\n  const getData = async () => {\r\n    const encodedQuery = encodeURIComponent(\r\n      `SELECT kddept, nmdept FROM dbref.t_dept_2025  order by kddept asc;`\r\n    );\r\n    const cleanedQuery = decodeURIComponent(encodedQuery)\r\n      .replace(/\\n/g, \" \")\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n    const encryptedQuery = Encrypt(cleanedQuery);\r\n\r\n    try {\r\n      setLoading(true);\r\n\r\n      const response = await axiosJWT.post(\r\n        `${process.env.NEXT_PUBLIC_GET_REFERENSI}`,\r\n        { query: encryptedQuery },\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Accept: \"application/json, text/plain, */*\",\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      // Add \"Nasional\" option at the beginning\r\n      const nasionalOption = { kddept: \"000\", nmdept: \"SEMUA KL\" };\r\n      const deptData = [nasionalOption, ...(response.data.result || [])];\r\n      setDataKddept(deptData);\r\n    } catch (err) {\r\n      const { data } = err.response || {};\r\n      showToast(data && data.error, \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return <Skeleton className=\"w-full sm:w-44 lg:w-52 h-10 rounded-lg\" />;\r\n  }\r\n\r\n  return (\r\n    <Autocomplete\r\n      placeholder=\"Pilih Kementerian\"\r\n      aria-label=\"kddept\"\r\n      className=\"w-full sm:w-44 lg:w-52 h-10\"\r\n      size=\"md\"\r\n      variant=\"flat\"\r\n      color=\"default\"\r\n      selectedKey={selectedKddept}\r\n      onSelectionChange={(key) => onKddeptChange(key)}\r\n      classNames={{\r\n        base: \"rounded-lg bg-slate-50/80 dark:bg-slate-800/80\",\r\n        selectorButton:\r\n          \"rounded-lg bg-slate-50/80 dark:bg-slate-800/80 border-slate-200/60 dark:border-slate-700/60 hover:bg-slate-100/80 dark:hover:bg-slate-700/80 data-[hover=true]:bg-slate-100/80 dark:data-[hover=true]:bg-slate-700/80\",\r\n        listbox: \"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 p-2\",\r\n        popoverContent:\r\n          \"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 backdrop-blur-sm border-slate-200/60 dark:border-slate-700/60 !z-50 !mt-2 shadow-lg\",\r\n      }}\r\n      allowsCustomValue\r\n      defaultItems={dataKddept}\r\n    >\r\n      {(item) => (\r\n        <AutocompleteItem\r\n          key={item.kddept}\r\n          textValue={\r\n            item.kddept === \"000\"\r\n              ? item.nmdept\r\n              : `${item.nmdept} ${item.kddept}`\r\n          }\r\n        >\r\n          {item.kddept === \"000\"\r\n            ? item.nmdept\r\n            : `${item.nmdept} - (${item.kddept})`}\r\n        </AutocompleteItem>\r\n      )}\r\n    </Autocomplete>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AAgCW;;AA9BX;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;AAPA;;;;;;AASO,MAAM,SAAS;QAAC,EAAE,cAAc,EAAE,cAAc,EAAE;;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IAEzC,MAAM,UAAU;QACd,MAAM,eAAe,mBAClB;QAEH,MAAM,eAAe,mBAAmB,cACrC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,QAAQ,KAChB,IAAI;QACP,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,IAAI;YACF,WAAW;YAEX,MAAM,WAAW,MAAM,SAAS,IAAI,CAClC,AAAC,GAAwC,gFACzC;gBAAE,OAAO;YAAe,GACxB;gBACE,SAAS;oBACP,gBAAgB;oBAChB,QAAQ;oBACR,eAAe,AAAC,UAAe,OAAN;gBAC3B;YACF;YAGF,yCAAyC;YACzC,MAAM,iBAAiB;gBAAE,QAAQ;gBAAO,QAAQ;YAAW;YAC3D,MAAM,WAAW;gBAAC;mBAAoB,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;aAAE;YAClE,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,IAAI,CAAC;YAClC,UAAU,QAAQ,KAAK,KAAK,EAAE;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBAAO,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,qBACE,6LAAC,iOAAA,CAAA,eAAY;QACX,aAAY;QACZ,cAAW;QACX,WAAU;QACV,MAAK;QACL,SAAQ;QACR,OAAM;QACN,aAAa;QACb,mBAAmB,CAAC,MAAQ,eAAe;QAC3C,YAAY;YACV,MAAM;YACN,gBACE;YACF,SAAS;YACT,gBACE;QACJ;QACA,iBAAiB;QACjB,cAAc;kBAEb,CAAC,qBACA,6LAAC,qOAAA,CAAA,mBAAgB;gBAEf,WACE,KAAK,MAAM,KAAK,QACZ,KAAK,MAAM,GACX,AAAC,GAAiB,OAAf,KAAK,MAAM,EAAC,KAAe,OAAZ,KAAK,MAAM;0BAGlC,KAAK,MAAM,KAAK,QACb,KAAK,MAAM,GACX,AAAC,GAAoB,OAAlB,KAAK,MAAM,EAAC,QAAkB,OAAZ,KAAK,MAAM,EAAC;eAThC,KAAK,MAAM;;;;;;;;;;AAc1B;GA3Fa;;QAKW,uJAAA,CAAA,WAAQ;;;KALnB", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/Thang.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport \"./style.css\";\r\n\r\nconst Thang = ({ jenlap, onChange, value }) => {\r\n  const thang = [\r\n    { value: \"2014\", label: \"2014\" },\r\n    { value: \"2015\", label: \"2015\" },\r\n    { value: \"2016\", label: \"2016\" },\r\n    { value: \"2017\", label: \"2017\" },\r\n    { value: \"2018\", label: \"2018\" },\r\n    { value: \"2019\", label: \"2019\" },\r\n    { value: \"2020\", label: \"2020\" },\r\n    { value: \"2021\", label: \"2021\" },\r\n    { value: \"2022\", label: \"2022\" },\r\n    { value: \"2023\", label: \"2023\" },\r\n    { value: \"2024\", label: \"2024\" },\r\n    { value: \"2025\", label: \"2025\" },\r\n  ];\r\n\r\n  const handleSelectionChange = (selectedKey) => {\r\n    if (selectedKey && selectedKey.size > 0) {\r\n      const selectedValue = Array.from(selectedKey)[0];\r\n      onChange(selectedValue);\r\n    }\r\n  };\r\n\r\n  const isDisabled = (tahunValue) => {\r\n    return (\r\n      (jenlap === \"1\" && tahunValue < \"2019\") ||\r\n      (jenlap === \"6\" && tahunValue < \"2018\") ||\r\n      (jenlap === \"7\" && tahunValue < \"2024\")\r\n    );\r\n  };\r\n\r\n  const availableYears = thang.filter((tahun) => !isDisabled(tahun.value));\r\n\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"w-1 h-4 bg-gradient-to-b from-blue-500 to-indigo-500 rounded-full\"></div>\r\n        <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\r\n          Tahun\r\n        </label>\r\n      </div>\r\n\r\n      <Select\r\n        placeholder=\"Pilih Tahun\"\r\n        selectedKeys={value ? new Set([value]) : new Set()}\r\n        onSelectionChange={handleSelectionChange}\r\n        variant=\"bordered\"\r\n        size=\"sm\"\r\n        classNames={{\r\n          trigger:\r\n            \"bg-white dark:bg-slate-700 border-blue-200 dark:border-blue-600 hover:border-blue-300 dark:hover:border-blue-500\",\r\n          value: \"text-slate-700 dark:text-slate-300\",\r\n          label: \"text-slate-600 dark:text-slate-400\",\r\n        }}\r\n      >\r\n        {availableYears.map((tahun) => (\r\n          <SelectItem\r\n            key={tahun.value}\r\n            value={tahun.value}\r\n            className=\"text-slate-700 dark:text-slate-300\"\r\n          >\r\n            {tahun.label}\r\n          </SelectItem>\r\n        ))}\r\n      </Select>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Thang;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAFA;;;;;AAKA,MAAM,QAAQ;QAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;IACxC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAQ,OAAO;QAAO;KAChC;IAED,MAAM,wBAAwB,CAAC;QAC7B,IAAI,eAAe,YAAY,IAAI,GAAG,GAAG;YACvC,MAAM,gBAAgB,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE;YAChD,SAAS;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OACE,AAAC,WAAW,OAAO,aAAa,UAC/B,WAAW,OAAO,aAAa,UAC/B,WAAW,OAAO,aAAa;IAEpC;IAEA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,QAAU,CAAC,WAAW,MAAM,KAAK;IAEtE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAM,WAAU;kCAA2D;;;;;;;;;;;;0BAK9E,6LAAC,+MAAA,CAAA,SAAM;gBACL,aAAY;gBACZ,cAAc,QAAQ,IAAI,IAAI;oBAAC;iBAAM,IAAI,IAAI;gBAC7C,mBAAmB;gBACnB,SAAQ;gBACR,MAAK;gBACL,YAAY;oBACV,SACE;oBACF,OAAO;oBACP,OAAO;gBACT;0BAEC,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC,+NAAA,CAAA,aAAU;wBAET,OAAO,MAAM,KAAK;wBAClB,WAAU;kCAET,MAAM,KAAK;uBAJP,MAAM,KAAK;;;;;;;;;;;;;;;;AAU5B;KAnEM;uCAqES", "debugId": null}}, {"offset": {"line": 1736, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/charts/fungsi.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Encrypt from \"@/lib/utils/random\";\r\nimport { Card, CardBody, Chip, Skeleton } from \"@heroui/react\";\r\nimport { Database, FileX } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { useContext, useEffect, useState } from \"react\";\r\n\r\nconst Chart = dynamic(() => import(\"./client-chart\"), {\r\n  ssr: false,\r\n  loading: () => (\r\n    <div className=\"w-full h-full flex items-center justify-center\">\r\n      <Skeleton className=\"w-full h-64\" />\r\n    </div>\r\n  ),\r\n});\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport MyContext from \"@/stores/data/Context\";\r\n\r\nexport const Fungsi = ({ selectedKanwil, selectedKddept }) => {\r\n  const [dataDipaPerFungsi, setDataDipaPerFungsi] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const { theme } = useTheme();\r\n  const { showToast } = useToast();\r\n  const context = useContext(MyContext);\r\n  const { token, axiosJWT } = context;\r\n\r\n  const formatTrillions = (amount) => {\r\n    return (\r\n      new Intl.NumberFormat(\"id-ID\", {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2,\r\n      }).format(amount / 1_000_000_000_000) + \" T\"\r\n    );\r\n  };\r\n\r\n  const getThemeColors = () => {\r\n    const isDark = theme === \"dark\";\r\n    return {\r\n      foreColor: isDark ? \"#a1a1aa\" : \"#52525b\",\r\n      borderColor: isDark ? \"#3f3f46\" : \"#e4e4e7\",\r\n      gridColor: isDark ? \"#27272a\" : \"#f4f4f5\",\r\n      axisColor: isDark ? \"#52525b\" : \"#71717a\",\r\n      primary: \"#3B82F6\",\r\n      success: \"#10B981\",\r\n      textPrimary: isDark ? \"#f3f4f6\" : \"#374151\",\r\n    };\r\n  };\r\n\r\n  const getData = async () => {\r\n    let kanwilFilter =\r\n      selectedKanwil && selectedKanwil !== \"00\"\r\n        ? ` and a.kdkanwil='${selectedKanwil}'`\r\n        : \"\";\r\n    let kddeptFilter =\r\n      selectedKddept && selectedKddept !== \"000\"\r\n        ? ` and a.kddept='${selectedKddept}'`\r\n        : \"\";\r\n\r\n    const encodedQuery = encodeURIComponent(`\r\n      SELECT a.thang, a.kddept, c.nmdept, a.kdfungsi, b.nmfungsi, a.kdkanwil, k.nmkanwil,\r\n             SUM(a.pagu) AS pagu, SUM(a.realisasi) AS realisasi\r\n      FROM dashboard.dipa_per_fungsi a\r\n      LEFT JOIN dbref.t_fungsi_2025 b ON a.kdfungsi = b.kdfungsi\r\n      LEFT JOIN dbref.t_kanwil_2025 k ON a.kdkanwil = k.kdkanwil\r\n      LEFT JOIN dbref.t_dept_2025 c ON a.kddept = c.kddept\r\n      WHERE a.thang='2025' ${kanwilFilter} ${kddeptFilter}\r\n      GROUP BY a.kdfungsi\r\n      ORDER BY a.thang, a.kdfungsi;\r\n    `);\r\n\r\n    const cleanedQuery = decodeURIComponent(encodedQuery)\r\n      .replace(/\\n/g, \" \")\r\n      .replace(/\\s+/g, \" \")\r\n      .trim();\r\n    const encryptedQuery = Encrypt(cleanedQuery);\r\n\r\n    try {\r\n      setLoading(true);\r\n      const response = await axiosJWT.post(\r\n        process.env.NEXT_PUBLIC_GET_REFERENSI,\r\n        { query: encryptedQuery }\r\n      );\r\n      setDataDipaPerFungsi(response.data.result || []);\r\n    } catch (err) {\r\n      showToast(\"Terjadi Permasalahan Koneksi atau Server Backend\", \"error\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, [selectedKanwil, selectedKddept]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card className=\"border-none shadow-sm bg-gradient-to-br from-default-50 to-default-100\">\r\n        <CardBody className=\"p-6 space-y-4\">\r\n          {Array.from({ length: 5 }).map((_, i) => (\r\n            <div key={i} className=\"flex justify-between items-center\">\r\n              <Skeleton className=\"h-4 w-32 rounded\" />\r\n              <Skeleton className=\"h-6 w-16 rounded-full\" />\r\n            </div>\r\n          ))}\r\n        </CardBody>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  const getThemeClasses = () => {\r\n    const isDark = theme === \"dark\";\r\n    return {\r\n      cardBg: isDark\r\n        ? \"bg-gradient-to-br from-slate-800/90 to-slate-700/90\"\r\n        : \"bg-gradient-to-br from-white/90 to-slate-50/90\",\r\n    };\r\n  };\r\n\r\n  const isEmpty =\r\n    dataDipaPerFungsi.length === 0 ||\r\n    Object.values(dataDipaPerFungsi[0])\r\n      .slice(2)\r\n      .every((val) => val === 0 || val === null);\r\n\r\n  if (isEmpty) {\r\n    return (\r\n      <div className=\"w-full h-full\">\r\n        <Card\r\n          className={`border-none shadow-sm ${getThemeClasses().cardBg} h-full`}\r\n        >\r\n          <CardBody className=\"pt-0 px-4 md:px-6 flex flex-col items-center justify-center text-center py-8\">\r\n            <FileX className=\"w-12 h-12 text-default-400 mb-4\" />\r\n            <Chip\r\n              size=\"sm\"\r\n              variant=\"flat\"\r\n              color=\"warning\"\r\n              startContent={<Database className=\"w-3 h-3\" />}\r\n              className=\"text-xs\"\r\n            >\r\n              Data Tidak Tersedia\r\n            </Chip>\r\n          </CardBody>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const fungsiMap = new Map();\r\n  dataDipaPerFungsi.forEach((item) => {\r\n    const key = item.kdfungsi;\r\n    const existing = fungsiMap.get(key);\r\n    if (existing) {\r\n      existing.pagu += item.pagu || 0;\r\n      existing.realisasi += item.realisasi || 0;\r\n    } else {\r\n      fungsiMap.set(key, {\r\n        nmfungsi: item.nmfungsi || `Fungsi ${key}`,\r\n        pagu: item.pagu || 0,\r\n        realisasi: item.realisasi || 0,\r\n      });\r\n    }\r\n  });\r\n\r\n  const sortedEntries = Array.from(fungsiMap.entries()).sort(([a], [b]) =>\r\n    a.localeCompare(b)\r\n  );\r\n  const kodeFungsiList = sortedEntries.map(([kdfungsi]) => kdfungsi);\r\n  const fungsiNamesLengkap = sortedEntries.map(([_, data]) => data.nmfungsi);\r\n  const paguData = sortedEntries.map(([_, data]) => data.pagu);\r\n  const realisasiData = sortedEntries.map(([_, data]) => data.realisasi);\r\n\r\n  const series = [\r\n    { name: \"Pagu\", data: paguData },\r\n    { name: \"Realisasi\", data: realisasiData },\r\n  ];\r\n\r\n  const colors = getThemeColors();\r\n\r\n  const options = {\r\n    chart: {\r\n      type: \"area\",\r\n      animations: { speed: 300 },\r\n      stacked: true,\r\n      toolbar: { show: false },\r\n      parentHeightOffset: 0,\r\n      foreColor: colors.foreColor,\r\n    },\r\n    dataLabels: {\r\n      enabled: true,\r\n      formatter: (value) => formatTrillions(value),\r\n    },\r\n    legend: {\r\n      position: \"top\",\r\n      horizontalAlign: \"center\",\r\n      fontSize: \"12px\",\r\n      fontWeight: 500,\r\n      labels: { colors: colors.textPrimary },\r\n      markers: { size: 8 },\r\n      itemMargin: { horizontal: 10, vertical: 5 },\r\n    },\r\n    xaxis: {\r\n      categories: kodeFungsiList,\r\n      labels: { style: { colors: colors.foreColor } },\r\n      axisBorder: { color: colors.borderColor },\r\n      axisTicks: { color: colors.borderColor },\r\n    },\r\n    yaxis: {\r\n      labels: {\r\n        style: { colors: colors.foreColor },\r\n        formatter: (value) => formatTrillions(value),\r\n      },\r\n    },\r\n    tooltip: {\r\n      theme: theme === \"dark\" ? \"dark\" : \"light\",\r\n      style: { fontSize: \"12px\" },\r\n      x: {\r\n        formatter: (_, { dataPointIndex }) =>\r\n          fungsiNamesLengkap[dataPointIndex],\r\n      },\r\n      y: {\r\n        formatter: (value) => formatTrillions(value),\r\n      },\r\n    },\r\n    colors: [colors.primary, colors.success],\r\n    grid: {\r\n      show: true,\r\n      borderColor: colors.gridColor,\r\n      strokeDashArray: 0,\r\n      position: \"back\",\r\n    },\r\n    stroke: {\r\n      curve: \"smooth\",\r\n    },\r\n    markers: { show: false },\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full h-full relative\">\r\n      <Chart\r\n        key={theme}\r\n        options={options}\r\n        series={series}\r\n        type=\"area\"\r\n        height=\"100%\"\r\n        width=\"100%\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AAkFQ;;AAhFR;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAWA;AACA;;;;AAnBA;;;;;;;AASA,MAAM,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACpB,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;;;;;;;KAJpB;;;AAYC,MAAM,SAAS;QAAC,EAAE,cAAc,EAAE,cAAc,EAAE;;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,kBAAkB,CAAC;QACvB,OACE,IAAI,KAAK,YAAY,CAAC,SAAS;YAC7B,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC,SAAS,qBAAqB;IAE5C;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAS,UAAU;QACzB,OAAO;YACL,WAAW,SAAS,YAAY;YAChC,aAAa,SAAS,YAAY;YAClC,WAAW,SAAS,YAAY;YAChC,WAAW,SAAS,YAAY;YAChC,SAAS;YACT,SAAS;YACT,aAAa,SAAS,YAAY;QACpC;IACF;IAEA,MAAM,UAAU;QACd,IAAI,eACF,kBAAkB,mBAAmB,OACjC,AAAC,oBAAkC,OAAf,gBAAe,OACnC;QACN,IAAI,eACF,kBAAkB,mBAAmB,QACjC,AAAC,kBAAgC,OAAf,gBAAe,OACjC;QAEN,MAAM,eAAe,mBAAmB,AAAC,oaAOA,OAAhB,cAAa,KAAgB,OAAb,cAAa;QAKtD,MAAM,eAAe,mBAAmB,cACrC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,QAAQ,KAChB,IAAI;QACP,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,SAAS,IAAI,yEAElC;gBAAE,OAAO;YAAe;YAE1B,qBAAqB,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACjD,EAAE,OAAO,KAAK;YACZ,UAAU,oDAAoD;QAChE,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;QAAgB;KAAe;IAEnC,IAAI,SAAS;QACX,qBACE,6LAAC,yMAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gBAAC,WAAU;0BACjB,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;wBAAY,WAAU;;0CACrB,6LAAC,qNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,qNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;uBAFZ;;;;;;;;;;;;;;;IAQpB;IAEA,MAAM,kBAAkB;QACtB,MAAM,SAAS,UAAU;QACzB,OAAO;YACL,QAAQ,SACJ,wDACA;QACN;IACF;IAEA,MAAM,UACJ,kBAAkB,MAAM,KAAK,KAC7B,OAAO,MAAM,CAAC,iBAAiB,CAAC,EAAE,EAC/B,KAAK,CAAC,GACN,KAAK,CAAC,CAAC,MAAQ,QAAQ,KAAK,QAAQ;IAEzC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,yMAAA,CAAA,OAAI;gBACH,WAAW,AAAC,yBAAiD,OAAzB,kBAAkB,MAAM,EAAC;0BAE7D,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6LAAC,2MAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC,yMAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,OAAM;4BACN,4BAAc,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAClC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,MAAM,YAAY,IAAI;IACtB,kBAAkB,OAAO,CAAC,CAAC;QACzB,MAAM,MAAM,KAAK,QAAQ;QACzB,MAAM,WAAW,UAAU,GAAG,CAAC;QAC/B,IAAI,UAAU;YACZ,SAAS,IAAI,IAAI,KAAK,IAAI,IAAI;YAC9B,SAAS,SAAS,IAAI,KAAK,SAAS,IAAI;QAC1C,OAAO;YACL,UAAU,GAAG,CAAC,KAAK;gBACjB,UAAU,KAAK,QAAQ,IAAI,AAAC,UAAa,OAAJ;gBACrC,MAAM,KAAK,IAAI,IAAI;gBACnB,WAAW,KAAK,SAAS,IAAI;YAC/B;QACF;IACF;IAEA,MAAM,gBAAgB,MAAM,IAAI,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC;YAAC,CAAC,EAAE,UAAE,CAAC,EAAE;eAClE,EAAE,aAAa,CAAC;;IAElB,MAAM,iBAAiB,cAAc,GAAG,CAAC;YAAC,CAAC,SAAS;eAAK;;IACzD,MAAM,qBAAqB,cAAc,GAAG,CAAC;YAAC,CAAC,GAAG,KAAK;eAAK,KAAK,QAAQ;;IACzE,MAAM,WAAW,cAAc,GAAG,CAAC;YAAC,CAAC,GAAG,KAAK;eAAK,KAAK,IAAI;;IAC3D,MAAM,gBAAgB,cAAc,GAAG,CAAC;YAAC,CAAC,GAAG,KAAK;eAAK,KAAK,SAAS;;IAErE,MAAM,SAAS;QACb;YAAE,MAAM;YAAQ,MAAM;QAAS;QAC/B;YAAE,MAAM;YAAa,MAAM;QAAc;KAC1C;IAED,MAAM,SAAS;IAEf,MAAM,UAAU;QACd,OAAO;YACL,MAAM;YACN,YAAY;gBAAE,OAAO;YAAI;YACzB,SAAS;YACT,SAAS;gBAAE,MAAM;YAAM;YACvB,oBAAoB;YACpB,WAAW,OAAO,SAAS;QAC7B;QACA,YAAY;YACV,SAAS;YACT,WAAW,CAAC,QAAU,gBAAgB;QACxC;QACA,QAAQ;YACN,UAAU;YACV,iBAAiB;YACjB,UAAU;YACV,YAAY;YACZ,QAAQ;gBAAE,QAAQ,OAAO,WAAW;YAAC;YACrC,SAAS;gBAAE,MAAM;YAAE;YACnB,YAAY;gBAAE,YAAY;gBAAI,UAAU;YAAE;QAC5C;QACA,OAAO;YACL,YAAY;YACZ,QAAQ;gBAAE,OAAO;oBAAE,QAAQ,OAAO,SAAS;gBAAC;YAAE;YAC9C,YAAY;gBAAE,OAAO,OAAO,WAAW;YAAC;YACxC,WAAW;gBAAE,OAAO,OAAO,WAAW;YAAC;QACzC;QACA,OAAO;YACL,QAAQ;gBACN,OAAO;oBAAE,QAAQ,OAAO,SAAS;gBAAC;gBAClC,WAAW,CAAC,QAAU,gBAAgB;YACxC;QACF;QACA,SAAS;YACP,OAAO,UAAU,SAAS,SAAS;YACnC,OAAO;gBAAE,UAAU;YAAO;YAC1B,GAAG;gBACD,WAAW,CAAC;wBAAG,EAAE,cAAc,EAAE;2BAC/B,kBAAkB,CAAC,eAAe;;YACtC;YACA,GAAG;gBACD,WAAW,CAAC,QAAU,gBAAgB;YACxC;QACF;QACA,QAAQ;YAAC,OAAO,OAAO;YAAE,OAAO,OAAO;SAAC;QACxC,MAAM;YACJ,MAAM;YACN,aAAa,OAAO,SAAS;YAC7B,iBAAiB;YACjB,UAAU;QACZ;QACA,QAAQ;YACN,OAAO;QACT;QACA,SAAS;YAAE,MAAM;QAAM;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAEC,SAAS;YACT,QAAQ;YACR,MAAK;YACL,QAAO;YACP,OAAM;WALD;;;;;;;;;;AASb;GAtOa;;QAGO,mJAAA,CAAA,WAAQ;QACJ,uJAAA,CAAA,WAAQ;;;MAJnB", "debugId": null}}, {"offset": {"line": 2110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/%28dashboard%29/dashboard/loading.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n  Skel<PERSON>,\r\n} from \"@heroui/react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useState } from \"react\";\r\n\r\nimport { BelanjaTerbesar } from \"@/components/features/dashboard/dataDipa/BelanjaTerbesar\";\r\nimport { GetDipa } from \"@/components/features/dashboard/dataDipa/getDipa\";\r\nimport PerformaTerbesar from \"@/components/features/dashboard/dataDipa/PerformaTerbesar\";\r\nimport { Kanwil } from \"@/components/features/reference/Kanwil\";\r\nimport { Kddept } from \"@/components/features/reference/Kddept\";\r\nimport Thang from \"@/components/features/reference/Thang\";\r\nimport { Fungsi } from \"@/components/ui/charts/fungsi\";\r\nimport { AlertTriangle, BarChart3, CheckCircle2, Clock } from \"lucide-react\";\r\nimport dynamic from \"next/dynamic\";\r\nimport NextLink from \"next/link\";\r\n\r\nconst DukmanTeknis = dynamic(\r\n  () =>\r\n    import(\"@/components/ui/charts/dukman<PERSON><PERSON>nis\").then((module) => ({\r\n      default: module.Dukman<PERSON>eknis,\r\n    })),\r\n  {\r\n    ssr: false,\r\n    loading: () => <Skeleton className=\"w-full h-64\" />,\r\n  }\r\n);\r\n\r\nconst realizationProgramData = [\r\n  { name: \"Infrastruktur\", pagu: 450, realisasi: 390 },\r\n  { name: \"Pendidikan\", pagu: 380, realisasi: 342 },\r\n  { name: \"Kesehatan\", pagu: 320, realisasi: 294 },\r\n  { name: \"Sosial\", pagu: 280, realisasi: 268 },\r\n  { name: \"Pertahanan\", pagu: 250, realisasi: 218 },\r\n  { name: \"Ekonomi\", pagu: 220, realisasi: 210 },\r\n];\r\n\r\nconst recentActivities = [\r\n  {\r\n    title: \"Pencairan Anggaran Pendidikan\",\r\n    ministry: \"Kemendikbud\",\r\n    amount: \"Rp 125.4 M\",\r\n    time: \"2 jam lalu\",\r\n    status: \"completed\",\r\n  },\r\n  {\r\n    title: \"Revisi DIPA Kesehatan\",\r\n    ministry: \"Kemenkes\",\r\n    amount: \"Rp 89.2 M\",\r\n    time: \"4 jam lalu\",\r\n    status: \"pending\",\r\n  },\r\n  {\r\n    title: \"Penyesuaian Alokasi PUPR\",\r\n    ministry: \"Kemen PUPR\",\r\n    amount: \"Rp 67.8 M\",\r\n    time: \"6 jam lalu\",\r\n    status: \"in-review\",\r\n  },\r\n];\r\n\r\nexport default function DashboardLoading() {\r\n  const { theme } = useTheme();\r\n  const [selectedKanwil, setSelectedKanwil] = useState(\"00\");\r\n  const [selectedKddept, setSelectedKddept] = useState(\"000\");\r\n\r\n  // Handler untuk menerima perubahan nilai kanwil dari komponen Kanwil\r\n  const handleKanwilChange = (kanwilValue) => {\r\n    setSelectedKanwil(kanwilValue);\r\n  };\r\n\r\n  // Handler untuk menerima perubahan nilai kddept dari komponen Kddept\r\n  const handleKddeptChange = (kddeptValue) => {\r\n    setSelectedKddept(kddeptValue);\r\n  };\r\n\r\n  // Theme-aware class function\r\n  const getCardClasses = () => {\r\n    const isDark = theme === \"dark\";\r\n    return isDark\r\n      ? \"bg-gradient-to-br from-slate-800 to-slate-700\"\r\n      : \"bg-gradient-to-br from-slate-100 to-slate-200\";\r\n  };\r\n\r\n  const cardClasses = getCardClasses();\r\n\r\n  const TrenApbn = dynamic(() => import(\"@/components/ui/charts/trenApbn\"), {\r\n    ssr: false,\r\n    loading: () => <Skeleton className=\"w-full h-64\" />,\r\n  });\r\n\r\n  return (\r\n    <div className=\"h-full lg:px-6 pt-4\">\r\n      {/* Header Section Skeleton */}\r\n      <div className=\"flex flex-col gap-2 pt-2 px-4 lg:px-0 max-w-[90rem] mx-auto w-full\">\r\n        <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4\">\r\n          <div>\r\n            {\" \"}\r\n            <h1 className=\"text-xl md:text-2xl font-medium text-foreground tracking-wide\">\r\n              Dashboard Realisasi APBN\r\n            </h1>\r\n          </div>\r\n          <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3\">\r\n            <Thang />\r\n            <Kddept\r\n              onKddeptChange={handleKddeptChange}\r\n              selectedKddept={selectedKddept}\r\n            />\r\n            <Kanwil\r\n              onKanwilChange={handleKanwilChange}\r\n              selectedKanwil={selectedKanwil}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quick Stats Cards Skeleton */}\r\n        <GetDipa\r\n          selectedKanwil={selectedKanwil}\r\n          selectedKddept={selectedKddept}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content Grid Skeleton */}\r\n      <div className=\"flex flex-col gap-6 pt-6 px-4 lg:px-0 max-w-[90rem] mx-auto w-full\">\r\n        {/* Three Card Row Skeleton */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-6 xl:grid-cols-12 gap-6\">\r\n          {/* Overall Summary Skeleton */}\r\n          <BelanjaTerbesar\r\n            selectedKanwil={selectedKanwil}\r\n            selectedKddept={selectedKddept}\r\n          />\r\n\r\n          {/* Ministry Performance Skeleton */}\r\n          <PerformaTerbesar\r\n            selectedKanwil={selectedKanwil}\r\n            selectedKddept={selectedKddept}\r\n          />\r\n\r\n          {/* Chart Skeleton */}\r\n          {/* <Card className=\"border-none shadow-sm bg-gradient-to-br from-default-50 to-default-100 lg:col-span-12 xl:col-span-6\">\r\n            <CardHeader className=\"pb-2 px-4 md:px-6\">\r\n              <div className=\"flex flex-col gap-2 w-full\">\r\n                <Skeleton className=\"h-5 md:h-6 w-48 rounded\" />\r\n                <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2\">\r\n                  <Skeleton className=\"h-3 md:h-4 w-40 rounded\" />\r\n                  <Skeleton className=\"h-6 w-20 rounded-full\" />\r\n                </div>\r\n              </div>\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 px-2 pb-2\">\r\n              <div className=\"h-[200px] md:h-[280px] w-full flex items-center justify-center\">\r\n                <Skeleton className=\"h-full w-full rounded-lg\" />\r\n              </div>\r\n            </CardBody>\r\n          </Card> */}\r\n          <Card\r\n            className={`border-none shadow-sm ${cardClasses} sm:col-span-2 lg:col-span-12 xl:col-span-6`}\r\n          >\r\n            <CardHeader className=\"pb-1 px-4 md:px-6\">\r\n              <div className=\"flex flex-col gap-1\">\r\n                <h3 className=\"text-sm md:text-base font-semibold\">\r\n                  Tren Realisasi APBN\r\n                </h3>\r\n              </div>\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 px-2 md:px-4 pb-1\">\r\n              <div className=\"h-[150px] md:h-[200px] w-full flex flex-col overflow-hidden\">\r\n                <TrenApbn\r\n                  selectedKanwil={selectedKanwil}\r\n                  selectedKddept={selectedKddept}\r\n                />\r\n              </div>\r\n            </CardBody>\r\n          </Card>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-3\">\r\n          {/* Realisasi Fungsi Chart */}\r\n          <Card\r\n            className={`border-none shadow-sm ${cardClasses} lg:col-span-2`}\r\n          >\r\n            <CardHeader className=\"pb-2 px-4 md:px-6\">\r\n              <div className=\"flex items-center justify-between w-full\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <BarChart3 className=\"h-3 w-3 text-default-500\" />\r\n                  <h3 className=\"text-sm md:text-base font-semibold\">\r\n                    Realisasi Fungsi\r\n                  </h3>\r\n                </div>\r\n                <Chip\r\n                  color=\"primary\"\r\n                  variant=\"flat\"\r\n                  size=\"sm\"\r\n                  className=\"w-fit\"\r\n                >\r\n                  Per Sektor\r\n                </Chip>\r\n              </div>\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 px-2 md:px-4 pb-1\">\r\n              <div className=\"h-[200px] md:h-[250px] w-full flex flex-col overflow-hidden\">\r\n                <Fungsi\r\n                  selectedKanwil={selectedKanwil}\r\n                  selectedKddept={selectedKddept}\r\n                />\r\n              </div>\r\n            </CardBody>\r\n          </Card>\r\n\r\n          {/* Pagu Dukman vs Teknis Chart */}\r\n          <Card className={`border-none shadow-sm ${cardClasses}`}>\r\n            <CardHeader className=\"pb-2 px-4 md:px-6\">\r\n              <div className=\"flex items-center justify-between w-full\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <BarChart3 className=\"h-3 w-3 text-default-500\" />\r\n                  <h3 className=\"text-sm md:text-base font-semibold\">\r\n                    Pagu Dukman vs Teknis\r\n                  </h3>\r\n                </div>\r\n                <Chip\r\n                  color=\"primary\"\r\n                  variant=\"flat\"\r\n                  size=\"sm\"\r\n                  className=\"w-fit\"\r\n                >\r\n                  2024\r\n                </Chip>\r\n              </div>\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 px-2 md:px-4 pb-1\">\r\n              <div className=\"h-[200px] md:h-[250px] w-full flex flex-col overflow-hidden\">\r\n                <DukmanTeknis\r\n                  selectedKanwil={selectedKanwil}\r\n                  selectedKddept={selectedKddept}\r\n                />\r\n              </div>\r\n            </CardBody>\r\n          </Card>\r\n          <Card className={`border-none shadow-sm ${cardClasses}`}>\r\n            <CardHeader className=\"pb-2 px-4 md:px-6\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Clock className=\"h-3 w-3 text-default-500\" />\r\n                <h3 className=\"text-sm md:text-base font-semibold\">\r\n                  Aktivitas Terkini\r\n                </h3>\r\n              </div>\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 px-4 md:px-6\">\r\n              <div className=\"space-y-2\">\r\n                {recentActivities.map((activity, index) => (\r\n                  <div key={index} className=\"flex gap-2\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      {activity.status === \"completed\" ? (\r\n                        <CheckCircle2 className=\"h-3 w-3 text-success\" />\r\n                      ) : activity.status === \"pending\" ? (\r\n                        <Clock className=\"h-3 w-3 text-warning\" />\r\n                      ) : (\r\n                        <AlertTriangle className=\"h-3 w-3 text-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <p className=\"text-xs font-medium truncate\">\r\n                        {activity.title}\r\n                      </p>\r\n                      <p className=\"text-xs text-default-500\">\r\n                        {activity.ministry}\r\n                      </p>\r\n                      <div className=\"flex justify-between items-center mt-0.5\">\r\n                        <span className=\"text-xs font-medium text-primary\">\r\n                          {activity.amount}\r\n                        </span>\r\n                        <span className=\"text-xs text-default-400\">\r\n                          {activity.time}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <Divider className=\"my-2\" />\r\n              <Button\r\n                variant=\"flat\"\r\n                color=\"primary\"\r\n                size=\"sm\"\r\n                className=\"w-full\"\r\n                as={NextLink}\r\n                href=\"/mbg/kertas-kerja\"\r\n              >\r\n                Lihat Semua Aktivitas\r\n              </Button>\r\n            </CardBody>\r\n          </Card>\r\n        </div>\r\n        {/* Bottom Row Skeleton */}\r\n        {/* <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n          <Card className=\"border-none shadow-sm bg-gradient-to-br from-default-50 to-default-100\">\r\n            <CardHeader className=\"pb-3 md:pb-4 px-4 md:px-6\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Skeleton className=\"h-4 w-4 rounded\" />\r\n                <Skeleton className=\"h-5 md:h-6 w-32 rounded\" />\r\n              </div>\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 px-4 md:px-6\">\r\n              <div className=\"space-y-3 md:space-y-4\">\r\n                {Array.from({ length: 3 }).map((_, index) => (\r\n                  <div key={index} className=\"flex gap-3\">\r\n                    <Skeleton className=\"h-4 w-4 rounded flex-shrink-0 mt-1\" />\r\n                    <div className=\"flex-1 min-w-0 space-y-2\">\r\n                      <Skeleton className=\"h-4 w-48 rounded\" />\r\n                      <Skeleton className=\"h-3 w-24 rounded\" />\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <Skeleton className=\"h-3 w-20 rounded\" />\r\n                        <Skeleton className=\"h-3 w-16 rounded\" />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"my-4\">\r\n                <Skeleton className=\"h-px w-full rounded\" />\r\n              </div>\r\n              <Skeleton className=\"h-8 w-full rounded-lg\" />\r\n            </CardBody>\r\n          </Card>\r\n\r\n          <Card className=\"border-none shadow-sm bg-gradient-to-br from-default-50 to-default-100\">\r\n            <CardHeader className=\"pb-3 md:pb-4 px-4 md:px-6\">\r\n              <Skeleton className=\"h-5 md:h-6 w-24 rounded\" />\r\n            </CardHeader>\r\n            <CardBody className=\"pt-0 space-y-3 px-4 md:px-6\">\r\n              {Array.from({ length: 3 }).map((_, index) => (\r\n                <Skeleton key={index} className=\"h-8 w-full rounded-lg\" />\r\n              ))}\r\n            </CardBody>\r\n          </Card>\r\n        </div> */}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAvBA;;;;;;;;;;;;;;AAyBA,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACzB,IACE,oKAA8C,IAAI,CAAC,CAAC,SAAW,CAAC;YAC9D,SAAS,OAAO,YAAY;QAC9B,CAAC;;;;;;IAED,KAAK;IACL,SAAS,kBAAM,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;;KAPjC;AAWN,MAAM,yBAAyB;IAC7B;QAAE,MAAM;QAAiB,MAAM;QAAK,WAAW;IAAI;IACnD;QAAE,MAAM;QAAc,MAAM;QAAK,WAAW;IAAI;IAChD;QAAE,MAAM;QAAa,MAAM;QAAK,WAAW;IAAI;IAC/C;QAAE,MAAM;QAAU,MAAM;QAAK,WAAW;IAAI;IAC5C;QAAE,MAAM;QAAc,MAAM;QAAK,WAAW;IAAI;IAChD;QAAE,MAAM;QAAW,MAAM;QAAK,WAAW;IAAI;CAC9C;AAED,MAAM,mBAAmB;IACvB;QACE,OAAO;QACP,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qEAAqE;IACrE,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;IACpB;IAEA,qEAAqE;IACrE,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;IACpB;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB;QACrB,MAAM,SAAS,UAAU;QACzB,OAAO,SACH,kDACA;IACN;IAEA,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;QACvB,KAAK;QACL,SAAS,kBAAM,6LAAC,qNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;;IAGrC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCACE;kDACD,6LAAC;wCAAG,WAAU;kDAAgE;;;;;;;;;;;;0CAIhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uJAAA,CAAA,UAAK;;;;;kDACN,6LAAC,wJAAA,CAAA,SAAM;wCACL,gBAAgB;wCAChB,gBAAgB;;;;;;kDAElB,6LAAC,wJAAA,CAAA,SAAM;wCACL,gBAAgB;wCAChB,gBAAgB;;;;;;;;;;;;;;;;;;kCAMtB,6LAAC,qKAAA,CAAA,UAAO;wBACN,gBAAgB;wBAChB,gBAAgB;;;;;;;;;;;;0BAKpB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6KAAA,CAAA,kBAAe;gCACd,gBAAgB;gCAChB,gBAAgB;;;;;;0CAIlB,6LAAC,8KAAA,CAAA,UAAgB;gCACf,gBAAgB;gCAChB,gBAAgB;;;;;;0CAoBlB,6LAAC,yMAAA,CAAA,OAAI;gCACH,WAAW,AAAC,yBAAoC,OAAZ,aAAY;;kDAEhD,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;;;;;;;;;;;kDAKvD,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;kDAClB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,gBAAgB;gDAChB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,yMAAA,CAAA,OAAI;gCACH,WAAW,AAAC,yBAAoC,OAAZ,aAAY;;kDAEhD,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;;;;;;;8DAIrD,6LAAC,yMAAA,CAAA,OAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;kDAKL,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;kDAClB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+IAAA,CAAA,SAAM;gDACL,gBAAgB;gDAChB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;0CAOxB,6LAAC,yMAAA,CAAA,OAAI;gCAAC,WAAW,AAAC,yBAAoC,OAAZ;;kDACxC,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;;;;;;;8DAIrD,6LAAC,yMAAA,CAAA,OAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;kDAKL,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;kDAClB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,gBAAgB;gDAChB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;0CAKxB,6LAAC,yMAAA,CAAA,OAAI;gCAAC,WAAW,AAAC,yBAAoC,OAAZ;;kDACxC,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;;;;;;;;;;;;kDAKvD,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAU;0EACZ,SAAS,MAAM,KAAK,4BACnB,6LAAC,wNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;2EACtB,SAAS,MAAM,KAAK,0BACtB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;yFAEjB,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAG7B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFACV,SAAS,KAAK;;;;;;kFAEjB,6LAAC;wEAAE,WAAU;kFACV,SAAS,QAAQ;;;;;;kFAEpB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,SAAS,MAAM;;;;;;0FAElB,6LAAC;gFAAK,WAAU;0FACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;;uDAtBZ;;;;;;;;;;0DA6Bd,6LAAC,kNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC,+MAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAU;gDACV,IAAI,+JAAA,CAAA,UAAQ;gDACZ,MAAK;0DACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDf;GAtRwB;;QACJ,mJAAA,CAAA,WAAQ;;;MADJ", "debugId": null}}]}