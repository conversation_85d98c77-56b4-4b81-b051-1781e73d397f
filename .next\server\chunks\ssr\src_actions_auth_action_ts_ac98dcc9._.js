module.exports = {

"[project]/src/actions/auth.action.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_4b410d59._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/actions/auth.action.ts [app-ssr] (ecmascript)");
    });
});
}),

};