(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_47df725f._.js",
  "static/chunks/node_modules_@heroui_dom-animation_dist_index_mjs_9faac202._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/ui/charts/client-chart.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_8738571e._.js",
  "static/chunks/src_components_ui_charts_client-chart_tsx_41f658ca._.js",
  "static/chunks/src_components_ui_charts_client-chart_tsx_45f51aeb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/charts/client-chart.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/src/components/ui/charts/dukmanTeknis.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_ui_charts_client-chart_tsx_c77f67cf._.js",
  "static/chunks/src_components_ui_c9340e0d._.js",
  "static/chunks/src_components_ui_charts_dukmanTeknis_jsx_45f51aeb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/charts/dukmanTeknis.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
"[project]/src/components/ui/charts/trenApbn.jsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-apexcharts_dist_react-apexcharts_min_c611432a.js",
  "static/chunks/src_components_ui_bc309193._.js",
  "static/chunks/src_components_ui_charts_trenApbn_jsx_45f51aeb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/charts/trenApbn.jsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);