(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_47df725f._.js",
  "static/chunks/node_modules_@heroui_dom-animation_dist_index_mjs_080b651c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/xlsx/xlsx.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_xlsx_xlsx_mjs_ad755052._.js",
  "static/chunks/node_modules_xlsx_xlsx_mjs_080b651c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/xlsx/xlsx.mjs [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_bf7a6005._.js",
  "static/chunks/node_modules_9beba542._.js",
  "static/chunks/node_modules_jspdf_dist_jspdf_es_min_080b651c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_jspdf-autotable_dist_jspdf_plugin_autotable_mjs_2871a214._.js",
  "static/chunks/node_modules_jspdf-autotable_dist_jspdf_plugin_autotable_mjs_080b651c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-client] (ecmascript)");
    });
});
}),
}]);