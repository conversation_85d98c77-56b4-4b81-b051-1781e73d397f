module.exports = {

"[project]/src/components/features/inquiry/shared/utils/exportUtils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Utility for robust CSV export
__turbopack_context__.s({
    "exportToCSV": ()=>exportToCSV,
    "exportToExcel": ()=>exportToExcel,
    "exportToJSON": ()=>exportToJSON,
    "exportToPDF": ()=>exportToPDF,
    "exportToText": ()=>exportToText
});
function exportToCSV(data, filename = "data.csv") {
    if (!data || !data.length) return;
    const replacer = (key, value)=>value === null || value === undefined ? "" : value;
    const header = Object.keys(data[0]);
    const csv = [
        header.join(","),
        ...data.map((row)=>header.map((fieldName)=>JSON.stringify(row[fieldName], replacer)).join(","))
    ].join("\r\n");
    const blob = new Blob([
        csv
    ], {
        type: "text/csv"
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}
async function exportToExcel(data, filename = "data.xlsx") {
    if (!data || !data.length) return;
    const xlsx = await __turbopack_context__.r("[project]/node_modules/xlsx/xlsx.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
    const ws = xlsx.utils.json_to_sheet(data);
    const wb = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, ws, "Sheet1");
    const wbout = xlsx.write(wb, {
        bookType: "xlsx",
        type: "array"
    });
    const blob = new Blob([
        wbout
    ], {
        type: "application/octet-stream"
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}
function exportToJSON(data, filename = "data.json") {
    if (!data || !data.length) return;
    const jsonContent = JSON.stringify(data, null, 2);
    const blob = new Blob([
        jsonContent
    ], {
        type: "application/json"
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}
function exportToText(data, filename = "data.txt") {
    if (!data || !data.length) return;
    const textContent = JSON.stringify(data, null, 2);
    const blob = new Blob([
        textContent
    ], {
        type: "text/plain"
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}
async function exportToPDF(data, filename = "data.pdf") {
    if (!data || !data.length) return;
    const jsPDF = (await __turbopack_context__.r("[project]/node_modules/jspdf/dist/jspdf.es.min.js [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    const autoTable = (await __turbopack_context__.r("[project]/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    const doc = new jsPDF();
    const header = Object.keys(data[0]);
    const rows = data.map((row)=>header.map((field)=>row[field]));
    autoTable(doc, {
        head: [
            header
        ],
        body: rows,
        styles: {
            fontSize: 8
        },
        headStyles: {
            fillColor: [
                41,
                128,
                185
            ]
        }
    });
    doc.save(filename);
}
}),
"[project]/src/components/features/inquiry/shared/utils/filterUtils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getFilteredKegiatan": ()=>getFilteredKegiatan,
    "getFilteredOutputs": ()=>getFilteredOutputs,
    "getFilteredPrograms": ()=>getFilteredPrograms
});
var __TURBOPACK__imported__module__$5b$project$5d2f$data$2f$reference$2f$functional$2f$Kdoutput$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/data/reference/functional/Kdoutput.json (json)");
;
/**
 * Get filtered data based on parent selections
 * This function provides hierarchical filtering for program -> kegiatan -> output -> suboutput -> komponen -> subkomponen
 */ // Utility function to get unique values from array of objects
const getUniqueValues = (data, key, nameKey)=>{
    const unique = new Map();
    data.forEach((item)=>{
        if (item[key] && !unique.has(item[key])) {
            unique.set(item[key], {
                value: item[key],
                label: item[nameKey] || item[key]
            });
        }
    });
    return Array.from(unique.values());
};
const getFilteredPrograms = (dept, kdunit)=>{
    let filteredData = __TURBOPACK__imported__module__$5b$project$5d2f$data$2f$reference$2f$functional$2f$Kdoutput$2e$json__$28$json$29$__["default"];
    if (dept && dept !== "XX") {
        filteredData = filteredData.filter((item)=>item.kddept === dept);
    }
    if (kdunit && kdunit !== "XX") {
        filteredData = filteredData.filter((item)=>item.kdunit === kdunit);
    }
    return getUniqueValues(filteredData, "kdprogram", "nmprogram");
};
const getFilteredKegiatan = (dept, kdunit, program)=>{
    let filteredData = __TURBOPACK__imported__module__$5b$project$5d2f$data$2f$reference$2f$functional$2f$Kdoutput$2e$json__$28$json$29$__["default"];
    if (dept && dept !== "XX") {
        filteredData = filteredData.filter((item)=>item.kddept === dept);
    }
    if (kdunit && kdunit !== "XX") {
        filteredData = filteredData.filter((item)=>item.kdunit === kdunit);
    }
    if (program && program !== "XX") {
        filteredData = filteredData.filter((item)=>item.kdprogram === program);
    }
    return getUniqueValues(filteredData, "kdgiat", "nmgiat");
};
const getFilteredOutputs = (dept, kdunit, program, kegiatan)=>{
    let filteredData = __TURBOPACK__imported__module__$5b$project$5d2f$data$2f$reference$2f$functional$2f$Kdoutput$2e$json__$28$json$29$__["default"];
    if (dept && dept !== "XX") {
        filteredData = filteredData.filter((item)=>item.kddept === dept);
    }
    if (kdunit && kdunit !== "XX") {
        filteredData = filteredData.filter((item)=>item.kdunit === kdunit);
    }
    if (program && program !== "XX") {
        filteredData = filteredData.filter((item)=>item.kdprogram === program);
    }
    if (kegiatan && kegiatan !== "XX") {
        filteredData = filteredData.filter((item)=>item.kdgiat === kegiatan);
    }
    return getUniqueValues(filteredData, "kdoutput", "nmoutput");
}; // Note: Since there's no child data for suboutput, komponen, and subkomponen,
 // these filters will use the existing component logic without parent-child filtering
}),
"[project]/src/components/features/inquiry/shared/hooks/useInquiryState.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>useInquiryState
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$data$2f$Context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/data/Context.tsx [app-ssr] (ecmascript)");
"use client";
;
;
function useInquiryState() {
    const { role, telp, verified, loadingExcell, setloadingExcell, kdkppn: kodekppn, kdkanwil: kodekanwil, settampilAI } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$data$2f$Context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
    // Modal states
    const [showModal, setShowModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalKedua, setShowModalKedua] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalsql, setShowModalsql] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalApbn, setShowModalApbn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalAkumulasi, setShowModalAkumulasi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalBulanan, setShowModalBulanan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalBlokir, setShowModalBlokir] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalPN, setShowModalPN] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalPN2, setShowModalPN2] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalJnsblokir, setShowModalJnsblokir] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalPDF, setShowModalPDF] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showModalsimpan, setShowModalsimpan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Form states
    const [jenlap, setJenlap] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("2");
    const [thang, setThang] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(new Date().getFullYear().toString());
    const [tanggal, setTanggal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [cutoff, setCutoff] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("0");
    const [pembulatan, setPembulatan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [akumulatif, setAkumulatif] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("0");
    const [selectedFormat, setSelectedFormat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("pdf");
    const [export2, setExport2] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loadingStatus, setLoadingStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showFormatDropdown, setShowFormatDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Filter visibility states
    const [kddept, setKddept] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [unit, setUnit] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kddekon, setKddekon] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdlokasi, setKdlokasi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdkabkota, setKdkabkota] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdkanwil, setKdkanwil] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdkppn, setKdkppn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdsatker, setKdsatker] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdfungsi, setKdfungsi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdsfungsi, setKdsfungsi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdprogram, setKdprogram] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdgiat, setKdgiat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdoutput, setKdoutput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdsoutput, setKdsoutput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdkomponen, setKdkomponen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdskomponen, setKdskomponen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdakun, setKdakun] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdsdana, setKdsdana] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdregister, setKdregister] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Special filter switches
    const [kdInflasi, setKdInflasi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdIkn, setKdIkn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [kdKemiskinan, setKdKemiskinan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdPRI, setKdPRI] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdPangan, setKdPangan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdStunting, setKdStunting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdPemilu, setKdPemilu] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdTema, setKdTema] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdPN, setKdPN] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdPP, setKdPP] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdKegPP, setKdKegPP] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [KdMP, setKdMP] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Filter values
    const [dept, setDept] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("000");
    const [deptkondisi, setDeptkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Kementerian filter
    const [katadept, setKatadept] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Kementerian filter
    const [kdunit, setKdunit] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [unitkondisi, setUnitkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Unit filter
    const [kataunit, setKataunit] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Unit filter
    const [dekon, setDekon] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [dekonkondisi, setDekonkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Dekon filter
    const [katadekon, setKatadekon] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Dekon filter
    const [prov, setProv] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [lokasikondisi, setLokasikondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Lokasi filter
    const [katalokasi, setKatalokasi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Lokasi filter
    const [kabkota, setKabkota] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [kabkotakondisi, setKabkotakondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Kabkota filter
    const [katakabkota, setKatakabkota] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Kabkota filter
    const [kanwil, setKanwil] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [kanwilkondisi, setKanwilkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Kanwil filter
    const [katakanwil, setKatakanwil] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Kanwil filter
    const [kppn, setKppn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [kppnkondisi, setKppnkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced KPPN filter
    const [katakppn, setKatakppn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced KPPN filter
    const [satker, setSatker] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [satkerkondisi, setSatkerkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Satker filter
    const [katasatker, setKatasatker] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Satker filter
    const [fungsi, setFungsi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [fungsikondisi, setFungsikondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Fungsi filter
    const [katafungsi, setKatafungsi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Fungsi filter
    const [sfungsi, setSfungsi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [subfungsikondisi, setSubfungsikondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Subfungsi filter
    const [katasubfungsi, setKatasubfungsi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Subfungsi filter
    const [program, setProgram] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [programkondisi, setProgramkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Program filter
    const [kataprogram, setKataprogram] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Program filter
    const [giat, setGiat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [giatkondisi, setGiatkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Giat filter
    const [katagiat, setKatagiat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Giat filter
    const [output, setOutput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [outputkondisi, setOutputkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Output filter
    const [kataoutput, setKataoutput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Output filter
    const [soutput, setsOutput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [soutputkondisi, setSoutputkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Soutput filter
    const [katasoutput, setKatasoutput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Soutput filter
    const [komponen, setKomponen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [komponenkondisi, setKomponenkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Komponen filter
    const [katakomponen, setKatakomponen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Komponen filter
    const [skomponen, setSkomponen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [skomponenkondisi, setSkomponenkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Subkomponen filter
    const [kataskomponen, setKataskomponen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Subkomponen filter
    const [akun, setAkun] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("AKUN");
    const [akunkondisi, setAkunkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Akun filter
    const [kataakun, setKataakun] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Akun filter
    const [sdana, setSdana] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [sdanakondisi, setSdanakondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Sdana filter
    const [katasdana, setKatasdana] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Sdana filter
    const [register, setRegister] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [registerkondisi, setRegisterkondisi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Register filter
    const [kataregister, setKataregister] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""); // NEW: for advanced Register filter
    const [PN, setPN] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [PP, setPP] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [PRI, setPRI] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [MP, setMP] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Tema, setTema] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Inflasi, setInflasi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Stunting, setStunting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Miskin, setMiskin] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Pemilu, setPemilu] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Ikn, setIkn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [Pangan, setPangan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    // Radio states
    const [deptradio, setDeptradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [unitradio, setUnitradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [dekonradio, setDekonradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [locradio, setLocradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1"); // Changed from provradio to locradio
    const [kabkotaradio, setKabkotaradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [kanwilradio, setKanwilradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [kppnradio, setKppnradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [satkerradio, setSatkerradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [fungsiradio, setFungsiradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [subfungsiradio, setSubfungsiradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [programradio, setProgramradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [kegiatanradio, setKegiatanradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [outputradio, setOutputradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [soutputradio, setsOutputradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [komponenradio, setKomponenradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [skomponenradio, setSkomponenradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [akunradio, setAkunradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [sdanaradio, setSdanaradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [registerradio, setRegisterradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [inflasiradio, setInflasiradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [iknradio, setIknradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [kemiskinanradio, setKemiskinanradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [priradio, setPriradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [panganradio, setPanganradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [stuntingradio, setStuntingradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [pemiluradio, setPemiluradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [pnradio, setPnradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [ppradio, setPpradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [mpradio, setMpradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    const [temaradio, setTemaradio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    // Special filter option states
    const [opsidept, setOpsidept] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("pilihdept");
    const [opsiInflasi, setOpsiInflasi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("pilihInflasi");
    const [opsiIkn, setOpsiIkn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("pilihikn");
    const [opsiKemiskinan, setOpsiKemiskinan] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("pilihKemiskinan");
    // SQL state
    const [sql, setSql] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [from, setFrom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [select, setSelect] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR");
    // Kegiatan Prioritas filter value and radio
    const [kegiatanprioritas, setKegiatanPrioritas] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("XX");
    const [kegiatanprioritasradio, setKegiatanPrioritasRadio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("1");
    return {
        // Context values
        role,
        telp,
        verified,
        loadingExcell,
        setloadingExcell,
        kodekppn,
        kodekanwil,
        settampilAI,
        // Modal states
        showModal,
        setShowModal,
        showModalKedua,
        setShowModalKedua,
        showModalsql,
        setShowModalsql,
        showModalApbn,
        setShowModalApbn,
        showModalAkumulasi,
        setShowModalAkumulasi,
        showModalBulanan,
        setShowModalBulanan,
        showModalBlokir,
        setShowModalBlokir,
        showModalPN,
        setShowModalPN,
        showModalPN2,
        setShowModalPN2,
        showModalJnsblokir,
        setShowModalJnsblokir,
        showModalPDF,
        setShowModalPDF,
        showModalsimpan,
        setShowModalsimpan,
        // Form states
        jenlap,
        setJenlap,
        thang,
        setThang,
        tanggal,
        setTanggal,
        cutoff,
        setCutoff,
        pembulatan,
        setPembulatan,
        akumulatif,
        setAkumulatif,
        selectedFormat,
        setSelectedFormat,
        export2,
        setExport2,
        loadingStatus,
        setLoadingStatus,
        showFormatDropdown,
        setShowFormatDropdown,
        // Filter visibility states
        kddept,
        setKddept,
        unit,
        setUnit,
        kddekon,
        setKddekon,
        kdlokasi,
        setKdlokasi,
        kdkabkota,
        setKdkabkota,
        kdkanwil,
        setKdkanwil,
        kdkppn,
        setKdkppn,
        kdsatker,
        setKdsatker,
        kdfungsi,
        setKdfungsi,
        kdsfungsi,
        setKdsfungsi,
        kdprogram,
        setKdprogram,
        kdgiat,
        setKdgiat,
        kdoutput,
        setKdoutput,
        kdsoutput,
        setKdsoutput,
        kdkomponen,
        setKdkomponen,
        kdskomponen,
        setKdskomponen,
        kdakun,
        setKdakun,
        kdsdana,
        setKdsdana,
        kdregister,
        setKdregister,
        kdInflasi,
        setKdInflasi,
        kdIkn,
        setKdIkn,
        kdKemiskinan,
        setKdKemiskinan,
        KdPRI,
        setKdPRI,
        KdPangan,
        setKdPangan,
        KdStunting,
        setKdStunting,
        KdPemilu,
        setKdPemilu,
        KdTema,
        setKdTema,
        KdPN,
        setKdPN,
        KdPP,
        setKdPP,
        KdKegPP,
        setKdKegPP,
        KdMP,
        setKdMP,
        // Filter values
        dept,
        setDept,
        deptkondisi,
        setDeptkondisi,
        katadept,
        setKatadept,
        kdunit,
        setKdunit,
        unitkondisi,
        setUnitkondisi,
        kataunit,
        setKataunit,
        dekon,
        setDekon,
        dekonkondisi,
        setDekonkondisi,
        katadekon,
        setKatadekon,
        prov,
        setProv,
        lokasikondisi,
        setLokasikondisi,
        katalokasi,
        setKatalokasi,
        kabkota,
        setKabkota,
        kabkotakondisi,
        setKabkotakondisi,
        katakabkota,
        setKatakabkota,
        kanwil,
        setKanwil,
        kanwilkondisi,
        setKanwilkondisi,
        katakanwil,
        setKatakanwil,
        kppn,
        setKppn,
        kppnkondisi,
        setKppnkondisi,
        katakppn,
        setKatakppn,
        satker,
        setSatker,
        satkerkondisi,
        setSatkerkondisi,
        katasatker,
        setKatasatker,
        fungsi,
        setFungsi,
        fungsikondisi,
        setFungsikondisi,
        katafungsi,
        setKatafungsi,
        sfungsi,
        setSfungsi,
        subfungsikondisi,
        setSubfungsikondisi,
        katasubfungsi,
        setKatasubfungsi,
        program,
        setProgram,
        programkondisi,
        setProgramkondisi,
        kataprogram,
        setKataprogram,
        giat,
        setGiat,
        giatkondisi,
        setGiatkondisi,
        katagiat,
        setKatagiat,
        output,
        setOutput,
        outputkondisi,
        setOutputkondisi,
        kataoutput,
        setKataoutput,
        soutput,
        setsOutput,
        soutputkondisi,
        setSoutputkondisi,
        katasoutput,
        setKatasoutput,
        komponen,
        setKomponen,
        komponenkondisi,
        setKomponenkondisi,
        katakomponen,
        setKatakomponen,
        skomponen,
        setSkomponen,
        skomponenkondisi,
        setSkomponenkondisi,
        kataskomponen,
        setKataskomponen,
        akun,
        setAkun,
        akunkondisi,
        setAkunkondisi,
        kataakun,
        setKataakun,
        sdana,
        setSdana,
        sdanakondisi,
        setSdanakondisi,
        katasdana,
        setKatasdana,
        register,
        setRegister,
        registerkondisi,
        setRegisterkondisi,
        kataregister,
        setKataregister,
        PN,
        setPN,
        PP,
        setPP,
        PRI,
        setPRI,
        MP,
        setMP,
        Tema,
        setTema,
        Inflasi,
        setInflasi,
        Stunting,
        setStunting,
        Miskin,
        setMiskin,
        Pemilu,
        setPemilu,
        Ikn,
        setIkn,
        Pangan,
        setPangan,
        // Radio states
        deptradio,
        setDeptradio,
        unitradio,
        setUnitradio,
        dekonradio,
        setDekonradio,
        locradio,
        setLocradio,
        kabkotaradio,
        setKabkotaradio,
        kanwilradio,
        setKanwilradio,
        kppnradio,
        setKppnradio,
        satkerradio,
        setSatkerradio,
        fungsiradio,
        setFungsiradio,
        subfungsiradio,
        setSubfungsiradio,
        programradio,
        setProgramradio,
        kegiatanradio,
        setKegiatanradio,
        outputradio,
        setOutputradio,
        soutputradio,
        setsOutputradio,
        komponenradio,
        setKomponenradio,
        skomponenradio,
        setSkomponenradio,
        akunradio,
        setAkunradio,
        sdanaradio,
        setSdanaradio,
        registerradio,
        setRegisterradio,
        inflasiradio,
        setInflasiradio,
        iknradio,
        setIknradio,
        kemiskinanradio,
        setKemiskinanradio,
        priradio,
        setPriradio,
        panganradio,
        setPanganradio,
        stuntingradio,
        setStuntingradio,
        pemiluradio,
        setPemiluradio,
        pnradio,
        setPnradio,
        ppradio,
        setPpradio,
        mpradio,
        setMpradio,
        temaradio,
        setTemaradio,
        // Special filter option states
        opsidept,
        setOpsidept,
        opsiInflasi,
        setOpsiInflasi,
        opsiIkn,
        setOpsiIkn,
        opsiKemiskinan,
        setOpsiKemiskinan,
        // SQL state
        sql,
        setSql,
        from,
        setFrom,
        select,
        setSelect,
        // Kegiatan Prioritas filter value and radio
        kegiatanprioritas,
        setKegiatanPrioritas,
        kegiatanprioritasradio,
        setKegiatanPrioritasRadio
    };
}
}),
"[project]/src/components/features/inquiry/shared/hooks/useQueryBuilderModular.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Modern useQueryBuilder Hook - Modular and Scalable
 *
 * This replaces the 1800+ line monolithic query builder with a clean,
 * modular approach using separate filter classes.
 */ __turbopack_context__.s({
    "default": ()=>useQueryBuilder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$QueryBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__QueryBuilder$3e$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/QueryBuilder.js [app-ssr] (ecmascript) <export default as QueryBuilder>");
"use client";
;
;
function useQueryBuilder(inquiryState) {
    const [queryCache, setQueryCache] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // Initialize query builder instance
    const queryBuilder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$QueryBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__QueryBuilder$3e$__["QueryBuilder"](), []);
    // Extract key state for dependency tracking
    const { thang, jenlap, cutoff, tanggal, akumulatif, pembulatan, // Filter switches and values are automatically handled by the filter modules
    setFrom, setSelect, setSql } = inquiryState;
    /**
   * Build the complete SQL query
   */ const buildQuery = ()=>{
        try {
            const query = queryBuilder.buildQuery(inquiryState);
            // Update state components for backward compatibility
            const preview = queryBuilder.generateSqlPreview(inquiryState);
            setFrom && setFrom(preview.fromClause);
            setSelect && setSelect(preview.selectClause);
            setSql && setSql(query);
            return query;
        } catch (error) {
            console.error("Error building query:", error);
            return "";
        }
    };
    /**
   * Get query performance metrics
   */ const getQueryPerformanceMetrics = ()=>{
        return queryBuilder.getQueryPerformanceMetrics(inquiryState);
    };
    /**
   * Generate SQL preview without full execution
   */ const generateSqlPreview = ()=>{
        return queryBuilder.generateSqlPreview(inquiryState);
    };
    /**
   * Validate current query
   */ const validateQuery = (query = buildQuery)=>{
        return queryBuilder.validateQuery(query);
    };
    /**
   * Get filter statistics
   */ const getFilterStats = ()=>{
        return queryBuilder.filterBuilder.getFilterStats(inquiryState);
    };
    /**
   * Check if specific filter is enabled
   */ const isFilterEnabled = (filterName)=>{
        return queryBuilder.filterBuilder.isFilterEnabled(filterName, inquiryState);
    };
    /**
   * Get all available filters
   */ const getAvailableFilters = ()=>{
        return queryBuilder.filterBuilder.getAvailableFilters();
    };
    /**
   * Build specific filter only
   */ const buildFilter = (filterName)=>{
        return queryBuilder.filterBuilder.buildFilter(filterName, inquiryState);
    };
    /**
   * Debug filter behavior
   */ const debugFilter = (filterName)=>{
        const filterResult = buildFilter(filterName);
        const isEnabled = isFilterEnabled(filterName);
        console.log(`🔍 Debug Filter: ${filterName}`, {
            isEnabled,
            columns: filterResult.columns,
            joinClause: filterResult.joinClause,
            whereConditions: filterResult.whereConditions,
            groupBy: filterResult.groupBy
        });
        return {
            filterName,
            isEnabled,
            ...filterResult
        };
    };
    /**
   * Debug special filters for jenlap = 6 and 7
   */ const debugSpecialFilters = ()=>{
        const { jenlap } = inquiryState;
        if (jenlap === "6") {
            // jenlap = 6 now uses blokir filter (original jenlap = 7 logic)
            return debugFilter("blokir");
        } else if (jenlap === "7") {
            // jenlap = 7 now uses special filters (original jenlap = 6 logic)
            const specialFilters = [
                "inflasi",
                "stunting",
                "kemiskinan",
                "pemilu",
                "ikn",
                "pangan",
                "specialgrouping"
            ];
            return specialFilters.map((filterName)=>debugFilter(filterName));
        }
        return [];
    };
    /**
   * Get query complexity analysis
   */ const analyzeQueryComplexity = ()=>{
        const metrics = getQueryPerformanceMetrics();
        const stats = getFilterStats();
        return {
            complexity: {
                low: stats.enabledFilters <= 3 && metrics.validation.stats.joinCount <= 3,
                medium: stats.enabledFilters <= 6 && metrics.validation.stats.joinCount <= 6,
                high: stats.enabledFilters > 6 || metrics.validation.stats.joinCount > 6
            },
            metrics,
            stats,
            recommendations: metrics.recommendations
        };
    };
    /**
   * Cache queries for performance
   */ const getCachedQuery = (cacheKey)=>{
        return queryCache[cacheKey];
    };
    const setCachedQuery = (cacheKey, query)=>{
        setQueryCache((prev)=>({
                ...prev,
                [cacheKey]: {
                    query,
                    timestamp: Date.now()
                }
            }));
    };
    /**
   * Clear query cache
   */ const clearQueryCache = ()=>{
        setQueryCache({});
    };
    // Log performance metrics in development
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            try {
                const metrics = getQueryPerformanceMetrics();
                if (metrics.validation.warnings.length > 0) {
                    console.warn("⚠️ Query Builder Warnings:", metrics.validation.warnings);
                }
                if (metrics.recommendations.length > 0) {
                    console.info("💡 Query Builder Recommendations:", metrics.recommendations);
                }
                console.log("📊 Query Stats:", {
                    buildTime: `${metrics.buildTime.toFixed(2)}ms`,
                    enabledFilters: metrics.filterStats.enabledFilters,
                    queryLength: metrics.validation.stats.queryLength,
                    joinCount: metrics.validation.stats.joinCount
                });
            } catch (error) {
                console.warn("Error getting query performance metrics:", error);
            }
        }
    }, [
        thang,
        jenlap,
        cutoff,
        tanggal,
        akumulatif,
        pembulatan
    ]); // Use specific dependencies instead of buildQuery
    // Backward compatibility methods (matching original useQueryBuilder interface)
    const legacyMethods = {
        generateSqlPreview,
        generateOptimizedSql: ()=>buildQuery,
        parseAdvancedConditions: (kondisiValue, fieldName)=>{
            // Delegate to BaseFilter logic
            const baseFilter = new queryBuilder.filterBuilder.filters.department.constructor();
            return baseFilter.parseKondisiConditions(kondisiValue);
        },
        optimizeGroupBy: (columns, groupFields)=>{
            return [
                ...new Set(groupFields)
            ].filter((group)=>columns.some((col)=>col.includes(group) || group.includes("a.")));
        },
        optimizeJoins: (joinClause)=>{
            return queryBuilder.filterBuilder.optimizeJoins(Array.isArray(joinClause) ? joinClause : [
                joinClause
            ]);
        },
        validateQuery,
        getQueryPerformanceMetrics,
        getQueryStats: getFilterStats
    };
    return {
        // Core functionality - Return both string value and function for compatibility
        buildQuery: buildQuery,
        getBuildQuery: ()=>buildQuery,
        // Analysis and debugging
        generateSqlPreview,
        validateQuery,
        getQueryPerformanceMetrics,
        getFilterStats,
        analyzeQueryComplexity,
        // Filter management
        isFilterEnabled,
        getAvailableFilters,
        buildFilter,
        debugFilter,
        debugSpecialFilters,
        // Performance
        getCachedQuery,
        setCachedQuery,
        clearQueryCache,
        // Backward compatibility
        ...legacyMethods
    };
}
}),
"[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Base Filter Class
 * Contains common filter logic that all specific filters extend from
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
class BaseFilter {
    constructor(fieldName, tableName, referenceTable = null){
        this.fieldName = fieldName;
        this.tableName = tableName;
        this.referenceTable = referenceTable;
    }
    /**
   * Build column selection based on radio button value
   * @param {string} radio - Radio button value ("1" = code, "2" = code+text, "3" = text)
   * @param {string} thang - Year parameter
   * @param {object} inquiryState - (optional) full inquiry state, used for jenlap-specific logic
   * @returns {object} - { columns: [], joinClause: string, groupBy: [] }
   */ buildColumns(radio, thang = "", inquiryState = {}) {
        const result = {
            columns: [],
            joinClause: "",
            groupBy: []
        };
        if (!radio) return result;
        const codeField = `a.${this.fieldName}`;
        const textField = this.referenceTable ? `${this.referenceTable.alias}.${this.referenceTable.nameField}` : null;
        // Determine pagu field name based on jenlap (for use in child filters)
        const paguField = inquiryState && inquiryState.jenlap === "1" ? "a.pagu_apbn" : "a.pagu";
        switch(radio){
            case "1":
                result.columns.push(codeField);
                result.groupBy.push(codeField);
                break;
            case "2":
                result.columns.push(codeField);
                if (textField) {
                    result.columns.push(textField);
                    result.joinClause = this.buildJoinClause(thang);
                    result.groupBy.push(textField); // Add text field to GROUP BY
                }
                result.groupBy.push(codeField);
                break;
            case "3":
                if (textField) {
                    result.columns.push(textField);
                    result.joinClause = this.buildJoinClause(thang);
                    result.groupBy.push(textField); // Add text field to GROUP BY
                }
                result.groupBy.push(codeField);
                break;
        }
        // Optionally expose paguField for use in child filters
        result.paguField = paguField;
        return result;
    }
    /**
   * Build JOIN clause for reference table
   * @param {string} thang - Year parameter
   * @returns {string} - JOIN clause
   */ buildJoinClause(thang = "") {
        if (!this.referenceTable) return "";
        const yearSuffix = this.referenceTable.hasYear ? `_${thang}` : "";
        const tableName = `${this.referenceTable.schema}.${this.referenceTable.table}${yearSuffix}`;
        return ` LEFT JOIN ${tableName} ${this.referenceTable.alias} ON ${this.referenceTable.joinCondition}`;
    }
    /**
   * Build WHERE conditions based on filter type
   * @param {object} filterData - Filter configuration
   * @returns {string[]} - Array of WHERE conditions
   */ buildWhereConditions(filterData) {
        const conditions = [];
        const { pilihValue, kondisiValue, kataValue, opsiType, defaultValues = [
            "XXX",
            "000",
            "XX",
            "00",
            "XXXX",
            "0000",
            "XXXXXX",
            "000000"
        ] } = filterData;
        // Priority: 1. kata (keyword), 2. kondisi (conditions), 3. pilih (select)
        if (kataValue && kataValue.trim() !== "") {
            const textField = this.referenceTable ? `${this.referenceTable.alias}.${this.referenceTable.nameField}` : `a.${this.fieldName}`;
            conditions.push(`${textField} LIKE '%${kataValue}%'`);
        } else if (kondisiValue && kondisiValue.trim() !== "") {
            conditions.push(this.parseKondisiConditions(kondisiValue));
        } else if (pilihValue && !defaultValues.includes(pilihValue)) {
            conditions.push(`a.${this.fieldName} = '${pilihValue}'`);
        }
        return conditions.filter((condition)=>condition && condition.trim() !== "");
    }
    /**
   * Parse kondisi (conditions) input with advanced operators
   * @param {string} kondisiValue - Conditions string
   * @returns {string} - WHERE condition
   */ parseKondisiConditions(kondisiValue) {
        if (!kondisiValue || kondisiValue.trim() === "") return "";
        const fieldName = `a.${this.fieldName}`;
        // Handle NOT IN conditions (starting with !)
        if (kondisiValue.substring(0, 1) === "!") {
            const cleanValue = kondisiValue.substring(1);
            const values = cleanValue.split(",").map((val)=>val.trim()).filter((val)=>val !== "");
            if (values.length > 0) {
                const formattedValues = values.map((val)=>`'${val}'`).join(",");
                return `${fieldName} NOT IN (${formattedValues})`;
            }
        } else if (kondisiValue.includes("%")) {
            return `${fieldName} LIKE '${kondisiValue}'`;
        } else if (kondisiValue.includes("-") && !kondisiValue.includes(",")) {
            const [start, end] = kondisiValue.split("-").map((val)=>val.trim());
            if (start && end) {
                return `${fieldName} BETWEEN '${start}' AND '${end}'`;
            }
        } else {
            const values = kondisiValue.split(",").map((val)=>val.trim()).filter((val)=>val !== "");
            if (values.length > 0) {
                const formattedValues = values.map((val)=>`'${val}'`).join(",");
                return `${fieldName} IN (${formattedValues})`;
            }
        }
        return "";
    }
    /**
   * Main method to build complete filter
   * @param {object} filterState - Complete filter state
   * @param {string} thang - Year parameter
   * @returns {object} - Complete filter result
   */ build(filterState, thang = "") {
        const { isEnabled, radio, pilihValue, kondisiValue, kataValue, opsiType } = filterState;
        const result = {
            columns: [],
            joinClause: "",
            groupBy: [],
            whereConditions: []
        };
        // Only build if filter is enabled
        if (!isEnabled) return result;
        // Build columns and joins
        const columnResult = this.buildColumns(radio, thang);
        result.columns = columnResult.columns;
        result.joinClause = columnResult.joinClause;
        result.groupBy = columnResult.groupBy;
        // Special handling for kata (keyword) filters
        // When using kata filter, we need the descriptive column and JOIN regardless of radio setting
        if (kataValue && kataValue.trim() !== "" && this.referenceTable) {
            const textField = `${this.referenceTable.alias}.${this.referenceTable.nameField}`;
            // Add JOIN if not already present
            if (!result.joinClause) {
                result.joinClause = this.buildJoinClause(thang);
            }
            // Add descriptive column to SELECT if not already present
            if (!result.columns.includes(textField)) {
                result.columns.push(textField);
                // Also add to GROUP BY to avoid GROUP BY errors
                if (!result.groupBy.includes(textField)) {
                    result.groupBy.push(textField);
                }
            }
        }
        // Build WHERE conditions
        result.whereConditions = this.buildWhereConditions({
            pilihValue,
            kondisiValue,
            kataValue,
            opsiType
        });
        return result;
    }
    getEmptyResult() {
        return {
            columns: [],
            joinClause: "",
            whereConditions: [],
            groupBy: []
        };
    }
}
const __TURBOPACK__default__export__ = BaseFilter;
}),
"[project]/src/components/features/inquiry/shared/filters/DepartmentFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DekonFilter": ()=>DekonFilter,
    "DepartmentFilter": ()=>DepartmentFilter,
    "SatkerFilter": ()=>SatkerFilter,
    "UnitFilter": ()=>UnitFilter,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
;
/**
 * Department Filter Handler
 */ class DepartmentFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kddept", "department", {
            schema: "dbref",
            table: "t_dept",
            alias: "b",
            nameField: "nmdept",
            hasYear: true,
            joinCondition: "a.kddept=b.kddept"
        });
    }
    /**
   * Build department filter from inquiry state
   * @param {object} inquiryState - Full inquiry state
   * @returns {object} - Filter result
   */ buildFromState(inquiryState) {
        const { kddept: isEnabled, dept: pilihValue, deptkondisi: kondisiValue, katadept: kataValue, deptradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Unit Filter Handler
 */ class UnitFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdunit", "unit", {
            schema: "dbref",
            table: "t_unit",
            alias: "c",
            nameField: "nmunit",
            hasYear: true,
            joinCondition: "a.kddept=c.kddept AND a.kdunit=c.kdunit"
        });
    }
    buildFromState(inquiryState) {
        const { unit: isEnabled, kdunit: pilihValue, unitkondisi: kondisiValue, kataunit: kataValue, unitradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Dekonsentrasi Filter Handler
 */ class DekonFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kddekon", "dekonsentrasi", {
            schema: "dbref",
            table: "t_dekon",
            alias: "d",
            nameField: "nmdekon",
            hasYear: true,
            joinCondition: "a.kddekon=d.kddekon"
        });
    }
    buildFromState(inquiryState) {
        const { kddekon: isEnabled, dekon: pilihValue, dekonkondisi: kondisiValue, katadekon: kataValue, dekonradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Satker Filter Handler
 */ class SatkerFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdsatker", "satker", {
            schema: "dbref",
            table: "t_satker",
            alias: "s",
            nameField: "nmsatker",
            hasYear: true,
            joinCondition: "a.kddept=s.kddept AND a.kdunit=s.kdunit AND a.kdsatker=s.kdsatker"
        });
    }
    buildFromState(inquiryState) {
        const { kdsatker: isEnabled, satker: pilihValue, satkerkondisi: kondisiValue, katasatker: kataValue, satkerradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
;
const __TURBOPACK__default__export__ = DepartmentFilter;
}),
"[project]/src/components/features/inquiry/shared/filters/LocationFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "KabkotaFilter": ()=>KabkotaFilter,
    "KanwilFilter": ()=>KanwilFilter,
    "KppnFilter": ()=>KppnFilter,
    "ProvinsiFilter": ()=>ProvinsiFilter,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
;
/**
 * Provinsi Filter Handler
 */ class ProvinsiFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdlokasi", "provinsi", {
            schema: "dbref",
            table: "t_lokasi",
            alias: "p",
            nameField: "nmlokasi",
            hasYear: true,
            joinCondition: "a.kdlokasi=p.kdlokasi"
        });
    }
    buildFromState(inquiryState) {
        const { kdlokasi: isEnabled, prov: pilihValue, lokasikondisi: kondisiValue, katalokasi: kataValue, locradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Kabupaten/Kota Filter Handler
 */ class KabkotaFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdkabkota", "kabkota", {
            schema: "dbref",
            table: "t_kabkota",
            alias: "kk",
            nameField: "nmkabkota",
            hasYear: true,
            joinCondition: "a.kdlokasi=kk.kdlokasi AND a.kdkabkota=kk.kdkabkota"
        });
    }
    buildFromState(inquiryState) {
        const { kdkabkota: isEnabled, kabkota: pilihValue, kabkotakondisi: kondisiValue, katakabkota: kataValue, kabkotaradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Kanwil Filter Handler
 */ class KanwilFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdkanwil", "kanwil", {
            schema: "dbref",
            table: "t_kanwil",
            alias: "kw",
            nameField: "nmkanwil",
            hasYear: true,
            joinCondition: "a.kdkanwil=kw.kdkanwil"
        });
    }
    buildFromState(inquiryState) {
        const { kdkanwil: isEnabled, kanwil: pilihValue, kanwilkondisi: kondisiValue, katakanwil: kataValue, kanwilradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * KPPN Filter Handler
 */ class KppnFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdkppn", "kppn", {
            schema: "dbref",
            table: "t_kppn",
            alias: "kp",
            nameField: "nmkppn",
            hasYear: true,
            joinCondition: "a.kdkppn=kp.kdkppn"
        });
    }
    buildFromState(inquiryState) {
        const { kdkppn: isEnabled, kppn: pilihValue, kppnkondisi: kondisiValue, katakppn: kataValue, kppnradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
;
const __TURBOPACK__default__export__ = ProvinsiFilter;
}),
"[project]/src/components/features/inquiry/shared/filters/ProgramFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "FungsiFilter": ()=>FungsiFilter,
    "KegiatanFilter": ()=>KegiatanFilter,
    "OutputFilter": ()=>OutputFilter,
    "ProgramFilter": ()=>ProgramFilter,
    "SubFungsiFilter": ()=>SubFungsiFilter,
    "SubOutputFilter": ()=>SubOutputFilter,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
;
/**
 * Fungsi Filter Handler
 */ class FungsiFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdfungsi", "fungsi", {
            schema: "dbref",
            table: "t_fungsi",
            alias: "f",
            nameField: "nmfungsi",
            hasYear: true,
            joinCondition: "a.kdfungsi=f.kdfungsi"
        });
    }
    buildFromState(inquiryState) {
        const { kdfungsi: isEnabled, fungsi: pilihValue, fungsikondisi: kondisiValue, katafungsi: kataValue, fungsiradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Sub Fungsi Filter Handler
 */ class SubFungsiFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdsfung", "subfungsi", {
            schema: "dbref",
            table: "t_sfung",
            alias: "sf",
            nameField: "nmsfung",
            hasYear: true,
            joinCondition: "a.kdfungsi=sf.kdfungsi AND a.kdsfung=sf.kdsfung"
        });
    }
    buildFromState(inquiryState) {
        const { kdsfungsi: isEnabled, sfungsi: pilihValue, subfungsikondisi: kondisiValue, katasubfungsi: kataValue, subfungsiradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Program Filter Handler
 */ class ProgramFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdprogram", "program", {
            schema: "dbref",
            table: "t_program",
            alias: "pr",
            nameField: "nmprogram",
            hasYear: true,
            joinCondition: "a.kddept=pr.kddept AND a.kdunit=pr.kdunit AND a.kdprogram=pr.kdprogram"
        });
    }
    buildFromState(inquiryState) {
        const { kdprogram: isEnabled, program: pilihValue, programkondisi: kondisiValue, kataprogram: kataValue, programradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Kegiatan Filter Handler
 */ class KegiatanFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdgiat", "kegiatan", {
            schema: "dbref",
            table: "t_giat",
            alias: "g",
            nameField: "nmgiat",
            hasYear: true,
            joinCondition: "a.kddept=g.kddept AND a.kdunit=g.kdunit AND a.kdprogram=g.kdprogram AND a.kdgiat=g.kdgiat"
        });
    }
    buildFromState(inquiryState) {
        const { kdgiat: isEnabled, giat: pilihValue, giatkondisi: kondisiValue, katagiat: kataValue, kegiatanradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Output Filter Handler
 */ class OutputFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdoutput", "output", {
            schema: "dbref",
            table: "t_output",
            alias: "o",
            nameField: "nmoutput",
            hasYear: true,
            joinCondition: "a.kddept=o.kddept AND a.kdunit=o.kdunit AND a.kdprogram=o.kdprogram AND a.kdgiat=o.kdgiat AND a.kdoutput=o.kdoutput"
        });
    }
    buildFromState(inquiryState) {
        const { kdoutput: isEnabled, output: pilihValue, outputkondisi: kondisiValue, kataoutput: kataValue, outputradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Sub Output Filter Handler
 */ class SubOutputFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdsoutput", "suboutput", {
            schema: "dbref",
            table: "t_soutput",
            alias: "so",
            nameField: "nmsoutput",
            hasYear: true,
            joinCondition: "a.kddept=so.kddept AND a.kdunit=so.kdunit AND a.kdprogram=so.kdprogram AND a.kdgiat=so.kdgiat AND a.kdoutput=so.kdoutput AND a.kdsoutput=so.kdsoutput"
        });
    }
    buildFromState(inquiryState) {
        const { kdsoutput: isEnabled, soutput: pilihValue, soutputkondisi: kondisiValue, katasoutput: kataValue, soutputradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
;
const __TURBOPACK__default__export__ = ProgramFilter;
}),
"[project]/src/components/features/inquiry/shared/filters/AccountFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AkunFilter": ()=>AkunFilter,
    "RegisterFilter": ()=>RegisterFilter,
    "SumberDanaFilter": ()=>SumberDanaFilter,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
;
/**
 * Akun Filter Handler
 */ class AkunFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdakun", "akun", {
            schema: "dbref",
            table: "t_akun",
            alias: "ak",
            nameField: "nmakun",
            hasYear: true,
            joinCondition: "a.kdakun=ak.kdakun"
        });
    }
    buildFromState(inquiryState) {
        const { kdakun: isEnabled, akun: pilihValue, akunkondisi: kondisiValue, kataakun: kataValue, akunradio: radio, thang } = inquiryState;
        // Universal: If Jenis Tampilan is 'Jangan Tampilkan' (radio === '4'), return empty result (no-op)
        if (isEnabled && radio === "4") {
            return {
                columns: [],
                groupBy: [],
                joinClause: "",
                whereConditions: []
            };
        }
        // Special: If 'Kode BKPK' or 'Jenis Belanja' is selected, customize SELECT, GROUP BY, and filter logic
        if (isEnabled && (pilihValue === "BKPK" || pilihValue === "JENBEL")) {
            // Use 4 for BKPK, 2 for JENBEL
            const leftLen = pilihValue === "BKPK" ? 4 : 2;
            // Build default, but override columns, groupBy, joinClause, and whereConditions
            const result = this.build({
                isEnabled: true,
                radio,
                pilihValue: "",
                kondisiValue: "",
                kataValue: ""
            }, thang);
            if (pilihValue === "BKPK") {
                const bkpkTable = `dbref.t_bkpk_${thang}`;
                if (radio === "3") {
                    result.columns = [
                        "bk.nmbkpk"
                    ];
                    result.joinClause = ` LEFT JOIN ${bkpkTable} bk ON LEFT(a.kdakun,${leftLen}) = bk.kdbkpk`;
                    result.groupBy = [
                        `LEFT(a.kdakun,${leftLen})`
                    ];
                } else if (radio === "2") {
                    result.columns = [
                        `LEFT(a.kdakun,${leftLen}) AS kdbkpk`,
                        "bk.nmbkpk"
                    ];
                    result.joinClause = ` LEFT JOIN ${bkpkTable} bk ON LEFT(a.kdakun,${leftLen}) = bk.kdbkpk`;
                    result.groupBy = [
                        `LEFT(a.kdakun,${leftLen})`
                    ];
                } else {
                    result.columns = [
                        `LEFT(a.kdakun,${leftLen}) AS kdbkpk`
                    ];
                    result.groupBy = [
                        `LEFT(a.kdakun,${leftLen})`
                    ];
                    result.joinClause = "";
                }
                // Custom filter for kondisi (akunkondisi)
                if (kondisiValue && /^[0-9]+$/.test(kondisiValue)) {
                    const n = kondisiValue.length;
                    result.whereConditions = [
                        `LEFT(a.kdakun,${n}) IN ('${kondisiValue}')`
                    ];
                }
                // Custom filter for kata (bk.nmbkpk)
                if (kataValue && kataValue.trim() !== "") {
                    result.whereConditions = [
                        `bk.nmbkpk LIKE '%${kataValue.trim()}%'`
                    ];
                }
            } else if (pilihValue === "JENBEL") {
                const gbkpkTable = `dbref.t_gbkpk_${thang}`;
                if (radio === "3") {
                    result.columns = [
                        "gb.nmgbkpk"
                    ];
                    result.joinClause = ` LEFT JOIN ${gbkpkTable} gb ON LEFT(a.kdakun,${leftLen}) = gb.kdgbkpk`;
                    result.groupBy = [
                        `LEFT(a.kdakun,${leftLen})`
                    ];
                } else if (radio === "2") {
                    result.columns = [
                        `LEFT(a.kdakun,${leftLen}) AS kdgbkpk`,
                        "gb.nmgbkpk"
                    ];
                    result.joinClause = ` LEFT JOIN ${gbkpkTable} gb ON LEFT(a.kdakun,${leftLen}) = gb.kdgbkpk`;
                    result.groupBy = [
                        `LEFT(a.kdakun,${leftLen})`
                    ];
                } else {
                    result.columns = [
                        `LEFT(a.kdakun,${leftLen}) AS kdgbkpk`
                    ];
                    result.groupBy = [
                        `LEFT(a.kdakun,${leftLen})`
                    ];
                    result.joinClause = "";
                }
                // Custom filter for kondisi (akunkondisi)
                if (kondisiValue && /^[0-9]+$/.test(kondisiValue)) {
                    const n = kondisiValue.length;
                    result.whereConditions = [
                        `LEFT(a.kdakun,${n}) IN ('${kondisiValue}')`
                    ];
                }
                // Custom filter for kata (gb.nmgbkpk)
                if (kataValue && kataValue.trim() !== "") {
                    result.whereConditions = [
                        `gb.nmgbkpk LIKE '%${kataValue.trim()}%'`
                    ];
                }
            }
            return result;
        }
        // If 'Kode Akun' (AKUN) is selected, skip whereCondition but keep columns
        if (isEnabled && (pilihValue === "AKUN" || !pilihValue) && !kondisiValue && !kataValue) {
            // Pass isEnabled true, but pilihValue empty so build() only returns columns
            return this.build({
                isEnabled: true,
                radio,
                pilihValue: "",
                kondisiValue: "",
                kataValue: ""
            }, thang);
        }
        // Custom: if user enters a value in akunkondisi, use LEFT(a.kdakun, N) IN (...)
        if (isEnabled && kondisiValue && /^[0-9]+$/.test(kondisiValue)) {
            const n = kondisiValue.length;
            const whereCondition = `LEFT(a.kdakun,${n}) IN ('${kondisiValue}')`;
            // Call build, but override whereConditions
            const result = this.build({
                isEnabled,
                radio,
                pilihValue: "",
                kondisiValue: "",
                kataValue
            }, thang);
            // Inject custom whereCondition
            return {
                ...result,
                whereConditions: [
                    whereCondition
                ]
            };
        }
        // Custom: if user enters a value in kataValue, use ak.nmakun LIKE '%kataValue%'
        if (isEnabled && kataValue && kataValue.trim() !== "") {
            const whereCondition = `ak.nmakun LIKE '%${kataValue.trim()}%'`;
            const result = this.build({
                isEnabled,
                radio,
                pilihValue: "",
                kondisiValue: "",
                kataValue
            }, thang);
            return {
                ...result,
                whereConditions: [
                    whereCondition
                ]
            };
        }
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Sumber Dana Filter Handler
 */ class SumberDanaFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdsdana", "sdana", {
            schema: "dbref",
            table: "t_sdana",
            alias: "sd",
            nameField: "nmsdana",
            hasYear: true,
            joinCondition: "a.kdsdana=sd.kdsdana"
        });
    }
    buildFromState(inquiryState) {
        const { kdsdana: isEnabled, sdana: pilihValue, sdanakondisi: kondisiValue, opsikatasdana: kataValue, sdanaradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Register Filter Handler
 */ class RegisterFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdregister", "register", {
            schema: "dbref",
            table: "t_register",
            alias: "r",
            nameField: "nmregister",
            hasYear: true,
            joinCondition: "a.kdregister=r.kdregister"
        });
    }
    buildFromState(inquiryState) {
        const { kdregister: isEnabled, register: pilihValue, registerkondisi: kondisiValue, opsikataregister: kataValue, registerradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
;
const __TURBOPACK__default__export__ = AkunFilter;
}),
"[project]/src/components/features/inquiry/shared/filters/PriorityFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "KegiatanPrioritasFilter": ()=>KegiatanPrioritasFilter,
    "MegaProjectFilter": ()=>MegaProjectFilter,
    "PrioritasFilter": ()=>PrioritasFilter,
    "PronasFilter": ()=>PronasFilter,
    "PropresFilter": ()=>PropresFilter,
    "TemaFilter": ()=>TemaFilter,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
;
/**
 * Pronas (PN) Filter Handler
 */ class PronasFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdpn", "pronas", {
            schema: "dbref",
            table: "t_prinas",
            alias: "pn",
            nameField: "nmpn",
            hasYear: true,
            joinCondition: "a.kdpn=pn.kdpn"
        });
    }
    buildFromState(inquiryState) {
        const { KdPN: isEnabled, PN: pilihValue, PNkondisi: kondisiValue, opsikataPN: kataValue, pnradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Propres (PP) Filter Handler
 */ class PropresFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdpp", "propres", {
            schema: "dbref",
            table: "t_priprog",
            alias: "pp",
            nameField: "nmpp",
            hasYear: true,
            joinCondition: "a.kdpp=pp.kdpp"
        });
    }
    buildFromState(inquiryState) {
        const { KdPP: isEnabled, PP: pilihValue, PPkondisi: kondisiValue, opsikataPP: kataValue, ppradio: radio, thang } = inquiryState;
        // Handle composite PP value (e.g., "01-01" should become "01")
        let processedPilihValue = pilihValue;
        if (pilihValue && pilihValue.includes("-")) {
            processedPilihValue = pilihValue.split("-")[1]; // Get the part after the dash
            console.log(`PropresFilter (inquiry) - Extracted PP from composite value: "${pilihValue}" -> "${processedPilihValue}"`);
        }
        return this.build({
            isEnabled,
            radio,
            pilihValue: processedPilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Kegiatan Prioritas (KP) Filter Handler
 */ class KegiatanPrioritasFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdkp", "kegiatanprioritas", {
            schema: "dbref",
            table: "t_prigiat",
            alias: "pg",
            nameField: "nmkp",
            hasYear: true,
            joinCondition: "a.kdkp=pg.kdkp AND a.kdpp=pg.kdpp AND a.kdpn=pg.kdpn"
        });
    }
    buildFromState(inquiryState) {
        const { KdKegPP: isEnabled, kegiatanprioritas: pilihValue, kegiatanprioritasradio: radio, thang } = inquiryState;
        const kondisiValue = undefined;
        const kataValue = undefined;
        // DEBUG: Log the state values
        console.log("🔍 KegiatanPrioritasFilter DEBUG:", {
            isEnabled,
            pilihValue,
            radio,
            thang,
            timestamp: new Date().toISOString()
        });
        const result = this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
        // DEBUG: Log the result
        console.log("🔍 KegiatanPrioritasFilter RESULT:", result);
        // SPECIAL HANDLING: For Kegiatan Prioritas, we always need the JOIN clause when enabled,
        // even if pilihValue is "00" (Semua) and radio is "4" (Jangan Tampilkan)
        // This ensures the table is included in the query structure for proper filtering
        if (isEnabled && !result.joinClause) {
            result.joinClause = this.buildJoinClause(thang);
            console.log("🔍 KegiatanPrioritasFilter FORCED JOIN:", result.joinClause);
        }
        return result;
    }
}
/**
 * Prioritas (PRI) Filter Handler
 */ class PrioritasFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdproy", "prioritas", {
            schema: "dbref",
            table: "t_priproy",
            alias: "pri",
            nameField: "nmproy",
            hasYear: true,
            joinCondition: "a.kdproy=pri.kdproy"
        });
    }
    buildFromState(inquiryState) {
        const { KdPRI: isEnabled, PRI: pilihValue, PRIkondisi: kondisiValue, opsikataPRI: kataValue, priradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Tema Filter Handler
 */ class TemaFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdtema", "tema", {
            schema: "dbref",
            table: "t_tema",
            alias: "tm",
            nameField: "nmtema",
            hasYear: true,
            joinCondition: "a.kdtema=tm.kdtema"
        });
    }
    buildFromState(inquiryState) {
        const { KdTema: isEnabled, Tema: pilihValue, Temakondisi: kondisiValue, opsikataTema: kataValue, temaradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
/**
 * Mega Project (MP) Filter Handler
 */ class MegaProjectFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kdmp", "megaproject", {
            schema: "dbref",
            table: "t_mp",
            alias: "mp",
            nameField: "nmmp",
            hasYear: false,
            joinCondition: "a.kdmp=mp.kdmp"
        });
    }
    buildFromState(inquiryState) {
        const { KdMP: isEnabled, MP: pilihValue, MPkondisi: kondisiValue, opsikataMP: kataValue, mpradio: radio, thang } = inquiryState;
        return this.build({
            isEnabled,
            radio,
            pilihValue,
            kondisiValue,
            kataValue
        }, thang);
    }
}
;
const __TURBOPACK__default__export__ = PronasFilter;
}),
"[project]/src/components/features/inquiry/shared/filters/SpecialFilter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Special Filters for jenlap = 7 (Priority Programs)
 * Handles: Inflasi, Stunting, Kemiskinan, Pemilu, IKN, Pangan
 * Based on original SQL.jsx jenlap = 6 logic
 */ __turbopack_context__.s({
    "BlokirFilter": ()=>BlokirFilter,
    "IknFilter": ()=>IknFilter,
    "InflasiFilter": ()=>InflasiFilter,
    "KemiskinanFilter": ()=>KemiskinanFilter,
    "PanganFilter": ()=>PanganFilter,
    "PemiluFilter": ()=>PemiluFilter,
    "SpecialGroupingFilter": ()=>SpecialGroupingFilter,
    "StuntingFilter": ()=>StuntingFilter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
;
class InflasiFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("inflasi");
    }
    buildFromState(inquiryState) {
        const { jenlap, Inflasi, inflasiradio, opsiInflasi } = inquiryState;
        // Only activate this filter for jenlap 6, never for jenlap 7
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // FUNGSI KDINFLASI - Based on original jenlap = 6 logic
        if (inflasiradio === "1" && Inflasi !== "XX") {
            result.columns.push("a.inf_intervensi", "a.inf_pengeluaran");
            result.groupBy.push("a.inf_intervensi", "a.inf_pengeluaran");
        }
        if (inflasiradio === "2" && Inflasi !== "XX") {
            result.columns.push("a.inf_intervensi", "bb.ur_inf_intervensi", "a.inf_pengeluaran", "inf.ur_inf_pengeluaran");
            result.joinClause = " LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi" + " LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran";
            result.groupBy.push("a.inf_intervensi", "a.inf_pengeluaran");
        }
        if (inflasiradio === "3" && Inflasi !== "XX") {
            result.columns.push("bb.ur_inf_intervensi", "inf.ur_inf_pengeluaran");
            result.joinClause = " LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi" + " LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran";
            result.groupBy.push("a.inf_intervensi", "a.inf_pengeluaran");
        }
        if (inflasiradio === "4") {
            result.columns = [];
        }
        // Filter condition when opsiInflasi is set
        if (opsiInflasi === "pilihInflasi" && Inflasi !== "XX") {
            if (Inflasi && Inflasi !== "00") {
                result.whereConditions.push("(a.inf_intervensi <> 'NULL' OR a.inf_pengeluaran <> 'NULL')");
            }
        }
        return result;
    }
}
class StuntingFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("stunting");
    }
    buildFromState(inquiryState) {
        const { jenlap, Stunting, stuntingradio } = inquiryState;
        // Only activate this filter for jenlap 6, never for jenlap 7
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // FUNGSI KDSTUNTING - Based on original jenlap = 6 logic
        if (stuntingradio === "1" && Stunting !== "XX") {
            result.columns.push("a.stun_intervensi");
            result.groupBy.push("a.stun_intervensi");
        }
        if (stuntingradio === "2" && Stunting !== "XX") {
            result.columns.push("a.stun_intervensi", "stun.ur_stun_intervensi");
            result.joinClause = " LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi";
            result.groupBy.push("a.stun_intervensi");
        }
        if (stuntingradio === "3" && Stunting !== "XX") {
            result.columns.push("stun.ur_stun_intervensi");
            result.joinClause = " LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi";
            result.groupBy.push("a.stun_intervensi");
        }
        if (stuntingradio === "4") {
            result.columns = [];
        }
        // Note: opsiStunting field doesn't exist in state, removing filter condition
        // TODO: Add opsiStunting to state if needed for filtering by specific stunting intervention
        return result;
    }
}
class KemiskinanFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("kemiskinan");
    }
    buildFromState(inquiryState) {
        const { jenlap, Miskin, kemiskinanradio, opsiKemiskinan } = inquiryState;
        // Only activate this filter for jenlap 6, never for jenlap 7
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // FUNGSI KEMISKINAN EKSTRIM - Based on original jenlap = 6 logic
        if (kemiskinanradio === "1" && Miskin !== "XX") {
            result.columns.push("a.kemiskinan_ekstrim");
            result.groupBy.push("a.kemiskinan_ekstrim");
        }
        if (kemiskinanradio === "2" && Miskin !== "XX") {
            result.columns.push("a.kemiskinan_ekstrim", "(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim");
            result.groupBy.push("a.kemiskinan_ekstrim");
        }
        if (kemiskinanradio === "3" && Miskin !== "XX") {
            result.columns.push("(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim");
            result.groupBy.push("a.kemiskinan_ekstrim");
        }
        if (kemiskinanradio === "4") {
            result.columns = [];
        }
        // Filter condition when opsiKemiskinan is set
        if (opsiKemiskinan === "pilihKemiskinan" && Miskin !== "XX") {
            if (Miskin && Miskin !== "00") {
                result.whereConditions.push(`a.kemiskinan_ekstrim = '${Miskin}'`);
            }
        }
        return result;
    }
}
class PemiluFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("pemilu");
    }
    buildFromState(inquiryState) {
        const { jenlap, Pemilu, pemiluradio } = inquiryState;
        // Only activate this filter for jenlap 6, never for jenlap 7
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // FUNGSI BELANJA PEMILU - Based on original jenlap = 6 logic
        if (pemiluradio === "1" && Pemilu !== "XX") {
            result.columns.push("a.pemilu");
            result.groupBy.push("a.pemilu");
        }
        if (pemiluradio === "2" && Pemilu !== "XX") {
            result.columns.push("a.pemilu", "(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu");
            result.groupBy.push("a.pemilu");
        }
        if (pemiluradio === "3" && Pemilu !== "XX") {
            result.columns.push("(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu");
            result.groupBy.push("a.pemilu");
        }
        if (pemiluradio === "4") {
            result.columns = [];
        }
        // Note: opsiPemilu field doesn't exist in state, removing filter condition
        // TODO: Add opsiPemilu to state if needed for filtering by specific election type
        return result;
    }
}
class IknFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("ikn");
    }
    buildFromState(inquiryState) {
        const { jenlap, Ikn, iknradio, opsiIkn } = inquiryState;
        // Only activate this filter for jenlap 6, never for jenlap 7
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // FUNGSI BELANJA IKN - Based on original jenlap = 6 logic
        if (iknradio === "1" && Ikn !== "XX") {
            result.columns.push("a.ikn");
            result.groupBy.push("a.ikn");
        }
        if (iknradio === "2" && Ikn !== "XX") {
            result.columns.push("a.ikn", "(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn");
            result.groupBy.push("a.ikn");
        }
        if (iknradio === "3" && Ikn !== "XX") {
            result.columns.push("(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn");
            result.groupBy.push("a.ikn");
        }
        if (iknradio === "4") {
            result.columns = [];
        }
        // Filter condition when opsiIkn is set
        if (opsiIkn === "pilihikn" && Ikn !== "XX") {
            if (Ikn && Ikn !== "00") {
                result.whereConditions.push(`a.ikn = '${Ikn}'`);
            }
        }
        return result;
    }
}
class PanganFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("pangan");
    }
    buildFromState(inquiryState) {
        const { jenlap, Pangan, panganradio } = inquiryState;
        // Only activate this filter for jenlap 6, never for jenlap 7
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // FUNGSI BELANJA KETAHANAN PANGAN - Based on original jenlap = 6 logic
        if (panganradio === "1" && Pangan !== "XX") {
            result.columns.push("a.pangan");
            result.groupBy.push("a.pangan");
        }
        if (panganradio === "2" && Pangan !== "XX") {
            result.columns.push("a.pangan", "(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan");
            result.groupBy.push("a.pangan");
        }
        if (panganradio === "3" && Pangan !== "XX") {
            result.columns.push("(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan");
            result.groupBy.push("a.pangan");
        }
        if (panganradio === "4") {
            result.columns = [];
        }
        // Note: opsiPangan field doesn't exist in state, removing filter condition
        // TODO: Add opsiPangan to state if needed for filtering by specific food security type
        return result;
    }
}
class BlokirFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("blokir");
    }
    buildFromState(inquiryState) {
        const { jenlap, thang } = inquiryState;
        if (jenlap !== "6") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // KODE JENIS BLOKIR - Based on original jenlap = 7 logic
        result.columns.push("a.kdblokir", "a.nmblokir");
        result.groupBy.push("a.kdblokir");
        return result;
    }
}
class SpecialGroupingFilter extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(){
        super("specialgrouping");
    }
    buildFromState(inquiryState) {
        const { jenlap, thang } = inquiryState;
        if (jenlap !== "7") {
            return this.getEmptyResult();
        }
        const result = this.getEmptyResult();
        // PENAMBAHAN GROUPING DATA CAPAIAN OUTPUT
        if (thang >= "2021") {
            result.groupBy.push("a.sat", "a.os", "a.ket");
        } else {
            result.groupBy.push("a.sat");
        }
        return result;
    }
}
}),
"[project]/src/components/features/inquiry/shared/filters/FilterBuilder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Filter Builder - Orchestrates all filter modules
 * Provides a unified interface to build all filters
 */ // Import all filter modules
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/DepartmentFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/LocationFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/ProgramFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$AccountFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/AccountFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/PriorityFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/SpecialFilter.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
class FilterBuilder {
    constructor(){
        // Initialize all filter instances
        this.filters = {
            // Department & Organization
            department: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DepartmentFilter"](),
            unit: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnitFilter"](),
            dekon: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DekonFilter"](),
            satker: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SatkerFilter"](),
            // Location
            provinsi: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProvinsiFilter"](),
            kabkota: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KabkotaFilter"](),
            kanwil: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KanwilFilter"](),
            kppn: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KppnFilter"](),
            // Program Structure
            fungsi: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FungsiFilter"](),
            subfungsi: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SubFungsiFilter"](),
            program: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProgramFilter"](),
            kegiatan: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KegiatanFilter"](),
            output: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OutputFilter"](),
            suboutput: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SubOutputFilter"](),
            // Account
            akun: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$AccountFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AkunFilter"](),
            sdana: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$AccountFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SumberDanaFilter"](),
            register: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$AccountFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RegisterFilter"](),
            // Priority Programs
            pronas: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PronasFilter"](),
            propres: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PropresFilter"](),
            kegiatanprioritas: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KegiatanPrioritasFilter"](),
            prioritas: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PrioritasFilter"](),
            tema: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TemaFilter"](),
            megaproject: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MegaProjectFilter"](),
            // Special Filters (jenlap-specific)
            inflasi: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InflasiFilter"](),
            stunting: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StuntingFilter"](),
            kemiskinan: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["KemiskinanFilter"](),
            pemilu: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PemiluFilter"](),
            ikn: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IknFilter"](),
            pangan: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PanganFilter"](),
            blokir: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlokirFilter"](),
            specialgrouping: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SpecialGroupingFilter"]()
        };
    }
    /**
   * Build all enabled filters from inquiry state
   * @param {object} inquiryState - Complete inquiry state
   * @returns {object} - Aggregated filter results
   */ buildAllFilters(inquiryState) {
        const result = {
            columns: [],
            joinClauses: [],
            groupBy: [],
            whereConditions: []
        };
        // Only build filters that are enabled (except blokir and specialgrouping, which are jenlap-specific)
        Object.entries(this.filters).forEach(([key, filter])=>{
            let enabled = false;
            if (key === "blokir") {
                // Only enable blokir for jenlap 7 (Pergerakan Blokir Bulanan per Jenis)
                enabled = inquiryState.jenlap === "7";
            } else if (key === "specialgrouping") {
                // Always enable specialgrouping for jenlap 6 (Volume Output Kegiatan - Data Caput)
                enabled = inquiryState.jenlap === "6";
            } else {
                enabled = this.isFilterEnabled(key, inquiryState);
            }
            // Debug special filters
            if ([
                "inflasi",
                "stunting",
                "kemiskinan",
                "pemilu",
                "ikn",
                "pangan"
            ].includes(key)) {
                console.log(`🔍 Special Filter Debug - ${key}:`, {
                    enabled,
                    jenlap: inquiryState.jenlap,
                    filterSwitch: this.getFilterSwitchValue(key, inquiryState),
                    radioValue: this.getFilterRadioValue(key, inquiryState),
                    optionValue: this.getFilterOptionValue(key, inquiryState)
                });
            }
            if (!enabled) return;
            try {
                const filterResult = filter.buildFromState(inquiryState);
                if (filterResult.columns.length > 0) {
                    result.columns.push(...filterResult.columns);
                }
                if (filterResult.joinClause) {
                    result.joinClauses.push(filterResult.joinClause);
                }
                if (filterResult.groupBy.length > 0) {
                    result.groupBy.push(...filterResult.groupBy);
                }
                if (filterResult.whereConditions.length > 0) {
                    result.whereConditions.push(...filterResult.whereConditions);
                }
            } catch (error) {
                console.warn(`Error building ${key} filter:`, error);
            }
        });
        // Remove duplicates and optimize
        result.columns = [
            ...new Set(result.columns)
        ];
        result.joinClauses = this.optimizeJoins(result.joinClauses);
        result.groupBy = [
            ...new Set(result.groupBy)
        ];
        result.whereConditions = result.whereConditions.filter((condition)=>condition && condition.trim() !== "");
        return result;
    }
    /**
   * Build specific filter by name
   * @param {string} filterName - Name of the filter
   * @param {object} inquiryState - Complete inquiry state
   * @returns {object} - Filter result
   */ buildFilter(filterName, inquiryState) {
        const filter = this.filters[filterName];
        if (!filter) {
            throw new Error(`Filter '${filterName}' not found`);
        }
        return filter.buildFromState(inquiryState);
    }
    /**
   * Get list of available filters
   * @returns {string[]} - Array of filter names
   */ getAvailableFilters() {
        return Object.keys(this.filters);
    }
    /**
   * Check if a specific filter is enabled in the inquiry state
   * @param {string} filterName - Name of the filter
   * @param {object} inquiryState - Complete inquiry state
   * @returns {boolean} - Whether the filter is enabled
   */ isFilterEnabled(filterName, inquiryState) {
        const enabledFields = {
            department: "kddept",
            unit: "unit",
            dekon: "kddekon",
            satker: "kdsatker",
            provinsi: "kdlokasi",
            kabkota: "kdkabkota",
            kanwil: "kdkanwil",
            kppn: "kdkppn",
            fungsi: "kdfungsi",
            subfungsi: "kdsfungsi",
            program: "kdprogram",
            kegiatan: "kdgiat",
            output: "kdoutput",
            suboutput: "kdsoutput",
            akun: "kdakun",
            sdana: "kdsdana",
            register: "kdregister",
            pronas: "KdPN",
            propres: "KdPP",
            kegiatanprioritas: "KdKegPP",
            prioritas: "KdPRI",
            tema: "KdTema",
            megaproject: "KdMP",
            // Special filters
            inflasi: "kdInflasi",
            stunting: "KdStunting",
            kemiskinan: "kdKemiskinan",
            pemilu: "KdPemilu",
            ikn: "kdIkn",
            pangan: "KdPangan"
        };
        const enabledField = enabledFields[filterName];
        return enabledField ? Boolean(inquiryState[enabledField]) : false;
    }
    /**
   * Optimize JOIN clauses by removing duplicates
   * @param {string[]} joinClauses - Array of JOIN clauses
   * @returns {string[]} - Optimized JOIN clauses
   */ optimizeJoins(joinClauses) {
        // Remove duplicates and empty joins
        const uniqueJoins = [
            ...new Set(joinClauses)
        ].filter((join)=>join && join.trim() !== "");
        // Sort joins for consistent ordering
        return uniqueJoins.sort();
    }
    /**
   * Build role-based access control conditions
   * @param {object} inquiryState - Complete inquiry state
   * @returns {string} - Access control WHERE clause
   */ buildAccessControl(inquiryState) {
        const { role, kodekppn, kodekanwil } = inquiryState;
        if (role === "3" && kodekppn) {
            return `a.kdkppn = '${kodekppn}'`;
        } else if (role === "2" && kodekanwil) {
            return `a.kdkanwil = '${kodekanwil}'`;
        }
        return "";
    }
    /**
   * Build complete WHERE clause including access control
   * @param {object} inquiryState - Complete inquiry state
   * @returns {string} - Complete WHERE clause
   */ buildWhereClause(inquiryState) {
        const filterResult = this.buildAllFilters(inquiryState);
        const accessControl = this.buildAccessControl(inquiryState);
        const conditions = [
            ...filterResult.whereConditions
        ];
        if (accessControl) {
            conditions.push(accessControl);
        }
        if (conditions.length === 0) {
            return "";
        }
        return `WHERE ${conditions.join(" AND ")}`;
    }
    /**
   * Validate filter configuration
   * @param {object} inquiryState - Complete inquiry state
   * @returns {object} - Validation result
   */ validateFilters(inquiryState) {
        const errors = [];
        const warnings = [];
        // Check for conflicting filter combinations
        const enabledFilters = this.getAvailableFilters().filter((name)=>this.isFilterEnabled(name, inquiryState));
        // Performance warnings for too many filters
        if (enabledFilters.length > 10) {
            warnings.push(`High number of filters enabled (${enabledFilters.length}). Consider reducing for better performance.`);
        }
        // Check for missing required dependencies
        if (this.isFilterEnabled("unit", inquiryState) && !this.isFilterEnabled("department", inquiryState)) {
            warnings.push("Unit filter is enabled but Department filter is not. Consider enabling Department filter for better context.");
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            enabledFilters
        };
    }
    /**
   * Get filter statistics
   * @param {object} inquiryState - Complete inquiry state
   * @returns {object} - Filter statistics
   */ getFilterStats(inquiryState) {
        const filterResult = this.buildAllFilters(inquiryState);
        const validation = this.validateFilters(inquiryState);
        return {
            totalFilters: Object.keys(this.filters).length,
            enabledFilters: validation.enabledFilters.length,
            enabledFilterNames: validation.enabledFilters,
            columnsCount: filterResult.columns.length,
            joinsCount: filterResult.joinClauses.length,
            whereConditionsCount: filterResult.whereConditions.length,
            groupByCount: filterResult.groupBy.length,
            validation
        };
    }
    /**
   * Get filter switch value for debugging
   * @param {string} filterName - Name of the filter
   * @param {object} inquiryState - Complete inquiry state
   * @returns {any} - Filter switch value
   */ getFilterSwitchValue(filterName, inquiryState) {
        const enabledFields = {
            inflasi: "kdInflasi",
            stunting: "KdStunting",
            kemiskinan: "kdKemiskinan",
            pemilu: "KdPemilu",
            ikn: "kdIkn",
            pangan: "KdPangan"
        };
        const field = enabledFields[filterName];
        return field ? inquiryState[field] : undefined;
    }
    /**
   * Get filter radio value for debugging
   * @param {string} filterName - Name of the filter
   * @param {object} inquiryState - Complete inquiry state
   * @returns {any} - Filter radio value
   */ getFilterRadioValue(filterName, inquiryState) {
        const radioFields = {
            inflasi: "inflasiradio",
            stunting: "stuntingradio",
            kemiskinan: "kemiskinanradio",
            pemilu: "pemiluradio",
            ikn: "iknradio",
            pangan: "panganradio"
        };
        const field = radioFields[filterName];
        return field ? inquiryState[field] : undefined;
    }
    /**
   * Get filter option value for debugging
   * @param {string} filterName - Name of the filter
   * @param {object} inquiryState - Complete inquiry state
   * @returns {any} - Filter option value
   */ getFilterOptionValue(filterName, inquiryState) {
        const optionFields = {
            inflasi: "Inflasi",
            stunting: "Stunting",
            kemiskinan: "Miskin",
            pemilu: "Pemilu",
            ikn: "Ikn",
            pangan: "Pangan"
        };
        const field = optionFields[filterName];
        return field ? inquiryState[field] : undefined;
    }
}
const __TURBOPACK__default__export__ = FilterBuilder;
}),
"[project]/src/components/features/inquiry/shared/filters/QueryBuilder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Modular Query Builder
 * Replaces the monolithic useQueryBuilder with a clean, modular approach
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$FilterBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/FilterBuilder.js [app-ssr] (ecmascript)");
;
class QueryBuilder {
    constructor(){
        this.filterBuilder = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$FilterBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    }
    /**
   * Build dynamic FROM and SELECT clauses based on jenlap and other parameters
   * @param {object} params - Query parameters
   * @returns {object} - { dynamicFrom, dynamicSelect }
   */ buildDynamicFromAndSelect(params) {
        const { thang, jenlap, cutoff, tanggal, akumulatif, pembulatan } = params;
        // Base table names (from original logic)
        const fromapbn = `monev${thang}.pagu_real_detail_harian_dipa_apbn_${thang} a`;
        const fromBulanan = `monev${thang}.pagu_real_detail_bulan_${thang} a`;
        const fromcaput = thang >= "2021" ? `monev${thang}.pagu_output_${thang}_new a` : `monev${thang}.pagu_output_${thang}_new a`;
        const fromJnsblokir = `monev${thang}.pa_pagu_blokir_akun_${thang}_bulanan a`;
        // Generate cutoff-based realizations
        const validCutoff = parseInt(cutoff) >= 1 && parseInt(cutoff) <= 12 ? parseInt(cutoff) : 12;
        let realColumns = "";
        for(let i = 1; i <= validCutoff; i++){
            realColumns += `real${i}`;
            if (i !== validCutoff) realColumns += "+ ";
        }
        // Build SELECT clauses - use pagu_apbn for jenlap=1, pagu for others
        const paguField = jenlap === "1" ? "a.pagu_apbn" : "a.pagu";
        const pagu = `, ROUND(SUM(${paguField})/${pembulatan},0) AS PAGU`;
        const paguapbn = `, ROUND(SUM(${paguField})/${pembulatan},0) AS PAGU_APBN`;
        const pagudipa = `, ROUND(SUM(a.pagu_dipa)/${pembulatan},0) AS PAGU_DIPA`;
        const blokir = `, ROUND(SUM(a.blokir)/${pembulatan},0) AS BLOKIR`;
        const selectClause = `, ROUND(SUM(${realColumns})/${pembulatan},0) AS REALISASI`;
        const realapbn = `, ROUND(SUM(real1+real2+real3+real4+real5+real6+real7+real8+real9+real10+real11+real12)/${pembulatan},0) AS REALISASI`;
        // Monthly columns for jenlap=3
        const monthLabels = [
            "JAN",
            "FEB",
            "MAR",
            "APR",
            "MEI",
            "JUN",
            "JUL",
            "AGS",
            "SEP",
            "OKT",
            "NOV",
            "DES"
        ];
        let realbulanan = "";
        let realbulananakumulatif = "";
        let realBulanan = "";
        let blokirBulanan = "";
        for(let i = 1; i <= 12; i++){
            const monthName = monthLabels[i - 1];
            if (i <= validCutoff) {
                // Monthly realization
                realbulanan += `, ROUND(SUM(real${i})/${pembulatan},0) AS ${monthName}`;
                // Accumulated monthly
                let accumulatedRealCols = "";
                for(let j = 1; j <= i; j++){
                    accumulatedRealCols += `real${j}`;
                    if (j < i) accumulatedRealCols += "+";
                }
                realbulananakumulatif += `, ROUND(SUM(${accumulatedRealCols})/${pembulatan},0) AS ${monthName}`;
                // Pagu Bulanan
                realBulanan += `, ROUND(sum(pagu${i})/${pembulatan}, 0) AS ${monthName}`;
                // Blokir Bulanan
                blokirBulanan += `, ROUND(sum(blokir${i})/${pembulatan}, 0) AS ${monthName}`;
            } else {
                realbulanan += `, 0 AS ${monthName}`;
                realbulananakumulatif += `, 0 AS ${monthName}`;
                realBulanan += `, 0 AS ${monthName}`;
                blokirBulanan += `, 0 AS ${monthName}`;
            }
        }
        // Special cases
        const selectcaput = `, ROUND(SUM(${paguField})/${pembulatan},0) AS PAGU, ROUND(SUM(${realColumns})/${pembulatan},0) AS REALISASI`;
        const blokircaput = `, ROUND(SUM(a.blokir)/${pembulatan},0) AS BLOKIR`;
        const jnsblokirBulanan = `, a.kdblokir, ROUND(SUM(a.blokir)/${pembulatan},0) AS BLOKIR`;
        // jenlap = 6: Volume Output Kegiatan (PN) - Data Caput (uses pagu_output table)
        const jenlap6Select = `, a.sat as satuan, SUM(vol) AS vol, sum(${paguField}) as pagu, sum(real1) as rjan, sum(persen1) as pjan, sum(realfisik1) as rpjan, sum(real2) as rfeb, sum(persen2) as pfeb, sum(realfisik2) as rpfeb, sum(real3) as rmar, sum(persen3) as pmar, sum(realfisik3) as rpmar, sum(real4) as rapr, sum(persen4) as papr, sum(realfisik4) as rpapr, sum(real5) as rmei, sum(persen5) as pmei, sum(realfisik5) as rpmei, sum(real6) as rjun, sum(persen6) as pjun, sum(realfisik6) as rpjun, sum(real7) as rjul, sum(persen7) as pjul, sum(realfisik7) as rpjul, sum(real8) as rags, sum(persen8) as pags, sum(realfisik8) as rpags, sum(real9) as rsep, sum(persen9) as psep, sum(realfisik9) as rpsep, sum(real10) as rokt, sum(persen10) as pokt, sum(realfisik10) as rpokt, sum(real11) as rnov, sum(persen11) as pnov, sum(realfisik11) as rpnov, sum(real12) as rdes, sum(persen12) as pdes, sum(realfisik12) as rpdes, os, a.ket`;
        // jenlap = 7: Pergerakan Blokir Bulanan per Jenis (kdblokir, nmblokir + monthly blokir breakdown)
        const blokirBulananSelect = `, a.kdblokir, a.nmblokir${blokirBulanan}`;
        // Historical tables
        const monthNames = [
            "",
            "januari",
            "februari",
            "maret",
            "april",
            "mei",
            "juni",
            "juli",
            "agustus",
            "september",
            "oktober",
            "november",
            "desember"
        ];
        const historicalTable = `dbhistori.pagu_real_detail_harian_${monthNames[parseInt(cutoff)]}_${thang} a`;
        const currentTable = `monev${thang}.pagu_real_detail_harian_${thang} a`;
        // Main switch logic
        let dynamicFrom = "";
        let dynamicSelect = "";
        // Debug logging to check jenlap value
        console.log("🔍 QueryBuilder Debug:", {
            jenlap,
            jenlapType: typeof jenlap
        });
        switch(jenlap){
            case "1":
                console.log("📊 Using jenlap 1 (DIPA APBN)");
                dynamicFrom = fromapbn;
                dynamicSelect = paguapbn + pagudipa + realapbn + blokir;
                break;
            case "2":
                console.log("📊 Using jenlap 2 (Pagu Realisasi Blokir)");
                dynamicFrom = tanggal ? historicalTable : currentTable;
                dynamicSelect = pagu + selectClause + blokir;
                break;
            case "3":
                console.log("📊 Using jenlap 3 (Realisasi Bulanan)");
                dynamicFrom = tanggal ? historicalTable : currentTable;
                dynamicSelect = jenlap === "3" && akumulatif === "1" ? pagu + realbulananakumulatif + blokir : pagu + realbulanan + blokir;
                break;
            case "4":
                console.log("📊 Using jenlap 4 (Pagu Bulanan)");
                dynamicFrom = fromBulanan;
                dynamicSelect = realBulanan;
                break;
            case "5":
                console.log("📊 Using jenlap 5 (Blokir Bulanan)");
                dynamicFrom = fromBulanan;
                dynamicSelect = blokirBulanan;
                break;
            case "6":
                console.log("📊 Using jenlap 6 (Volume Output Kegiatan - Data Caput)");
                dynamicFrom = fromcaput;
                dynamicSelect = jenlap6Select;
                break;
            case "7":
                console.log("📊 Using jenlap 7 (Pergerakan Blokir Bulanan per Jenis)");
                dynamicFrom = fromJnsblokir;
                dynamicSelect = blokirBulananSelect;
                break;
            default:
                console.log("📊 Using default jenlap");
                dynamicFrom = currentTable;
                dynamicSelect = pagu + blokir;
                break;
        }
        return {
            dynamicFrom,
            dynamicSelect
        };
    }
    /**
   * Build complete SQL query from inquiry state
   * @param {object} inquiryState - Complete inquiry state
   * @returns {string} - Complete SQL query
   */ buildQuery(inquiryState) {
        // Build dynamic FROM and SELECT
        const { dynamicFrom, dynamicSelect } = this.buildDynamicFromAndSelect(inquiryState);
        // Build all filters
        const filterResult = this.filterBuilder.buildAllFilters(inquiryState);
        // Build WHERE clause with access control
        const whereClause = this.filterBuilder.buildWhereClause(inquiryState);
        // Build final SELECT clause
        let finalSelectClause = "";
        if (filterResult.columns.length > 0) {
            finalSelectClause = filterResult.columns.join(", ") + dynamicSelect;
        } else {
            finalSelectClause = dynamicSelect.substring(1); // Remove leading comma
        }
        // Build GROUP BY clause
        let groupByClause = "";
        const groupByFields = [
            ...filterResult.groupBy
        ];
        console.log("🔍 Initial groupByFields:", groupByFields);
        // Special handling for jenlap = 6: Add sat, os, ket to GROUP BY (Volume Output Kegiatan - Data Caput)
        if (inquiryState.jenlap === "6") {
            console.log("📊 Adding sat, os, ket to GROUP BY for jenlap 6");
            groupByFields.push("a.sat", "a.os", "a.ket");
        }
        // Special handling for jenlap = 7: Add kdblokir to GROUP BY for blokir breakdown (Pergerakan Blokir Bulanan per Jenis)
        if (inquiryState.jenlap === "7") {
            console.log("📊 Adding kdblokir to GROUP BY for jenlap 7");
            groupByFields.push("a.kdblokir");
        }
        console.log("🔍 Final groupByFields:", groupByFields);
        if (groupByFields.length > 0) {
            groupByClause = `GROUP BY ${groupByFields.join(", ")}`;
        }
        // Combine JOIN clauses
        const joinClause = filterResult.joinClauses.join("");
        // Build final query
        const finalQuery = `
      SELECT ${finalSelectClause}
      FROM ${dynamicFrom}${joinClause}
      ${whereClause}
      ${groupByClause}
    `.trim();
        return finalQuery;
    }
    /**
   * Validate query before execution
   * @param {string} query - SQL query to validate
   * @returns {object} - Validation result
   */ validateQuery(query) {
        const errors = [];
        const warnings = [];
        if (!query || query.trim() === "") {
            errors.push("Query is empty");
        }
        if (!query.includes("FROM")) {
            errors.push("Query missing FROM clause");
        }
        if (!query.includes("SELECT")) {
            errors.push("Query missing SELECT clause");
        }
        // Check for potentially dangerous patterns
        const dangerousPatterns = [
            /;\s*drop\s+table/i,
            /;\s*delete\s+from/i,
            /;\s*update\s+.*\s+set/i,
            /union\s+select/i
        ];
        dangerousPatterns.forEach((pattern)=>{
            if (pattern.test(query)) {
                errors.push("Potentially dangerous SQL pattern detected");
            }
        });
        // Performance warnings
        const joinCount = (query.match(/LEFT JOIN/gi) || []).length;
        if (joinCount > 10) {
            warnings.push(`High number of JOINs (${joinCount}). Query may be slow.`);
        }
        const whereConditions = (query.match(/AND|OR/gi) || []).length;
        if (whereConditions > 15) {
            warnings.push(`High number of WHERE conditions (${whereConditions}). Query may be slow.`);
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            stats: {
                queryLength: query.length,
                joinCount,
                whereConditions
            }
        };
    }
    /**
   * Get query performance metrics
   * @param {object} inquiryState - Complete inquiry state
   * @returns {object} - Performance metrics
   */ getQueryPerformanceMetrics(inquiryState) {
        const startTime = performance.now();
        const query = this.buildQuery(inquiryState);
        const endTime = performance.now();
        const validation = this.validateQuery(query);
        const filterStats = this.filterBuilder.getFilterStats(inquiryState);
        return {
            query,
            buildTime: endTime - startTime,
            validation,
            filterStats,
            recommendations: this.generatePerformanceRecommendations(validation, filterStats)
        };
    }
    /**
   * Generate performance recommendations
   * @param {object} validation - Query validation result
   * @param {object} filterStats - Filter statistics
   * @returns {string[]} - Array of recommendations
   */ generatePerformanceRecommendations(validation, filterStats) {
        const recommendations = [];
        if (filterStats.enabledFilters > 8) {
            recommendations.push("Consider reducing the number of active filters for better performance");
        }
        if (validation.stats.joinCount > 8) {
            recommendations.push("High number of table JOINs detected. Consider using indexed columns");
        }
        if (validation.stats.queryLength > 5000) {
            recommendations.push("Query is very long. Consider breaking it into smaller queries");
        }
        if (filterStats.whereConditionsCount > 12) {
            recommendations.push("Many WHERE conditions detected. Ensure proper indexing on filtered columns");
        }
        return recommendations;
    }
    /**
   * Generate SQL preview without building full query
   * @param {object} inquiryState - Complete inquiry state
   * @returns {object} - SQL preview components
   */ generateSqlPreview(inquiryState) {
        const { dynamicFrom, dynamicSelect } = this.buildDynamicFromAndSelect(inquiryState);
        const filterResult = this.filterBuilder.buildAllFilters(inquiryState);
        const whereClause = this.filterBuilder.buildWhereClause(inquiryState);
        return {
            fromClause: dynamicFrom,
            selectClause: dynamicSelect,
            columns: filterResult.columns,
            joinClauses: filterResult.joinClauses,
            whereClause: whereClause,
            groupBy: filterResult.groupBy,
            filterStats: this.filterBuilder.getFilterStats(inquiryState)
        };
    }
}
const __TURBOPACK__default__export__ = QueryBuilder;
}),
"[project]/src/components/features/inquiry/shared/filters/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

// Filter modules index
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/DepartmentFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/LocationFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/ProgramFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$AccountFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/AccountFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/PriorityFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/SpecialFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$FilterBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/FilterBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$QueryBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/QueryBuilder.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
}),
"[project]/src/components/features/inquiry/shared/filters/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$BaseFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/BaseFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$DepartmentFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/DepartmentFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$LocationFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/LocationFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$ProgramFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/ProgramFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$AccountFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/AccountFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$PriorityFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/PriorityFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$SpecialFilter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/SpecialFilter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$FilterBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/FilterBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$QueryBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/QueryBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/index.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/src/components/features/inquiry/shared/filters/QueryBuilder.js [app-ssr] (ecmascript) <export default as QueryBuilder>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "QueryBuilder": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$QueryBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$filters$2f$QueryBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/filters/QueryBuilder.js [app-ssr] (ecmascript)");
}),
"[project]/src/components/features/inquiry/shared/formInquiryMod.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/feedback/Omspan.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$data$2f$Context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/data/Context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$SqlPreviewModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/Modals/SqlPreviewModal.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$SaveQueryModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/Modals/SaveQueryModal.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$ExportModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/Modals/ExportModal.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$InquiryModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/Modals/InquiryModal.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSelector$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/FilterSelector.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$QueryButtons$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/QueryButtons.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$LaporanSelector$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/components/LaporanSelector.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$hooks$2f$useInquiryState$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/hooks/useInquiryState.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$hooks$2f$useQueryBuilderModular$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/hooks/useQueryBuilderModular.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$utils$2f$exportUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/inquiry/shared/utils/exportUtils.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const InquiryMod = ()=>{
    // Use modular inquiry state hook
    const inquiry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$hooks$2f$useInquiryState$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$data$2f$Context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
    const { statusLogin, token, axiosJWT } = context;
    // Use modular query builder hook
    const { buildQuery } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$hooks$2f$useQueryBuilderModular$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(inquiry);
    // Destructure all needed state and setters from inquiry
    const { role, telp, verified, loadingExcell, setloadingExcell, kodekppn, kodekanwil, settampilAI, showModal, setShowModal, showModalKedua, setShowModalKedua, showModalsql, setShowModalsql, showModalPDF, setShowModalPDF, showModalsimpan, setShowModalsimpan, jenlap, setJenlap, thang, setThang, tanggal, setTanggal, cutoff, setCutoff, pembulatan, setPembulatan, akumulatif, setAkumulatif, selectedFormat, setSelectedFormat, export2, setExport2, loadingStatus, setLoadingStatus, showFormatDropdown, setShowFormatDropdown, kddept, setKddept, unit, setUnit, kddekon, setKddekon, kdlokasi, setKdlokasi, kdkabkota, setKdkabkota, kdkanwil, setKdkanwil, kdkppn, setKdkppn, kdsatker, setKdsatker, kdfungsi, setKdfungsi, kdsfungsi, setKdsfungsi, kdprogram, setKdprogram, kdgiat, setKdgiat, kdoutput, setKdoutput, kdsoutput, setKdsoutput, kdkomponen, setKdkomponen, kdskomponen, setKdskomponen, kdakun, setKdakun, kdsdana, setKdsdana, kdregister, setKdregister, kdInflasi, setKdInflasi, kdIkn, setKdIkn, kdKemiskinan, setKdKemiskinan, KdPRI, setKdPRI, KdPangan, setKdPangan, KdPemilu, setKdPemilu, KdStunting, setKdStunting, KdTema, setKdTema, KdPN, setKdPN, KdPP, setKdPP, KdMP, setKdMP, KdKegPP, setKdKegPP, Pangan, setPangan, Pemilu, setPemilu, dept, setDept, deptkondisi, setDeptkondisi, katadept, setKatadept, kdunit, setKdunit, unitkondisi, setUnitkondisi, kataunit, setKataunit, dekon, setDekon, dekonkondisi, setDekonkondisi, katadekon, setKatadekon, prov, setProv, lokasikondisi, setLokasikondisi, katalokasi, setKatalokasi, kabkota, setKabkota, kabkotakondisi, setKabkotakondisi, katakabkota, setKatakabkota, kanwil, setKanwil, kanwilkondisi, setKanwilkondisi, katakanwil, setKatakanwil, kppn, setKppn, kppnkondisi, setKppnkondisi, katakppn, setKatakppn, satker, setSatker, satkerkondisi, setSatkerkondisi, katasatker, setKatasatker, fungsi, setFungsi, fungsikondisi, setFungsikondisi, katafungsi, setKatafungsi, sfungsi, setSfungsi, subfungsikondisi, setSubfungsikondisi, katasubfungsi, setKatasubfungsi, program, setProgram, programkondisi, setProgramkondisi, kataprogram, setKataprogram, giat, setGiat, giatkondisi, setGiatkondisi, katagiat, setKatagiat, output, setOutput, outputkondisi, setOutputkondisi, kataoutput, setKataoutput, soutput, setsOutput, soutputkondisi, setSoutputkondisi, katasoutput, setKatasoutput, komponen, setKomponen, komponenkondisi, setKomponenkondisi, katakomponen, setKatakomponen, skomponen, setSkomponen, skomponenkondisi, setSkomponenkondisi, kataskomponen, setKataskomponen, akun, setAkun, akunkondisi, setAkunkondisi, kataakun, setKataakun, sdana, setSdana, sdanakondisi, setSdanakondisi, katasdana, setKatasdana, register, setRegister, registerkondisi, setRegisterkondisi, kataregister, setKataregister, PN, setPN, PP, setPP, PRI, setPRI, MP, setMP, Tema, setTema, Inflasi, setInflasi, Stunting, setStunting, Miskin, setMiskin, Ikn, setIkn, deptradio, setDeptradio, unitradio, setUnitradio, dekonradio, setDekonradio, locradio, setLocradio, kabkotaradio, setKabkotaradio, kanwilradio, setKanwilradio, kppnradio, setKppnradio, satkerradio, setSatkerradio, fungsiradio, setFungsiradio, subfungsiradio, setSubfungsiradio, programradio, setProgramradio, kegiatanradio, setKegiatanradio, outputradio, setOutputradio, soutputradio, setsOutputradio, komponenradio, setKomponenradio, skomponenradio, setSkomponenradio, akunradio, setAkunradio, sdanaradio, setSdanaradio, registerradio, setRegisterradio, inflasiradio, setInflasiradio, iknradio, setIknradio, kemiskinanradio, setKemiskinanradio, pnradio, setPnradio, ppradio, setPpradio, mpradio, setMpradio, temaradio, setTemaradio, panganradio, setPanganradio, stuntingradio, setStuntingradio, pemiluradio, setPemiluradio, priradio, setPriradio, opsiInflasi, setOpsiInflasi, opsiIkn, setOpsiIkn, opsiKemiskinan, setOpsiKemiskinan, kegiatanprioritas, setKegiatanPrioritas, kegiatanprioritasradio, setKegiatanPrioritasRadio, sql, setSql, from, setFrom, select, setSelect, akunType, akunValue, akunSql } = inquiry;
    // Modal handlers
    const openModalKedua = ()=>{
        setShowModalKedua(true);
        settampilAI(true);
    };
    const closeModalKedua = ()=>{
        setShowModalKedua(false);
        settampilAI(false);
    };
    // Replace generateSql with buildQuery in all handlers
    const handleGenerateExcel = ()=>{
        buildQuery(); // Call the function to build the query
        setloadingExcell(true);
    };
    const handleDataFetchComplete = (total)=>{
        if (total > 0) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])(`${total} data berhasil diexport`);
        } else {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Tidak Ada Data");
        }
        setloadingExcell(false);
    };
    // **UNIFIED QUERY GENERATION** - All functions now use the same query builder
    const generateUnifiedQuery = ()=>{
        const sql = buildQuery(); // Use buildQuery() to get the complete SQL string
        return sql;
    };
    // **DEBUGGING HELPER** - Get current query for monitoring
    const getCurrentQuery = ()=>{
        return generateUnifiedQuery();
    };
    const handlegetQuery = async ()=>{
        const sql = generateUnifiedQuery();
        inquiry.setSql(sql);
        setShowModal(true); // Always open InquiryModal
    };
    // **UPDATED** - SQL preview handler now uses unified query generation
    const handlegetQuerySQL = ()=>{
        const latestSql = generateUnifiedQuery(); // Same query as execute
        inquiry.setSql(latestSql); // update global state
        setShowModalsql(true); // open modal
    };
    // Modal close handlers
    const closeModal = ()=>{
        setShowModal(false);
        setShowModalsimpan(false);
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
    };
    const closeModalsql = ()=>{
        setShowModalsql(false);
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
    };
    const closeModalsimpan = ()=>{
        setShowModalsimpan(false);
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
    };
    // Add useEffect for handling cutoff changes
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        // Update SQL or other dependent values when these parameters change
        const updateDependentValues = ()=>{
            // Call buildQuery to rebuild when parameters change
            buildQuery();
        };
        updateDependentValues();
    }, [
        thang,
        cutoff,
        pembulatan,
        akumulatif
    ]); // Remove buildQuery from dependencies to prevent infinite loops
    // Add ref to track if component has mounted (to avoid resetting on initial load)
    const hasMountedRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(false);
    // Add useEffect for handling fungsi-subfungsi parent-child relationship
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        // Skip the reset on initial mount
        if (!hasMountedRef.current) {
            hasMountedRef.current = true;
            return;
        }
        // Reset subfungsi to default when fungsi changes (after initial mount)
        // This ensures when user selects a different fungsi, subfungsi goes back to "Semua Sub Fungsi"
        // Also clear related advanced filtering states
        setSfungsi("XX");
        setSubfungsikondisi("");
        setKatasubfungsi("");
    }, [
        fungsi
    ]);
    // Handler to turn all switches on/off
    const handlePilihSemua = (isOn)=>{
        setTanggal(isOn);
        setKddept(isOn);
        setKddekon(isOn);
        setKdlokasi(isOn);
        setKdkabkota(isOn);
        setKdkanwil(isOn);
        setKdkppn(isOn);
        setKdsatker(isOn);
        setKdfungsi(isOn);
        setKdsfungsi(isOn);
        setKdprogram(isOn);
        setKdgiat(isOn);
        setKdoutput(isOn);
        setKdsoutput(isOn);
        setKdkomponen(isOn);
        setKdskomponen(isOn);
        setKdakun(isOn);
        setKdsdana(isOn);
        setKdregister(isOn);
        // Only set boolean switches, do not set 'unit' or other radio/select values
        setCutoff(isOn ? "12" : "0");
        setShowCutoffSelector(isOn);
    };
    // Handler to reset all filters and parameters to their initial state
    const handleReset = ()=>{
        setJenlap("2");
        setThang(new Date().getFullYear().toString());
        setTanggal(false);
        setKddept(true);
        setUnit(false);
        setKddekon(false);
        setKdlokasi(false);
        setKdkabkota(false);
        setKdkanwil(false);
        setKdkppn(false);
        setKdsatker(false);
        setKdfungsi(false);
        setKdsfungsi(false);
        setKdprogram(false);
        setKdgiat(false);
        setKdoutput(false);
        setKdsoutput(false);
        setKdkomponen(false);
        setKdskomponen(false);
        setKdakun(false);
        setKdsdana(false);
        setKdregister(false);
        setKdInflasi(false);
        setKdIkn(false);
        setKdKemiskinan(false);
        setKdPRI(false);
        setKdPangan(false);
        setKdPemilu(false);
        setKdStunting(false);
        setKdTema(false);
        setKdPN(false);
        setKdPP(false);
        setKdMP(false);
        setKdKegPP(false);
        setAkumulatif("0");
        setCutoff("0");
        setShowCutoffSelector(false);
        setPN("XX");
        setPP("XX");
        setPRI("XX");
        setMP("XX");
        setTema("XX");
        setInflasi("XX");
        setStunting("XX");
        setMiskin("XX");
        setPemilu("XX");
        setIkn("XX");
        setPangan("XX");
        setKegiatanPrioritas("XX");
        setDept("000");
        setKdunit("XX");
        setDekon("XX");
        setProv("XX");
        setKabkota("XX");
        setKabkotakondisi("");
        setKatakabkota("");
        setKanwil("XX");
        setKppn("XX");
        setKppnkondisi("");
        setKatakppn("");
        setSatker("XX");
        setSatkerkondisi("");
        setKatasatker("");
        setFungsi("XX");
        setFungsikondisi("");
        setKatafungsi("");
        setSfungsi("XX");
        setSubfungsikondisi("");
        setKatasubfungsi("");
        setProgram("XX");
        setGiat("XX");
        setOutput("XX");
        setsOutput("XX");
        setKomponen("XX");
        setSkomponen("XX");
        setAkun("XX");
        setSdana("XX");
        setRegister("XX");
        setPembulatan("1");
        setDeptradio("1");
        setUnitradio("1");
        setDekonradio("1");
        setLocradio("1");
        setKabkotaradio("1");
        setKanwilradio("1");
        setKppnradio("1");
        setSatkerradio("1");
        setFungsiradio("1");
        setSubfungsiradio("1");
        setProgramradio("1");
        setKegiatanradio("1");
        setOutputradio("1");
        setsOutputradio("1");
        setKomponenradio("1");
        setSkomponenradio("1");
        setAkunradio("1");
        setSdanaradio("1");
        setRegisterradio("1");
        setInflasiradio("1");
        setIknradio("1");
        setKemiskinanradio("1");
        setPnradio("1");
        setPpradio("1");
        setMpradio("1");
        setTemaradio("1");
        setPanganradio("1");
        setStuntingradio("1");
        setPemiluradio("1");
        setPriradio("1");
        setKegiatanPrioritasRadio("1");
        setOpsiInflasi("pilihInflasi");
        setOpsiIkn("pilihikn");
        setOpsiKemiskinan("pilihKemiskinan");
        setSql("");
        setFrom("");
        setSelect(", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR");
    };
    // Add MP state if not present
    const [opsiMP, setOpsiMP] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState("pilihmp");
    // Handler for MpRadio
    const handleRadioMP = (val)=>setMPradio(val);
    // Add handlePDF for Export PDF modal (matches old form)
    const handlePDF = ()=>{
        setShowModalPDF(true);
    };
    // Add state to control visibility of CutoffMonthSelector
    const [showCutoffSelector, setShowCutoffSelector] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(cutoff !== "0");
    // Add state for SaveQueryModal
    const [showSaveQueryModal, setShowSaveQueryModal] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    // Helper to fetch data from backend using current filters/query
    // **UPDATED** - Export data fetcher now uses unified query generation
    async function fetchExportData() {
        // Use the same query builder as execute and show SQL
        const sql = generateUnifiedQuery(); // Consistent with all other operations
        if (!sql || typeof sql !== "string" || sql.trim() === "") {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Query tidak valid, silakan cek filter dan parameter.");
            return [];
        }
        // If not logged in, return empty array
        if (!statusLogin) {
            return [];
        }
        try {
            // Use the same backend URL as in InquiryModal
            const backendUrl = "http://localhost:88";
            const response = await axiosJWT.post(`${backendUrl}/next/inquiry`, {
                sql,
                page: 1
            }, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            // If backend supports returning all data for export, use that.
            // Otherwise, you may need to adjust API/backend to support full export.
            if (response.data && Array.isArray(response.data.data)) {
                return response.data.data;
            }
            return [];
        } catch (e) {
            console.error("Export API error:", e);
            return [];
        }
    }
    // Robust Excel export handler (fetches fresh data)
    const handleExportExcel = async ()=>{
        setloadingExcell(true);
        try {
            const exportData = await fetchExportData();
            if (!exportData || !exportData.length) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Tidak ada data untuk diexport");
                setloadingExcell(false);
                return;
            }
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$utils$2f$exportUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportToExcel"])(exportData, "inquiry_data.xlsx");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Data berhasil diexport ke Excel");
        } catch (e) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Gagal export Excel");
        }
        setloadingExcell(false);
    };
    // Robust CSV export handler (fetches fresh data)
    const handleExportCSV = async ()=>{
        setloadingExcell(true);
        try {
            const exportData = await fetchExportData();
            if (!exportData || !exportData.length) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Tidak ada data untuk diexport");
                setloadingExcell(false);
                return;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$utils$2f$exportUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportToCSV"])(exportData, "inquiry_data.csv");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Data berhasil diexport ke CSV");
        } catch (e) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Gagal export CSV");
        }
        setloadingExcell(false);
    };
    // Robust JSON export handler (fetches fresh data)
    const handleExportJSON = async ()=>{
        setloadingExcell(true);
        try {
            const exportData = await fetchExportData();
            if (!exportData || !exportData.length) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Tidak ada data untuk diexport");
                setloadingExcell(false);
                return;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$utils$2f$exportUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportToJSON"])(exportData, "inquiry_data.json");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Data berhasil diexport ke JSON");
        } catch (e) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Gagal export JSON");
        }
        setloadingExcell(false);
    };
    // Robust Text export handler (fetches fresh data)
    const handleExportText = async ()=>{
        setloadingExcell(true);
        try {
            const exportData = await fetchExportData();
            if (!exportData || !exportData.length) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Tidak ada data untuk diexport");
                setloadingExcell(false);
                return;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$utils$2f$exportUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportToText"])(exportData, "inquiry_data.txt");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Data berhasil diexport ke Text");
        } catch (e) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$feedback$2f$Omspan$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Pesan"])("Gagal export Text");
        }
        setloadingExcell(false);
    };
    // Add useEffect for handling Akun filter changes
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        // Update SQL or other dependent values when Akun filter changes
        buildQuery();
    }, [
        akunType,
        akunValue,
        akunSql
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "xl:px-8 p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold mb-6",
                        children: "Inquiry Data Belanja"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                        lineNumber: 732,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$LaporanSelector$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: {
                            jenlap,
                            setJenlap,
                            pembulatan,
                            setPembulatan,
                            akumulatif,
                            setAkumulatif,
                            thang,
                            setThang
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                        lineNumber: 735,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$FilterSelector$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        inquiryState: {
                            // Report type for determining default switches
                            jenlap,
                            // Basic form states
                            tanggal,
                            setTanggal,
                            cutoff,
                            setCutoff,
                            showCutoffSelector,
                            setShowCutoffSelector,
                            akumulatif,
                            setAkumulatif,
                            // Filter visibility states
                            kddept,
                            setKddept,
                            unit,
                            setUnit,
                            kddekon,
                            setKddekon,
                            kdlokasi,
                            setKdlokasi,
                            kdkabkota,
                            setKdkabkota,
                            kdkanwil,
                            setKdkanwil,
                            kdkppn,
                            setKdkppn,
                            kdsatker,
                            setKdsatker,
                            kdfungsi,
                            setKdfungsi,
                            kdsfungsi,
                            setKdsfungsi,
                            kdprogram,
                            setKdprogram,
                            kdgiat,
                            setKdgiat,
                            kdoutput,
                            setKdoutput,
                            kdsoutput,
                            setKdsoutput,
                            kdkomponen,
                            setKdkomponen,
                            kdskomponen,
                            setKdskomponen,
                            kdakun,
                            setKdakun,
                            kdsdana,
                            setKdsdana,
                            kdregister,
                            setKdregister,
                            // Department filter values
                            dept,
                            setDept,
                            deptkondisi,
                            setDeptkondisi,
                            katadept,
                            setKatadept,
                            deptradio,
                            setDeptradio,
                            // Unit filter values
                            kdunit,
                            setKdunit,
                            unitkondisi,
                            setUnitkondisi,
                            kataunit,
                            setKataunit,
                            unitradio,
                            setUnitradio,
                            // Location filter values
                            prov,
                            setProv,
                            lokasikondisi,
                            setLokasikondisi,
                            katalokasi,
                            setKatalokasi,
                            locradio,
                            setLocradio,
                            // Dekon filter values
                            dekon,
                            setDekon,
                            dekonkondisi,
                            setDekonkondisi,
                            katadekon,
                            setKatadekon,
                            dekonradio,
                            setDekonradio,
                            // Kabkota filter values
                            kabkota,
                            setKabkota,
                            kabkotakondisi,
                            setKabkotakondisi,
                            katakabkota,
                            setKatakabkota,
                            kabkotaradio,
                            setKabkotaradio,
                            // Kanwil filter values
                            kanwil,
                            setKanwil,
                            kanwilkondisi,
                            setKanwilkondisi,
                            katakanwil,
                            setKatakanwil,
                            kanwilradio,
                            setKanwilradio,
                            // KPPN filter values
                            kppn,
                            setKppn,
                            kppnkondisi,
                            setKppnkondisi,
                            katakppn,
                            setKatakppn,
                            kppnradio,
                            setKppnradio,
                            // Satker filter values
                            satker,
                            setSatker,
                            satkerkondisi,
                            setSatkerkondisi,
                            katasatker,
                            setKatasatker,
                            satkerradio,
                            setSatkerradio,
                            // Fungsi filter values
                            fungsi,
                            setFungsi,
                            fungsikondisi,
                            setFungsikondisi,
                            katafungsi,
                            setKatafungsi,
                            fungsiradio,
                            setFungsiradio,
                            // Sub-fungsi filter values
                            sfungsi,
                            setSfungsi,
                            subfungsikondisi,
                            setSubfungsikondisi,
                            katasubfungsi,
                            setKatasubfungsi,
                            subfungsiradio,
                            setSubfungsiradio,
                            // Special states needed by FilterSelector
                            KdPRI,
                            setKdPRI,
                            KdPangan,
                            setKdPangan,
                            KdPemilu,
                            setKdPemilu,
                            KdStunting,
                            setKdStunting,
                            KdTema,
                            setKdTema,
                            KdPN,
                            setKdPN,
                            KdPP,
                            setKdPP,
                            KdMP,
                            setKdMP,
                            KdKegPP,
                            setKdKegPP,
                            // Kegiatan Prioritas filter values
                            kegiatanprioritas,
                            setKegiatanPrioritas,
                            kegiatanprioritasradio,
                            setKegiatanPrioritasRadio,
                            // Program filter values
                            program,
                            setProgram,
                            programkondisi,
                            setProgramkondisi,
                            kataprogram,
                            setKataprogram,
                            programradio,
                            setProgramradio,
                            // Kegiatan filter values
                            giat,
                            setGiat,
                            giatkondisi,
                            setGiatkondisi,
                            katagiat,
                            setKatagiat,
                            kegiatanradio,
                            setKegiatanradio,
                            // Output filter values
                            output,
                            setOutput,
                            outputkondisi,
                            setOutputkondisi,
                            kataoutput,
                            setKataoutput,
                            outputradio,
                            setOutputradio,
                            // Sub-output filter values
                            soutput,
                            setsOutput,
                            soutputkondisi,
                            setSoutputkondisi,
                            katasoutput,
                            setKatasoutput,
                            soutputradio,
                            setsOutputradio,
                            // Komponen filter values
                            komponen,
                            setKomponen,
                            komponenkondisi,
                            setKomponenkondisi,
                            katakomponen,
                            setKatakomponen,
                            komponenradio,
                            setKomponenradio,
                            // Subkomponen filter values
                            skomponen,
                            setSkomponen,
                            skomponenkondisi,
                            setSkomponenkondisi,
                            kataskomponen,
                            setKataskomponen,
                            skomponenradio,
                            setSkomponenradio,
                            // Akun filter values
                            akun,
                            setAkun,
                            akunkondisi,
                            setAkunkondisi,
                            kataakun,
                            setKataakun,
                            akunradio,
                            setAkunradio,
                            // Sumber Dana filter values
                            sdana,
                            setSdana,
                            sdanakondisi,
                            setSdanakondisi,
                            katasdana,
                            setKatasdana,
                            sdanaradio,
                            setSdanaradio,
                            // Register filter values
                            register,
                            setRegister,
                            registerkondisi,
                            setRegisterkondisi,
                            kataregister,
                            setKataregister,
                            registerradio,
                            setRegisterradio,
                            // New modularized filter states
                            kdInflasi,
                            setKdInflasi,
                            Inflasi,
                            setInflasi,
                            inflasiradio,
                            setInflasiradio,
                            opsiInflasi,
                            setOpsiInflasi,
                            kdIkn,
                            setKdIkn,
                            Ikn,
                            setIkn,
                            iknradio,
                            setIknradio,
                            opsiIkn,
                            setOpsiIkn,
                            kdKemiskinan,
                            setKdKemiskinan,
                            Miskin,
                            setMiskin,
                            kemiskinanradio,
                            setKemiskinanradio,
                            opsiKemiskinan,
                            setOpsiKemiskinan,
                            // Add new special filter states
                            Pangan,
                            setPangan,
                            panganradio,
                            setPanganradio,
                            Stunting,
                            setStunting,
                            stuntingradio,
                            setStuntingradio,
                            Pemilu,
                            setPemilu,
                            pemiluradio,
                            setPemiluradio,
                            PN,
                            setPN,
                            pnradio,
                            setPnradio,
                            PP,
                            setPP,
                            ppradio,
                            setPpradio,
                            MP,
                            setMP,
                            mpradio,
                            setMpradio,
                            Tema,
                            setTema,
                            temaradio,
                            setTemaradio,
                            PRI,
                            setPRI,
                            priradio,
                            setPriradio
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                        lineNumber: 750,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "my-3 sm:px-16",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col md:flex-row md:flex-wrap lg:flex-nowrap gap-2 border-2 dark:border-zinc-600 rounded-xl shadow-sm py-2 px-4 font-mono tracking-wide bg-zinc-100 dark:bg-black",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "font-semibold text-blue-600 ml-4",
                                            children: "Tahun Anggaran:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1063,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: thang
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1066,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                    lineNumber: 1062,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "font-semibold text-green-600 ml-4",
                                            children: "Jenis Laporan:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1069,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: jenlap === "1" ? "Pagu APBN" : jenlap === "2" ? "Pagu Realisasi" : jenlap === "3" ? "Pagu Realisasi Bulanan" : jenlap === "4" ? "Pergerakan Pagu Bulanan" : jenlap === "5" ? "Pergerakan Blokir Bulanan" : jenlap === "7" ? "Pergerakan Blokir Bulanan per Jenis" : "Volume Output Kegiatan (PN) - Data Caput"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1072,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                    lineNumber: 1068,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "font-semibold text-purple-600 ml-4",
                                            children: "Pembulatan:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1089,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: pembulatan === "1" ? "Rupiah" : pembulatan === "1000" ? "Ribuan" : pembulatan === "1000000" ? "Jutaan" : pembulatan === "1000000000" ? "Miliaran" : "Triliunan"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1092,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                    lineNumber: 1088,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "font-semibold text-orange-600 ml-4",
                                            children: "Filter Aktif:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1105,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "ml-2",
                                            children: [
                                                [
                                                    tanggal,
                                                    kddept,
                                                    unit,
                                                    kddekon,
                                                    kdlokasi,
                                                    kdkabkota,
                                                    kdkanwil,
                                                    kdkppn,
                                                    kdsatker,
                                                    kdfungsi,
                                                    kdsfungsi,
                                                    kdprogram,
                                                    kdgiat,
                                                    kdoutput,
                                                    kdsoutput,
                                                    kdkomponen,
                                                    kdskomponen,
                                                    kdakun,
                                                    kdsdana,
                                                    kdregister,
                                                    kdInflasi,
                                                    kdIkn,
                                                    kdKemiskinan,
                                                    KdPRI,
                                                    KdPangan,
                                                    KdPemilu,
                                                    KdStunting,
                                                    KdTema,
                                                    KdPN,
                                                    KdPP,
                                                    KdMP,
                                                    KdKegPP
                                                ].filter(Boolean).length,
                                                " ",
                                                "dari",
                                                " ",
                                                [
                                                    tanggal,
                                                    kddept,
                                                    unit,
                                                    kddekon,
                                                    kdlokasi,
                                                    kdkabkota,
                                                    kdkanwil,
                                                    kdkppn,
                                                    kdsatker,
                                                    kdfungsi,
                                                    kdsfungsi,
                                                    kdprogram,
                                                    kdgiat,
                                                    kdoutput,
                                                    kdsoutput,
                                                    kdkomponen,
                                                    kdskomponen,
                                                    kdakun,
                                                    kdsdana,
                                                    kdregister,
                                                    kdInflasi,
                                                    kdIkn,
                                                    kdKemiskinan,
                                                    KdPRI,
                                                    KdPangan,
                                                    KdPemilu,
                                                    KdStunting,
                                                    KdTema,
                                                    KdPN,
                                                    KdPP,
                                                    KdMP,
                                                    KdKegPP
                                                ].length
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                            lineNumber: 1108,
                                            columnNumber: 15
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                                    lineNumber: 1104,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                            lineNumber: 1061,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                        lineNumber: 1060,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$QueryButtons$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        onExecuteQuery: handlegetQuery,
                        onExportExcel: handleExportExcel,
                        onExportCSV: handleExportCSV,
                        onExportPDF: handlePDF,
                        onReset: handleReset,
                        isLoading: loadingExcell,
                        onSaveQuery: ()=>setShowSaveQueryModal(true),
                        onShowSQL: handlegetQuerySQL
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                        lineNumber: 1188,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                lineNumber: 731,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            showModalsql && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$SqlPreviewModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showModalsql,
                onClose: closeModalsql,
                query: sql
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                lineNumber: 1202,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            showModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$InquiryModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showModal,
                onClose: closeModal,
                sql: sql,
                from: from,
                thang: thang,
                pembulatan: pembulatan
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                lineNumber: 1210,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            showModalsimpan && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$SaveQueryModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showModalsimpan,
                onClose: closeModalsimpan,
                sql: sql
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                lineNumber: 1221,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            showModalPDF && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$ExportModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                showModalPDF: showModalPDF,
                setShowModalPDF: setShowModalPDF,
                selectedFormat: selectedFormat,
                setSelectedFormat: setSelectedFormat,
                fetchExportData: fetchExportData,
                filename: "inquiry_data",
                loading: loadingExcell
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                lineNumber: 1229,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            showSaveQueryModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$inquiry$2f$shared$2f$components$2f$Modals$2f$SaveQueryModal$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showSaveQueryModal,
                onClose: ()=>setShowSaveQueryModal(false),
                query: sql,
                thang: thang,
                queryType: "INQUIRY"
            }, void 0, false, {
                fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
                lineNumber: 1242,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/features/inquiry/shared/formInquiryMod.jsx",
        lineNumber: 730,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = InquiryMod;
}),

};

//# sourceMappingURL=src_components_features_inquiry_shared_a3c0c4e9._.js.map