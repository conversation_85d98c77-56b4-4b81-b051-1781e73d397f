{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/%28auth%29/layout.tsx"], "sourcesContent": ["import { AuthLayoutWrapper } from \"@/components/auth/authLayout\";\r\nimport \"@/styles/globals.css\";\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return <AuthLayoutWrapper>{children}</AuthLayoutWrapper>;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBAAO,8OAAC;kBAAmB;;;;;;AAC7B", "debugId": null}}]}