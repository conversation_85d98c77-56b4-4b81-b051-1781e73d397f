{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/charts/client-chart.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Chart from \"react-apexcharts\";\r\nimport React from \"react\";\r\n\r\n// This is a client-only wrapper for ApexCharts\r\nexport default function ClientChart(props: any) {\r\n  return <Chart {...props} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAMe,SAAS,YAAY,KAAU;IAC5C,qBAAO,6LAAC,4KAAA,CAAA,UAAK;QAAE,GAAG,KAAK;;;;;;AACzB;KAFwB", "debugId": null}}]}