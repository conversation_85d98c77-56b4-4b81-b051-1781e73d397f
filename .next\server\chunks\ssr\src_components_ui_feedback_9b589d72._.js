module.exports = {

"[project]/src/components/ui/feedback/Omspan.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "EPANOTIF": ()=>EPANOTIF,
    "NotifDisclaimer": ()=>NotifDisclaimer,
    "NotifPesan": ()=>NotifPesan,
    "Omspan": ()=>Omspan,
    "Pesan": ()=><PERSON><PERSON>,
    "Toast": ()=>Toast,
    "Tunda": ()=>Tunda
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sweetalert2/dist/sweetalert2.esm.all.js [app-ssr] (ecmascript)");
;
const showToast = (pesan)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].close(); // Menutup notifikasi yang sedang ditampilkan
    setTimeout(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fire({
            text: `${pesan} `,
            position: "top-end",
            showConfirmButton: false,
            toast: true,
            timer: 5000,
            background: "black",
            color: "#ffffff",
            showClass: {
                popup: "animate__animated "
            }
        });
    }, 500); // Menunggu 500ms sebelum menampilkan notifikasi baru
};
const NotifikasiToast = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fire({
        text: `${pesan} 👋`,
        position: "top-end",
        showConfirmButton: false,
        toast: true,
        timer: 3000,
        background: "#C16DFA",
        color: "#ffffff"
    });
};
const NotifikasiToastEPA = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fire({
        text: `${pesan} `,
        position: "top-start",
        showConfirmButton: false,
        toast: true,
        timer: 3000,
        background: "#17c3fa",
        color: "#ffffff"
    });
};
const Toast = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].mixin({
    toast: true,
    position: "top-start",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    customClass: {
        popup: "custom-toast-font custom-toast-primary-light"
    },
    didOpen: (toast)=>{
        toast.onmouseenter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].stopTimer;
        toast.onmouseleave = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].resumeTimer;
    }
});
const NotifikasiDisclaimer = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fire({
        text: `${pesan} `,
        position: "top-start",
        showConfirmButton: false,
        toast: true,
        timer: 5000,
        background: "#FF5733",
        color: "#ffffff",
        showCloseButton: true
    });
};
const UserLogin = async (pesan)=>{
    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fire({
        text: `${pesan} `,
        position: "bottom-start",
        showConfirmButton: false,
        toast: true,
        timer: 3000,
        background: "#FF5733",
        color: "#ffffff",
        showCloseButton: true,
        animation: false
    });
};
const Omspan = (username, message)=>{
    showToast(username, message);
};
const Tunda = (username)=>{
    showToast(username, "Tunda OMSPAN berhasil");
};
const Pesan = (pesan)=>{
    showToast(pesan);
};
const NotifPesan = (pesan)=>{
    NotifikasiToast(pesan);
};
const NotifDisclaimer = (pesan)=>{
    NotifikasiDisclaimer(pesan);
};
const EPANOTIF = (pesan)=>{
    NotifikasiToastEPA(pesan);
};
}),
"[project]/src/components/ui/feedback/toastError.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ToastError": ()=>ToastError,
    "handleHttpError": ()=>handleHttpError
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sweetalert2/dist/sweetalert2.esm.all.js [app-ssr] (ecmascript)");
;
// Fungsi untuk menampilkan pesan toast dengan SweetAlert2
const ToastError = (title, text, icon = "error")=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sweetalert2$2f$dist$2f$sweetalert2$2e$esm$2e$all$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].fire({
        title,
        text,
        icon,
        position: "top-end",
        toast: true,
        showConfirmButton: false,
        timer: 5000,
        showCloseButton: true,
        background: "red",
        color: "white",
        // color: "#716add",
        // customClass: {
        //   container: "toast-container",
        //   popup: "colored-toast",
        // },
        timerProgressBar: true
    });
};
// Fungsi untuk menampilkan pesan error berdasarkan kode status HTTP
const handleHttpError = (status, text)=>{
    switch(status){
        case 400:
            ToastError(`Kesalahan Permintaan, Permintaan tidak valid. (${text})`);
            break;
        case 401:
            ToastError(`Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (${text})`);
            break;
        case 403:
            ToastError(`Akses Ditolak, Akses ke sumber daya dilarang. (${text})`);
            break;
        case 404:
            ToastError(`Error Refresh Token. Silahkan Login Ulang... (${text})`);
            break;
        case 429:
            ToastError(`Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${text})`);
            break;
        case 422:
            ToastError(`Unprocessable Entity, Permintaan tidak dapat diolah. (${text})`);
            break;
        case 500:
            ToastError("Kesalahan Pada Query", text);
            break;
        case 503:
            ToastError(`Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${text})`);
            break;
        case 504:
            ToastError(`Waktu Habis, Permintaan waktu habis. (${text})`);
            break;
        case 505:
            ToastError(`Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${text})`);
            break;
        case 507:
            ToastError(`Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${text})`);
            break;
        case 511:
            ToastError(`Autentikasi Diperlukan, Autentikasi diperlukan. (${text})`);
            break;
        default:
            ToastError(`Kesalahan Server, ${text} `);
            break;
    }
};
;
}),

};

//# sourceMappingURL=src_components_ui_feedback_9b589d72._.js.map