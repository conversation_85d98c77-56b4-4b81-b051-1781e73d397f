module.exports = {

"[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_8949b27e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-ssr] (ecmascript)");
    });
});
}),

};