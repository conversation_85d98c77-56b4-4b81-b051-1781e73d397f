{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport const createAuthCookie = async (nama: string, nilai: string) => {\r\n  const cookieStore = await cookies();\r\n  const isProduction = process.env.NODE_ENV === \"production\";\r\n\r\n  cookieStore.set(\"userAuth\", \"myToken\", {\r\n    secure: isProduction,\r\n    httpOnly: false,\r\n    sameSite: \"lax\",\r\n  });\r\n  cookieStore.set(nama, nilai, {\r\n    secure: isProduction,\r\n    httpOnly: false,\r\n    sameSite: \"lax\",\r\n  });\r\n};\r\n\r\nexport const deleteAuthCookie = async () => {\r\n  const cookieStore = await cookies();\r\n  cookieStore.delete(\"userAuth\");\r\n  cookieStore.delete(\"token\");\r\n  cookieStore.delete(\"nextToken\");\r\n};\r\n\r\nexport const checkAuthStatus = async (): Promise<{\r\n  userAuth: boolean;\r\n  token: boolean;\r\n  accessToken: boolean;\r\n  nextToken: boolean;\r\n}> => {\r\n  const cookieStore = await cookies();\r\n\r\n  return {\r\n    userAuth: cookieStore.has(\"userAuth\"),\r\n    token: cookieStore.has(\"token\"),\r\n    accessToken: cookieStore.has(\"accessToken\"),\r\n    nextToken: cookieStore.has(\"nextToken\"),\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;AAEO,MAAM,mBAAmB,OAAO,MAAc;IACnD,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,eAAe,oDAAyB;IAE9C,YAAY,GAAG,CAAC,YAAY,WAAW;QACrC,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;IACA,YAAY,GAAG,CAAC,MAAM,OAAO;QAC3B,QAAQ;QACR,UAAU;QACV,UAAU;IACZ;AACF;AAEO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;IACnB,YAAY,MAAM,CAAC;IACnB,YAAY,MAAM,CAAC;AACrB;AAEO,MAAM,kBAAkB;IAM7B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAA<PERSON>,CAAA,UAAO,AAAD;IAEhC,OAAO;QACL,UAAU,YAAY,GAAG,CAAC;QAC1B,OAAO,YAAY,GAAG,CAAC;QACvB,aAAa,YAAY,GAAG,CAAC;QAC7B,WAAW,YAAY,GAAG,CAAC;IAC7B;AACF;;;IArCa;IAgBA;IAOA;;AAvBA,+OAAA;AAgBA,+OAAA;AAOA,+OAAA", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/.next-internal/server/app/%28dashboard%29/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {createAuthCookie as '7f793f15c78691ba6efad96ed648418e18bf59ab7d'} from 'ACTIONS_MODULE0'\nexport {deleteAuthCookie as '7fcb5cfcc0973f18fb0d6c7d4b808fcc5dca7cb9eb'} from 'ACTIONS_MODULE0'\nexport {checkAuthStatus as '7f4245e88c549c29f0200e45bbbfc5bebfb9249ba0'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/%28dashboard%29/page.tsx"], "sourcesContent": ["import type { NextPage } from \"next\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\nconst Home: NextPage = () => {\r\n  redirect(\"/dashboard\");\r\n};\r\n\r\nexport default Home;\r\n"], "names": [], "mappings": ";;;AACA;AAAA;;AAEA,MAAM,OAAiB;IACrB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;uCAEe", "debugId": null}}]}