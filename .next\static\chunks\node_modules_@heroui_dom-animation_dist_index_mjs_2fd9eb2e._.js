(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_66bd21ae._.js",
  "static/chunks/node_modules_@heroui_dom-animation_dist_index_mjs_872557a4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@heroui/dom-animation/dist/index.mjs [app-client] (ecmascript)");
    });
});
}),
}]);