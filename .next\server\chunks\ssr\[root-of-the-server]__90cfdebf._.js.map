{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/NotificationContext.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState } from \"react\";\r\n\r\nconst NotificationContext = createContext(undefined);\r\n\r\nexport const useNotifications = () => {\r\n  const context = useContext(NotificationContext);\r\n  if (context === undefined) {\r\n    throw new Error(\r\n      \"useNotifications must be used within a NotificationProvider\"\r\n    );\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const NotificationProvider = ({ children }) => {\r\n  const [notifications, setNotifications] = useState([\r\n    {\r\n      id: 1,\r\n      header: \"New Message\",\r\n      message: \"New message from <PERSON>\",\r\n      read: false,\r\n    },\r\n    {\r\n      id: 2,\r\n      header: \"Comment Activity\",\r\n      message: \"New comment on your post\",\r\n      read: false,\r\n    },\r\n    {\r\n      id: 3,\r\n      header: \"System Update\",\r\n      message: \"System update available\",\r\n      read: true,\r\n    },\r\n  ]);\r\n\r\n  const unreadCount = notifications.filter((n) => !n.read).length;\r\n\r\n  const markAsRead = (id) => {\r\n    setNotifications((prev) =>\r\n      prev.map((n) => (n.id === id ? { ...n, read: true } : n))\r\n    );\r\n  };\r\n\r\n  const addNotification = (notification) => {\r\n    const newId = Math.max(...notifications.map((n) => n.id), 0) + 1;\r\n    setNotifications((prev) => [{ ...notification, id: newId }, ...prev]);\r\n  };\r\n\r\n  return (\r\n    <NotificationContext.Provider\r\n      value={{\r\n        notifications,\r\n        unreadCount,\r\n        markAsRead,\r\n        addNotification,\r\n      }}\r\n    >\r\n      {children}\r\n    </NotificationContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAEnC,MAAM,mBAAmB;IAC9B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;AAEO,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD;YACE,IAAI;YACJ,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,SAAS;YACT,MAAM;QACR;KACD;IAED,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;IAE/D,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAC,OAChB,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,KAAK;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,IAAI;IAE1D;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE,GAAG,KAAK;QAC/D,iBAAiB,CAAC,OAAS;gBAAC;oBAAE,GAAG,YAAY;oBAAE,IAAI;gBAAM;mBAAM;aAAK;IACtE;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAC3B,OAAO;YACL;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/ToastContext.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useCallback } from \"react\";\r\n\r\nconst ToastContext = createContext(undefined);\r\n\r\nexport const useToast = () => {\r\n  const context = useContext(ToastContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useToast must be used within a ToastProvider\");\r\n  }\r\n  return context;\r\n};\r\n\r\nexport const ToastProvider = ({ children }) => {\r\n  const [toasts, setToasts] = useState([]);\r\n\r\n  const removeToast = useCallback((id) => {\r\n    setToasts((prev) => prev.filter((toast) => toast.id !== id));\r\n  }, []);\r\n\r\n  const showToast = useCallback(\r\n    (message, type, duration = 5000) => {\r\n      const id = Math.random().toString(36).substr(2, 9);\r\n      const newToast = { id, message, type, duration };\r\n\r\n      setToasts((prev) => [...prev, newToast]); // Auto remove after duration\r\n      if (duration > 0) {\r\n        setTimeout(() => {\r\n          removeToast(id);\r\n        }, duration);\r\n      }\r\n    },\r\n    [removeToast]\r\n  );\r\n\r\n  return (\r\n    <ToastContext.Provider\r\n      value={{\r\n        toasts,\r\n        showToast,\r\n        removeToast,\r\n      }}\r\n    >\r\n      {children}\r\n    </ToastContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AAE5B,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE;IACxC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;IAC1D,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1B,CAAC,SAAS,MAAM,WAAW,IAAI;QAC7B,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAW;YAAE;YAAI;YAAS;YAAM;QAAS;QAE/C,UAAU,CAAC,OAAS;mBAAI;gBAAM;aAAS,GAAG,6BAA6B;QACvE,IAAI,WAAW,GAAG;YAChB,WAAW;gBACT,YAAY;YACd,GAAG;QACL;IACF,GACA;QAAC;KAAY;IAGf,qBACE,8OAAC,aAAa,QAAQ;QACpB,OAAO;YACL;YACA;YACA;QACF;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/notif.jsx"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\nimport \"./notif.css\";\r\n\r\nconst Notifikasi = (message) => {\r\n  Swal.fire({\r\n    html: `<div className='text-danger mt-4'>${message}</div>`,\r\n    icon: \"error\", // Tambahkan ikon error\r\n    position: \"top\",\r\n    buttonsStyling: false,\r\n    customClass: {\r\n      popup: \"swal2-animation\",\r\n      container: \"swal2-animation\",\r\n      confirmButton: \"swal2-confirm \", // Gunakan kelas CSS kustom untuk tombol\r\n      icon: \"swal2-icon\", // Gunakan kelas CSS kustom untuk ikon\r\n    },\r\n    confirmButtonText: \"Tutup\",\r\n  });\r\n};\r\n\r\nexport default Notifikasi;\r\n"], "names": [], "mappings": ";;;AAAA;;;AAGA,MAAM,aAAa,CAAC;IAClB,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACR,MAAM,CAAC,kCAAkC,EAAE,QAAQ,MAAM,CAAC;QAC1D,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,aAAa;YACX,OAAO;YACP,WAAW;YACX,eAAe;YACf,MAAM;QACR;QACA,mBAAmB;IACrB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/auth/authHelper.ts"], "sourcesContent": ["/**\r\n * Helper functions untuk debugging dan pengel<PERSON>an autentikasi\r\n */\r\n\r\n// Fungsi untuk mengecek apakah sedang di halaman auth\r\nexport const isAuthPage = () => {\r\n  if (typeof window === \"undefined\") return false;\r\n  const currentPath = window.location.pathname;\r\n  return (\r\n    currentPath.includes(\"/login\") ||\r\n    currentPath.includes(\"/register\") ||\r\n    currentPath.includes(\"/auth\")\r\n  );\r\n};\r\n\r\n// Fungsi untuk membersihkan semua data autentikasi\r\nexport const clearAllAuthData = async () => {\r\n  if (typeof window !== \"undefined\") {\r\n    // Hapus dari localStorage\r\n    localStorage.removeItem(\"token\");\r\n    localStorage.removeItem(\"status\");\r\n\r\n    // Hapus dari sessionStorage jika ada\r\n    sessionStorage.removeItem(\"token\");\r\n    sessionStorage.removeItem(\"status\");\r\n  }\r\n\r\n  try {\r\n    // Import dinamis untuk menghindari error server-side\r\n    const { deleteAuthCookie } = await import(\"../../actions/auth.action\");\r\n    await deleteAuthCookie();\r\n  } catch (error) {\r\n    console.error(\"❌ Error clearing auth cookies:\", error);\r\n  }\r\n};\r\n\r\n// Fungsi untuk memeriksa status autentikasi saat ini\r\nexport const checkAuthStatus = () => {\r\n  if (typeof window === \"undefined\") {\r\n    return {\r\n      hasLocalStorageToken: false,\r\n      localStorage: null,\r\n      sessionStorage: null,\r\n    };\r\n  }\r\n\r\n  const localStorageToken = localStorage.getItem(\"token\");\r\n  const sessionStorageToken = sessionStorage.getItem(\"token\");\r\n  const localStorageStatus = localStorage.getItem(\"status\");\r\n\r\n  return {\r\n    hasLocalStorageToken: !!localStorageToken,\r\n    hasSessionStorageToken: !!sessionStorageToken,\r\n    localStorage: {\r\n      token: localStorageToken,\r\n      status: localStorageStatus,\r\n    },\r\n    sessionStorage: {\r\n      token: sessionStorageToken,\r\n    },\r\n  };\r\n};\r\n\r\n// Fungsi untuk force logout dengan debugging\r\nexport const forceLogout = async (reason = \"Manual logout\") => {\r\n  // Cek apakah sedang di halaman auth untuk menghindari loop\r\n  const currentPath =\r\n    typeof window !== \"undefined\" ? window.location.pathname : \"\";\r\n  const isAuthPage =\r\n    currentPath.includes(\"/login\") || currentPath.includes(\"/register\");\r\n\r\n  await clearAllAuthData();\r\n\r\n  // Redirect ke login hanya jika tidak sedang di halaman auth\r\n  if (typeof window !== \"undefined\" && !isAuthPage) {\r\n    window.location.href = \"/login\";\r\n  }\r\n};\r\n\r\n// Fungsi untuk memantau perubahan localStorage secara real-time\r\nexport const setupStorageWatcher = (onTokenRemoved) => {\r\n  if (typeof window === \"undefined\") return () => {};\r\n\r\n  let lastTokenValue = localStorage.getItem(\"token\");\r\n\r\n  const checkInterval = setInterval(() => {\r\n    const currentTokenValue = localStorage.getItem(\"token\");\r\n\r\n    if (lastTokenValue && !currentTokenValue) {\r\n      onTokenRemoved();\r\n    }\r\n\r\n    lastTokenValue = currentTokenValue;\r\n  }, 5000);\r\n\r\n  const handleStorageChange = (e) => {\r\n    if (e.key === \"token\" && !e.newValue && e.oldValue) {\r\n      onTokenRemoved();\r\n    }\r\n  };\r\n\r\n  window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n  // Return cleanup function\r\n  return () => {\r\n    clearInterval(checkInterval);\r\n    window.removeEventListener(\"storage\", handleStorageChange);\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,sDAAsD;;;;;;;;AAC/C,MAAM,aAAa;IACxB,wCAAmC,OAAO;;;IAC1C,MAAM;AAMR;AAGO,MAAM,mBAAmB;IAC9B;;IAUA,IAAI;QACF,qDAAqD;QACrD,MAAM,EAAE,gBAAgB,EAAE,GAAG;QAC7B,MAAM;IACR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF;AAGO,MAAM,kBAAkB;IAC7B,wCAAmC;QACjC,OAAO;YACL,sBAAsB;YACtB,cAAc;YACd,gBAAgB;QAClB;IACF;;;IAEA,MAAM;IACN,MAAM;IACN,MAAM;AAaR;AAGO,MAAM,cAAc,OAAO,SAAS,eAAe;IACxD,2DAA2D;IAC3D,MAAM,cACJ,sCAAgC,0BAA2B;IAC7D,MAAM,aACJ,YAAY,QAAQ,CAAC,aAAa,YAAY,QAAQ,CAAC;IAEzD,MAAM;IAEN,4DAA4D;IAC5D;;AAGF;AAGO,MAAM,sBAAsB,CAAC;IAClC,wCAAmC,OAAO,KAAO;;;IAEjD,IAAI;IAEJ,MAAM;IAUN,MAAM;AAaR", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/encryption/decrypt.ts"], "sourcesContent": ["import CryptoJS from \"crypto-js\";\r\nconst secretKey = \"mebe23\";\r\nexport const decryptData = (queryParams) => {\r\n  const decData = CryptoJS.enc.Base64.parse(queryParams).toString(\r\n    CryptoJS.enc.Utf8\r\n  );\r\n  const bytes = CryptoJS.AES.decrypt(decData, secretKey).toString(\r\n    CryptoJS.enc.Utf8\r\n  );\r\n  return bytes;\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY;AACX,MAAM,cAAc,CAAC;IAC1B,MAAM,UAAU,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,QAAQ,CAC7D,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,IAAI;IAEnB,MAAM,QAAQ,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,WAAW,QAAQ,CAC7D,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,IAAI;IAEnB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/utils/handleError.ts"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\n\r\n// Fungsi untuk menampilkan pesan toast dengan SweetAlert2\r\nconst ToastError = (title, text, icon = \"error\") => {\r\n  Swal.fire({\r\n    title,\r\n    text,\r\n    icon,\r\n    position: \"top-end\", // Menentukan posisi di atas sebelah kanan\r\n    toast: true,\r\n    showConfirmButton: false, // Tidak menampilkan tombol OK\r\n    timer: 5000,\r\n    showCloseButton: true,\r\n    background: \"red\",\r\n    color: \"white\",\r\n    // color: \"#716add\",\r\n    // customClass: {\r\n    //   container: \"toast-container\",\r\n    //   popup: \"colored-toast\",\r\n    // },\r\n    timerProgressBar: true,\r\n  });\r\n};\r\n\r\n// Fungsi untuk menampilkan pesan error berdasarkan kode status HTTP\r\nconst handleHttpError = (status, text) => {\r\n  switch (status) {\r\n    case 400:\r\n      ToastError(`<PERSON><PERSON>ahan <PERSON>, Permintaan tidak valid. (${text})`);\r\n      break;\r\n    case 401:\r\n      ToastError(\r\n        `Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (${text})`\r\n      );\r\n      break;\r\n    case 403:\r\n      ToastError(`<PERSON><PERSON><PERSON>, <PERSON>ks<PERSON> ke sumber daya dilarang. (${text})`);\r\n      break;\r\n    case 404:\r\n      ToastError(`Error Refresh Token. Silahkan Login Ulang... (${text})`);\r\n      break;\r\n    case 429:\r\n      ToastError(\r\n        `Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${text})`\r\n      );\r\n      break;\r\n    case 422:\r\n      ToastError(\r\n        `Unprocessable Entity, Permintaan tidak dapat diolah. (${text})`\r\n      );\r\n      break;\r\n    case 500:\r\n      ToastError(\"Kesalahan Pada Query\", text);\r\n      break;\r\n    case 503:\r\n      ToastError(\r\n        `Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${text})`\r\n      );\r\n      break;\r\n    case 504:\r\n      ToastError(`Waktu Habis, Permintaan waktu habis. (${text})`);\r\n      break;\r\n    case 505:\r\n      ToastError(\r\n        `Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${text})`\r\n      );\r\n      break;\r\n    case 507:\r\n      ToastError(\r\n        `Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${text})`\r\n      );\r\n      break;\r\n    case 511:\r\n      ToastError(`Autentikasi Diperlukan, Autentikasi diperlukan. (${text})`);\r\n      break;\r\n    default:\r\n      ToastError(`Kesalahan Server, ${text} `);\r\n      break;\r\n  }\r\n};\r\n\r\nexport { ToastError, handleHttpError };\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,0DAA0D;AAC1D,MAAM,aAAa,CAAC,OAAO,MAAM,OAAO,OAAO;IAC7C,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACR;QACA;QACA;QACA,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,OAAO;QACP,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,oBAAoB;QACpB,iBAAiB;QACjB,kCAAkC;QAClC,4BAA4B;QAC5B,KAAK;QACL,kBAAkB;IACpB;AACF;AAEA,oEAAoE;AACpE,MAAM,kBAAkB,CAAC,QAAQ;IAC/B,OAAQ;QACN,KAAK;YACH,WAAW,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACpE;QACF,KAAK;YACH,WACE,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YAEtE;QACF,KAAK;YACH,WAAW,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACpE;QACF,KAAK;YACH,WAAW,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACnE;QACF,KAAK;YACH,WACE,CAAC,kEAAkE,EAAE,KAAK,CAAC,CAAC;YAE9E;QACF,KAAK;YACH,WACE,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;YAElE;QACF,KAAK;YACH,WAAW,wBAAwB;YACnC;QACF,KAAK;YACH,WACE,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YAEtE;QACF,KAAK;YACH,WAAW,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC3D;QACF,KAAK;YACH,WACE,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAEnE;QACF,KAAK;YACH,WACE,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAEnE;QACF,KAAK;YACH,WAAW,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACtE;QACF;YACE,WAAW,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACvC;IACJ;AACF", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/stores/data/Context.tsx"], "sourcesContent": ["\"use client\";\r\nimport Notifikasi from \"@/components/ui/feedback/notif\";\r\nimport {\r\n  checkAuthStatus as debugAuthStatus,\r\n  isAuthPage,\r\n  setupStorageWatcher,\r\n} from \"@/lib/auth/authHelper\";\r\nimport { decryptData } from \"@/lib/encryption/decrypt\";\r\nimport { handleHttpError } from \"@/lib/utils/handleError\";\r\nimport axios from \"axios\";\r\nimport { jwtDecode } from \"jwt-decode\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { createContext, useEffect, useState } from \"react\";\r\nimport { io } from \"socket.io-client\";\r\nimport Swal from \"sweetalert2\";\r\n\r\nconst MyContext = createContext({\r\n  role: \"\",\r\n  telp: \"\",\r\n  verified: \"\",\r\n  name: \"\",\r\n  statusLogin: false,\r\n  token: \"\",\r\n  axiosJWT: null,\r\n  isInitializing: true,\r\n});\r\n\r\nexport const MyContextProvider = ({ children }) => {\r\n  const router = useRouter();\r\n  const [loggedinUsers, setLoggedinUsers] = useState([]);\r\n  const [loggedInUser2, setLoggedInUser2] = useState(null);\r\n  const [namelogin, setNamelogin] = useState(null);\r\n  const [name, setName] = useState(\"\");\r\n  const [session, setSession] = useState(\"\");\r\n  const [role, setRole] = useState(\"\");\r\n  const [nmrole, setNmrole] = useState(\"\");\r\n  const [active, setActive] = useState(\"\");\r\n  const [kdlokasi, setKdlokasi] = useState(\"\");\r\n  const [verified, setVerified] = useState(\"\");\r\n  const [kdkanwil, setKdkanwil] = useState(\"\");\r\n  const [deptlimit, setDeptlimit] = useState(\"\");\r\n  const [kdkppn, setKdkppn] = useState(\"\");\r\n  const [expire, setExpire] = useState(\"\");\r\n  const [token, setToken] = useState(\"\");\r\n  const [iduser, setIduser] = useState(\"\");\r\n  const [url, setUrl] = useState(\"\");\r\n  const [statusLogin, setstatusLogin] = useState(false);\r\n  const [username, setUsername] = useState(\"\");\r\n  const [mode, setMode] = useState(\"\");\r\n  const [tampil, setTampil] = useState(\"\");\r\n  const [tampilverify, setTampilverify] = useState(\"\");\r\n  const [status, setStatus] = useState(\"\");\r\n  const [persentase, setPersentase] = useState([]);\r\n  const [logoutLoading, setLogoutLoading] = useState(false);\r\n  const [stat, setStat] = useState(\"\");\r\n  const [processquery, setProccess] = useState(\"\");\r\n  const [errorprocessquery, seterrorProccess] = useState(\"\");\r\n  const [loadingExcell, setloadingExcell] = useState(false);\r\n  const [totNotif, settotNotif] = useState(0);\r\n  const [listNotif, setlistNotif] = useState([]);\r\n  const [visibilityStatuses, setVisibilityStatuses] = useState({});\r\n  const [offline, setOffline] = useState(false);\r\n  const [offlinest, setOfflinest] = useState(\"\");\r\n  const [telp, setTelp] = useState(\"\");\r\n  const [tampilAI, settampilAI] = useState(false);\r\n  const [loginDengan, setloginDengan] = useState(null);\r\n  const [dataEpa, setDataEpa] = useState({});\r\n  const [viewMode, setViewMode] = useState(\"sppg\");\r\n  const [isInitializing, setIsInitializing] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const checkTokenStatus = async () => {\r\n      if (typeof window === \"undefined\") {\r\n        return;\r\n      }\r\n\r\n      // Cek apakah sedang di halaman login/register untuk menghindari loop\r\n      const currentPath = window.location.pathname;\r\n      const isOnAuthPage = isAuthPage();\r\n\r\n      try {\r\n        const storedToken = localStorage.getItem(\"token\");\r\n\r\n        if (!storedToken) {\r\n          setstatusLogin(false);\r\n          await deleteAuthCookie(); // Hapus cookie auth\r\n          setIsInitializing(false);\r\n\r\n          // Hanya redirect jika tidak sedang di halaman auth\r\n          if (!isOnAuthPage) {\r\n            window.location.href = \"/v3/next/login\";\r\n          }\r\n          return;\r\n        }\r\n\r\n        const decoded = jwtDecode(decryptData(storedToken));\r\n        const currentTime = Date.now() / 1000;\r\n\r\n        if (decoded.exp > currentTime) {\r\n          setToken(storedToken);\r\n          setName(decoded.name);\r\n          setExpire(decoded.exp);\r\n          setstatusLogin(true);\r\n          setRole(decoded.role);\r\n          setKdkanwil(decoded.kdkanwil);\r\n          setKdkppn(decoded.kdkppn);\r\n          setKdlokasi(decoded.kdlokasi);\r\n          setActive(decoded.active);\r\n          setDeptlimit(decoded.dept_limit);\r\n          setNmrole(decoded.namarole);\r\n          setIduser(decoded.userId);\r\n          setUrl(decoded.url);\r\n          setUsername(decoded.username);\r\n          setMode(decoded.mode);\r\n          setTampil(decoded.tampil);\r\n          setTampilverify(decoded.tampilverify);\r\n          setSession(decoded.session);\r\n          setVerified(decoded.verified);\r\n          setTelp(decoded.telp);\r\n\r\n          // Sinkronisasi dengan cookie auth\r\n          await createAuthCookie(\"token\", storedToken);\r\n          setIsInitializing(false);\r\n        } else {\r\n          // Token expired, remove from localStorage and logout\r\n          if (typeof window !== \"undefined\") {\r\n            localStorage.removeItem(\"token\");\r\n          }\r\n          await deleteAuthCookie();\r\n          setstatusLogin(false);\r\n          setIsInitializing(false);\r\n\r\n          // Hanya redirect jika tidak sedang di halaman auth\r\n          if (!isOnAuthPage) {\r\n            window.location.href = \"v3/login2\";\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error in checkTokenStatus:\", error);\r\n\r\n        // Hanya hapus token jika ada masalah dengan token (bukan network error)\r\n        if (\r\n          error.name === \"InvalidTokenError\" ||\r\n          error.message.includes(\"decode\")\r\n        ) {\r\n          if (typeof window !== \"undefined\") {\r\n            localStorage.removeItem(\"token\");\r\n          }\r\n          await deleteAuthCookie();\r\n          setstatusLogin(false);\r\n          setIsInitializing(false);\r\n\r\n          // Hanya redirect jika tidak sedang di halaman auth\r\n          if (!isOnAuthPage) {\r\n            window.location.href = \"v3/next/login\";\r\n          }\r\n        } else {\r\n          // Untuk error lain (network, dll), jangan logout otomatis\r\n          console.warn(\"Non-critical error in token check:\", error);\r\n          setIsInitializing(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    checkTokenStatus();\r\n  }, []);\r\n\r\n  // Efek untuk memantau perubahan localStorage token\r\n  useEffect(() => {\r\n    if (typeof window === \"undefined\" || isInitializing) return;\r\n\r\n    // Setup watcher menggunakan helper function yang lebih robust\r\n    const cleanup = setupStorageWatcher(() => {\r\n      const isOnAuthPage = isAuthPage();\r\n\r\n      if (statusLogin && !isOnAuthPage) {\r\n        console.log(\"🚨 Token removal detected, executing logout...\");\r\n        logout();\r\n      }\r\n    });\r\n\r\n    return cleanup;\r\n  }, [statusLogin, isInitializing]);\r\n\r\n  const logout = async () => {\r\n    try {\r\n      debugAuthStatus(); // Debug current state\r\n\r\n      // Cek apakah sedang di halaman login untuk menghindari loop\r\n      const isOnAuthPage = isAuthPage();\r\n\r\n      setLogoutLoading(true);\r\n      setUsername(\"\");\r\n\r\n      // Hanya panggil backend logout jika ada endpoint\r\n      try {\r\n        await axios.delete(process.env.NEXT_PUBLIC_LOCAL_LOGOUT);\r\n      } catch (error) {\r\n        console.log(\r\n          \"Backend logout failed (might be expected):\",\r\n          error.message\r\n        );\r\n      }\r\n\r\n      setstatusLogin(false);\r\n      setOffline(false);\r\n      setLoggedinUsers([]);\r\n      setTampil(false);\r\n      setTampilverify(false);\r\n      setNamelogin(null);\r\n      setTelp(\"\");\r\n      setloginDengan(null);\r\n\r\n      if (typeof window !== \"undefined\") {\r\n        localStorage.removeItem(\"token\");\r\n        localStorage.removeItem(\"status\");\r\n      }\r\n\r\n      // Hapus cookie auth\r\n      await deleteAuthCookie();\r\n\r\n      // console.log(\"✅ Logout process completed successfully\");\r\n\r\n      if (stat === \"true\") {\r\n        Swal.fire({\r\n          position: \"top\",\r\n          icon: \"success\",\r\n          title: \"Logout Berhasil\",\r\n          timer: 2000,\r\n          showConfirmButton: false,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"❌ Logout process failed:\", error);\r\n      Swal.fire({\r\n        position: \"top\",\r\n        icon: \"error\",\r\n        title: \"Logout Gagal\",\r\n        timer: 2000,\r\n        showConfirmButton: false,\r\n      });\r\n    } finally {\r\n      setLogoutLoading(false);\r\n\r\n      // Hanya redirect jika tidak sedang di halaman auth\r\n      const isOnAuthPage = isAuthPage();\r\n\r\n      if (!isOnAuthPage) {\r\n        router.push(\"/login\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const axiosJWT = axios.create({\r\n    withCredentials: true,\r\n  });\r\n\r\n  axiosJWT.interceptors.request.use(\r\n    async (config) => {\r\n      const storedToken = localStorage.getItem(\"token\");\r\n\r\n      if (!storedToken) {\r\n        // No token available, redirect to login\r\n        const isOnAuthPage = isAuthPage();\r\n        if (!isOnAuthPage) {\r\n          logout();\r\n        }\r\n        return Promise.reject(new Error(\"No token available\"));\r\n      }\r\n\r\n      try {\r\n        // Decode token untuk cek expiry secara real-time\r\n        const decoded = jwtDecode(decryptData(storedToken));\r\n        const currentTime = Date.now() / 1000;\r\n\r\n        if (decoded.exp < currentTime) {\r\n          // Token expired, remove from localStorage and logout\r\n          if (typeof window !== \"undefined\") {\r\n            localStorage.removeItem(\"token\");\r\n          }\r\n          await deleteAuthCookie();\r\n\r\n          const isOnAuthPage = isAuthPage();\r\n          if (!isOnAuthPage) {\r\n            logout();\r\n          }\r\n          return Promise.reject(new Error(\"Token expired\"));\r\n        } else {\r\n          config.headers.Authorization = `Bearer ${storedToken}`;\r\n        }\r\n      } catch (error) {\r\n        // Token tidak valid, hapus dan logout\r\n        console.error(\"Invalid token in interceptor:\", error);\r\n        if (typeof window !== \"undefined\") {\r\n          localStorage.removeItem(\"token\");\r\n        }\r\n        await deleteAuthCookie();\r\n\r\n        const isOnAuthPage = isAuthPage();\r\n        if (!isOnAuthPage) {\r\n          logout();\r\n        }\r\n        return Promise.reject(new Error(\"Invalid token\"));\r\n      }\r\n\r\n      return config;\r\n    },\r\n    (error) => {\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (statusLogin && session === \"1\" && namelogin !== \"\") {\r\n      cekLogin();\r\n    }\r\n  }, [namelogin]);\r\n\r\n  useEffect(() => {\r\n    stat === \"false\" &&\r\n      Notifikasi(\"System melakukan logout otomatis terhadap akun anda.\");\r\n    stat === \"false\" && logout();\r\n  }, [stat]);\r\n\r\n  const cekLogin = async () => {\r\n    try {\r\n      const response = await axios.get(\r\n        `${process.env.NEXT_PUBLIC_LOCAL_CEKLOGIN}?username=${username}`\r\n      );\r\n\r\n      setStat(response.data);\r\n    } catch (error) {\r\n      handleHttpError(\"Terjadi Permasalahan Koneksi atau Server Backend \");\r\n      setstatusLogin(false);\r\n      setTampil(false);\r\n      setTampilverify(false);\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    if (statusLogin) {\r\n      const socket = io(process.env.NEXT_PUBLIC_LOCAL_SOCKET);\r\n      socket.on(\"nmuser\", (nmuser) => {\r\n        if (nmuser) {\r\n          const uppercaseNamelogin = nmuser.toUpperCase();\r\n          setLoggedinUsers((prevUsers) => [...prevUsers, uppercaseNamelogin]);\r\n          setNamelogin(uppercaseNamelogin);\r\n        } else {\r\n          setNamelogin(null);\r\n        }\r\n      });\r\n\r\n      socket.on(\"loginBy\", (loginBy) => {\r\n        if (loginBy) {\r\n          setloginDengan(loginBy);\r\n        }\r\n      });\r\n      return () => {\r\n        socket.off(\"userLoggedin\");\r\n        socket.off(\"nmuser\");\r\n        socket.off(\"loginBy\");\r\n      };\r\n    }\r\n  }, [statusLogin, loginDengan]);\r\n\r\n  useEffect(() => {\r\n    if (statusLogin) {\r\n      const socket = io(process.env.NEXT_PUBLIC_LOCAL_SOCKET, {\r\n        transports: [\"websocket\", \"polling\"],\r\n        timeout: 10000,\r\n        reconnection: true,\r\n        reconnectionDelay: 1000,\r\n        reconnectionAttempts: 5,\r\n      });\r\n      socket.on(\"running_querys\", (running_querys) => {\r\n        if (running_querys) {\r\n          const uppercaseProccess = running_querys.toLowerCase();\r\n          setProccess((prev) => [...prev, uppercaseProccess]);\r\n        } else {\r\n          setProccess([]);\r\n        }\r\n      });\r\n      socket.on(\"error_querys\", (error_querys) => {\r\n        if (error_querys) {\r\n          const uppercaseProccessError = error_querys.toLowerCase();\r\n          seterrorProccess((preverror) => [\r\n            ...preverror,\r\n            uppercaseProccessError,\r\n          ]);\r\n        } else {\r\n          seterrorProccess([]);\r\n        }\r\n      });\r\n\r\n      return () => {\r\n        socket.off(\"running_querys\");\r\n        socket.off(\"error_querys\");\r\n      };\r\n    }\r\n  }, [statusLogin]);\r\n\r\n  return (\r\n    <MyContext.Provider\r\n      value={{\r\n        processquery,\r\n        errorprocessquery,\r\n        setLoggedinUsers,\r\n        setNamelogin,\r\n        namelogin,\r\n        setLoggedInUser2,\r\n        loggedInUser2,\r\n        logout,\r\n        loggedinUsers,\r\n        setSession,\r\n        setExpire,\r\n        setToken,\r\n        setMode,\r\n        setTampil,\r\n        setTampilverify,\r\n        setStatus,\r\n        url,\r\n        setUrl,\r\n        statusLogin,\r\n        name,\r\n        setName,\r\n        setstatusLogin,\r\n        role,\r\n        kdkanwil,\r\n        kdkppn,\r\n        kdlokasi,\r\n        setRole,\r\n        setKdkanwil,\r\n        setKdkppn,\r\n        setKdlokasi,\r\n        setActive,\r\n        expire,\r\n        token,\r\n        axiosJWT,\r\n        active,\r\n        deptlimit,\r\n        setDeptlimit,\r\n        setNmrole,\r\n        nmrole,\r\n        setIduser,\r\n        iduser,\r\n        setUsername,\r\n        username,\r\n        mode,\r\n        status,\r\n        tampil,\r\n        tampilverify,\r\n        persentase,\r\n        setPersentase,\r\n        loadingExcell,\r\n        setloadingExcell,\r\n        setVerified,\r\n        verified,\r\n        totNotif,\r\n        settotNotif,\r\n        listNotif,\r\n        setlistNotif,\r\n        visibilityStatuses,\r\n        setVisibilityStatuses,\r\n        offline,\r\n        setOffline,\r\n        offlinest,\r\n        setOfflinest,\r\n        telp,\r\n        setTelp,\r\n        tampilAI,\r\n        settampilAI,\r\n        loginDengan,\r\n        setloginDengan,\r\n        dataEpa,\r\n        setDataEpa,\r\n        viewMode,\r\n        setViewMode,\r\n        isInitializing,\r\n      }}\r\n    >\r\n      {children}\r\n    </MyContext.Provider>\r\n  );\r\n};\r\n\r\nexport default MyContext;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAdA;;;;;;;;;;;;AAgBA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;IAC9B,MAAM;IACN,MAAM;IACN,UAAU;IACV,MAAM;IACN,aAAa;IACb,OAAO;IACP,UAAU;IACV,gBAAgB;AAClB;AAEO,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE;IAC5C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,wCAAmC;gBACjC;YACF;;;YAEA,qEAAqE;YACrE,MAAM;YACN,MAAM;QAoFR;QAEA;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAqD;;;QAErD,8DAA8D;QAC9D,MAAM;IAUR,GAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,SAAS;QACb,IAAI;YACF,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,KAAK,sBAAsB;YAEzC,4DAA4D;YAC5D,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD;YAE9B,iBAAiB;YACjB,YAAY;YAEZ,iDAAiD;YACjD,IAAI;gBACF,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CACT,8CACA,MAAM,OAAO;YAEjB;YAEA,eAAe;YACf,WAAW;YACX,iBAAiB,EAAE;YACnB,UAAU;YACV,gBAAgB;YAChB,aAAa;YACb,QAAQ;YACR,eAAe;YAEf;;YAKA,oBAAoB;YACpB,MAAM;YAEN,0DAA0D;YAE1D,IAAI,SAAS,QAAQ;gBACnB,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;oBACR,UAAU;oBACV,MAAM;oBACN,OAAO;oBACP,OAAO;oBACP,mBAAmB;gBACrB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;gBACR,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,mBAAmB;YACrB;QACF,SAAU;YACR,iBAAiB;YAEjB,mDAAmD;YACnD,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD;YAE9B,IAAI,CAAC,cAAc;gBACjB,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,MAAM,WAAW,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC5B,iBAAiB;IACnB;IAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,OAAO;QACL,MAAM,cAAc,aAAa,OAAO,CAAC;QAEzC,IAAI,CAAC,aAAa;YAChB,wCAAwC;YACxC,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD;YAC9B,IAAI,CAAC,cAAc;gBACjB;YACF;YACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;QAClC;QAEA,IAAI;YACF,iDAAiD;YACjD,MAAM,UAAU,CAAA,GAAA,sJAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD,EAAE;YACtC,MAAM,cAAc,KAAK,GAAG,KAAK;YAEjC,IAAI,QAAQ,GAAG,GAAG,aAAa;gBAC7B,qDAAqD;gBACrD;;gBAGA,MAAM;gBAEN,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD;gBAC9B,IAAI,CAAC,cAAc;oBACjB;gBACF;gBACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;YAClC,OAAO;gBACL,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;YACxD;QACF,EAAE,OAAO,OAAO;YACd,sCAAsC;YACtC,QAAQ,KAAK,CAAC,iCAAiC;YAC/C;;YAGA,MAAM;YAEN,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD;YAC9B,IAAI,CAAC,cAAc;gBACjB;YACF;YACA,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;QAClC;QAEA,OAAO;IACT,GACA,CAAC;QACC,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,YAAY,OAAO,cAAc,IAAI;YACtD;QACF;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WACP,CAAA,GAAA,6IAAA,CAAA,UAAU,AAAD,EAAE;QACb,SAAS,WAAW;IACtB,GAAG;QAAC;KAAK;IAET,MAAM,WAAW;QACf,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,6EAA0C,UAAU,EAAE,UAAU;YAGlE,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE;YAChB,eAAe;YACf,UAAU;YACV,gBAAgB;QAClB;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD;YAChB,OAAO,EAAE,CAAC,UAAU,CAAC;gBACnB,IAAI,QAAQ;oBACV,MAAM,qBAAqB,OAAO,WAAW;oBAC7C,iBAAiB,CAAC,YAAc;+BAAI;4BAAW;yBAAmB;oBAClE,aAAa;gBACf,OAAO;oBACL,aAAa;gBACf;YACF;YAEA,OAAO,EAAE,CAAC,WAAW,CAAC;gBACpB,IAAI,SAAS;oBACX,eAAe;gBACjB;YACF;YACA,OAAO;gBACL,OAAO,GAAG,CAAC;gBACX,OAAO,GAAG,CAAC;gBACX,OAAO,GAAG,CAAC;YACb;QACF;IACF,GAAG;QAAC;QAAa;KAAY;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,2DAAwC;gBACtD,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS;gBACT,cAAc;gBACd,mBAAmB;gBACnB,sBAAsB;YACxB;YACA,OAAO,EAAE,CAAC,kBAAkB,CAAC;gBAC3B,IAAI,gBAAgB;oBAClB,MAAM,oBAAoB,eAAe,WAAW;oBACpD,YAAY,CAAC,OAAS;+BAAI;4BAAM;yBAAkB;gBACpD,OAAO;oBACL,YAAY,EAAE;gBAChB;YACF;YACA,OAAO,EAAE,CAAC,gBAAgB,CAAC;gBACzB,IAAI,cAAc;oBAChB,MAAM,yBAAyB,aAAa,WAAW;oBACvD,iBAAiB,CAAC,YAAc;+BAC3B;4BACH;yBACD;gBACH,OAAO;oBACL,iBAAiB,EAAE;gBACrB;YACF;YAEA,OAAO;gBACL,OAAO,GAAG,CAAC;gBACX,OAAO,GAAG,CAAC;YACb;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC,UAAU,QAAQ;QACjB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/providers.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { HeroUIProvider } from \"@heroui/react\";\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\r\nimport { NotificationProvider } from \"@/components/ui/feedback/NotificationContext\";\r\nimport { ToastProvider } from \"@/components/ui/feedback/ToastContext\";\r\nimport { MyContextProvider } from \"@/stores/data/Context\";\r\nimport { ReactNode } from \"react\";\r\n\r\ninterface ProvidersProps {\r\n  children: ReactNode;\r\n  themeProps?: any;\r\n}\r\n\r\nexport function Providers({ children, themeProps }: ProvidersProps) {\r\n  return (\r\n    <MyContextProvider>\r\n      <HeroUIProvider>\r\n        <NextThemesProvider\r\n          defaultTheme=\"system\"\r\n          attribute=\"class\"\r\n          enableSystem={true}\r\n          enableColorScheme={true}\r\n          storageKey=\"sintesa-theme\"\r\n          {...themeProps}\r\n        >\r\n          <NotificationProvider>\r\n            <ToastProvider>{children}</ToastProvider>\r\n          </NotificationProvider>\r\n        </NextThemesProvider>\r\n      </HeroUIProvider>\r\n    </MyContextProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAcO,SAAS,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAkB;IAChE,qBACE,8OAAC,iIAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC,gKAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,gJAAA,CAAA,gBAAkB;gBACjB,cAAa;gBACb,WAAU;gBACV,cAAc;gBACd,mBAAmB;gBACnB,YAAW;gBACV,GAAG,UAAU;0BAEd,cAAA,8OAAC,2JAAA,CAAA,uBAAoB;8BACnB,cAAA,8OAAC,oJAAA,CAAA,gBAAa;kCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/ToastContainer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useToast } from \"@/components/ui/feedback/ToastContext\";\r\nimport { clsx } from \"clsx\";\r\nimport { AlertTriangle, CheckCircle, Info, X, XCircle } from \"lucide-react\";\r\n\r\nconst ToastComponent = ({ toast }) => {\r\n  const { removeToast } = useToast();\r\n\r\n  const getIcon = () => {\r\n    switch (toast.type) {\r\n      case \"success\":\r\n        return <CheckCircle className=\"w-5 h-5 text-green-600\" />;\r\n      case \"error\":\r\n        return <XCircle className=\"w-5 h-5 text-red-600\" />;\r\n      case \"warning\":\r\n        return <AlertTriangle className=\"w-5 h-5 text-yellow-600\" />;\r\n      case \"info\":\r\n        return <Info className=\"w-5 h-5 text-blue-600\" />;\r\n      default:\r\n        return <Info className=\"w-5 h-5 text-blue-600\" />;\r\n    }\r\n  };\r\n\r\n  const getBackgroundColor = () => {\r\n    switch (toast.type) {\r\n      case \"success\":\r\n        return \"bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800\";\r\n      case \"error\":\r\n        return \"bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800\";\r\n      case \"warning\":\r\n        return \"bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800\";\r\n      case \"info\":\r\n        return \"bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800\";\r\n      default:\r\n        return \"bg-gray-50 border-gray-200 dark:bg-gray-950 dark:border-gray-800\";\r\n    }\r\n  };\r\n\r\n  const getTextColor = () => {\r\n    switch (toast.type) {\r\n      case \"success\":\r\n        return \"text-green-800 dark:text-green-200\";\r\n      case \"error\":\r\n        return \"text-red-800 dark:text-red-200\";\r\n      case \"warning\":\r\n        return \"text-yellow-800 dark:text-yellow-200\";\r\n      case \"info\":\r\n        return \"text-blue-800 dark:text-blue-200\";\r\n      default:\r\n        return \"text-gray-800 dark:text-gray-200\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={clsx(\r\n        \"flex items-start gap-3 p-4 rounded-lg border shadow-lg max-w-md transition-all duration-300 ease-in-out transform\",\r\n        getBackgroundColor(),\r\n        \"animate-in slide-in-from-right-full\"\r\n      )}\r\n      role=\"alert\"\r\n    >\r\n      <div className=\"flex-shrink-0\">{getIcon()}</div>\r\n      <div className={clsx(\"flex-1 text-sm font-medium\", getTextColor())}>\r\n        {toast.message}\r\n      </div>\r\n      <button\r\n        onClick={() => removeToast(toast.id)}\r\n        className={clsx(\r\n          \"flex-shrink-0 p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors\",\r\n          getTextColor()\r\n        )}\r\n        aria-label=\"Close notification\"\r\n      >\r\n        <X className=\"w-4 h-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const ToastContainer = () => {\r\n  const { toasts } = useToast();\r\n\r\n  if (toasts.length === 0) return null;\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-50 space-y-2\">\r\n      {toasts.map((toast) => (\r\n        <ToastComponent key={toast.id} toast={toast} />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE;IAC/B,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD;IAE/B,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,qHACA,sBACA;QAEF,MAAK;;0BAEL,8OAAC;gBAAI,WAAU;0BAAiB;;;;;;0BAChC,8OAAC;gBAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE,8BAA8B;0BAChD,MAAM,OAAO;;;;;;0BAEhB,8OAAC;gBACC,SAAS,IAAM,YAAY,MAAM,EAAE;gBACnC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,2FACA;gBAEF,cAAW;0BAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;AAEO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD;IAE1B,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAChC,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gBAA8B,OAAO;eAAjB,MAAM,EAAE;;;;;;;;;;AAIrC", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/hooks/api/useBackendStatusSocket.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState, useRef } from \"react\";\nimport { io } from \"socket.io-client\";\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_LOCAL_SOCKET;\nconst FALLBACK_URL = \"http://*************:88\"; // Fallback to current network IP\nconst SOCKET_ENABLED = process.env.NEXT_PUBLIC_SOCKET_ENABLED !== \"false\"; // Allow disabling sockets\n\n// Global socket instance untuk memastikan hanya ada satu koneksi\nlet globalSocket = null;\nlet globalSocketListeners = new Set();\nlet reconnectAttempts = 0;\nconst MAX_RECONNECT_ATTEMPTS = 5;\nconst RECONNECT_DELAY = 3000;\n\nexport const useBackendStatusSocket = (options = {}) => {\n  const {\n    autoReconnect = true,\n    checkInterval = 30000, // 30 seconds\n    onStatusChange = null,\n    onOffline = null,\n    disabled = false, // Add disabled option\n  } = options;\n\n  const [isOnline, setIsOnline] = useState(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [lastCheck, setLastCheck] = useState(null);\n  const intervalRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const listenerId = useRef(Math.random().toString(36).substr(2, 9));\n\n  const connectSocket = () => {\n    // Skip socket connection if disabled\n    if (!SOCKET_ENABLED) {\n      console.log(\"🔇 Socket connections disabled via environment variable\");\n      setIsConnected(false);\n      setIsOnline(true); // Assume online when sockets are disabled\n      setLastCheck(new Date());\n      return;\n    }\n\n    // Jika sudah ada global socket yang connected, gunakan yang ada\n    if (globalSocket?.connected) {\n      setIsConnected(true);\n      setIsOnline(true);\n      setLastCheck(new Date());\n      registerListener();\n      return;\n    }\n\n    // Jika sedang mencoba reconnect dan sudah mencapai batas maksimum\n    if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {\n      console.warn(\n        \"❌ Max reconnection attempts reached, stopping auto-reconnect\"\n      );\n      setIsConnected(false);\n      setIsOnline(false);\n      return;\n    }\n\n    try {\n      // Disconnect socket lama jika ada\n      if (globalSocket) {\n        globalSocket.disconnect();\n      }\n\n      globalSocket = io(BACKEND_URL, {\n        transports: [\"websocket\", \"polling\"],\n        timeout: 10000, // Increased from 5000 to 10000ms\n        forceNew: true,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        maxReconnectionAttempts: 5,\n      });\n\n      globalSocket.on(\"connect\", () => {\n        console.log(\"✅ Backend socket connected\");\n        reconnectAttempts = 0; // Reset counter pada koneksi sukses\n\n        // Update semua listeners\n        globalSocketListeners.forEach((listener) => {\n          if (listener.setIsConnected) listener.setIsConnected(true);\n          if (listener.setIsOnline) listener.setIsOnline(true);\n          if (listener.setLastCheck) listener.setLastCheck(new Date());\n          if (listener.onStatusChange) listener.onStatusChange(true);\n        });\n      });\n\n      globalSocket.on(\"disconnect\", (reason) => {\n        console.log(\"❌ Backend socket disconnected:\", reason);\n\n        // Update semua listeners\n        globalSocketListeners.forEach((listener) => {\n          if (listener.setIsConnected) listener.setIsConnected(false);\n          if (listener.setIsOnline) listener.setIsOnline(false);\n          if (listener.setLastCheck) listener.setLastCheck(new Date());\n          if (listener.onStatusChange) listener.onStatusChange(false);\n        });\n\n        // Hanya trigger onOffline jika disconnect karena masalah backend, bukan client\n        if (\n          reason === \"io server disconnect\" ||\n          reason === \"transport close\" ||\n          reason === \"transport error\"\n        ) {\n          globalSocketListeners.forEach((listener) => {\n            if (listener.onOffline) listener.onOffline();\n          });\n        }\n\n        // Auto reconnect dengan backoff\n        if (autoReconnect && reason !== \"io client disconnect\") {\n          reconnectAttempts++;\n          const delay =\n            RECONNECT_DELAY * Math.pow(2, Math.min(reconnectAttempts - 1, 3)); // Exponential backoff\n          console.log(\n            `🔄 Attempting reconnect ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS} in ${delay}ms`\n          );\n\n          reconnectTimeoutRef.current = setTimeout(() => {\n            connectSocket();\n          }, delay);\n        }\n      });\n\n      globalSocket.on(\"connect_error\", (error) => {\n        console.error(\"Socket connection error:\", error);\n        reconnectAttempts++;\n\n        // Update semua listeners\n        globalSocketListeners.forEach((listener) => {\n          if (listener.setIsConnected) listener.setIsConnected(false);\n          if (listener.setIsOnline) listener.setIsOnline(false);\n          if (listener.setLastCheck) listener.setLastCheck(new Date());\n          if (listener.onStatusChange) listener.onStatusChange(false);\n        });\n\n        // Hanya trigger onOffline jika ini bukan error sementara\n        if (reconnectAttempts >= 3) {\n          globalSocketListeners.forEach((listener) => {\n            if (listener.onOffline) listener.onOffline();\n          });\n        }\n\n        // Auto reconnect dengan backoff\n        if (autoReconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {\n          const delay =\n            RECONNECT_DELAY * Math.pow(2, Math.min(reconnectAttempts - 1, 3));\n          reconnectTimeoutRef.current = setTimeout(() => {\n            connectSocket();\n          }, delay);\n        }\n      });\n\n      globalSocket.on(\"backend-status\", (data) => {\n        const status = data.status === \"OK\";\n        globalSocketListeners.forEach((listener) => {\n          if (listener.setIsOnline) listener.setIsOnline(status);\n          if (listener.setLastCheck) listener.setLastCheck(new Date());\n          if (listener.onStatusChange) listener.onStatusChange(status);\n        });\n      });\n\n      globalSocket.on(\"pong\", (data) => {\n        const status = data.status === \"OK\";\n        globalSocketListeners.forEach((listener) => {\n          if (listener.setIsOnline) listener.setIsOnline(status);\n          if (listener.setLastCheck) listener.setLastCheck(new Date());\n        });\n      });\n\n      registerListener();\n    } catch (error) {\n      console.error(\"Failed to create socket connection:\", error);\n      reconnectAttempts++;\n\n      globalSocketListeners.forEach((listener) => {\n        if (listener.setIsConnected) listener.setIsConnected(false);\n        if (listener.setIsOnline) listener.setIsOnline(false);\n        if (listener.onStatusChange) listener.onStatusChange(false);\n      });\n\n      // Hanya trigger onOffline setelah beberapa kali gagal\n      if (reconnectAttempts >= 3) {\n        globalSocketListeners.forEach((listener) => {\n          if (listener.onOffline) listener.onOffline();\n        });\n      }\n    }\n  };\n\n  const registerListener = () => {\n    const listener = {\n      id: listenerId.current,\n      setIsConnected,\n      setIsOnline,\n      setLastCheck,\n      onStatusChange,\n      onOffline,\n    };\n    globalSocketListeners.add(listener);\n  };\n\n  const unregisterListener = () => {\n    globalSocketListeners.forEach((listener) => {\n      if (listener.id === listenerId.current) {\n        globalSocketListeners.delete(listener);\n      }\n    });\n  };\n\n  const checkStatus = () => {\n    if (globalSocket?.connected) {\n      globalSocket.emit(\"ping\");\n    } else {\n      // Jangan langsung trigger offline, coba reconnect dulu\n      if (autoReconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {\n        console.log(\"🔄 Socket not connected, attempting to reconnect...\");\n        connectSocket();\n      } else {\n        // Hanya set offline jika sudah tidak bisa reconnect\n        setIsOnline(false);\n        setIsConnected(false);\n        setLastCheck(new Date());\n\n        if (onStatusChange) {\n          onStatusChange(false);\n        }\n\n        // Hanya trigger onOffline setelah semua usaha reconnect gagal\n        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS && onOffline) {\n          onOffline();\n        }\n      }\n    }\n  };\n\n  const disconnect = () => {\n    // Cleanup interval dan timeout\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n\n    // Unregister listener dari global socket\n    unregisterListener();\n\n    // Jangan disconnect global socket kecuali tidak ada listener lain\n    if (globalSocketListeners.size === 0 && globalSocket) {\n      console.log(\"🔌 No more listeners, disconnecting global socket\");\n      globalSocket.disconnect();\n      globalSocket = null;\n      reconnectAttempts = 0;\n    }\n\n    setIsConnected(false);\n    setIsOnline(null);\n  };\n\n  useEffect(() => {\n    // Skip if disabled\n    if (disabled) {\n      return;\n    }\n\n    // Initial connection dan register listener\n    connectSocket();\n\n    // Set up periodic status check\n    if (checkInterval > 0) {\n      intervalRef.current = setInterval(checkStatus, checkInterval);\n    }\n\n    // Cleanup hanya unregister listener, tidak disconnect global socket\n    return () => {\n      // Cleanup interval dan timeout\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n\n      // Hanya unregister listener, biarkan socket tetap hidup untuk komponen lain\n      unregisterListener();\n    };\n  }, [disabled]); // Add disabled to dependency array\n\n  return {\n    isOnline,\n    isConnected,\n    lastCheck,\n    checkStatus,\n    disconnect,\n    reconnect: () => {\n      reconnectAttempts = 0; // Reset attempts\n      connectSocket();\n    },\n    reconnectAttempts,\n    maxReconnectAttempts: MAX_RECONNECT_ATTEMPTS,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKA,MAAM;AACN,MAAM,eAAe,2BAA2B,iCAAiC;AACjF,MAAM,iBAAiB,8CAA2C,SAAS,0BAA0B;AAErG,iEAAiE;AACjE,IAAI,eAAe;AACnB,IAAI,wBAAwB,IAAI;AAChC,IAAI,oBAAoB;AACxB,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB;AAEjB,MAAM,yBAAyB,CAAC,UAAU,CAAC,CAAC;IACjD,MAAM,EACJ,gBAAgB,IAAI,EACpB,gBAAgB,KAAK,EACrB,iBAAiB,IAAI,EACrB,YAAY,IAAI,EAChB,WAAW,KAAK,EACjB,GAAG;IAEJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAE/D,MAAM,gBAAgB;QACpB,qCAAqC;QACrC,wCAAqB;YACnB,QAAQ,GAAG,CAAC;YACZ,eAAe;YACf,YAAY,OAAO,0CAA0C;YAC7D,aAAa,IAAI;YACjB;QACF;;;IAuJF;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW;YACf,IAAI,WAAW,OAAO;YACtB;YACA;YACA;YACA;YACA;QACF;QACA,sBAAsB,GAAG,CAAC;IAC5B;IAEA,MAAM,qBAAqB;QACzB,sBAAsB,OAAO,CAAC,CAAC;YAC7B,IAAI,SAAS,EAAE,KAAK,WAAW,OAAO,EAAE;gBACtC,sBAAsB,MAAM,CAAC;YAC/B;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,cAAc,WAAW;YAC3B,aAAa,IAAI,CAAC;QACpB,OAAO;YACL,uDAAuD;YACvD,IAAI,iBAAiB,oBAAoB,wBAAwB;gBAC/D,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,oDAAoD;gBACpD,YAAY;gBACZ,eAAe;gBACf,aAAa,IAAI;gBAEjB,IAAI,gBAAgB;oBAClB,eAAe;gBACjB;gBAEA,8DAA8D;gBAC9D,IAAI,qBAAqB,0BAA0B,WAAW;oBAC5D;gBACF;YACF;QACF;IACF;IAEA,MAAM,aAAa;QACjB,+BAA+B;QAC/B,IAAI,YAAY,OAAO,EAAE;YACvB,cAAc,YAAY,OAAO;QACnC;QAEA,IAAI,oBAAoB,OAAO,EAAE;YAC/B,aAAa,oBAAoB,OAAO;QAC1C;QAEA,yCAAyC;QACzC;QAEA,kEAAkE;QAClE,IAAI,sBAAsB,IAAI,KAAK,KAAK,cAAc;YACpD,QAAQ,GAAG,CAAC;YACZ,aAAa,UAAU;YACvB,eAAe;YACf,oBAAoB;QACtB;QAEA,eAAe;QACf,YAAY;IACd;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB,IAAI,UAAU;YACZ;QACF;QAEA,2CAA2C;QAC3C;QAEA,+BAA+B;QAC/B,IAAI,gBAAgB,GAAG;YACrB,YAAY,OAAO,GAAG,YAAY,aAAa;QACjD;QAEA,oEAAoE;QACpE,OAAO;YACL,+BAA+B;YAC/B,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;YACnC;YAEA,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,aAAa,oBAAoB,OAAO;YAC1C;YAEA,4EAA4E;YAC5E;QACF;IACF,GAAG;QAAC;KAAS,GAAG,mCAAmC;IAEnD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,WAAW;YACT,oBAAoB,GAAG,iBAAiB;YACxC;QACF;QACA;QACA,sBAAsB;IACxB;AACF", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/dashboard/BackendStatusMonitor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useContext } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useBackendStatusSocket } from \"@/hooks/api/useBackendStatusSocket\";\r\nimport MyContext from \"@/stores/data/Context\";\r\n\r\nconst BackendStatusMonitor = ({ children }) => {\r\n  const context = useContext(MyContext);\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  if (!context) return children;\r\n\r\n  // Don't run monitoring on offline page to avoid conflicts\r\n  const isOfflinePage = pathname === \"/offline\";\r\n\r\n  // Safely access setOffline\r\n  const setOffline = context?.setOffline;\r\n\r\n  const { isOnline, isConnected } = useBackendStatusSocket({\r\n    autoReconnect: true,\r\n    checkInterval: 30000, // Check every 30 seconds\r\n    disabled: isOfflinePage, // Disable on offline page\r\n    onStatusChange: (status) => {\r\n      console.log(\"Backend status changed:\", status);\r\n    },\r\n    onOffline: () => {\r\n      if (!isOfflinePage) {\r\n        console.log(\"Backend is offline, redirecting...\");\r\n        if (setOffline) {\r\n          setOffline(true);\r\n        }\r\n        router.push(\"/offline\");\r\n      }\r\n    },\r\n  });\r\n\r\n  // Always render children, even when offline\r\n  // Let the routing handle the offline page\r\n  return children;\r\n};\r\n\r\nexport default BackendStatusMonitor;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAOA,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAAE;IACxC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iIAAA,CAAA,UAAS;IACpC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,CAAC,SAAS,OAAO;IAErB,0DAA0D;IAC1D,MAAM,gBAAgB,aAAa;IAEnC,2BAA2B;IAC3B,MAAM,aAAa,SAAS;IAE5B,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD,EAAE;QACvD,eAAe;QACf,eAAe;QACf,UAAU;QACV,gBAAgB,CAAC;YACf,QAAQ,GAAG,CAAC,2BAA2B;QACzC;QACA,WAAW;YACT,IAAI,CAAC,eAAe;gBAClB,QAAQ,GAAG,CAAC;gBACZ,IAAI,YAAY;oBACd,WAAW;gBACb;gBACA,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,4CAA4C;IAC5C,0CAA0C;IAC1C,OAAO;AACT;uCAEe", "debugId": null}}]}