(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/charts/client-chart.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_8738571e._.js",
  "static/chunks/src_components_ui_charts_client-chart_tsx_41f658ca._.js",
  "static/chunks/src_components_ui_charts_client-chart_tsx_d825a548._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/ui/charts/client-chart.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);