{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/style.css"], "sourcesContent": [".jenis-laporan-option input[type=\"radio\"] {\r\n  -webkit-appearance: none;\r\n     -moz-appearance: none;\r\n          appearance: none;\r\n  width: 15px;\r\n  height: 15px;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n  margin: 0;\r\n  cursor: pointer;\r\n  outline: none;\r\n}\r\n\r\n/* Checked radio button style */\r\n.jenis-laporan-option input[type=\"radio\"]:checked {\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* Disabled radio button style */\r\n.jenis-laporan-option input[type=\"radio\"]:disabled {\r\n  /* opacity: 0.6; */\r\n  cursor: not-allowed;\r\n  background-color: rgb(235, 6, 6);\r\n  border: 2px solid #ffffff;\r\n}\r\n\r\n.jenis-laporan-option-tematik input[type=\"radio\"] {\r\n  -webkit-appearance: none;\r\n     -moz-appearance: none;\r\n          appearance: none;\r\n  width: 15px;\r\n  height: 15px;\r\n  border: 2px solid #ffffff;\r\n  border-radius: 50%;\r\n  margin: 0;\r\n  cursor: pointer;\r\n  outline: none;\r\n}\r\n\r\n/* Checked radio button style */\r\n.jenis-laporan-option-tematik input[type=\"radio\"]:checked {\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* Disabled radio button style */\r\n.jenis-laporan-option-tematik input[type=\"radio\"]:disabled {\r\n  /* opacity: 0.6; */\r\n  cursor: not-allowed;\r\n  background-color: rgb(235, 6, 6);\r\n  border: 2px solid #ffffff;\r\n}\r\n.jenis-laporan-option p {\r\n  color: #ffffff;\r\n}\r\n.jenis-laporan-option-tematik p {\r\n  color: #ffffff;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAcA;;;;AAKA;;;;;;AAOA;;;;;;;;;;;;;AAcA;;;;AAKA;;;;;;AAMA", "debugId": null}}]}