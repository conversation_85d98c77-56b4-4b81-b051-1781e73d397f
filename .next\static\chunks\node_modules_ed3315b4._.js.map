{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/tailwind-variants/dist/chunk-LK3VBVBE.js"], "sourcesContent": ["var u=e=>e===false?\"false\":e===true?\"true\":e===0?\"0\":e,a=e=>{if(!e||typeof e!=\"object\")return  true;for(let r in e)return  false;return  true},y=(e,r)=>{if(e===r)return  true;if(!e||!r)return  false;let n=Object.keys(e),t=Object.keys(r);if(n.length!==t.length)return  false;for(let s=0;s<n.length;s++){let f=n[s];if(!t.includes(f)||e[f]!==r[f])return  false}return  true},p=e=>e===true||e===false;function o(e,r){for(let n=0;n<e.length;n++){let t=e[n];Array.isArray(t)?o(t,r):r.push(t);}}function g(e){let r=[];return o(e,r),r}var i=(...e)=>{let r=[];o(e,r);let n=[];for(let t=0;t<r.length;t++)r[t]&&n.push(r[t]);return n},c=(e,r)=>{let n={};for(let t in e){let s=e[t];if(t in r){let f=r[t];Array.isArray(s)||Array.isArray(f)?n[t]=i(f,s):typeof s==\"object\"&&typeof f==\"object\"&&s&&f?n[t]=c(s,f):n[t]=f+\" \"+s;}else n[t]=s;}for(let t in r)t in e||(n[t]=r[t]);return n},l=/\\s+/g,x=e=>!e||typeof e!=\"string\"?e:e.replace(l,\" \").trim();export{u as a,a as b,y as c,p as d,g as e,i as f,c as g,x as h};"], "names": [], "mappings": ";;;;;;;;;;AAAA,IAAI,IAAE,CAAA,IAAG,MAAI,QAAM,UAAQ,MAAI,OAAK,SAAO,MAAI,IAAE,MAAI,GAAE,IAAE,CAAA;IAAI,IAAG,CAAC,KAAG,OAAO,KAAG,UAAS,OAAQ;IAAK,IAAI,IAAI,KAAK,EAAE,OAAQ;IAAM,OAAQ;AAAI,GAAE,IAAE,CAAC,GAAE;IAAK,IAAG,MAAI,GAAE,OAAQ;IAAK,IAAG,CAAC,KAAG,CAAC,GAAE,OAAQ;IAAM,IAAI,IAAE,OAAO,IAAI,CAAC,IAAG,IAAE,OAAO,IAAI,CAAC;IAAG,IAAG,EAAE,MAAM,KAAG,EAAE,MAAM,EAAC,OAAQ;IAAM,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,CAAC,EAAE,QAAQ,CAAC,MAAI,CAAC,CAAC,EAAE,KAAG,CAAC,CAAC,EAAE,EAAC,OAAQ;IAAK;IAAC,OAAQ;AAAI,GAAE,IAAE,CAAA,IAAG,MAAI,QAAM,MAAI;AAAM,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,MAAM,OAAO,CAAC,KAAG,EAAE,GAAE,KAAG,EAAE,IAAI,CAAC;IAAG;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,EAAE;IAAC,OAAO,EAAE,GAAE,IAAG;AAAC;AAAC,IAAI,IAAE;qCAAI;QAAA;;IAAK,IAAI,IAAE,EAAE;IAAC,EAAE,GAAE;IAAG,IAAI,IAAE,EAAE;IAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,CAAC,CAAC,EAAE,IAAE,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;IAAE,OAAO;AAAC,GAAE,IAAE,CAAC,GAAE;IAAK,IAAI,IAAE,CAAC;IAAE,IAAI,IAAI,KAAK,EAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,KAAK,GAAE;YAAC,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,MAAM,OAAO,CAAC,MAAI,MAAM,OAAO,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,KAAG,OAAO,KAAG,YAAU,OAAO,KAAG,YAAU,KAAG,IAAE,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,KAAG,CAAC,CAAC,EAAE,GAAC,IAAE,MAAI;QAAE,OAAM,CAAC,CAAC,EAAE,GAAC;IAAE;IAAC,IAAI,IAAI,KAAK,EAAE,KAAK,KAAG,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAE,OAAO;AAAC,GAAE,IAAE,QAAO,IAAE,CAAA,IAAG,CAAC,KAAG,OAAO,KAAG,WAAS,IAAE,EAAE,OAAO,CAAC,GAAE,KAAK,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/tailwind-variants/dist/chunk-RFCF2RNL.js"], "sourcesContent": ["import {b}from'./chunk-LK3VBVBE.js';import {twMerge,extendTailwindMerge}from'tailwind-merge';var p=s=>b(s)?twMerge:extendTailwindMerge({...s,extend:{theme:s.theme,classGroups:s.classGroups,conflictingClassGroupModifiers:s.conflictingClassGroupModifiers,conflictingClassGroups:s.conflictingClassGroups,...s.extend}});export{p as a};"], "names": [], "mappings": ";;;AAAA;AAAoC;;;AAAyD,IAAI,IAAE,CAAA,IAAG,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,KAAG,8JAAA,CAAA,UAAO,GAAC,CAAA,GAAA,8JAAA,CAAA,sBAAmB,AAAD,EAAE;QAAC,GAAG,CAAC;QAAC,QAAO;YAAC,OAAM,EAAE,KAAK;YAAC,aAAY,EAAE,WAAW;YAAC,gCAA+B,EAAE,8BAA8B;YAAC,wBAAuB,EAAE,sBAAsB;YAAC,GAAG,EAAE,MAAM;QAAA;IAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/tailwind-variants/dist/index.js"], "sourcesContent": ["import {a}from'./chunk-RFCF2RNL.js';import {b,g,c,f,a as a$1,h}from'./chunk-LK3VBVBE.js';var st={twMerge:true,twMergeConfig:{},responsiveVariants:false},x=(...l)=>{let u=[];X(l,u);let t=\"\";for(let d=0;d<u.length;d++)u[d]&&(t&&(t+=\" \"),t+=u[d]);return t||void 0};function X(l,u){for(let t=0;t<l.length;t++){let d=l[t];Array.isArray(d)?X(d,u):d&&u.push(d);}}var P=null,B={},F=false,S=(...l)=>u=>{let t=x(l);return !t||!u.twMerge?t:((!P||F)&&(F=false,P=a(B)),P(t)||void 0)},Q=(l,u)=>{for(let t in u)t in l?l[t]=x(l[t],u[t]):l[t]=u[t];return l},rt=(l,u)=>{let{extend:t=null,slots:d={},variants:R={},compoundVariants:q=[],compoundSlots:A=[],defaultVariants:L={}}=l,m={...st,...u},M=t?.base?x(t.base,l?.base):l?.base,y=t?.variants&&!b(t.variants)?g(R,t.variants):R,T=t?.defaultVariants&&!b(t.defaultVariants)?{...t.defaultVariants,...L}:L;!b(m.twMergeConfig)&&!c(m.twMergeConfig,B)&&(F=true,B=m.twMergeConfig);let j=b(t?.slots),$=b(d)?{}:{base:x(l?.base,j&&t?.base),...d},N=j?$:Q({...t?.slots},b($)?{base:l?.base}:$),w=b(t?.compoundVariants)?q:f(t?.compoundVariants,q),V=b$1=>{if(b(y)&&b(d)&&j)return S(M,b$1?.class,b$1?.className)(m);if(w&&!Array.isArray(w))throw new TypeError(`The \"compoundVariants\" prop must be an array. Received: ${typeof w}`);if(A&&!Array.isArray(A))throw new TypeError(`The \"compoundSlots\" prop must be an array. Received: ${typeof A}`);let Z=(n,e,s=[],o)=>{let a=s;if(typeof e==\"string\"){let c=h(e).split(\" \");for(let f=0;f<c.length;f++)a.push(`${n}:${c[f]}`);}else if(Array.isArray(e))for(let r=0;r<e.length;r++)a.push(`${n}:${e[r]}`);else if(typeof e==\"object\"&&typeof o==\"string\"&&o in e){let r=e[o];if(r&&typeof r==\"string\"){let f=h(r).split(\" \"),p=[];for(let i=0;i<f.length;i++)p.push(`${n}:${f[i]}`);a[o]=a[o]?a[o].concat(p):p;}else if(Array.isArray(r)&&r.length>0){let c=[];for(let f=0;f<r.length;f++)c.push(`${n}:${r[f]}`);a[o]=c;}}return a},U=(n,e=y,s=null,o=null)=>{let a=e[n];if(!a||b(a))return null;let r=o?.[n]??b$1?.[n];if(r===null)return null;let c=a$1(r),f=Array.isArray(m.responsiveVariants)&&m.responsiveVariants.length>0||m.responsiveVariants===true,p=T?.[n],i=[];if(typeof c==\"object\"&&f)for(let[C,G]of Object.entries(c)){let nt=a[G];if(C===\"initial\"){p=G;continue}Array.isArray(m.responsiveVariants)&&!m.responsiveVariants.includes(C)||(i=Z(C,nt,i,s));}let v=c!=null&&typeof c!=\"object\"?c:a$1(p),h=a[v||\"false\"];return typeof i==\"object\"&&typeof s==\"string\"&&i[s]?Q(i,h):i.length>0?(i.push(h),s===\"base\"?i.join(\" \"):i):h},_=()=>{if(!y)return null;let n=Object.keys(y),e=[];for(let s=0;s<n.length;s++){let o=U(n[s],y);o&&e.push(o);}return e},K=(n,e)=>{if(!y||typeof y!=\"object\")return null;let s=[];for(let o in y){let a=U(o,y,n,e),r=n===\"base\"&&typeof a==\"string\"?a:a&&a[n];r&&s.push(r);}return s},W={};for(let n in b$1){let e=b$1[n];e!==void 0&&(W[n]=e);}let z=(n,e)=>{let s=typeof b$1?.[n]==\"object\"?{[n]:b$1[n]?.initial}:{};return {...T,...W,...s,...e}},D=(n=[],e)=>{let s=[],o=n.length;for(let a=0;a<o;a++){let{class:r,className:c,...f}=n[a],p=true,i=z(null,e);for(let v in f){let h=f[v],C=i[v];if(Array.isArray(h)){if(!h.includes(C)){p=false;break}}else {if((h==null||h===false)&&(C==null||C===false))continue;if(C!==h){p=false;break}}}p&&(r&&s.push(r),c&&s.push(c));}return s},tt=n=>{let e=D(w,n);if(!Array.isArray(e))return e;let s={},o=S;for(let a=0;a<e.length;a++){let r=e[a];if(typeof r==\"string\")s.base=o(s.base,r)(m);else if(typeof r==\"object\")for(let c in r)s[c]=o(s[c],r[c])(m);}return s},et=n=>{if(A.length<1)return null;let e={},s=z(null,n);for(let o=0;o<A.length;o++){let{slots:a=[],class:r,className:c,...f}=A[o];if(!b(f)){let p=true;for(let i in f){let v=s[i],h=f[i];if(v===void 0||(Array.isArray(h)?!h.includes(v):h!==v)){p=false;break}}if(!p)continue}for(let p=0;p<a.length;p++){let i=a[p];e[i]||(e[i]=[]),e[i].push([r,c]);}}return e};if(!b(d)||!j){let n={};if(typeof N==\"object\"&&!b(N)){let e=S;for(let s in N)n[s]=o=>{let a=tt(o),r=et(o);return e(N[s],K(s,o),a?a[s]:void 0,r?r[s]:void 0,o?.class,o?.className)(m)};}return n}return S(M,_(),D(w),b$1?.class,b$1?.className)(m)},Y=()=>{if(!(!y||typeof y!=\"object\"))return Object.keys(y)};return V.variantKeys=Y(),V.extend=t,V.base=M,V.slots=N,V.variants=y,V.defaultVariants=T,V.compoundSlots=A,V.compoundVariants=w,V},it=l=>(u,t)=>rt(u,t?g(l,t):l);export{S as cn,x as cnBase,it as createTV,st as defaultConfig,rt as tv};"], "names": [], "mappings": ";;;;;;;AAAA;AAAoC;;;AAAqD,IAAI,KAAG;IAAC,SAAQ;IAAK,eAAc,CAAC;IAAE,oBAAmB;AAAK,GAAE,IAAE;qCAAI;QAAA;;IAAK,IAAI,IAAE,EAAE;IAAC,EAAE,GAAE;IAAG,IAAI,IAAE;IAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC,CAAC,EAAE;IAAE,OAAO,KAAG,KAAK;AAAC;AAAE,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,MAAM,OAAO,CAAC,KAAG,EAAE,GAAE,KAAG,KAAG,EAAE,IAAI,CAAC;IAAG;AAAC;AAAC,IAAI,IAAE,MAAK,IAAE,CAAC,GAAE,IAAE,OAAM,IAAE;qCAAI;QAAA;;WAAI,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,OAAO,CAAC,KAAG,CAAC,EAAE,OAAO,GAAC,IAAE,CAAC,CAAC,CAAC,KAAG,CAAC,KAAG,CAAC,IAAE,OAAM,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,EAAE,GAAE,EAAE,MAAI,KAAK,CAAC;IAAC;GAAE,IAAE,CAAC,GAAE;IAAK,IAAI,IAAI,KAAK,EAAE,KAAK,IAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;IAAC,OAAO;AAAC,GAAE,KAAG,CAAC,GAAE;IAAK,IAAG,EAAC,QAAO,IAAE,IAAI,EAAC,OAAM,IAAE,CAAC,CAAC,EAAC,UAAS,IAAE,CAAC,CAAC,EAAC,kBAAiB,IAAE,EAAE,EAAC,eAAc,IAAE,EAAE,EAAC,iBAAgB,IAAE,CAAC,CAAC,EAAC,GAAC,GAAE,IAAE;QAAC,GAAG,EAAE;QAAC,GAAG,CAAC;IAAA,GAAE,IAAE,CAAA,cAAA,wBAAA,EAAG,IAAI,IAAC,EAAE,EAAE,IAAI,EAAC,cAAA,wBAAA,EAAG,IAAI,IAAE,cAAA,wBAAA,EAAG,IAAI,EAAC,IAAE,CAAA,cAAA,wBAAA,EAAG,QAAQ,KAAE,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,EAAE,QAAQ,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,GAAE,EAAE,QAAQ,IAAE,GAAE,IAAE,CAAA,cAAA,wBAAA,EAAG,eAAe,KAAE,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,EAAE,eAAe,IAAE;QAAC,GAAG,EAAE,eAAe;QAAC,GAAG,CAAC;IAAA,IAAE;IAAE,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,EAAE,aAAa,KAAG,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,EAAE,aAAa,EAAC,MAAI,CAAC,IAAE,MAAK,IAAE,EAAE,aAAa;IAAE,IAAI,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,cAAA,wBAAA,EAAG,KAAK,GAAE,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,KAAG,CAAC,IAAE;QAAC,MAAK,EAAE,cAAA,wBAAA,EAAG,IAAI,EAAC,MAAG,cAAA,wBAAA,EAAG,IAAI;QAAE,GAAG,CAAC;IAAA,GAAE,IAAE,IAAE,IAAE,EAAE;WAAI,cAAA,wBAAA,EAAG,KAAK,AAAX;IAAW,GAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,KAAG;QAAC,IAAI,EAAC,cAAA,wBAAA,EAAG,IAAI;IAAA,IAAE,IAAG,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,cAAA,wBAAA,EAAG,gBAAgB,IAAE,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,cAAA,wBAAA,EAAG,gBAAgB,EAAC,IAAG,IAAE,CAAA;QAAM,IAAG,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,MAAI,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,MAAI,GAAE,OAAO,EAAE,GAAE,gBAAA,0BAAA,IAAK,KAAK,EAAC,gBAAA,0BAAA,IAAK,SAAS,EAAE;QAAG,IAAG,KAAG,CAAC,MAAM,OAAO,CAAC,IAAG,MAAM,IAAI,UAAU,AAAC,2DAAmE,OAAT,OAAO;QAAK,IAAG,KAAG,CAAC,MAAM,OAAO,CAAC,IAAG,MAAM,IAAI,UAAU,AAAC,wDAAgE,OAAT,OAAO;QAAK,IAAI,IAAE,SAAC,GAAE;gBAAE,qEAAE,EAAE,EAAC;YAAK,IAAI,IAAE;YAAE,IAAG,OAAO,KAAG,UAAS;gBAAC,IAAI,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,GAAG,KAAK,CAAC;gBAAK,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,AAAC,GAAO,OAAL,GAAE,KAAQ,OAAL,CAAC,CAAC,EAAE;YAAI,OAAM,IAAG,MAAM,OAAO,CAAC,IAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,AAAC,GAAO,OAAL,GAAE,KAAQ,OAAL,CAAC,CAAC,EAAE;iBAAS,IAAG,OAAO,KAAG,YAAU,OAAO,KAAG,YAAU,KAAK,GAAE;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAG,KAAG,OAAO,KAAG,UAAS;oBAAC,IAAI,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,GAAG,KAAK,CAAC,MAAK,IAAE,EAAE;oBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,AAAC,GAAO,OAAL,GAAE,KAAQ,OAAL,CAAC,CAAC,EAAE;oBAAI,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,KAAG;gBAAE,OAAM,IAAG,MAAM,OAAO,CAAC,MAAI,EAAE,MAAM,GAAC,GAAE;oBAAC,IAAI,IAAE,EAAE;oBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,EAAE,IAAI,CAAC,AAAC,GAAO,OAAL,GAAE,KAAQ,OAAL,CAAC,CAAC,EAAE;oBAAI,CAAC,CAAC,EAAE,GAAC;gBAAE;YAAC;YAAC,OAAO;QAAC,GAAE,IAAE,SAAC;gBAAE,qEAAE,GAAE,qEAAE,MAAK,qEAAE;YAAQ,IAAI,IAAE,CAAC,CAAC,EAAE;YAAC,IAAG,CAAC,KAAG,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,IAAG,OAAO;gBAAW;YAAN,IAAI,IAAE,CAAA,OAAA,cAAA,wBAAA,CAAG,CAAC,EAAE,cAAN,kBAAA,OAAQ,gBAAA,0BAAA,GAAK,CAAC,EAAE;YAAC,IAAG,MAAI,MAAK,OAAO;YAAK,IAAI,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAG,AAAD,EAAE,IAAG,IAAE,MAAM,OAAO,CAAC,EAAE,kBAAkB,KAAG,EAAE,kBAAkB,CAAC,MAAM,GAAC,KAAG,EAAE,kBAAkB,KAAG,MAAK,IAAE,cAAA,wBAAA,CAAG,CAAC,EAAE,EAAC,IAAE,EAAE;YAAC,IAAG,OAAO,KAAG,YAAU,GAAE,KAAI,IAAG,CAAC,GAAE,EAAE,IAAG,OAAO,OAAO,CAAC,GAAG;gBAAC,IAAI,KAAG,CAAC,CAAC,EAAE;gBAAC,IAAG,MAAI,WAAU;oBAAC,IAAE;oBAAE;gBAAQ;gBAAC,MAAM,OAAO,CAAC,EAAE,kBAAkB,KAAG,CAAC,EAAE,kBAAkB,CAAC,QAAQ,CAAC,MAAI,CAAC,IAAE,EAAE,GAAE,IAAG,GAAE,EAAE;YAAE;YAAC,IAAI,IAAE,KAAG,QAAM,OAAO,KAAG,WAAS,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAG,AAAD,EAAE,IAAG,IAAE,CAAC,CAAC,KAAG,QAAQ;YAAC,OAAO,OAAO,KAAG,YAAU,OAAO,KAAG,YAAU,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,KAAG,EAAE,MAAM,GAAC,IAAE,CAAC,EAAE,IAAI,CAAC,IAAG,MAAI,SAAO,EAAE,IAAI,CAAC,OAAK,CAAC,IAAE;QAAC,GAAE,IAAE;YAAK,IAAG,CAAC,GAAE,OAAO;YAAK,IAAI,IAAE,OAAO,IAAI,CAAC,IAAG,IAAE,EAAE;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE,EAAE,CAAC,CAAC,EAAE,EAAC;gBAAG,KAAG,EAAE,IAAI,CAAC;YAAG;YAAC,OAAO;QAAC,GAAE,IAAE,CAAC,GAAE;YAAK,IAAG,CAAC,KAAG,OAAO,KAAG,UAAS,OAAO;YAAK,IAAI,IAAE,EAAE;YAAC,IAAI,IAAI,KAAK,EAAE;gBAAC,IAAI,IAAE,EAAE,GAAE,GAAE,GAAE,IAAG,IAAE,MAAI,UAAQ,OAAO,KAAG,WAAS,IAAE,KAAG,CAAC,CAAC,EAAE;gBAAC,KAAG,EAAE,IAAI,CAAC;YAAG;YAAC,OAAO;QAAC,GAAE,IAAE,CAAC;QAAE,IAAI,IAAI,KAAK,IAAI;YAAC,IAAI,IAAE,GAAG,CAAC,EAAE;YAAC,MAAI,KAAK,KAAG,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC;QAAE;QAAC,IAAI,IAAE,CAAC,GAAE;gBAA0C;YAArC,IAAI,IAAE,QAAO,gBAAA,0BAAA,GAAK,CAAC,EAAE,KAAE,WAAS;gBAAC,CAAC,EAAE,GAAC,SAAA,GAAG,CAAC,EAAE,cAAN,6BAAA,OAAQ,OAAO;YAAA,IAAE,CAAC;YAAE,OAAO;gBAAC,GAAG,CAAC;gBAAC,GAAG,CAAC;gBAAC,GAAG,CAAC;gBAAC,GAAG,CAAC;YAAA;QAAC,GAAE,IAAE;gBAAC,qEAAE,EAAE,EAAC;YAAK,IAAI,IAAE,EAAE,EAAC,IAAE,EAAE,MAAM;YAAC,IAAI,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI;gBAAC,IAAG,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,GAAG,GAAE,GAAC,CAAC,CAAC,EAAE,EAAC,IAAE,MAAK,IAAE,EAAE,MAAK;gBAAG,IAAI,IAAI,KAAK,EAAE;oBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;oBAAC,IAAG,MAAM,OAAO,CAAC,IAAG;wBAAC,IAAG,CAAC,EAAE,QAAQ,CAAC,IAAG;4BAAC,IAAE;4BAAM;wBAAK;oBAAC,OAAM;wBAAC,IAAG,CAAC,KAAG,QAAM,MAAI,KAAK,KAAG,CAAC,KAAG,QAAM,MAAI,KAAK,GAAE;wBAAS,IAAG,MAAI,GAAE;4BAAC,IAAE;4BAAM;wBAAK;oBAAC;gBAAC;gBAAC,KAAG,CAAC,KAAG,EAAE,IAAI,CAAC,IAAG,KAAG,EAAE,IAAI,CAAC,EAAE;YAAE;YAAC,OAAO;QAAC,GAAE,KAAG,CAAA;YAAI,IAAI,IAAE,EAAE,GAAE;YAAG,IAAG,CAAC,MAAM,OAAO,CAAC,IAAG,OAAO;YAAE,IAAI,IAAE,CAAC,GAAE,IAAE;YAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;gBAAC,IAAG,OAAO,KAAG,UAAS,EAAE,IAAI,GAAC,EAAE,EAAE,IAAI,EAAC,GAAG;qBAAQ,IAAG,OAAO,KAAG,UAAS,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAE,EAAE;YAAG;YAAC,OAAO;QAAC,GAAE,KAAG,CAAA;YAAI,IAAG,EAAE,MAAM,GAAC,GAAE,OAAO;YAAK,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE,MAAK;YAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;gBAAC,IAAG,EAAC,OAAM,IAAE,EAAE,EAAC,OAAM,CAAC,EAAC,WAAU,CAAC,EAAC,GAAG,GAAE,GAAC,CAAC,CAAC,EAAE;gBAAC,IAAG,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,IAAG;oBAAC,IAAI,IAAE;oBAAK,IAAI,IAAI,KAAK,EAAE;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,CAAC,CAAC,EAAE;wBAAC,IAAG,MAAI,KAAK,KAAG,CAAC,MAAM,OAAO,CAAC,KAAG,CAAC,EAAE,QAAQ,CAAC,KAAG,MAAI,CAAC,GAAE;4BAAC,IAAE;4BAAM;wBAAK;oBAAC;oBAAC,IAAG,CAAC,GAAE;gBAAQ;gBAAC,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;oBAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;wBAAC;wBAAE;qBAAE;gBAAE;YAAC;YAAC,OAAO;QAAC;QAAE,IAAG,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,MAAI,CAAC,GAAE;YAAC,IAAI,IAAE,CAAC;YAAE,IAAG,OAAO,KAAG,YAAU,CAAC,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,IAAG;gBAAC,IAAI,IAAE;gBAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,GAAC,CAAA;oBAAI,IAAI,IAAE,GAAG,IAAG,IAAE,GAAG;oBAAG,OAAO,EAAE,CAAC,CAAC,EAAE,EAAC,EAAE,GAAE,IAAG,uCAAE,CAAC,CAAC,EAAE,GAAC,yBAAO,IAAE,CAAC,CAAC,EAAE,GAAC,KAAK,GAAE,cAAA,wBAAA,EAAG,KAAK,EAAC,cAAA,wBAAA,EAAG,SAAS,EAAE;gBAAE;YAAE;YAAC,OAAO;QAAC;QAAC,OAAO,EAAE,GAAE,KAAI,EAAE,IAAG,gBAAA,0BAAA,IAAK,KAAK,EAAC,gBAAA,0BAAA,IAAK,SAAS,EAAE;IAAE,GAAE,IAAE;QAAK,IAAG,CAAC,CAAC,CAAC,KAAG,OAAO,KAAG,QAAQ,GAAE,OAAO,OAAO,IAAI,CAAC;IAAE;IAAE,OAAO,EAAE,WAAW,GAAC,KAAI,EAAE,MAAM,GAAC,GAAE,EAAE,IAAI,GAAC,GAAE,EAAE,KAAK,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,eAAe,GAAC,GAAE,EAAE,aAAa,GAAC,GAAE,EAAE,gBAAgB,GAAC,GAAE;AAAC,GAAE,KAAG,CAAA,IAAG,CAAC,GAAE,IAAI,GAAG,GAAE,IAAE,CAAA,GAAA,oKAAA,CAAA,IAAC,AAAD,EAAE,GAAE,KAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/flags/dist/module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/flags/dist/packages/%40react-stately/flags/src/index.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;AAED,IAAI,yCAAmB;AACvB,IAAI,mCAAa;AAEV,SAAS;IACd,yCAAmB;AACrB;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,mCAAa;AACf;AAEO,SAAS;IACd,OAAO;AACT", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/utils/dist/useControlledState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/utils/dist/packages/%40react-stately/utils/src/useControlledState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\n\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined>, defaultValue: Exclude<T, undefined> | undefined, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined> | undefined, defaultValue: Exclude<T, undefined>, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: T, defaultValue: T, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void] {\n  let [stateValue, setStateValue] = useState(value || defaultValue);\n\n  let isControlledRef = useRef(value !== undefined);\n  let isControlled = value !== undefined;\n  useEffect(() => {\n    let wasControlled = isControlledRef.current;\n    if (wasControlled !== isControlled && process.env.NODE_ENV !== 'production') {\n      console.warn(`WARN: A component changed from ${wasControlled ? 'controlled' : 'uncontrolled'} to ${isControlled ? 'controlled' : 'uncontrolled'}.`);\n    }\n    isControlledRef.current = isControlled;\n  }, [isControlled]);\n\n  let currentValue = isControlled ? value : stateValue;\n  let setValue = useCallback((value, ...args) => {\n    let onChangeCaller = (value, ...onChangeArgs) => {\n      if (onChange) {\n        if (!Object.is(currentValue, value)) {\n          onChange(value, ...onChangeArgs);\n        }\n      }\n      if (!isControlled) {\n        // If uncontrolled, mutate the currentValue local variable so that\n        // calling setState multiple times with the same value only emits onChange once.\n        // We do not use a ref for this because we specifically _do_ want the value to\n        // reset every render, and assigning to a ref in render breaks aborted suspended renders.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        currentValue = value;\n      }\n    };\n\n    if (typeof value === 'function') {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320');\n      }\n      // this supports functional updates https://reactjs.org/docs/hooks-reference.html#functional-updates\n      // when someone using useControlledState calls setControlledState(myFunc)\n      // this will call our useState setState with a function as well which invokes myFunc and calls onChange with the value from myFunc\n      // if we're in an uncontrolled state, then we also return the value of myFunc which to setState looks as though it was just called with myFunc from the beginning\n      // otherwise we just return the controlled value, which won't cause a rerender because React knows to bail out when the value is the same\n      let updateFunction = (oldValue, ...functionArgs) => {\n        let interceptedValue = value(isControlled ? currentValue : oldValue, ...functionArgs);\n        onChangeCaller(interceptedValue, ...args);\n        if (!isControlled) {\n          return interceptedValue;\n        }\n        return oldValue;\n      };\n      setStateValue(updateFunction);\n    } else {\n      if (!isControlled) {\n        setStateValue(value);\n      }\n      onChangeCaller(value, ...args);\n    }\n  }, [isControlled, currentValue, onChange]);\n\n  return [currentValue, setValue];\n}\n"], "names": [], "mappings": ";;;AAuB0C,QAAQ,GAAG,CAAC,QAAQ;;;AAvB9D;;;;;;;;;;CAUC,GAMM,SAAS,0CAA6B,KAAQ,EAAE,YAAe,EAAE,QAAyC;IAC/G,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,iKAAA,WAAO,EAAE,SAAS;IAEpD,IAAI,kBAAkB,CAAA,GAAA,uKAAK,EAAE,UAAU;IACvC,IAAI,eAAe,UAAU;IAC7B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,gBAAgB,gBAAgB,OAAO;QAC3C,IAAI,kBAAkB,oEAAyC,cAC7D,QAAQ,IAAI,CAAC,AAAC,+BAA+B,GAAsD,OAApD,gBAAgB,eAAe,gBAAe,IAAI,0BAAiB,eAAe,gBAAe,CAAC,CAAC;QAEpJ,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAa;IAEjB,IAAI,eAAe,eAAe,QAAQ;IAC1C,IAAI,WAAW,CAAA,GAAA,4KAAU,EAAE,SAAC,OAAO;;YAAG;;QACpC,IAAI,iBAAiB,SAAC,OAAO;;gBAAG;;YAC9B,IAAI,UACF;gBAAA,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,QAC3B,SAAS,UAAU;YACrB;YAEF,IAAI,CAAC,cACH,AACA,kEADkE,cACc;YAChF,8EAA8E;YAC9E,yFAAyF;YACzF,uDAAuD;YACvD,eAAe;QAEnB;QAEA,IAAI,OAAO,UAAU,YAAY;YAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,WAC3B,QAAQ,IAAI,CAAC;YAEf,oGAAoG;YACpG,yEAAyE;YACzE,kIAAkI;YAClI,iKAAiK;YACjK,yIAAyI;YACzI,IAAI,iBAAiB,SAAC,UAAU;;oBAAG;;gBACjC,IAAI,mBAAmB,MAAM,eAAe,eAAe,aAAa;gBACxE,eAAe,qBAAqB;gBACpC,IAAI,CAAC,cACH,OAAO;gBAET,OAAO;YACT;YACA,cAAc;QAChB,OAAO;YACL,IAAI,CAAC,cACH,cAAc;YAEhB,eAAe,UAAU;QAC3B;IACF,GAAG;QAAC;QAAc;QAAc;KAAS;IAEzC,OAAO;QAAC;QAAc;KAAS;AACjC", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/utils/dist/number.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/utils/dist/packages/%40react-stately/utils/src/number.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n */\nexport function clamp(value: number, min: number = -Infinity, max: number = Infinity): number {\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\n\nexport function roundToStepPrecision(value: number, step: number): number {\n  let roundedValue = value;\n  let precision = 0;\n  let stepString = step.toString();\n  // Handle negative exponents in exponential notation (e.g., \"1e-7\" → precision 8)\n  let eIndex = stepString.toLowerCase().indexOf('e-');\n  if (eIndex > 0) {\n    precision = Math.abs(Math.floor(Math.log10(Math.abs(step)))) + eIndex;\n  } else {\n    let pointIndex = stepString.indexOf('.');\n    if (pointIndex >= 0) {\n      precision = stepString.length - pointIndex;\n    }\n  }\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\n\nexport function snapValueToStep(value: number, min: number | undefined, max: number | undefined, step: number): number {\n  min = Number(min);\n  max = Number(max);\n  let remainder = ((value - (isNaN(min) ? 0 : min)) % step);\n  let snappedValue = roundToStepPrecision(Math.abs(remainder) * 2 >= step\n    ? value + Math.sign(remainder) * (step - Math.abs(remainder))\n    : value - remainder, step);\n\n  if (!isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min;\n    } else if (!isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor(roundToStepPrecision((max - min) / step, step)) * step;\n    }\n  } else if (!isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(roundToStepPrecision(max / step, step)) * step;\n  }\n\n  // correct floating point behavior by rounding to step precision\n  snappedValue = roundToStepPrecision(snappedValue, step);\n\n  return snappedValue;\n}\n\n/* Takes a value and rounds off to the number of digits. */\nexport function toFixedNumber(value: number, digits: number, base: number = 10): number {\n  const pow = Math.pow(base, digits);\n\n  return Math.round(value * pow) / pow;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;CAEC;;;;;;AACM,SAAS,0CAAM,KAAa;cAAE,iEAAc,CAAC,QAAQ,QAAE,iEAAc,QAAQ;IAClF,IAAI,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,MAAM;IAC9C,OAAO;AACT;AAEO,SAAS,0CAAqB,KAAa,EAAE,IAAY;IAC9D,IAAI,eAAe;IACnB,IAAI,YAAY;IAChB,IAAI,aAAa,KAAK,QAAQ;IAC9B,iFAAiF;IACjF,IAAI,SAAS,WAAW,WAAW,GAAG,OAAO,CAAC;IAC9C,IAAI,SAAS,GACX,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,WAAW;SAC1D;QACL,IAAI,aAAa,WAAW,OAAO,CAAC;QACpC,IAAI,cAAc,GAChB,YAAY,WAAW,MAAM,GAAG;IAEpC;IACA,IAAI,YAAY,GAAG;QACjB,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI;QACvB,eAAe,KAAK,KAAK,CAAC,eAAe,OAAO;IAClD;IACA,OAAO;AACT;AAEO,SAAS,0CAAgB,KAAa,EAAE,GAAuB,EAAE,GAAuB,EAAE,IAAY;IAC3G,MAAM,OAAO;IACb,MAAM,OAAO;IACb,IAAI,YAAc,CAAA,QAAS,CAAA,MAAM,OAAO,IAAI,GAAE,CAAC,IAAK;IACpD,IAAI,eAAe,0CAAqB,KAAK,GAAG,CAAC,aAAa,KAAK,OAC/D,QAAQ,KAAK,IAAI,CAAC,aAAc,CAAA,OAAO,KAAK,GAAG,CAAC,UAAS,IACzD,QAAQ,WAAW;IAEvB,IAAI,CAAC,MAAM,MAAM;QACf,IAAI,eAAe,KACjB,eAAe;aACV,IAAI,CAAC,MAAM,QAAQ,eAAe,KACvC,eAAe,MAAM,KAAK,KAAK,CAAC,0CAAsB,CAAA,MAAM,GAAE,IAAK,MAAM,SAAS;IAEtF,OAAO,IAAI,CAAC,MAAM,QAAQ,eAAe,KACvC,eAAe,KAAK,KAAK,CAAC,0CAAqB,MAAM,MAAM,SAAS;IAGtE,gEAAgE;IAChE,eAAe,0CAAqB,cAAc;IAElD,OAAO;AACT;AAGO,SAAS,yCAAc,KAAa,EAAE,MAAc;eAAE,iEAAe,EAAE;IAC5E,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM;IAE3B,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO;AACnC", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/form/dist/useFormValidationState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/form/dist/packages/%40react-stately/form/src/useFormValidationState.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Context, createContext, useContext, useEffect, useMemo, useRef, useState} from 'react';\nimport {Validation, ValidationErrors, ValidationFunction, ValidationResult} from '@react-types/shared';\n\nexport const VALID_VALIDITY_STATE: ValidityState = {\n  badInput: false,\n  customError: false,\n  patternMismatch: false,\n  rangeOverflow: false,\n  rangeUnderflow: false,\n  stepMismatch: false,\n  tooLong: false,\n  tooShort: false,\n  typeMismatch: false,\n  valueMissing: false,\n  valid: true\n};\n\nconst CUSTOM_VALIDITY_STATE: ValidityState = {\n  ...VALID_VALIDITY_STATE,\n  customError: true,\n  valid: false\n};\n\nexport const DEFAULT_VALIDATION_RESULT: ValidationResult = {\n  isInvalid: false,\n  validationDetails: VALID_VALIDITY_STATE,\n  validationErrors: []\n};\n\nexport const FormValidationContext: Context<ValidationErrors> = createContext<ValidationErrors>({});\n\nexport const privateValidationStateProp: string = '__formValidationState' + Date.now();\n\ninterface FormValidationProps<T> extends Validation<T> {\n  builtinValidation?: ValidationResult,\n  name?: string | string[],\n  value: T | null\n}\n\nexport interface FormValidationState {\n  /** Realtime validation results, updated as the user edits the value. */\n  realtimeValidation: ValidationResult,\n  /** Currently displayed validation results, updated when the user commits their changes. */\n  displayValidation: ValidationResult,\n  /** Updates the current validation result. Not displayed to the user until `commitValidation` is called. */\n  updateValidation(result: ValidationResult): void,\n  /** Resets the displayed validation state to valid when the user resets the form. */\n  resetValidation(): void,\n  /** Commits the realtime validation so it is displayed to the user. */\n  commitValidation(): void\n}\n\nexport function useFormValidationState<T>(props: FormValidationProps<T>): FormValidationState {\n  // Private prop for parent components to pass state to children.\n  if (props[privateValidationStateProp]) {\n    let {realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation} = props[privateValidationStateProp] as FormValidationState;\n    return {realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation};\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useFormValidationStateImpl(props);\n}\n\nfunction useFormValidationStateImpl<T>(props: FormValidationProps<T>): FormValidationState {\n  let {isInvalid, validationState, name, value, builtinValidation, validate, validationBehavior = 'aria'} = props;\n\n  // backward compatibility.\n  if (validationState) {\n    isInvalid ||= validationState === 'invalid';\n  }\n\n  // If the isInvalid prop is controlled, update validation result in realtime.\n  let controlledError: ValidationResult | null = isInvalid !== undefined ? {\n    isInvalid,\n    validationErrors: [],\n    validationDetails: CUSTOM_VALIDITY_STATE\n  } : null;\n\n  // Perform custom client side validation.\n  let clientError: ValidationResult | null = useMemo(() => {\n    if (!validate || value == null) {\n      return null;\n    }\n    let validateErrors = runValidate(validate, value);\n    return getValidationResult(validateErrors);\n  }, [validate, value]);\n\n  if (builtinValidation?.validationDetails.valid) {\n    builtinValidation = undefined;\n  }\n\n  // Get relevant server errors from the form.\n  let serverErrors = useContext(FormValidationContext);\n  let serverErrorMessages = useMemo(() => {\n    if (name) {\n      return Array.isArray(name) ? name.flatMap(name => asArray(serverErrors[name])) : asArray(serverErrors[name]);\n    }\n    return [];\n  }, [serverErrors, name]);\n\n  // Show server errors when the form gets a new value, and clear when the user changes the value.\n  let [lastServerErrors, setLastServerErrors] = useState(serverErrors);\n  let [isServerErrorCleared, setServerErrorCleared] = useState(false);\n  if (serverErrors !== lastServerErrors) {\n    setLastServerErrors(serverErrors);\n    setServerErrorCleared(false);\n  }\n\n  let serverError: ValidationResult | null = useMemo(() =>\n    getValidationResult(isServerErrorCleared ? [] : serverErrorMessages),\n    [isServerErrorCleared, serverErrorMessages]\n  );\n\n  // Track the next validation state in a ref until commitValidation is called.\n  let nextValidation = useRef(DEFAULT_VALIDATION_RESULT);\n  let [currentValidity, setCurrentValidity] = useState(DEFAULT_VALIDATION_RESULT);\n\n  let lastError = useRef(DEFAULT_VALIDATION_RESULT);\n  let commitValidation = () => {\n    if (!commitQueued) {\n      return;\n    }\n\n    setCommitQueued(false);\n    let error = clientError || builtinValidation || nextValidation.current;\n    if (!isEqualValidation(error, lastError.current)) {\n      lastError.current = error;\n      setCurrentValidity(error);\n    }\n  };\n\n  let [commitQueued, setCommitQueued] = useState(false);\n  useEffect(commitValidation);\n\n  // realtimeValidation is used to update the native input element's state based on custom validation logic.\n  // displayValidation is the currently displayed validation state that the user sees (e.g. on input change/form submit).\n  // With validationBehavior=\"aria\", all errors are displayed in realtime rather than on submit.\n  let realtimeValidation = controlledError || serverError || clientError || builtinValidation || DEFAULT_VALIDATION_RESULT;\n  let displayValidation = validationBehavior === 'native'\n    ? controlledError || serverError || currentValidity\n    : controlledError || serverError || clientError || builtinValidation || currentValidity;\n\n  return {\n    realtimeValidation,\n    displayValidation,\n    updateValidation(value) {\n      // If validationBehavior is 'aria', update in realtime. Otherwise, store in a ref until commit.\n      if (validationBehavior === 'aria' && !isEqualValidation(currentValidity, value)) {\n        setCurrentValidity(value);\n      } else {\n        nextValidation.current = value;\n      }\n    },\n    resetValidation() {\n      // Update the currently displayed validation state to valid on form reset,\n      // even if the native validity says it isn't. It'll show again on the next form submit.\n      let error = DEFAULT_VALIDATION_RESULT;\n      if (!isEqualValidation(error, lastError.current)) {\n        lastError.current = error;\n        setCurrentValidity(error);\n      }\n\n      // Do not commit validation after the next render. This avoids a condition where\n      // useSelect calls commitValidation inside an onReset handler.\n      if (validationBehavior === 'native') {\n        setCommitQueued(false);\n      }\n\n      setServerErrorCleared(true);\n    },\n    commitValidation() {\n      // Commit validation state so the user sees it on blur/change/submit. Also clear any server errors.\n      // Wait until after the next render to commit so that the latest value has been validated.\n      if (validationBehavior === 'native') {\n        setCommitQueued(true);\n      }\n      setServerErrorCleared(true);\n    }\n  };\n}\n\nfunction asArray<T>(v: T | T[]): T[] {\n  if (!v) {\n    return [];\n  }\n\n  return Array.isArray(v) ? v : [v];\n}\n\nfunction runValidate<T>(validate: ValidationFunction<T>, value: T): string[] {\n  if (typeof validate === 'function') {\n    let e = validate(value);\n    if (e && typeof e !== 'boolean') {\n      return asArray(e);\n    }\n  }\n\n  return [];\n}\n\nfunction getValidationResult(errors: string[]): ValidationResult | null {\n  return errors.length ? {\n    isInvalid: true,\n    validationErrors: errors,\n    validationDetails: CUSTOM_VALIDITY_STATE\n  } : null;\n}\n\nfunction isEqualValidation(a: ValidationResult | null, b: ValidationResult | null): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  return !!a && !!b\n    && a.isInvalid === b.isInvalid\n    && a.validationErrors.length === b.validationErrors.length\n    && a.validationErrors.every((a, i) => a === b.validationErrors[i])\n    && Object.entries(a.validationDetails).every(([k, v]) => b.validationDetails[k] === v);\n}\n\nexport function mergeValidation(...results: ValidationResult[]): ValidationResult {\n  let errors = new Set<string>();\n  let isInvalid = false;\n  let validationDetails = {\n    ...VALID_VALIDITY_STATE\n  };\n\n  for (let v of results) {\n    for (let e of v.validationErrors) {\n      errors.add(e);\n    }\n\n    // Only these properties apply for checkboxes.\n    isInvalid ||= v.isInvalid;\n    for (let key in validationDetails) {\n      validationDetails[key] ||= v.validationDetails[key];\n    }\n  }\n\n  validationDetails.valid = !isInvalid;\n  return {\n    isInvalid,\n    validationErrors: [...errors],\n    validationDetails\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAKM,MAAM,4CAAsC;IACjD,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,SAAS;IACT,UAAU;IACV,cAAc;IACd,cAAc;IACd,OAAO;AACT;AAEA,MAAM,8CAAuC;IAC3C,GAAG,yCAAoB;IACvB,aAAa;IACb,OAAO;AACT;AAEO,MAAM,4CAA8C;IACzD,WAAW;IACX,mBAAmB;IACnB,kBAAkB,EAAE;AACtB;AAEO,MAAM,4CAAmD,CAAA,iKAAA,gBAAY,EAAoB,CAAC;AAE1F,MAAM,2CAAqC,0BAA0B,KAAK,GAAG;AAqB7E,SAAS,0CAA0B,KAA6B;IACrE,gEAAgE;IAChE,IAAI,KAAK,CAAC,yCAA2B,EAAE;QACrC,IAAI,EAAA,oBAAC,kBAAkB,EAAA,mBAAE,iBAAiB,EAAA,kBAAE,gBAAgB,EAAA,iBAAE,eAAe,EAAA,kBAAE,gBAAgB,EAAC,GAAG,KAAK,CAAC,yCAA2B;QACpI,OAAO;gCAAC;+BAAoB;8BAAmB;6BAAkB;8BAAiB;QAAgB;IACpG;IAEA,sDAAsD;IACtD,OAAO,iDAA2B;AACpC;AAEA,SAAS,iDAA8B,KAA6B;IAClE,IAAI,EAAA,WAAC,SAAS,EAAA,iBAAE,eAAe,EAAA,MAAE,IAAI,EAAA,OAAE,KAAK,EAAA,mBAAE,iBAAiB,EAAA,UAAE,QAAQ,EAAA,oBAAE,qBAAqB,MAAA,EAAO,GAAG;IAE1G,0BAA0B;IAC1B,IAAI,iBACF,aAAA,CAAA,YAAc,oBAAoB,SAAA;IAGpC,6EAA6E;IAC7E,IAAI,kBAA2C,cAAc,YAAY;mBACvE;QACA,kBAAkB,EAAE;QACpB,mBAAmB;IACrB,IAAI;IAEJ,yCAAyC;IACzC,IAAI,cAAuC,CAAA,GAAA,wKAAM,EAAE;QACjD,IAAI,CAAC,YAAY,SAAS,MACxB,OAAO;QAET,IAAI,iBAAiB,kCAAY,UAAU;QAC3C,OAAO,0CAAoB;IAC7B,GAAG;QAAC;QAAU;KAAM;IAEpB,IAAI,sBAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAmB,iBAAiB,CAAC,KAAK,EAC5C,oBAAoB;IAGtB,4CAA4C;IAC5C,IAAI,eAAe,CAAA,iKAAA,aAAS,EAAE;IAC9B,IAAI,sBAAsB,CAAA,iKAAA,UAAM,EAAE;QAChC,IAAI,MACF,OAAO,MAAM,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAA,OAAQ,8BAAQ,YAAY,CAAC,KAAK,KAAK,8BAAQ,YAAY,CAAC,KAAK;QAE7G,OAAO,EAAE;IACX,GAAG;QAAC;QAAc;KAAK;IAEvB,gGAAgG;IAChG,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvD,IAAI,CAAC,sBAAsB,sBAAsB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAC7D,IAAI,iBAAiB,kBAAkB;QACrC,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,IAAI,cAAuC,CAAA,gKAAA,WAAM,EAAE,IACjD,0CAAoB,uBAAuB,EAAE,GAAG,sBAChD;QAAC;QAAsB;KAAoB;IAG7C,6EAA6E;IAC7E,IAAI,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC5B,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAErD,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE;IACvB,IAAI,mBAAmB;QACrB,IAAI,CAAC,cACH;QAGF,gBAAgB;QAChB,IAAI,QAAQ,eAAe,qBAAqB,eAAe,OAAO;QACtE,IAAI,CAAC,wCAAkB,OAAO,UAAU,OAAO,GAAG;YAChD,UAAU,OAAO,GAAG;YACpB,mBAAmB;QACrB;IACF;IAEA,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,gKAAA,YAAO,EAAE;IAC/C,CAAA,iKAAA,YAAQ,EAAE;IAEV,0GAA0G;IAC1G,uHAAuH;IACvH,8FAA8F;IAC9F,IAAI,qBAAqB,mBAAmB,eAAe,eAAe,qBAAqB;IAC/F,IAAI,oBAAoB,uBAAuB,WAC3C,mBAAmB,eAAe,kBAClC,mBAAmB,eAAe,eAAe,qBAAqB;IAE1E,OAAO;4BACL;2BACA;QACA,kBAAiB,KAAK;YACpB,+FAA+F;YAC/F,IAAI,uBAAuB,UAAU,CAAC,wCAAkB,iBAAiB,QACvE,mBAAmB;iBAEnB,eAAe,OAAO,GAAG;QAE7B;QACA;YACE,0EAA0E;YAC1E,uFAAuF;YACvF,IAAI,QAAQ;YACZ,IAAI,CAAC,wCAAkB,OAAO,UAAU,OAAO,GAAG;gBAChD,UAAU,OAAO,GAAG;gBACpB,mBAAmB;YACrB;YAEA,gFAAgF;YAChF,8DAA8D;YAC9D,IAAI,uBAAuB,UACzB,gBAAgB;YAGlB,sBAAsB;QACxB;QACA;YACE,mGAAmG;YACnG,0FAA0F;YAC1F,IAAI,uBAAuB,UACzB,gBAAgB;YAElB,sBAAsB;QACxB;IACF;AACF;AAEA,SAAS,8BAAW,CAAU;IAC5B,IAAI,CAAC,GACH,OAAO,EAAE;IAGX,OAAO,MAAM,OAAO,CAAC,KAAK,IAAI;QAAC;KAAE;AACnC;AAEA,SAAS,kCAAe,QAA+B,EAAE,KAAQ;IAC/D,IAAI,OAAO,aAAa,YAAY;QAClC,IAAI,IAAI,SAAS;QACjB,IAAI,KAAK,OAAO,MAAM,WACpB,OAAO,8BAAQ;IAEnB;IAEA,OAAO,EAAE;AACX;AAEA,SAAS,0CAAoB,MAAgB;IAC3C,OAAO,OAAO,MAAM,GAAG;QACrB,WAAW;QACX,kBAAkB;QAClB,mBAAmB;IACrB,IAAI;AACN;AAEA,SAAS,wCAAkB,CAA0B,EAAE,CAA0B;IAC/E,IAAI,MAAM,GACR,OAAO;IAGT,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,KACX,EAAE,SAAS,KAAK,EAAE,SAAS,IAC3B,EAAE,gBAAgB,CAAC,MAAM,KAAK,EAAE,gBAAgB,CAAC,MAAM,IACvD,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,IAAM,MAAM,EAAE,gBAAgB,CAAC,EAAE,KAC9D,OAAO,OAAO,CAAC,EAAE,iBAAiB,EAAE,KAAK,CAAC;YAAC,CAAC,GAAG,EAAE;eAAK,EAAE,iBAAiB,CAAC,EAAE,KAAK;;AACxF;AAEO,SAAS;YAAgB,yBAAG;uCAA2B;;IAC5D,IAAI,SAAS,IAAI;IACjB,IAAI,YAAY;IAChB,IAAI,oBAAoB;QACtB,GAAG,yCAAoB;IACzB;IAEA,KAAK,IAAI,KAAK,QAAS;YAQnB,oBAAkB;QAPpB,KAAK,IAAI,KAAK,EAAE,gBAAgB,CAC9B,OAAO,GAAG,CAAC;QAGb,8CAA8C;QAC9C,aAAA,CAAA,YAAc,EAAE,SAAS;QACzB,IAAK,IAAI,OAAO,kBACd,CAAA,qBAAA,iBAAA,CAAiB,CAAC,QAAA,IAAI,IAAA,CAAtB,kBAAiB,CAAC,MAAI,GAAK,EAAE,iBAAiB,CAAC,IAAI;IAEvD;IAEA,kBAAkB,KAAK,GAAG,CAAC;IAC3B,OAAO;mBACL;QACA,kBAAkB;eAAI;SAAO;2BAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/toggle/dist/useToggleState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/toggle/dist/packages/%40react-stately/toggle/src/useToggleState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ToggleStateOptions} from '@react-types/checkbox';\nimport {useControlledState} from '@react-stately/utils';\nimport {useState} from 'react';\n\nexport type {ToggleStateOptions};\n\nexport interface ToggleState {\n  /** Whether the toggle is selected. */\n  readonly isSelected: boolean,\n\n  /** Whether the toggle is selected by default. */\n  readonly defaultSelected: boolean,\n\n  /** Updates selection state. */\n  setSelected(isSelected: boolean): void,\n\n  /** Toggle the selection state. */\n  toggle(): void\n}\n\n/**\n * Provides state management for toggle components like checkboxes and switches.\n */\nexport function useToggleState(props: ToggleStateOptions = {}): ToggleState {\n  let {isReadOnly} = props;\n\n  // have to provide an empty function so useControlledState doesn't throw a fit\n  // can't use useControlledState's prop calling because we need the event object from the change\n  let [isSelected, setSelected] = useControlledState(props.isSelected, props.defaultSelected || false, props.onChange);\n  let [initialValue] = useState(isSelected);\n\n  function updateSelected(value) {\n    if (!isReadOnly) {\n      setSelected(value);\n    }\n  }\n\n  function toggleState() {\n    if (!isReadOnly) {\n      setSelected(!isSelected);\n    }\n  }\n\n  return {\n    isSelected,\n    defaultSelected: props.defaultSelected ?? initialValue,\n    setSelected: updateSelected,\n    toggle: toggleState\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAyBM,SAAS;gBAAe,iEAA4B,CAAC,CAAC;IAC3D,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG;IAEnB,8EAA8E;IAC9E,+FAA+F;IAC/F,IAAI,CAAC,YAAY,YAAY,GAAG,CAAA,iLAAA,qBAAiB,EAAE,MAAM,UAAU,EAAE,MAAM,eAAe,IAAI,OAAO,MAAM,QAAQ;IACnH,IAAI,CAAC,aAAa,GAAG,CAAA,iKAAA,WAAO,EAAE;IAE9B,SAAS,eAAe,KAAK;QAC3B,IAAI,CAAC,YACH,YAAY;IAEhB;IAEA,SAAS;QACP,IAAI,CAAC,YACH,YAAY,CAAC;IAEjB;QAImB;IAFnB,OAAO;oBACL;QACA,iBAAiB,CAAA,yBAAA,MAAM,eAAe,MAAA,QAArB,2BAAA,KAAA,IAAA,yBAAyB;QAC1C,aAAa;QACb,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/overlays/dist/packages/%40react-stately/overlays/src/useOverlayTriggerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {OverlayTriggerProps} from '@react-types/overlays';\nimport {useCallback} from 'react';\nimport {useControlledState} from '@react-stately/utils';\n\nexport interface OverlayTriggerState {\n  /** Whether the overlay is currently open. */\n  readonly isOpen: boolean,\n  /** Sets whether the overlay is open. */\n  setOpen(isOpen: boolean): void,\n  /** Opens the overlay. */\n  open(): void,\n  /** Closes the overlay. */\n  close(): void,\n  /** Toggles the overlay's visibility. */\n  toggle(): void\n}\n\n/**\n * Manages state for an overlay trigger. Tracks whether the overlay is open, and provides\n * methods to toggle this state.\n */\nexport function useOverlayTriggerState(props: OverlayTriggerProps): OverlayTriggerState  {\n  let [isOpen, setOpen] = useControlledState(props.isOpen, props.defaultOpen || false, props.onOpenChange);\n\n  const open = useCallback(() => {\n    setOpen(true);\n  }, [setOpen]);\n\n  const close = useCallback(() => {\n    setOpen(false);\n  }, [setOpen]);\n\n  const toggle = useCallback(() => {\n    setOpen(!isOpen);\n  }, [setOpen, isOpen]);\n\n  return {\n    isOpen,\n    setOpen,\n    open,\n    close,\n    toggle\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAuBM,SAAS,0CAAuB,KAA0B;IAC/D,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAA,iLAAA,qBAAiB,EAAE,MAAM,MAAM,EAAE,MAAM,WAAW,IAAI,OAAO,MAAM,YAAY;IAEvG,MAAM,OAAO,CAAA,iKAAA,cAAU,EAAE;QACvB,QAAQ;IACV,GAAG;QAAC;KAAQ;IAEZ,MAAM,QAAQ,CAAA,iKAAA,cAAU,EAAE;QACxB,QAAQ;IACV,GAAG;QAAC;KAAQ;IAEZ,MAAM,SAAS,CAAA,iKAAA,cAAU,EAAE;QACzB,QAAQ,CAAC;IACX,GAAG;QAAC;QAAS;KAAO;IAEpB,OAAO;gBACL;iBACA;cACA;eACA;gBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/menu/dist/useMenuTriggerState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/menu/dist/packages/%40react-stately/menu/src/useMenuTriggerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusStrategy, Key} from '@react-types/shared';\nimport {MenuTriggerProps} from '@react-types/menu';\nimport {OverlayTriggerState, useOverlayTriggerState} from '@react-stately/overlays';\nimport {useState} from 'react';\n\nexport interface MenuTriggerState extends OverlayTriggerState {\n  /** Controls which item will be auto focused when the menu opens. */\n  readonly focusStrategy: FocusStrategy | null,\n\n  /** Opens the menu. */\n  open(focusStrategy?: FocusStrategy | null): void,\n\n  /** Toggles the menu. */\n  toggle(focusStrategy?: FocusStrategy | null): void\n}\n\nexport interface RootMenuTriggerState extends MenuTriggerState {\n  /** Opens a specific submenu tied to a specific menu item at a specific level. */\n  openSubmenu: (triggerKey: Key, level: number) => void,\n\n  /** Closes a specific submenu tied to a specific menu item at a specific level. */\n  closeSubmenu: (triggerKey: Key, level: number) => void,\n\n  /** An array of open submenu trigger keys within the menu tree.\n   * The index of key within array matches the submenu level in the tree.\n   */\n  expandedKeysStack: Key[],\n\n  /** Closes the menu and all submenus in the menu tree. */\n  close: () => void\n}\n\n/**\n * Manages state for a menu trigger. Tracks whether the menu is currently open,\n * and controls which item will receive focus when it opens. Also tracks the open submenus within\n * the menu tree via their trigger keys.\n */\nexport function useMenuTriggerState(props: MenuTriggerProps): RootMenuTriggerState  {\n  let overlayTriggerState = useOverlayTriggerState(props);\n  let [focusStrategy, setFocusStrategy] = useState<FocusStrategy | null>(null);\n  let [expandedKeysStack, setExpandedKeysStack] = useState<Key[]>([]);\n\n  let closeAll = () => {\n    setExpandedKeysStack([]);\n    overlayTriggerState.close();\n  };\n\n  let openSubmenu = (triggerKey: Key, level: number) => {\n    setExpandedKeysStack(oldStack => {\n      if (level > oldStack.length) {\n        return oldStack;\n      }\n\n      return [...oldStack.slice(0, level), triggerKey];\n    });\n  };\n\n  let closeSubmenu = (triggerKey: Key, level: number) => {\n    setExpandedKeysStack(oldStack => {\n      let key = oldStack[level];\n      if (key === triggerKey) {\n        return oldStack.slice(0, level);\n      } else {\n        return oldStack;\n      }\n    });\n  };\n\n  return {\n    focusStrategy,\n    ...overlayTriggerState,\n    open(focusStrategy: FocusStrategy | null = null) {\n      setFocusStrategy(focusStrategy);\n      overlayTriggerState.open();\n    },\n    toggle(focusStrategy: FocusStrategy | null = null) {\n      setFocusStrategy(focusStrategy);\n      overlayTriggerState.toggle();\n    },\n    close() {\n      closeAll();\n    },\n    expandedKeysStack,\n    openSubmenu,\n    closeSubmenu\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAuCM,SAAS,0CAAoB,KAAuB;IACzD,IAAI,sBAAsB,CAAA,wLAAA,yBAAqB,EAAE;IACjD,IAAI,CAAC,eAAe,iBAAiB,GAAG,CAAA,iKAAA,WAAO,EAAwB;IACvE,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,iKAAA,WAAO,EAAS,EAAE;IAElE,IAAI,WAAW;QACb,qBAAqB,EAAE;QACvB,oBAAoB,KAAK;IAC3B;IAEA,IAAI,cAAc,CAAC,YAAiB;QAClC,qBAAqB,CAAA;YACnB,IAAI,QAAQ,SAAS,MAAM,EACzB,OAAO;YAGT,OAAO;mBAAI,SAAS,KAAK,CAAC,GAAG;gBAAQ;aAAW;QAClD;IACF;IAEA,IAAI,eAAe,CAAC,YAAiB;QACnC,qBAAqB,CAAA;YACnB,IAAI,MAAM,QAAQ,CAAC,MAAM;YACzB,IAAI,QAAQ,YACV,OAAO,SAAS,KAAK,CAAC,GAAG;iBAEzB,OAAO;QAEX;IACF;IAEA,OAAO;uBACL;QACA,GAAG,mBAAmB;QACtB;gCAAK,iEAAsC,IAAI;YAC7C,iBAAiB;YACjB,oBAAoB,IAAI;QAC1B;QACA;gCAAO,iEAAsC,IAAI;YAC/C,iBAAiB;YACjB,oBAAoB,MAAM;QAC5B;QACA;YACE;QACF;2BACA;qBACA;sBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/collections/dist/Item.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/collections/dist/packages/%40react-stately/collections/src/Item.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ItemElement, ItemProps} from '@react-types/shared';\nimport {PartialNode} from './types';\nimport React, {JSX, ReactElement} from 'react';\n\nfunction Item<T>(props: ItemProps<T>): ReactElement | null { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return null;\n}\n\nItem.getCollectionNode = function* getCollectionNode<T>(props: ItemProps<T>, context: any): Generator<PartialNode<T>> {\n  let {childItems, title, children} = props;\n\n  let rendered = props.title || props.children;\n  let textValue = props.textValue || (typeof rendered === 'string' ? rendered : '') || props['aria-label'] || '';\n\n  // suppressTextValueWarning is used in components like Tabs, which don't have type to select support.\n  if (!textValue && !context?.suppressTextValueWarning && process.env.NODE_ENV !== 'production') {\n    console.warn('<Item> with non-plain text contents is unsupported by type to select for accessibility. Please add a `textValue` prop.');\n  }\n\n  yield {\n    type: 'item',\n    props: props,\n    rendered,\n    textValue,\n    'aria-label': props['aria-label'],\n    hasChildNodes: hasChildItems(props),\n    *childNodes() {\n      if (childItems) {\n        for (let child of childItems) {\n          yield {\n            type: 'item',\n            value: child\n          };\n        }\n      } else if (title) {\n        let items: PartialNode<T>[] = [];\n        React.Children.forEach(children, child => {\n          items.push({\n            type: 'item',\n            element: child as ItemElement<T>\n          });\n        });\n\n        yield* items;\n      }\n    }\n  };\n};\n\nfunction hasChildItems<T>(props: ItemProps<T>) {\n  if (props.hasChildItems != null) {\n    return props.hasChildItems;\n  }\n\n  if (props.childItems) {\n    return true;\n  }\n\n  if (props.title && React.Children.count(props.children) > 0) {\n    return true;\n  }\n\n  return false;\n}\n\n// We don't want getCollectionNode to show up in the type definition\nlet _Item = Item as <T>(props: ItemProps<T>) => JSX.Element;\nexport {_Item as Item};\n"], "names": [], "mappings": ";;;AA2B0D,QAAQ,GAAG,CAAC,QAAQ;;;AA3B9E;;;;;;;;;;CAUC,GAMD,SAAS,2BAAQ,KAAmB;IAClC,OAAO;AACT;AAEA,2BAAK,iBAAiB,GAAG,UAAU,kBAAqB,KAAmB,EAAE,OAAY;IACvF,IAAI,EAAA,YAAC,UAAU,EAAA,OAAE,KAAK,EAAA,UAAE,QAAQ,EAAC,GAAG;IAEpC,IAAI,WAAW,MAAM,KAAK,IAAI,MAAM,QAAQ;IAC5C,IAAI,YAAY,MAAM,SAAS,IAAK,CAAA,OAAO,aAAa,WAAW,WAAW,EAAC,KAAM,KAAK,CAAC,aAAa,IAAI;IAE5G,qGAAqG;IACrG,IAAI,CAAC,aAAa,CAAA,CAAC,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,wBAAwB,yDAA6B,cAC/E,QAAQ,IAAI,CAAC;IAGf,MAAM;QACJ,MAAM;QACN,OAAO;kBACP;mBACA;QACA,cAAc,KAAK,CAAC,aAAa;QACjC,eAAe,oCAAc;QAC7B,CAAC;YACC,IAAI,YACF,KAAK,IAAI,SAAS,WAChB,MAAM;gBACJ,MAAM;gBACN,OAAO;YACT;iBAEG,IAAI,OAAO;gBAChB,IAAI,QAA0B,EAAE;gBAChC,CAAA,iKAAA,UAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAA;oBAC/B,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,SAAS;oBACX;gBACF;gBAEA,OAAO;YACT;QACF;IACF;AACF;AAEA,SAAS,oCAAiB,KAAmB;IAC3C,IAAI,MAAM,aAAa,IAAI,MACzB,OAAO,MAAM,aAAa;IAG5B,IAAI,MAAM,UAAU,EAClB,OAAO;IAGT,IAAI,MAAM,KAAK,IAAI,CAAA,iKAAA,UAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,QAAQ,IAAI,GACxD,OAAO;IAGT,OAAO;AACT;AAEA,oEAAoE;AACpE,IAAI,4CAAQ", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/collections/dist/getChildNodes.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/collections/dist/packages/%40react-stately/collections/src/getChildNodes.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {Collection, Node} from '@react-types/shared';\n\nexport function getChildNodes<T>(node: Node<T>, collection: Collection<Node<T>>): Iterable<Node<T>> {\n  // New API: call collection.getChildren with the node key.\n  if (typeof collection.getChildren === 'function') {\n    return collection.getChildren(node.key);\n  }\n\n  // Old API: access childNodes directly.\n  return node.childNodes;\n}\n\nexport function getFirstItem<T>(iterable: Iterable<T>): T | undefined {\n  return getNthItem(iterable, 0);\n}\n\nexport function getNthItem<T>(iterable: Iterable<T>, index: number): T | undefined {\n  if (index < 0) {\n    return undefined;\n  }\n\n  let i = 0;\n  for (let item of iterable) {\n    if (i === index) {\n      return item;\n    }\n\n    i++;\n  }\n}\n\nexport function getLastItem<T>(iterable: Iterable<T>): T | undefined {\n  let lastItem: T | undefined = undefined;\n  for (let value of iterable) {\n    lastItem = value;\n  }\n\n  return lastItem;\n}\n\nexport function compareNodeOrder<T>(collection: Collection<Node<T>>, a: Node<T>, b: Node<T>): number {\n  // If the two nodes have the same parent, compare their indices.\n  if (a.parentKey === b.parentKey) {\n    return a.index - b.index;\n  }\n\n  // Otherwise, collect all of the ancestors from each node, and find the first one that doesn't match starting from the root.\n  // Include the base nodes in case we are comparing nodes of different levels so that we can compare the higher node to the lower level node's\n  // ancestor of the same level\n  let aAncestors = [...getAncestors(collection, a), a];\n  let bAncestors = [...getAncestors(collection, b), b];\n  let firstNonMatchingAncestor = aAncestors.slice(0, bAncestors.length).findIndex((a, i) => a !== bAncestors[i]);\n  if (firstNonMatchingAncestor !== -1) {\n    // Compare the indices of two children within the common ancestor.\n    a = aAncestors[firstNonMatchingAncestor];\n    b = bAncestors[firstNonMatchingAncestor];\n    return a.index - b.index;\n  }\n\n  // If there isn't a non matching ancestor, we might be in a case where one of the nodes is the ancestor of the other.\n  if (aAncestors.findIndex(node => node === b) >= 0) {\n    return 1;\n  } else if (bAncestors.findIndex(node => node === a) >= 0) {\n    return -1;\n  }\n\n  // 🤷\n  return -1;\n}\n\nfunction getAncestors<T>(collection: Collection<Node<T>>, node: Node<T>): Node<T>[] {\n  let parents: Node<T>[] = [];\n\n  let currNode: Node<T> | null = node;\n  while (currNode?.parentKey != null) {\n    currNode = collection.getItem(currNode.parentKey);\n    if (currNode) {\n      parents.unshift(currNode);\n    }\n  }\n\n  return parents;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;AAIM,SAAS,0CAAiB,IAAa,EAAE,UAA+B;IAC7E,0DAA0D;IAC1D,IAAI,OAAO,WAAW,WAAW,KAAK,YACpC,OAAO,WAAW,WAAW,CAAC,KAAK,GAAG;IAGxC,uCAAuC;IACvC,OAAO,KAAK,UAAU;AACxB;AAEO,SAAS,0CAAgB,QAAqB;IACnD,OAAO,0CAAW,UAAU;AAC9B;AAEO,SAAS,0CAAc,QAAqB,EAAE,KAAa;IAChE,IAAI,QAAQ,GACV,OAAO;IAGT,IAAI,IAAI;IACR,KAAK,IAAI,QAAQ,SAAU;QACzB,IAAI,MAAM,OACR,OAAO;QAGT;IACF;AACF;AAEO,SAAS,0CAAe,QAAqB;IAClD,IAAI,WAA0B;IAC9B,KAAK,IAAI,SAAS,SAChB,WAAW;IAGb,OAAO;AACT;AAEO,SAAS,yCAAoB,UAA+B,EAAE,CAAU,EAAE,CAAU;IACzF,gEAAgE;IAChE,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,EAC7B,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;IAG1B,4HAA4H;IAC5H,6IAA6I;IAC7I,6BAA6B;IAC7B,IAAI,aAAa;WAAI,mCAAa,YAAY;QAAI;KAAE;IACpD,IAAI,aAAa;WAAI,mCAAa,YAAY;QAAI;KAAE;IACpD,IAAI,2BAA2B,WAAW,KAAK,CAAC,GAAG,WAAW,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG,IAAM,MAAM,UAAU,CAAC,EAAE;IAC7G,IAAI,6BAA6B,CAAA,GAAI;QACnC,kEAAkE;QAClE,IAAI,UAAU,CAAC,yBAAyB;QACxC,IAAI,UAAU,CAAC,yBAAyB;QACxC,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;IAC1B;IAEA,qHAAqH;IACrH,IAAI,WAAW,SAAS,CAAC,CAAA,OAAQ,SAAS,MAAM,GAC9C,OAAO;SACF,IAAI,WAAW,SAAS,CAAC,CAAA,OAAQ,SAAS,MAAM,GACrD,OAAO,CAAA;IAGT,KAAK;IACL,OAAO,CAAA;AACT;AAEA,SAAS,mCAAgB,UAA+B,EAAE,IAAa;IACrE,IAAI,UAAqB,EAAE;IAE3B,IAAI,WAA2B;IAC/B,MAAO,CAAA,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,SAAS,KAAI,KAAM;QAClC,WAAW,WAAW,OAAO,CAAC,SAAS,SAAS;QAChD,IAAI,UACF,QAAQ,OAAO,CAAC;IAEpB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/collections/dist/getItemCount.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/collections/dist/packages/%40react-stately/collections/src/getItemCount.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Node} from '@react-types/shared';\nimport {getChildNodes} from './getChildNodes';\n\nconst cache = new WeakMap<Iterable<unknown>, number>();\n\nexport function getItemCount<T>(collection: Collection<Node<T>>): number {\n  let count = cache.get(collection);\n  if (count != null) {\n    return count;\n  }\n\n  // TS isn't smart enough to know we've ensured count is a number, so use a new variable\n  let counter = 0;\n  let countItems = (items: Iterable<Node<T>>) => {\n    for (let item of items) {\n      if (item.type === 'section') {\n        countItems(getChildNodes(item, collection));\n      } else if (item.type === 'item') {\n        counter++;\n      }\n    }\n  };\n\n  countItems(collection);\n  cache.set(collection, counter);\n  return counter;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAKD,MAAM,8BAAQ,IAAI;AAEX,SAAS,0CAAgB,UAA+B;IAC7D,IAAI,QAAQ,4BAAM,GAAG,CAAC;IACtB,IAAI,SAAS,MACX,OAAO;IAGT,uFAAuF;IACvF,IAAI,UAAU;IACd,IAAI,aAAa,CAAC;QAChB,KAAK,IAAI,QAAQ,MAAO;YACtB,IAAI,KAAK,IAAI,KAAK,WAChB,WAAW,CAAA,kLAAA,gBAAY,EAAE,MAAM;iBAC1B,IAAI,KAAK,IAAI,KAAK,QACvB;QAEJ;IACF;IAEA,WAAW;IACX,4BAAM,GAAG,CAAC,YAAY;IACtB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/collections/dist/CollectionBuilder.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/collections/dist/packages/%40react-stately/collections/src/CollectionBuilder.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CollectionBase, CollectionElement, Key, Node} from '@react-types/shared';\nimport {PartialNode} from './types';\nimport React, {ReactElement} from 'react';\n\ninterface CollectionBuilderState {\n  renderer?: (value: any) => ReactElement | null\n}\n\ninterface CollectReactElement<T> extends ReactElement {\n  getCollectionNode(props: any, context: any): Generator<PartialNode<T>, void, Node<T>[]>\n}\n\nexport class CollectionBuilder<T extends object> {\n  private context?: unknown;\n  private cache: WeakMap<T, Node<T>> = new WeakMap();\n\n  build(props: Partial<CollectionBase<T>>, context?: unknown): Iterable<Node<T>> {\n    this.context = context;\n    return iterable(() => this.iterateCollection(props));\n  }\n\n  private *iterateCollection(props: Partial<CollectionBase<T>>): Generator<Node<T>> {\n    let {children, items} = props;\n\n    if (React.isValidElement<{children: CollectionElement<T>}>(children) && children.type === React.Fragment) {\n      yield* this.iterateCollection({\n        children: children.props.children,\n        items\n      });\n    } else if (typeof children === 'function') {\n      if (!items) {\n        throw new Error('props.children was a function but props.items is missing');\n      }\n\n      let index = 0;\n      for (let item of items) {\n        yield* this.getFullNode({\n          value: item,\n          index\n        }, {renderer: children});\n        index++;\n      }\n    } else {\n      let items: CollectionElement<T>[] = [];\n      React.Children.forEach(children, child => {\n        if (child) {\n          items.push(child);\n        }\n      });\n\n      let index = 0;\n      for (let item of items) {\n        let nodes = this.getFullNode({\n          element: item,\n          index: index\n        }, {});\n\n        for (let node of nodes) {\n          index++;\n          yield node;\n        }\n      }\n    }\n  }\n\n  private getKey(item: NonNullable<CollectionElement<T>>, partialNode: PartialNode<T>, state: CollectionBuilderState, parentKey?: Key | null): Key {\n    if (item.key != null) {\n      return item.key;\n    }\n\n    if (partialNode.type === 'cell' && partialNode.key != null) {\n      return `${parentKey}${partialNode.key}`;\n    }\n\n    let v = partialNode.value as any;\n    if (v != null) {\n      let key = v.key ?? v.id;\n      if (key == null) {\n        throw new Error('No key found for item');\n      }\n\n      return key;\n    }\n\n    return parentKey ? `${parentKey}.${partialNode.index}` : `$.${partialNode.index}`;\n  }\n\n  private getChildState(state: CollectionBuilderState, partialNode: PartialNode<T>) {\n    return {\n      renderer: partialNode.renderer || state.renderer\n    };\n  }\n\n  private *getFullNode(partialNode: PartialNode<T> & {index: number}, state: CollectionBuilderState, parentKey?: Key | null, parentNode?: Node<T>): Generator<Node<T>> {\n    if (React.isValidElement<{children: CollectionElement<T>}>(partialNode.element) && partialNode.element.type === React.Fragment) {\n      let children: CollectionElement<T>[] = [];\n\n      React.Children.forEach(partialNode.element.props.children, child => {\n        children.push(child);\n      });\n\n      let index = partialNode.index ?? 0;\n\n      for (const child of children) {\n        yield* this.getFullNode({\n          element: child,\n          index: index++\n        }, state, parentKey, parentNode);\n      }\n\n      return;\n    }\n\n    // If there's a value instead of an element on the node, and a parent renderer function is available,\n    // use it to render an element for the value.\n    let element = partialNode.element;\n    if (!element && partialNode.value && state && state.renderer) {\n      let cached = this.cache.get(partialNode.value);\n      if (cached && (!cached.shouldInvalidate || !cached.shouldInvalidate(this.context))) {\n        cached.index = partialNode.index;\n        cached.parentKey = parentNode ? parentNode.key : null;\n        yield cached;\n        return;\n      }\n\n      element = state.renderer(partialNode.value);\n    }\n\n    // If there's an element with a getCollectionNode function on its type, then it's a supported component.\n    // Call this function to get a partial node, and recursively build a full node from there.\n    if (React.isValidElement(element)) {\n      let type = element.type as unknown as CollectReactElement<T>;\n      if (typeof type !== 'function' && typeof type.getCollectionNode !== 'function') {\n        let name = element.type;\n        throw new Error(`Unknown element <${name}> in collection.`);\n      }\n\n      let childNodes = type.getCollectionNode(element.props, this.context) as Generator<PartialNode<T>, void, Node<T>[]>;\n      let index = partialNode.index ?? 0;\n      let result = childNodes.next();\n      while (!result.done && result.value) {\n        let childNode = result.value;\n\n        partialNode.index = index;\n\n        let nodeKey = childNode.key ?? null;\n        if (nodeKey == null) {\n          nodeKey = childNode.element ? null : this.getKey(element as NonNullable<CollectionElement<T>>, partialNode, state, parentKey);\n        }\n\n        let nodes = this.getFullNode({\n          ...childNode,\n          key: nodeKey,\n          index,\n          wrapper: compose(partialNode.wrapper, childNode.wrapper)\n        }, this.getChildState(state, childNode), parentKey ? `${parentKey}${element.key}` : element.key, parentNode);\n\n        let children = [...nodes];\n        for (let node of children) {\n          // Cache the node based on its value\n          node.value = childNode.value ?? partialNode.value ?? null;\n          if (node.value) {\n            this.cache.set(node.value, node);\n          }\n\n          // The partial node may have specified a type for the child in order to specify a constraint.\n          // Verify that the full node that was built recursively matches this type.\n          if (partialNode.type && node.type !== partialNode.type) {\n            throw new Error(`Unsupported type <${capitalize(node.type)}> in <${capitalize(parentNode?.type ?? 'unknown parent type')}>. Only <${capitalize(partialNode.type)}> is supported.`);\n          }\n\n          index++;\n          yield node;\n        }\n\n        result = childNodes.next(children);\n      }\n\n      return;\n    }\n\n    // Ignore invalid elements\n    if (partialNode.key == null || partialNode.type == null) {\n      return;\n    }\n\n    // Create full node\n    let builder = this;\n    let node: Node<T> = {\n      type: partialNode.type,\n      props: partialNode.props,\n      key: partialNode.key,\n      parentKey: parentNode ? parentNode.key : null,\n      value: partialNode.value ?? null,\n      level: parentNode ? parentNode.level + 1 : 0,\n      index: partialNode.index,\n      rendered: partialNode.rendered,\n      textValue: partialNode.textValue ?? '',\n      'aria-label': partialNode['aria-label'],\n      wrapper: partialNode.wrapper,\n      shouldInvalidate: partialNode.shouldInvalidate,\n      hasChildNodes: partialNode.hasChildNodes || false,\n      childNodes: iterable(function *() {\n        if (!partialNode.hasChildNodes || !partialNode.childNodes) {\n          return;\n        }\n\n        let index = 0;\n        for (let child of partialNode.childNodes()) {\n          // Ensure child keys are globally unique by prepending the parent node's key\n          if (child.key != null) {\n            // TODO: Remove this line entirely and enforce that users always provide unique keys.\n            // Currently this line will have issues when a parent has a key `a` and a child with key `bc`\n            // but another parent has key `ab` and its child has a key `c`. The combined keys would result in both\n            // children having a key of `abc`.\n            child.key = `${node.key}${child.key}`;\n          }\n\n          let nodes = builder.getFullNode({...child, index}, builder.getChildState(state, child), node.key, node);\n          for (let node of nodes) {\n            index++;\n            yield node;\n          }\n        }\n      })\n    };\n\n    yield node;\n  }\n}\n\n// Wraps an iterator function as an iterable object, and caches the results.\nfunction iterable<T>(iterator: () => IterableIterator<Node<T>>): Iterable<Node<T>> {\n  let cache: Array<Node<T>> = [];\n  let iterable: null | IterableIterator<Node<T>> = null;\n  return {\n    *[Symbol.iterator]() {\n      for (let item of cache) {\n        yield item;\n      }\n\n      if (!iterable) {\n        iterable = iterator();\n      }\n\n      for (let item of iterable) {\n        cache.push(item);\n        yield item;\n      }\n    }\n  };\n}\n\ntype Wrapper = (element: ReactElement) => ReactElement;\nfunction compose(outer: Wrapper | void, inner: Wrapper | void): Wrapper | undefined {\n  if (outer && inner) {\n    return (element) => outer(inner(element));\n  }\n\n  if (outer) {\n    return outer;\n  }\n\n  if (inner) {\n    return inner;\n  }\n}\n\nfunction capitalize(str: string) {\n  return str[0].toUpperCase() + str.slice(1);\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAcM,MAAM;IAIX,MAAM,KAAiC,EAAE,OAAiB,EAAqB;QAC7E,IAAI,CAAC,OAAO,GAAG;QACf,OAAO,+BAAS,IAAM,IAAI,CAAC,iBAAiB,CAAC;IAC/C;IAEA,CAAS,kBAAkB,KAAiC,EAAsB;QAChF,IAAI,EAAA,UAAC,QAAQ,EAAA,OAAE,KAAK,EAAC,GAAG;QAExB,IAAI,CAAA,iKAAA,UAAI,EAAE,cAAc,CAAmC,aAAa,SAAS,IAAI,KAAK,CAAA,iKAAA,UAAI,EAAE,QAAQ,EACtG,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAC5B,UAAU,SAAS,KAAK,CAAC,QAAQ;mBACjC;QACF;aACK,IAAI,OAAO,aAAa,YAAY;YACzC,IAAI,CAAC,OACH,MAAM,IAAI,MAAM;YAGlB,IAAI,QAAQ;YACZ,KAAK,IAAI,QAAQ,MAAO;gBACtB,OAAO,IAAI,CAAC,WAAW,CAAC;oBACtB,OAAO;2BACP;gBACF,GAAG;oBAAC,UAAU;gBAAQ;gBACtB;YACF;QACF,OAAO;YACL,IAAI,QAAgC,EAAE;YACtC,CAAA,iKAAA,UAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAA;gBAC/B,IAAI,OACF,MAAM,IAAI,CAAC;YAEf;YAEA,IAAI,QAAQ;YACZ,KAAK,IAAI,QAAQ,MAAO;gBACtB,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC;oBAC3B,SAAS;oBACT,OAAO;gBACT,GAAG,CAAC;gBAEJ,KAAK,IAAI,QAAQ,MAAO;oBACtB;oBACA,MAAM;gBACR;YACF;QACF;IACF;IAEQ,OAAO,IAAuC,EAAE,WAA2B,EAAE,KAA6B,EAAE,SAAsB,EAAO;QAC/I,IAAI,KAAK,GAAG,IAAI,MACd,OAAO,KAAK,GAAG;QAGjB,IAAI,YAAY,IAAI,KAAK,UAAU,YAAY,GAAG,IAAI,MACpD,OAAO,UAAG,WAA2B,CAAE,MAAjB,YAAY,GAAG;QAGvC,IAAI,IAAI,YAAY,KAAK;QACzB,IAAI,KAAK,MAAM;gBACH;YAAV,IAAI,MAAM,CAAA,SAAA,EAAE,GAAG,MAAA,QAAL,WAAA,KAAA,IAAA,SAAS,EAAE,EAAE;YACvB,IAAI,OAAO,MACT,MAAM,IAAI,MAAM;YAGlB,OAAO;QACT;QAEA,OAAO,YAAY,GAAgB,OAAb,WAAU,CAAC,IAAmB,CAAE,kBAAP,KAAK,IAAK,AAAC,EAAE,GAAmB,CAAE,MAAnB,YAAY,KAAK;IACjF;IAEQ,cAAc,KAA6B,EAAE,WAA2B,EAAE;QAChF,OAAO;YACL,UAAU,YAAY,QAAQ,IAAI,MAAM,QAAQ;QAClD;IACF;IAEA,CAAS,YAAY,WAA6C,EAAE,KAA6B,EAAE,SAAsB,EAAE,UAAoB,EAAsB;QACnK,IAAI,CAAA,iKAAA,UAAI,EAAE,cAAc,CAAmC,YAAY,OAAO,KAAK,YAAY,OAAO,CAAC,IAAI,KAAK,CAAA,iKAAA,UAAI,EAAE,QAAQ,EAAE;YAC9H,IAAI,WAAmC,EAAE;YAEzC,CAAA,iKAAA,UAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAA;gBACzD,SAAS,IAAI,CAAC;YAChB;gBAEY;YAAZ,IAAI,QAAQ,CAAA,qBAAA,YAAY,KAAK,MAAA,QAAjB,uBAAA,KAAA,IAAA,qBAAqB;YAEjC,KAAK,MAAM,SAAS,SAClB,OAAO,IAAI,CAAC,WAAW,CAAC;gBACtB,SAAS;gBACT,OAAO;YACT,GAAG,OAAO,WAAW;YAGvB;QACF;QAEA,qGAAqG;QACrG,6CAA6C;QAC7C,IAAI,UAAU,YAAY,OAAO;QACjC,IAAI,CAAC,WAAW,YAAY,KAAK,IAAI,SAAS,MAAM,QAAQ,EAAE;YAC5D,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,KAAK;YAC7C,IAAI,UAAW,CAAA,CAAC,OAAO,gBAAgB,IAAI,CAAC,OAAO,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAA,GAAI;gBAClF,OAAO,KAAK,GAAG,YAAY,KAAK;gBAChC,OAAO,SAAS,GAAG,aAAa,WAAW,GAAG,GAAG;gBACjD,MAAM;gBACN;YACF;YAEA,UAAU,MAAM,QAAQ,CAAC,YAAY,KAAK;QAC5C;QAEA,wGAAwG;QACxG,0FAA0F;QAC1F,IAAI,CAAA,iKAAA,UAAI,EAAE,cAAc,CAAC,UAAU;YACjC,IAAI,OAAO,QAAQ,IAAI;YACvB,IAAI,OAAO,SAAS,cAAc,OAAO,KAAK,iBAAiB,KAAK,YAAY;gBAC9E,IAAI,OAAO,QAAQ,IAAI;gBACvB,MAAM,IAAI,MAAM,AAAC,iBAAiB,UAAE,MAAK,gBAAgB,CAAC;YAC5D;YAEA,IAAI,aAAa,KAAK,iBAAiB,CAAC,QAAQ,KAAK,EAAE,IAAI,CAAC,OAAO;gBACvD;YAAZ,IAAI,QAAQ,CAAA,sBAAA,YAAY,KAAK,MAAA,QAAjB,wBAAA,KAAA,IAAA,sBAAqB;YACjC,IAAI,SAAS,WAAW,IAAI;YAC5B,MAAO,CAAC,OAAO,IAAI,IAAI,OAAO,KAAK,CAAE;gBACnC,IAAI,YAAY,OAAO,KAAK;gBAE5B,YAAY,KAAK,GAAG;oBAEN;gBAAd,IAAI,UAAU,CAAA,iBAAA,UAAU,GAAG,MAAA,QAAb,mBAAA,KAAA,IAAA,iBAAiB;gBAC/B,IAAI,WAAW,MACb,UAAU,UAAU,OAAO,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,SAA8C,aAAa,OAAO;gBAGrH,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC;oBAC3B,GAAG,SAAS;oBACZ,KAAK;2BACL;oBACA,SAAS,8BAAQ,YAAY,OAAO,EAAE,UAAU,OAAO;gBACzD,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,YAAY,YAAY,GAAe,OAAZ,WAAuB,CAAE,cAAL,GAAG,IAAK,QAAQ,GAAG,EAAE;gBAEjG,IAAI,WAAW;uBAAI;iBAAM;gBACzB,KAAK,IAAI,QAAQ,SAAU;wBAEZ,kBAAA;oBADb,oCAAoC;oBACpC,KAAK,KAAK,GAAG,CAAA,OAAA,CAAA,mBAAA,UAAU,KAAK,MAAA,QAAf,qBAAA,KAAA,IAAA,mBAAmB,YAAY,KAAK,MAAA,QAApC,SAAA,KAAA,IAAA,OAAwC;oBACrD,IAAI,KAAK,KAAK,EACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE;wBAMmD;oBAHhF,6FAA6F;oBAC7F,0EAA0E;oBAC1E,IAAI,YAAY,IAAI,IAAI,KAAK,IAAI,KAAK,YAAY,IAAI,EACpD,MAAM,IAAI,MAAM,AAAC,kBAAkB,UAAE,iCAAW,KAAK,IAAI,GAAE,MAAM,IAAmE,OAAjE,iCAAW,CAAA,mBAAA,eAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAY,IAAI,MAAA,QAAhB,qBAAA,KAAA,IAAA,mBAAoB,wBAAuB,SAAS,4CAAa,YAAY,IAAI,GAAE,eAAe,CAAC;oBAGnL;oBACA,MAAM;gBACR;gBAEA,SAAS,WAAW,IAAI,CAAC;YAC3B;YAEA;QACF;QAEA,0BAA0B;QAC1B,IAAI,YAAY,GAAG,IAAI,QAAQ,YAAY,IAAI,IAAI,MACjD;QAGF,mBAAmB;QACnB,IAAI,UAAU,IAAI;YAMT,oBAII;QATb,IAAI,OAAgB;YAClB,MAAM,YAAY,IAAI;YACtB,OAAO,YAAY,KAAK;YACxB,KAAK,YAAY,GAAG;YACpB,WAAW,aAAa,WAAW,GAAG,GAAG;YACzC,OAAO,CAAA,qBAAA,YAAY,KAAK,MAAA,QAAjB,uBAAA,KAAA,IAAA,qBAAqB;YAC5B,OAAO,aAAa,WAAW,KAAK,GAAG,IAAI;YAC3C,OAAO,YAAY,KAAK;YACxB,UAAU,YAAY,QAAQ;YAC9B,WAAW,CAAA,yBAAA,YAAY,SAAS,MAAA,QAArB,2BAAA,KAAA,IAAA,yBAAyB;YACpC,cAAc,WAAW,CAAC,aAAa;YACvC,SAAS,YAAY,OAAO;YAC5B,kBAAkB,YAAY,gBAAgB;YAC9C,eAAe,YAAY,aAAa,IAAI;YAC5C,YAAY,+BAAS;gBACnB,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,YAAY,UAAU,EACvD;gBAGF,IAAI,QAAQ;gBACZ,KAAK,IAAI,SAAS,YAAY,UAAU,GAAI;oBAC1C,4EAA4E;oBAC5E,IAAI,MAAM,GAAG,IAAI,MACf,AACA,qFADqF,QACQ;oBAC7F,sGAAsG;oBACtG,kCAAkC;oBAClC,MAAM,GAAG,GAAG,GAAc,OAAX,KAAK,GAAG,EAAY,CAAE,YAAL,GAAG;oBAGrC,IAAI,QAAQ,QAAQ,WAAW,CAAC;wBAAC,GAAG,KAAK;+BAAE;oBAAK,GAAG,QAAQ,aAAa,CAAC,OAAO,QAAQ,KAAK,GAAG,EAAE;oBAClG,KAAK,IAAI,QAAQ,MAAO;wBACtB;wBACA,MAAM;oBACR;gBACF;YACF;QACF;QAEA,MAAM;IACR;;aAtNQ,KAAA,GAA6B,IAAI;;AAuN3C;AAEA,4EAA4E;AAC5E,SAAS,+BAAY,QAAyC;IAC5D,IAAI,QAAwB,EAAE;IAC9B,IAAI,WAA6C;IACjD,OAAO;QACL,CAAC,CAAC,OAAO,QAAQ,CAAC;YAChB,KAAK,IAAI,QAAQ,MACf,MAAM;YAGR,IAAI,CAAC,UACH,WAAW;YAGb,KAAK,IAAI,QAAQ,SAAU;gBACzB,MAAM,IAAI,CAAC;gBACX,MAAM;YACR;QACF;IACF;AACF;AAGA,SAAS,8BAAQ,KAAqB,EAAE,KAAqB;IAC3D,IAAI,SAAS,OACX,OAAO,CAAC,UAAY,MAAM,MAAM;IAGlC,IAAI,OACF,OAAO;IAGT,IAAI,OACF,OAAO;AAEX;AAEA,SAAS,iCAAW,GAAW;IAC7B,OAAO,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,KAAK,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/collections/dist/useCollection.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/collections/dist/packages/%40react-stately/collections/src/useCollection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, CollectionStateBase, Node} from '@react-types/shared';\nimport {CollectionBuilder} from './CollectionBuilder';\nimport {ReactElement, useMemo} from 'react';\n\ninterface CollectionOptions<T, C extends Collection<Node<T>>> extends Omit<CollectionStateBase<T, C>, 'children'> {\n  children?: ReactElement<any> | null | (ReactElement<any> | null)[] | ((item: T) => ReactElement<any> | null)\n}\n\ntype CollectionFactory<T, C extends Collection<Node<T>>> = (node: Iterable<Node<T>>) => C;\n\nexport function useCollection<T extends object, C extends Collection<Node<T>> = Collection<Node<T>>>(props: CollectionOptions<T, C>, factory: CollectionFactory<T, C>, context?: unknown): C {\n  let builder = useMemo(() => new CollectionBuilder<T>(), []);\n  let {children, items, collection} = props;\n  let result = useMemo(() => {\n    if (collection) {\n      return collection;\n    }\n    let nodes = builder.build({children, items}, context);\n    return factory(nodes);\n  }, [builder, children, items, collection, context, factory]);\n  return result;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAYM,SAAS,0CAAqF,KAA8B,EAAE,OAAgC,EAAE,OAAiB;IACtL,IAAI,UAAU,CAAA,iKAAA,UAAM,EAAE,IAAM,IAAI,CAAA,sLAAA,oBAAgB,KAAQ,EAAE;IAC1D,IAAI,EAAA,UAAC,QAAQ,EAAA,OAAE,KAAK,EAAA,YAAE,UAAU,EAAC,GAAG;IACpC,IAAI,SAAS,CAAA,iKAAA,UAAM,EAAE;QACnB,IAAI,YACF,OAAO;QAET,IAAI,QAAQ,QAAQ,KAAK,CAAC;sBAAC;mBAAU;QAAK,GAAG;QAC7C,OAAO,QAAQ;IACjB,GAAG;QAAC;QAAS;QAAU;QAAO;QAAY;QAAS;KAAQ;IAC3D,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/tree/dist/TreeCollection.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/tree/dist/packages/%40react-stately/tree/src/TreeCollection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Key, Node} from '@react-types/shared';\n\nexport class TreeCollection<T> implements Collection<Node<T>> {\n  private keyMap: Map<Key, Node<T>> = new Map();\n  private iterable: Iterable<Node<T>>;\n  private firstKey: Key | null = null;\n  private lastKey: Key | null = null;\n\n  constructor(nodes: Iterable<Node<T>>, {expandedKeys}: {expandedKeys?: Set<Key>} = {}) {\n    this.iterable = nodes;\n    expandedKeys = expandedKeys || new Set();\n\n    let visit = (node: Node<T>) => {\n      this.keyMap.set(node.key, node);\n\n      if (node.childNodes && (node.type === 'section' || expandedKeys.has(node.key))) {\n        for (let child of node.childNodes) {\n          visit(child);\n        }\n      }\n    };\n\n    for (let node of nodes) {\n      visit(node);\n    }\n\n    let last: Node<T> | null = null;\n    let index = 0;\n    for (let [key, node] of this.keyMap) {\n      if (last) {\n        last.nextKey = key;\n        node.prevKey = last.key;\n      } else {\n        this.firstKey = key;\n        node.prevKey = undefined;\n      }\n\n      if (node.type === 'item') {\n        node.index = index++;\n      }\n\n      last = node;\n\n      // Set nextKey as undefined since this might be the last node\n      // If it isn't the last node, last.nextKey will properly set at start of new loop\n      last.nextKey = undefined;\n    }\n\n    this.lastKey = last?.key ?? null;\n  }\n\n  *[Symbol.iterator](): IterableIterator<Node<T>> {\n    yield* this.iterable;\n  }\n\n  get size(): number {\n    return this.keyMap.size;\n  }\n\n  getKeys(): IterableIterator<Key> {\n    return this.keyMap.keys();\n  }\n\n  getKeyBefore(key: Key): Key | null {\n    let node = this.keyMap.get(key);\n    return node ? node.prevKey ?? null : null;\n  }\n\n  getKeyAfter(key: Key): Key | null {\n    let node = this.keyMap.get(key);\n    return node ? node.nextKey ?? null : null;\n  }\n\n  getFirstKey(): Key | null {\n    return this.firstKey;\n  }\n\n  getLastKey(): Key | null {\n    return this.lastKey;\n  }\n\n  getItem(key: Key): Node<T> | null {\n    return this.keyMap.get(key) ?? null;\n  }\n\n  at(idx: number): Node<T> | null {\n    const keys = [...this.getKeys()];\n    return this.getItem(keys[idx]);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAIM,MAAM;IAiDX,CAAC,CAAC,OAAO,QAAQ,CAAC,GAA8B;QAC9C,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA,UAAiC;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA,aAAa,GAAQ,EAAc;QACjC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACb;QAAd,OAAO,OAAO,CAAA,gBAAA,KAAK,OAAO,MAAA,QAAZ,kBAAA,KAAA,IAAA,gBAAgB,OAAO;IACvC;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACb;QAAd,OAAO,OAAO,CAAA,gBAAA,KAAK,OAAO,MAAA,QAAZ,kBAAA,KAAA,IAAA,gBAAgB,OAAO;IACvC;IAEA,cAA0B;QACxB,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,aAAyB;QACvB,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,QAAQ,GAAQ,EAAkB;YACzB;QAAP,OAAO,CAAA,mBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAA,MAAA,QAAhB,qBAAA,KAAA,IAAA,mBAAwB;IACjC;IAEA,GAAG,GAAW,EAAkB;QAC9B,MAAM,OAAO;eAAI,IAAI,CAAC,OAAO;SAAG;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IAC/B;IAhFA,YAAY,KAAwB,EAAE,EAAA,cAAC,YAAY,EAA4B,GAAG,CAAC,CAAC,CAAE;aAL9E,MAAA,GAA4B,IAAI;aAEhC,QAAA,GAAuB;aACvB,OAAA,GAAsB;QAG5B,IAAI,CAAC,QAAQ,GAAG;QAChB,eAAe,gBAAgB,IAAI;QAEnC,IAAI,QAAQ,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;YAE1B,IAAI,KAAK,UAAU,IAAK,CAAA,KAAK,IAAI,KAAK,aAAa,aAAa,GAAG,CAAC,KAAK,GAAG,CAAA,GAC1E,KAAK,IAAI,SAAS,KAAK,UAAU,CAC/B,MAAM;QAGZ;QAEA,KAAK,IAAI,QAAQ,MACf,MAAM;QAGR,IAAI,OAAuB;QAC3B,IAAI,QAAQ;QACZ,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE;YACnC,IAAI,MAAM;gBACR,KAAK,OAAO,GAAG;gBACf,KAAK,OAAO,GAAG,KAAK,GAAG;YACzB,OAAO;gBACL,IAAI,CAAC,QAAQ,GAAG;gBAChB,KAAK,OAAO,GAAG;YACjB;YAEA,IAAI,KAAK,IAAI,KAAK,QAChB,KAAK,KAAK,GAAG;YAGf,OAAO;YAEP,6DAA6D;YAC7D,iFAAiF;YACjF,KAAK,OAAO,GAAG;QACjB;YAEe;QAAf,IAAI,CAAC,OAAO,GAAG,CAAA,YAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,GAAG,MAAA,QAAT,cAAA,KAAA,IAAA,YAAa;IAC9B;AAwCF", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/tree/dist/useTreeState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/tree/dist/packages/%40react-stately/tree/src/useTreeState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, CollectionStateBase, DisabledBehavior, Expandable, Key, MultipleSelection, Node} from '@react-types/shared';\nimport {SelectionManager, useMultipleSelectionState} from '@react-stately/selection';\nimport {TreeCollection} from './TreeCollection';\nimport {useCallback, useEffect, useMemo} from 'react';\nimport {useCollection} from '@react-stately/collections';\nimport {useControlledState} from '@react-stately/utils';\n\nexport interface TreeProps<T> extends CollectionStateBase<T>, Expandable, MultipleSelection {\n  /** Whether `disabledKeys` applies to all interactions, or only selection. */\n  disabledBehavior?: DisabledBehavior\n}\nexport interface TreeState<T> {\n  /** A collection of items in the tree. */\n  readonly collection: Collection<Node<T>>,\n\n  /** A set of keys for items that are disabled. */\n  readonly disabledKeys: Set<Key>,\n\n  /** A set of keys for items that are expanded. */\n  readonly expandedKeys: Set<Key>,\n\n  /** Toggles the expanded state for an item by its key. */\n  toggleKey(key: Key): void,\n\n  /** Replaces the set of expanded keys. */\n  setExpandedKeys(keys: Set<Key>): void,\n\n  /** A selection manager to read and update multiple selection state. */\n  readonly selectionManager: SelectionManager\n}\n\n/**\n * Provides state management for tree-like components. Handles building a collection\n * of items from props, item expanded state, and manages multiple selection state.\n */\nexport function useTreeState<T extends object>(props: TreeProps<T>): TreeState<T> {\n  let {\n    onExpandedChange\n  } = props;\n\n  let [expandedKeys, setExpandedKeys] = useControlledState(\n    props.expandedKeys ? new Set(props.expandedKeys) : undefined,\n    props.defaultExpandedKeys ? new Set(props.defaultExpandedKeys) : new Set(),\n    onExpandedChange\n  );\n\n  let selectionState = useMultipleSelectionState(props);\n  let disabledKeys = useMemo(() =>\n    props.disabledKeys ? new Set(props.disabledKeys) : new Set<Key>()\n  , [props.disabledKeys]);\n\n  let tree = useCollection(props, useCallback(nodes => new TreeCollection(nodes, {expandedKeys}), [expandedKeys]), null);\n\n  // Reset focused key if that item is deleted from the collection.\n  useEffect(() => {\n    if (selectionState.focusedKey != null && !tree.getItem(selectionState.focusedKey)) {\n      selectionState.setFocusedKey(null);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [tree, selectionState.focusedKey]);\n\n  let onToggle = (key: Key) => {\n    setExpandedKeys(toggleKey(expandedKeys, key));\n  };\n\n  return {\n    collection: tree,\n    expandedKeys,\n    disabledKeys,\n    toggleKey: onToggle,\n    setExpandedKeys,\n    selectionManager: new SelectionManager(tree, selectionState)\n  };\n}\n\nfunction toggleKey(set: Set<Key>, key: Key): Set<Key> {\n  let res = new Set(set);\n  if (res.has(key)) {\n    res.delete(key);\n  } else {\n    res.add(key);\n  }\n\n  return res;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqCM,SAAS,0CAA+B,KAAmB;IAChE,IAAI,EAAA,kBACF,gBAAgB,EACjB,GAAG;IAEJ,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,iLAAA,qBAAiB,EACrD,MAAM,YAAY,GAAG,IAAI,IAAI,MAAM,YAAY,IAAI,WACnD,MAAM,mBAAmB,GAAG,IAAI,IAAI,MAAM,mBAAmB,IAAI,IAAI,OACrE;IAGF,IAAI,iBAAiB,CAAA,4LAAA,4BAAwB,EAAE;IAC/C,IAAI,eAAe,CAAA,iKAAA,UAAM,EAAE,IACzB,MAAM,YAAY,GAAG,IAAI,IAAI,MAAM,YAAY,IAAI,IAAI,OACvD;QAAC,MAAM,YAAY;KAAC;IAEtB,IAAI,OAAO,CAAA,kLAAA,gBAAY,EAAE,OAAO,CAAA,iKAAA,cAAU,EAAE,CAAA,QAAS,IAAI,CAAA,4KAAA,iBAAa,EAAE,OAAO;0BAAC;QAAY,IAAI;QAAC;KAAa,GAAG;IAEjH,iEAAiE;IACjE,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,eAAe,UAAU,IAAI,QAAQ,CAAC,KAAK,OAAO,CAAC,eAAe,UAAU,GAC9E,eAAe,aAAa,CAAC;IAEjC,uDAAuD;IACvD,GAAG;QAAC;QAAM,eAAe,UAAU;KAAC;IAEpC,IAAI,WAAW,CAAC;QACd,gBAAgB,gCAAU,cAAc;IAC1C;IAEA,OAAO;QACL,YAAY;sBACZ;sBACA;QACA,WAAW;yBACX;QACA,kBAAkB,IAAI,CAAA,mLAAA,mBAAe,EAAE,MAAM;IAC/C;AACF;AAEA,SAAS,gCAAU,GAAa,EAAE,GAAQ;IACxC,IAAI,MAAM,IAAI,IAAI;IAClB,IAAI,IAAI,GAAG,CAAC,MACV,IAAI,MAAM,CAAC;SAEX,IAAI,GAAG,CAAC;IAGV,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/selection/dist/Selection.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/selection/dist/packages/%40react-stately/selection/src/Selection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\n\n/**\n * A Selection is a special Set containing Keys, which also has an anchor\n * and current selected key for use when range selecting.\n */\nexport class Selection extends Set<Key> {\n  anchorKey: Key | null;\n  currentKey: Key | null;\n\n  constructor(keys?: Iterable<Key> | Selection, anchorKey?: Key | null, currentKey?: Key | null) {\n    super(keys);\n    if (keys instanceof Selection) {\n      this.anchorKey = anchorKey ?? keys.anchorKey;\n      this.currentKey = currentKey ?? keys.currentKey;\n    } else {\n      this.anchorKey = anchorKey ?? null;\n      this.currentKey = currentKey ?? null;\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAQM,MAAM,kDAAkB;IAI7B,YAAY,IAAgC,EAAE,SAAsB,EAAE,UAAuB,CAAE;QAC7F,KAAK,CAAC;QACN,IAAI,gBAAgB,2CAAW;YAC7B,IAAI,CAAC,SAAS,GAAG,cAAA,QAAA,cAAA,KAAA,IAAA,YAAa,KAAK,SAAS;YAC5C,IAAI,CAAC,UAAU,GAAG,eAAA,QAAA,eAAA,KAAA,IAAA,aAAc,KAAK,UAAU;QACjD,OAAO;YACL,IAAI,CAAC,SAAS,GAAG,cAAA,QAAA,cAAA,KAAA,IAAA,YAAa;YAC9B,IAAI,CAAC,UAAU,GAAG,eAAA,QAAA,eAAA,KAAA,IAAA,aAAc;QAClC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/selection/dist/useMultipleSelectionState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/selection/dist/packages/%40react-stately/selection/src/useMultipleSelectionState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DisabledBehavior, FocusStrategy, Key, MultipleSelection, SelectionBehavior, SelectionMode} from '@react-types/shared';\nimport {MultipleSelectionState} from './types';\nimport {Selection} from './Selection';\nimport {useControlledState} from '@react-stately/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nfunction equalSets(setA, setB) {\n  if (setA.size !== setB.size) {\n    return false;\n  }\n\n  for (let item of setA) {\n    if (!setB.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexport interface MultipleSelectionStateProps extends MultipleSelection {\n  /** How multiple selection should behave in the collection. */\n  selectionBehavior?: SelectionBehavior,\n  /** Whether onSelectionChange should fire even if the new set of keys is the same as the last. */\n  allowDuplicateSelectionEvents?: boolean,\n  /** Whether `disabledKeys` applies to all interactions, or only selection. */\n  disabledBehavior?: DisabledBehavior\n}\n\n/**\n * Manages state for multiple selection and focus in a collection.\n */\nexport function useMultipleSelectionState(props: MultipleSelectionStateProps): MultipleSelectionState {\n  let {\n    selectionMode = 'none' as SelectionMode,\n    disallowEmptySelection = false,\n    allowDuplicateSelectionEvents,\n    selectionBehavior: selectionBehaviorProp = 'toggle',\n    disabledBehavior = 'all'\n  } = props;\n\n  // We want synchronous updates to `isFocused` and `focusedKey` after their setters are called.\n  // But we also need to trigger a react re-render. So, we have both a ref (sync) and state (async).\n  let isFocusedRef = useRef(false);\n  let [, setFocused] = useState(false);\n  let focusedKeyRef = useRef<Key | null>(null);\n  let childFocusStrategyRef = useRef<FocusStrategy | null>(null);\n  let [, setFocusedKey] = useState<Key | null>(null);\n  let selectedKeysProp = useMemo(() => convertSelection(props.selectedKeys), [props.selectedKeys]);\n  let defaultSelectedKeys = useMemo(() => convertSelection(props.defaultSelectedKeys, new Selection()), [props.defaultSelectedKeys]);\n  let [selectedKeys, setSelectedKeys] = useControlledState(\n    selectedKeysProp,\n    defaultSelectedKeys!,\n    props.onSelectionChange\n  );\n  let disabledKeysProp = useMemo(() =>\n    props.disabledKeys ? new Set(props.disabledKeys) : new Set<Key>()\n  , [props.disabledKeys]);\n  let [selectionBehavior, setSelectionBehavior] = useState(selectionBehaviorProp);\n\n  // If the selectionBehavior prop is set to replace, but the current state is toggle (e.g. due to long press\n  // to enter selection mode on touch), and the selection becomes empty, reset the selection behavior.\n  if (selectionBehaviorProp === 'replace' && selectionBehavior === 'toggle' && typeof selectedKeys === 'object' && selectedKeys.size === 0) {\n    setSelectionBehavior('replace');\n  }\n\n  // If the selectionBehavior prop changes, update the state as well.\n  let lastSelectionBehavior = useRef(selectionBehaviorProp);\n  useEffect(() => {\n    if (selectionBehaviorProp !== lastSelectionBehavior.current) {\n      setSelectionBehavior(selectionBehaviorProp);\n      lastSelectionBehavior.current = selectionBehaviorProp;\n    }\n  }, [selectionBehaviorProp]);\n\n  return {\n    selectionMode,\n    disallowEmptySelection,\n    selectionBehavior,\n    setSelectionBehavior,\n    get isFocused() {\n      return isFocusedRef.current;\n    },\n    setFocused(f) {\n      isFocusedRef.current = f;\n      setFocused(f);\n    },\n    get focusedKey() {\n      return focusedKeyRef.current;\n    },\n    get childFocusStrategy() {\n      return childFocusStrategyRef.current;\n    },\n    setFocusedKey(k, childFocusStrategy = 'first') {\n      focusedKeyRef.current = k;\n      childFocusStrategyRef.current = childFocusStrategy;\n      setFocusedKey(k);\n    },\n    selectedKeys,\n    setSelectedKeys(keys) {\n      if (allowDuplicateSelectionEvents || !equalSets(keys, selectedKeys)) {\n        setSelectedKeys(keys);\n      }\n    },\n    disabledKeys: disabledKeysProp,\n    disabledBehavior\n  };\n}\n\nfunction convertSelection(selection: 'all' | Iterable<Key> | null | undefined, defaultValue?: Selection): 'all' | Set<Key> | undefined {\n  if (!selection) {\n    return defaultValue;\n  }\n\n  return selection === 'all'\n    ? 'all'\n    : new Selection(selection);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAQD,SAAS,gCAAU,IAAI,EAAE,IAAI;IAC3B,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EACzB,OAAO;IAGT,KAAK,IAAI,QAAQ,KAAM;QACrB,IAAI,CAAC,KAAK,GAAG,CAAC,OACZ,OAAO;IAEX;IAEA,OAAO;AACT;AAcO,SAAS,0CAA0B,KAAkC;IAC1E,IAAI,EAAA,eACF,gBAAgB,MAAA,EAAA,wBAChB,yBAAyB,KAAA,EAAA,+BACzB,6BAA6B,EAC7B,mBAAmB,wBAAwB,QAAQ,EAAA,kBACnD,mBAAmB,KAAA,EACpB,GAAG;IAEJ,8FAA8F;IAC9F,kGAAkG;IAClG,IAAI,eAAe,CAAA,iKAAA,SAAK,EAAE;IAC1B,IAAI,GAAG,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IAC9B,IAAI,gBAAgB,CAAA,iKAAA,SAAK,EAAc;IACvC,IAAI,wBAAwB,CAAA,iKAAA,SAAK,EAAwB;IACzD,IAAI,GAAG,cAAc,GAAG,CAAA,iKAAA,WAAO,EAAc;IAC7C,IAAI,mBAAmB,CAAA,iKAAA,UAAM,EAAE,IAAM,uCAAiB,MAAM,YAAY,GAAG;QAAC,MAAM,YAAY;KAAC;IAC/F,IAAI,sBAAsB,CAAA,iKAAA,UAAM,EAAE,IAAM,uCAAiB,MAAM,mBAAmB,EAAE,IAAI,CAAA,4KAAA,YAAQ,MAAM;QAAC,MAAM,mBAAmB;KAAC;IACjI,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,iLAAA,qBAAiB,EACrD,kBACA,qBACA,MAAM,iBAAiB;IAEzB,IAAI,mBAAmB,CAAA,iKAAA,UAAM,EAAE,IAC7B,MAAM,YAAY,GAAG,IAAI,IAAI,MAAM,YAAY,IAAI,IAAI,OACvD;QAAC,MAAM,YAAY;KAAC;IACtB,IAAI,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAEzD,2GAA2G;IAC3G,oGAAoG;IACpG,IAAI,0BAA0B,aAAa,sBAAsB,YAAY,OAAO,iBAAiB,YAAY,aAAa,IAAI,KAAK,GACrI,qBAAqB;IAGvB,mEAAmE;IACnE,IAAI,wBAAwB,CAAA,iKAAA,SAAK,EAAE;IACnC,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,0BAA0B,sBAAsB,OAAO,EAAE;YAC3D,qBAAqB;YACrB,sBAAsB,OAAO,GAAG;QAClC;IACF,GAAG;QAAC;KAAsB;IAE1B,OAAO;uBACL;gCACA;2BACA;8BACA;QACA,IAAI,aAAY;YACd,OAAO,aAAa,OAAO;QAC7B;QACA,YAAW,CAAC;YACV,aAAa,OAAO,GAAG;YACvB,WAAW;QACb;QACA,IAAI,cAAa;YACf,OAAO,cAAc,OAAO;QAC9B;QACA,IAAI,sBAAqB;YACvB,OAAO,sBAAsB,OAAO;QACtC;QACA,eAAc,CAAC;qCAAE,iEAAqB,OAAO;YAC3C,cAAc,OAAO,GAAG;YACxB,sBAAsB,OAAO,GAAG;YAChC,cAAc;QAChB;sBACA;QACA,iBAAgB,IAAI;YAClB,IAAI,iCAAiC,CAAC,gCAAU,MAAM,eACpD,gBAAgB;QAEpB;QACA,cAAc;0BACd;IACF;AACF;AAEA,SAAS,uCAAiB,SAAmD,EAAE,YAAwB;IACrG,IAAI,CAAC,WACH,OAAO;IAGT,OAAO,cAAc,QACjB,QACA,IAAI,CAAA,4KAAA,YAAQ,EAAE;AACpB", "debugId": null}}, {"offset": {"line": 1587, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/selection/dist/SelectionManager.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/selection/dist/packages/%40react-stately/selection/src/SelectionManager.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  Collection, DisabledBehavior,\n  FocusStrategy,\n  Selection as ISelection,\n  Key,\n  LayoutDelegate,\n  LongPressEvent,\n  Node,\n  PressEvent,\n  SelectionBehavior,\n  SelectionMode\n} from '@react-types/shared';\nimport {compareNodeOrder, getChildNodes, getFirstItem} from '@react-stately/collections';\nimport {MultipleSelectionManager, MultipleSelectionState} from './types';\nimport {Selection} from './Selection';\n\ninterface SelectionManagerOptions {\n  allowsCellSelection?: boolean,\n  layoutDelegate?: LayoutDelegate\n}\n\n/**\n * An interface for reading and updating multiple selection state.\n */\nexport class SelectionManager implements MultipleSelectionManager {\n  collection: Collection<Node<unknown>>;\n  private state: MultipleSelectionState;\n  private allowsCellSelection: boolean;\n  private _isSelectAll: boolean | null;\n  private layoutDelegate: LayoutDelegate | null;\n\n  constructor(collection: Collection<Node<unknown>>, state: MultipleSelectionState, options?: SelectionManagerOptions) {\n    this.collection = collection;\n    this.state = state;\n    this.allowsCellSelection = options?.allowsCellSelection ?? false;\n    this._isSelectAll = null;\n    this.layoutDelegate = options?.layoutDelegate || null;\n  }\n\n  /**\n   * The type of selection that is allowed in the collection.\n   */\n  get selectionMode(): SelectionMode {\n    return this.state.selectionMode;\n  }\n\n  /**\n   * Whether the collection allows empty selection.\n   */\n  get disallowEmptySelection(): boolean {\n    return this.state.disallowEmptySelection;\n  }\n\n  /**\n   * The selection behavior for the collection.\n   */\n  get selectionBehavior(): SelectionBehavior {\n    return this.state.selectionBehavior;\n  }\n\n  /**\n   * Sets the selection behavior for the collection.\n   */\n  setSelectionBehavior(selectionBehavior: SelectionBehavior): void {\n    this.state.setSelectionBehavior(selectionBehavior);\n  }\n\n  /**\n   * Whether the collection is currently focused.\n   */\n  get isFocused(): boolean {\n    return this.state.isFocused;\n  }\n\n  /**\n   * Sets whether the collection is focused.\n   */\n  setFocused(isFocused: boolean): void {\n    this.state.setFocused(isFocused);\n  }\n\n  /**\n   * The current focused key in the collection.\n   */\n  get focusedKey(): Key | null {\n    return this.state.focusedKey;\n  }\n\n  /** Whether the first or last child of the focused key should receive focus. */\n  get childFocusStrategy(): FocusStrategy | null {\n    return this.state.childFocusStrategy;\n  }\n\n  /**\n   * Sets the focused key.\n   */\n  setFocusedKey(key: Key | null, childFocusStrategy?: FocusStrategy): void {\n    if (key == null || this.collection.getItem(key)) {\n      this.state.setFocusedKey(key, childFocusStrategy);\n    }\n  }\n\n  /**\n   * The currently selected keys in the collection.\n   */\n  get selectedKeys(): Set<Key> {\n    return this.state.selectedKeys === 'all'\n      ? new Set(this.getSelectAllKeys())\n      : this.state.selectedKeys;\n  }\n\n  /**\n   * The raw selection value for the collection.\n   * Either 'all' for select all, or a set of keys.\n   */\n  get rawSelection(): ISelection {\n    return this.state.selectedKeys;\n  }\n\n  /**\n   * Returns whether a key is selected.\n   */\n  isSelected(key: Key): boolean {\n    if (this.state.selectionMode === 'none') {\n      return false;\n    }\n\n    let mappedKey = this.getKey(key);\n    if (mappedKey == null) {\n      return false;\n    }\n    return this.state.selectedKeys === 'all'\n      ? this.canSelectItem(mappedKey)\n      : this.state.selectedKeys.has(mappedKey);\n  }\n\n  /**\n   * Whether the selection is empty.\n   */\n  get isEmpty(): boolean {\n    return this.state.selectedKeys !== 'all' && this.state.selectedKeys.size === 0;\n  }\n\n  /**\n   * Whether all items in the collection are selected.\n   */\n  get isSelectAll(): boolean {\n    if (this.isEmpty) {\n      return false;\n    }\n\n    if (this.state.selectedKeys === 'all') {\n      return true;\n    }\n\n    if (this._isSelectAll != null) {\n      return this._isSelectAll;\n    }\n\n    let allKeys = this.getSelectAllKeys();\n    let selectedKeys = this.state.selectedKeys;\n    this._isSelectAll = allKeys.every(k => selectedKeys.has(k));\n    return this._isSelectAll;\n  }\n\n  get firstSelectedKey(): Key | null {\n    let first: Node<unknown> | null = null;\n    for (let key of this.state.selectedKeys) {\n      let item = this.collection.getItem(key);\n      if (!first || (item && compareNodeOrder(this.collection, item, first) < 0)) {\n        first = item;\n      }\n    }\n\n    return first?.key ?? null;\n  }\n\n  get lastSelectedKey(): Key | null {\n    let last: Node<unknown> | null = null;\n    for (let key of this.state.selectedKeys) {\n      let item = this.collection.getItem(key);\n      if (!last || (item && compareNodeOrder(this.collection, item, last) > 0)) {\n        last = item;\n      }\n    }\n\n    return last?.key ?? null;\n  }\n\n  get disabledKeys(): Set<Key> {\n    return this.state.disabledKeys;\n  }\n\n  get disabledBehavior(): DisabledBehavior {\n    return this.state.disabledBehavior;\n  }\n\n  /**\n   * Extends the selection to the given key.\n   */\n  extendSelection(toKey: Key): void {\n    if (this.selectionMode === 'none') {\n      return;\n    }\n\n    if (this.selectionMode === 'single') {\n      this.replaceSelection(toKey);\n      return;\n    }\n\n    let mappedToKey = this.getKey(toKey);\n    if (mappedToKey == null) {\n      return;\n    }\n\n    let selection: Selection;\n\n    // Only select the one key if coming from a select all.\n    if (this.state.selectedKeys === 'all') {\n      selection = new Selection([mappedToKey], mappedToKey, mappedToKey);\n    } else {\n      let selectedKeys = this.state.selectedKeys as Selection;\n      let anchorKey = selectedKeys.anchorKey ?? mappedToKey;\n      selection = new Selection(selectedKeys, anchorKey, mappedToKey);\n      for (let key of this.getKeyRange(anchorKey, selectedKeys.currentKey ?? mappedToKey)) {\n        selection.delete(key);\n      }\n\n      for (let key of this.getKeyRange(mappedToKey, anchorKey)) {\n        if (this.canSelectItem(key)) {\n          selection.add(key);\n        }\n      }\n    }\n\n    this.state.setSelectedKeys(selection);\n  }\n\n  private getKeyRange(from: Key, to: Key) {\n    let fromItem = this.collection.getItem(from);\n    let toItem = this.collection.getItem(to);\n    if (fromItem && toItem) {\n      if (compareNodeOrder(this.collection, fromItem, toItem) <= 0) {\n        return this.getKeyRangeInternal(from, to);\n      }\n\n      return this.getKeyRangeInternal(to, from);\n    }\n\n    return [];\n  }\n\n  private getKeyRangeInternal(from: Key, to: Key) {\n    if (this.layoutDelegate?.getKeyRange) {\n      return this.layoutDelegate.getKeyRange(from, to);\n    }\n\n    let keys: Key[] = [];\n    let key: Key | null = from;\n    while (key != null) {\n      let item = this.collection.getItem(key);\n      if (item && (item.type === 'item' || (item.type === 'cell' && this.allowsCellSelection))) {\n        keys.push(key);\n      }\n\n      if (key === to) {\n        return keys;\n      }\n\n      key = this.collection.getKeyAfter(key);\n    }\n\n    return [];\n  }\n\n  private getKey(key: Key) {\n    let item = this.collection.getItem(key);\n    if (!item) {\n      // ¯\\_(ツ)_/¯\n      return key;\n    }\n\n    // If cell selection is allowed, just return the key.\n    if (item.type === 'cell' && this.allowsCellSelection) {\n      return key;\n    }\n\n    // Find a parent item to select\n    while (item && item.type !== 'item' && item.parentKey != null) {\n      item = this.collection.getItem(item.parentKey);\n    }\n\n    if (!item || item.type !== 'item') {\n      return null;\n    }\n\n    return item.key;\n  }\n\n  /**\n   * Toggles whether the given key is selected.\n   */\n  toggleSelection(key: Key): void {\n    if (this.selectionMode === 'none') {\n      return;\n    }\n\n    if (this.selectionMode === 'single' && !this.isSelected(key)) {\n      this.replaceSelection(key);\n      return;\n    }\n\n    let mappedKey = this.getKey(key);\n    if (mappedKey == null) {\n      return;\n    }\n\n    let keys = new Selection(this.state.selectedKeys === 'all' ? this.getSelectAllKeys() : this.state.selectedKeys);\n    if (keys.has(mappedKey)) {\n      keys.delete(mappedKey);\n      // TODO: move anchor to last selected key...\n      // Does `current` need to move here too?\n    } else if (this.canSelectItem(mappedKey)) {\n      keys.add(mappedKey);\n      keys.anchorKey = mappedKey;\n      keys.currentKey = mappedKey;\n    }\n\n    if (this.disallowEmptySelection && keys.size === 0) {\n      return;\n    }\n\n    this.state.setSelectedKeys(keys);\n  }\n\n  /**\n   * Replaces the selection with only the given key.\n   */\n  replaceSelection(key: Key): void {\n    if (this.selectionMode === 'none') {\n      return;\n    }\n\n    let mappedKey = this.getKey(key);\n    if (mappedKey == null) {\n      return;\n    }\n\n    let selection = this.canSelectItem(mappedKey)\n      ? new Selection([mappedKey], mappedKey, mappedKey)\n      : new Selection();\n\n    this.state.setSelectedKeys(selection);\n  }\n\n  /**\n   * Replaces the selection with the given keys.\n   */\n  setSelectedKeys(keys: Iterable<Key>): void {\n    if (this.selectionMode === 'none') {\n      return;\n    }\n\n    let selection = new Selection();\n    for (let key of keys) {\n      let mappedKey = this.getKey(key);\n      if (mappedKey != null) {\n        selection.add(mappedKey);\n        if (this.selectionMode === 'single') {\n          break;\n        }\n      }\n    }\n\n    this.state.setSelectedKeys(selection);\n  }\n\n  private getSelectAllKeys() {\n    let keys: Key[] = [];\n    let addKeys = (key: Key | null) => {\n      while (key != null) {\n        if (this.canSelectItem(key)) {\n          let item = this.collection.getItem(key);\n          if (item?.type === 'item') {\n            keys.push(key);\n          }\n\n          // Add child keys. If cell selection is allowed, then include item children too.\n          if (item?.hasChildNodes && (this.allowsCellSelection || item.type !== 'item')) {\n            addKeys(getFirstItem(getChildNodes(item, this.collection))?.key ?? null);\n          }\n        }\n\n        key = this.collection.getKeyAfter(key);\n      }\n    };\n\n    addKeys(this.collection.getFirstKey());\n    return keys;\n  }\n\n  /**\n   * Selects all items in the collection.\n   */\n  selectAll(): void {\n    if (!this.isSelectAll && this.selectionMode === 'multiple') {\n      this.state.setSelectedKeys('all');\n    }\n  }\n\n  /**\n   * Removes all keys from the selection.\n   */\n  clearSelection(): void {\n    if (!this.disallowEmptySelection && (this.state.selectedKeys === 'all' || this.state.selectedKeys.size > 0)) {\n      this.state.setSelectedKeys(new Selection());\n    }\n  }\n\n  /**\n   * Toggles between select all and an empty selection.\n   */\n  toggleSelectAll(): void {\n    if (this.isSelectAll) {\n      this.clearSelection();\n    } else {\n      this.selectAll();\n    }\n  }\n\n  select(key: Key, e?: PressEvent | LongPressEvent | PointerEvent): void {\n    if (this.selectionMode === 'none') {\n      return;\n    }\n\n    if (this.selectionMode === 'single') {\n      if (this.isSelected(key) && !this.disallowEmptySelection) {\n        this.toggleSelection(key);\n      } else {\n        this.replaceSelection(key);\n      }\n    } else if (this.selectionBehavior === 'toggle' || (e && (e.pointerType === 'touch' || e.pointerType === 'virtual'))) {\n      // if touch or virtual (VO) then we just want to toggle, otherwise it's impossible to multi select because they don't have modifier keys\n      this.toggleSelection(key);\n    } else {\n      this.replaceSelection(key);\n    }\n  }\n\n  /**\n   * Returns whether the current selection is equal to the given selection.\n   */\n  isSelectionEqual(selection: Set<Key>): boolean {\n    if (selection === this.state.selectedKeys) {\n      return true;\n    }\n\n    // Check if the set of keys match.\n    let selectedKeys = this.selectedKeys;\n    if (selection.size !== selectedKeys.size) {\n      return false;\n    }\n\n    for (let key of selection) {\n      if (!selectedKeys.has(key)) {\n        return false;\n      }\n    }\n\n    for (let key of selectedKeys) {\n      if (!selection.has(key)) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  canSelectItem(key: Key): boolean {\n    if (this.state.selectionMode === 'none' || this.state.disabledKeys.has(key)) {\n      return false;\n    }\n\n    let item = this.collection.getItem(key);\n    if (!item || item?.props?.isDisabled || (item.type === 'cell' && !this.allowsCellSelection)) {\n      return false;\n    }\n\n    return true;\n  }\n\n  isDisabled(key: Key): boolean {\n    return this.state.disabledBehavior === 'all' && (this.state.disabledKeys.has(key) || !!this.collection.getItem(key)?.props?.isDisabled);\n  }\n\n  isLink(key: Key): boolean {\n    return !!this.collection.getItem(key)?.props?.href;\n  }\n\n  getItemProps(key: Key): any {\n    return this.collection.getItem(key)?.props;\n  }\n\n  withCollection(collection: Collection<Node<unknown>>): SelectionManager {\n    return new SelectionManager(collection, this.state, {\n      allowsCellSelection: this.allowsCellSelection,\n      layoutDelegate: this.layoutDelegate || undefined\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GA0BM,MAAM;IAeX;;GAEC,GACD,IAAI,gBAA+B;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa;IACjC;IAEA;;GAEC,GACD,IAAI,yBAAkC;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB;IAC1C;IAEA;;GAEC,GACD,IAAI,oBAAuC;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB;IACrC;IAEA;;GAEC,GACD,qBAAqB,iBAAoC,EAAQ;QAC/D,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;IAClC;IAEA;;GAEC,GACD,IAAI,YAAqB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;IAC7B;IAEA;;GAEC,GACD,WAAW,SAAkB,EAAQ;QACnC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IACxB;IAEA;;GAEC,GACD,IAAI,aAAyB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU;IAC9B;IAEA,6EAA6E,GAC7E,IAAI,qBAA2C;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB;IACtC;IAEA;;GAEC,GACD,cAAc,GAAe,EAAE,kBAAkC,EAAQ;QACvE,IAAI,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MACzC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK;IAElC;IAEA;;GAEC,GACD,IAAI,eAAyB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,QAC/B,IAAI,IAAI,IAAI,CAAC,gBAAgB,MAC7B,IAAI,CAAC,KAAK,CAAC,YAAY;IAC7B;IAEA;;;GAGC,GACD,IAAI,eAA2B;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY;IAChC;IAEA;;GAEC,GACD,WAAW,GAAQ,EAAW;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,KAAK,QAC/B,OAAO;QAGT,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC;QAC5B,IAAI,aAAa,MACf,OAAO;QAET,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,QAC/B,IAAI,CAAC,aAAa,CAAC,aACnB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC;IAClC;IAEA;;GAEC,GACD,IAAI,UAAmB;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK;IAC/E;IAEA;;GAEC,GACD,IAAI,cAAuB;QACzB,IAAI,IAAI,CAAC,OAAO,EACd,OAAO;QAGT,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,OAC9B,OAAO;QAGT,IAAI,IAAI,CAAC,YAAY,IAAI,MACvB,OAAO,IAAI,CAAC,YAAY;QAG1B,IAAI,UAAU,IAAI,CAAC,gBAAgB;QACnC,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,YAAY;QAC1C,IAAI,CAAC,YAAY,GAAG,QAAQ,KAAK,CAAC,CAAA,IAAK,aAAa,GAAG,CAAC;QACxD,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,IAAI,mBAA+B;QACjC,IAAI,QAA8B;QAClC,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAE;YACvC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,SAAU,QAAQ,CAAA,kLAAA,mBAAe,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,SAAS,GACtE,QAAQ;QAEZ;YAEO;QAAP,OAAO,CAAA,aAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,GAAG,MAAA,QAAV,eAAA,KAAA,IAAA,aAAc;IACvB;IAEA,IAAI,kBAA8B;QAChC,IAAI,OAA6B;QACjC,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAE;YACvC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,QAAS,QAAQ,CAAA,kLAAA,mBAAe,EAAE,IAAI,CAAC,UAAU,EAAE,MAAM,QAAQ,GACpE,OAAO;QAEX;YAEO;QAAP,OAAO,CAAA,YAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,GAAG,MAAA,QAAT,cAAA,KAAA,IAAA,YAAa;IACtB;IAEA,IAAI,eAAyB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY;IAChC;IAEA,IAAI,mBAAqC;QACvC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB;IACpC;IAEA;;GAEC,GACD,gBAAgB,KAAU,EAAQ;QAChC,IAAI,IAAI,CAAC,aAAa,KAAK,QACzB;QAGF,IAAI,IAAI,CAAC,aAAa,KAAK,UAAU;YACnC,IAAI,CAAC,gBAAgB,CAAC;YACtB;QACF;QAEA,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,eAAe,MACjB;QAGF,IAAI;QAEJ,uDAAuD;QACvD,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,OAC9B,YAAY,IAAI,CAAA,4KAAA,YAAQ,EAAE;YAAC;SAAY,EAAE,aAAa;aACjD;YACL,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,YAAY;gBAC1B;YAAhB,IAAI,YAAY,CAAA,0BAAA,aAAa,SAAS,MAAA,QAAtB,4BAAA,KAAA,IAAA,0BAA0B;YAC1C,YAAY,IAAI,CAAA,4KAAA,YAAQ,EAAE,cAAc,WAAW;gBACP;YAA5C,KAAK,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAA,2BAAA,aAAa,UAAU,MAAA,QAAvB,6BAAA,KAAA,IAAA,2BAA2B,aACrE,UAAU,MAAM,CAAC;YAGnB,KAAK,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,WAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,MACrB,UAAU,GAAG,CAAC;QAGpB;QAEA,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IAC7B;IAEQ,YAAY,IAAS,EAAE,EAAO,EAAE;QACtC,IAAI,WAAW,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACvC,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACrC,IAAI,YAAY,QAAQ;YACtB,IAAI,CAAA,kLAAA,mBAAe,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,WAAW,GACzD,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAGxC,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI;QACtC;QAEA,OAAO,EAAE;IACX;IAEQ,oBAAoB,IAAS,EAAE,EAAO,EAAE;YAC1C;QAAJ,IAAA,CAAI,uBAAA,IAAI,CAAC,cAAc,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAqB,WAAW,EAClC,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM;QAG/C,IAAI,OAAc,EAAE;QACpB,IAAI,MAAkB;QACtB,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,QAAS,CAAA,KAAK,IAAI,KAAK,UAAW,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,mBAAmB,GACpF,KAAK,IAAI,CAAC;YAGZ,IAAI,QAAQ,IACV,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QACpC;QAEA,OAAO,EAAE;IACX;IAEQ,OAAO,GAAQ,EAAE;QACvB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,MAEH,AADA,OACO,KADK;QAId,qDAAqD;QACrD,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,mBAAmB,EAClD,OAAO;QAGT,+BAA+B;QAC/B,MAAO,QAAQ,KAAK,IAAI,KAAK,UAAU,KAAK,SAAS,IAAI,KACvD,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,SAAS;QAG/C,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,QACzB,OAAO;QAGT,OAAO,KAAK,GAAG;IACjB;IAEA;;GAEC,GACD,gBAAgB,GAAQ,EAAQ;QAC9B,IAAI,IAAI,CAAC,aAAa,KAAK,QACzB;QAGF,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM;YAC5D,IAAI,CAAC,gBAAgB,CAAC;YACtB;QACF;QAEA,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC;QAC5B,IAAI,aAAa,MACf;QAGF,IAAI,OAAO,IAAI,CAAA,4KAAA,YAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,QAAQ,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY;QAC9G,IAAI,KAAK,GAAG,CAAC,YACX,KAAK,MAAM,CAAC;aAGP,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY;YACxC,KAAK,GAAG,CAAC;YACT,KAAK,SAAS,GAAG;YACjB,KAAK,UAAU,GAAG;QACpB;QAEA,IAAI,IAAI,CAAC,sBAAsB,IAAI,KAAK,IAAI,KAAK,GAC/C;QAGF,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IAC7B;IAEA;;GAEC,GACD,iBAAiB,GAAQ,EAAQ;QAC/B,IAAI,IAAI,CAAC,aAAa,KAAK,QACzB;QAGF,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC;QAC5B,IAAI,aAAa,MACf;QAGF,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,aAC/B,IAAI,CAAA,4KAAA,YAAQ,EAAE;YAAC;SAAU,EAAE,WAAW,aACtC,IAAI,CAAA,4KAAA,YAAQ;QAEhB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IAC7B;IAEA;;GAEC,GACD,gBAAgB,IAAmB,EAAQ;QACzC,IAAI,IAAI,CAAC,aAAa,KAAK,QACzB;QAGF,IAAI,YAAY,IAAI,CAAA,4KAAA,YAAQ;QAC5B,KAAK,IAAI,OAAO,KAAM;YACpB,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC;YAC5B,IAAI,aAAa,MAAM;gBACrB,UAAU,GAAG,CAAC;gBACd,IAAI,IAAI,CAAC,aAAa,KAAK,UACzB;YAEJ;QACF;QAEA,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IAC7B;IAEQ,mBAAmB;QACzB,IAAI,OAAc,EAAE;QACpB,IAAI,UAAU,CAAC;YACb,MAAO,OAAO,KAAM;gBAClB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM;wBAQjB;oBAPV,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;oBACnC,IAAI,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,IAAI,MAAK,QACjB,KAAK,IAAI,CAAC;wBAKF;oBAFV,gFAAgF;oBAChF,IAAI,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,aAAa,KAAK,CAAA,IAAI,CAAC,mBAAmB,IAAI,KAAK,IAAI,KAAK,MAAK,GACzE,QAAQ,CAAA,oBAAA,CAAA,gBAAA,CAAA,kLAAA,eAAW,EAAE,CAAA,kLAAA,gBAAY,EAAE,MAAM,IAAI,CAAC,UAAU,EAAA,MAAA,QAAhD,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAoD,GAAG,MAAA,QAAvD,sBAAA,KAAA,IAAA,oBAA2D;gBAEvE;gBAEA,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;YACpC;QACF;QAEA,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW;QACnC,OAAO;IACT;IAEA;;GAEC,GACD,YAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,KAAK,YAC9C,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;IAE/B;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAK,CAAA,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,CAAA,GACvG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAA,4KAAA,YAAQ;IAE3C;IAEA;;GAEC,GACD,kBAAwB;QACtB,IAAI,IAAI,CAAC,WAAW,EAClB,IAAI,CAAC,cAAc;aAEnB,IAAI,CAAC,SAAS;IAElB;IAEA,OAAO,GAAQ,EAAE,CAA8C,EAAQ;QACrE,IAAI,IAAI,CAAC,aAAa,KAAK,QACzB;QAGF,IAAI,IAAI,CAAC,aAAa,KAAK,UAAA;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,EACtD,IAAI,CAAC,eAAe,CAAC;iBAErB,IAAI,CAAC,gBAAgB,CAAC;eAEnB,IAAI,IAAI,CAAC,iBAAiB,KAAK,YAAa,KAAM,CAAA,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,SAAQ,GAC9G,AACA,IAAI,CAAC,eAAe,CAAC,mHADmH;aAGxI,IAAI,CAAC,gBAAgB,CAAC;IAE1B;IAEA;;GAEC,GACD,iBAAiB,SAAmB,EAAW;QAC7C,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,YAAY,EACvC,OAAO;QAGT,kCAAkC;QAClC,IAAI,eAAe,IAAI,CAAC,YAAY;QACpC,IAAI,UAAU,IAAI,KAAK,aAAa,IAAI,EACtC,OAAO;QAGT,KAAK,IAAI,OAAO,UAAW;YACzB,IAAI,CAAC,aAAa,GAAG,CAAC,MACpB,OAAO;QAEX;QAEA,KAAK,IAAI,OAAO,aAAc;YAC5B,IAAI,CAAC,UAAU,GAAG,CAAC,MACjB,OAAO;QAEX;QAEA,OAAO;IACT;IAEA,cAAc,GAAQ,EAAW;YAMlB;QALb,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,KAAK,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MACrE,OAAO;QAGT,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,QAAA,CAAQ,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,cAAA,KAAM,KAAK,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAa,UAAU,KAAK,KAAK,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,mBAAmB,EACxF,OAAO;QAGT,OAAO;IACT;IAEA,WAAW,GAAQ,EAAW;YAC2D,gCAAA;QAAvF,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK,SAAU,CAAA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA,CAAA,CAAC,2BAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAA,MAAA,QAAxB,6BAAA,KAAA,IAAA,KAAA,IAAA,CAAA,iCAAA,yBAA8B,KAAK,MAAA,QAAnC,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAqC,UAAU,CAAD;IACvI;IAEA,OAAO,GAAQ,EAAW;YACf,gCAAA;QAAT,OAAO,CAAC,CAAA,CAAA,CAAC,2BAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAA,MAAA,QAAxB,6BAAA,KAAA,IAAA,KAAA,IAAA,CAAA,iCAAA,yBAA8B,KAAK,MAAA,QAAnC,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAqC,IAAI;IACpD;IAEA,aAAa,GAAQ,EAAO;YACnB;QAAP,OAAA,CAAO,2BAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAA,MAAA,QAAxB,6BAAA,KAAA,IAAA,KAAA,IAAA,yBAA8B,KAAK;IAC5C;IAEA,eAAe,UAAqC,EAAoB;QACtE,OAAO,IAAI,0CAAiB,YAAY,IAAI,CAAC,KAAK,EAAE;YAClD,qBAAqB,IAAI,CAAC,mBAAmB;YAC7C,gBAAgB,IAAI,CAAC,cAAc,IAAI;QACzC;IACF;IA7dA,YAAY,UAAqC,EAAE,KAA6B,EAAE,OAAiC,CAAE;QACnH,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;YACc;QAA3B,IAAI,CAAC,mBAAmB,GAAG,CAAA,+BAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,mBAAmB,MAAA,QAA5B,iCAAA,KAAA,IAAA,+BAAgC;QAC3D,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,cAAc,KAAI;IACnD;AAwdF", "debugId": null}}, {"offset": {"line": 1907, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-stately/tooltip/dist/useTooltipTriggerState.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-stately/tooltip/dist/packages/%40react-stately/tooltip/src/useTooltipTriggerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {TooltipTriggerProps} from '@react-types/tooltip';\nimport {useEffect, useMemo, useRef} from 'react';\nimport {useOverlayTriggerState} from '@react-stately/overlays';\n\nconst TOOLTIP_DELAY = 1500; // this seems to be a 1.5 second delay, check with design\nconst TOOLTIP_COOLDOWN = 500;\n\nexport interface TooltipTriggerState {\n  /** Whether the tooltip is currently showing. */\n  isOpen: boolean,\n  /**\n   * Shows the tooltip. By default, the tooltip becomes visible after a delay\n   * depending on a global warmup timer. The `immediate` option shows the\n   * tooltip immediately instead.\n   */\n  open(immediate?: boolean): void,\n  /** Hides the tooltip. */\n  close(immediate?: boolean): void\n}\n\nlet tooltips = {};\nlet tooltipId = 0;\nlet globalWarmedUp = false;\nlet globalWarmUpTimeout: ReturnType<typeof setTimeout> | null = null;\nlet globalCooldownTimeout: ReturnType<typeof setTimeout> | null = null;\n\n/**\n * Manages state for a tooltip trigger. Tracks whether the tooltip is open, and provides\n * methods to toggle this state. Ensures only one tooltip is open at a time and controls\n * the delay for showing a tooltip.\n */\nexport function useTooltipTriggerState(props: TooltipTriggerProps = {}): TooltipTriggerState {\n  let {delay = TOOLTIP_DELAY, closeDelay = TOOLTIP_COOLDOWN} = props;\n  let {isOpen, open, close} = useOverlayTriggerState(props);\n  let id = useMemo(() => `${++tooltipId}`, []);\n  let closeTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);\n  let closeCallback = useRef<() => void>(close);\n\n  let ensureTooltipEntry = () => {\n    tooltips[id] = hideTooltip;\n  };\n\n  let closeOpenTooltips = () => {\n    for (let hideTooltipId in tooltips) {\n      if (hideTooltipId !== id) {\n        tooltips[hideTooltipId](true);\n        delete tooltips[hideTooltipId];\n      }\n    }\n  };\n\n  let showTooltip = () => {\n    if (closeTimeout.current) {\n      clearTimeout(closeTimeout.current);\n    }\n    closeTimeout.current = null;\n    closeOpenTooltips();\n    ensureTooltipEntry();\n    globalWarmedUp = true;\n    open();\n    if (globalWarmUpTimeout) {\n      clearTimeout(globalWarmUpTimeout);\n      globalWarmUpTimeout = null;\n    }\n    if (globalCooldownTimeout) {\n      clearTimeout(globalCooldownTimeout);\n      globalCooldownTimeout = null;\n    }\n  };\n\n  let hideTooltip = (immediate?: boolean) => {\n    if (immediate || closeDelay <= 0) {\n      if (closeTimeout.current) {\n        clearTimeout(closeTimeout.current);\n      }\n      closeTimeout.current = null;\n      closeCallback.current();\n    } else if (!closeTimeout.current) {\n      closeTimeout.current = setTimeout(() => {\n        closeTimeout.current = null;\n        closeCallback.current();\n      }, closeDelay);\n    }\n\n    if (globalWarmUpTimeout) {\n      clearTimeout(globalWarmUpTimeout);\n      globalWarmUpTimeout = null;\n    }\n    if (globalWarmedUp) {\n      if (globalCooldownTimeout) {\n        clearTimeout(globalCooldownTimeout);\n      }\n      globalCooldownTimeout = setTimeout(() => {\n        delete tooltips[id];\n        globalCooldownTimeout = null;\n        globalWarmedUp = false;\n      }, Math.max(TOOLTIP_COOLDOWN, closeDelay));\n    }\n  };\n\n  let warmupTooltip = () => {\n    closeOpenTooltips();\n    ensureTooltipEntry();\n    if (!isOpen && !globalWarmUpTimeout && !globalWarmedUp) {\n      globalWarmUpTimeout = setTimeout(() => {\n        globalWarmUpTimeout = null;\n        globalWarmedUp = true;\n        showTooltip();\n      }, delay);\n    } else if (!isOpen) {\n      showTooltip();\n    }\n  };\n\n  useEffect(() => {\n    closeCallback.current = close;\n  }, [close]);\n\n\n  useEffect(() => {\n    return () => {\n      if (closeTimeout.current) {\n        clearTimeout(closeTimeout.current);\n      }\n      let tooltip = tooltips[id];\n      if (tooltip) {\n        delete tooltips[id];\n      }\n    };\n  }, [id]);\n\n  return {\n    isOpen,\n    open: (immediate) => {\n      if (!immediate && delay > 0 && !closeTimeout.current) {\n        warmupTooltip();\n      } else {\n        showTooltip();\n      }\n    },\n    close: hideTooltip\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMD,MAAM,sCAAgB,MAAM,yDAAyD;AACrF,MAAM,yCAAmB;AAezB,IAAI,iCAAW,CAAC;AAChB,IAAI,kCAAY;AAChB,IAAI,uCAAiB;AACrB,IAAI,4CAA4D;AAChE,IAAI,8CAA8D;AAO3D,SAAS;QAAuB,yEAA6B,CAAC,CAAC;IACpE,IAAI,EAAA,OAAC,QAAQ,mCAAA,EAAA,YAAe,aAAa,sCAAA,EAAiB,GAAG;IAC7D,IAAI,EAAA,QAAC,MAAM,EAAA,MAAE,IAAI,EAAA,OAAE,KAAK,EAAC,GAAG,CAAA,GAAA,8MAAqB,EAAE;IACnD,IAAI,KAAK,CAAA,iKAAA,UAAM,EAAE,IAAM,GAAK,CAAW,MAAb,EAAE,kCAAa,EAAE;IAC3C,IAAI,eAAe,CAAA,iKAAA,SAAK,EAAwC;IAChE,IAAI,gBAAgB,CAAA,iKAAA,SAAK,EAAc;IAEvC,IAAI,qBAAqB;QACvB,8BAAQ,CAAC,GAAG,GAAG;IACjB;IAEA,IAAI,oBAAoB;QACtB,IAAK,IAAI,iBAAiB,+BACxB,IAAI,kBAAkB,IAAI;YACxB,8BAAQ,CAAC,cAAc,CAAC;YACxB,OAAO,8BAAQ,CAAC,cAAc;QAChC;IAEJ;IAEA,IAAI,cAAc;QAChB,IAAI,aAAa,OAAO,EACtB,aAAa,aAAa,OAAO;QAEnC,aAAa,OAAO,GAAG;QACvB;QACA;QACA,uCAAiB;QACjB;QACA,IAAI,2CAAqB;YACvB,aAAa;YACb,4CAAsB;QACxB;QACA,IAAI,6CAAuB;YACzB,aAAa;YACb,8CAAwB;QAC1B;IACF;IAEA,IAAI,cAAc,CAAC;QACjB,IAAI,aAAa,cAAc,GAAG;YAChC,IAAI,aAAa,OAAO,EACtB,aAAa,aAAa,OAAO;YAEnC,aAAa,OAAO,GAAG;YACvB,cAAc,OAAO;QACvB,OAAO,IAAI,CAAC,aAAa,OAAO,EAC9B,aAAa,OAAO,GAAG,WAAW;YAChC,aAAa,OAAO,GAAG;YACvB,cAAc,OAAO;QACvB,GAAG;QAGL,IAAI,2CAAqB;YACvB,aAAa;YACb,4CAAsB;QACxB;QACA,IAAI,sCAAgB;YAClB,IAAI,6CACF,aAAa;YAEf,8CAAwB,WAAW;gBACjC,OAAO,8BAAQ,CAAC,GAAG;gBACnB,8CAAwB;gBACxB,uCAAiB;YACnB,GAAG,KAAK,GAAG,CAAC,wCAAkB;QAChC;IACF;IAEA,IAAI,gBAAgB;QAClB;QACA;QACA,IAAI,CAAC,UAAU,CAAC,6CAAuB,CAAC,sCACtC,4CAAsB,WAAW;YAC/B,4CAAsB;YACtB,uCAAiB;YACjB;QACF,GAAG;aACE,IAAI,CAAC,QACV;IAEJ;IAEA,CAAA,GAAA,0KAAQ,EAAE;QACR,cAAc,OAAO,GAAG;IAC1B,GAAG;QAAC;KAAM;IAGV,CAAA,iKAAA,YAAQ,EAAE;QACR,OAAO;YACL,IAAI,aAAa,OAAO,EACtB,aAAa,aAAa,OAAO;YAEnC,IAAI,UAAU,8BAAQ,CAAC,GAAG;YAC1B,IAAI,SACF,OAAO,8BAAQ,CAAC,GAAG;QAEvB;IACF,GAAG;QAAC;KAAG;IAEP,OAAO;gBACL;QACA,MAAM,CAAC;YACL,IAAI,CAAC,aAAa,QAAQ,KAAK,CAAC,aAAa,OAAO,EAClD;iBAEA;QAEJ;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2024, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2048, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,iMAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,CAAA,GAAA,kMAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2091, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,CAAA,GAAA,iMAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2130, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2215, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2649, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2676, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `\"auto\"`, `null`, `undefined` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | 'auto' | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  if (onNavigate) {\n    let isDefaultPrevented = false\n\n    onNavigate({\n      preventDefault: () => {\n        isDefaultPrevented = true\n      },\n    })\n\n    if (isDefaultPrevented) {\n      return\n    }\n  }\n\n  React.startTransition(() => {\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  })\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null || prefetchProp === 'auto'\n      ? PrefetchKind.AUTO\n      : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'prefetch') {\n        if (\n          props[key] != null &&\n          valType !== 'boolean' &&\n          props[key] !== 'auto'\n        ) {\n          throw createPropError({\n            key,\n            expected: '`boolean | \"auto\"`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "isDefaultPrevented", "React", "startTransition", "dispatchNavigateAction", "current", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMsE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAiTA;;;;;;;;;CASC,GACD,OAsaC,EAAA;eAtauBxE;;IA4aXC,aAAa,EAAA;eAAbA;;;;;iEAruB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,IAAIN,YAAY;QACd,IAAIQ,qBAAqB;QAEzBR,WAAW;YACTM,gBAAgB;gBACdE,qBAAqB;YACvB;QACF;QAEA,IAAIA,oBAAoB;YACtB;QACF;IACF;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAAC;QACpBC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBf,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBe,OAAO;IAE3B;AACF;AAEA,SAASC,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASnC,cACtBqC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMxB,kBAAkByB,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ3B,MAAM4B,QAAQ,EACd3B,IAAI4B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR9B,OAAO,EACP+B,OAAO,EACP9B,MAAM,EACN+B,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBnC,UAAU,EACVoC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAShC,OAAAA,OAAK,CAACiC,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,QAAQA,iBAAiB,SACtCmB,oBAAAA,YAAY,CAACC,IAAI,GACjBD,oBAAAA,YAAY,CAACE,IAAI;IAEvB,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DhE,MAAM;QACR;QACA,MAAMiE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DrE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR8B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBnC,YAAY;QACd;QACA,MAAMkE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IAAIZ,QAAQ,YAAY;gBAC7B,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACdY,YAAY,aACZnD,KAAK,CAACuC,IAAI,KAAK,QACf;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI7B;YACJ,IAAI,OAAO4B,aAAa,UAAU;gBAChC5B,OAAO4B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA3E,OAAO4B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI3E,MAAM;gBACR,MAAM4E,oBAAoB5E,KACvB6E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB3D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGa,OAAAA,OAAK,CAACoE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL5B,MAAMmF;gBACNlF,IAAI4B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQtE,OAAAA,OAAK,CAACyE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;IAGT,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B9E,OAAAA,OAAK,CAAC+E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB5C,gBAAgBe,OAAO,GAAG8E,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA9F,MACA8C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAIrB,gBAAgBe,OAAO,EAAE;wBAC3B+E,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC9F,gBAAgBe,OAAO;wBACvDf,gBAAgBe,OAAO,GAAG;oBAC5B;oBACAgF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBjD;QAAM8C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQpC,CAAC;YACP,IAAIuD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAACzD,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI4D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQpC;YACV;YAEA,IACEyC,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACpC;YACtB;YAEA,IAAI,CAAC+C,QAAQ;gBACX;YACF;YAEA,IAAI/C,EAAEsG,gBAAgB,EAAE;gBACtB;YACF;YAEAvG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACA+B,cAAarC,CAAC;YACZ,IAAI,CAACyC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBtC;YACnB;YAEA,IACEyC,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACrC;YAC3B;YAEA,IAAI,CAAC+C,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,AAChDC,0BACA,SAASnE,aAAavC,CAAC;YACrB,IAAI,CAACyC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBxC;YACnB;YAEA,IACEyC,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACvC;YAC3B;YAEA,IAAI,CAAC+C,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBxG,EAAEV,aAAa,EACfiH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAACzG,KAAK;QACrBmG,WAAWpG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACuC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWpG,IAAI,GAAG2G,CAAAA,GAAAA,aAAAA,WAAW,EAAC1G;IAChC;IAEA,IAAI2G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO9F,OAAAA,OAAK,CAACgG,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMxC,gBAAgB;IAC3B,OAAO8D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3077, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3088, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/format-error-message.mjs"], "sourcesContent": ["function formatErrorMessage(message, errorCode) {\n    return errorCode\n        ? `${message}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${errorCode}`\n        : message;\n}\n\nexport { formatErrorMessage };\n"], "names": [], "mappings": ";;;AAAA,SAAS,mBAAmB,OAAO,EAAE,SAAS;IAC1C,OAAO,YACD,AAAC,GAAmG,OAAjG,SAAQ,2FAAmG,OAAV,aACpG;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3099, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["import { formatErrorMessage } from './format-error-message.mjs';\n\nconst warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, errorCode) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(formatErrorMessage(message, errorCode));\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,SAAS,IAAI;AACnB,SAAS,UAAU,OAAO;IACtB,OAAO,OAAO,GAAG,CAAC;AACtB;AACA,SAAS,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS;IAC3C,IAAI,aAAa,OAAO,GAAG,CAAC,UACxB;IACJ,QAAQ,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;IACzC,OAAO,GAAG,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["import { formatErrorMessage } from './format-error-message.mjs';\n\nlet warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message, errorCode) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(formatErrorMessage(message, errorCode));\n        }\n    };\n    invariant = (check, message, errorCode) => {\n        if (!check) {\n            throw new Error(formatErrorMessage(message, errorCode));\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "names": [], "mappings": ";;;;AAII;AAJJ;;AAEA,IAAI,UAAU,KAAQ;AACtB,IAAI,YAAY,KAAQ;AACxB,wCAA2C;IACvC,UAAU,CAAC,OAAO,SAAS;QACvB,IAAI,CAAC,SAAS,OAAO,YAAY,aAAa;YAC1C,QAAQ,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;QAC7C;IACJ;IACA,YAAY,CAAC,OAAO,SAAS;QACzB,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM,CAAA,GAAA,+KAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;QAChD;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC,KAAK,KAAK;IACrB,IAAI,IAAI,KACJ,OAAO;IACX,IAAI,IAAI,KACJ,OAAO;IACX,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/array.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,GACvB,IAAI,IAAI,CAAC;AACjB;AACA,SAAS,WAAW,GAAG,EAAE,IAAI;IACzB,MAAM,QAAQ,IAAI,OAAO,CAAC;IAC1B,IAAI,QAAQ,CAAC,GACT,IAAI,MAAM,CAAC,OAAO;AAC1B;AACA,0BAA0B;AAC1B,SAAS,SAAS,KAAQ,EAAE,SAAS,EAAE,OAAO;QAA5B,CAAC,GAAG,IAAI,GAAR;IACd,MAAM,aAAa,YAAY,IAAI,IAAI,MAAM,GAAG,YAAY;IAC5D,IAAI,cAAc,KAAK,aAAa,IAAI,MAAM,EAAE;QAC5C,MAAM,WAAW,UAAU,IAAI,IAAI,MAAM,GAAG,UAAU;QACtD,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW;QACrC,IAAI,MAAM,CAAC,UAAU,GAAG;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/subscription-manager.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IAIF,IAAI,OAAO,EAAE;QACT,CAAA,GAAA,0JAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;QAClC,OAAO,IAAM,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;IAChD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAClD,IAAI,CAAC,kBACD;QACJ,IAAI,qBAAqB,GAAG;YACxB;;aAEC,GACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;QAChC,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC;;;iBAGC,GACD,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBACrC,WAAW,QAAQ,GAAG,GAAG;YAC7B;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;IACpC;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;IAChC;IAjCA,aAAc;QACV,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;AAgCJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/velocity-per-second.mjs"], "sourcesContent": ["/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AACA,SAAS,kBAAkB,QAAQ,EAAE,aAAa;IAC9C,OAAO,gBAAgB,WAAW,CAAC,OAAO,aAAa,IAAI;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,MAAM,OAAO,CAAC,MAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/utils/is-html-element.mjs"], "sourcesContent": ["import { isObject } from 'motion-utils';\n\n/**\n * Checks if an element is an HTML element in a way\n * that works across iframes\n */\nfunction isHTMLElement(element) {\n    return isObject(element) && \"offsetHeight\" in element;\n}\n\nexport { isHTMLElement };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,SAAS,cAAc,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,kBAAkB;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3269, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs"], "sourcesContent": ["const isMotionValue = (value) => Boolean(value && value.getVelocity);\n\nexport { isMotionValue };\n"], "names": [], "mappings": ";;;AAAA,MAAM,gBAAgB,CAAC,QAAU,QAAQ,SAAS,MAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/render/utils/keys-transform.mjs"], "sourcesContent": ["/**\n * Generate a list of every possible transform key.\n */\nconst transformPropOrder = [\n    \"transformPerspective\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"translateX\",\n    \"translateY\",\n    \"translateZ\",\n    \"scale\",\n    \"scaleX\",\n    \"scaleY\",\n    \"rotate\",\n    \"rotateX\",\n    \"rotateY\",\n    \"rotateZ\",\n    \"skew\",\n    \"skewX\",\n    \"skewY\",\n];\n/**\n * A quick lookup for transform props.\n */\nconst transformProps = /*@__PURE__*/ (() => new Set(transformPropOrder))();\n\nexport { transformPropOrder, transformProps };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACD,MAAM,qBAAqB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD;;CAEC,GACD,MAAM,iBAAiB,WAAW,GAAG,CAAC,IAAM,IAAI,IAAI,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/animation/utils/is-css-variable.mjs"], "sourcesContent": ["const checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = \n/*@__PURE__*/ checkStringStartsWith(\"--\");\nconst startsAsVariableToken = \n/*@__PURE__*/ checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\nexport { isCSSVariableName, isCSSVariableToken };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,wBAAwB,CAAC,QAAU,CAAC,MAAQ,OAAO,QAAQ,YAAY,IAAI,UAAU,CAAC;AAC5F,MAAM,oBACN,WAAW,GAAG,sBAAsB;AACpC,MAAM,wBACN,WAAW,GAAG,sBAAsB;AACpC,MAAM,qBAAqB,CAAC;IACxB,MAAM,kBAAkB,sBAAsB;IAC9C,IAAI,CAAC,iBACD,OAAO;IACX,6FAA6F;IAC7F,OAAO,uBAAuB,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;AAChE;AACA,MAAM,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3330, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/types/utils/get-as-type.mjs"], "sourcesContent": ["/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n    return type && typeof value === \"number\"\n        ? type.transform(value)\n        : value;\n};\n\nexport { getValueAsType };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,iBAAiB,CAAC,OAAO;IAC3B,OAAO,QAAQ,OAAO,UAAU,WAC1B,KAAK,SAAS,CAAC,SACf;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3343, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/types/numbers/index.mjs"], "sourcesContent": ["import { clamp } from 'motion-utils';\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => clamp(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\nexport { alpha, number, scale };\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,SAAS;IACX,MAAM,CAAC,IAAM,OAAO,MAAM;IAC1B,OAAO;IACP,WAAW,CAAC,IAAM;AACtB;AACA,MAAM,QAAQ;IACV,GAAG,MAAM;IACT,WAAW,CAAC,IAAM,CAAA,GAAA,0JAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG;AAClC;AACA,MAAM,QAAQ;IACV,GAAG,MAAM;IACT,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3368, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/types/int.mjs"], "sourcesContent": ["import { number } from './numbers/index.mjs';\n\nconst int = {\n    ...number,\n    transform: Math.round,\n};\n\nexport { int };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,MAAM;IACR,GAAG,qLAAA,CAAA,SAAM;IACT,WAAW,KAAK,KAAK;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/ (() => ({\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n}))();\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;AACtB,MAAM,iBAAiB,CAAC,OAAS,CAAC;QAC9B,MAAM,CAAC,IAAM,OAAO,MAAM,YAAY,EAAE,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,MAAM,KAAK;QAClF,OAAO;QACP,WAAW,CAAC,IAAM,AAAC,GAAM,OAAJ,GAAS,OAAL;IAC7B,CAAC;AACD,MAAM,UAAU,WAAW,GAAG,eAAe;AAC7C,MAAM,UAAU,WAAW,GAAG,eAAe;AAC7C,MAAM,KAAK,WAAW,GAAG,eAAe;AACxC,MAAM,KAAK,WAAW,GAAG,eAAe;AACxC,MAAM,KAAK,WAAW,GAAG,eAAe;AACxC,MAAM,qBAAqB,WAAW,GAAG,CAAC,IAAM,CAAC;QAC7C,GAAG,OAAO;QACV,OAAO,CAAC,IAAM,QAAQ,KAAK,CAAC,KAAK;QACjC,WAAW,CAAC,IAAM,QAAQ,SAAS,CAAC,IAAI;IAC5C,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3410, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/types/maps/transform.mjs"], "sourcesContent": ["import { scale, alpha } from '../numbers/index.mjs';\nimport { degrees, px, progressPercentage } from '../numbers/units.mjs';\n\nconst transformValueTypes = {\n    rotate: degrees,\n    rotateX: degrees,\n    rotateY: degrees,\n    rotateZ: degrees,\n    scale,\n    scaleX: scale,\n    scaleY: scale,\n    scaleZ: scale,\n    skew: degrees,\n    skewX: degrees,\n    skewY: degrees,\n    distance: px,\n    translateX: px,\n    translateY: px,\n    translateZ: px,\n    x: px,\n    y: px,\n    z: px,\n    perspective: px,\n    transformPerspective: px,\n    opacity: alpha,\n    originX: progressPercentage,\n    originY: progressPercentage,\n    originZ: px,\n};\n\nexport { transformValueTypes };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,sBAAsB;IACxB,QAAQ,qLAAA,CAAA,UAAO;IACf,SAAS,qLAAA,CAAA,UAAO;IAChB,SAAS,qLAAA,CAAA,UAAO;IAChB,SAAS,qLAAA,CAAA,UAAO;IAChB,OAAA,qLAAA,CAAA,QAAK;IACL,QAAQ,qLAAA,CAAA,QAAK;IACb,QAAQ,qLAAA,CAAA,QAAK;IACb,QAAQ,qLAAA,CAAA,QAAK;IACb,MAAM,qLAAA,CAAA,UAAO;IACb,OAAO,qLAAA,CAAA,UAAO;IACd,OAAO,qLAAA,CAAA,UAAO;IACd,UAAU,qLAAA,CAAA,KAAE;IACZ,YAAY,qLAAA,CAAA,KAAE;IACd,YAAY,qLAAA,CAAA,KAAE;IACd,YAAY,qLAAA,CAAA,KAAE;IACd,GAAG,qLAAA,CAAA,KAAE;IACL,GAAG,qLAAA,CAAA,KAAE;IACL,GAAG,qLAAA,CAAA,KAAE;IACL,aAAa,qLAAA,CAAA,KAAE;IACf,sBAAsB,qLAAA,CAAA,KAAE;IACxB,SAAS,qLAAA,CAAA,QAAK;IACd,SAAS,qLAAA,CAAA,qBAAkB;IAC3B,SAAS,qLAAA,CAAA,qBAAkB;IAC3B,SAAS,qLAAA,CAAA,KAAE;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3448, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/types/maps/number.mjs"], "sourcesContent": ["import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    ...transformValueTypes,\n    zIndex: int,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB;IACrB,eAAe;IACf,aAAa,qLAAA,CAAA,KAAE;IACf,gBAAgB,qLAAA,CAAA,KAAE;IAClB,kBAAkB,qLAAA,CAAA,KAAE;IACpB,mBAAmB,qLAAA,CAAA,KAAE;IACrB,iBAAiB,qLAAA,CAAA,KAAE;IACnB,cAAc,qLAAA,CAAA,KAAE;IAChB,QAAQ,qLAAA,CAAA,KAAE;IACV,qBAAqB,qLAAA,CAAA,KAAE;IACvB,sBAAsB,qLAAA,CAAA,KAAE;IACxB,yBAAyB,qLAAA,CAAA,KAAE;IAC3B,wBAAwB,qLAAA,CAAA,KAAE;IAC1B,oBAAoB;IACpB,OAAO,qLAAA,CAAA,KAAE;IACT,UAAU,qLAAA,CAAA,KAAE;IACZ,QAAQ,qLAAA,CAAA,KAAE;IACV,WAAW,qLAAA,CAAA,KAAE;IACb,KAAK,qLAAA,CAAA,KAAE;IACP,OAAO,qLAAA,CAAA,KAAE;IACT,QAAQ,qLAAA,CAAA,KAAE;IACV,MAAM,qLAAA,CAAA,KAAE;IACR,gBAAgB;IAChB,SAAS,qLAAA,CAAA,KAAE;IACX,YAAY,qLAAA,CAAA,KAAE;IACd,cAAc,qLAAA,CAAA,KAAE;IAChB,eAAe,qLAAA,CAAA,KAAE;IACjB,aAAa,qLAAA,CAAA,KAAE;IACf,QAAQ,qLAAA,CAAA,KAAE;IACV,WAAW,qLAAA,CAAA,KAAE;IACb,aAAa,qLAAA,CAAA,KAAE;IACf,cAAc,qLAAA,CAAA,KAAE;IAChB,YAAY,qLAAA,CAAA,KAAE;IACd,OAAO;IACP,qBAAqB,qLAAA,CAAA,KAAE;IACvB,qBAAqB,qLAAA,CAAA,KAAE;IACvB,GAAG,sLAAA,CAAA,sBAAmB;IACtB,QAAQ,wKAAA,CAAA,MAAG;IACX,MAAM;IACN,aAAa,qLAAA,CAAA,QAAK;IAClB,eAAe,qLAAA,CAAA,QAAK;IACpB,YAAY,wKAAA,CAAA,MAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3507, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/frameloop/order.mjs"], "sourcesContent": ["const stepsOrder = [\n    \"setup\", // Compute\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"preUpdate\", // Compute\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\n\nexport { stepsOrder };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3525, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/stats/buffer.mjs"], "sourcesContent": ["const statsBuffer = {\n    value: null,\n    addProjectionMetrics: null,\n};\n\nexport { statsBuffer };\n"], "names": [], "mappings": ";;;AAAA,MAAM,cAAc;IAChB,OAAO;IACP,sBAAsB;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3537, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/frameloop/render-step.mjs"], "sourcesContent": ["import { statsBuffer } from '../stats/buffer.mjs';\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && statsBuffer.value) {\n                statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,iBAAiB,YAAY,EAAE,QAAQ;IAC5C;;;KAGC,GACD,IAAI,YAAY,IAAI;IACpB,IAAI,YAAY,IAAI;IACpB;;;KAGC,GACD,IAAI,eAAe;IACnB,IAAI,iBAAiB;IACrB;;KAEC,GACD,MAAM,cAAc,IAAI;IACxB,IAAI,kBAAkB;QAClB,OAAO;QACP,WAAW;QACX,cAAc;IAClB;IACA,IAAI,WAAW;IACf,SAAS,gBAAgB,QAAQ;QAC7B,IAAI,YAAY,GAAG,CAAC,WAAW;YAC3B,KAAK,QAAQ,CAAC;YACd;QACJ;QACA;QACA,SAAS;IACb;IACA,MAAM,OAAO;QACT;;SAEC,GACD,UAAU,SAAC;gBAAU,6EAAY,OAAO,6EAAY;YAChD,MAAM,oBAAoB,aAAa;YACvC,MAAM,QAAQ,oBAAoB,YAAY;YAC9C,IAAI,WACA,YAAY,GAAG,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,CAAC,WACX,MAAM,GAAG,CAAC;YACd,OAAO;QACX;QACA;;SAEC,GACD,QAAQ,CAAC;YACL,UAAU,MAAM,CAAC;YACjB,YAAY,MAAM,CAAC;QACvB;QACA;;SAEC,GACD,SAAS,CAAC;YACN,kBAAkB;YAClB;;;;aAIC,GACD,IAAI,cAAc;gBACd,iBAAiB;gBACjB;YACJ;YACA,eAAe;YACf,CAAC,WAAW,UAAU,GAAG;gBAAC;gBAAW;aAAU;YAC/C,qBAAqB;YACrB,UAAU,OAAO,CAAC;YAClB;;aAEC,GACD,IAAI,YAAY,kKAAA,CAAA,cAAW,CAAC,KAAK,EAAE;gBAC/B,kKAAA,CAAA,cAAW,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;YAC/C;YACA,WAAW;YACX,2DAA2D;YAC3D,4DAA4D;YAC5D,UAAU,KAAK;YACf,eAAe;YACf,IAAI,gBAAgB;gBAChB,iBAAiB;gBACjB,KAAK,OAAO,CAAC;YACjB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/frameloop/batcher.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { stepsOrder } from './order.mjs';\nimport { createRenderStep } from './render-step.mjs';\n\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = createRenderStep(flagRunNextFrame, allowKeepAlive ? key : undefined);\n        return acc;\n    }, {});\n    const { setup, read, resolveKeyframes, preUpdate, update, preRender, render, postRender, } = steps;\n    const processBatch = () => {\n        const timestamp = MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        if (!MotionGlobalConfig.useManualTiming) {\n            state.delta = useDefaultElapsed\n                ? 1000 / 60\n                : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        }\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        setup.process(state);\n        read.process(state);\n        resolveKeyframes.process(state);\n        preUpdate.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\nexport { createRenderBatcher };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa;AACnB,SAAS,oBAAoB,iBAAiB,EAAE,cAAc;IAC1D,IAAI,eAAe;IACnB,IAAI,oBAAoB;IACxB,MAAM,QAAQ;QACV,OAAO;QACP,WAAW;QACX,cAAc;IAClB;IACA,MAAM,mBAAmB,IAAO,eAAe;IAC/C,MAAM,QAAQ,qKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK;QAClC,GAAG,CAAC,IAAI,GAAG,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,iBAAiB,MAAM;QACrE,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAG,GAAG;IAC7F,MAAM,eAAe;QACjB,MAAM,YAAY,qKAAA,CAAA,qBAAkB,CAAC,eAAe,GAC9C,MAAM,SAAS,GACf,YAAY,GAAG;QACrB,eAAe;QACf,IAAI,CAAC,qKAAA,CAAA,qBAAkB,CAAC,eAAe,EAAE;YACrC,MAAM,KAAK,GAAG,oBACR,OAAO,KACP,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,MAAM,SAAS,EAAE,aAAa;QACtE;QACA,MAAM,SAAS,GAAG;QAClB,MAAM,YAAY,GAAG;QACrB,wDAAwD;QACxD,MAAM,OAAO,CAAC;QACd,KAAK,OAAO,CAAC;QACb,iBAAiB,OAAO,CAAC;QACzB,UAAU,OAAO,CAAC;QAClB,OAAO,OAAO,CAAC;QACf,UAAU,OAAO,CAAC;QAClB,OAAO,OAAO,CAAC;QACf,WAAW,OAAO,CAAC;QACnB,MAAM,YAAY,GAAG;QACrB,IAAI,gBAAgB,gBAAgB;YAChC,oBAAoB;YACpB,kBAAkB;QACtB;IACJ;IACA,MAAM,OAAO;QACT,eAAe;QACf,oBAAoB;QACpB,IAAI,CAAC,MAAM,YAAY,EAAE;YACrB,kBAAkB;QACtB;IACJ;IACA,MAAM,WAAW,qKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAC,KAAK;QACrC,MAAM,OAAO,KAAK,CAAC,IAAI;QACvB,GAAG,CAAC,IAAI,GAAG,SAAC;gBAAS,6EAAY,OAAO,6EAAY;YAChD,IAAI,CAAC,cACD;YACJ,OAAO,KAAK,QAAQ,CAAC,SAAS,WAAW;QAC7C;QACA,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,SAAS,CAAC;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,qKAAA,CAAA,aAAU,CAAC,MAAM,EAAE,IAAK;YACxC,KAAK,CAAC,qKAAA,CAAA,aAAU,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;QAChC;IACJ;IACA,OAAO;QAAE;QAAU;QAAQ;QAAO;IAAM;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3709, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/frameloop/frame.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { createRenderBatcher } from './batcher.mjs';\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = /* @__PURE__ */ createRenderBatcher(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : noop, true);\n\nexport { cancelFrame, frame, frameData, frameSteps };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,EAAE,UAAU,KAAK,EAAE,QAAQ,WAAW,EAAE,OAAO,SAAS,EAAE,OAAO,UAAU,EAAG,GAAG,aAAa,GAAG,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,0BAA0B,cAAc,wBAAwB,yJAAA,CAAA,OAAI,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3725, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { frameData } from './frame.mjs';\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(frameData.isProcessing || MotionGlobalConfig.useManualTiming\n                ? frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\nexport { time };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI;AACJ,SAAS;IACL,MAAM;AACV;AACA;;;;;;;CAOC,GACD,MAAM,OAAO;IACT,KAAK;QACD,IAAI,QAAQ,WAAW;YACnB,KAAK,GAAG,CAAC,qKAAA,CAAA,YAAS,CAAC,YAAY,IAAI,qKAAA,CAAA,qBAAkB,CAAC,eAAe,GAC/D,qKAAA,CAAA,YAAS,CAAC,SAAS,GACnB,YAAY,GAAG;QACzB;QACA,OAAO;IACX;IACA,KAAK,CAAC;QACF,MAAM;QACN,eAAe;IACnB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3760, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/value/index.mjs"], "sourcesContent": ["import { warnOnce, SubscriptionManager, velocityPerSecond } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     */\n    constructor(init, options = {}) {\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v) => {\n            const currentTime = time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev) {\n                this.events.change?.notify(this.current);\n                if (this.dependents) {\n                    for (const dependent of this.dependents) {\n                        dependent.dirty();\n                    }\n                }\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v) {\n        if (!this.passiveEffect) {\n            this.updateAndNotify(v);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    dirty() {\n        this.events.change?.notify(this.current);\n    }\n    addDependent(dependent) {\n        if (!this.dependents) {\n            this.dependents = new Set();\n        }\n        this.dependents.add(dependent);\n    }\n    removeDependent(dependent) {\n        if (this.dependents) {\n            this.dependents.delete(dependent);\n        }\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return velocityPerSecond(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.dependents?.clear();\n        this.events.destroy?.notify();\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\nexport { MotionValue, collectMotionValues, motionValue };\n"], "names": [], "mappings": ";;;;;AAsHY;AAtHZ;AAAA;AAAA;AACA;AACA;;;;AAEA;;;CAGC,GACD,MAAM,qBAAqB;AAC3B,MAAM,UAAU,CAAC;IACb,OAAO,CAAC,MAAM,WAAW;AAC7B;AACA,MAAM,sBAAsB;IACxB,SAAS;AACb;AACA;;;;CAIC,GACD,MAAM;IA8CF,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,4KAAA,CAAA,OAAI,CAAC,GAAG;QACzB,IAAI,IAAI,CAAC,gBAAgB,KAAK,QAAQ,YAAY,WAAW;YACzD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,IAAI,CAAC,OAAO;QAChD;IACJ;IACA,oBAAiD;YAA/B,iBAAA,iEAAiB,IAAI,CAAC,OAAO;QAC3C,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS;IACvC;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuCC,GACD,SAAS,YAAY,EAAE;QACnB,wCAA2C;YACvC,CAAA,GAAA,iKAAA,CAAA,WAAQ,AAAD,EAAE,OAAQ;QACrB;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU;IAC7B;IACA,GAAG,SAAS,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,4KAAA,CAAA,sBAAmB;QACpD;QACA,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;QAC/C,IAAI,cAAc,UAAU;YACxB,OAAO;gBACH;gBACA;;;iBAGC,GACD,qKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACP,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI;wBAC/B,IAAI,CAAC,IAAI;oBACb;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB;QACb,IAAK,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAE;YACrC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK;QACpC;IACJ;IACA;;KAEC,GACD,OAAO,aAAa,EAAE,iBAAiB,EAAE;QACrC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA;;;;;;;;;;;;;;KAcC,GACD,IAAI,CAAC,EAAE;QACH,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,eAAe,CAAC;QACzB,OACK;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,eAAe;QAC9C;IACJ;IACA,gBAAgB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE;QAClC,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,GAAG;IAC1C;IACA;;;KAGC,GACD,KAAK,CAAC,EAAuB;YAArB,eAAA,iEAAe;QACnB,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG;QAC3C,gBAAgB,IAAI,CAAC,IAAI;QACzB,IAAI,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,iBAAiB;IAC9B;IACA,QAAQ;YACJ;SAAA,sBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,IAAI,CAAC,OAAO;IAC3C;IACA,aAAa,SAAS,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI;QAC1B;QACA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;IACxB;IACA,gBAAgB,SAAS,EAAE;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC3B;IACJ;IACA;;;;;;KAMC,GACD,MAAM;QACF,IAAI,oBAAoB,OAAO,EAAE;YAC7B,oBAAoB,OAAO,CAAC,IAAI,CAAC,IAAI;QACzC;QACA,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;KAEC,GACD,cAAc;QACV,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;;;;;KAMC,GACD,cAAc;QACV,MAAM,cAAc,4KAAA,CAAA,OAAI,CAAC,GAAG;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IACtB,IAAI,CAAC,cAAc,KAAK,aACxB,cAAc,IAAI,CAAC,SAAS,GAAG,oBAAoB;YACnD,OAAO;QACX;QACA,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;QAC5D,4CAA4C;QAC5C,OAAO,CAAA,GAAA,8KAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,IAAI,CAAC,OAAO,IAC5C,WAAW,IAAI,CAAC,cAAc,GAAG;IACzC;IACA;;;;;;;;;KASC,GACD,MAAM,cAAc,EAAE;QAClB,IAAI,CAAC,IAAI;QACT,OAAO,IAAI,QAAQ,CAAC;YAChB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,SAAS,GAAG,eAAe;YAChC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM;YACrC;QACJ,GAAG,IAAI,CAAC;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;gBAC/B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM;YACxC;YACA,IAAI,CAAC,cAAc;QACvB;IACJ;IACA;;;;KAIC,GACD,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,IAAI;YACnB,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM;YACtC;QACJ;QACA,IAAI,CAAC,cAAc;IACvB;IACA;;;;KAIC,GACD,cAAc;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS;IAC3B;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,SAAS;IACzB;IACA;;;;;;;;KAQC,GACD,UAAU;YACN,kBACA;SADA,mBAAA,IAAI,CAAC,UAAU,cAAf,uCAAA,iBAAiB,KAAK;SACtB,uBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,cAAnB,2CAAA,qBAAqB,MAAM;QAC3B,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,IAAI;QACT,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB;QAC1B;IACJ;IAvSA;;;;;KAKC,GACD,YAAY,IAAI,EAAE,UAAU,CAAC,CAAC,CAAE;QAC5B;;;;;;SAMC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,eAAe,GAAG,CAAC;YACpB,MAAM,cAAc,4KAAA,CAAA,OAAI,CAAC,GAAG;YAC5B;;;;aAIC,GACD,IAAI,IAAI,CAAC,SAAS,KAAK,aAAa;gBAChC,IAAI,CAAC,iBAAiB;YAC1B;YACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO;YACxB,IAAI,CAAC,UAAU,CAAC;YAChB,4BAA4B;YAC5B,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE;oBAC5B;iBAAA,sBAAA,IAAI,CAAC,MAAM,CAAC,MAAM,cAAlB,0CAAA,oBAAoB,MAAM,CAAC,IAAI,CAAC,OAAO;gBACvC,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,KAAK,MAAM,aAAa,IAAI,CAAC,UAAU,CAAE;wBACrC,UAAU,KAAK;oBACnB;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK;IAC9B;AA4PJ;AACA,SAAS,YAAY,IAAI,EAAE,OAAO;IAC9B,OAAO,IAAI,YAAY,MAAM;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4078, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/motion-dom/dist/es/animation/waapi/utils/accelerated-values.mjs"], "sourcesContent": ["/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Can be accelerated but currently disabled until https://issues.chromium.org/issues/41491098 is resolved\n    // or until we implement support for linear() easing.\n    // \"background-color\"\n]);\n\nexport { acceleratedValues };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;CAIH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4094, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/menu.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/menu.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 12h16', key: '1lakjw' }],\n  ['path', { d: 'M4 18h16', key: '19g7jn' }],\n  ['path', { d: 'M4 6h16', key: '1o0s65' }],\n];\n\n/**\n * @component @name Menu\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxMmgxNiIgLz4KICA8cGF0aCBkPSJNNCAxOGgxNiIgLz4KICA8cGF0aCBkPSJNNCA2aDE2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/menu\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Menu = createLucideIcon('menu', __iconNode);\n\nexport default Menu;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4143, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/bell-ring.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/bell-ring.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10.268 21a2 2 0 0 0 3.464 0', key: 'vwvbt9' }],\n  ['path', { d: 'M22 8c0-2.3-.8-4.3-2-6', key: '5bb3ad' }],\n  [\n    'path',\n    {\n      d: 'M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326',\n      key: '11g9vi',\n    },\n  ],\n  ['path', { d: 'M4 2C2.8 3.7 2 5.7 2 8', key: 'tap9e0' }],\n];\n\n/**\n * @component @name BellRing\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMjY4IDIxYTIgMiAwIDAgMCAzLjQ2NCAwIiAvPgogIDxwYXRoIGQ9Ik0yMiA4YzAtMi4zLS44LTQuMy0yLTYiIC8+CiAgPHBhdGggZD0iTTMuMjYyIDE1LjMyNkExIDEgMCAwIDAgNCAxN2gxNmExIDEgMCAwIDAgLjc0LTEuNjczQzE5LjQxIDEzLjk1NiAxOCAxMi40OTkgMTggOEE2IDYgMCAwIDAgNiA4YzAgNC40OTktMS40MTEgNS45NTYtMi43MzggNy4zMjYiIC8+CiAgPHBhdGggZD0iTTQgMkMyLjggMy43IDIgNS43IDIgOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bell-ring\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BellRing = createLucideIcon('bell-ring', __iconNode);\n\nexport default BellRing;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC7D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4199, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/book-check.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/book-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20',\n      key: 'k3hazp',\n    },\n  ],\n  ['path', { d: 'm9 9.5 2 2 4-4', key: '1dth82' }],\n];\n\n/**\n * @component @name BookCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDE5YTEgMSAwIDAgMSAxIDF2MThhMSAxIDAgMCAxLTEgMUg2LjVhMSAxIDAgMCAxIDAtNUgyMCIgLz4KICA8cGF0aCBkPSJtOSA5LjUgMiAyIDQtNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookCheck = createLucideIcon('book-check', __iconNode);\n\nexport default BookCheck;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4241, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,cAAgB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa7E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4276, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/database.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/database.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 5V19A9 3 0 0 0 21 19V5', key: '1wlel7' }],\n  ['path', { d: 'M3 12A9 3 0 0 0 21 12', key: 'mv7ke4' }],\n];\n\n/**\n * @component @name Database\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgNVYxOUE5IDMgMCAwIDAgMjEgMTlWNSIgLz4KICA8cGF0aCBkPSJNMyAxMkE5IDMgMCAwIDAgMjEgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/database\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Database = createLucideIcon('database', __iconNode);\n\nexport default Database;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAW,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1D;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4328, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/file-search.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/file-search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  [\n    'path',\n    { d: 'M4.268 21a2 2 0 0 0 1.727 1H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3', key: 'ms7g94' },\n  ],\n  ['path', { d: 'm9 18-1.5-1.5', key: '1j6qii' }],\n  ['circle', { cx: '5', cy: '14', r: '3', key: 'ufru5t' }],\n];\n\n/**\n * @component @name FileSearch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMnY0YTIgMiAwIDAgMCAyIDJoNCIgLz4KICA8cGF0aCBkPSJNNC4yNjggMjFhMiAyIDAgMCAwIDEuNzI3IDFIMThhMiAyIDAgMCAwIDItMlY3bC01LTVINmEyIDIgMCAwIDAtMiAydjMiIC8+CiAgPHBhdGggZD0ibTkgMTgtMS41LTEuNSIgLz4KICA8Y2lyY2xlIGN4PSI1IiBjeT0iMTQiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileSearch = createLucideIcon('file-search', __iconNode);\n\nexport default FileSearch;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YAAE,CAAA,CAAA,CAAG,uEAAyE,CAAA;YAAA,CAAA,CAAA,CAAA,EAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAC9F;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4386, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/ham.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/ham.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.144 21.144A7.274 10.445 45 1 0 2.856 10.856', key: '1k1t7q' }],\n  [\n    'path',\n    {\n      d: 'M13.144 21.144A7.274 4.365 45 0 0 2.856 10.856a7.274 4.365 45 0 0 10.288 10.288',\n      key: '153t1g',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M16.565 10.435 18.6 8.4a2.501 2.501 0 1 0 1.65-4.65 2.5 2.5 0 1 0-4.66 1.66l-2.024 2.025',\n      key: 'gzrt0n',\n    },\n  ],\n  ['path', { d: 'm8.5 16.5-1-1', key: 'otr954' }],\n];\n\n/**\n * @component @name Ham\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuMTQ0IDIxLjE0NEE3LjI3NCAxMC40NDUgNDUgMSAwIDIuODU2IDEwLjg1NiIgLz4KICA8cGF0aCBkPSJNMTMuMTQ0IDIxLjE0NEE3LjI3NCA0LjM2NSA0NSAwIDAgMi44NTYgMTAuODU2YTcuMjc0IDQuMzY1IDQ1IDAgMCAxMC4yODggMTAuMjg4IiAvPgogIDxwYXRoIGQ9Ik0xNi41NjUgMTAuNDM1IDE4LjYgOC40YTIuNTAxIDIuNTAxIDAgMSAwIDEuNjUtNC42NSAyLjUgMi41IDAgMSAwLTQuNjYgMS42NmwtMi4wMjQgMi4wMjUiIC8+CiAgPHBhdGggZD0ibTguNSAxNi41LTEtMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/ham\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ham = createLucideIcon('ham', __iconNode);\n\nexport default Ham;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChF;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4442, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/house.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4484, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/settings.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/settings.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('settings', __iconNode);\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4528, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/users.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,OAAA;QAAS,CAAA;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4586, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/building-2.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/building-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z', key: '1b4qmf' }],\n  ['path', { d: 'M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2', key: 'i71pzd' }],\n  ['path', { d: 'M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2', key: '10jefs' }],\n  ['path', { d: 'M10 6h4', key: '1itunk' }],\n  ['path', { d: 'M10 10h4', key: 'tcdvrf' }],\n  ['path', { d: 'M10 14h4', key: 'kelpxr' }],\n  ['path', { d: 'M10 18h4', key: '1ulq68' }],\n];\n\n/**\n * @component @name Building2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAyMlY0YTIgMiAwIDAgMSAyLTJoOGEyIDIgMCAwIDEgMiAydjE4WiIgLz4KICA8cGF0aCBkPSJNNiAxMkg0YTIgMiAwIDAgMC0yIDJ2NmEyIDIgMCAwIDAgMiAyaDIiIC8+CiAgPHBhdGggZD0iTTE4IDloMmEyIDIgMCAwIDEgMiAydjlhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNMTAgNmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxMGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxNGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOGg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/building-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building2 = createLucideIcon('building-2', __iconNode);\n\nexport default Building2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4663, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/chart-column.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['path', { d: 'M18 17V9', key: '2bz60n' }],\n  ['path', { d: 'M13 17V5', key: '1frdt8' }],\n  ['path', { d: 'M8 17v-3', key: '17ska0' }],\n];\n\n/**\n * @component @name ChartColumn\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTE4IDE3VjkiIC8+CiAgPHBhdGggZD0iTTEzIDE3VjUiIC8+CiAgPHBhdGggZD0iTTggMTd2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartColumn = createLucideIcon('chart-column', __iconNode);\n\nexport default ChartColumn;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4719, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/file-text.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/file-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('file-text', __iconNode);\n\nexport default FileText;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4782, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/credit-card.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '14', x: '2', y: '5', rx: '2', key: 'ynyp8z' }],\n  ['line', { x1: '2', x2: '22', y1: '10', y2: '10', key: '1b3vmo' }],\n];\n\n/**\n * @component @name CreditCard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjUiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIxMCIgeTI9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/credit-card\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CreditCard = createLucideIcon('credit-card', __iconNode);\n\nexport default CreditCard;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4831, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/banknote.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/banknote.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '12', x: '2', y: '6', rx: '2', key: '9lu3g6' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n  ['path', { d: 'M6 12h.01M18 12h.01', key: '113zkx' }],\n];\n\n/**\n * @component @name Banknote\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjYiIHJ4PSIyIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTYgMTJoLjAxTTE4IDEyaC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/banknote\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Banknote = createLucideIcon('banknote', __iconNode);\n\nexport default Banknote;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACtD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4886, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/upload.js", "sources": ["file:///D:/sintesaNEXT2/node_modules/lucide-react/src/icons/upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3v12', key: '1x0j5s' }],\n  ['path', { d: 'm17 8-5-5-5 5', key: '7q97r8' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3YxMiIgLz4KICA8cGF0aCBkPSJtMTcgOC01LTUtNSA1IiAvPgogIDxwYXRoIGQ9Ik0yMSAxNXY0YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('upload', __iconNode);\n\nexport default Upload;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 4935, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@internationalized/string/dist/LocalizedStringDictionary.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40internationalized/string/dist/packages/%40internationalized/string/src/LocalizedStringDictionary.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {LocalizedString} from './LocalizedStringFormatter';\n\nexport type LocalizedStrings<K extends string, T extends LocalizedString> = {\n  [lang: string]: Record<K, T>\n};\n\nconst localeSymbol = Symbol.for('react-aria.i18n.locale');\nconst stringsSymbol = Symbol.for('react-aria.i18n.strings');\nlet cachedGlobalStrings: {[packageName: string]: LocalizedStringDictionary<any, any>} | null | undefined = undefined;\n\n/**\n * Stores a mapping of localized strings. Can be used to find the\n * closest available string for a given locale.\n */\nexport class LocalizedStringDictionary<K extends string = string, T extends LocalizedString = string> {\n  private strings: LocalizedStrings<K, T>;\n  private defaultLocale: string;\n\n  constructor(messages: LocalizedStrings<K, T>, defaultLocale: string = 'en-US') {\n    // Clone messages so we don't modify the original object.\n    // Filter out entries with falsy values which may have been caused by applying optimize-locales-plugin.\n    this.strings = Object.fromEntries(\n      Object.entries(messages).filter(([, v]) => v)\n    );\n    this.defaultLocale = defaultLocale;\n  }\n\n  /** Returns a localized string for the given key and locale. */\n  getStringForLocale(key: K, locale: string): T {\n    let strings = this.getStringsForLocale(locale);\n    let string = strings[key];\n    if (!string) {\n      throw new Error(`Could not find intl message ${key} in ${locale} locale`);\n    }\n\n    return string;\n  }\n\n  /** Returns all localized strings for the given locale. */\n  getStringsForLocale(locale: string): Record<K, T> {\n    let strings = this.strings[locale];\n    if (!strings) {\n      strings = getStringsForLocale(locale, this.strings, this.defaultLocale);\n      this.strings[locale] = strings;\n    }\n\n    return strings;\n  }\n\n  static getGlobalDictionaryForPackage<K extends string = string, T extends LocalizedString = string>(packageName: string): LocalizedStringDictionary<K, T> | null {\n    if (typeof window === 'undefined') {\n      return null;\n    }\n\n    let locale = window[localeSymbol];\n    if (cachedGlobalStrings === undefined) {\n      let globalStrings = window[stringsSymbol];\n      if (!globalStrings) {\n        return null;\n      }\n\n      cachedGlobalStrings = {};\n      for (let pkg in globalStrings) {\n        cachedGlobalStrings[pkg] = new LocalizedStringDictionary({[locale]: globalStrings[pkg]}, locale);\n      }\n    }\n\n    let dictionary = cachedGlobalStrings?.[packageName];\n    if (!dictionary) {\n      throw new Error(`Strings for package \"${packageName}\" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);\n    }\n\n    return dictionary;\n  }\n}\n\nfunction getStringsForLocale<K extends string, T extends LocalizedString>(locale: string, strings: LocalizedStrings<K, T>, defaultLocale = 'en-US') {\n  // If there is an exact match, use it.\n  if (strings[locale]) {\n    return strings[locale];\n  }\n\n  // Attempt to find the closest match by language.\n  // For example, if the locale is fr-CA (French Canadian), but there is only\n  // an fr-FR (France) set of strings, use that.\n  // This could be replaced with Intl.LocaleMatcher once it is supported.\n  // https://github.com/tc39/proposal-intl-localematcher\n  let language = getLanguage(locale);\n  if (strings[language]) {\n    return strings[language];\n  }\n\n  for (let key in strings) {\n    if (key.startsWith(language + '-')) {\n      return strings[key];\n    }\n  }\n\n  // Nothing close, use english.\n  return strings[defaultLocale];\n}\n\nfunction getLanguage(locale: string) {\n  // @ts-ignore\n  if (Intl.Locale) {\n    // @ts-ignore\n    return new Intl.Locale(locale).language;\n  }\n\n  return locale.split('-')[0];\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAQD,MAAM,qCAAe,OAAO,GAAG,CAAC;AAChC,MAAM,sCAAgB,OAAO,GAAG,CAAC;AACjC,IAAI,4CAAuG;AAMpG,MAAM;IAaX,6DAA6D,GAC7D,mBAAmB,GAAM,EAAE,MAAc,EAAK;QAC5C,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC;QACvC,IAAI,SAAS,OAAO,CAAC,IAAI;QACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM,AAAC,4BAA4B,GAAY,OAAV,KAAI,IAAI,IAAS,OAAO,CAAC;QAG1E,OAAO;IACT;IAEA,wDAAwD,GACxD,oBAAoB,MAAc,EAAgB;QAChD,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO;QAClC,IAAI,CAAC,SAAS;YACZ,UAAU,0CAAoB,QAAQ,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa;YACtE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;QACzB;QAEA,OAAO;IACT;IAEA,OAAO,8BAA6F,WAAmB,EAA0C;QAC/J,IAAI,OAAO,WAAW,aACpB,OAAO;QAGT,IAAI,SAAS,MAAM,CAAC,mCAAa;QACjC,IAAI,8CAAwB,WAAW;YACrC,IAAI,gBAAgB,MAAM,CAAC,oCAAc;YACzC,IAAI,CAAC,eACH,OAAO;YAGT,4CAAsB,CAAC;YACvB,IAAK,IAAI,OAAO,cACd,yCAAmB,CAAC,IAAI,GAAG,IAAI,0CAA0B;gBAAC,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI;YAAA,GAAG;QAE7F;QAEA,IAAI,aAAa,8CAAA,QAAA,8CAAA,KAAA,IAAA,KAAA,IAAA,yCAAqB,CAAC,YAAY;QACnD,IAAI,CAAC,YACH,MAAM,IAAI,MAAO,AAAD,qBAAsB,GAAc,OAAZ,6GAAgI,CAAC;QAG3K,OAAO;IACT;IAvDA,YAAY,QAAgC,EAAE,gBAAwB,OAAO,CAAE;QAC7E,yDAAyD;QACzD,uGAAuG;QACvG,IAAI,CAAC,OAAO,GAAG,OAAO,WAAW,CAC/B,OAAO,OAAO,CAAC,UAAU,MAAM,CAAC;gBAAC,GAAG,EAAE;mBAAK;;QAE7C,IAAI,CAAC,aAAa,GAAG;IACvB;AAiDF;AAEA,SAAS,0CAAiE,MAAc,EAAE,OAA+B;wBAAE,iEAAgB,OAAO;IAChJ,sCAAsC;IACtC,IAAI,OAAO,CAAC,OAAO,EACjB,OAAO,OAAO,CAAC,OAAO;IAGxB,iDAAiD;IACjD,2EAA2E;IAC3E,8CAA8C;IAC9C,uEAAuE;IACvE,sDAAsD;IACtD,IAAI,WAAW,kCAAY;IAC3B,IAAI,OAAO,CAAC,SAAS,EACnB,OAAO,OAAO,CAAC,SAAS;IAG1B,IAAK,IAAI,OAAO,QAAS;QACvB,IAAI,IAAI,UAAU,CAAC,WAAW,MAC5B,OAAO,OAAO,CAAC,IAAI;IAEvB;IAEA,8BAA8B;IAC9B,OAAO,OAAO,CAAC,cAAc;AAC/B;AAEA,SAAS,kCAAY,MAAc;IACjC,aAAa;IACb,IAAI,KAAK,MAAM,EACb,AACA,OAAO,IAAI,EADE,GACG,MAAM,CAAC,QAAQ,QAAQ;IAGzC,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;AAC7B", "debugId": null}}, {"offset": {"line": 5019, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@internationalized/string/dist/LocalizedStringFormatter.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40internationalized/string/dist/packages/%40internationalized/string/src/LocalizedStringFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {LocalizedStringDictionary} from './LocalizedStringDictionary';\n\nexport type Variables = Record<string, string | number | boolean> | undefined;\nexport type LocalizedString = string | ((args: Variables, formatter?: LocalizedStringFormatter<any, any>) => string);\ntype InternalString = string | (() => string);\n\nconst pluralRulesCache = new Map<string, Intl.PluralRules>();\nconst numberFormatCache = new Map<string, Intl.NumberFormat>();\n\n/**\n * Formats localized strings from a LocalizedStringDictionary. Supports interpolating variables,\n * selecting the correct pluralization, and formatting numbers for the locale.\n */\nexport class LocalizedStringFormatter<K extends string = string, T extends LocalizedString = string> {\n  private locale: string;\n  private strings: LocalizedStringDictionary<K, T>;\n\n  constructor(locale: string, strings: LocalizedStringDictionary<K, T>) {\n    this.locale = locale;\n    this.strings = strings;\n  }\n\n  /** Formats a localized string for the given key with the provided variables. */\n  format(key: K, variables?: Variables): string {\n    let message = this.strings.getStringForLocale(key, this.locale);\n    return typeof message === 'function' ? message(variables, this) : message;\n  }\n\n  protected plural(count: number, options: Record<string, InternalString>, type: Intl.PluralRuleType = 'cardinal'): string {\n    let opt = options['=' + count];\n    if (opt) {\n      return typeof opt === 'function' ? opt() : opt;\n    }\n\n    let key = this.locale + ':' + type;\n    let pluralRules = pluralRulesCache.get(key);\n    if (!pluralRules) {\n      pluralRules = new Intl.PluralRules(this.locale, {type});\n      pluralRulesCache.set(key, pluralRules);\n    }\n\n    let selected = pluralRules.select(count);\n    opt = options[selected] || options.other;\n    return typeof opt === 'function' ? opt() : opt;\n  }\n\n  protected number(value: number): string {\n    let numberFormat = numberFormatCache.get(this.locale);\n    if (!numberFormat) {\n      numberFormat = new Intl.NumberFormat(this.locale);\n      numberFormatCache.set(this.locale, numberFormat);\n    }\n    return numberFormat.format(value);\n  }\n\n  protected select(options: Record<string, InternalString>, value: string): string {\n    let opt = options[value] || options.other;\n    return typeof opt === 'function' ? opt() : opt;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAQD,MAAM,yCAAmB,IAAI;AAC7B,MAAM,0CAAoB,IAAI;AAMvB,MAAM;IASX,8EAA8E,GAC9E,OAAO,GAAM,EAAE,SAAqB,EAAU;QAC5C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,MAAM;QAC9D,OAAO,OAAO,YAAY,aAAa,QAAQ,WAAW,IAAI,IAAI;IACpE;IAEU,OAAO,KAAa,EAAE,OAAuC,EAAkD;mBAAhD,iEAA4B,UAAU;QAC7G,IAAI,MAAM,OAAO,CAAC,MAAM,MAAM;QAC9B,IAAI,KACF,OAAO,OAAO,QAAQ,aAAa,QAAQ;QAG7C,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM;QAC9B,IAAI,cAAc,uCAAiB,GAAG,CAAC;QACvC,IAAI,CAAC,aAAa;YAChB,cAAc,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;sBAAC;YAAI;YACrD,uCAAiB,GAAG,CAAC,KAAK;QAC5B;QAEA,IAAI,WAAW,YAAY,MAAM,CAAC;QAClC,MAAM,OAAO,CAAC,SAAS,IAAI,QAAQ,KAAK;QACxC,OAAO,OAAO,QAAQ,aAAa,QAAQ;IAC7C;IAEU,OAAO,KAAa,EAAU;QACtC,IAAI,eAAe,wCAAkB,GAAG,CAAC,IAAI,CAAC,MAAM;QACpD,IAAI,CAAC,cAAc;YACjB,eAAe,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,MAAM;YAChD,wCAAkB,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;QACrC;QACA,OAAO,aAAa,MAAM,CAAC;IAC7B;IAEU,OAAO,OAAuC,EAAE,KAAa,EAAU;QAC/E,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,QAAQ,KAAK;QACzC,OAAO,OAAO,QAAQ,aAAa,QAAQ;IAC7C;IAzCA,YAAY,MAAc,EAAE,OAAwC,CAAE;QACpE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;AAuCF", "debugId": null}}]}