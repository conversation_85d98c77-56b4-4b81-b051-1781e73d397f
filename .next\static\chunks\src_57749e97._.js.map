{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/navbar/github-icon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nexport const GithubIcon = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"24\"\r\n      height=\"24\"\r\n      viewBox=\"0 0 512 512\"\r\n    >\r\n      <g id=\"_1528549580000\">\r\n        <polygon\r\n          className=\"fill-default-400\"\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          points=\"59.794,487.879 59.794,24.406 416.532,24.121 \r\n          416.532,202.489 380.858,202.489 380.858,60.08 95.468,60.08 95.468,452.206 416.532,452.063 416.532,487.594 \t\"\r\n        />\r\n        <path\r\n          className=\"fill-default-400\"\r\n          d=\"M357.884,416.817c-17.98,0-34.104-3.853-48.516-11.558c-14.27-7.706-25.542-18.408-33.676-31.964\r\n          c-8.134-13.556-12.272-28.824-12.272-45.662c0-16.981,4.138-32.249,12.272-45.662c8.134-13.556,19.407-24.258,33.676-31.964\r\n          c14.412-7.706,30.537-11.558,48.516-11.558c17.837,0,33.961,3.853,48.231,11.558c14.27,7.706,25.542,18.408,33.819,31.964\r\n          c8.134,13.413,12.272,28.682,12.272,45.662c0,16.838-4.138,32.106-12.272,45.662c-8.276,13.556-19.549,24.258-33.819,31.964\r\n          C391.846,412.965,375.721,416.817,357.884,416.817z M357.884,382.856c10.131,0,19.407-2.426,27.54-7.135\r\n          c8.276-4.709,14.698-11.13,19.407-19.549c4.566-8.419,6.992-17.837,6.992-28.539c0-10.702-2.426-20.263-6.992-28.682\r\n          c-4.709-8.276-11.13-14.84-19.407-19.549c-8.134-4.709-17.409-6.992-27.54-6.992c-10.274,0-19.407,2.283-27.683,6.992\r\n          c-8.134,4.709-14.555,11.273-19.264,19.549c-4.709,8.419-6.992,17.98-6.992,28.682c0,10.702,2.283,20.12,6.992,28.539\r\n          c4.709,8.419,11.13,14.84,19.264,19.549C338.478,380.43,347.61,382.856,357.884,382.856z\"\r\n        />\r\n      </g>\r\n    </svg>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,aAAa;IACxB,qBACE,6LAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,6LAAC;YAAE,IAAG;;8BACJ,6LAAC;oBACC,WAAU;oBACV,UAAS;oBACT,UAAS;oBACT,QAAO;;;;;;8BAGT,6LAAC;oBACC,WAAU;oBACV,GAAE;;;;;;;;;;;;;;;;;AAaZ;KA/Ba", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/encryption/encrypt.ts"], "sourcesContent": ["import CryptoJS from \"crypto-js\";\r\n\r\nconst secretKey = \"mebe23\";\r\nconst Encrypt = (word, key = secretKey) => {\r\n  let encJson = CryptoJS.AES.encrypt(JSON.stringify(word), key).toString();\r\n  let encData = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(encJson));\r\n  return encData;\r\n};\r\n\r\nexport default Encrypt;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY;AAClB,MAAM,UAAU,SAAC;QAAM,uEAAM;IAC3B,IAAI,UAAU,wIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC,OAAO,KAAK,QAAQ;IACtE,IAAI,UAAU,wIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,wIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;IACpE,OAAO;AACT;KAJM;uCAMS", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/lib/api/services/satkerService.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport Encrypt from \"@/lib/encryption/encrypt\";\r\nimport { useContext } from \"react\";\r\n\r\n// Utility function untuk mapping data dari database ke format SatkerItem\r\nconst mapDbToSatkerItem = (dbItem) => ({\r\n  id: dbItem.kdsatker,\r\n  kode: dbItem.kdsatker,\r\n  nama: dbItem.nmsatker,\r\n  kdsatker: dbItem.kdsatker,\r\n  nmsatker: dbItem.nmsatker,\r\n  email: dbItem.email,\r\n  kddept: dbItem.kddept,\r\n  nmdept: dbItem.nmdept,\r\n  kdunit: dbItem.kdunit,\r\n  nmunit: dbItem.nmunit,\r\n  kddekon: dbItem.kddekon,\r\n  nmdekon: dbItem.nmdekon,\r\n  kdkanwil: dbItem.kdkanwil,\r\n  nmkanwil: dbItem.nmkanwil,\r\n  kdkppn: dbItem.kdkppn,\r\n  nmkppn: dbItem.nmkppn,\r\n  thang: dbItem.thang,\r\n  kpa: dbItem.kpa,\r\n  bendahara: dbItem.bendahara,\r\n  ppspm: dbItem.ppspm,\r\n  npwp: dbItem.npwp,\r\n  statusblu: dbItem.statusblu,\r\n  kdjendok: dbItem.kdjendok,\r\n  level: dbItem.kddept,\r\n  alamat: `${dbItem.kdunit}`,\r\n});\r\n\r\nexport const useSatkerService = () => {\r\n  const context = useContext(MyContext);\r\n  const { token, axiosJWT } = context || {};\r\n\r\n  const cariSatker = async (kataPencarian) => {\r\n    const query = `\r\n        select kdsatker,nmsatker,kddept,kdunit from dbref.t_satker_2025 \r\n        where kdsatker like '%${kataPencarian}%' or nmsatker like '%${kataPencarian}%'\r\n        order by kdsatker limit 10;\r\n      `;\r\n    const cleanedQuery = query.replace(/\\n/g, \" \").replace(/\\s+/g, \" \").trim();\r\n\r\n    try {\r\n      const response = await axiosJWT.post(\r\n        process.env.NEXT_PUBLIC_GET_REFERENSI,\r\n        { query: Encrypt(cleanedQuery) }\r\n      );\r\n\r\n      if (response.status !== 200) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n      //   console.log(query);\r\n\r\n      // Map data dari response ke format SatkerItem\r\n      const mappedData = response.data.result.map((item) =>\r\n        mapDbToSatkerItem(item)\r\n      );\r\n\r\n      return mappedData;\r\n    } catch (error) {\r\n      console.error(\"Error dalam cariSatker:\", error);\r\n      throw new Error(\"Gagal mencari data satker\");\r\n    }\r\n  };\r\n\r\n  const ambilDetailSatker = async (idSatker) => {\r\n    const query = `\r\n    SELECT a.thang,a.kdjendok,a.kdsatker,c.kdkppn,b.nmsatker,a.kddept,d.nmdept,g.kdkanwil,a.kdunit,e.nmunit,\r\n    a.kddekon,a.kdlokasi,c.nmkppn,a.kpa,a.bendahara,a.ppspm,f.nmdekon,g.nmkanwil,a.npwp,a.statusblu,\r\n    a.email FROM dbdipa25.d_kpa a LEFT JOIN dbref.t_satker_2025 b ON a.kdsatker=b.kdsatker AND a.kddept=b.kddept \r\n    AND a.kdunit=b.kdunit LEFT JOIN dbref.t_kppn_2023 c ON b.kdkppn=c.kdkppn LEFT JOIN dbref.t_dept_2025 d \r\n    ON b.kddept=d.kddept LEFT JOIN dbref.t_unit_2025 e ON a.kddept=e.kddept AND b.kdunit=e.kdunit \r\n    LEFT JOIN dbref.t_dekon_2025 f ON a.kddekon=f.kddekon LEFT JOIN dbref.t_kanwil_2014 g \r\n    ON c.kdkanwil=g.kdkanwil WHERE a.kdsatker='${idSatker}' GROUP BY a.thang,a.kdjendok,a.kdsatker,c.kdkppn,b.nmsatker,a.kddept,d.nmdept,g.kdkanwil,a.kdunit,e.nmunit,a.kddekon,a.kdlokasi,c.nmkppn,a.kpa,a.bendahara,a.ppspm,f.nmdekon,g.nmkanwil,a.npwp,a.statusblu,a.email ORDER BY a.kdsatker limit 1;\r\n      \r\n      `;\r\n    const cleanedQuery = query.replace(/\\n/g, \" \").replace(/\\s+/g, \" \").trim();\r\n\r\n    try {\r\n      const response = await axiosJWT.post(\r\n        process.env.NEXT_PUBLIC_GET_REFERENSI,\r\n        { query: Encrypt(cleanedQuery) }\r\n      );\r\n\r\n      if (response.status !== 200) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      if (response.data.result.length === 0) {\r\n        return null;\r\n      }\r\n      //   console.log(\"Response data:\", response.data);\r\n\r\n      // Map data pertama dari response ke format SatkerItem\r\n      const mappedData = mapDbToSatkerItem(response.data.result[0]);\r\n      return mappedData;\r\n    } catch (error) {\r\n      throw new Error(\"Gagal mengambil detail satker\");\r\n    }\r\n  };\r\n\r\n  return {\r\n    cariSatker,\r\n    ambilDetailSatker,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAiDQ;AA/CR;AACA;AACA;;AAJA;;;;AAMA,yEAAyE;AACzE,MAAM,oBAAoB,CAAC,SAAW,CAAC;QACrC,IAAI,OAAO,QAAQ;QACnB,MAAM,OAAO,QAAQ;QACrB,MAAM,OAAO,QAAQ;QACrB,UAAU,OAAO,QAAQ;QACzB,UAAU,OAAO,QAAQ;QACzB,OAAO,OAAO,KAAK;QACnB,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM;QACrB,SAAS,OAAO,OAAO;QACvB,SAAS,OAAO,OAAO;QACvB,UAAU,OAAO,QAAQ;QACzB,UAAU,OAAO,QAAQ;QACzB,QAAQ,OAAO,MAAM;QACrB,QAAQ,OAAO,MAAM;QACrB,OAAO,OAAO,KAAK;QACnB,KAAK,OAAO,GAAG;QACf,WAAW,OAAO,SAAS;QAC3B,OAAO,OAAO,KAAK;QACnB,MAAM,OAAO,IAAI;QACjB,WAAW,OAAO,SAAS;QAC3B,UAAU,OAAO,QAAQ;QACzB,OAAO,OAAO,MAAM;QACpB,QAAQ,AAAC,GAAgB,OAAd,OAAO,MAAM;IAC1B,CAAC;AAEM,MAAM,mBAAmB;;IAC9B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;IAExC,MAAM,aAAa,OAAO;QACxB,MAAM,QAAQ,AAAC,6GAEmD,OAAtC,eAAc,0BAAsC,OAAd,eAAc;QAGhF,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,IAAI;QAExE,IAAI;YACF,MAAM,WAAW,MAAM,SAAS,IAAI,yEAElC;gBAAE,OAAO,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE;YAAc;YAGjC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YACxD;YACA,wBAAwB;YAExB,8CAA8C;YAC9C,MAAM,aAAa,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAC3C,kBAAkB;YAGpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,QAAQ,AAAC,wqBAOuC,OAAT,UAAS;QAGtD,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,IAAI;QAExE,IAAI;YACF,MAAM,WAAW,MAAM,SAAS,IAAI,yEAElC;gBAAE,OAAO,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE;YAAc;YAGjC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;YACxD;YAEA,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;gBACrC,OAAO;YACT;YACA,kDAAkD;YAElD,sDAAsD;YACtD,MAAM,aAAa,kBAAkB,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;YAC5D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO;QACL;QACA;IACF;AACF;GA3Ea", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/searchicon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nexport const SearchIcon = () => {\r\n  return (\r\n    <svg\r\n      aria-hidden=\"true\"\r\n      fill=\"none\"\r\n      focusable=\"false\"\r\n      height={18}\r\n      role=\"presentation\"\r\n      viewBox=\"0 0 24 24\"\r\n      width={18}\r\n    >\r\n      <path\r\n        className=\"stroke-default-400\"\r\n        d=\"M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z\"\r\n        stroke=\"currentColor\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n      <path\r\n        className=\"stroke-default-400\"\r\n        d=\"M22 22L20 20\"\r\n        stroke=\"currentColor\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,aAAa;IACxB,qBACE,6LAAC;QACC,eAAY;QACZ,MAAK;QACL,WAAU;QACV,QAAQ;QACR,MAAK;QACL,SAAQ;QACR,OAAO;;0BAEP,6LAAC;gBACC,WAAU;gBACV,GAAE;gBACF,QAAO;gBACP,eAAc;gBACd,gBAAe;;;;;;0BAEjB,6LAAC;gBACC,WAAU;gBACV,GAAE;gBACF,QAAO;gBACP,eAAc;gBACd,gBAAe;;;;;;;;;;;;AAIvB;KA3Ba", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/pencarian/HasilPencarianSatker.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { createPortal } from \"react-dom\";\r\nimport { <PERSON>, CardB<PERSON>, Spinner, Chip } from \"@heroui/react\";\r\n\r\nexport const HasilPencarianSatker = ({\r\n  hasil,\r\n  sedang<PERSON>emuat,\r\n  on<PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  kataPencarian,\r\n  inputRef,\r\n}) => {\r\n  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });\r\n\r\n  useEffect(() => {\r\n    if (inputRef?.current) {\r\n      const rect = inputRef.current.getBoundingClientRect();\r\n      setPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        left: rect.left + window.scrollX,\r\n        width: rect.width,\r\n      });\r\n    }\r\n  }, [inputRef, hasil]);\r\n  const highlightText = (text, searchTerm) => {\r\n    if (!searchTerm) return text;\r\n\r\n    const regex = new RegExp(`(${searchTerm})`, \"gi\");\r\n    const parts = text.split(regex);\r\n\r\n    return parts.map((part, index) =>\r\n      regex.test(part) ? (\r\n        <mark\r\n          key={index}\r\n          className=\"bg-yellow-200 dark:bg-yellow-800 px-1 rounded\"\r\n        >\r\n          {part}\r\n        </mark>\r\n      ) : (\r\n        part\r\n      )\r\n    );\r\n  };\r\n\r\n  if (sedangMemuat) {\r\n    const loadingContent = (\r\n      <Card\r\n        className=\"fixed z-[9999] mt-1 shadow-lg\"\r\n        style={{\r\n          top: `${position.top}px`,\r\n          left: `${position.left}px`,\r\n          width: `${position.width}px`,\r\n          zIndex: 9999,\r\n        }}\r\n      >\r\n        <CardBody className=\"p-4\">\r\n          <div className=\"flex items-center justify-center gap-2\">\r\n            <Spinner size=\"sm\" />\r\n            <span className=\"text-sm text-gray-600\">Mencari satker...</span>\r\n          </div>\r\n        </CardBody>\r\n      </Card>\r\n    );\r\n    return typeof window !== \"undefined\"\r\n      ? createPortal(loadingContent, document.body)\r\n      : null;\r\n  }\r\n\r\n  if (hasil.length === 0 && kataPencarian.length >= 3) {\r\n    const noResultsContent = (\r\n      <Card\r\n        className=\"fixed z-[9999] mt-1 shadow-lg\"\r\n        style={{\r\n          top: `${position.top}px`,\r\n          left: `${position.left}px`,\r\n          width: `${position.width}px`,\r\n          zIndex: 9999,\r\n        }}\r\n      >\r\n        <CardBody className=\"p-4\">\r\n          <div className=\"text-center text-gray-500\">\r\n            <p className=\"text-sm\">\r\n              Tidak ada Satker ditemukan, gunakan kata kunci yang berbeda\r\n            </p>\r\n            {/* <p className=\"text-xs mt-1\">Coba kata kunci yang berbeda</p> */}\r\n          </div>\r\n        </CardBody>\r\n      </Card>\r\n    );\r\n    return typeof window !== \"undefined\"\r\n      ? createPortal(noResultsContent, document.body)\r\n      : null;\r\n  }\r\n\r\n  if (hasil.length === 0) return null;\r\n\r\n  const searchResults = (\r\n    <Card\r\n      className=\"fixed z-[9999] mt-1 shadow-lg max-h-80 overflow-y-auto\"\r\n      style={{\r\n        top: `${position.top}px`,\r\n        left: `${position.left}px`,\r\n        width: `${position.width}px`,\r\n        zIndex: 9999,\r\n      }}\r\n    >\r\n      <CardBody className=\"p-0\">\r\n        {hasil.map((satker, index) => (\r\n          <div\r\n            key={satker.id}\r\n            onClick={() => onPilihSatker(satker)}\r\n            className={`\r\n              p-4 cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-gray-800\r\n              ${\r\n                index !== hasil.length - 1\r\n                  ? \"border-b border-gray-200 dark:border-gray-700\"\r\n                  : \"\"\r\n              }\r\n            `}\r\n          >\r\n            <div className=\"flex items-start justify-between gap-3\">\r\n              <div className=\"flex-1 min-w-0\">\r\n                <div className=\"flex items-center gap-2 mb-1\">\r\n                  <Chip\r\n                    size=\"sm\"\r\n                    variant=\"flat\"\r\n                    color=\"primary\"\r\n                    className=\"text-xs font-mono\"\r\n                  >\r\n                    {satker.level}.{satker.alamat}{\" \"}\r\n                    {highlightText(satker.kode, kataPencarian)}\r\n                  </Chip>\r\n                </div>\r\n                <h4 className=\"font-medium text-sm leading-tight\">\r\n                  {highlightText(satker.nama, kataPencarian)}\r\n                </h4>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        {hasil.length >= 10 && (\r\n          <div className=\"p-3 text-center bg-gray-50 dark:bg-gray-900\">\r\n            <p className=\"text-xs text-gray-600 dark:text-gray-400\">\r\n              Menampilkan {hasil.length} hasil pertama. Perbanyak kata kunci\r\n              untuk hasil yang lebih spesifik.\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardBody>\r\n    </Card>\r\n  );\r\n\r\n  // Use portal to render outside of any stacking context\r\n  return typeof window !== \"undefined\"\r\n    ? createPortal(searchResults, document.body)\r\n    : null;\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMO,MAAM,uBAAuB;QAAC,EACnC,KAAK,EACL,YAAY,EACZ,aAAa,EACb,aAAa,EACb,QAAQ,EACT;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,MAAM;QAAG,OAAO;IAAE;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,qBAAA,+BAAA,SAAU,OAAO,EAAE;gBACrB,MAAM,OAAO,SAAS,OAAO,CAAC,qBAAqB;gBACnD,YAAY;oBACV,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;oBACjC,MAAM,KAAK,IAAI,GAAG,OAAO,OAAO;oBAChC,OAAO,KAAK,KAAK;gBACnB;YACF;QACF;yCAAG;QAAC;QAAU;KAAM;IACpB,MAAM,gBAAgB,CAAC,MAAM;QAC3B,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,QAAQ,IAAI,OAAO,AAAC,IAAc,OAAX,YAAW,MAAI;QAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,QACtB,MAAM,IAAI,CAAC,sBACT,6LAAC;gBAEC,WAAU;0BAET;eAHI;;;;2DAMP;IAGN;IAEA,IAAI,cAAc;QAChB,MAAM,+BACJ,6LAAC,yMAAA,CAAA,OAAI;YACH,WAAU;YACV,OAAO;gBACL,KAAK,AAAC,GAAe,OAAb,SAAS,GAAG,EAAC;gBACrB,MAAM,AAAC,GAAgB,OAAd,SAAS,IAAI,EAAC;gBACvB,OAAO,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;gBACzB,QAAQ;YACV;sBAEA,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gBAAC,WAAU;0BAClB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,kNAAA,CAAA,UAAO;4BAAC,MAAK;;;;;;sCACd,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;QAKhD,OAAO,qDACH,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,SAAS,IAAI,IAC1C;IACN;IAEA,IAAI,MAAM,MAAM,KAAK,KAAK,cAAc,MAAM,IAAI,GAAG;QACnD,MAAM,iCACJ,6LAAC,yMAAA,CAAA,OAAI;YACH,WAAU;YACV,OAAO;gBACL,KAAK,AAAC,GAAe,OAAb,SAAS,GAAG,EAAC;gBACrB,MAAM,AAAC,GAAgB,OAAd,SAAS,IAAI,EAAC;gBACvB,OAAO,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;gBACzB,QAAQ;YACV;sBAEA,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gBAAC,WAAU;0BAClB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;;;;QAQ/B,OAAO,qDACH,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,SAAS,IAAI,IAC5C;IACN;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,MAAM,8BACJ,6LAAC,yMAAA,CAAA,OAAI;QACH,WAAU;QACV,OAAO;YACL,KAAK,AAAC,GAAe,OAAb,SAAS,GAAG,EAAC;YACrB,MAAM,AAAC,GAAgB,OAAd,SAAS,IAAI,EAAC;YACvB,OAAO,AAAC,GAAiB,OAAf,SAAS,KAAK,EAAC;YACzB,QAAQ;QACV;kBAEA,cAAA,6LAAC,kNAAA,CAAA,WAAQ;YAAC,WAAU;;gBACjB,MAAM,GAAG,CAAC,CAAC,QAAQ,sBAClB,6LAAC;wBAEC,SAAS,IAAM,cAAc;wBAC7B,WAAW,AAAC,gHAMT,OAHC,UAAU,MAAM,MAAM,GAAG,IACrB,kDACA,IACL;kCAGH,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,OAAM;4CACN,WAAU;;gDAET,OAAO,KAAK;gDAAC;gDAAE,OAAO,MAAM;gDAAE;gDAC9B,cAAc,OAAO,IAAI,EAAE;;;;;;;;;;;;kDAGhC,6LAAC;wCAAG,WAAU;kDACX,cAAc,OAAO,IAAI,EAAE;;;;;;;;;;;;;;;;;uBAzB7B,OAAO,EAAE;;;;;gBAgCjB,MAAM,MAAM,IAAI,oBACf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;4BACzC,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;IAStC,uDAAuD;IACvD,OAAO,qDACH,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,eAAe,SAAS,IAAI,IACzC;AACN;GAxJa;KAAA", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/pencarian/PencarianSatker.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSatkerService } from \"@/lib/api/services/satkerService\";\r\nimport { Input } from \"@heroui/react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { SearchIcon } from \"../icons/searchicon\";\r\nimport { HasilPencarianSatker } from \"./HasilPencarianSatker\";\r\n\r\nexport const PencarianSatker = ({\r\n  placeholder = \"Ketik Kode atau Nama Satker...\",\r\n  className = \"w-full\",\r\n}) => {\r\n  const [nilaiPencarian, setNilaiPencarian] = useState(\"\");\r\n  const [hasilPencarian, setHasilPencarian] = useState([]);\r\n  const [sedangMemuat, setSedangMemuat] = useState(false);\r\n  const [tampilkanHasil, setTampilkanHasil] = useState(false);\r\n  const pencarianRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const router = useRouter();\r\n\r\n  const { cariSatker } = useSatkerService();\r\n\r\n  // Debounce untuk pencarian\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (nilaiPencarian.length >= 3) {\r\n        lakukan_pencarian();\r\n      } else {\r\n        setHasilPencarian([]);\r\n        setTampilkanHasil(false);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [nilaiPencarian]);\r\n\r\n  // Handle click outside untuk menutup hasil pencarian\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        pencarianRef.current &&\r\n        !pencarianRef.current.contains(event.target)\r\n      ) {\r\n        setTampilkanHasil(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const lakukan_pencarian = async () => {\r\n    if (nilaiPencarian.length < 3) return;\r\n\r\n    setSedangMemuat(true);\r\n    try {\r\n      const hasil = await cariSatker(nilaiPencarian);\r\n      setHasilPencarian(hasil);\r\n      setTampilkanHasil(true);\r\n    } catch (error) {\r\n      console.error(\"Error saat mencari satker:\", error);\r\n      setHasilPencarian([]);\r\n    } finally {\r\n      setSedangMemuat(false);\r\n    }\r\n  };\r\n\r\n  const tanganiPilihSatker = (satker) => {\r\n    setNilaiPencarian(satker.nama);\r\n    setTampilkanHasil(false);\r\n    // Navigate ke halaman detail satker menggunakan router.push\r\n    router.push(`/satker/${satker.id}`);\r\n  };\r\n\r\n  const tanganiInputFocus = () => {\r\n    if (hasilPencarian.length > 0) {\r\n      setTampilkanHasil(true);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div ref={pencarianRef} className={`relative ${className}`}>\r\n      <Input\r\n        ref={inputRef}\r\n        startContent={<SearchIcon />}\r\n        isClearable\r\n        value={nilaiPencarian}\r\n        onValueChange={setNilaiPencarian}\r\n        onFocus={tanganiInputFocus}\r\n        className=\"w-full\"\r\n        classNames={{\r\n          input: \"w-full\",\r\n          mainWrapper: \"w-full\",\r\n        }}\r\n        placeholder={placeholder}\r\n        // description={\r\n        //   nilaiPencarian.length > 0 && nilaiPencarian.length < 3\r\n        //     ? `Minimal 3 karakter (${nilaiPencarian.length}/3)`\r\n        //     : undefined\r\n        // }\r\n      />\r\n\r\n      {tampilkanHasil && (\r\n        <HasilPencarianSatker\r\n          hasil={hasilPencarian}\r\n          sedangMemuat={sedangMemuat}\r\n          onPilihSatker={tanganiPilihSatker}\r\n          kataPencarian={nilaiPencarian}\r\n          inputRef={inputRef}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,MAAM,kBAAkB;QAAC,EAC9B,cAAc,gCAAgC,EAC9C,YAAY,QAAQ,EACrB;;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD;IAEtC,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,YAAY;uDAAW;oBAC3B,IAAI,eAAe,MAAM,IAAI,GAAG;wBAC9B;oBACF,OAAO;wBACL,kBAAkB,EAAE;wBACpB,kBAAkB;oBACpB;gBACF;sDAAG;YAEH;6CAAO,IAAM,aAAa;;QAC5B;oCAAG;QAAC;KAAe;IAEnB,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IACE,aAAa,OAAO,IACpB,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC3C;wBACA,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI,eAAe,MAAM,GAAG,GAAG;QAE/B,gBAAgB;QAChB,IAAI;YACF,MAAM,QAAQ,MAAM,WAAW;YAC/B,kBAAkB;YAClB,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,kBAAkB,EAAE;QACtB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB,OAAO,IAAI;QAC7B,kBAAkB;QAClB,4DAA4D;QAC5D,OAAO,IAAI,CAAC,AAAC,WAAoB,OAAV,OAAO,EAAE;IAClC;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,kBAAkB;QACpB;IACF;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAW,AAAC,YAAqB,OAAV;;0BAC7C,6LAAC,4MAAA,CAAA,QAAK;gBACJ,KAAK;gBACL,4BAAc,6LAAC,sJAAA,CAAA,aAAU;;;;;gBACzB,WAAW;gBACX,OAAO;gBACP,eAAe;gBACf,SAAS;gBACT,WAAU;gBACV,YAAY;oBACV,OAAO;oBACP,aAAa;gBACf;gBACA,aAAa;;;;;;YAQd,gCACC,6LAAC,oKAAA,CAAA,uBAAoB;gBACnB,OAAO;gBACP,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,UAAU;;;;;;;;;;;;AAKpB;GA3Ga;;QAUI,qIAAA,CAAA,YAAS;QAED,iJAAA,CAAA,mBAAgB;;;KAZ5B", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/layout-context.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext } from \"react\";\r\n\r\ninterface SidebarContext {\r\n  collapsed: boolean;\r\n  setCollapsed: () => void;\r\n}\r\n\r\nexport const SidebarContext = createContext<SidebarContext>({\r\n  collapsed: false,\r\n  setCollapsed: () => {},\r\n});\r\n\r\nexport const useSidebarContext = () => {\r\n  return useContext(SidebarContext);\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AASO,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAkB;IAC1D,WAAW;IACX,cAAc,KAAO;AACvB;AAEO,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AACpB;GAFa", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/header/navbar.styles.ts"], "sourcesContent": ["import { tv } from \"@heroui/react\";\r\n\r\n// NEEDS TO BE REFACTORED\r\n\r\nexport const StyledBurgerButton = tv({\r\n  base: \"flex flex-col justify-around w-6 h-6 bg-transparent border-none cursor-pointer padding-0 z-[202] focus:outline-none [&_div]:w-6 [&_div]:h-px [&_div]:bg-default-900 [&_div]:rounded-xl  [&_div]:transition-all  [&_div]:relative  [&_div]:origin-[1px] \",\r\n\r\n  variants: {\r\n    open: {\r\n      true: \"[&\",\r\n    },\r\n  },\r\n  //   \"\",\r\n  //   \"& div\": {\r\n\r\n  //     \"&:first-child\": {\r\n  //       transform: \"translateY(-4px) rotate(0deg)\",\r\n  //       height: \"1px\",\r\n  //       marginTop: \"10px\",\r\n  //     },\r\n  //     \"&:nth-child(2)\": {\r\n  //       transform: \"translateY(4px) rotate(0deg)\",\r\n  //       height: \"1px\",\r\n  //       marginBottom: \"10px\",\r\n  //     },\r\n  //   },\r\n  //   variants: {\r\n  //     open: {\r\n  //       true: {\r\n  //         \"& div\": {\r\n  //           \"&:first-child\": {\r\n  //             marginTop: \"0px\",\r\n  //             transform: \"translateY(1px) rotate(45deg)\",\r\n  //           },\r\n  //           \"&:nth-child(2)\": {\r\n  //             marginBottom: \"0px\",\r\n  //             transform: \"translateY(4px) rotate(-45deg)\",\r\n  //           },\r\n  //         },\r\n  //       },\r\n  //     },\r\n  //   },\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM,qBAAqB,CAAA,GAAA,kKAAA,CAAA,KAAE,AAAD,EAAE;IACnC,MAAM;IAEN,UAAU;QACR,MAAM;YACJ,MAAM;QACR;IACF;AA+BF", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/header/burguer-button.tsx"], "sourcesContent": ["import { Menu } from \"lucide-react\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\nimport { StyledBurgerButton } from \"./navbar.styles\";\r\n\r\nexport const BurguerButton = () => {\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  return (\r\n    <div className={StyledBurgerButton()} onClick={setCollapsed}>\r\n      <Menu size={24} />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAEO,MAAM,gBAAgB;;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD;QAAK,SAAS;kBAC7C,cAAA,6LAAC,qMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;;;;;;AAGlB;GARa;;QACyB,mJAAA,CAAA,oBAAiB;;;KAD1C", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/header/darkmodeswitch.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useTheme as useNextTheme } from \"next-themes\";\r\nimport { Switch } from \"@heroui/react\";\r\n\r\nexport const DarkModeSwitch = () => {\r\n  const { setTheme, resolvedTheme, theme } = useNextTheme();\r\n  const [mounted, setMounted] = React.useState(false);\r\n\r\n  // Only run on client side to avoid hydration mismatch\r\n  React.useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Only initialize theme once on mount\r\n  React.useEffect(() => {\r\n    // Don't do anything during SSR\r\n    if (!mounted) return;\r\n\r\n    try {\r\n      // Check if there's a stored theme\r\n      const storedTheme = localStorage.getItem(\"sintesa-theme\");\r\n\r\n      // If there's a stored theme and it doesn't match the current theme\r\n      if (storedTheme && theme !== storedTheme) {\r\n        console.log(\"Setting theme from localStorage:\", storedTheme);\r\n        setTheme(storedTheme);\r\n      }\r\n    } catch (e) {\r\n      console.error(\"Error syncing theme:\", e);\r\n    }\r\n  }, [mounted, theme, setTheme]);\r\n\r\n  // Function to handle theme toggle\r\n  const handleThemeChange = (isDark: boolean) => {\r\n    const newTheme = isDark ? \"dark\" : \"light\";\r\n\r\n    // Apply theme through next-themes (this will automatically update localStorage)\r\n    setTheme(newTheme);\r\n\r\n    console.log(\"Theme changed to:\", newTheme);\r\n  };\r\n\r\n  const MoonIcon = () => {\r\n    return (\r\n      <svg\r\n        aria-hidden=\"true\"\r\n        focusable=\"false\"\r\n        height=\"1em\"\r\n        role=\"presentation\"\r\n        viewBox=\"0 0 24 24\"\r\n        width=\"1em\"\r\n      >\r\n        <path\r\n          d=\"M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z\"\r\n          fill=\"currentColor\"\r\n        />\r\n      </svg>\r\n    );\r\n  };\r\n  const SunIcon = () => {\r\n    return (\r\n      <svg\r\n        aria-hidden=\"true\"\r\n        focusable=\"false\"\r\n        height=\"1em\"\r\n        role=\"presentation\"\r\n        viewBox=\"0 0 24 24\"\r\n        width=\"1em\"\r\n      >\r\n        <g fill=\"currentColor\">\r\n          <path d=\"M19 12a7 7 0 11-7-7 7 7 0 017 7z\" />\r\n          <path d=\"M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z\" />\r\n        </g>\r\n      </svg>\r\n    );\r\n  };\r\n\r\n  // If not mounted yet (during SSR), show an invisible switch with same dimensions\r\n  if (!mounted) {\r\n    return (\r\n      <Switch\r\n        isSelected={false}\r\n        aria-label=\"Toggle dark mode\"\r\n        className=\"opacity-0\"\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Switch\r\n      isSelected={resolvedTheme === \"dark\"}\r\n      onValueChange={handleThemeChange}\r\n      aria-label=\"Toggle dark mode\"\r\n      color=\"warning\"\r\n      thumbIcon={({ isSelected, className }) =>\r\n        isSelected ? <MoonIcon /> : <SunIcon />\r\n      }\r\n    />\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAEO,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7C,sDAAsD;IACtD,6JAAA,CAAA,UAAK,CAAC,SAAS;oCAAC;YACd,WAAW;QACb;mCAAG,EAAE;IAEL,sCAAsC;IACtC,6JAAA,CAAA,UAAK,CAAC,SAAS;oCAAC;YACd,+BAA+B;YAC/B,IAAI,CAAC,SAAS;YAEd,IAAI;gBACF,kCAAkC;gBAClC,MAAM,cAAc,aAAa,OAAO,CAAC;gBAEzC,mEAAmE;gBACnE,IAAI,eAAe,UAAU,aAAa;oBACxC,QAAQ,GAAG,CAAC,oCAAoC;oBAChD,SAAS;gBACX;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;mCAAG;QAAC;QAAS;QAAO;KAAS;IAE7B,kCAAkC;IAClC,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,SAAS,SAAS;QAEnC,gFAAgF;QAChF,SAAS;QAET,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,WAAW;QACf,qBACE,6LAAC;YACC,eAAY;YACZ,WAAU;YACV,QAAO;YACP,MAAK;YACL,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;IAIb;IACA,MAAM,UAAU;QACd,qBACE,6LAAC;YACC,eAAY;YACZ,WAAU;YACV,QAAO;YACP,MAAK;YACL,SAAQ;YACR,OAAM;sBAEN,cAAA,6LAAC;gBAAE,MAAK;;kCACN,6LAAC;wBAAK,GAAE;;;;;;kCACR,6LAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;;IAIhB;IAEA,iFAAiF;IACjF,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,+MAAA,CAAA,SAAM;YACL,YAAY;YACZ,cAAW;YACX,WAAU;;;;;;IAGhB;IAEA,qBACE,6LAAC,+MAAA,CAAA,SAAM;QACL,YAAY,kBAAkB;QAC9B,eAAe;QACf,cAAW;QACX,OAAM;QACN,WAAW;gBAAC,EAAE,UAAU,EAAE,SAAS,EAAE;mBACnC,2BAAa,6LAAC;;;;uCAAc,6LAAC;;;;;;;;;;;AAIrC;GA/Fa;;QACgC,mJAAA,CAAA,WAAY;;;KAD5C", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/header/NotificationBellFixed.tsx"], "sourcesContent": ["import { useNotifications } from \"@/components/ui/feedback/NotificationContext\";\r\nimport {\r\n  Dropdown,\r\n  DropdownItem,\r\n  DropdownMenu,\r\n  DropdownTrigger,\r\n} from \"@heroui/react\";\r\nimport { BellRing } from \"lucide-react\";\r\n\r\nconst NotificationBell = () => {\r\n  const { notifications, unreadCount, markAsRead } = useNotifications();\r\n  return (\r\n    <div className=\"relative flex items-center\">\r\n      {\" \"}\r\n      <Dropdown\r\n        placement=\"bottom-end\"\r\n        className=\"min-w-[320px]\"\r\n        closeOnSelect={false}\r\n      >\r\n        <DropdownTrigger>\r\n          <button\r\n            className=\"relative p-2 rounded-xl hover:bg-zinc-100 dark:hover:bg-zinc-700 transition-all duration-150 cursor-pointer flex items-center justify-center focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 active:outline-none\"\r\n            aria-label=\"Toggle notifications\"\r\n            type=\"button\"\r\n            style={{ outline: \"none\", boxShadow: \"none\" }}\r\n          >\r\n            <BellRing\r\n              size={24}\r\n              className=\"text-default-400 dark:text-default-400\"\r\n            />\r\n            {unreadCount > 0 && (\r\n              <span className=\"absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-600 rounded-full\">\r\n                {unreadCount}\r\n              </span>\r\n            )}\r\n          </button>\r\n        </DropdownTrigger>\r\n        <DropdownMenu\r\n          aria-label=\"Notifications\"\r\n          className=\"w-full\"\r\n          onAction={(key) => {\r\n            if (key !== \"empty\") {\r\n              const notificationId = Number(key);\r\n              markAsRead(notificationId);\r\n            }\r\n          }}\r\n        >\r\n          {notifications.length === 0 ? (\r\n            <DropdownItem key=\"empty\" className=\"text-default-500\" isReadOnly>\r\n              No notifications\r\n            </DropdownItem>\r\n          ) : (\r\n            notifications.map((notification) => (\r\n              <DropdownItem\r\n                key={notification.id}\r\n                className={`text-default-900 transition-colors ${\r\n                  notification.read ? \"opacity-60\" : \"\"\r\n                }`}\r\n              >\r\n                {\" \"}\r\n                <div className=\"flex flex-col gap-1\">\r\n                  <div\r\n                    className={`text-sm font-medium ${\r\n                      notification.read\r\n                        ? \"text-gray-500 dark:text-gray-400\"\r\n                        : \"text-gray-900 dark:text-gray-100\"\r\n                    }`}\r\n                  >\r\n                    {notification.header}\r\n                  </div>\r\n                  <div\r\n                    className={`text-xs ${\r\n                      notification.read\r\n                        ? \"text-gray-400 dark:text-gray-500\"\r\n                        : \"text-gray-600 dark:text-gray-300\"\r\n                    }`}\r\n                  >\r\n                    {notification.message}\r\n                  </div>\r\n                </div>\r\n              </DropdownItem>\r\n            ))\r\n          )}\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;;;;;;AAEA,MAAM,mBAAmB;;IACvB,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD;IAClE,qBACE,6LAAC;QAAI,WAAU;;YACZ;0BACD,6LAAC,qNAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,WAAU;gBACV,eAAe;;kCAEf,6LAAC,oOAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC;4BACC,WAAU;4BACV,cAAW;4BACX,MAAK;4BACL,OAAO;gCAAE,SAAS;gCAAQ,WAAW;4BAAO;;8CAE5C,6LAAC,iNAAA,CAAA,WAAQ;oCACP,MAAM;oCACN,WAAU;;;;;;gCAEX,cAAc,mBACb,6LAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKT,6LAAC,8NAAA,CAAA,eAAY;wBACX,cAAW;wBACX,WAAU;wBACV,UAAU,CAAC;4BACT,IAAI,QAAQ,SAAS;gCACnB,MAAM,iBAAiB,OAAO;gCAC9B,WAAW;4BACb;wBACF;kCAEC,cAAc,MAAM,KAAK,kBACxB,6LAAC,2NAAA,CAAA,eAAY;4BAAa,WAAU;4BAAmB,UAAU;sCAAC;2BAAhD;;;;uEAIlB,cAAc,GAAG,CAAC,CAAC,6BACjB,6LAAC,2NAAA,CAAA,eAAY;gCAEX,WAAW,AAAC,sCAEX,OADC,aAAa,IAAI,GAAG,eAAe;;oCAGpC;kDACD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,AAAC,uBAIX,OAHC,aAAa,IAAI,GACb,qCACA;0DAGL,aAAa,MAAM;;;;;;0DAEtB,6LAAC;gDACC,WAAW,AAAC,WAIX,OAHC,aAAa,IAAI,GACb,qCACA;0DAGL,aAAa,OAAO;;;;;;;;;;;;;+BAvBpB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;AAiCpC;GA9EM;;QAC+C,8JAAA,CAAA,mBAAgB;;;KAD/D;uCAgFS", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/logo/snext_dark.svg.js"], "sourcesContent": ["var _defs, _g;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nvar SvgSnextDark = function SvgSnextDark(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    id: \"snext_dark_svg__Layer_2\",\n    \"data-name\": \"Layer 2\",\n    viewBox: \"0 0 3399.57 600\"\n  }, props), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"style\", null, \".snext_dark_svg__cls-1{fill:#0f52ba}\"))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    id: \"snext_dark_svg__Layer_1-2\",\n    \"data-name\": \"Layer 1\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M525 150c-41.46 0-75-33.54-75-75S416.42 0 375 0H75C33.58 0 0 33.58 0 75v300c0 41.42 33.58 75 75 75s75 33.54 75 75 33.58 75 75 75h300c41.42 0 75-33.58 75-75V225c0-41.42-33.58-75-75-75m-75 37.5V375c0 41.42-33.58 75-75 75H112.5c20.71 0 37.5-16.79 37.5-37.5V225c0-41.42 33.58-75 75-75h262.5c-20.71 0-37.5 16.79-37.5 37.5M1031.72 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68L935.94 468c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08 26.22-22.17 63.94-33.25 113.14-33.25 24.86 0 47.71 3.65 68.54 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM1313.84 108.26c-14.88 0-27.69-5.37-38.43-16.12-10.74-10.74-16.12-23.55-16.12-38.43s5.37-27.54 16.12-38.02c10.74-10.46 23.55-15.7 38.43-15.7s27.69 5.24 38.43 15.7c10.74 10.47 16.12 23.14 16.12 38.02s-5.37 27.69-16.12 38.43c-10.74 10.74-23.55 16.12-38.43 16.12m-46.28 486.66V191.86a5.08 5.08 0 0 1 5.08-5.08h81.57a5.08 5.08 0 0 1 5.08 5.08v403.06a5.08 5.08 0 0 1-5.08 5.08h-81.57a5.08 5.08 0 0 1-5.08-5.08M1462.03 594.9V190.07c0-2.82 2.29-5.1 5.1-5.1h77.3c2.68 0 4.9 2.07 5.09 4.75l2.77 39.27c.33 4.74 6.39 6.48 9.21 2.65 11.96-16.25 26.53-29.18 43.72-38.78 21.3-11.89 45.78-17.85 73.46-17.85q49.8 0 85.08 19.92c23.51 13.28 41.5 33.9 53.96 61.84 12.45 27.95 18.68 64.33 18.68 109.16v228.98c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1V387.52c0-33.2-3.73-58.79-11.21-76.78-7.47-17.98-17.99-30.57-31.54-37.77-13.57-7.19-29.47-10.79-47.73-10.79-31-.54-55.07 9.69-72.22 30.71-17.16 21.04-25.73 51.19-25.73 90.48v211.55c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1ZM2086.2 600c-42.82 0-75.48-10.7-97.97-32.11-22.5-21.41-33.74-52.16-33.74-92.28V266.79c0-2.76-2.24-5-5-5h-57.48c-2.76 0-5-2.24-5-5v-68.05c0-2.76 2.24-5 5-5h57.48c2.76 0 5-2.24 5-5V61.91c0-2.76 2.24-5 5-5h80.24c2.76 0 5 2.24 5 5v116.83c0 2.76 2.24 5 5 5h99.76c2.76 0 5 2.24 5 5v68.05c0 2.76-2.24 5-5 5h-99.76c-2.76 0-5 2.24-5 5v191.75c0 18.98 4.47 33.33 13.41 43.09s22.08 14.63 39.43 14.63c5.41 0 11.38-1.08 17.89-3.25 5.21-1.73 10.95-4.69 17.21-8.86 2.53-1.69 5.98-.78 7.3 1.97l28.9 59.92c1.09 2.26.33 4.97-1.76 6.36-12.89 8.56-25.76 15.19-38.64 19.89-14.1 5.14-28.19 7.72-42.28 7.72ZM2420.33 600c-39.48 0-74.49-9.06-105.03-27.17-30.55-18.11-54.62-43.11-72.19-75.02-17.58-31.9-26.36-68.66-26.36-110.31s8.92-78.4 26.76-110.31c17.84-31.9 42.3-56.9 73.4-75.02C2348 184.06 2383.54 175 2423.57 175c36.22 0 68.94 9.33 98.14 27.98s52.31 45.56 69.35 80.7c16.44 33.93 24.94 74.13 25.52 120.64.03 2.79-2.2 5.07-4.99 5.07h-295.26c-2.97 0-5.3 2.58-4.96 5.53 3.57 31.44 15.97 56.37 37.18 74.77 22.43 19.47 49.07 29.2 79.89 29.2 24.86 0 45.42-5.54 61.64-16.63 15.01-10.25 27.01-23.41 36.01-39.46a4.98 4.98 0 0 1 6.29-2.16l71.06 30.15c2.74 1.16 3.86 4.44 2.41 7.04-10.93 19.61-24.52 36.9-40.75 51.88-17.58 16.22-38.4 28.67-62.45 37.31-24.07 8.64-51.5 12.98-82.32 12.98m-98.11-260.35h190.7c2.99 0 5.33-2.61 4.96-5.57-2.18-16.98-7.66-31.21-16.45-42.69-9.73-12.7-21.63-22.3-35.69-28.79-14.07-6.49-28.93-9.73-44.61-9.73s-30.42 3.12-45.83 9.33c-15.41 6.22-28.53 15.82-39.34 28.79-9.73 11.67-15.96 25.96-18.68 42.87a4.99 4.99 0 0 0 4.93 5.79ZM2834.96 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68l69.95-31.01c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08C2751.28 186.08 2789 175 2838.2 175c24.86 0 47.71 3.65 68.53 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM3196.81 600c-48.66 0-86.38-10.81-113.14-32.44-26.76-21.62-40.15-52.17-40.15-91.65 0-42.18 14.19-74.34 42.58-96.52 28.39-22.16 67.99-33.25 118.82-33.25h97.37c3.01 0 5.35-2.66 4.95-5.64-3.65-26.98-11.81-47.81-24.45-62.49-13.52-15.68-33.53-23.52-60.02-23.52-19.47 0-36.5 4.06-51.1 12.17-13.62 7.57-25.37 19.14-35.22 34.71a4.97 4.97 0 0 1-5.92 2.01l-70.4-25.86c-2.82-1.04-4.1-4.32-2.71-6.99 8.31-15.94 18.96-30.88 31.93-44.82 14.32-15.41 32.44-27.7 54.34-36.9 21.9-9.19 48.26-13.79 79.08-13.79 39.46 0 72.45 7.71 98.95 23.12 26.49 15.41 46.09 37.45 58.8 66.1 12.7 28.67 19.06 63.26 19.06 103.82l-2.38 217.3a4.985 4.985 0 0 1-4.99 4.93h-73.86c-2.66 0-4.85-2.09-4.98-4.74l-1.45-29.5c-.23-4.66-6.15-6.47-8.96-2.75-9.8 12.99-22.07 23.43-36.8 31.31-19.2 10.27-42.31 15.41-69.35 15.41Zm11.35-76.24q30 0 53.13-13.38c23.13-13.38 27.3-20.95 35.69-36.09 8.38-15.13 12.57-31.9 12.57-50.29v-3.12c0-2.75-2.23-4.99-4.99-4.99h-70.44c-36.23 0-61.64 5.01-76.24 15-14.6 10.01-21.9 24.21-21.9 42.58 0 15.69 6.35 27.98 19.06 36.9 12.7 8.92 30.42 13.38 53.12 13.38Z\",\n    className: \"snext_dark_svg__cls-1\"\n  }))));\n};\nexport default SvgSnextDark;"], "names": [], "mappings": ";;;AAEA;AAFA,IAAI,OAAO;AACX,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;AAEnR,IAAI,eAAe,SAAS,aAAa,KAAK;IAC5C,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,SAAS;QACtD,OAAO;QACP,IAAI;QACJ,aAAa;QACb,SAAS;IACX,GAAG,QAAQ,SAAS,CAAC,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,MAAM,wCAAwC,GAAG,MAAM,CAAC,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,KAAK;QACvN,IAAI;QACJ,aAAa;IACf,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC1C,GAAG;QACH,WAAW;IACb,GAAG;AACL;KAbI;uCAcW", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/logo/snext_light.svg.js"], "sourcesContent": ["var _defs, _g;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nvar SvgSnextLight = function SvgSnextLight(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    id: \"snext_light_svg__Layer_2\",\n    \"data-name\": \"Layer 2\",\n    viewBox: \"0 0 3399.57 600\"\n  }, props), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"style\", null, \".snext_light_svg__cls-1{fill:#eaa221}\"))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    id: \"snext_light_svg__Layer_1-2\",\n    \"data-name\": \"Layer 1\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M525 150c-41.46 0-75-33.54-75-75S416.42 0 375 0H75C33.58 0 0 33.58 0 75v300c0 41.42 33.58 75 75 75s75 33.54 75 75 33.58 75 75 75h300c41.42 0 75-33.58 75-75V225c0-41.42-33.58-75-75-75m-75 37.5V375c0 41.42-33.58 75-75 75H112.5c20.71 0 37.5-16.79 37.5-37.5V225c0-41.42 33.58-75 75-75h262.5c-20.71 0-37.5 16.79-37.5 37.5M1031.72 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68L935.94 468c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08 26.22-22.17 63.94-33.25 113.14-33.25 24.86 0 47.71 3.65 68.54 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM1313.84 108.26c-14.88 0-27.69-5.37-38.43-16.12-10.74-10.74-16.12-23.55-16.12-38.43s5.37-27.54 16.12-38.02c10.74-10.46 23.55-15.7 38.43-15.7s27.69 5.24 38.43 15.7c10.74 10.47 16.12 23.14 16.12 38.02s-5.37 27.69-16.12 38.43c-10.74 10.74-23.55 16.12-38.43 16.12m-46.28 486.66V191.86a5.08 5.08 0 0 1 5.08-5.08h81.57a5.08 5.08 0 0 1 5.08 5.08v403.06a5.08 5.08 0 0 1-5.08 5.08h-81.57a5.08 5.08 0 0 1-5.08-5.08M1462.03 594.9V190.07c0-2.82 2.29-5.1 5.1-5.1h77.3c2.68 0 4.9 2.07 5.09 4.75l2.77 39.27c.33 4.74 6.39 6.48 9.21 2.65 11.96-16.25 26.53-29.18 43.72-38.78 21.3-11.89 45.78-17.85 73.46-17.85q49.8 0 85.08 19.92c23.51 13.28 41.5 33.9 53.96 61.84 12.45 27.95 18.68 64.33 18.68 109.16v228.98c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1V387.52c0-33.2-3.73-58.79-11.21-76.78-7.47-17.98-17.99-30.57-31.54-37.77-13.57-7.19-29.47-10.79-47.73-10.79-31-.54-55.07 9.69-72.22 30.71-17.16 21.04-25.73 51.19-25.73 90.48v211.55c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1ZM2086.2 600c-42.82 0-75.48-10.7-97.97-32.11-22.5-21.41-33.74-52.16-33.74-92.28V266.79c0-2.76-2.24-5-5-5h-57.48c-2.76 0-5-2.24-5-5v-68.05c0-2.76 2.24-5 5-5h57.48c2.76 0 5-2.24 5-5V61.91c0-2.76 2.24-5 5-5h80.24c2.76 0 5 2.24 5 5v116.83c0 2.76 2.24 5 5 5h99.76c2.76 0 5 2.24 5 5v68.05c0 2.76-2.24 5-5 5h-99.76c-2.76 0-5 2.24-5 5v191.75c0 18.98 4.47 33.33 13.41 43.09s22.08 14.63 39.43 14.63c5.41 0 11.38-1.08 17.89-3.25 5.21-1.73 10.95-4.69 17.21-8.86 2.53-1.69 5.98-.78 7.3 1.97l28.9 59.92c1.09 2.26.33 4.97-1.76 6.36-12.89 8.56-25.76 15.19-38.64 19.89-14.1 5.14-28.19 7.72-42.28 7.72ZM2420.33 600c-39.48 0-74.49-9.06-105.03-27.17-30.55-18.11-54.62-43.11-72.19-75.02-17.58-31.9-26.36-68.66-26.36-110.31s8.92-78.4 26.76-110.31c17.84-31.9 42.3-56.9 73.4-75.02C2348 184.06 2383.54 175 2423.57 175c36.22 0 68.94 9.33 98.14 27.98s52.31 45.56 69.35 80.7c16.44 33.93 24.94 74.13 25.52 120.64.03 2.79-2.2 5.07-4.99 5.07h-295.26c-2.97 0-5.3 2.58-4.96 5.53 3.57 31.44 15.97 56.37 37.18 74.77 22.43 19.47 49.07 29.2 79.89 29.2 24.86 0 45.42-5.54 61.64-16.63 15.01-10.25 27.01-23.41 36.01-39.46a4.98 4.98 0 0 1 6.29-2.16l71.06 30.15c2.74 1.16 3.86 4.44 2.41 7.04-10.93 19.61-24.52 36.9-40.75 51.88-17.58 16.22-38.4 28.67-62.45 37.31-24.07 8.64-51.5 12.98-82.32 12.98m-98.11-260.35h190.7c2.99 0 5.33-2.61 4.96-5.57-2.18-16.98-7.66-31.21-16.45-42.69-9.73-12.7-21.63-22.3-35.69-28.79-14.07-6.49-28.93-9.73-44.61-9.73s-30.42 3.12-45.83 9.33c-15.41 6.22-28.53 15.82-39.34 28.79-9.73 11.67-15.96 25.96-18.68 42.87a4.99 4.99 0 0 0 4.93 5.79ZM2834.96 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68l69.95-31.01c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08C2751.28 186.08 2789 175 2838.2 175c24.86 0 47.71 3.65 68.53 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM3196.81 600c-48.66 0-86.38-10.81-113.14-32.44-26.76-21.62-40.15-52.17-40.15-91.65 0-42.18 14.19-74.34 42.58-96.52 28.39-22.16 67.99-33.25 118.82-33.25h97.37c3.01 0 5.35-2.66 4.95-5.64-3.65-26.98-11.81-47.81-24.45-62.49-13.52-15.68-33.53-23.52-60.02-23.52-19.47 0-36.5 4.06-51.1 12.17-13.62 7.57-25.37 19.14-35.22 34.71a4.97 4.97 0 0 1-5.92 2.01l-70.4-25.86c-2.82-1.04-4.1-4.32-2.71-6.99 8.31-15.94 18.96-30.88 31.93-44.82 14.32-15.41 32.44-27.7 54.34-36.9 21.9-9.19 48.26-13.79 79.08-13.79 39.46 0 72.45 7.71 98.95 23.12 26.49 15.41 46.09 37.45 58.8 66.1 12.7 28.67 19.06 63.26 19.06 103.82l-2.38 217.3a4.985 4.985 0 0 1-4.99 4.93h-73.86c-2.66 0-4.85-2.09-4.98-4.74l-1.45-29.5c-.23-4.66-6.15-6.47-8.96-2.75-9.8 12.99-22.07 23.43-36.8 31.31-19.2 10.27-42.31 15.41-69.35 15.41Zm11.35-76.24q30 0 53.13-13.38c23.13-13.38 27.3-20.95 35.69-36.09 8.38-15.13 12.57-31.9 12.57-50.29v-3.12c0-2.75-2.23-4.99-4.99-4.99h-70.44c-36.23 0-61.64 5.01-76.24 15-14.6 10.01-21.9 24.21-21.9 42.58 0 15.69 6.35 27.98 19.06 36.9 12.7 8.92 30.42 13.38 53.12 13.38Z\",\n    className: \"snext_light_svg__cls-1\"\n  }))));\n};\nexport default SvgSnextLight;"], "names": [], "mappings": ";;;AAEA;AAFA,IAAI,OAAO;AACX,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;AAEnR,IAAI,gBAAgB,SAAS,cAAc,KAAK;IAC9C,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,SAAS;QACtD,OAAO;QACP,IAAI;QACJ,aAAa;QACb,SAAS;IACX,GAAG,QAAQ,SAAS,CAAC,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,MAAM,yCAAyC,GAAG,MAAM,CAAC,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,KAAK;QACxN,IAAI;QACJ,aAAa;IACf,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC1C,GAAG;QACH,WAAW;IACb,GAAG;AACL;KAbI;uCAcW", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/logo/snext_logoonly_dark.svg.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nvar SvgSnextLogoonlyDark = function SvgSnextLogoonlyDark(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"data-name\": \"Layer 2\",\n    viewBox: \"0 0 1600 1600\"\n  }, props), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M1400 400c-110.55 0-200-89.45-200-200S1110.46 0 1000 0H200C89.54 0 0 89.54 0 200v800c0 110.46 89.54 200 200 200s200 89.45 200 200 89.54 200 200 200h800c110.46 0 200-89.54 200-200V600c0-110.46-89.54-200-200-200m-200 100v500c0 110.46-89.54 200-200 200H300c55.23 0 100-44.77 100-100V600c0-110.46 89.54-200 200-200h700c-55.23 0-100 44.77-100 100\",\n    \"data-name\": \"Layer 1\",\n    style: {\n      fill: \"#0f52ba\"\n    }\n  }));\n};\nexport default SvgSnextLogoonlyDark;"], "names": [], "mappings": ";;;AACA;AADA,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;AAEnR,IAAI,uBAAuB,SAAS,qBAAqB,KAAK;IAC5D,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,SAAS;QACtD,OAAO;QACP,aAAa;QACb,SAAS;IACX,GAAG,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAClD,GAAG;QACH,aAAa;QACb,OAAO;YACL,MAAM;QACR;IACF;AACF;KAZI;uCAaW", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/logo/snext_logoonly_light.svg.js"], "sourcesContent": ["function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nvar SvgSnextLogoonlyLight = function SvgSnextLogoonlyLight(props) {\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    \"data-name\": \"Layer 2\",\n    viewBox: \"0 0 1600 1600\"\n  }, props), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M1400 400c-110.55 0-200-89.45-200-200S1110.46 0 1000 0H200C89.54 0 0 89.54 0 200v800c0 110.46 89.54 200 200 200s200 89.45 200 200 89.54 200 200 200h800c110.46 0 200-89.54 200-200V600c0-110.46-89.54-200-200-200m-200 100v500c0 110.46-89.54 200-200 200H300c55.23 0 100-44.77 100-100V600c0-110.46 89.54-200 200-200h700c-55.23 0-100 44.77-100 100\",\n    \"data-name\": \"Layer 1\",\n    style: {\n      fill: \"#eaa221\"\n    }\n  }));\n};\nexport default SvgSnextLogoonlyLight;"], "names": [], "mappings": ";;;AACA;AADA,SAAS;IAAa,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAAmK,SAAS,KAAK,CAAC,MAAM;AAAY;;AAEnR,IAAI,wBAAwB,SAAS,sBAAsB,KAAK;IAC9D,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,SAAS;QACtD,OAAO;QACP,aAAa;QACb,SAAS;IACX,GAAG,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAClD,GAAG;QACH,aAAa;QACb,OAAO;YACL,MAAM;QACR;IACF;AACF;KAZI;uCAaW", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/header/user-dropdownjsx.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useContext, useCallback } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport {\r\n  Avatar,\r\n  Dropdown,\r\n  DropdownItem,\r\n  DropdownMenu,\r\n  DropdownTrigger,\r\n  NavbarItem,\r\n} from \"@heroui/react\";\r\n\r\nimport { deleteAuthCookie } from \"@/actions/auth.action\";\r\nimport Link from \"next/link\";\r\nimport MyContext from \"@/stores/data/Context\";\r\n\r\nexport const UserDropdownjsx = () => {\r\n  const router = useRouter();\r\n  const context = useContext(MyContext);\r\n  const { name, logout } = context || { name: \"User\" };\r\n\r\n  const handleLogout = useCallback(async () => {\r\n    await logout();\r\n  }, []);\r\n\r\n  return (\r\n    <Dropdown>\r\n      <NavbarItem>\r\n        <DropdownTrigger>\r\n          <Avatar\r\n            as=\"button\"\r\n            color=\"secondary\"\r\n            size=\"md\"\r\n            src=\"https://i.pravatar.cc/150?u=a042581f4e29026704d\"\r\n          />\r\n        </DropdownTrigger>\r\n      </NavbarItem>\r\n      <DropdownMenu\r\n        disabledKeys={[\"username\"]}\r\n        aria-label=\"User menu actions\"\r\n        onAction={(actionKey) => console.log({ actionKey })}\r\n      >\r\n        <DropdownItem\r\n          key=\"username\"\r\n          className=\"flex flex-col justify-start w-full items-start\"\r\n        >\r\n          <p>Signed in as</p>\r\n          <p>{name}</p>\r\n        </DropdownItem>\r\n        <DropdownItem key=\"profil-user\">\r\n          <Link href=\"/profil-user\" className=\"dropdown-item\">\r\n            Profil User\r\n          </Link>\r\n        </DropdownItem>\r\n        <DropdownItem key=\"settings\">Settings</DropdownItem>\r\n\r\n        {/* <DropdownItem key=\"analytics\">Analytics</DropdownItem> */}\r\n        {/* <DropdownItem key=\"system\">System</DropdownItem> */}\r\n\r\n        {/* <DropdownItem key=\"help_and_feedback\">Help & Feedback</DropdownItem> */}\r\n        <DropdownItem key=\"user-manage\">\r\n          <Link href=\"/accounts\" className=\"dropdown-item\">\r\n            User Management\r\n          </Link>\r\n        </DropdownItem>\r\n        <DropdownItem\r\n          key=\"logout\"\r\n          color=\"danger\"\r\n          className=\"text-danger\"\r\n          onPress={handleLogout}\r\n        >\r\n          Log Out\r\n        </DropdownItem>\r\n      </DropdownMenu>\r\n    </Dropdown>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAfA;;;;;;;AAiBO,MAAM,kBAAkB;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,oIAAA,CAAA,UAAS;IACpC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,WAAW;QAAE,MAAM;IAAO;IAEnD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC/B,MAAM;QACR;oDAAG,EAAE;IAEL,qBACE,6LAAC,qNAAA,CAAA,WAAQ;;0BACP,6LAAC,wNAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,oOAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC,+MAAA,CAAA,SAAM;wBACL,IAAG;wBACH,OAAM;wBACN,MAAK;wBACL,KAAI;;;;;;;;;;;;;;;;0BAIV,6LAAC,8NAAA,CAAA,eAAY;gBACX,cAAc;oBAAC;iBAAW;gBAC1B,cAAW;gBACX,UAAU,CAAC,YAAc,QAAQ,GAAG,CAAC;wBAAE;oBAAU;;kCAEjD,6LAAC,2NAAA,CAAA,eAAY;wBAEX,WAAU;;0CAEV,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAG;;;;;;;uBAJA;;;;;kCAMN,6LAAC,2NAAA,CAAA,eAAY;kCACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAe,WAAU;sCAAgB;;;;;;uBADpC;;;;;kCAKlB,6LAAC,2NAAA,CAAA,eAAY;kCAAgB;uBAAX;;;;;kCAMlB,6LAAC,2NAAA,CAAA,eAAY;kCACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;sCAAgB;;;;;;uBADjC;;;;;kCAKlB,6LAAC,2NAAA,CAAA,eAAY;wBAEX,OAAM;wBACN,WAAU;wBACV,SAAS;kCACV;uBAJK;;;;;;;;;;;;;;;;;AAUd;GA5Da;;QACI,qIAAA,CAAA,YAAS;;;KADb", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/header/navbar.tsx"], "sourcesContent": ["import { Link, Navbar, NavbarContent } from \"@heroui/react\";\r\nimport React from \"react\";\r\nimport { GithubIcon } from \"../icons/navbar/github-icon\";\r\nimport { PencarianSatker } from \"../pencarian/PencarianSatker.jsx\";\r\nimport { BurguerButton } from \"./burguer-button\";\r\nimport { DarkModeSwitch } from \"./darkmodeswitch\";\r\n\r\nimport NotificationBell from \"@/components/layout/header/NotificationBellFixed\";\r\nimport SintesaLogoDark from \"../icons/logo/snext_dark.svg\";\r\nimport SintesaLogoLight from \"../icons/logo/snext_light.svg\";\r\nimport SintesaLogoOnlyDark from \"../icons/logo/snext_logoonly_dark.svg\";\r\nimport SintesaLogoOnlyLight from \"../icons/logo/snext_logoonly_light.svg\";\r\nimport { UserDropdownjsx } from \"./user-dropdownjsx\";\r\n\r\ninterface Props {\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport const NavbarWrapper = ({ children }: Props) => {\r\n  return (\r\n    <div className=\"w-full relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden\">\r\n      <Navbar\r\n        isBordered\r\n        className=\"w-full fixed top-0 z-50 bg-white dark:bg-black\"\r\n        classNames={{\r\n          wrapper: \"w-full max-w-full\",\r\n        }}\r\n      >\r\n        <NavbarContent className=\"xl:hidden\" justify=\"start\">\r\n          <span className=\"flex items-center mr-4\">\r\n            <SintesaLogoOnlyDark className=\"h-6 w-auto block dark:hidden\" />\r\n            <SintesaLogoOnlyLight className=\"h-6 w-auto hidden dark:block\" />\r\n          </span>\r\n          <BurguerButton />\r\n        </NavbarContent>\r\n        <NavbarContent className=\"xl:hidden\" justify=\"center\">\r\n          <div className=\"flex-1\">\r\n            <PencarianSatker />\r\n          </div>\r\n          <NotificationBell />\r\n          <DarkModeSwitch />\r\n          <UserDropdownjsx />\r\n        </NavbarContent>\r\n        <NavbarContent className=\"w-full max-xl:hidden\">\r\n          {/* <CompaniesDropdown /> */}\r\n          <div className=\"flex-shrink-0\"></div>\r\n          <span className=\"flex items-center mr-4\">\r\n            <SintesaLogoDark className=\"h-6 w-auto block dark:hidden\" />\r\n            <SintesaLogoLight className=\"h-6 w-auto hidden dark:block\" />\r\n          </span>\r\n          <PencarianSatker placeholder=\"Ketik Kode atau Nama Satker...\" />\r\n        </NavbarContent>\r\n        <NavbarContent\r\n          justify=\"end\"\r\n          className=\"w-fit data-[justify=end]:flex-grow-0 max-xl:hidden\"\r\n        >\r\n          {/* <div className=\"flex items-center gap-2\">\r\n            <FeedbackIcon />\r\n            <span>Feedback?</span>\r\n          </div> */}\r\n\r\n          <NotificationBell />\r\n\r\n          {/* <NotificationTester /> */}\r\n\r\n          <Link href=\"https://spanint.kemenkeu.go.id/\" target={\"_blank\"}>\r\n            <GithubIcon />\r\n          </Link>\r\n          <DarkModeSwitch />\r\n\r\n          <UserDropdownjsx />\r\n        </NavbarContent>\r\n      </Navbar>\r\n      <div className=\"pt-16\">{children}</div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAMO,MAAM,gBAAgB;QAAC,EAAE,QAAQ,EAAS;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+MAAA,CAAA,SAAM;gBACL,UAAU;gBACV,WAAU;gBACV,YAAY;oBACV,SAAS;gBACX;;kCAEA,6LAAC,8NAAA,CAAA,gBAAa;wBAAC,WAAU;wBAAY,SAAQ;;0CAC3C,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,6KAAA,CAAA,UAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC,8KAAA,CAAA,UAAoB;wCAAC,WAAU;;;;;;;;;;;;0CAElC,6LAAC,8JAAA,CAAA,gBAAa;;;;;;;;;;;kCAEhB,6LAAC,8NAAA,CAAA,gBAAa;wBAAC,WAAU;wBAAY,SAAQ;;0CAC3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,kBAAe;;;;;;;;;;0CAElB,6LAAC,kKAAA,CAAA,UAAgB;;;;;0CACjB,6LAAC,2JAAA,CAAA,iBAAc;;;;;0CACf,6LAAC,gKAAA,CAAA,kBAAe;;;;;;;;;;;kCAElB,6LAAC,8NAAA,CAAA,gBAAa;wBAAC,WAAU;;0CAEvB,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,oKAAA,CAAA,UAAe;wCAAC,WAAU;;;;;;kDAC3B,6LAAC,qKAAA,CAAA,UAAgB;wCAAC,WAAU;;;;;;;;;;;;0CAE9B,6LAAC,+JAAA,CAAA,kBAAe;gCAAC,aAAY;;;;;;;;;;;;kCAE/B,6LAAC,8NAAA,CAAA,gBAAa;wBACZ,SAAQ;wBACR,WAAU;;0CAOV,6LAAC,kKAAA,CAAA,UAAgB;;;;;0CAIjB,6LAAC,yMAAA,CAAA,OAAI;gCAAC,MAAK;gCAAkC,QAAQ;0CACnD,cAAA,6LAAC,oKAAA,CAAA,aAAU;;;;;;;;;;0CAEb,6LAAC,2JAAA,CAAA,iBAAc;;;;;0CAEf,6LAAC,gKAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;0BAGpB,6LAAC;gBAAI,WAAU;0BAAS;;;;;;;;;;;;AAG9B;KA1Da", "debugId": null}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/epa-hrzntl-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport {\r\n  Dropdown,\r\n  DropdownTrigger,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { Building2, BarChart3, FileText } from \"lucide-react\";\r\n\r\ninterface DropdownItemType {\r\n  label: string;\r\n  href: string;\r\n  icon: React.ReactNode;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | DropdownItemType[];\r\n  isActive?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport const EpaHorizontalDD = ({\r\n  icon,\r\n  title,\r\n  items,\r\n  isActive,\r\n  children,\r\n}: Props) => {\r\n  const router = useRouter();\r\n  const pathname = usePathname(); // Convert string items to DropdownItemType with default icons and routes\r\n  const dropdownItems: DropdownItemType[] = React.useMemo(() => {\r\n    if (items.length === 0) return [];\r\n\r\n    if (typeof items[0] === \"string\") {\r\n      return (items as string[]).map((item, index) => {\r\n        let icon: React.ReactNode;\r\n        let href: string; // Handle epa menu items\r\n        if (title.toLowerCase() === \"epa\") {\r\n          const itemSlug = item\r\n            .toLowerCase()\r\n            .replace(/\\s+/g, \"-\")\r\n            .replace(/[\\/]/g, \"-\");\r\n          href = `/epa/${itemSlug}`;\r\n\r\n          // Assign icons based on menu item names\r\n          switch (item.toLowerCase()) {\r\n            case \"summary\":\r\n              icon = <BarChart3 size={20} />;\r\n              href = `/epa/summary`;\r\n              break;\r\n            case \"analisa\":\r\n              icon = <FileText size={20} />;\r\n              href = `/epa/analisa`;\r\n              break;\r\n            default:\r\n              icon = <Building2 size={20} />;\r\n          }\r\n        } else {\r\n          // Default handling for other menu items\r\n          icon = <Building2 size={20} />;\r\n          href = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n        }\r\n\r\n        return {\r\n          label: item,\r\n          href,\r\n          icon,\r\n        };\r\n      });\r\n    }\r\n    return items as DropdownItemType[];\r\n  }, [items, title]);\r\n  const handleItemClick = (href: string) => {\r\n    router.push(href);\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (href: string): boolean => {\r\n    return pathname === href;\r\n  };\r\n  return (\r\n    <div className=\"relative\">\r\n      <Dropdown placement=\"bottom-start\" className=\"min-w-[240px]\">\r\n        <DropdownTrigger>\r\n          <div\r\n            data-testid=\"hover-dropdown-button\"\r\n            className={clsx(\r\n              isActive\r\n                ? \"bg-success-100 [&_svg]:stroke-success-500\"\r\n                : \"hover:bg-success-100\",\r\n              \"flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]\"\r\n            )}\r\n          >\r\n            {icon}\r\n            <span className=\"text-default-900\">{title}</span>\r\n            {children}\r\n          </div>\r\n        </DropdownTrigger>\r\n        <DropdownMenu\r\n          aria-label={`${title} menu`}\r\n          className=\"w-full font-sans py-2\"\r\n          onAction={(key) => {\r\n            const item = dropdownItems[Number(key)];\r\n            if (item) {\r\n              handleItemClick(item.href);\r\n            }\r\n          }}\r\n        >\r\n          {dropdownItems.map((item, index) => {\r\n            const itemActive = isItemActive(item.href);\r\n            return (\r\n              <DropdownItem\r\n                key={index}\r\n                className={clsx(\r\n                  itemActive\r\n                    ? \"bg-success-100 [&_*]:text-primary\"\r\n                    : \"data-[hover=true]:bg-default-100\",\r\n                  \"font-sans text-sm py-3 px-4 min-h-[44px] group\"\r\n                )}\r\n              >\r\n                <div className=\"flex items-center gap-3\">\r\n                  {\" \"}\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-default-900 [&_svg]:stroke-success\"\r\n                        : \"text-default-900 group-hover:text-success [&_svg]:group-hover:stroke-success\",\r\n                      \"flex-shrink-0 transition-colors\"\r\n                    )}\r\n                  >\r\n                    {React.cloneElement(\r\n                      item.icon as React.ReactElement,\r\n                      {\r\n                        strokeWidth: 2.5,\r\n                      } as any\r\n                    )}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"!text-success font-medium\"\r\n                        : \"text-default-900 group-hover:text-success font-medium\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {item.label}\r\n                  </span>\r\n                </div>\r\n              </DropdownItem>\r\n            );\r\n          })}\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAAA;AAAA;;;AAVA;;;;;;AA0BO,MAAM,kBAAkB;QAAC,EAC9B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACF;;IACN,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,KAAK,yEAAyE;IACzG,MAAM,gBAAoC,6JAAA,CAAA,UAAK,CAAC,OAAO;kDAAC;YACtD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;YAEjC,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,UAAU;gBAChC,OAAO,AAAC,MAAmB,GAAG;8DAAC,CAAC,MAAM;wBACpC,IAAI;wBACJ,IAAI,MAAc,wBAAwB;wBAC1C,IAAI,MAAM,WAAW,OAAO,OAAO;4BACjC,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;4BACpB,OAAO,AAAC,QAAgB,OAAT;4BAEf,wCAAwC;4BACxC,OAAQ,KAAK,WAAW;gCACtB,KAAK;oCACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB,OAAQ;oCACR;gCACF,KAAK;oCACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB,OAAQ;oCACR;gCACF;oCACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;4BAC5B;wBACF,OAAO;4BACL,wCAAwC;4BACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;4BACxB,OAAO,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;wBAChD;wBAEA,OAAO;4BACL,OAAO;4BACP;4BACA;wBACF;oBACF;;YACF;YACA,OAAO;QACT;iDAAG;QAAC;QAAO;KAAM;IACjB,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,OAAO,aAAa;IACtB;IACA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;YAAe,WAAU;;8BAC3C,6LAAC,oOAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC;wBACC,eAAY;wBACZ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,WACI,8CACA,wBACJ;;4BAGD;0CACD,6LAAC;gCAAK,WAAU;0CAAoB;;;;;;4BACnC;;;;;;;;;;;;8BAGL,6LAAC,8NAAA,CAAA,eAAY;oBACX,cAAY,AAAC,GAAQ,OAAN,OAAM;oBACrB,WAAU;oBACV,UAAU,CAAC;wBACT,MAAM,OAAO,aAAa,CAAC,OAAO,KAAK;wBACvC,IAAI,MAAM;4BACR,gBAAgB,KAAK,IAAI;wBAC3B;oBACF;8BAEC,cAAc,GAAG,CAAC,CAAC,MAAM;wBACxB,MAAM,aAAa,aAAa,KAAK,IAAI;wBACzC,qBACE,6LAAC,2NAAA,CAAA,eAAY;4BAEX,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,sCACA,oCACJ;sCAGF,cAAA,6LAAC;gCAAI,WAAU;;oCACZ;kDACD,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,4CACA,gFACJ;kDAGD,cAAA,6JAAA,CAAA,UAAK,CAAC,YAAY,CACjB,KAAK,IAAI,EACT;4CACE,aAAa;wCACf;;;;;;kDAGJ,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,8BACA,yDACJ;kDAGD,KAAK,KAAK;;;;;;;;;;;;2BAjCV;;;;;oBAsCX;;;;;;;;;;;;;;;;;AAKV;GAtIa;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KARjB", "debugId": null}}, {"offset": {"line": 1815, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/icons/sidebar/chevron-down-icon.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface Props extends React.SVGAttributes<SVGElement> {}\r\nexport const ChevronDownIcon = ({ ...props }: Props) => {\r\n    return (\r\n        <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 24 24\"\r\n            {...props}>\r\n            <path\r\n                className=\"fill-default-400\"\r\n                d=\"m6.293 10.707 1.414-1.414L12 13.586l4.293-4.293 1.414 1.414L12 16.414z\"></path>\r\n        </svg>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,kBAAkB;QAAC,EAAE,GAAG,OAAc;IAC/C,qBACI,6LAAC;QACG,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACP,GAAG,KAAK;kBACT,cAAA,6LAAC;YACG,WAAU;YACV,GAAE;;;;;;;;;;;AAGlB;KAba", "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/epa-vrtkl-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\nimport { Accordion, AccordionItem } from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { BarChart3, Building2, FileText } from \"lucide-react\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { ChevronDownIcon } from \"../icons/sidebar/chevron-down-icon\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\n\r\ninterface MenuItem {\r\n  label: string;\r\n  href: string;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | MenuItem[];\r\n  isActive?: boolean;\r\n}\r\n\r\nexport const EpaVertikalDD = ({\r\n  icon,\r\n  items,\r\n  title,\r\n  isActive = false,\r\n}: Props) => {\r\n  const [open, setOpen] = useState(false);\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  // Function to get appropriate icon for each menu item\r\n  const getItemIcon = (itemLabel: string) => {\r\n    // Handle epa menu items\r\n    if (title.toLowerCase() === \"epa\") {\r\n      switch (itemLabel.toLowerCase()) {\r\n        case \"summary\":\r\n          return <BarChart3 size={16} />;\r\n        case \"analisa\":\r\n          return <FileText size={16} />;\r\n        default:\r\n          return <Building2 size={16} />;\r\n      }\r\n    } else {\r\n      // Default handling for other menu items\r\n      return <Building2 size={16} />;\r\n    }\r\n  };\r\n\r\n  // Helper function to handle navigation\r\n  const handleItemClick = (item: string | MenuItem) => {\r\n    if (typeof item === \"string\") {\r\n      // For string items, create a route based on the parent title and item name\r\n      let route: string;\r\n\r\n      if (title.toLowerCase() === \"epa\") {\r\n        // Special handling for epa data items\r\n        switch (item.toLowerCase()) {\r\n          case \"summary\":\r\n            route = \"/epa/summary\";\r\n            break;\r\n          case \"analisa\":\r\n            route = \"/epa/analisa\";\r\n            break;\r\n          default:\r\n            const itemSlug = item\r\n              .toLowerCase()\r\n              .replace(/\\s+/g, \"-\")\r\n              .replace(/[\\/]/g, \"-\");\r\n            route = `/epa/${itemSlug}`;\r\n        }\r\n      } else {\r\n        // Default route pattern for other items\r\n        route = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n      }\r\n\r\n      router.push(route);\r\n    } else {\r\n      // For MenuItem objects, use the href property\r\n      router.push(item.href);\r\n    } // Close the sidebar after navigation (for screens below XL)\r\n    if (window.innerWidth < 1280) {\r\n      setCollapsed();\r\n    }\r\n  };\r\n\r\n  // Helper function to get item label\r\n  const getItemLabel = (item: string | MenuItem): string => {\r\n    return typeof item === \"string\" ? item : item.label;\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (item: string | MenuItem): boolean => {\r\n    if (typeof item === \"string\") {\r\n      if (title.toLowerCase() === \"epa\") {\r\n        switch (item.toLowerCase()) {\r\n          case \"summary\":\r\n            return pathname === \"/epa/summary\";\r\n          case \"analisa\":\r\n            return pathname === \"/epa/analisa\";\r\n          default:\r\n            const itemSlug = item\r\n              .toLowerCase()\r\n              .replace(/\\s+/g, \"-\")\r\n              .replace(/[\\/]/g, \"-\");\r\n            return pathname === `/epa/${itemSlug}`;\r\n        }\r\n      }\r\n      return pathname === `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n    } else {\r\n      return pathname === item.href;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex gap-4 h-full items-center cursor-pointer\">\r\n      <Accordion className=\"px-0 w-full\">\r\n        <AccordionItem\r\n          indicator={<ChevronDownIcon />}\r\n          classNames={{\r\n            indicator: \"data-[open=true]:-rotate-180\",\r\n            trigger: clsx(\r\n              isActive\r\n                ? \"bg-primary-100 [&_svg]:stroke-primary-500\"\r\n                : \"hover:bg-default-100\",\r\n              \"py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5\"\r\n            ),\r\n            title:\r\n              \"px-0 flex text-base gap-2 h-full items-center cursor-pointer\",\r\n            content: \"px-0 pb-2\",\r\n          }}\r\n          aria-label=\"Accordion 1\"\r\n          title={\r\n            <div className=\"flex flex-row gap-2\">\r\n              <span>{icon}</span>\r\n              <span>{title}</span>\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"space-y-1\">\r\n            {items.map((item, index) => {\r\n              const itemLabel = getItemLabel(item);\r\n              const itemActive = isItemActive(item);\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  onClick={() => handleItemClick(item)}\r\n                  className={clsx(\r\n                    itemActive\r\n                      ? \"bg-primary-100 text-primary [&_svg]:text-primary [&_svg]:stroke-primary\"\r\n                      : \"hover:text-primary hover:bg-default-100\",\r\n                    \"w-full flex py-2 pl-8 pr-2 rounded-lg transition-colors cursor-pointer min-h-[36px] items-center gap-2 group\"\r\n                  )}\r\n                >\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-primary\"\r\n                        : \"text-default-400 group-hover:text-primary\",\r\n                      \"transition-colors\"\r\n                    )}\r\n                  >\r\n                    {getItemIcon(itemLabel)}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive ? \"text-primary\" : \"group-hover:text-primary\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {itemLabel}\r\n                  </span>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </AccordionItem>\r\n      </Accordion>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAqBO,MAAM,gBAAgB;QAAC,EAC5B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACV;;IACN,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,sDAAsD;IACtD,MAAM,cAAc,CAAC;QACnB,wBAAwB;QACxB,IAAI,MAAM,WAAW,OAAO,OAAO;YACjC,OAAQ,UAAU,WAAW;gBAC3B,KAAK;oBACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B,KAAK;oBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB;oBACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;YAC5B;QACF,OAAO;YACL,wCAAwC;YACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;QAC1B;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,UAAU;YAC5B,2EAA2E;YAC3E,IAAI;YAEJ,IAAI,MAAM,WAAW,OAAO,OAAO;gBACjC,sCAAsC;gBACtC,OAAQ,KAAK,WAAW;oBACtB,KAAK;wBACH,QAAQ;wBACR;oBACF,KAAK;wBACH,QAAQ;wBACR;oBACF;wBACE,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;wBACpB,QAAQ,AAAC,QAAgB,OAAT;gBACpB;YACF,OAAO;gBACL,wCAAwC;gBACxC,QAAQ,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;YACjD;YAEA,OAAO,IAAI,CAAC;QACd,OAAO;YACL,8CAA8C;YAC9C,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB,EAAE,4DAA4D;QAC9D,IAAI,OAAO,UAAU,GAAG,MAAM;YAC5B;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK,KAAK;IACrD;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS,UAAU;YAC5B,IAAI,MAAM,WAAW,OAAO,OAAO;gBACjC,OAAQ,KAAK,WAAW;oBACtB,KAAK;wBACH,OAAO,aAAa;oBACtB,KAAK;wBACH,OAAO,aAAa;oBACtB;wBACE,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;wBACpB,OAAO,aAAa,AAAC,QAAgB,OAAT;gBAChC;YACF;YACA,OAAO,aAAa,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;QAC7D,OAAO;YACL,OAAO,aAAa,KAAK,IAAI;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,wNAAA,CAAA,YAAS;YAAC,WAAU;sBACnB,cAAA,6LAAC,sOAAA,CAAA,gBAAa;gBACZ,yBAAW,6LAAC,8KAAA,CAAA,kBAAe;;;;;gBAC3B,YAAY;oBACV,WAAW;oBACX,SAAS,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACV,WACI,8CACA,wBACJ;oBAEF,OACE;oBACF,SAAS;gBACX;gBACA,cAAW;gBACX,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAM;;;;;;sCACP,6LAAC;sCAAM;;;;;;;;;;;;0BAIX,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,YAAY,aAAa;wBAC/B,MAAM,aAAa,aAAa;wBAChC,qBACE,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,4EACA,2CACJ;;8CAGF,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,iBACA,6CACJ;8CAGD,YAAY;;;;;;8CAEf,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,iBAAiB,4BAC9B;8CAGD;;;;;;;2BAzBE;;;;;oBA6BX;;;;;;;;;;;;;;;;;;;;;AAMZ;GAhKa;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACQ,mJAAA,CAAA,oBAAiB;;;KAT1C", "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/inquiry-hrzntl-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport {\r\n  Dropdown,\r\n  DropdownTrigger,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { Building2, CreditCard, Banknote } from \"lucide-react\";\r\n\r\ninterface DropdownItemType {\r\n  label: string;\r\n  href: string;\r\n  icon: React.ReactNode;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | DropdownItemType[];\r\n  isActive?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport const InqHorizontalDD = ({\r\n  icon,\r\n  title,\r\n  items,\r\n  isActive: isActiveProp,\r\n  children,\r\n}: Props) => {\r\n  const router = useRouter();\r\n  const pathname = usePathname(); // Convert string items to DropdownItemType with default icons and routes\r\n  const dropdownItems: DropdownItemType[] = React.useMemo(() => {\r\n    if (items.length === 0) return [];\r\n\r\n    if (typeof items[0] === \"string\") {\r\n      return (items as string[]).map((item, index) => {\r\n        let icon: React.ReactNode;\r\n        let href: string;\r\n\r\n        // Handle inquiry-data menu items\r\n        if (title.toLowerCase() === \"inquiry data\") {\r\n          const itemSlug = item\r\n            .toLowerCase()\r\n            .replace(/\\s+/g, \"-\")\r\n            .replace(/[\\/]/g, \"-\");\r\n          href = `/inquiry-data/${itemSlug}`;\r\n\r\n          // Assign icons based on menu item names\r\n          switch (item.toLowerCase()) {\r\n            case \"belanja\":\r\n              icon = <CreditCard size={20} />;\r\n              break;\r\n\r\n            case \"tematik\":\r\n              icon = <Building2 size={20} />;\r\n              break;\r\n            case \"kontrak\":\r\n              icon = <Banknote size={20} />;\r\n              break;\r\n            case \"up/tup\":\r\n              icon = <Building2 size={20} />;\r\n              break;\r\n            case \"bansos\":\r\n              icon = <CreditCard size={20} />;\r\n              break;\r\n            case \"deviasi\":\r\n              icon = <Banknote size={20} />;\r\n              break;\r\n            case \"rkakl detail\":\r\n              icon = <Building2 size={20} />;\r\n              break;\r\n            default:\r\n              icon = <Building2 size={20} />;\r\n          }\r\n        } else {\r\n          // Default handling for other menu items\r\n          icon = <Building2 size={20} />;\r\n          href = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n        }\r\n\r\n        return {\r\n          label: item,\r\n          href,\r\n          icon,\r\n        };\r\n      });\r\n    }\r\n    return items as DropdownItemType[];\r\n  }, [items, title]);\r\n  const handleItemClick = (href: string) => {\r\n    router.push(href);\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (href: string): boolean => {\r\n    return pathname === href;\r\n  };\r\n  // Compute isActive: true if any dropdown item href matches current pathname\r\n  const computedIsActive = React.useMemo(() => {\r\n    return dropdownItems.some((item) => pathname === item.href);\r\n  }, [dropdownItems, pathname]);\r\n  const isActive =\r\n    typeof isActiveProp === \"boolean\" ? isActiveProp : computedIsActive;\r\n  return (\r\n    <div className=\"relative\">\r\n      <Dropdown placement=\"bottom-start\" className=\"min-w-[240px]\">\r\n        <DropdownTrigger>\r\n          <div\r\n            data-testid=\"hover-dropdown-button\"\r\n            className={clsx(\r\n              isActive\r\n                ? \"bg-secondary-100 [&_svg]:stroke-secondary-500\"\r\n                : \"hover:bg-secondary-100\",\r\n              \"flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]\"\r\n            )}\r\n          >\r\n            {icon}\r\n            <span className=\"text-default-900\">{title}</span>\r\n            {children}\r\n          </div>\r\n        </DropdownTrigger>\r\n        <DropdownMenu\r\n          aria-label={`${title} menu`}\r\n          className=\"w-full font-sans py-2\"\r\n          onAction={(key) => {\r\n            const item = dropdownItems[Number(key)];\r\n            if (item) {\r\n              handleItemClick(item.href);\r\n            }\r\n          }}\r\n        >\r\n          {dropdownItems.map((item, index) => {\r\n            const itemActive = isItemActive(item.href);\r\n            return (\r\n              <DropdownItem\r\n                key={index}\r\n                className={clsx(\r\n                  itemActive\r\n                    ? \"bg-secondary-100 [&_*]:text-secondary\"\r\n                    : \"data-[hover=true]:bg-default-100\",\r\n                  \"font-sans text-sm py-3 px-4 min-h-[44px] group\"\r\n                )}\r\n              >\r\n                <div className=\"flex items-center gap-3\">\r\n                  {\" \"}\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-default-900 [&_svg]:stroke-secondary\"\r\n                        : \"text-default-900 group-hover:text-secondary [&_svg]:group-hover:stroke-secondary\",\r\n                      \"flex-shrink-0 transition-colors\"\r\n                    )}\r\n                  >\r\n                    {React.cloneElement(\r\n                      item.icon as React.ReactElement,\r\n                      {\r\n                        strokeWidth: 2.5,\r\n                      } as any\r\n                    )}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-secondary font-medium\"\r\n                        : \"text-default-900 group-hover:text-secondary font-medium\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {item.label}\r\n                  </span>\r\n                </div>\r\n              </DropdownItem>\r\n            );\r\n          })}\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAAA;AAAA;;;AAVA;;;;;;AA0BO,MAAM,kBAAkB;QAAC,EAC9B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,UAAU,YAAY,EACtB,QAAQ,EACF;;IACN,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,KAAK,yEAAyE;IACzG,MAAM,gBAAoC,6JAAA,CAAA,UAAK,CAAC,OAAO;kDAAC;YACtD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;YAEjC,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,UAAU;gBAChC,OAAO,AAAC,MAAmB,GAAG;8DAAC,CAAC,MAAM;wBACpC,IAAI;wBACJ,IAAI;wBAEJ,iCAAiC;wBACjC,IAAI,MAAM,WAAW,OAAO,gBAAgB;4BAC1C,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;4BACpB,OAAO,AAAC,iBAAyB,OAAT;4BAExB,wCAAwC;4BACxC,OAAQ,KAAK,WAAW;gCACtB,KAAK;oCACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACzB;gCAEF,KAAK;oCACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB;gCACF,KAAK;oCACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB;gCACF,KAAK;oCACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB;gCACF,KAAK;oCACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACzB;gCACF,KAAK;oCACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB;gCACF,KAAK;oCACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB;gCACF;oCACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;4BAC5B;wBACF,OAAO;4BACL,wCAAwC;4BACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;4BACxB,OAAO,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;wBAChD;wBAEA,OAAO;4BACL,OAAO;4BACP;4BACA;wBACF;oBACF;;YACF;YACA,OAAO;QACT;iDAAG;QAAC;QAAO;KAAM;IACjB,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,OAAO,aAAa;IACtB;IACA,4EAA4E;IAC5E,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YACrC,OAAO,cAAc,IAAI;6DAAC,CAAC,OAAS,aAAa,KAAK,IAAI;;QAC5D;oDAAG;QAAC;QAAe;KAAS;IAC5B,MAAM,WACJ,OAAO,iBAAiB,YAAY,eAAe;IACrD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;YAAe,WAAU;;8BAC3C,6LAAC,oOAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC;wBACC,eAAY;wBACZ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,WACI,kDACA,0BACJ;;4BAGD;0CACD,6LAAC;gCAAK,WAAU;0CAAoB;;;;;;4BACnC;;;;;;;;;;;;8BAGL,6LAAC,8NAAA,CAAA,eAAY;oBACX,cAAY,AAAC,GAAQ,OAAN,OAAM;oBACrB,WAAU;oBACV,UAAU,CAAC;wBACT,MAAM,OAAO,aAAa,CAAC,OAAO,KAAK;wBACvC,IAAI,MAAM;4BACR,gBAAgB,KAAK,IAAI;wBAC3B;oBACF;8BAEC,cAAc,GAAG,CAAC,CAAC,MAAM;wBACxB,MAAM,aAAa,aAAa,KAAK,IAAI;wBACzC,qBACE,6LAAC,2NAAA,CAAA,eAAY;4BAEX,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,0CACA,oCACJ;sCAGF,cAAA,6LAAC;gCAAI,WAAU;;oCACZ;kDACD,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,8CACA,oFACJ;kDAGD,cAAA,6JAAA,CAAA,UAAK,CAAC,YAAY,CACjB,KAAK,IAAI,EACT;4CACE,aAAa;wCACf;;;;;;kDAGJ,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,+BACA,2DACJ;kDAGD,KAAK,KAAK;;;;;;;;;;;;2BAjCV;;;;;oBAsCX;;;;;;;;;;;;;;;;;AAKV;GA5Ja;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KARjB", "debugId": null}}, {"offset": {"line": 2363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/inquiry-vrtkl-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\nimport { Accordion, AccordionItem } from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { Banknote, Building2, CreditCard } from \"lucide-react\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { ChevronDownIcon } from \"../icons/sidebar/chevron-down-icon\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\n\r\ninterface MenuItem {\r\n  label: string;\r\n  href: string;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | MenuItem[];\r\n  isActive?: boolean;\r\n}\r\n\r\nexport const InqVertikalDD = ({\r\n  icon,\r\n  items,\r\n  title,\r\n  isActive = false,\r\n}: Props) => {\r\n  const [open, setOpen] = useState(false);\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  // Function to get appropriate icon for each menu item\r\n  const getItemIcon = (itemLabel: string) => {\r\n    // Handle inquiry-data menu items\r\n    if (title.toLowerCase() === \"inquiry data\") {\r\n      switch (itemLabel.toLowerCase()) {\r\n        case \"belanja\":\r\n          return <CreditCard size={16} />;\r\n        case \"tematik\":\r\n          return <Building2 size={16} />;\r\n        case \"kontrak\":\r\n          return <Banknote size={16} />;\r\n        case \"up/tup\":\r\n          return <Building2 size={16} />;\r\n        case \"bansos\":\r\n          return <CreditCard size={16} />;\r\n        case \"deviasi\":\r\n          return <Banknote size={16} />;\r\n        case \"rkakl detail\":\r\n          return <Building2 size={16} />;\r\n        default:\r\n          return <Building2 size={16} />;\r\n      }\r\n    } else {\r\n      // Default handling for other menu items\r\n      return <Building2 size={16} />;\r\n    }\r\n  };\r\n\r\n  // Helper function to handle navigation\r\n  const handleItemClick = (item: string | MenuItem) => {\r\n    if (typeof item === \"string\") {\r\n      // For string items, create a route based on the parent title and item name\r\n      let route: string;\r\n\r\n      if (title.toLowerCase() === \"inquiry data\") {\r\n        // Special handling for inquiry data items\r\n        const itemSlug = item\r\n          .toLowerCase()\r\n          .replace(/\\s+/g, \"-\")\r\n          .replace(/[\\/]/g, \"-\");\r\n        route = `/inquiry-data/${itemSlug}`;\r\n      } else {\r\n        // Default route pattern for other items\r\n        route = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n      }\r\n\r\n      router.push(route);\r\n    } else {\r\n      // For MenuItem objects, use the href property\r\n      router.push(item.href);\r\n    }\r\n\r\n    // Close the sidebar after navigation (for screens below XL)\r\n    if (window.innerWidth < 1280) {\r\n      setCollapsed();\r\n    }\r\n  };\r\n\r\n  // Helper function to get item label\r\n  const getItemLabel = (item: string | MenuItem): string => {\r\n    return typeof item === \"string\" ? item : item.label;\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (item: string | MenuItem): boolean => {\r\n    if (typeof item === \"string\") {\r\n      if (title.toLowerCase() === \"inquiry data\") {\r\n        const itemSlug = item\r\n          .toLowerCase()\r\n          .replace(/\\s+/g, \"-\")\r\n          .replace(/[\\/]/g, \"-\");\r\n        return pathname === `/inquiry-data/${itemSlug}`;\r\n      }\r\n      return pathname === `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n    } else {\r\n      return pathname === item.href;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex gap-4 h-full items-center cursor-pointer\">\r\n      <Accordion className=\"px-0 w-full\">\r\n        <AccordionItem\r\n          indicator={<ChevronDownIcon />}\r\n          classNames={{\r\n            indicator: \"data-[open=true]:-rotate-180\",\r\n            trigger: clsx(\r\n              isActive\r\n                ? \"bg-primary-100 [&_svg]:stroke-primary-500\"\r\n                : \"hover:bg-default-100\",\r\n              \"py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5\"\r\n            ),\r\n            title:\r\n              \"px-0 flex text-base gap-2 h-full items-center cursor-pointer\",\r\n            content: \"px-0 pb-2\",\r\n          }}\r\n          aria-label=\"Accordion 1\"\r\n          title={\r\n            <div className=\"flex flex-row gap-2\">\r\n              <span>{icon}</span>\r\n              <span>{title}</span>\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"space-y-1\">\r\n            {items.map((item, index) => {\r\n              const itemLabel = getItemLabel(item);\r\n              const itemActive = isItemActive(item);\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  onClick={() => handleItemClick(item)}\r\n                  className={clsx(\r\n                    itemActive\r\n                      ? \"bg-primary-100 text-primary [&_svg]:text-primary\"\r\n                      : \"hover:text-primary hover:bg-default-100\",\r\n                    \"w-full flex py-2 pl-8 pr-2 rounded-xl transition-colors cursor-pointer min-h-[36px] items-center gap-2 group\"\r\n                  )}\r\n                >\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-primary\"\r\n                        : \"text-default-400 group-hover:text-primary\",\r\n                      \"transition-colors\"\r\n                    )}\r\n                  >\r\n                    {getItemIcon(itemLabel)}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive ? \"text-primary\" : \"group-hover:text-primary\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {itemLabel}\r\n                  </span>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </AccordionItem>\r\n      </Accordion>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAqBO,MAAM,gBAAgB;QAAC,EAC5B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACV;;IACN,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,sDAAsD;IACtD,MAAM,cAAc,CAAC;QACnB,iCAAiC;QACjC,IAAI,MAAM,WAAW,OAAO,gBAAgB;YAC1C,OAAQ,UAAU,WAAW;gBAC3B,KAAK;oBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;wBAAC,MAAM;;;;;;gBAC3B,KAAK;oBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B,KAAK;oBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB,KAAK;oBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B,KAAK;oBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;wBAAC,MAAM;;;;;;gBAC3B,KAAK;oBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB,KAAK;oBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B;oBACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;YAC5B;QACF,OAAO;YACL,wCAAwC;YACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;QAC1B;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,UAAU;YAC5B,2EAA2E;YAC3E,IAAI;YAEJ,IAAI,MAAM,WAAW,OAAO,gBAAgB;gBAC1C,0CAA0C;gBAC1C,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;gBACpB,QAAQ,AAAC,iBAAyB,OAAT;YAC3B,OAAO;gBACL,wCAAwC;gBACxC,QAAQ,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;YACjD;YAEA,OAAO,IAAI,CAAC;QACd,OAAO;YACL,8CAA8C;YAC9C,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;QAEA,4DAA4D;QAC5D,IAAI,OAAO,UAAU,GAAG,MAAM;YAC5B;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK,KAAK;IACrD;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS,UAAU;YAC5B,IAAI,MAAM,WAAW,OAAO,gBAAgB;gBAC1C,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;gBACpB,OAAO,aAAa,AAAC,iBAAyB,OAAT;YACvC;YACA,OAAO,aAAa,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;QAC7D,OAAO;YACL,OAAO,aAAa,KAAK,IAAI;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,wNAAA,CAAA,YAAS;YAAC,WAAU;sBACnB,cAAA,6LAAC,sOAAA,CAAA,gBAAa;gBACZ,yBAAW,6LAAC,8KAAA,CAAA,kBAAe;;;;;gBAC3B,YAAY;oBACV,WAAW;oBACX,SAAS,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACV,WACI,8CACA,wBACJ;oBAEF,OACE;oBACF,SAAS;gBACX;gBACA,cAAW;gBACX,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAM;;;;;;sCACP,6LAAC;sCAAM;;;;;;;;;;;;0BAIX,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,YAAY,aAAa;wBAC/B,MAAM,aAAa,aAAa;wBAChC,qBACE,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,qDACA,2CACJ;;8CAGF,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,iBACA,6CACJ;8CAGD,YAAY;;;;;;8CAEf,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,iBAAiB,4BAC9B;8CAGD;;;;;;;2BAzBE;;;;;oBA6BX;;;;;;;;;;;;;;;;;;;;;AAMZ;GA5Ja;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACQ,mJAAA,CAAA,oBAAiB;;;KAT1C", "debugId": null}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/mbg-hrzntl-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport {\r\n  Dropdown,\r\n  DropdownTrigger,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { Building2, Bar<PERSON>hart3, FileText, Database } from \"lucide-react\";\r\n\r\ninterface DropdownItemType {\r\n  label: string;\r\n  href: string;\r\n  icon: React.ReactNode;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | DropdownItemType[];\r\n  isActive?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport const MbgHorizontalDD = ({\r\n  icon,\r\n  title,\r\n  items,\r\n  isActive,\r\n  children,\r\n}: Props) => {\r\n  const router = useRouter();\r\n  const pathname = usePathname(); // Convert string items to DropdownItemType with default icons and routes\r\n  const dropdownItems: DropdownItemType[] = React.useMemo(() => {\r\n    if (items.length === 0) return [];\r\n\r\n    if (typeof items[0] === \"string\") {\r\n      return (items as string[]).map((item, index) => {\r\n        let icon: React.ReactNode;\r\n        let href: string; // Handle mbg menu items\r\n        if (\r\n          title.toLowerCase() === \"mbg\" ||\r\n          title.toLowerCase() === \"makan bergizi\"\r\n        ) {\r\n          const itemSlug = item\r\n            .toLowerCase()\r\n            .replace(/\\s+/g, \"-\")\r\n            .replace(/[\\/]/g, \"-\");\r\n          href = `/mbg/${itemSlug}`;\r\n\r\n          // Assign icons based on menu item names\r\n          switch (item.toLowerCase()) {\r\n            case \"dashboard\":\r\n              icon = <BarChart3 size={20} />;\r\n              href = `/mbg/dashboard-mbg`;\r\n              break;\r\n            case \"kertas kerja\":\r\n              icon = <FileText size={20} />;\r\n              href = `/mbg/kertas-kerja`;\r\n              break;\r\n            case \"data\":\r\n              icon = <Database size={20} />;\r\n              href = `/mbg/data-update`;\r\n              break;\r\n            default:\r\n              icon = <Building2 size={20} />;\r\n          }\r\n        } else {\r\n          // Default handling for other menu items\r\n          icon = <Building2 size={20} />;\r\n          href = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n        }\r\n\r\n        return {\r\n          label: item,\r\n          href,\r\n          icon,\r\n        };\r\n      });\r\n    }\r\n    return items as DropdownItemType[];\r\n  }, [items, title]);\r\n  const handleItemClick = (href: string) => {\r\n    router.push(href);\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (href: string): boolean => {\r\n    return pathname === href;\r\n  };\r\n  return (\r\n    <div className=\"relative\">\r\n      <Dropdown placement=\"bottom-start\" className=\"min-w-[240px]\">\r\n        <DropdownTrigger>\r\n          <div\r\n            data-testid=\"hover-dropdown-button\"\r\n            className={clsx(\r\n              isActive\r\n                ? \"bg-amber-100 [&_svg]:stroke-amber-700\"\r\n                : \"hover:bg-amber-100\",\r\n              \"flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]\"\r\n            )}\r\n          >\r\n            {icon}\r\n            <span className=\"text-default-900 \">{title}</span>\r\n            {children}\r\n          </div>\r\n        </DropdownTrigger>\r\n        <DropdownMenu\r\n          aria-label={`${title} menu`}\r\n          className=\"w-full font-sans py-2\"\r\n          onAction={(key) => {\r\n            const item = dropdownItems[Number(key)];\r\n            if (item) {\r\n              handleItemClick(item.href);\r\n            }\r\n          }}\r\n        >\r\n          {dropdownItems.map((item, index) => {\r\n            const itemActive = isItemActive(item.href);\r\n            return (\r\n              <DropdownItem\r\n                key={index}\r\n                className={clsx(\r\n                  itemActive\r\n                    ? \"bg-amber-100 [&_*]:text-primary\"\r\n                    : \"data-[hover=true]:bg-default-100\",\r\n                  \"font-sans font-semibold text-sm py-3 px-4 min-h-[44px] group\"\r\n                )}\r\n              >\r\n                <div className=\"flex items-center gap-3\">\r\n                  {\" \"}\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-default-900 [&_svg]:stroke-amber-700 \"\r\n                        : \"text-default-900 group-hover:text-amber-700 [&_svg]:group-hover:stroke-amber-700\",\r\n                      \"flex-shrink-0 transition-colors\"\r\n                    )}\r\n                  >\r\n                    {React.cloneElement(\r\n                      item.icon as React.ReactElement,\r\n                      {\r\n                        strokeWidth: 2.5,\r\n                      } as any\r\n                    )}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"!text-amber-700 font-medium\"\r\n                        : \"text-default-900 group-hover:text-amber-700 font-medium\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {item.label}\r\n                  </span>\r\n                </div>\r\n              </DropdownItem>\r\n            );\r\n          })}\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;;;AAVA;;;;;;AA0BO,MAAM,kBAAkB;QAAC,EAC9B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACF;;IACN,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,KAAK,yEAAyE;IACzG,MAAM,gBAAoC,6JAAA,CAAA,UAAK,CAAC,OAAO;kDAAC;YACtD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;YAEjC,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,UAAU;gBAChC,OAAO,AAAC,MAAmB,GAAG;8DAAC,CAAC,MAAM;wBACpC,IAAI;wBACJ,IAAI,MAAc,wBAAwB;wBAC1C,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,iBACxB;4BACA,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;4BACpB,OAAO,AAAC,QAAgB,OAAT;4BAEf,wCAAwC;4BACxC,OAAQ,KAAK,WAAW;gCACtB,KAAK;oCACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB,OAAQ;oCACR;gCACF,KAAK;oCACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB,OAAQ;oCACR;gCACF,KAAK;oCACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB,OAAQ;oCACR;gCACF;oCACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;4BAC5B;wBACF,OAAO;4BACL,wCAAwC;4BACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;4BACxB,OAAO,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;wBAChD;wBAEA,OAAO;4BACL,OAAO;4BACP;4BACA;wBACF;oBACF;;YACF;YACA,OAAO;QACT;iDAAG;QAAC;QAAO;KAAM;IACjB,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,OAAO,aAAa;IACtB;IACA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;YAAe,WAAU;;8BAC3C,6LAAC,oOAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC;wBACC,eAAY;wBACZ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,WACI,0CACA,sBACJ;;4BAGD;0CACD,6LAAC;gCAAK,WAAU;0CAAqB;;;;;;4BACpC;;;;;;;;;;;;8BAGL,6LAAC,8NAAA,CAAA,eAAY;oBACX,cAAY,AAAC,GAAQ,OAAN,OAAM;oBACrB,WAAU;oBACV,UAAU,CAAC;wBACT,MAAM,OAAO,aAAa,CAAC,OAAO,KAAK;wBACvC,IAAI,MAAM;4BACR,gBAAgB,KAAK,IAAI;wBAC3B;oBACF;8BAEC,cAAc,GAAG,CAAC,CAAC,MAAM;wBACxB,MAAM,aAAa,aAAa,KAAK,IAAI;wBACzC,qBACE,6LAAC,2NAAA,CAAA,eAAY;4BAEX,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,oCACA,oCACJ;sCAGF,cAAA,6LAAC;gCAAI,WAAU;;oCACZ;kDACD,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,+CACA,oFACJ;kDAGD,cAAA,6JAAA,CAAA,UAAK,CAAC,YAAY,CACjB,KAAK,IAAI,EACT;4CACE,aAAa;wCACf;;;;;;kDAGJ,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,gCACA,2DACJ;kDAGD,KAAK,KAAK;;;;;;;;;;;;2BAjCV;;;;;oBAsCX;;;;;;;;;;;;;;;;;AAKV;GA7Ia;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KARjB", "debugId": null}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/mbg-vrtkl-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\nimport { Accordion, AccordionItem } from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { BarChart3, Building2, Database, FileText } from \"lucide-react\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { ChevronDownIcon } from \"../icons/sidebar/chevron-down-icon\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\n\r\ninterface MenuItem {\r\n  label: string;\r\n  href: string;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | MenuItem[];\r\n  isActive?: boolean;\r\n}\r\n\r\nexport const MbgVertikalDD = ({\r\n  icon,\r\n  items,\r\n  title,\r\n  isActive = false,\r\n}: Props) => {\r\n  const [open, setOpen] = useState(false);\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  // Function to get appropriate icon for each menu item\r\n  const getItemIcon = (itemLabel: string) => {\r\n    // Handle mbg menu items\r\n    if (\r\n      title.toLowerCase() === \"mbg\" ||\r\n      title.toLowerCase() === \"makan bergizi\"\r\n    ) {\r\n      switch (itemLabel.toLowerCase()) {\r\n        case \"dashboard\":\r\n        case \"dashboard mbg\":\r\n          return <BarChart3 size={16} />;\r\n        case \"kertas kerja\":\r\n        case \"kertas kerja mbg\":\r\n          return <FileText size={16} />;\r\n        case \"data\":\r\n        case \"data mbg\":\r\n          return <Database size={16} />;\r\n        default:\r\n          return <Building2 size={16} />;\r\n      }\r\n    } else {\r\n      // Default handling for other menu items\r\n      return <Building2 size={16} />;\r\n    }\r\n  };\r\n\r\n  // Helper function to handle navigation\r\n  const handleItemClick = (item: string | MenuItem) => {\r\n    if (typeof item === \"string\") {\r\n      // For string items, create a route based on the parent title and item name\r\n      let route: string;\r\n\r\n      if (\r\n        title.toLowerCase() === \"mbg\" ||\r\n        title.toLowerCase() === \"makan bergizi\"\r\n      ) {\r\n        // Special handling for mbg data items\r\n        switch (item.toLowerCase()) {\r\n          case \"dashboard\":\r\n          case \"dashboard mbg\":\r\n            route = \"/mbg/dashboard-mbg\";\r\n            break;\r\n          case \"kertas kerja\":\r\n          case \"kertas kerja mbg\":\r\n            route = \"/mbg/kertas-kerja\";\r\n            break;\r\n          case \"data\":\r\n          case \"data mbg\":\r\n            route = \"/mbg/data-update\";\r\n            break;\r\n          default:\r\n            const itemSlug = item\r\n              .toLowerCase()\r\n              .replace(/\\s+/g, \"-\")\r\n              .replace(/[\\/]/g, \"-\");\r\n            route = `/mbg/${itemSlug}`;\r\n        }\r\n      } else {\r\n        // Default route pattern for other items\r\n        route = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n      }\r\n\r\n      router.push(route);\r\n    } else {\r\n      // For MenuItem objects, use the href property\r\n      router.push(item.href);\r\n    }\r\n\r\n    // Close the sidebar after navigation (for screens below XL)\r\n    if (window.innerWidth < 1280) {\r\n      setCollapsed();\r\n    }\r\n  };\r\n\r\n  // Helper function to get item label\r\n  const getItemLabel = (item: string | MenuItem): string => {\r\n    return typeof item === \"string\" ? item : item.label;\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (item: string | MenuItem): boolean => {\r\n    if (typeof item === \"string\") {\r\n      if (\r\n        title.toLowerCase() === \"mbg\" ||\r\n        title.toLowerCase() === \"makan bergizi\"\r\n      ) {\r\n        switch (item.toLowerCase()) {\r\n          case \"dashboard\":\r\n          case \"dashboard mbg\":\r\n            return pathname === \"/mbg/dashboard-mbg\";\r\n          case \"kertas kerja\":\r\n          case \"kertas kerja mbg\":\r\n            return pathname === \"/mbg/kertas-kerja\";\r\n          case \"data\":\r\n          case \"data mbg\":\r\n            return pathname === \"/mbg/data-update\";\r\n          default:\r\n            const itemSlug = item\r\n              .toLowerCase()\r\n              .replace(/\\s+/g, \"-\")\r\n              .replace(/[\\/]/g, \"-\");\r\n            return pathname === `/mbg/${itemSlug}`;\r\n        }\r\n      }\r\n      return pathname === `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n    } else {\r\n      return pathname === item.href;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex gap-4 h-full items-center cursor-pointer\">\r\n      <Accordion className=\"px-0 w-full\">\r\n        <AccordionItem\r\n          indicator={<ChevronDownIcon />}\r\n          classNames={{\r\n            indicator: \"data-[open=true]:-rotate-180\",\r\n            trigger: clsx(\r\n              isActive\r\n                ? \"bg-primary-100 [&_svg]:stroke-primary-500\"\r\n                : \"hover:bg-default-100\",\r\n              \"py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5\"\r\n            ),\r\n            title:\r\n              \"px-0 flex text-base gap-2 h-full items-center cursor-pointer\",\r\n            content: \"px-0 pb-2\",\r\n          }}\r\n          aria-label=\"Accordion 1\"\r\n          title={\r\n            <div className=\"flex flex-row gap-2\">\r\n              <span>{icon}</span>\r\n              <span>{title}</span>\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"space-y-1\">\r\n            {items.map((item, index) => {\r\n              const itemLabel = getItemLabel(item);\r\n              const itemActive = isItemActive(item);\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  onClick={() => handleItemClick(item)}\r\n                  className={clsx(\r\n                    itemActive\r\n                      ? \"bg-primary-100 text-primary [&_svg]:text-primary [&_svg]:stroke-primary\"\r\n                      : \"hover:text-primary hover:bg-default-100\",\r\n                    \"w-full flex py-2 pl-8 pr-2 rounded-lg transition-colors cursor-pointer min-h-[36px] items-center gap-2 group\"\r\n                  )}\r\n                >\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-primary\"\r\n                        : \"text-default-400 group-hover:text-primary\",\r\n                      \"transition-colors\"\r\n                    )}\r\n                  >\r\n                    {getItemIcon(itemLabel)}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive ? \"text-primary\" : \"group-hover:text-primary\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {itemLabel}\r\n                  </span>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </AccordionItem>\r\n      </Accordion>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAqBO,MAAM,gBAAgB;QAAC,EAC5B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACV;;IACN,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,sDAAsD;IACtD,MAAM,cAAc,CAAC;QACnB,wBAAwB;QACxB,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,iBACxB;YACA,OAAQ,UAAU,WAAW;gBAC3B,KAAK;gBACL,KAAK;oBACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B,KAAK;gBACL,KAAK;oBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB,KAAK;gBACL,KAAK;oBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB;oBACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;YAC5B;QACF,OAAO;YACL,wCAAwC;YACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;QAC1B;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,UAAU;YAC5B,2EAA2E;YAC3E,IAAI;YAEJ,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,iBACxB;gBACA,sCAAsC;gBACtC,OAAQ,KAAK,WAAW;oBACtB,KAAK;oBACL,KAAK;wBACH,QAAQ;wBACR;oBACF,KAAK;oBACL,KAAK;wBACH,QAAQ;wBACR;oBACF,KAAK;oBACL,KAAK;wBACH,QAAQ;wBACR;oBACF;wBACE,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;wBACpB,QAAQ,AAAC,QAAgB,OAAT;gBACpB;YACF,OAAO;gBACL,wCAAwC;gBACxC,QAAQ,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;YACjD;YAEA,OAAO,IAAI,CAAC;QACd,OAAO;YACL,8CAA8C;YAC9C,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;QAEA,4DAA4D;QAC5D,IAAI,OAAO,UAAU,GAAG,MAAM;YAC5B;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK,KAAK;IACrD;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS,UAAU;YAC5B,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,iBACxB;gBACA,OAAQ,KAAK,WAAW;oBACtB,KAAK;oBACL,KAAK;wBACH,OAAO,aAAa;oBACtB,KAAK;oBACL,KAAK;wBACH,OAAO,aAAa;oBACtB,KAAK;oBACL,KAAK;wBACH,OAAO,aAAa;oBACtB;wBACE,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;wBACpB,OAAO,aAAa,AAAC,QAAgB,OAAT;gBAChC;YACF;YACA,OAAO,aAAa,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;QAC7D,OAAO;YACL,OAAO,aAAa,KAAK,IAAI;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,wNAAA,CAAA,YAAS;YAAC,WAAU;sBACnB,cAAA,6LAAC,sOAAA,CAAA,gBAAa;gBACZ,yBAAW,6LAAC,8KAAA,CAAA,kBAAe;;;;;gBAC3B,YAAY;oBACV,WAAW;oBACX,SAAS,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACV,WACI,8CACA,wBACJ;oBAEF,OACE;oBACF,SAAS;gBACX;gBACA,cAAW;gBACX,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAM;;;;;;sCACP,6LAAC;sCAAM;;;;;;;;;;;;0BAIX,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,YAAY,aAAa;wBAC/B,MAAM,aAAa,aAAa;wBAChC,qBACE,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,4EACA,2CACJ;;8CAGF,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,iBACA,6CACJ;8CAGD,YAAY;;;;;;8CAEf,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,iBAAiB,4BAC9B;8CAGD;;;;;;;2BAzBE;;;;;oBA6BX;;;;;;;;;;;;;;;;;;;;;AAMZ;GA3La;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACQ,mJAAA,CAAA,oBAAiB;;;KAT1C", "debugId": null}}, {"offset": {"line": 3114, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/sidebar-item.tsx"], "sourcesContent": ["import clsx from \"clsx\";\r\nimport NextLink from \"next/link\";\r\nimport React from \"react\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\n\r\ninterface Props {\r\n  title: string;\r\n  icon: React.ReactNode;\r\n  isActive?: boolean;\r\n  href?: string;\r\n  // New props for customization\r\n  color?: string;\r\n  bgColor?: string;\r\n  hoverColor?: string;\r\n  activeColor?: string;\r\n  activeBgColor?: string;\r\n  className?: string;\r\n}\r\n\r\nexport const SidebarItem = ({\r\n  icon,\r\n  title,\r\n  isActive,\r\n  href = \"\",\r\n  color = \"text-default-900\",\r\n  bgColor,\r\n  hoverColor = \"hover:bg-default-100\",\r\n  activeColor,\r\n  activeBgColor = \"bg-primary-100\",\r\n  className = \"\",\r\n}: Props) => {\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  const handleClick = () => {\r\n    if (window.innerWidth < 1280) {\r\n      setCollapsed();\r\n    }\r\n  };\r\n\r\n  // Build dynamic classes based on props\r\n  const getActiveClasses = () => {\r\n    if (isActive) {\r\n      const bgClass = bgColor || activeBgColor;\r\n      const iconClass = activeColor\r\n        ? `[&_svg]:stroke-${activeColor.replace(\"text-\", \"\")}`\r\n        : \"[&_svg]:stroke-primary-500\";\r\n      return `${bgClass} ${iconClass}`;\r\n    }\r\n    return hoverColor;\r\n  };\r\n\r\n  return (\r\n    <NextLink\r\n      href={href}\r\n      className=\"text-default-900 active:bg-none max-w-full\"\r\n    >\r\n      <div\r\n        className={clsx(\r\n          getActiveClasses(),\r\n          \"flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]\",\r\n          className\r\n        )}\r\n        onClick={handleClick}\r\n      >\r\n        {icon}\r\n        <span className={clsx(color)}>{title}</span>\r\n      </div>\r\n    </NextLink>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AAgBO,MAAM,cAAc;QAAC,EAC1B,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,OAAO,EAAE,EACT,QAAQ,kBAAkB,EAC1B,OAAO,EACP,aAAa,sBAAsB,EACnC,WAAW,EACX,gBAAgB,gBAAgB,EAChC,YAAY,EAAE,EACR;;IACN,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,MAAM,cAAc;QAClB,IAAI,OAAO,UAAU,GAAG,MAAM;YAC5B;QACF;IACF;IAEA,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,IAAI,UAAU;YACZ,MAAM,UAAU,WAAW;YAC3B,MAAM,YAAY,cACd,AAAC,kBAAkD,OAAjC,YAAY,OAAO,CAAC,SAAS,OAC/C;YACJ,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAa,OAAV;QACvB;QACA,OAAO;IACT;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAQ;QACP,MAAM;QACN,WAAU;kBAEV,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,oBACA,uIACA;YAEF,SAAS;;gBAER;8BACD,6LAAC;oBAAK,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE;8BAAS;;;;;;;;;;;;;;;;;AAIvC;GAlDa;;QAYyB,mJAAA,CAAA,oBAAiB;;;KAZ1C", "debugId": null}}, {"offset": {"line": 3189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/sidebar-menu.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface Props {\r\n  title: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport const SidebarMenu = ({ title, children }: Props) => {\r\n  return (\r\n    <div className=\"flex gap-2 flex-col\">\r\n      <span className=\"text-xs font-normal \">{title}</span>\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,cAAc;QAAC,EAAE,KAAK,EAAE,QAAQ,EAAS;IACpD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;YACvC;;;;;;;AAGP;KAPa", "debugId": null}}, {"offset": {"line": 3227, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/sidebar.styles.ts"], "sourcesContent": ["import { tv } from \"@heroui/react\";\r\n\r\nexport const SidebarWrapper = tv({\r\n  base: \"bg-background transition-transform h-full fixed -translate-x-full w-64 shrink-0 z-50 overflow-y-auto border-r border-divider flex-col py-6 px-3 xl:ml-0 xl:flex xl:static xl:h-screen xl:translate-x-0 [&::-webkit-scrollbar]:w-3 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-default-300/30 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-default-300/40 [&::-webkit-scrollbar-thumb]:transition-colors\",\r\n\r\n  variants: {\r\n    collapsed: {\r\n      true: \"translate-x-0 ml-0 pt-20 [display:inherit]\",\r\n    },\r\n  },\r\n  // \"\"\r\n  //   \"@md\": {\r\n  //     marginLeft: \"0\",\r\n  //     display: \"flex\",\r\n  //     position: \"static\",\r\n  //     height: \"100vh\",\r\n  //     transform: \"translateX(0)\",\r\n  //   },\r\n  //   variants: {\r\n  //     collapsed: {\r\n  //       true: {\r\n  //         display: \"inherit\",\r\n  //         marginLeft: \"0 \",\r\n  //         transform: \"translateX(0)\",\r\n  //       },\r\n  //     },\r\n  //   },\r\n});\r\nexport const Overlay = tv({\r\n  base: \"bg-[rgb(15_23_42/0.1)] fixed inset-0 z-40 opacity-40 transition-opacity xl:hidden xl:z-auto xl:opacity-100\",\r\n});\r\n\r\nexport const Header = tv({\r\n  base: \"flex gap-8 items-center px-6\",\r\n});\r\n\r\nexport const Body = tv({\r\n  base: \"flex flex-col gap-6 mt-9 px-2\",\r\n});\r\n\r\nexport const Footer = tv({\r\n  base: \"flex items-center justify-center gap-6 pt-16 pb-8 px-8 xl:pt-10 xl:pb-0\",\r\n});\r\n\r\nexport const Sidebar = Object.assign(SidebarWrapper, {\r\n  Header,\r\n  Body,\r\n  Overlay,\r\n  Footer,\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEO,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,KAAE,AAAD,EAAE;IAC/B,MAAM;IAEN,UAAU;QACR,WAAW;YACT,MAAM;QACR;IACF;AAkBF;AACO,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,KAAE,AAAD,EAAE;IACxB,MAAM;AACR;AAEO,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,KAAE,AAAD,EAAE;IACvB,MAAM;AACR;AAEO,MAAM,OAAO,CAAA,GAAA,kKAAA,CAAA,KAAE,AAAD,EAAE;IACrB,MAAM;AACR;AAEO,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,KAAE,AAAD,EAAE;IACvB,MAAM;AACR;AAEO,MAAM,UAAU,OAAO,MAAM,CAAC,gBAAgB;IACnD;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 3275, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/tkd-horizontal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport {\r\n  Dropdown,\r\n  DropdownTrigger,\r\n  DropdownMenu,\r\n  DropdownItem,\r\n} from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { Building2, Upload, BarChart3, FileText, Database } from \"lucide-react\";\r\n\r\ninterface DropdownItemType {\r\n  label: string;\r\n  href: string;\r\n  icon: React.ReactNode;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | DropdownItemType[];\r\n  isActive?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport const TkdHorizontalDD = ({\r\n  icon,\r\n  title,\r\n  items,\r\n  isActive: isActiveProp,\r\n  children,\r\n}: Props) => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Convert string items to DropdownItemType with default icons and routes\r\n  const dropdownItems: DropdownItemType[] = React.useMemo(() => {\r\n    if (items.length === 0) return [];\r\n\r\n    if (typeof items[0] === \"string\") {\r\n      return (items as string[]).map((item) => {\r\n        let icon: React.ReactNode;\r\n        let href: string;\r\n\r\n        // Handle TKD menu items\r\n        if (\r\n          title.toLowerCase() === \"tkd\" ||\r\n          title.toLowerCase() === \"transfer ke daerah\"\r\n        ) {\r\n          const itemSlug = item\r\n            .toLowerCase()\r\n            .replace(/\\s+/g, \"-\")\r\n            .replace(/[\\/]/g, \"-\");\r\n          href = `/tkd/${itemSlug}`;\r\n\r\n          // Assign icons based on menu item names\r\n          switch (item.toLowerCase()) {\r\n            case \"dashboard\":\r\n              icon = <BarChart3 size={20} />;\r\n              break;\r\n            case \"upload laporan\":\r\n              icon = <Upload size={20} />;\r\n              break;\r\n            case \"dau\":\r\n              icon = <Building2 size={20} />;\r\n              break;\r\n            case \"dbh\":\r\n              icon = <FileText size={20} />;\r\n              break;\r\n            case \"bok\":\r\n              icon = <Database size={20} />;\r\n              break;\r\n            case \"insentif fiskal\":\r\n              icon = <BarChart3 size={20} />;\r\n              break;\r\n            default:\r\n              icon = <Building2 size={20} />;\r\n          }\r\n        } else {\r\n          // Default handling for other menu items\r\n          icon = <Building2 size={20} />;\r\n          href = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n        }\r\n\r\n        return {\r\n          label: item,\r\n          href,\r\n          icon,\r\n        };\r\n      });\r\n    }\r\n    return items as DropdownItemType[];\r\n  }, [items, title]);\r\n\r\n  const handleItemClick = (href: string) => {\r\n    router.push(href);\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (href: string): boolean => {\r\n    return pathname === href;\r\n  };\r\n\r\n  // Compute isActive: true if any dropdown item href matches current pathname\r\n  const computedIsActive = React.useMemo(() => {\r\n    return dropdownItems.some((item) => pathname === item.href);\r\n  }, [dropdownItems, pathname]);\r\n\r\n  const isActive =\r\n    typeof isActiveProp === \"boolean\" ? isActiveProp : computedIsActive;\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <Dropdown placement=\"bottom-start\" className=\"min-w-[240px]\">\r\n        <DropdownTrigger>\r\n          <div\r\n            data-testid=\"hover-dropdown-button\"\r\n            className={clsx(\r\n              isActive\r\n                ? \"bg-secondary-100 [&_svg]:stroke-secondary-500\"\r\n                : \"hover:bg-secondary-100\",\r\n              \"flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]\"\r\n            )}\r\n          >\r\n            {icon}\r\n            <span className=\"text-default-900\">{title}</span>\r\n            {children}\r\n          </div>\r\n        </DropdownTrigger>\r\n        <DropdownMenu\r\n          aria-label={`${title} menu`}\r\n          className=\"w-full font-sans py-2\"\r\n          onAction={(key) => {\r\n            const item = dropdownItems[Number(key)];\r\n            if (item) {\r\n              handleItemClick(item.href);\r\n            }\r\n          }}\r\n        >\r\n          {dropdownItems.map((item, index) => {\r\n            const itemActive = isItemActive(item.href);\r\n            return (\r\n              <DropdownItem\r\n                key={index}\r\n                className={clsx(\r\n                  itemActive\r\n                    ? \"bg-secondary-100 [&_*]:text-secondary\"\r\n                    : \"data-[hover=true]:bg-default-100\",\r\n                  \"font-sans text-sm py-3 px-4 min-h-[44px] group\"\r\n                )}\r\n              >\r\n                <div className=\"flex items-center gap-3\">\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-default-900 [&_svg]:stroke-secondary\"\r\n                        : \"text-default-900 group-hover:text-secondary [&_svg]:group-hover:stroke-secondary\",\r\n                      \"flex-shrink-0 transition-colors\"\r\n                    )}\r\n                  >\r\n                    {React.cloneElement(\r\n                      item.icon as React.ReactElement,\r\n                      {\r\n                        strokeWidth: 2.5,\r\n                      } as any\r\n                    )}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-secondary font-medium\"\r\n                        : \"text-default-900 group-hover:text-secondary font-medium\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {item.label}\r\n                  </span>\r\n                </div>\r\n              </DropdownItem>\r\n            );\r\n          })}\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;AA0BO,MAAM,kBAAkB;QAAC,EAC9B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,UAAU,YAAY,EACtB,QAAQ,EACF;;IACN,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,yEAAyE;IACzE,MAAM,gBAAoC,6JAAA,CAAA,UAAK,CAAC,OAAO;kDAAC;YACtD,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;YAEjC,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,UAAU;gBAChC,OAAO,AAAC,MAAmB,GAAG;8DAAC,CAAC;wBAC9B,IAAI;wBACJ,IAAI;wBAEJ,wBAAwB;wBACxB,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,sBACxB;4BACA,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;4BACpB,OAAO,AAAC,QAAgB,OAAT;4BAEf,wCAAwC;4BACxC,OAAQ,KAAK,WAAW;gCACtB,KAAK;oCACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB;gCACF,KAAK;oCACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;oCACrB;gCACF,KAAK;oCACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB;gCACF,KAAK;oCACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB;gCACF,KAAK;oCACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACvB;gCACF,KAAK;oCACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;oCACxB;gCACF;oCACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;4BAC5B;wBACF,OAAO;4BACL,wCAAwC;4BACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;4BACxB,OAAO,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;wBAChD;wBAEA,OAAO;4BACL,OAAO;4BACP;4BACA;wBACF;oBACF;;YACF;YACA,OAAO;QACT;iDAAG;QAAC;QAAO;KAAM;IAEjB,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,OAAO,aAAa;IACtB;IAEA,4EAA4E;IAC5E,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YACrC,OAAO,cAAc,IAAI;6DAAC,CAAC,OAAS,aAAa,KAAK,IAAI;;QAC5D;oDAAG;QAAC;QAAe;KAAS;IAE5B,MAAM,WACJ,OAAO,iBAAiB,YAAY,eAAe;IAErD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;YAAC,WAAU;YAAe,WAAU;;8BAC3C,6LAAC,oOAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC;wBACC,eAAY;wBACZ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,WACI,kDACA,0BACJ;;4BAGD;0CACD,6LAAC;gCAAK,WAAU;0CAAoB;;;;;;4BACnC;;;;;;;;;;;;8BAGL,6LAAC,8NAAA,CAAA,eAAY;oBACX,cAAY,AAAC,GAAQ,OAAN,OAAM;oBACrB,WAAU;oBACV,UAAU,CAAC;wBACT,MAAM,OAAO,aAAa,CAAC,OAAO,KAAK;wBACvC,IAAI,MAAM;4BACR,gBAAgB,KAAK,IAAI;wBAC3B;oBACF;8BAEC,cAAc,GAAG,CAAC,CAAC,MAAM;wBACxB,MAAM,aAAa,aAAa,KAAK,IAAI;wBACzC,qBACE,6LAAC,2NAAA,CAAA,eAAY;4BAEX,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,0CACA,oCACJ;sCAGF,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,8CACA,oFACJ;kDAGD,cAAA,6JAAA,CAAA,UAAK,CAAC,YAAY,CACjB,KAAK,IAAI,EACT;4CACE,aAAa;wCACf;;;;;;kDAGJ,6LAAC;wCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,+BACA,2DACJ;kDAGD,KAAK,KAAK;;;;;;;;;;;;2BAhCV;;;;;oBAqCX;;;;;;;;;;;;;;;;;AAKV;GAhKa;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KARjB", "debugId": null}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/tkd-vertical.tsx"], "sourcesContent": ["\"use client\";\r\nimport { Accordion, AccordionItem } from \"@heroui/react\";\r\nimport clsx from \"clsx\";\r\nimport { BarChart3, Building2, Database, FileText, Upload } from \"lucide-react\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { ChevronDownIcon } from \"../icons/sidebar/chevron-down-icon\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\n\r\ninterface MenuItem {\r\n  label: string;\r\n  href: string;\r\n}\r\n\r\ninterface Props {\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  items: string[] | MenuItem[];\r\n  isActive?: boolean;\r\n}\r\n\r\nexport const TkdVertikalDD = ({\r\n  icon,\r\n  items,\r\n  title,\r\n  isActive = false,\r\n}: Props) => {\r\n  const [open, setOpen] = useState(false);\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  // Function to get appropriate icon for each menu item\r\n  const getItemIcon = (itemLabel: string) => {\r\n    // Handle TKD menu items\r\n    if (\r\n      title.toLowerCase() === \"tkd\" ||\r\n      title.toLowerCase() === \"transfer ke daerah\"\r\n    ) {\r\n      switch (itemLabel.toLowerCase()) {\r\n        case \"dashboard\":\r\n          return <BarChart3 size={16} />;\r\n        case \"upload laporan\":\r\n          return <Upload size={16} />;\r\n        case \"dau\":\r\n          return <Building2 size={16} />;\r\n        case \"dbh\":\r\n          return <FileText size={16} />;\r\n        case \"bok\":\r\n          return <Database size={16} />;\r\n        case \"insentif fiskal\":\r\n          return <BarChart3 size={16} />;\r\n        default:\r\n          return <Building2 size={16} />;\r\n      }\r\n    } else {\r\n      // Default handling for other menu items\r\n      return <Building2 size={16} />;\r\n    }\r\n  };\r\n\r\n  // Helper function to handle navigation\r\n  const handleItemClick = (item: string | MenuItem) => {\r\n    if (typeof item === \"string\") {\r\n      // For string items, create a route based on the parent title and item name\r\n      let route: string;\r\n\r\n      if (\r\n        title.toLowerCase() === \"tkd\" ||\r\n        title.toLowerCase() === \"transfer ke daerah\"\r\n      ) {\r\n        // Special handling for TKD items\r\n        const itemSlug = item\r\n          .toLowerCase()\r\n          .replace(/\\s+/g, \"-\")\r\n          .replace(/[\\/]/g, \"-\");\r\n        route = `/tkd/${itemSlug}`;\r\n      } else {\r\n        // Default route pattern for other items\r\n        route = `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n      }\r\n\r\n      router.push(route);\r\n    } else {\r\n      // For MenuItem objects, use the href property\r\n      router.push(item.href);\r\n    }\r\n\r\n    // Close the sidebar after navigation (for screens below XL)\r\n    if (window.innerWidth < 1280) {\r\n      setCollapsed();\r\n    }\r\n  };\r\n\r\n  // Helper function to get item label\r\n  const getItemLabel = (item: string | MenuItem): string => {\r\n    return typeof item === \"string\" ? item : item.label;\r\n  };\r\n\r\n  // Helper function to check if an item is active\r\n  const isItemActive = (item: string | MenuItem): boolean => {\r\n    if (typeof item === \"string\") {\r\n      if (\r\n        title.toLowerCase() === \"tkd\" ||\r\n        title.toLowerCase() === \"transfer ke daerah\"\r\n      ) {\r\n        const itemSlug = item\r\n          .toLowerCase()\r\n          .replace(/\\s+/g, \"-\")\r\n          .replace(/[\\/]/g, \"-\");\r\n        return pathname === `/tkd/${itemSlug}`;\r\n      }\r\n      return pathname === `/${item.toLowerCase().replace(/\\s+/g, \"-\")}`;\r\n    } else {\r\n      return pathname === item.href;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex gap-4 h-full items-center cursor-pointer\">\r\n      <Accordion className=\"px-0 w-full\">\r\n        <AccordionItem\r\n          indicator={<ChevronDownIcon />}\r\n          classNames={{\r\n            indicator: \"data-[open=true]:-rotate-180\",\r\n            trigger: clsx(\r\n              isActive\r\n                ? \"bg-primary-100 [&_svg]:stroke-primary-500\"\r\n                : \"hover:bg-default-100\",\r\n              \"py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5\"\r\n            ),\r\n            title:\r\n              \"px-0 flex text-base gap-2 h-full items-center cursor-pointer\",\r\n            content: \"px-0 pb-2\",\r\n          }}\r\n          aria-label=\"Accordion 1\"\r\n          title={\r\n            <div className=\"flex flex-row gap-2\">\r\n              <span>{icon}</span>\r\n              <span>{title}</span>\r\n            </div>\r\n          }\r\n        >\r\n          <div className=\"space-y-1\">\r\n            {items.map((item, index) => {\r\n              const itemLabel = getItemLabel(item);\r\n              const itemActive = isItemActive(item);\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  onClick={() => handleItemClick(item)}\r\n                  className={clsx(\r\n                    itemActive\r\n                      ? \"bg-primary-100 text-primary [&_svg]:text-primary\"\r\n                      : \"hover:text-primary hover:bg-default-100\",\r\n                    \"w-full flex py-2 pl-8 pr-2 rounded-xl transition-colors cursor-pointer min-h-[36px] items-center gap-2 group\"\r\n                  )}\r\n                >\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive\r\n                        ? \"text-primary\"\r\n                        : \"text-default-400 group-hover:text-primary\",\r\n                      \"transition-colors\"\r\n                    )}\r\n                  >\r\n                    {getItemIcon(itemLabel)}\r\n                  </span>\r\n                  <span\r\n                    className={clsx(\r\n                      itemActive ? \"text-primary\" : \"group-hover:text-primary\",\r\n                      \"text-base transition-colors\"\r\n                    )}\r\n                  >\r\n                    {itemLabel}\r\n                  </span>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </AccordionItem>\r\n      </Accordion>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;;AAqBO,MAAM,gBAAgB;QAAC,EAC5B,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACV;;IACN,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,sDAAsD;IACtD,MAAM,cAAc,CAAC;QACnB,wBAAwB;QACxB,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,sBACxB;YACA,OAAQ,UAAU,WAAW;gBAC3B,KAAK;oBACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B,KAAK;oBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;wBAAC,MAAM;;;;;;gBACvB,KAAK;oBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B,KAAK;oBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB,KAAK;oBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;gBACzB,KAAK;oBACH,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;gBAC1B;oBACE,qBAAO,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;YAC5B;QACF,OAAO;YACL,wCAAwC;YACxC,qBAAO,6LAAC,mNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;QAC1B;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,UAAU;YAC5B,2EAA2E;YAC3E,IAAI;YAEJ,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,sBACxB;gBACA,iCAAiC;gBACjC,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;gBACpB,QAAQ,AAAC,QAAgB,OAAT;YAClB,OAAO;gBACL,wCAAwC;gBACxC,QAAQ,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;YACjD;YAEA,OAAO,IAAI,CAAC;QACd,OAAO;YACL,8CAA8C;YAC9C,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;QAEA,4DAA4D;QAC5D,IAAI,OAAO,UAAU,GAAG,MAAM;YAC5B;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK,KAAK;IACrD;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS,UAAU;YAC5B,IACE,MAAM,WAAW,OAAO,SACxB,MAAM,WAAW,OAAO,sBACxB;gBACA,MAAM,WAAW,KACd,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,SAAS;gBACpB,OAAO,aAAa,AAAC,QAAgB,OAAT;YAC9B;YACA,OAAO,aAAa,AAAC,IAA2C,OAAxC,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;QAC7D,OAAO;YACL,OAAO,aAAa,KAAK,IAAI;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,wNAAA,CAAA,YAAS;YAAC,WAAU;sBACnB,cAAA,6LAAC,sOAAA,CAAA,gBAAa;gBACZ,yBAAW,6LAAC,8KAAA,CAAA,kBAAe;;;;;gBAC3B,YAAY;oBACV,WAAW;oBACX,SAAS,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACV,WACI,8CACA,wBACJ;oBAEF,OACE;oBACF,SAAS;gBACX;gBACA,cAAW;gBACX,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAM;;;;;;sCACP,6LAAC;sCAAM;;;;;;;;;;;;0BAIX,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,YAAY,aAAa;wBAC/B,MAAM,aAAa,aAAa;wBAChC,qBACE,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,qDACA,2CACJ;;8CAGF,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aACI,iBACA,6CACJ;8CAGD,YAAY;;;;;;8CAEf,6LAAC;oCACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,iBAAiB,4BAC9B;8CAGD;;;;;;;2BAzBE;;;;;oBA6BX;;;;;;;;;;;;;;;;;;;;;AAMZ;GAnKa;;QAOI,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACQ,mJAAA,CAAA,oBAAiB;;;KAT1C", "debugId": null}}, {"offset": {"line": 3796, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/sidebar/sidebar.tsx"], "sourcesContent": ["import { Tooltip } from \"@heroui/react\";\r\nimport {\r\n  BookCheck,\r\n  ChevronDown,\r\n  Database,\r\n  FileSearch,\r\n  Ham,\r\n  Home,\r\n  Info,\r\n  Settings,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { useSidebarContext } from \"../layout-context\";\r\nimport { EpaHorizontalDD } from \"./epa-hrzntl-dropdown\";\r\nimport { EpaVertikalDD } from \"./epa-vrtkl-dropdown\";\r\nimport { InqHorizontalDD } from \"./inquiry-hrzntl-dropdown\";\r\nimport { InqVertikalDD } from \"./inquiry-vrtkl-dropdown\";\r\nimport { MbgHorizontalDD } from \"./mbg-hrzntl-dropdown\";\r\nimport { MbgVertikalDD } from \"./mbg-vrtkl-dropdown\";\r\nimport { SidebarItem } from \"./sidebar-item\";\r\nimport { SidebarMenu } from \"./sidebar-menu\";\r\nimport { Sidebar } from \"./sidebar.styles\";\r\nimport { TkdHorizontalDD } from \"./tkd-horizontal\";\r\nimport { TkdVertikalDD } from \"./tkd-vertical\";\r\n\r\nexport const SidebarWrapper = () => {\r\n  const pathname = usePathname();\r\n  const { collapsed, setCollapsed } = useSidebarContext();\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile Sidebar (Vertical) */}\r\n      <aside className=\"xl:hidden h-screen z-[20] sticky top-0\">\r\n        {collapsed ? (\r\n          <div className={Sidebar.Overlay()} onClick={setCollapsed} />\r\n        ) : null}\r\n        <div\r\n          className={Sidebar({\r\n            collapsed: collapsed,\r\n          })}\r\n        >\r\n          {/* <div className={Sidebar.Header()}>\r\n            <CompaniesDropdown />\r\n          </div> */}\r\n          <div className=\"flex flex-col justify-between h-full\">\r\n            <div className={Sidebar.Body()}>\r\n              <SidebarItem\r\n                title=\"Dashboard\"\r\n                icon={<Home className=\"text-primary\" />}\r\n                isActive={pathname === \"/\"}\r\n                href=\"/\"\r\n              />\r\n\r\n              <SidebarMenu title=\"Menu Utama\">\r\n                <MbgVertikalDD\r\n                  icon={<Ham className=\"text-amber-700\" />}\r\n                  items={[\"Dashboard\", \"Kertas Kerja\", \"Data\"]}\r\n                  title=\"Makan Bergizi\"\r\n                  isActive={pathname.startsWith(\"/mbg\")}\r\n                />\r\n\r\n                <SidebarItem\r\n                  isActive={pathname === \"/profilkl\"}\r\n                  title=\"Profil K/L\"\r\n                  icon={<Users className=\"text-warning\" />}\r\n                  href=\"/profilkl\"\r\n                />\r\n                <EpaVertikalDD\r\n                  icon={<BookCheck className=\"text-success\" />}\r\n                  items={[\"Summary\", \"Analisa\"]}\r\n                  title=\"EPA\"\r\n                  isActive={pathname.startsWith(\"/epa\")}\r\n                ></EpaVertikalDD>\r\n                <InqVertikalDD\r\n                  icon={<Database className=\"text-secondary\" />}\r\n                  items={[\r\n                    \"Belanja\",\r\n                    \"Tematik\",\r\n                    \"Kontrak\",\r\n                    \"UP/TUP\",\r\n                    \"Bansos\",\r\n                    \"Deviasi\",\r\n                    \"RKAKL Detail\",\r\n                  ]}\r\n                  title=\"Inquiry Data\"\r\n                  isActive={pathname.startsWith(\"/inquiry-data\")}\r\n                />\r\n                <SidebarItem\r\n                  isActive={pathname === \"/test-belanja\"}\r\n                  title=\"Spending Review\"\r\n                  icon={<FileSearch />}\r\n                  href=\"/test-belanja\"\r\n                />\r\n                <TkdVertikalDD\r\n                  icon={\r\n                    <Database\r\n                      className=\"text-secondary\"\r\n                      strokeWidth={2.5}\r\n                      size={26}\r\n                    />\r\n                  }\r\n                  items={[\"DAU\", \"Upload Laporan\"]}\r\n                  title=\"TKD\"\r\n                  isActive={pathname.startsWith(\"/tkd\")}\r\n                />\r\n\r\n                {/* <SidebarItem\r\n                    isActive={pathname === \"/test-belanja\"}\r\n                    title=\"Laporan\"\r\n                    icon={<ScrollText />}\r\n                    href=\"/test-belanja\"\r\n                  /> */}\r\n              </SidebarMenu>\r\n\r\n              <SidebarMenu title=\"Lainnya\">\r\n                <SidebarItem\r\n                  isActive={pathname === \"/tentang-kami\"}\r\n                  title=\"Tentang Kami\"\r\n                  icon={<Info />}\r\n                  href=\"/tentang-kami\"\r\n                />\r\n              </SidebarMenu>\r\n            </div>\r\n            <div className={Sidebar.Footer()}>\r\n              <Tooltip content={\"Settings\"} color=\"primary\">\r\n                <div className=\"max-w-fit\">\r\n                  <Settings />\r\n                </div>\r\n              </Tooltip>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Desktop Horizontal Navbar - Only for XL screens */}\r\n      <nav className=\"hidden xl:block w-full z-[50] bg-white dark:bg-black border-b border-gray-200 dark:border-gray-700 shadow-md fixed top-16\">\r\n        <div className=\"px-6 pt-1 pb-3\">\r\n          <div className=\"flex items-center justify-center font-medium\">\r\n            {/* Center - Navigation Items */}\r\n            <div className=\"flex items-center space-x-4 overflow-x-auto overflow-y-visible\">\r\n              <SidebarItem\r\n                title=\"Dashboard\"\r\n                icon={\r\n                  <Home className=\"text-primary\" strokeWidth={2.5} size={25} />\r\n                }\r\n                isActive={pathname === \"/\"}\r\n                href=\"/\"\r\n              />\r\n\r\n              <MbgHorizontalDD\r\n                icon={\r\n                  <Ham className=\"text-amber-700\" strokeWidth={2.5} size={25} />\r\n                }\r\n                items={[\"Dashboard\", \"Kertas Kerja\", \"Data\"]}\r\n                title=\"Makan Bergizi\"\r\n                isActive={pathname.startsWith(\"/mbg\")}\r\n              >\r\n                <ChevronDown size={20} strokeWidth={2.5} />\r\n              </MbgHorizontalDD>\r\n\r\n              <SidebarItem\r\n                isActive={pathname === \"/profilkl\"}\r\n                title=\"Profil K/L\"\r\n                icon={\r\n                  <Users className=\"text-warning\" strokeWidth={2.5} size={25} />\r\n                }\r\n                href=\"/profilkl\"\r\n                bgColor=\"bg-warning-100\"\r\n                color=\"text-default-900\"\r\n                activeBgColor=\"bg-warning-100\"\r\n                activeColor=\"text-warning\"\r\n                hoverColor=\"hover:bg-warning-100\"\r\n              />\r\n              <EpaHorizontalDD\r\n                icon={\r\n                  <BookCheck\r\n                    className=\"text-success\"\r\n                    strokeWidth={2.5}\r\n                    size={26}\r\n                  />\r\n                }\r\n                items={[\"Summary\", \"Analisa\"]}\r\n                title=\"EPA\"\r\n                isActive={pathname.startsWith(\"/epa\")}\r\n              >\r\n                <ChevronDown size={20} strokeWidth={2.5} />\r\n              </EpaHorizontalDD>\r\n              <SidebarItem\r\n                isActive={pathname === \"/test-belanja\"}\r\n                title=\"Spending Review\"\r\n                icon={\r\n                  <FileSearch\r\n                    className=\"text-teal-500\"\r\n                    strokeWidth={2.5}\r\n                    size={25}\r\n                  />\r\n                }\r\n                href=\"/test-belanja\"\r\n                color=\"text-default-900\"\r\n                activeBgColor=\"bg-teal-100\"\r\n                activeColor=\"text-teal-500\"\r\n                hoverColor=\"hover:bg-teal-100\"\r\n              />\r\n              <TkdHorizontalDD\r\n                icon={\r\n                  <Database\r\n                    className=\"text-secondary\"\r\n                    strokeWidth={2.5}\r\n                    size={26}\r\n                  />\r\n                }\r\n                items={[\"DAU 2024\", \"DAU 2025\", \"Upload Laporan\"]}\r\n                title=\"TKD\"\r\n                isActive={pathname.startsWith(\"/tkd\")}\r\n              >\r\n                <ChevronDown size={20} strokeWidth={2.5} />\r\n              </TkdHorizontalDD>\r\n              <InqHorizontalDD\r\n                icon={\r\n                  <Database\r\n                    className=\"text-secondary\"\r\n                    strokeWidth={2.5}\r\n                    size={26}\r\n                  />\r\n                }\r\n                items={[\r\n                  \"Belanja\",\r\n                  \"Tematik\",\r\n                  \"Kontrak\",\r\n                  \"UP/TUP\",\r\n                  \"Bansos\",\r\n                  \"Deviasi\",\r\n                  \"RKAKL Detail\",\r\n                ]}\r\n                title=\"Inquiry Data\"\r\n                isActive={pathname.startsWith(\"/inquiry-data\")}\r\n              >\r\n                <ChevronDown size={20} strokeWidth={2.5} />\r\n              </InqHorizontalDD>\r\n\r\n              {/* <SidebarItem\r\n                isActive={pathname === \"/test-belanja\"}\r\n                title=\"Laporan\"\r\n                icon={<ScrollText strokeWidth={2.5} size={25} />}\r\n                href=\"/test-belanja\"\r\n              /> */}\r\n              <SidebarItem\r\n                isActive={pathname === \"/tentang-kami\"}\r\n                title=\"Tentang Kami\"\r\n                icon={\r\n                  <Info\r\n                    className=\"text-default-500\"\r\n                    strokeWidth={2.5}\r\n                    size={25}\r\n                  />\r\n                }\r\n                href=\"/tentang-kami\"\r\n                color=\"text-default-900\"\r\n                activeBgColor=\"bg-default-200\"\r\n                activeColor=\"text-default-500\"\r\n                hoverColor=\"hover:bg-default-200\"\r\n              />\r\n            </div>\r\n\r\n            {/* Right side - User Actions */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* <Tooltip content={\"Settings\"} color=\"primary\">\r\n                <div className=\"max-w-fit cursor-pointer\">\r\n                  <Settings />\r\n                </div>\r\n              </Tooltip> */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAEO,MAAM,iBAAiB;;IAC5B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,oBAAiB,AAAD;IAEpD,qBACE;;0BAEE,6LAAC;gBAAM,WAAU;;oBACd,0BACC,6LAAC;wBAAI,WAAW,8JAAA,CAAA,UAAO,CAAC,OAAO;wBAAI,SAAS;;;;;mEAC1C;kCACJ,6LAAC;wBACC,WAAW,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;4BACjB,WAAW;wBACb;kCAKA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,8JAAA,CAAA,UAAO,CAAC,IAAI;;sDAC1B,6LAAC,6JAAA,CAAA,cAAW;4CACV,OAAM;4CACN,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACtB,UAAU,aAAa;4CACvB,MAAK;;;;;;sDAGP,6LAAC,6JAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,6LAAC,sKAAA,CAAA,gBAAa;oDACZ,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACrB,OAAO;wDAAC;wDAAa;wDAAgB;qDAAO;oDAC5C,OAAM;oDACN,UAAU,SAAS,UAAU,CAAC;;;;;;8DAGhC,6LAAC,6JAAA,CAAA,cAAW;oDACV,UAAU,aAAa;oDACvB,OAAM;oDACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDACvB,MAAK;;;;;;8DAEP,6LAAC,sKAAA,CAAA,gBAAa;oDACZ,oBAAM,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAC3B,OAAO;wDAAC;wDAAW;qDAAU;oDAC7B,OAAM;oDACN,UAAU,SAAS,UAAU,CAAC;;;;;;8DAEhC,6LAAC,0KAAA,CAAA,gBAAa;oDACZ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAC1B,OAAO;wDACL;wDACA;wDACA;wDACA;wDACA;wDACA;wDACA;qDACD;oDACD,OAAM;oDACN,UAAU,SAAS,UAAU,CAAC;;;;;;8DAEhC,6LAAC,6JAAA,CAAA,cAAW;oDACV,UAAU,aAAa;oDACvB,OAAM;oDACN,oBAAM,6LAAC,qNAAA,CAAA,aAAU;;;;;oDACjB,MAAK;;;;;;8DAEP,6LAAC,6JAAA,CAAA,gBAAa;oDACZ,oBACE,6LAAC,6MAAA,CAAA,WAAQ;wDACP,WAAU;wDACV,aAAa;wDACb,MAAM;;;;;;oDAGV,OAAO;wDAAC;wDAAO;qDAAiB;oDAChC,OAAM;oDACN,UAAU,SAAS,UAAU,CAAC;;;;;;;;;;;;sDAWlC,6LAAC,6JAAA,CAAA,cAAW;4CAAC,OAAM;sDACjB,cAAA,6LAAC,6JAAA,CAAA,cAAW;gDACV,UAAU,aAAa;gDACvB,OAAM;gDACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;;;;;gDACX,MAAK;;;;;;;;;;;;;;;;;8CAIX,6LAAC;oCAAI,WAAW,8JAAA,CAAA,UAAO,CAAC,MAAM;8CAC5B,cAAA,6LAAC,kNAAA,CAAA,UAAO;wCAAC,SAAS;wCAAY,OAAM;kDAClC,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6JAAA,CAAA,cAAW;wCACV,OAAM;wCACN,oBACE,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;4CAAe,aAAa;4CAAK,MAAM;;;;;;wCAEzD,UAAU,aAAa;wCACvB,MAAK;;;;;;kDAGP,6LAAC,uKAAA,CAAA,kBAAe;wCACd,oBACE,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;4CAAiB,aAAa;4CAAK,MAAM;;;;;;wCAE1D,OAAO;4CAAC;4CAAa;4CAAgB;yCAAO;wCAC5C,OAAM;wCACN,UAAU,SAAS,UAAU,CAAC;kDAE9B,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,aAAa;;;;;;;;;;;kDAGtC,6LAAC,6JAAA,CAAA,cAAW;wCACV,UAAU,aAAa;wCACvB,OAAM;wCACN,oBACE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAe,aAAa;4CAAK,MAAM;;;;;;wCAE1D,MAAK;wCACL,SAAQ;wCACR,OAAM;wCACN,eAAc;wCACd,aAAY;wCACZ,YAAW;;;;;;kDAEb,6LAAC,uKAAA,CAAA,kBAAe;wCACd,oBACE,6LAAC,mNAAA,CAAA,YAAS;4CACR,WAAU;4CACV,aAAa;4CACb,MAAM;;;;;;wCAGV,OAAO;4CAAC;4CAAW;yCAAU;wCAC7B,OAAM;wCACN,UAAU,SAAS,UAAU,CAAC;kDAE9B,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,aAAa;;;;;;;;;;;kDAEtC,6LAAC,6JAAA,CAAA,cAAW;wCACV,UAAU,aAAa;wCACvB,OAAM;wCACN,oBACE,6LAAC,qNAAA,CAAA,aAAU;4CACT,WAAU;4CACV,aAAa;4CACb,MAAM;;;;;;wCAGV,MAAK;wCACL,OAAM;wCACN,eAAc;wCACd,aAAY;wCACZ,YAAW;;;;;;kDAEb,6LAAC,+JAAA,CAAA,kBAAe;wCACd,oBACE,6LAAC,6MAAA,CAAA,WAAQ;4CACP,WAAU;4CACV,aAAa;4CACb,MAAM;;;;;;wCAGV,OAAO;4CAAC;4CAAY;4CAAY;yCAAiB;wCACjD,OAAM;wCACN,UAAU,SAAS,UAAU,CAAC;kDAE9B,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,aAAa;;;;;;;;;;;kDAEtC,6LAAC,2KAAA,CAAA,kBAAe;wCACd,oBACE,6LAAC,6MAAA,CAAA,WAAQ;4CACP,WAAU;4CACV,aAAa;4CACb,MAAM;;;;;;wCAGV,OAAO;4CACL;4CACA;4CACA;4CACA;4CACA;4CACA;4CACA;yCACD;wCACD,OAAM;wCACN,UAAU,SAAS,UAAU,CAAC;kDAE9B,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,aAAa;;;;;;;;;;;kDAStC,6LAAC,6JAAA,CAAA,cAAW;wCACV,UAAU,aAAa;wCACvB,OAAM;wCACN,oBACE,6LAAC,qMAAA,CAAA,OAAI;4CACH,WAAU;4CACV,aAAa;4CACb,MAAM;;;;;;wCAGV,MAAK;wCACL,OAAM;wCACN,eAAc;wCACd,aAAY;wCACZ,YAAW;;;;;;;;;;;;0CAKf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAY3B;GA5Pa;;QACM,qIAAA,CAAA,cAAW;QACQ,mJAAA,CAAA,oBAAiB;;;KAF1C", "debugId": null}}, {"offset": {"line": 4345, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/hooks/ui/useBodyLock.ts"], "sourcesContent": ["import { useEffect, useState } from 'react';\n\nexport function useLockedBody(locked: boolean = false): [boolean, (locked: boolean) => void] {\n  const [isLocked, setIsLocked] = useState(locked);\n\n  useEffect(() => {\n    if (typeof document === 'undefined') return;\n\n    const body = document.body;\n    const originalOverflow = body.style.overflow;\n    const originalPaddingRight = body.style.paddingRight;\n\n    if (isLocked) {\n      // Get the scrollbar width\n      const scrollBarWidth = window.innerWidth - document.documentElement.clientWidth;\n      \n      body.style.overflow = 'hidden';\n      body.style.paddingRight = `${scrollBarWidth}px`;\n    } else {\n      body.style.overflow = originalOverflow;\n      body.style.paddingRight = originalPaddingRight;\n    }\n\n    return () => {\n      body.style.overflow = originalOverflow;\n      body.style.paddingRight = originalPaddingRight;\n    };\n  }, [isLocked]);\n\n  return [isLocked, setIsLocked];\n}\n\nexport default useLockedBody;\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;QAAc,SAAA,iEAAkB;;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,OAAO,aAAa,aAAa;YAErC,MAAM,OAAO,SAAS,IAAI;YAC1B,MAAM,mBAAmB,KAAK,KAAK,CAAC,QAAQ;YAC5C,MAAM,uBAAuB,KAAK,KAAK,CAAC,YAAY;YAEpD,IAAI,UAAU;gBACZ,0BAA0B;gBAC1B,MAAM,iBAAiB,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW;gBAE/E,KAAK,KAAK,CAAC,QAAQ,GAAG;gBACtB,KAAK,KAAK,CAAC,YAAY,GAAG,AAAC,GAAiB,OAAf,gBAAe;YAC9C,OAAO;gBACL,KAAK,KAAK,CAAC,QAAQ,GAAG;gBACtB,KAAK,KAAK,CAAC,YAAY,GAAG;YAC5B;YAEA;2CAAO;oBACL,KAAK,KAAK,CAAC,QAAQ,GAAG;oBACtB,KAAK,KAAK,CAAC,YAAY,GAAG;gBAC5B;;QACF;kCAAG;QAAC;KAAS;IAEb,OAAO;QAAC;QAAU;KAAY;AAChC;GA5BgB;uCA8BD", "debugId": null}}, {"offset": {"line": 4397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/layout/layoutJSX.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavbarWrapper } from \"@/components/layout/header/navbar\";\r\nimport { SidebarWrapper } from \"@/components/layout/sidebar/sidebar\";\r\nimport { useLockedBody } from \"@/hooks/ui/useBodyLock\";\r\nimport React from \"react\";\r\nimport { SidebarContext } from \"./layout-context\";\r\n\r\nexport const LayoutJSX = ({ children }) => {\r\n  const [sidebarOpen, setSidebarOpen] = React.useState(false);\r\n  const [_, setLocked] = useLockedBody(false);\r\n  const handleToggleSidebar = () => {\r\n    setSidebarOpen(!sidebarOpen);\r\n    setLocked(!sidebarOpen);\r\n  };\r\n\r\n  return (\r\n    <SidebarContext.Provider\r\n      value={{\r\n        collapsed: sidebarOpen,\r\n        setCollapsed: handleToggleSidebar,\r\n      }}\r\n    >\r\n      <div className=\"flex\">\r\n        {/* Hamburger Sidebar for all screens except XL */}\r\n        <div className=\"xl:hidden\">\r\n          <SidebarWrapper />\r\n        </div>\r\n\r\n        {/* Main content area with navbar and desktop sidebar */}\r\n        <div className=\"flex-1\">\r\n          <NavbarWrapper>\r\n            {/* Desktop Horizontal Sidebar - positioned below navbar (XL screens only) */}\r\n            <div className=\"hidden xl:block\">\r\n              <SidebarWrapper />\r\n            </div>\r\n            {/* Content with proper padding for fixed navbar and sidebar */}\r\n            <div className=\"xl:pt-16\">{children}</div>\r\n          </NavbarWrapper>\r\n        </div>\r\n      </div>\r\n    </SidebarContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,MAAM,YAAY;QAAC,EAAE,QAAQ,EAAE;;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,GAAG,UAAU,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE;IACrC,MAAM,sBAAsB;QAC1B,eAAe,CAAC;QAChB,UAAU,CAAC;IACb;IAEA,qBACE,6LAAC,mJAAA,CAAA,iBAAc,CAAC,QAAQ;QACtB,OAAO;YACL,WAAW;YACX,cAAc;QAChB;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qJAAA,CAAA,iBAAc;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mJAAA,CAAA,gBAAa;;0CAEZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qJAAA,CAAA,iBAAc;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvC;GAnCa;;QAEY,oIAAA,CAAA,gBAAa;;;KAFzB", "debugId": null}}]}