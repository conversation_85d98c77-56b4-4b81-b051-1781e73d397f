self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f793f15c78691ba6efad96ed648418e18bf59ab7d\": {\n      \"workers\": {\n        \"app/(auth)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/inquiry-data/belanja/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/inquiry-data/belanja/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/login/page\": \"action-browser\",\n        \"app/(dashboard)/dashboard/page\": \"action-browser\",\n        \"app/(dashboard)/inquiry-data/belanja/page\": \"action-browser\",\n        \"app/(dashboard)/page\": \"action-browser\"\n      }\n    },\n    \"7fcb5cfcc0973f18fb0d6c7d4b808fcc5dca7cb9eb\": {\n      \"workers\": {\n        \"app/(auth)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/inquiry-data/belanja/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/inquiry-data/belanja/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/login/page\": \"action-browser\",\n        \"app/(dashboard)/dashboard/page\": \"action-browser\",\n        \"app/(dashboard)/inquiry-data/belanja/page\": \"action-browser\",\n        \"app/(dashboard)/page\": \"action-browser\"\n      }\n    },\n    \"7f4245e88c549c29f0200e45bbbfc5bebfb9249ba0\": {\n      \"workers\": {\n        \"app/(auth)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/inquiry-data/belanja/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/inquiry-data/belanja/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(dashboard)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(dashboard)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/login/page\": \"action-browser\",\n        \"app/(dashboard)/dashboard/page\": \"action-browser\",\n        \"app/(dashboard)/inquiry-data/belanja/page\": \"action-browser\",\n        \"app/(dashboard)/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"tjo5aRt5Eb9A0uoTFVg8uA4Wg69/YPaS48CrWr9kCJM=\"\n}"