{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/framer-motion/dist/es/render/dom/features-animation.mjs"], "sourcesContent": ["import { animations } from '../../motion/features/animations.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domAnimation = {\n    renderer: createDomVisualElement,\n    ...animations,\n    ...gestureAnimations,\n};\n\nexport { domAnimation };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA;;CAEC,GACD,MAAM,eAAe;IACjB,UAAU,kMAAA,CAAA,yBAAsB;IAChC,GAAG,sLAAA,CAAA,aAAU;IACb,GAAG,oLAAA,CAAA,oBAAiB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/node_modules/%40heroui/dom-animation/dist/index.mjs"], "sourcesContent": ["\"use client\";\n\n// src/index.ts\nimport { domAnimation } from \"framer-motion\";\nvar index_default = domAnimation;\nexport {\n  index_default as default\n};\n"], "names": [], "mappings": ";;;AAEA,eAAe;AACf;AAHA;;AAIA,IAAI,gBAAgB,4LAAA,CAAA,eAAY", "ignoreList": [0], "debugId": null}}]}