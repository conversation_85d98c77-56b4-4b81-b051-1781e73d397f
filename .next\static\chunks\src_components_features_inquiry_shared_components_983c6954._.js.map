{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterSwitch.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Switch } from \"@heroui/react\";\r\n\r\nconst FilterSwitch = ({\r\n  id,\r\n  checked,\r\n  onChange,\r\n  label,\r\n  size = \"sm\",\r\n  disabled = false,\r\n}) => {\r\n  return (\r\n    <div\r\n      className={`flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${\r\n        disabled ? \"opacity-50\" : \"\"\r\n      }`}\r\n    >\r\n      <Switch\r\n        id={id}\r\n        isSelected={checked}\r\n        onValueChange={disabled ? undefined : onChange}\r\n        size={size}\r\n        isDisabled={disabled}\r\n        aria-label={label}\r\n        aria-labelledby={`${id}-label`}\r\n        classNames={{\r\n          wrapper:\r\n            \"group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400\",\r\n          thumb: \"group-data-[selected=true]:bg-white shadow-lg\",\r\n        }}\r\n      />\r\n      <label\r\n        id={`${id}-label`}\r\n        htmlFor={id}\r\n        className={`text-sm font-medium transition-colors duration-200 flex-1 ${\r\n          disabled\r\n            ? \"text-gray-400 cursor-not-allowed\"\r\n            : \"text-gray-700 group-hover:text-purple-600 cursor-pointer\"\r\n        }`}\r\n      >\r\n        {label}\r\n      </label>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FilterSwitch;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,eAAe;QAAC,EACpB,EAAE,EACF,OAAO,EACP,QAAQ,EACR,KAAK,EACL,OAAO,IAAI,EACX,WAAW,KAAK,EACjB;IACC,qBACE,6LAAC;QACC,WAAW,AAAC,6FAEX,OADC,WAAW,eAAe;;0BAG5B,6LAAC,+MAAA,CAAA,SAAM;gBACL,IAAI;gBACJ,YAAY;gBACZ,eAAe,WAAW,YAAY;gBACtC,MAAM;gBACN,YAAY;gBACZ,cAAY;gBACZ,mBAAiB,AAAC,GAAK,OAAH,IAAG;gBACvB,YAAY;oBACV,SACE;oBACF,OAAO;gBACT;;;;;;0BAEF,6LAAC;gBACC,IAAI,AAAC,GAAK,OAAH,IAAG;gBACV,SAAS;gBACT,WAAW,AAAC,6DAIX,OAHC,WACI,qCACA;0BAGL;;;;;;;;;;;;AAIT;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/FilterSelector.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card, CardBody } from \"@heroui/react\";\r\nimport FilterSwitch from \"./FilterSwitch\";\r\n\r\n// Import filter group components\r\nimport KementerianFilter from \"./FilterGroups/KementerianFilter\";\r\nimport UnitFilter from \"./FilterGroups/UnitFilter\";\r\nimport LokasiFilter from \"./FilterGroups/LokasiFilter\";\r\nimport FungsiFilter from \"./FilterGroups/FungsiFilter\";\r\nimport SubfungsiFilter from \"./FilterGroups/SubfungsiFilter\";\r\nimport ProgramFilter from \"./FilterGroups/ProgramFilter\";\r\nimport KegiatanFilter from \"./FilterGroups/KegiatanFilter\";\r\nimport OutputFilter from \"./FilterGroups/OutputFilter\";\r\nimport SuboutputFilter from \"./FilterGroups/SuboutputFilter\";\r\nimport KomponenFilter from \"./FilterGroups/KomponenFilter\";\r\nimport SubkomponenFilter from \"./FilterGroups/SubkomponenFilter\";\r\nimport AkunFilter from \"./FilterGroups/AkunFilter\";\r\nimport SumberdanaFilter from \"./FilterGroups/SumberDanaFilter\";\r\nimport RegisterFilter from \"./FilterGroups/RegisterFilter\";\r\nimport InflasiFilter from \"./FilterGroups/InflasiFilter\";\r\nimport IknFilter from \"./FilterGroups/IknFilter\";\r\nimport KemiskinanFilter from \"./FilterGroups/KemiskinanFilter\";\r\nimport PanganFilter from \"./FilterGroups/PanganFilter\";\r\nimport StuntingFilter from \"./FilterGroups/StuntingFilter\";\r\nimport PemiluFilter from \"./FilterGroups/PemiluFilter\";\r\nimport PrinasFilter from \"./FilterGroups/PrinasFilter\";\r\nimport ProgrampriFilter from \"./FilterGroups/ProgrampriFilter\";\r\nimport KegiatanpriFilter from \"./FilterGroups/KegiatanpriFilter\";\r\nimport ProyekprioritasFilter from \"./FilterGroups/ProyekpriFilter\";\r\nimport MajorprFilter from \"./FilterGroups/MajorprFilter\";\r\nimport TematikFilter from \"./FilterGroups/TematikFilter\";\r\nimport DekonFilter from \"./FilterGroups/DekonFilter\";\r\nimport KabkotaFilter from \"./FilterGroups/KabkotaFilter\";\r\nimport KanwilFilter from \"./FilterGroups/KanwilFilter\";\r\nimport KppnFilter from \"./FilterGroups/KppnFilter\";\r\nimport SatkerFilter from \"./FilterGroups/SatkerFilter\";\r\nimport CutoffFilter from \"./FilterGroups/CutoffFilter\";\r\n\r\nconst FilterSection = ({ inquiryState }) => {\r\n  const {\r\n    // Report type for determining default switches\r\n    jenlap,\r\n    // Filter visibility states\r\n    tanggal,\r\n    setTanggal,\r\n    cutoff,\r\n    setCutoff,\r\n    showCutoffSelector,\r\n    setShowCutoffSelector,\r\n    akumulatif,\r\n    setAkumulatif,\r\n    kddept,\r\n    setKddept,\r\n    unit,\r\n    setUnit,\r\n    kddekon,\r\n    setKddekon,\r\n    kdlokasi,\r\n    setKdlokasi,\r\n    kdkabkota,\r\n    setKdkabkota,\r\n    kdkanwil,\r\n    setKdkanwil,\r\n    kdkppn,\r\n    setKdkppn,\r\n    kdsatker,\r\n    setKdsatker,\r\n    kdfungsi,\r\n    setKdfungsi,\r\n    kdsfungsi,\r\n    setKdsfungsi,\r\n    kdprogram,\r\n    setKdprogram,\r\n    kdgiat,\r\n    setKdgiat,\r\n    kdoutput,\r\n    setKdoutput,\r\n    kdsoutput,\r\n    setKdsoutput,\r\n    kdkomponen,\r\n    setKdkomponen,\r\n    kdskomponen,\r\n    setKdskomponen,\r\n    kdakun,\r\n    setKdakun,\r\n    kdsdana,\r\n    setKdsdana,\r\n    kdregister,\r\n    setKdregister,\r\n    kdInflasi,\r\n    setKdInflasi,\r\n    kdIkn,\r\n    setKdIkn,\r\n    kdKemiskinan,\r\n    setKdKemiskinan,\r\n    KdPRI,\r\n    setKdPRI,\r\n    KdPangan,\r\n    setKdPangan,\r\n    KdPemilu,\r\n    setKdPemilu,\r\n    KdStunting,\r\n    setKdStunting,\r\n    KdTema,\r\n    setKdTema,\r\n    KdPN,\r\n    setKdPN,\r\n    KdPP,\r\n    setKdPP,\r\n    KdKegPP,\r\n    setKdKegPP,\r\n    KdMP,\r\n    setKdMP,\r\n\r\n    // Filter values and conditions\r\n    dept,\r\n    setDept,\r\n    deptkondisi,\r\n    setDeptkondisi,\r\n    katadept,\r\n    setKatadept,\r\n    deptradio,\r\n    setDeptradio,\r\n    kdunit,\r\n    setKdunit,\r\n    unitkondisi,\r\n    setUnitkondisi,\r\n    kataunit,\r\n    setKataunit,\r\n    unitradio,\r\n    setUnitradio,\r\n    dekon,\r\n    setDekon,\r\n    dekonkondisi,\r\n    setDekonkondisi,\r\n    katadekon,\r\n    setKatadekon,\r\n    dekonradio,\r\n    setDekonradio,\r\n    prov,\r\n    setProv,\r\n    lokasikondisi,\r\n    setLokasikondisi,\r\n    katalokasi,\r\n    setKatalokasi,\r\n    locradio,\r\n    setLocradio,\r\n    kabkota,\r\n    setKabkota,\r\n    kabkotakondisi,\r\n    setKabkotakondisi,\r\n    katakabkota,\r\n    setKatakabkota,\r\n    kabkotaradio,\r\n    setKabkotaradio,\r\n    kanwil,\r\n    setKanwil,\r\n    kanwilkondisi,\r\n    setKanwilkondisi,\r\n    katakanwil,\r\n    setKatakanwil,\r\n    kanwilradio,\r\n    setKanwilradio,\r\n    kppn,\r\n    setKppn,\r\n    kppnkondisi,\r\n    setKppnkondisi,\r\n    katakppn,\r\n    setKatakppn,\r\n    kppnradio,\r\n    setKppnradio,\r\n    satker,\r\n    setSatker,\r\n    satkerkondisi,\r\n    setSatkerkondisi,\r\n    katasatker,\r\n    setKatasatker,\r\n    satkerradio,\r\n    setSatkerradio,\r\n    fungsi,\r\n    setFungsi,\r\n    fungsikondisi,\r\n    setFungsikondisi,\r\n    katafungsi,\r\n    setKatafungsi,\r\n    fungsiradio,\r\n    setFungsiradio,\r\n    sfungsi,\r\n    setSfungsi,\r\n    subfungsikondisi,\r\n    setSubfungsikondisi,\r\n    katasubfungsi,\r\n    setKatasubfungsi,\r\n    subfungsiradio,\r\n    setSubfungsiradio,\r\n    program,\r\n    setProgram,\r\n    programkondisi,\r\n    setProgramkondisi,\r\n    kataprogram,\r\n    setKataprogram,\r\n    programradio,\r\n    setProgramradio,\r\n    giat,\r\n    setGiat,\r\n    giatkondisi,\r\n    setGiatkondisi,\r\n    katagiat,\r\n    setKatagiat,\r\n    kegiatanradio,\r\n    setKegiatanradio,\r\n    output,\r\n    setOutput,\r\n    outputkondisi,\r\n    setOutputkondisi,\r\n    kataoutput,\r\n    setKataoutput,\r\n    outputradio,\r\n    setOutputradio,\r\n    soutput,\r\n    setsOutput,\r\n    soutputkondisi,\r\n    setSoutputkondisi,\r\n    katasoutput,\r\n    setKatasoutput,\r\n    soutputradio,\r\n    setsOutputradio,\r\n    komponen,\r\n    setKomponen,\r\n    komponenkondisi,\r\n    setKomponenkondisi,\r\n    katakomponen,\r\n    setKatakomponen,\r\n    komponenradio,\r\n    setKomponenradio,\r\n    skomponen,\r\n    setSkomponen,\r\n    skomponenkondisi,\r\n    setSkomponenkondisi,\r\n    kataskomponen,\r\n    setKataskomponen,\r\n    skomponenradio,\r\n    setSkomponenradio,\r\n    akun,\r\n    setAkun,\r\n    akunkondisi,\r\n    setAkunkondisi,\r\n    kataakun,\r\n    setKataakun,\r\n    akunradio,\r\n    setAkunradio,\r\n    sdana,\r\n    setSdana,\r\n    sdanakondisi,\r\n    setSdanakondisi,\r\n    katasdana,\r\n    setKatasdana,\r\n    sdanaradio,\r\n    setSdanaradio,\r\n    register,\r\n    setRegister,\r\n    registerkondisi,\r\n    setRegisterkondisi,\r\n    kataregister,\r\n    setKataregister,\r\n    registerradio,\r\n    setRegisterradio,\r\n    Inflasi,\r\n    setInflasi,\r\n    inflasiradio,\r\n    setInflasiradio,\r\n    opsiInflasi,\r\n    setOpsiInflasi,\r\n    Ikn,\r\n    setIkn,\r\n    iknradio,\r\n    setIknradio,\r\n    opsiIkn,\r\n    setOpsiIkn,\r\n    Miskin,\r\n    setMiskin,\r\n    kemiskinanradio,\r\n    setKemiskinanradio,\r\n    opsiKemiskinan,\r\n    setOpsiKemiskinan,\r\n    Pangan,\r\n    setPangan,\r\n    panganradio,\r\n    setPanganradio,\r\n    opsiPangan,\r\n    setOpsiPangan,\r\n    Stunting,\r\n    setStunting,\r\n    stuntingradio,\r\n    setStuntingradio,\r\n    opsiStunting,\r\n    setOpsiStunting,\r\n    PN,\r\n    setPN,\r\n    pnradio,\r\n    setPnradio,\r\n    PP,\r\n    setPP,\r\n    ppradio,\r\n    setPpradio,\r\n    kegiatanprioritas,\r\n    setKegiatanPrioritas,\r\n    kegiatanprioritasradio,\r\n    setKegiatanPrioritasRadio,\r\n    MP,\r\n    setMP,\r\n    mpradio,\r\n    setMpradio,\r\n    Tema,\r\n    setTema,\r\n    temaradio,\r\n    setTemaradio,\r\n    Pemilu,\r\n    setPemilu,\r\n    pemiluradio,\r\n    setPemiluradio,\r\n    PRI,\r\n    setPRI,\r\n    priradio,\r\n    setPriradio,\r\n  } = inquiryState;\r\n\r\n  // Remove local state - use the actual filter switch states from inquiryState instead\r\n  // const [showKementerian, setShowKementerian] = React.useState(false);\r\n  // const [showUnit, setShowUnit] = React.useState(false);\r\n  // const [showDekon, setShowDekon] = React.useState(false);\r\n  // const [showKabkota, setShowKabkota] = React.useState(false);\r\n  // const [showKanwil, setShowKanwil] = React.useState(false);\r\n  // const [showLokasi, setShowLokasi] = React.useState(false);\r\n\r\n  // Use the actual filter switch states from inquiryState\r\n  const showKementerian = kddept;\r\n  const setShowKementerian = setKddept;\r\n  const showUnit = unit;\r\n  const setShowUnit = setUnit;\r\n  const showDekon = kddekon;\r\n  const setShowDekon = setKddekon;\r\n  const showKabkota = kdkabkota;\r\n  const setShowKabkota = setKdkabkota;\r\n  const showKanwil = kdkanwil;\r\n  const setShowKanwil = setKdkanwil;\r\n  const showLokasi = kdlokasi;\r\n  const setShowLokasi = setKdlokasi;\r\n\r\n  // Determine which switches should be disabled based on report type\r\n  const getDisabledSwitches = (reportType) => {\r\n    // For Volume Output Kegiatan (6), disable cutoff, akun, register, sumber dana\r\n    if (reportType === \"6\") {\r\n      return [\r\n        \"cutoff\", // Cutoff\r\n        \"kdakun\", // Akun\r\n        \"kdregister\", // Register\r\n        \"kdsdana\", // Sumber Dana\r\n      ];\r\n    }\r\n\r\n    // Base disabled switches for most report types (same as Pagu Realisasi)\r\n    const baseDisabledSwitches = [\r\n      \"kdsoutput\", // Sub-output\r\n      \"KdPN\", // Prioritas Nasional\r\n      \"KdPP\", // Program Prioritas\r\n      \"KdKegPP\", // Kegiatan Prioritas\r\n      \"KdPRI\", // Proyek Prioritas\r\n      \"KdMP\", // Major Project\r\n      \"KdTema\", // Tematik\r\n      \"kdInflasi\", // Inflasi\r\n      \"KdStunting\", // Stunting\r\n      \"kdKemiskinan\", // Kemiskinan\r\n      \"KdPemilu\", // Pemilu\r\n      \"kdIkn\", // IKN\r\n      \"KdPangan\", // Pangan\r\n    ];\r\n\r\n    // For Pagu APBN (1), disable the same switches as other types plus cutoff\r\n    if (reportType === \"1\") {\r\n      return [...baseDisabledSwitches, \"cutoff\"];\r\n    }\r\n\r\n    // For all other report types (including Pagu Realisasi \"2\"), disable base switches\r\n    return baseDisabledSwitches;\r\n  };\r\n\r\n  const disabledSwitches = getDisabledSwitches(jenlap);\r\n\r\n  // Automatically turn off disabled switches when report type changes\r\n  // This effect runs when jenlap changes to manage switch states\r\n  React.useEffect(() => {\r\n    if (!jenlap) return;\r\n\r\n    // If switching to a report type that disables certain switches, turn them off\r\n    if (disabledSwitches.length > 0) {\r\n      if (disabledSwitches.includes(\"kdsoutput\") && kdsoutput) {\r\n        setKdsoutput && setKdsoutput(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPN\") && KdPN) {\r\n        setKdPN && setKdPN(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPP\") && KdPP) {\r\n        setKdPP && setKdPP(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdKegPP\") && KdKegPP) {\r\n        setKdKegPP && setKdKegPP(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPRI\") && KdPRI) {\r\n        setKdPRI && setKdPRI(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdMP\") && KdMP) {\r\n        setKdMP && setKdMP(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdTema\") && KdTema) {\r\n        setKdTema && setKdTema(false);\r\n      }\r\n      if (disabledSwitches.includes(\"kdInflasi\") && kdInflasi) {\r\n        setKdInflasi && setKdInflasi(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdStunting\") && KdStunting) {\r\n        setKdStunting && setKdStunting(false);\r\n      }\r\n      if (disabledSwitches.includes(\"kdKemiskinan\") && kdKemiskinan) {\r\n        setKdKemiskinan && setKdKemiskinan(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPemilu\") && KdPemilu) {\r\n        setKdPemilu && setKdPemilu(false);\r\n      }\r\n      if (disabledSwitches.includes(\"kdIkn\") && kdIkn) {\r\n        setKdIkn && setKdIkn(false);\r\n      }\r\n      if (disabledSwitches.includes(\"KdPangan\") && KdPangan) {\r\n        setKdPangan && setKdPangan(false);\r\n      }\r\n      if (disabledSwitches.includes(\"cutoff\") && cutoff !== \"0\") {\r\n        setCutoff && setCutoff(\"0\");\r\n        setShowCutoffSelector && setShowCutoffSelector(false);\r\n      }\r\n    }\r\n  }, [jenlap]); // Only depend on jenlap to avoid circular dependencies\r\n\r\n  // Reset filter values when switches are turned off\r\n  React.useEffect(() => {\r\n    if (!kddept) {\r\n      // Reset Kementerian filter state\r\n      setDept && setDept(\"000\"); // Default dept value\r\n      setDeptkondisi && setDeptkondisi(\"\");\r\n      setKatadept && setKatadept(\"\");\r\n      setDeptradio && setDeptradio(\"1\");\r\n    }\r\n  }, [kddept, setDept, setDeptkondisi, setKatadept, setDeptradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!unit) {\r\n      // Reset Unit filter state\r\n      setKdunit && setKdunit(\"XX\");\r\n      setUnitkondisi && setUnitkondisi(\"\");\r\n      setKataunit && setKataunit(\"\");\r\n      setUnitradio && setUnitradio(\"1\");\r\n    }\r\n  }, [unit, setKdunit, setUnitkondisi, setKataunit, setUnitradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kddekon) {\r\n      // Reset Dekon filter state\r\n      setDekon && setDekon(\"XX\");\r\n      setDekonkondisi && setDekonkondisi(\"\");\r\n      setKatadekon && setKatadekon(\"\");\r\n      setDekonradio && setDekonradio(\"1\");\r\n    }\r\n  }, [kddekon, setDekon, setDekonkondisi, setKatadekon, setDekonradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdlokasi) {\r\n      // Reset Provinsi filter state\r\n      setProv && setProv(\"XX\");\r\n      setLokasikondisi && setLokasikondisi(\"\");\r\n      setKatalokasi && setKatalokasi(\"\");\r\n      setLocradio && setLocradio(\"1\");\r\n    }\r\n  }, [kdlokasi, setProv, setLokasikondisi, setKatalokasi, setLocradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkabkota) {\r\n      // Reset Kabkota filter state\r\n      setKabkota && setKabkota(\"XX\");\r\n      setKabkotakondisi && setKabkotakondisi(\"\");\r\n      setKatakabkota && setKatakabkota(\"\");\r\n      setKabkotaradio && setKabkotaradio(\"1\");\r\n    }\r\n  }, [\r\n    kdkabkota,\r\n    setKabkota,\r\n    setKabkotakondisi,\r\n    setKatakabkota,\r\n    setKabkotaradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkanwil) {\r\n      // Reset Kanwil filter state\r\n      setKanwil && setKanwil(\"XX\");\r\n      setKanwilkondisi && setKanwilkondisi(\"\");\r\n      setKatakanwil && setKatakanwil(\"\");\r\n      setKanwilradio && setKanwilradio(\"1\");\r\n    }\r\n  }, [kdkanwil, setKanwil, setKanwilkondisi, setKatakanwil, setKanwilradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkppn) {\r\n      // Reset KPPN filter state\r\n      setKppn && setKppn(\"XX\");\r\n      setKppnkondisi && setKppnkondisi(\"\");\r\n      setKatakppn && setKatakppn(\"\");\r\n      setKppnradio && setKppnradio(\"1\");\r\n    }\r\n  }, [kdkppn, setKppn, setKppnkondisi, setKatakppn, setKppnradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsatker) {\r\n      // Reset Satker filter state\r\n      setSatker && setSatker(\"XX\");\r\n      setSatkerkondisi && setSatkerkondisi(\"\");\r\n      setKatasatker && setKatasatker(\"\");\r\n      setSatkerradio && setSatkerradio(\"1\");\r\n    }\r\n  }, [kdsatker, setSatker, setSatkerkondisi, setKatasatker, setSatkerradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdfungsi) {\r\n      // Reset Fungsi filter state\r\n      setFungsi && setFungsi(\"XX\");\r\n      setFungsikondisi && setFungsikondisi(\"\");\r\n      setKatafungsi && setKatafungsi(\"\");\r\n      setFungsiradio && setFungsiradio(\"1\");\r\n    }\r\n  }, [kdfungsi, setFungsi, setFungsikondisi, setKatafungsi, setFungsiradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsfungsi) {\r\n      // Reset Sub-fungsi filter state\r\n      setSfungsi && setSfungsi(\"XX\");\r\n      setSubfungsikondisi && setSubfungsikondisi(\"\");\r\n      setKatasubfungsi && setKatasubfungsi(\"\");\r\n      setSubfungsiradio && setSubfungsiradio(\"1\");\r\n    }\r\n  }, [\r\n    kdsfungsi,\r\n    setSfungsi,\r\n    setSubfungsikondisi,\r\n    setKatasubfungsi,\r\n    setSubfungsiradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdprogram) {\r\n      // Reset Program filter state\r\n      setProgram && setProgram(\"XX\");\r\n      setProgramkondisi && setProgramkondisi(\"\");\r\n      setKataprogram && setKataprogram(\"\");\r\n      setProgramradio && setProgramradio(\"1\");\r\n    }\r\n  }, [\r\n    kdprogram,\r\n    setProgram,\r\n    setProgramkondisi,\r\n    setKataprogram,\r\n    setProgramradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdgiat) {\r\n      // Reset Kegiatan filter state\r\n      setGiat && setGiat(\"XX\");\r\n      setGiatkondisi && setGiatkondisi(\"\");\r\n      setKatagiat && setKatagiat(\"\");\r\n      setKegiatanradio && setKegiatanradio(\"1\");\r\n    }\r\n  }, [kdgiat, setGiat, setGiatkondisi, setKatagiat, setKegiatanradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdoutput) {\r\n      // Reset Output filter state\r\n      setOutput && setOutput(\"XX\");\r\n      setOutputkondisi && setOutputkondisi(\"\");\r\n      setKataoutput && setKataoutput(\"\");\r\n      setOutputradio && setOutputradio(\"1\");\r\n    }\r\n  }, [kdoutput, setOutput, setOutputkondisi, setKataoutput, setOutputradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsoutput) {\r\n      // Reset Sub-output filter state\r\n      setsOutput && setsOutput(\"XX\");\r\n      setSoutputkondisi && setSoutputkondisi(\"\");\r\n      setKatasoutput && setKatasoutput(\"\");\r\n      setsOutputradio && setsOutputradio(\"1\");\r\n    }\r\n  }, [\r\n    kdsoutput,\r\n    setsOutput,\r\n    setSoutputkondisi,\r\n    setKatasoutput,\r\n    setsOutputradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdkomponen) {\r\n      // Reset Komponen filter state\r\n      setKomponen && setKomponen(\"XX\");\r\n      setKomponenkondisi && setKomponenkondisi(\"\");\r\n      setKatakomponen && setKatakomponen(\"\");\r\n      setKomponenradio && setKomponenradio(\"1\");\r\n    }\r\n  }, [\r\n    kdkomponen,\r\n    setKomponen,\r\n    setKomponenkondisi,\r\n    setKatakomponen,\r\n    setKomponenradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdskomponen) {\r\n      // Reset Sub-komponen filter state\r\n      setSkomponen && setSkomponen(\"XX\");\r\n      setSkomponenkondisi && setSkomponenkondisi(\"\");\r\n      setKataskomponen && setKataskomponen(\"\");\r\n      setSkomponenradio && setSkomponenradio(\"1\");\r\n    }\r\n  }, [\r\n    kdskomponen,\r\n    setSkomponen,\r\n    setSkomponenkondisi,\r\n    setKataskomponen,\r\n    setSkomponenradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdakun) {\r\n      // Reset Akun filter state\r\n      setAkun && setAkun(\"AKUN\");\r\n      setAkunkondisi && setAkunkondisi(\"\");\r\n      setKataakun && setKataakun(\"\");\r\n      setAkunradio && setAkunradio(\"1\");\r\n    }\r\n  }, [kdakun, setAkun, setAkunkondisi, setKataakun, setAkunradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdsdana) {\r\n      // Reset Sumber Dana filter state\r\n      setSdana && setSdana(\"XX\");\r\n      setSdanakondisi && setSdanakondisi(\"\");\r\n      setKatasdana && setKatasdana(\"\");\r\n      setSdanaradio && setSdanaradio(\"1\");\r\n    }\r\n  }, [kdsdana, setSdana, setSdanakondisi, setKatasdana, setSdanaradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdregister) {\r\n      // Reset Register filter state\r\n      setRegister && setRegister(\"XX\");\r\n      setRegisterkondisi && setRegisterkondisi(\"\");\r\n      setKataregister && setKataregister(\"\");\r\n      setRegisterradio && setRegisterradio(\"1\");\r\n    }\r\n  }, [\r\n    kdregister,\r\n    setRegister,\r\n    setRegisterkondisi,\r\n    setKataregister,\r\n    setRegisterradio,\r\n  ]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdInflasi) {\r\n      // Reset Inflasi filter state\r\n      setInflasi && setInflasi(\"00\");\r\n      setInflasiradio && setInflasiradio(\"1\");\r\n    }\r\n  }, [kdInflasi, setInflasi, setInflasiradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdIkn) {\r\n      // Reset IKN filter state\r\n      setIkn && setIkn(\"00\");\r\n      setIknradio && setIknradio(\"1\");\r\n    }\r\n  }, [kdIkn, setIkn, setIknradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!kdKemiskinan) {\r\n      // Reset Kemiskinan filter state\r\n      setMiskin && setMiskin(\"00\");\r\n      setKemiskinanradio && setKemiskinanradio(\"1\");\r\n    }\r\n  }, [kdKemiskinan, setMiskin, setKemiskinanradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPangan) {\r\n      // Reset Pangan filter state\r\n      setPangan && setPangan(\"00\");\r\n      setPanganradio && setPanganradio(\"1\");\r\n    }\r\n  }, [KdPangan, setPangan, setPanganradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdStunting) {\r\n      // Reset Stunting filter state\r\n      setStunting && setStunting(\"00\");\r\n      setStuntingradio && setStuntingradio(\"1\");\r\n    }\r\n  }, [KdStunting, setStunting, setStuntingradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPN) {\r\n      // Reset Prinas filter state\r\n      setPN && setPN(\"00\");\r\n      setPnradio && setPnradio(\"1\");\r\n    }\r\n  }, [KdPN, setPN, setPnradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPP) {\r\n      // Reset Programpri filter state\r\n      setPP && setPP(\"00\");\r\n      setPpradio && setPpradio(\"1\");\r\n    }\r\n  }, [KdPP, setPP, setPpradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdKegPP) {\r\n      // Reset Kegiatanpri filter state\r\n      setKegiatanPrioritas && setKegiatanPrioritas(\"XX\");\r\n      setKegiatanPrioritasRadio && setKegiatanPrioritasRadio(\"1\");\r\n    }\r\n  }, [KdKegPP, setKegiatanPrioritas, setKegiatanPrioritasRadio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdMP) {\r\n      // Reset Majorpr filter state\r\n      setMP && setMP(\"00\");\r\n      setMpradio && setMpradio(\"1\");\r\n    }\r\n  }, [KdMP, setMP, setMpradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdTema) {\r\n      // Reset Tematik filter state\r\n      setTema && setTema(\"00\");\r\n      setTemaradio && setTemaradio(\"1\");\r\n    }\r\n  }, [KdTema, setTema, setTemaradio]);\r\n\r\n  React.useEffect(() => {\r\n    if (!KdPemilu) {\r\n      // Reset Pemilu filter state\r\n      setPemilu && setPemilu(\"00\");\r\n      setPemiluradio && setPemiluradio(\"1\");\r\n    }\r\n  }, [KdPemilu, setPemilu, setPemiluradio]);\r\n\r\n  // Set default filter switches based on report type (jenlap)\r\n  React.useEffect(() => {\r\n    if (!jenlap) return; // Don't do anything if jenlap is not set\r\n\r\n    // Define default switches for each report type\r\n    const getDefaultSwitches = (reportType) => {\r\n      // Base configuration: Only Kementerian is ON by default for all report types\r\n      const baseConfig = {\r\n        kddept: true, // Kementerian is always ON\r\n        unit: false,\r\n        kddekon: false,\r\n        kdlokasi: false,\r\n        kdkabkota: false,\r\n        kdkanwil: false,\r\n        kdkppn: false,\r\n        kdsatker: false,\r\n        kdfungsi: false,\r\n        kdsfungsi: false,\r\n        kdprogram: false,\r\n        kdgiat: false,\r\n        kdoutput: false,\r\n        kdsoutput: false,\r\n        kdakun: false,\r\n        kdsdana: false,\r\n        kdregister: false,\r\n        KdPN: false,\r\n        KdPP: false,\r\n        KdKegPP: false,\r\n        KdPRI: false,\r\n        KdMP: false,\r\n        KdTema: false,\r\n        kdInflasi: false,\r\n        KdStunting: false,\r\n        kdKemiskinan: false,\r\n        KdPemilu: false,\r\n        kdIkn: false,\r\n        KdPangan: false,\r\n      };\r\n\r\n      // Return base config for all report types\r\n      // Special cases (Pagu APBN and Volume Output Kegiatan) will have all switches available\r\n      // Other report types will have certain switches disabled (handled in the UI)\r\n      return baseConfig;\r\n    };\r\n\r\n    const defaultSwitches = getDefaultSwitches(jenlap);\r\n\r\n    // Apply the default switches\r\n    setKddept && setKddept(defaultSwitches.kddept);\r\n    setUnit && setUnit(defaultSwitches.unit);\r\n    setKddekon && setKddekon(defaultSwitches.kddekon);\r\n    setKdlokasi && setKdlokasi(defaultSwitches.kdlokasi);\r\n    setKdkabkota && setKdkabkota(defaultSwitches.kdkabkota);\r\n    setKdkanwil && setKdkanwil(defaultSwitches.kdkanwil);\r\n    setKdkppn && setKdkppn(defaultSwitches.kdkppn);\r\n    setKdsatker && setKdsatker(defaultSwitches.kdsatker);\r\n    setKdfungsi && setKdfungsi(defaultSwitches.kdfungsi);\r\n    setKdsfungsi && setKdsfungsi(defaultSwitches.kdsfungsi);\r\n    setKdprogram && setKdprogram(defaultSwitches.kdprogram);\r\n    setKdgiat && setKdgiat(defaultSwitches.kdgiat);\r\n    setKdoutput && setKdoutput(defaultSwitches.kdoutput);\r\n    setKdsoutput && setKdsoutput(defaultSwitches.kdsoutput);\r\n    setKdakun && setKdakun(defaultSwitches.kdakun);\r\n    setKdsdana && setKdsdana(defaultSwitches.kdsdana);\r\n    setKdregister && setKdregister(defaultSwitches.kdregister);\r\n    setKdPN && setKdPN(defaultSwitches.KdPN);\r\n    setKdPP && setKdPP(defaultSwitches.KdPP);\r\n    setKdKegPP && setKdKegPP(defaultSwitches.KdKegPP);\r\n    setKdPRI && setKdPRI(defaultSwitches.KdPRI);\r\n    setKdMP && setKdMP(defaultSwitches.KdMP);\r\n    setKdTema && setKdTema(defaultSwitches.KdTema);\r\n    setKdInflasi && setKdInflasi(defaultSwitches.kdInflasi);\r\n    setKdStunting && setKdStunting(defaultSwitches.KdStunting);\r\n    setKdKemiskinan && setKdKemiskinan(defaultSwitches.kdKemiskinan);\r\n    setKdPemilu && setKdPemilu(defaultSwitches.KdPemilu);\r\n    setKdIkn && setKdIkn(defaultSwitches.kdIkn);\r\n    setKdPangan && setKdPangan(defaultSwitches.KdPangan);\r\n  }, [jenlap]); // Only run when jenlap changes\r\n\r\n  return (\r\n    <>\r\n      {/* Filter Options Card - Modern design with gradient and rounded corners */}\r\n      <div className=\"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl\">\r\n        {/* <div className=\"flex items-center gap-3 mb-4\">\r\n          <h5 className=\"text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">\r\n            Filter Options\r\n          </h5>\r\n        </div> */}\r\n        <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3\">\r\n          {/* <FilterSwitch\r\n            id=\"tanggal-filter\"\r\n            checked={tanggal}\r\n            onChange={setTanggal}\r\n            label=\"Tanggal\"\r\n          /> */}\r\n          <FilterSwitch\r\n            id=\"cutoff-filter\"\r\n            checked={cutoff !== \"0\"}\r\n            onChange={(val) => {\r\n              if (val) {\r\n                // When enabled, reset to January\r\n                setCutoff(\"1\");\r\n              } else {\r\n                // When disabled, set to \"0\" (disabled state)\r\n                setCutoff(\"0\");\r\n              }\r\n              setShowCutoffSelector(val);\r\n            }}\r\n            label=\"Cutoff\"\r\n            disabled={disabledSwitches.includes(\"cutoff\")}\r\n          />\r\n          {/* <FilterSwitch\r\n            id=\"akumulatif-filter\"\r\n            checked={akumulatif}\r\n            onChange={setAkumulatif}\r\n            label=\"Akumulatif\"\r\n          /> */}\r\n          <FilterSwitch\r\n            id=\"kddept-filter\"\r\n            checked={Boolean(showKementerian)}\r\n            onChange={setShowKementerian}\r\n            label=\"Kementerian\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"unit-filter\"\r\n            checked={showUnit}\r\n            onChange={setShowUnit}\r\n            label=\"Eselon I\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"dekon-filter\"\r\n            checked={showDekon}\r\n            onChange={setShowDekon}\r\n            label=\"Kewenangan\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"lokasi-filter\"\r\n            checked={showLokasi}\r\n            onChange={setShowLokasi}\r\n            label=\"Provinsi\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kabkota-filter\"\r\n            checked={showKabkota}\r\n            onChange={setShowKabkota}\r\n            label=\"Kabupaten/Kota\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kanwil-filter\"\r\n            checked={showKanwil}\r\n            onChange={setShowKanwil}\r\n            label=\"Kanwil\"\r\n          />\r\n\r\n          <FilterSwitch\r\n            id=\"kdkppn-filter\"\r\n            checked={kdkppn}\r\n            onChange={setKdkppn}\r\n            label=\"KPPN\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdsatker-filter\"\r\n            checked={kdsatker}\r\n            onChange={setKdsatker}\r\n            label=\"Satker\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdfungsi-filter\"\r\n            checked={kdfungsi}\r\n            onChange={setKdfungsi}\r\n            label=\"Fungsi\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdsfungsi-filter\"\r\n            checked={kdsfungsi}\r\n            onChange={setKdsfungsi}\r\n            label=\"Sub-fungsi\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdprogram-filter\"\r\n            checked={kdprogram}\r\n            onChange={setKdprogram}\r\n            label=\"Program\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdgiat-filter\"\r\n            checked={kdgiat}\r\n            onChange={setKdgiat}\r\n            label=\"Kegiatan\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdoutput-filter\"\r\n            checked={kdoutput}\r\n            onChange={setKdoutput}\r\n            label=\"Output\"\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdsoutput-filter\"\r\n            checked={kdsoutput}\r\n            onChange={setKdsoutput}\r\n            label=\"Sub-output\"\r\n            disabled={disabledSwitches.includes(\"kdsoutput\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdakun-filter\"\r\n            checked={kdakun}\r\n            onChange={setKdakun}\r\n            label=\"Akun\"\r\n            disabled={disabledSwitches.includes(\"kdakun\")}\r\n          />\r\n          {/* <FilterSwitch\r\n            id=\"kdkomponen-filter\"\r\n            checked={kdkomponen}\r\n            onChange={setKdkomponen}\r\n            label=\"Komponen\"\r\n          /> */}\r\n          {/* <FilterSwitch\r\n            id=\"kdskomponen-filter\"\r\n            checked={kdskomponen}\r\n            onChange={setKdskomponen}\r\n            label=\"Sub-komponen\"\r\n          /> */}\r\n\r\n          <FilterSwitch\r\n            id=\"kdsdana-filter\"\r\n            checked={kdsdana}\r\n            onChange={setKdsdana}\r\n            label=\"Sumber Dana\"\r\n            disabled={disabledSwitches.includes(\"kdsdana\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kdregister-filter\"\r\n            checked={kdregister}\r\n            onChange={setKdregister}\r\n            label=\"Register\"\r\n            disabled={disabledSwitches.includes(\"kdregister\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"prinas-filter\"\r\n            checked={KdPN}\r\n            onChange={setKdPN}\r\n            label=\"Prioritas Nasional\"\r\n            disabled={disabledSwitches.includes(\"KdPN\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"programpri-filter\"\r\n            checked={KdPP}\r\n            onChange={setKdPP}\r\n            label=\"Program Prioritas\"\r\n            disabled={disabledSwitches.includes(\"KdPP\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kegiatanpri-filter\"\r\n            checked={KdKegPP}\r\n            onChange={setKdKegPP}\r\n            label=\"Kegiatan Prioritas\"\r\n            disabled={disabledSwitches.includes(\"KdKegPP\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"proyek-prioritas-filter\"\r\n            checked={KdPRI}\r\n            onChange={setKdPRI}\r\n            label=\"Proyek Prioritas\"\r\n            disabled={disabledSwitches.includes(\"KdPRI\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"majorpr-filter\"\r\n            checked={KdMP}\r\n            onChange={setKdMP}\r\n            label=\"Major Project\"\r\n            disabled={disabledSwitches.includes(\"KdMP\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"tematik-filter\"\r\n            checked={KdTema}\r\n            onChange={setKdTema}\r\n            label=\"Tematik\"\r\n            disabled={disabledSwitches.includes(\"KdTema\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"inflasi-filter\"\r\n            checked={kdInflasi}\r\n            onChange={setKdInflasi}\r\n            label=\"Inflasi\"\r\n            disabled={disabledSwitches.includes(\"kdInflasi\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"stunting-filter\"\r\n            checked={KdStunting}\r\n            onChange={setKdStunting}\r\n            label=\"Stunting\"\r\n            disabled={disabledSwitches.includes(\"KdStunting\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"kemiskinan-filter\"\r\n            checked={kdKemiskinan}\r\n            onChange={setKdKemiskinan}\r\n            label=\"Kemiskinan Extrem\"\r\n            disabled={disabledSwitches.includes(\"kdKemiskinan\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"pemilu-filter\"\r\n            checked={KdPemilu}\r\n            onChange={setKdPemilu}\r\n            label=\"Pemilu\"\r\n            disabled={disabledSwitches.includes(\"KdPemilu\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"ikn-filter\"\r\n            checked={kdIkn}\r\n            onChange={setKdIkn}\r\n            label=\"IKN\"\r\n            disabled={disabledSwitches.includes(\"kdIkn\")}\r\n          />\r\n          <FilterSwitch\r\n            id=\"pangan-filter\"\r\n            checked={KdPangan}\r\n            onChange={setKdPangan}\r\n            label=\"Ketahanan Pangan\"\r\n            disabled={disabledSwitches.includes(\"KdPangan\")}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filter Components Row - Modern spacing and styling */}\r\n      <div className=\"space-y-4 mb-4\">\r\n        {/* Always show CutoffFilter card, but disable select when switch is OFF */}\r\n        <CutoffFilter inquiryState={inquiryState} />\r\n        {showKementerian && (\r\n          <KementerianFilter\r\n            inquiryState={inquiryState}\r\n            status={showKementerian ? \"pilihdept\" : \"\"}\r\n          />\r\n        )}\r\n        {showUnit && <UnitFilter inquiryState={inquiryState} />}\r\n        {showDekon && <DekonFilter inquiryState={inquiryState} />}\r\n        {showLokasi && <LokasiFilter inquiryState={inquiryState} />}\r\n        {showKabkota && <KabkotaFilter inquiryState={inquiryState} />}\r\n        {showKanwil && <KanwilFilter inquiryState={inquiryState} />}\r\n        {kdkppn && <KppnFilter inquiryState={inquiryState} />}\r\n        {kdsatker && <SatkerFilter inquiryState={inquiryState} />}\r\n        {kdfungsi && <FungsiFilter inquiryState={inquiryState} />}\r\n        {kdsfungsi && <SubfungsiFilter inquiryState={inquiryState} />}\r\n        {kdprogram && <ProgramFilter inquiryState={inquiryState} />}\r\n        {kdgiat && <KegiatanFilter inquiryState={inquiryState} />}\r\n        {kdoutput && <OutputFilter type=\"output\" inquiryState={inquiryState} />}\r\n        {kdsoutput && <SuboutputFilter inquiryState={inquiryState} />}\r\n        {kdakun && <AkunFilter inquiryState={inquiryState} />}\r\n        {kdkomponen && <KomponenFilter inquiryState={inquiryState} />}\r\n        {kdskomponen && <SubkomponenFilter inquiryState={inquiryState} />}\r\n        {kdsdana && (\r\n          <SumberdanaFilter type=\"source\" inquiryState={inquiryState} />\r\n        )}\r\n        {kdregister && (\r\n          <RegisterFilter type=\"register\" inquiryState={inquiryState} />\r\n        )}\r\n        {KdPN && <PrinasFilter inquiryState={inquiryState} />}\r\n        {KdPP && <ProgrampriFilter inquiryState={inquiryState} />}\r\n        {KdKegPP && <KegiatanpriFilter inquiryState={inquiryState} />}\r\n        {KdPRI && <ProyekprioritasFilter inquiryState={inquiryState} />}\r\n        {KdMP && <MajorprFilter inquiryState={inquiryState} />}\r\n        {KdTema && <TematikFilter inquiryState={inquiryState} />}\r\n        {kdInflasi && <InflasiFilter inquiryState={inquiryState} />}\r\n        {KdStunting && <StuntingFilter inquiryState={inquiryState} />}\r\n        {kdKemiskinan && <KemiskinanFilter inquiryState={inquiryState} />}\r\n        {KdPemilu && <PemiluFilter inquiryState={inquiryState} />}\r\n        {kdIkn && <IknFilter inquiryState={inquiryState} />}\r\n        {KdPangan && <PanganFilter inquiryState={inquiryState} />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default FilterSection;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,gBAAgB;QAAC,EAAE,YAAY,EAAE;;IACrC,MAAM,EACJ,+CAA+C;IAC/C,MAAM,EACN,2BAA2B;IAC3B,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,kBAAkB,EAClB,qBAAqB,EACrB,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,OAAO,EACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,WAAW,EACX,UAAU,EACV,aAAa,EACb,MAAM,EACN,SAAS,EACT,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,IAAI,EACJ,OAAO,EAEP,+BAA+B;IAC/B,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,IAAI,EACJ,OAAO,EACP,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,MAAM,EACN,SAAS,EACT,aAAa,EACb,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,EACd,OAAO,EACP,UAAU,EACV,cAAc,EACd,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACnB,aAAa,EACb,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,IAAI,EACJ,OAAO,EACP,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACX,SAAS,EACT,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,eAAe,EACf,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,GAAG,EACH,MAAM,EACN,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,EACV,MAAM,EACN,SAAS,EACT,eAAe,EACf,kBAAkB,EAClB,cAAc,EACd,iBAAiB,EACjB,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,UAAU,EACV,aAAa,EACb,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,EAAE,EACF,KAAK,EACL,OAAO,EACP,UAAU,EACV,EAAE,EACF,KAAK,EACL,OAAO,EACP,UAAU,EACV,iBAAiB,EACjB,oBAAoB,EACpB,sBAAsB,EACtB,yBAAyB,EACzB,EAAE,EACF,KAAK,EACL,OAAO,EACP,UAAU,EACV,IAAI,EACJ,OAAO,EACP,SAAS,EACT,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,EACX,cAAc,EACd,GAAG,EACH,MAAM,EACN,QAAQ,EACR,WAAW,EACZ,GAAG;IAEJ,qFAAqF;IACrF,uEAAuE;IACvE,yDAAyD;IACzD,2DAA2D;IAC3D,+DAA+D;IAC/D,6DAA6D;IAC7D,6DAA6D;IAE7D,wDAAwD;IACxD,MAAM,kBAAkB;IACxB,MAAM,qBAAqB;IAC3B,MAAM,WAAW;IACjB,MAAM,cAAc;IACpB,MAAM,YAAY;IAClB,MAAM,eAAe;IACrB,MAAM,cAAc;IACpB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IACtB,MAAM,aAAa;IACnB,MAAM,gBAAgB;IAEtB,mEAAmE;IACnE,MAAM,sBAAsB,CAAC;QAC3B,8EAA8E;QAC9E,IAAI,eAAe,KAAK;YACtB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;QAEA,wEAAwE;QACxE,MAAM,uBAAuB;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,0EAA0E;QAC1E,IAAI,eAAe,KAAK;YACtB,OAAO;mBAAI;gBAAsB;aAAS;QAC5C;QAEA,mFAAmF;QACnF,OAAO;IACT;IAEA,MAAM,mBAAmB,oBAAoB;IAE7C,oEAAoE;IACpE,+DAA+D;IAC/D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ;YAEb,8EAA8E;YAC9E,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,IAAI,iBAAiB,QAAQ,CAAC,gBAAgB,WAAW;oBACvD,gBAAgB,aAAa;gBAC/B;gBACA,IAAI,iBAAiB,QAAQ,CAAC,WAAW,MAAM;oBAC7C,WAAW,QAAQ;gBACrB;gBACA,IAAI,iBAAiB,QAAQ,CAAC,WAAW,MAAM;oBAC7C,WAAW,QAAQ;gBACrB;gBACA,IAAI,iBAAiB,QAAQ,CAAC,cAAc,SAAS;oBACnD,cAAc,WAAW;gBAC3B;gBACA,IAAI,iBAAiB,QAAQ,CAAC,YAAY,OAAO;oBAC/C,YAAY,SAAS;gBACvB;gBACA,IAAI,iBAAiB,QAAQ,CAAC,WAAW,MAAM;oBAC7C,WAAW,QAAQ;gBACrB;gBACA,IAAI,iBAAiB,QAAQ,CAAC,aAAa,QAAQ;oBACjD,aAAa,UAAU;gBACzB;gBACA,IAAI,iBAAiB,QAAQ,CAAC,gBAAgB,WAAW;oBACvD,gBAAgB,aAAa;gBAC/B;gBACA,IAAI,iBAAiB,QAAQ,CAAC,iBAAiB,YAAY;oBACzD,iBAAiB,cAAc;gBACjC;gBACA,IAAI,iBAAiB,QAAQ,CAAC,mBAAmB,cAAc;oBAC7D,mBAAmB,gBAAgB;gBACrC;gBACA,IAAI,iBAAiB,QAAQ,CAAC,eAAe,UAAU;oBACrD,eAAe,YAAY;gBAC7B;gBACA,IAAI,iBAAiB,QAAQ,CAAC,YAAY,OAAO;oBAC/C,YAAY,SAAS;gBACvB;gBACA,IAAI,iBAAiB,QAAQ,CAAC,eAAe,UAAU;oBACrD,eAAe,YAAY;gBAC7B;gBACA,IAAI,iBAAiB,QAAQ,CAAC,aAAa,WAAW,KAAK;oBACzD,aAAa,UAAU;oBACvB,yBAAyB,sBAAsB;gBACjD;YACF;QACF;kCAAG;QAAC;KAAO,GAAG,uDAAuD;IAErE,mDAAmD;IACnD,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ;gBACX,iCAAiC;gBACjC,WAAW,QAAQ,QAAQ,qBAAqB;gBAChD,kBAAkB,eAAe;gBACjC,eAAe,YAAY;gBAC3B,gBAAgB,aAAa;YAC/B;QACF;kCAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAa;IAE/D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,MAAM;gBACT,0BAA0B;gBAC1B,aAAa,UAAU;gBACvB,kBAAkB,eAAe;gBACjC,eAAe,YAAY;gBAC3B,gBAAgB,aAAa;YAC/B;QACF;kCAAG;QAAC;QAAM;QAAW;QAAgB;QAAa;KAAa;IAE/D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,SAAS;gBACZ,2BAA2B;gBAC3B,YAAY,SAAS;gBACrB,mBAAmB,gBAAgB;gBACnC,gBAAgB,aAAa;gBAC7B,iBAAiB,cAAc;YACjC;QACF;kCAAG;QAAC;QAAS;QAAU;QAAiB;QAAc;KAAc;IAEpE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,8BAA8B;gBAC9B,WAAW,QAAQ;gBACnB,oBAAoB,iBAAiB;gBACrC,iBAAiB,cAAc;gBAC/B,eAAe,YAAY;YAC7B;QACF;kCAAG;QAAC;QAAU;QAAS;QAAkB;QAAe;KAAY;IAEpE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,WAAW;gBACd,6BAA6B;gBAC7B,cAAc,WAAW;gBACzB,qBAAqB,kBAAkB;gBACvC,kBAAkB,eAAe;gBACjC,mBAAmB,gBAAgB;YACrC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,4BAA4B;gBAC5B,aAAa,UAAU;gBACvB,oBAAoB,iBAAiB;gBACrC,iBAAiB,cAAc;gBAC/B,kBAAkB,eAAe;YACnC;QACF;kCAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ;gBACX,0BAA0B;gBAC1B,WAAW,QAAQ;gBACnB,kBAAkB,eAAe;gBACjC,eAAe,YAAY;gBAC3B,gBAAgB,aAAa;YAC/B;QACF;kCAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAa;IAE/D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,4BAA4B;gBAC5B,aAAa,UAAU;gBACvB,oBAAoB,iBAAiB;gBACrC,iBAAiB,cAAc;gBAC/B,kBAAkB,eAAe;YACnC;QACF;kCAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,4BAA4B;gBAC5B,aAAa,UAAU;gBACvB,oBAAoB,iBAAiB;gBACrC,iBAAiB,cAAc;gBAC/B,kBAAkB,eAAe;YACnC;QACF;kCAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,WAAW;gBACd,gCAAgC;gBAChC,cAAc,WAAW;gBACzB,uBAAuB,oBAAoB;gBAC3C,oBAAoB,iBAAiB;gBACrC,qBAAqB,kBAAkB;YACzC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,WAAW;gBACd,6BAA6B;gBAC7B,cAAc,WAAW;gBACzB,qBAAqB,kBAAkB;gBACvC,kBAAkB,eAAe;gBACjC,mBAAmB,gBAAgB;YACrC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ;gBACX,8BAA8B;gBAC9B,WAAW,QAAQ;gBACnB,kBAAkB,eAAe;gBACjC,eAAe,YAAY;gBAC3B,oBAAoB,iBAAiB;YACvC;QACF;kCAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAiB;IAEnE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,4BAA4B;gBAC5B,aAAa,UAAU;gBACvB,oBAAoB,iBAAiB;gBACrC,iBAAiB,cAAc;gBAC/B,kBAAkB,eAAe;YACnC;QACF;kCAAG;QAAC;QAAU;QAAW;QAAkB;QAAe;KAAe;IAEzE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,WAAW;gBACd,gCAAgC;gBAChC,cAAc,WAAW;gBACzB,qBAAqB,kBAAkB;gBACvC,kBAAkB,eAAe;gBACjC,mBAAmB,gBAAgB;YACrC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,YAAY;gBACf,8BAA8B;gBAC9B,eAAe,YAAY;gBAC3B,sBAAsB,mBAAmB;gBACzC,mBAAmB,gBAAgB;gBACnC,oBAAoB,iBAAiB;YACvC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,aAAa;gBAChB,kCAAkC;gBAClC,gBAAgB,aAAa;gBAC7B,uBAAuB,oBAAoB;gBAC3C,oBAAoB,iBAAiB;gBACrC,qBAAqB,kBAAkB;YACzC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ;gBACX,0BAA0B;gBAC1B,WAAW,QAAQ;gBACnB,kBAAkB,eAAe;gBACjC,eAAe,YAAY;gBAC3B,gBAAgB,aAAa;YAC/B;QACF;kCAAG;QAAC;QAAQ;QAAS;QAAgB;QAAa;KAAa;IAE/D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,SAAS;gBACZ,iCAAiC;gBACjC,YAAY,SAAS;gBACrB,mBAAmB,gBAAgB;gBACnC,gBAAgB,aAAa;gBAC7B,iBAAiB,cAAc;YACjC;QACF;kCAAG;QAAC;QAAS;QAAU;QAAiB;QAAc;KAAc;IAEpE,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,YAAY;gBACf,8BAA8B;gBAC9B,eAAe,YAAY;gBAC3B,sBAAsB,mBAAmB;gBACzC,mBAAmB,gBAAgB;gBACnC,oBAAoB,iBAAiB;YACvC;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,WAAW;gBACd,6BAA6B;gBAC7B,cAAc,WAAW;gBACzB,mBAAmB,gBAAgB;YACrC;QACF;kCAAG;QAAC;QAAW;QAAY;KAAgB;IAE3C,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,OAAO;gBACV,yBAAyB;gBACzB,UAAU,OAAO;gBACjB,eAAe,YAAY;YAC7B;QACF;kCAAG;QAAC;QAAO;QAAQ;KAAY;IAE/B,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,cAAc;gBACjB,gCAAgC;gBAChC,aAAa,UAAU;gBACvB,sBAAsB,mBAAmB;YAC3C;QACF;kCAAG;QAAC;QAAc;QAAW;KAAmB;IAEhD,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,4BAA4B;gBAC5B,aAAa,UAAU;gBACvB,kBAAkB,eAAe;YACnC;QACF;kCAAG;QAAC;QAAU;QAAW;KAAe;IAExC,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,YAAY;gBACf,8BAA8B;gBAC9B,eAAe,YAAY;gBAC3B,oBAAoB,iBAAiB;YACvC;QACF;kCAAG;QAAC;QAAY;QAAa;KAAiB;IAE9C,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,MAAM;gBACT,4BAA4B;gBAC5B,SAAS,MAAM;gBACf,cAAc,WAAW;YAC3B;QACF;kCAAG;QAAC;QAAM;QAAO;KAAW;IAE5B,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,MAAM;gBACT,gCAAgC;gBAChC,SAAS,MAAM;gBACf,cAAc,WAAW;YAC3B;QACF;kCAAG;QAAC;QAAM;QAAO;KAAW;IAE5B,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,SAAS;gBACZ,iCAAiC;gBACjC,wBAAwB,qBAAqB;gBAC7C,6BAA6B,0BAA0B;YACzD;QACF;kCAAG;QAAC;QAAS;QAAsB;KAA0B;IAE7D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,MAAM;gBACT,6BAA6B;gBAC7B,SAAS,MAAM;gBACf,cAAc,WAAW;YAC3B;QACF;kCAAG;QAAC;QAAM;QAAO;KAAW;IAE5B,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ;gBACX,6BAA6B;gBAC7B,WAAW,QAAQ;gBACnB,gBAAgB,aAAa;YAC/B;QACF;kCAAG;QAAC;QAAQ;QAAS;KAAa;IAElC,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,UAAU;gBACb,4BAA4B;gBAC5B,aAAa,UAAU;gBACvB,kBAAkB,eAAe;YACnC;QACF;kCAAG;QAAC;QAAU;QAAW;KAAe;IAExC,4DAA4D;IAC5D,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,CAAC,QAAQ,QAAQ,yCAAyC;YAE9D,+CAA+C;YAC/C,MAAM;8DAAqB,CAAC;oBAC1B,6EAA6E;oBAC7E,MAAM,aAAa;wBACjB,QAAQ;wBACR,MAAM;wBACN,SAAS;wBACT,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,QAAQ;wBACR,UAAU;wBACV,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,YAAY;wBACZ,MAAM;wBACN,MAAM;wBACN,SAAS;wBACT,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,UAAU;wBACV,OAAO;wBACP,UAAU;oBACZ;oBAEA,0CAA0C;oBAC1C,wFAAwF;oBACxF,6EAA6E;oBAC7E,OAAO;gBACT;;YAEA,MAAM,kBAAkB,mBAAmB;YAE3C,6BAA6B;YAC7B,aAAa,UAAU,gBAAgB,MAAM;YAC7C,WAAW,QAAQ,gBAAgB,IAAI;YACvC,cAAc,WAAW,gBAAgB,OAAO;YAChD,eAAe,YAAY,gBAAgB,QAAQ;YACnD,gBAAgB,aAAa,gBAAgB,SAAS;YACtD,eAAe,YAAY,gBAAgB,QAAQ;YACnD,aAAa,UAAU,gBAAgB,MAAM;YAC7C,eAAe,YAAY,gBAAgB,QAAQ;YACnD,eAAe,YAAY,gBAAgB,QAAQ;YACnD,gBAAgB,aAAa,gBAAgB,SAAS;YACtD,gBAAgB,aAAa,gBAAgB,SAAS;YACtD,aAAa,UAAU,gBAAgB,MAAM;YAC7C,eAAe,YAAY,gBAAgB,QAAQ;YACnD,gBAAgB,aAAa,gBAAgB,SAAS;YACtD,aAAa,UAAU,gBAAgB,MAAM;YAC7C,cAAc,WAAW,gBAAgB,OAAO;YAChD,iBAAiB,cAAc,gBAAgB,UAAU;YACzD,WAAW,QAAQ,gBAAgB,IAAI;YACvC,WAAW,QAAQ,gBAAgB,IAAI;YACvC,cAAc,WAAW,gBAAgB,OAAO;YAChD,YAAY,SAAS,gBAAgB,KAAK;YAC1C,WAAW,QAAQ,gBAAgB,IAAI;YACvC,aAAa,UAAU,gBAAgB,MAAM;YAC7C,gBAAgB,aAAa,gBAAgB,SAAS;YACtD,iBAAiB,cAAc,gBAAgB,UAAU;YACzD,mBAAmB,gBAAgB,gBAAgB,YAAY;YAC/D,eAAe,YAAY,gBAAgB,QAAQ;YACnD,YAAY,SAAS,gBAAgB,KAAK;YAC1C,eAAe,YAAY,gBAAgB,QAAQ;QACrD;kCAAG;QAAC;KAAO,GAAG,+BAA+B;IAE7C,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BAMb,cAAA,6LAAC;oBAAI,WAAU;;sCAOb,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS,WAAW;4BACpB,UAAU,CAAC;gCACT,IAAI,KAAK;oCACP,iCAAiC;oCACjC,UAAU;gCACZ,OAAO;oCACL,6CAA6C;oCAC7C,UAAU;gCACZ;gCACA,sBAAsB;4BACxB;4BACA,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAQtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS,QAAQ;4BACjB,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAGR,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;;;;;;sCAER,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAetC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;sCAEtC,6LAAC,oLAAA,CAAA,UAAY;4BACX,IAAG;4BACH,SAAS;4BACT,UAAU;4BACV,OAAM;4BACN,UAAU,iBAAiB,QAAQ,CAAC;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC3B,iCACC,6LAAC,yMAAA,CAAA,UAAiB;wBAChB,cAAc;wBACd,QAAQ,kBAAkB,cAAc;;;;;;oBAG3C,0BAAY,6LAAC,kMAAA,CAAA,UAAU;wBAAC,cAAc;;;;;;oBACtC,2BAAa,6LAAC,mMAAA,CAAA,UAAW;wBAAC,cAAc;;;;;;oBACxC,4BAAc,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC1C,6BAAe,6LAAC,qMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBAC5C,4BAAc,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBAC1C,wBAAU,6LAAC,kMAAA,CAAA,UAAU;wBAAC,cAAc;;;;;;oBACpC,0BAAY,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACxC,0BAAY,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACxC,2BAAa,6LAAC,uMAAA,CAAA,UAAe;wBAAC,cAAc;;;;;;oBAC5C,2BAAa,6LAAC,qMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBAC1C,wBAAU,6LAAC,sMAAA,CAAA,UAAc;wBAAC,cAAc;;;;;;oBACxC,0BAAY,6LAAC,oMAAA,CAAA,UAAY;wBAAC,MAAK;wBAAS,cAAc;;;;;;oBACtD,2BAAa,6LAAC,uMAAA,CAAA,UAAe;wBAAC,cAAc;;;;;;oBAC5C,wBAAU,6LAAC,kMAAA,CAAA,UAAU;wBAAC,cAAc;;;;;;oBACpC,4BAAc,6LAAC,sMAAA,CAAA,UAAc;wBAAC,cAAc;;;;;;oBAC5C,6BAAe,6LAAC,yMAAA,CAAA,UAAiB;wBAAC,cAAc;;;;;;oBAChD,yBACC,6LAAC,wMAAA,CAAA,UAAgB;wBAAC,MAAK;wBAAS,cAAc;;;;;;oBAE/C,4BACC,6LAAC,sMAAA,CAAA,UAAc;wBAAC,MAAK;wBAAW,cAAc;;;;;;oBAE/C,sBAAQ,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACpC,sBAAQ,6LAAC,wMAAA,CAAA,UAAgB;wBAAC,cAAc;;;;;;oBACxC,yBAAW,6LAAC,yMAAA,CAAA,UAAiB;wBAAC,cAAc;;;;;;oBAC5C,uBAAS,6LAAC,uMAAA,CAAA,UAAqB;wBAAC,cAAc;;;;;;oBAC9C,sBAAQ,6LAAC,qMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBACrC,wBAAU,6LAAC,qMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBACvC,2BAAa,6LAAC,qMAAA,CAAA,UAAa;wBAAC,cAAc;;;;;;oBAC1C,4BAAc,6LAAC,sMAAA,CAAA,UAAc;wBAAC,cAAc;;;;;;oBAC5C,8BAAgB,6LAAC,wMAAA,CAAA,UAAgB;wBAAC,cAAc;;;;;;oBAChD,0BAAY,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;oBACxC,uBAAS,6LAAC,iMAAA,CAAA,UAAS;wBAAC,cAAc;;;;;;oBAClC,0BAAY,6LAAC,oMAAA,CAAA,UAAY;wBAAC,cAAc;;;;;;;;;;;;;;AAIjD;GAxkCM;KAAA;uCA0kCS", "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/QueryButtons.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Button, ButtonGroup, Card, CardBody } from \"@heroui/react\";\r\nimport {\r\n  Play,\r\n  Download,\r\n  RefreshCw,\r\n  FileText,\r\n  Save,\r\n  MessageCircleHeart,\r\n} from \"lucide-react\";\r\n\r\nconst QueryButtons = ({\r\n  onExecuteQuery,\r\n  onExportExcel,\r\n  onExportCSV,\r\n  onExportPDF,\r\n  onReset,\r\n  onSaveQuery,\r\n  onShowSQL,\r\n  isLoading,\r\n}) => {\r\n  return (\r\n    <Card className=\"mb-4 shadow-none bg-transparent\">\r\n      <CardBody>\r\n        <div className=\"flex flex-wrap gap-6 justify-center md:justify-center\">\r\n          <Button\r\n            color=\"primary\"\r\n            startContent={<Play size={16} />}\r\n            onClick={onExecuteQuery}\r\n            isLoading={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Tayang Data\r\n          </Button>\r\n          <Button\r\n            color=\"danger\"\r\n            variant=\"ghost\"\r\n            startContent={<RefreshCw size={16} />}\r\n            onClick={onReset}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Reset Filter\r\n          </Button>\r\n          <ButtonGroup>\r\n            <Button\r\n              color=\"success\"\r\n              variant=\"flat\"\r\n              startContent={<Download size={16} />}\r\n              onClick={onExportExcel}\r\n              isDisabled={isLoading}\r\n              className=\"w-[120px] h-[50px]\"\r\n            >\r\n              Excel\r\n            </Button>\r\n\r\n            <Button\r\n              color=\"secondary\"\r\n              variant=\"flat\"\r\n              startContent={<Download size={16} />}\r\n              onClick={onExportCSV}\r\n              isDisabled={isLoading}\r\n              className=\"w-[120px] h-[50px]\"\r\n            >\r\n              CSV\r\n            </Button>\r\n          </ButtonGroup>\r\n          <Button\r\n            color=\"success\"\r\n            variant=\"flat\"\r\n            startContent={<MessageCircleHeart size={16} />}\r\n            onClick={onExportPDF}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Kirim WA\r\n          </Button>\r\n\r\n          <Button\r\n            color=\"warning\"\r\n            variant=\"flat\"\r\n            startContent={<Save size={16} />}\r\n            onClick={onSaveQuery}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Simpan Query\r\n          </Button>\r\n\r\n          <Button\r\n            color=\"default\"\r\n            variant=\"flat\"\r\n            startContent={<FileText size={16} />} // You can use a different icon if desired\r\n            onClick={onShowSQL}\r\n            isDisabled={isLoading}\r\n            className=\"w-[160px] h-[50px]\"\r\n          >\r\n            Tayang SQL\r\n          </Button>\r\n        </div>\r\n      </CardBody>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default QueryButtons;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AASA,MAAM,eAAe;QAAC,EACpB,cAAc,EACd,aAAa,EACb,WAAW,EACX,WAAW,EACX,OAAO,EACP,WAAW,EACX,SAAS,EACT,SAAS,EACV;IACC,qBACE,6LAAC,yMAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;sBACP,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,4BAAc,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAC1B,SAAS;wBACT,WAAW;wBACX,WAAU;kCACX;;;;;;kCAGD,6LAAC,+MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,6LAAC,mNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;wBAC/B,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;kCAGD,6LAAC,0NAAA,CAAA,cAAW;;0CACV,6LAAC,+MAAA,CAAA,SAAM;gCACL,OAAM;gCACN,SAAQ;gCACR,4BAAc,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;gCAC9B,SAAS;gCACT,YAAY;gCACZ,WAAU;0CACX;;;;;;0CAID,6LAAC,+MAAA,CAAA,SAAM;gCACL,OAAM;gCACN,SAAQ;gCACR,4BAAc,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;gCAC9B,SAAS;gCACT,YAAY;gCACZ,WAAU;0CACX;;;;;;;;;;;;kCAIH,6LAAC,+MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,6LAAC,yOAAA,CAAA,qBAAkB;4BAAC,MAAM;;;;;;wBACxC,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;kCAID,6LAAC,+MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAC1B,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;kCAID,6LAAC,+MAAA,CAAA,SAAM;wBACL,OAAM;wBACN,SAAQ;wBACR,4BAAc,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;wBAC9B,SAAS;wBACT,YAAY;wBACZ,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;KA5FM;uCA8FS", "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/inquiry/shared/components/LaporanSelector.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card, CardBody, Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst ReportTypeSelector = ({ inquiryState, onFilterChange }) => {\r\n  // --- DESTRUCTURE STATE FROM INQUIRY STATE ---\r\n  const {\r\n    thang,\r\n    setThang,\r\n    jenlap,\r\n    setJenlap,\r\n    pembulatan,\r\n    setPembulatan,\r\n    akumulatif,\r\n    setAkumulatif,\r\n  } = inquiryState || {};\r\n\r\n  // --- FALLBACK STATE FOR BACKWARDS COMPATIBILITY ---\r\n  const [localThang, setLocalThang] = React.useState(\"2025\");\r\n  const [localJenlap, setLocalJenlap] = React.useState(\"2\");\r\n  const [localPembulatan, setLocalPembulatan] = React.useState(\"1\");\r\n  const [localAkumulatif, setLocalAkumulatif] = React.useState(\"0\");\r\n\r\n  // Use inquiryState values if available, otherwise use local state\r\n  const currentThang =\r\n    thang !== undefined && thang !== null ? thang : localThang;\r\n  const currentJenlap =\r\n    jenlap !== undefined && jenlap !== null ? jenlap : localJenlap;\r\n  const currentPembulatan =\r\n    pembulatan !== undefined && pembulatan !== null\r\n      ? pembulatan\r\n      : localPembulatan;\r\n  const currentAkumulatif =\r\n    akumulatif !== undefined && akumulatif !== null\r\n      ? akumulatif\r\n      : localAkumulatif;\r\n\r\n  const currentSetThang = setThang || setLocalThang;\r\n  const currentSetJenlap = setJenlap || setLocalJenlap;\r\n  const currentSetPembulatan = setPembulatan || setLocalPembulatan;\r\n  const currentSetAkumulatif = setAkumulatif || setLocalAkumulatif;\r\n\r\n  // Debug akumulatif value changes\r\n  React.useEffect(() => {\r\n    console.log(\r\n      \"LaporanSelector - currentAkumulatif changed to:\",\r\n      currentAkumulatif\r\n    );\r\n  }, [currentAkumulatif]);\r\n\r\n  // This effect reports changes up to the parent component if callback is provided\r\n  React.useEffect(() => {\r\n    if (onFilterChange) {\r\n      onFilterChange({\r\n        thang: currentThang,\r\n        jenlap: currentJenlap,\r\n        pembulatan: currentPembulatan,\r\n        akumulatif: currentAkumulatif,\r\n      });\r\n    }\r\n  }, [\r\n    currentThang,\r\n    currentJenlap,\r\n    currentPembulatan,\r\n    currentAkumulatif,\r\n    onFilterChange,\r\n  ]);\r\n\r\n  // --- LOGIC FOR CONDITIONAL \"AKUMULATIF\" ---\r\n  const isAkumulatifActive = currentJenlap === \"3\";\r\n\r\n  React.useEffect(() => {\r\n    console.log(\"LaporanSelector - akumulatif useEffect triggered:\", {\r\n      currentJenlap,\r\n      isAkumulatifActive,\r\n      currentAkumulatif,\r\n    });\r\n\r\n    if (!isAkumulatifActive) {\r\n      // When akumulatif is not active (jenlap !== \"3\"), set to \"0\" (Non-Akumulatif)\r\n\r\n      currentSetAkumulatif(\"0\");\r\n    } else {\r\n      // When akumulatif becomes active (jenlap === \"3\"), ensure it has a valid value\r\n      // If currentAkumulatif is empty or undefined, default to \"0\" (Non-Akumulatif)\r\n      if (\r\n        !currentAkumulatif ||\r\n        (currentAkumulatif !== \"0\" && currentAkumulatif !== \"1\")\r\n      ) {\r\n        currentSetAkumulatif(\"0\");\r\n      } else {\r\n      }\r\n    }\r\n  }, [currentJenlap, isAkumulatifActive]); // Remove currentSetAkumulatif from dependencies\r\n\r\n  // Available years for selection\r\n  const Tahun = [\r\n    \"2025\",\r\n    \"2024\",\r\n    \"2023\",\r\n    \"2022\",\r\n    \"2021\",\r\n    \"2020\",\r\n    \"2019\",\r\n    \"2018\",\r\n    \"2017\",\r\n    \"2016\",\r\n  ];\r\n\r\n  // Report types (match old form)\r\n  const jenlapOpt = [\r\n    { value: \"1\", label: \"Pagu APBN\" },\r\n    { value: \"2\", label: \"Pagu Realisasi\" },\r\n    { value: \"3\", label: \"Pagu Realisasi Bulanan\" },\r\n    { value: \"4\", label: \"Pergerakan Pagu Bulanan\" },\r\n    { value: \"5\", label: \"Pergerakan Blokir Bulanan\" },\r\n    { value: \"7\", label: \"Pergerakan Blokir Bulanan per Jenis\" },\r\n    { value: \"6\", label: \"Volume Output Kegiatan (PN) - Data Caput\" },\r\n  ];\r\n\r\n  // Akumulatif options\r\n  const akumulatifOpt = [\r\n    { value: \"1\", label: \"Akumulatif\" },\r\n    { value: \"0\", label: \"Non-Akumulatif\" },\r\n  ];\r\n\r\n  // Rounding options\r\n  const pembulatanOpt = [\r\n    { value: \"1\", label: \"Rupiah\" },\r\n    { value: \"1000\", label: \"Ribuan\" },\r\n    { value: \"1000000\", label: \"Jutaan\" },\r\n    { value: \"1000000000\", label: \"Miliar\" },\r\n    { value: \"1000000000000\", label: \"Triliun\" },\r\n  ];\r\n\r\n  const handleSelectionChange = (setter) => (keys) => {\r\n    const value = Array.from(keys)[0];\r\n\r\n    if (setter && value !== undefined) setter(value);\r\n  };\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <div className=\"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-sky-100 to-teal-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl\">\r\n        {/* <h5 className=\"text-lg font-semibold mb-4\">Report Settings</h5> */}\r\n\r\n        <div className=\"flex flex-col md:flex-row gap-6 w-full\">\r\n          {/* Year Selection */}\r\n          <div className=\"flex-1\">\r\n            <label id=\"thang-label\" className=\"block text-sm font-medium mb-2\">\r\n              Tahun Anggaran\r\n            </label>\r\n            <Select\r\n              selectedKeys={[currentThang]}\r\n              onSelectionChange={handleSelectionChange(currentSetThang)}\r\n              className=\"w-full\"\r\n              placeholder=\"Pilih Tahun\"\r\n              disallowEmptySelection // Prevent unselecting\r\n              aria-labelledby=\"thang-label\"\r\n              aria-label=\"Pilih Tahun Anggaran\"\r\n            >\r\n              {Tahun.map((year) => (\r\n                <SelectItem key={year} textValue={year}>\r\n                  {year}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Report Type Selection */}\r\n          <div className=\"flex-[1.5]\">\r\n            <label id=\"jenlap-label\" className=\"block text-sm font-medium mb-2\">\r\n              Jenis Laporan\r\n            </label>\r\n            <Select\r\n              selectedKeys={[currentJenlap]}\r\n              onSelectionChange={handleSelectionChange(currentSetJenlap)}\r\n              className=\"w-full\"\r\n              placeholder=\"Pilih Jenis Laporan\"\r\n              disallowEmptySelection // Prevent unselecting\r\n              aria-labelledby=\"jenlap-label\"\r\n              aria-label=\"Pilih Jenis Laporan\"\r\n            >\r\n              {jenlapOpt.map((type) => (\r\n                <SelectItem key={type.value} textValue={type.label}>\r\n                  {type.label}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Akumulatif Selection */}\r\n          <div className=\"flex-[0.5] min-w-[120px]\">\r\n            <label\r\n              id=\"akumulatif-label\"\r\n              className=\"block text-sm font-medium mb-2\"\r\n            >\r\n              Akumulatif\r\n            </label>\r\n            <Select\r\n              selectedKeys={isAkumulatifActive ? [currentAkumulatif] : []}\r\n              onSelectionChange={handleSelectionChange(currentSetAkumulatif)}\r\n              className=\"w-full\"\r\n              placeholder={isAkumulatifActive ? \"Pilih Akumulatif\" : \"Disabled\"}\r\n              isDisabled={!isAkumulatifActive}\r\n              disallowEmptySelection // This is now always true when active\r\n              aria-labelledby=\"akumulatif-label\"\r\n              aria-label=\"Pilih Akumulatif\"\r\n            >\r\n              {akumulatifOpt.map((option) => (\r\n                <SelectItem key={option.value} textValue={option.label}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Rounding Options as Dropdown */}\r\n          <div className=\"flex-1\">\r\n            <label\r\n              id=\"pembulatan-label\"\r\n              className=\"block text-sm font-medium mb-2\"\r\n            >\r\n              Pembulatan\r\n            </label>\r\n            <Select\r\n              selectedKeys={[currentPembulatan]}\r\n              onSelectionChange={handleSelectionChange(currentSetPembulatan)}\r\n              className=\"w-full\"\r\n              placeholder=\"Pilih Pembulatan\"\r\n              disallowEmptySelection // Prevent unselecting\r\n              aria-labelledby=\"pembulatan-label\"\r\n              aria-label=\"Pilih Pembulatan\"\r\n            >\r\n              {pembulatanOpt.map((option) => (\r\n                <SelectItem key={option.value} textValue={option.label}>\r\n                  {option.label}\r\n                </SelectItem>\r\n              ))}\r\n            </Select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportTypeSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;AAEA,MAAM,qBAAqB;QAAC,EAAE,YAAY,EAAE,cAAc,EAAE;;IAC1D,+CAA+C;IAC/C,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,MAAM,EACN,SAAS,EACT,UAAU,EACV,aAAa,EACb,UAAU,EACV,aAAa,EACd,GAAG,gBAAgB,CAAC;IAErB,qDAAqD;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7D,kEAAkE;IAClE,MAAM,eACJ,UAAU,aAAa,UAAU,OAAO,QAAQ;IAClD,MAAM,gBACJ,WAAW,aAAa,WAAW,OAAO,SAAS;IACrD,MAAM,oBACJ,eAAe,aAAa,eAAe,OACvC,aACA;IACN,MAAM,oBACJ,eAAe,aAAa,eAAe,OACvC,aACA;IAEN,MAAM,kBAAkB,YAAY;IACpC,MAAM,mBAAmB,aAAa;IACtC,MAAM,uBAAuB,iBAAiB;IAC9C,MAAM,uBAAuB,iBAAiB;IAE9C,iCAAiC;IACjC,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,QAAQ,GAAG,CACT,mDACA;QAEJ;uCAAG;QAAC;KAAkB;IAEtB,iFAAiF;IACjF,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,IAAI,gBAAgB;gBAClB,eAAe;oBACb,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,YAAY;gBACd;YACF;QACF;uCAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,6CAA6C;IAC7C,MAAM,qBAAqB,kBAAkB;IAE7C,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,QAAQ,GAAG,CAAC,qDAAqD;gBAC/D;gBACA;gBACA;YACF;YAEA,IAAI,CAAC,oBAAoB;gBACvB,8EAA8E;gBAE9E,qBAAqB;YACvB,OAAO;gBACL,+EAA+E;gBAC/E,8EAA8E;gBAC9E,IACE,CAAC,qBACA,sBAAsB,OAAO,sBAAsB,KACpD;oBACA,qBAAqB;gBACvB,OAAO,CACP;YACF;QACF;uCAAG;QAAC;QAAe;KAAmB,GAAG,gDAAgD;IAEzF,gCAAgC;IAChC,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gCAAgC;IAChC,MAAM,YAAY;QAChB;YAAE,OAAO;YAAK,OAAO;QAAY;QACjC;YAAE,OAAO;YAAK,OAAO;QAAiB;QACtC;YAAE,OAAO;YAAK,OAAO;QAAyB;QAC9C;YAAE,OAAO;YAAK,OAAO;QAA0B;QAC/C;YAAE,OAAO;YAAK,OAAO;QAA4B;QACjD;YAAE,OAAO;YAAK,OAAO;QAAsC;QAC3D;YAAE,OAAO;YAAK,OAAO;QAA2C;KACjE;IAED,qBAAqB;IACrB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAa;QAClC;YAAE,OAAO;YAAK,OAAO;QAAiB;KACvC;IAED,mBAAmB;IACnB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAK,OAAO;QAAS;QAC9B;YAAE,OAAO;YAAQ,OAAO;QAAS;QACjC;YAAE,OAAO;YAAW,OAAO;QAAS;QACpC;YAAE,OAAO;YAAc,OAAO;QAAS;QACvC;YAAE,OAAO;YAAiB,OAAO;QAAU;KAC5C;IAED,MAAM,wBAAwB,CAAC,SAAW,CAAC;YACzC,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YAEjC,IAAI,UAAU,UAAU,WAAW,OAAO;QAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAGb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,IAAG;gCAAc,WAAU;0CAAiC;;;;;;0CAGnE,6LAAC,+MAAA,CAAA,SAAM;gCACL,cAAc;oCAAC;iCAAa;gCAC5B,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAY;gCACZ,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,+NAAA,CAAA,aAAU;wCAAY,WAAW;kDAC/B;uCADc;;;;;;;;;;;;;;;;kCAQvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,IAAG;gCAAe,WAAU;0CAAiC;;;;;;0CAGpE,6LAAC,+MAAA,CAAA,SAAM;gCACL,cAAc;oCAAC;iCAAc;gCAC7B,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAY;gCACZ,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,+NAAA,CAAA,aAAU;wCAAkB,WAAW,KAAK,KAAK;kDAC/C,KAAK,KAAK;uCADI,KAAK,KAAK;;;;;;;;;;;;;;;;kCAQjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,IAAG;gCACH,WAAU;0CACX;;;;;;0CAGD,6LAAC,+MAAA,CAAA,SAAM;gCACL,cAAc,qBAAqB;oCAAC;iCAAkB,GAAG,EAAE;gCAC3D,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAa,qBAAqB,qBAAqB;gCACvD,YAAY,CAAC;gCACb,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,+NAAA,CAAA,aAAU;wCAAoB,WAAW,OAAO,KAAK;kDACnD,OAAO,KAAK;uCADE,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,IAAG;gCACH,WAAU;0CACX;;;;;;0CAGD,6LAAC,+MAAA,CAAA,SAAM;gCACL,cAAc;oCAAC;iCAAkB;gCACjC,mBAAmB,sBAAsB;gCACzC,WAAU;gCACV,aAAY;gCACZ,sBAAsB;gCACtB,mBAAgB;gCAChB,cAAW;0CAEV,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,+NAAA,CAAA,aAAU;wCAAoB,WAAW,OAAO,KAAK;kDACnD,OAAO,KAAK;uCADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C;GAjPM;KAAA;uCAmPS", "debugId": null}}]}