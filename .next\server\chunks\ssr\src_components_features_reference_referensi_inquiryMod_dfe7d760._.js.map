{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kddept.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kddept.json\";\r\n\r\nconst Kddept = (props) => {\r\n  // Destructure the new popoverClassName prop to separate it from other props\r\n  const { popoverClassName, triggerClassName, isDisabled, ...otherProps } =\r\n    props;\r\n\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"000\";\r\n    if (props.onChange) props.onChange(val);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      isVirtualized\r\n      selectedKeys={[props.value || \"000\"]}\r\n      onSelectionChange={handleSelectionChange}\r\n      size={props.size || \"sm\"}\r\n      className={props.className || \"w-full min-w-0 max-w-full\"}\r\n      disallowEmptySelection\r\n      isDisabled={isDisabled}\r\n      aria-label=\"Pilih Kementerian\"\r\n      placeholder=\"Pilih Kementerian\"\r\n      classNames={{\r\n        // Apply the custom class from props for the popover's content area.\r\n        // If no class is provided, it defaults to a reasonable responsive width.\r\n        popoverContent: popoverClassName || \"w-80 sm:w-96\",\r\n        // Make trigger responsive - full width by default, can be overridden\r\n        trigger: `${triggerClassName || \"w-full\"} max-w-full`,\r\n        // Fix the value display to prevent stretching when long text is selected\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        // Ensure the main input area doesn't expand\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        // Additional constraint on the base element\r\n        base: \"w-full max-w-full\",\r\n        // Ensure the label area doesn't expand\r\n        label: \"truncate\",\r\n      }}\r\n    >\r\n      <SelectItem key=\"000\" textValue=\"Semua Kementerian\">\r\n        Semua Kementerian\r\n      </SelectItem>\r\n      {data.map((kl) => (\r\n        <SelectItem key={kl.kddept} textValue={`${kl.kddept} - ${kl.nmdept}`}>\r\n          {/* This span remains to apply ellipsis if the text overflows the container width */}\r\n          <span className=\"block whitespace-nowrap overflow-hidden text-ellipsis\">\r\n            {kl.kddept} - {kl.nmdept}\r\n          </span>\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kddept;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,SAAS,CAAC;IACd,4EAA4E;IAC5E,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,GAAG,YAAY,GACrE;IAEF,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC;IACrC;IAEA,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,aAAa;QACb,cAAc;YAAC,MAAM,KAAK,IAAI;SAAM;QACpC,mBAAmB;QACnB,MAAM,MAAM,IAAI,IAAI;QACpB,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,YAAY;QACZ,cAAW;QACX,aAAY;QACZ,YAAY;YACV,oEAAoE;YACpE,yEAAyE;YACzE,gBAAgB,oBAAoB;YACpC,qEAAqE;YACrE,SAAS,GAAG,oBAAoB,SAAS,WAAW,CAAC;YACrD,yEAAyE;YACzE,OAAO;YACP,4CAA4C;YAC5C,aAAa;YACb,cAAc;YACd,4CAA4C;YAC5C,MAAM;YACN,uCAAuC;YACvC,OAAO;QACT;;0BAEA,8OAAC,4NAAA,CAAA,aAAU;gBAAW,WAAU;0BAAoB;eAApC;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,mBACT,8OAAC,4NAAA,CAAA,aAAU;oBAAiB,WAAW,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,EAAE;8BAElE,cAAA,8OAAC;wBAAK,WAAU;;4BACb,GAAG,MAAM;4BAAC;4BAAI,GAAG,MAAM;;;;;;;mBAHX,GAAG,MAAM;;;;;;;;;;;AASlC;uCAEe", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdunit.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdunit.json\";\r\n\r\nconst Kdunit = (props) => {\r\n  // Destructure props for easier access and clarity\r\n  const {\r\n    value,\r\n    onChange,\r\n    status,\r\n    size,\r\n    placeholder,\r\n    className,\r\n    kddept,\r\n    popoverClassName,\r\n    triggerClassName,\r\n    isDisabled,\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Handler to always pass a string value to the parent component.\r\n  // This matches the robust handler from Kddept.jsx\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"XX\";\r\n    if (onChange) onChange(val);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      isVirtualized\r\n      // Use selectedKeys (plural) which expects an array\r\n      selectedKeys={[value || \"XX\"]}\r\n      onSelectionChange={handleSelectionChange}\r\n      // Combine the original disabled logic with new isDisabled prop\r\n      isDisabled={isDisabled || status !== \"pilihunit\"}\r\n      size={size || \"sm\"}\r\n      placeholder={placeholder || \"Pilih Unit\"}\r\n      className={className || \"w-full min-w-0 max-w-full\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Unit\"\r\n      classNames={{\r\n        // Apply custom class from props for popover width, with a sensible default\r\n        popoverContent: popoverClassName || \"w-80 sm:w-96\",\r\n        // Make trigger responsive - full width by default, can be overridden\r\n        trigger: `${triggerClassName || \"w-full\"} max-w-full`,\r\n        // Fix the value display to prevent stretching when long text is selected\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        // Ensure the main input area doesn't expand\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        // Additional constraint on the base element\r\n        base: \"w-full max-w-full\",\r\n        // Ensure the label area doesn't expand\r\n        label: \"truncate\",\r\n      }}\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Unit\">\r\n        Semua Unit\r\n      </SelectItem>\r\n      {data\r\n        // Preserve the crucial filtering logic from the original component\r\n        .filter((item) => !kddept || item.kddept === kddept)\r\n        .map((item) => (\r\n          <SelectItem\r\n            key={item.kdunit}\r\n            textValue={`${item.kdunit} - ${item.nmunit}`}\r\n          >\r\n            {/* This span applies ellipsis for clean UI with long text */}\r\n            <span className=\"block whitespace-nowrap overflow-hidden text-ellipsis\">\r\n              {item.kdunit} - {item.nmunit}\r\n            </span>\r\n          </SelectItem>\r\n        ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdunit;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,SAAS,CAAC;IACd,kDAAkD;IAClD,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,WAAW,EACX,SAAS,EACT,MAAM,EACN,gBAAgB,EAChB,gBAAgB,EAChB,UAAU,EACV,GAAG,YACJ,GAAG;IAEJ,iEAAiE;IACjE,kDAAkD;IAClD,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,UAAU,SAAS;IACzB;IAEA,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,aAAa;QACb,mDAAmD;QACnD,cAAc;YAAC,SAAS;SAAK;QAC7B,mBAAmB;QACnB,+DAA+D;QAC/D,YAAY,cAAc,WAAW;QACrC,MAAM,QAAQ;QACd,aAAa,eAAe;QAC5B,WAAW,aAAa;QACxB,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,2EAA2E;YAC3E,gBAAgB,oBAAoB;YACpC,qEAAqE;YACrE,SAAS,GAAG,oBAAoB,SAAS,WAAW,CAAC;YACrD,yEAAyE;YACzE,OAAO;YACP,4CAA4C;YAC5C,aAAa;YACb,cAAc;YACd,4CAA4C;YAC5C,MAAM;YACN,uCAAuC;YACvC,OAAO;QACT;;0BAEA,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAa;eAA5B;;;;;YAGf,IACC,mEAAmE;aAClE,MAAM,CAAC,CAAC,OAAS,CAAC,UAAU,KAAK,MAAM,KAAK,QAC5C,GAAG,CAAC,CAAC,qBACJ,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE;8BAG5C,cAAA,8OAAC;wBAAK,WAAU;;4BACb,KAAK,MAAM;4BAAC;4BAAI,KAAK,MAAM;;;;;;;mBALzB,KAAK,MAAM;;;;;;;;;;;AAW5B;uCAEe", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdlokasi.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport data from \"@/data/Kdlokasi.json\";\r\n\r\nconst Kdlokasi = (props) => {\r\n  // Destructure the new popoverClassName and triggerClassName props\r\n  const { popoverClassName, triggerClassName } = props;\r\n  const { role, kdlokasi } = useContext(MyContext);\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"XX\";\r\n    if (props.onChange) {\r\n      props.onChange(val);\r\n    }\r\n  };\r\n\r\n  // Determine user permissions\r\n  // Treat empty role as admin for backward compatibility\r\n  const canSeeAllProvinces =\r\n    role === \"0\" || role === \"1\" || role === \"X\" || role === \"\" || !role; // Determine selectable data based on role\r\n  const selectableData = canSeeAllProvinces\r\n    ? data\r\n    : data.filter((item) => item.kdlokasi === kdlokasi);\r\n  // Ensure the selected value exists in the collection\r\n  const availableKeys = [\"XX\", ...selectableData.map((item) => item.kdlokasi)];\r\n  const currentValue = props.value || \"XX\";\r\n  const validSelectedValue = availableKeys.includes(currentValue)\r\n    ? currentValue\r\n    : \"XX\";\r\n\r\n  return (\r\n    <Select\r\n      isVirtualized\r\n      selectedKeys={new Set([validSelectedValue])}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihprov\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder={props.placeholder || \"Pilih Provinsi\"}\r\n      className={props.className || \"w-full min-w-0 max-w-full\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Provinsi\"\r\n      classNames={{\r\n        popoverContent: popoverClassName || \"w-80 sm:w-96\",\r\n        trigger: `${triggerClassName || \"w-full\"} max-w-full`,\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        base: \"w-full max-w-full\",\r\n        label: \"truncate\",\r\n      }}\r\n    >\r\n      {\" \"}\r\n      <SelectItem key=\"XX\" textValue=\"Semua Provinsi\">\r\n        Semua Provinsi\r\n      </SelectItem>\r\n      {selectableData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdlokasi}\r\n          textValue={`${item.kdlokasi} - ${item.nmlokasi}`}\r\n        >\r\n          <span className=\"block whitespace-nowrap overflow-hidden text-ellipsis\">\r\n            {item.kdlokasi} - {item.nmlokasi}\r\n          </span>\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdlokasi;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;;;;;;;;AAGA,MAAM,WAAW,CAAC;IAChB,kEAAkE;IAClE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG;IAC/C,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iIAAA,CAAA,UAAS;IAC/C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC;QACjB;IACF;IAEA,6BAA6B;IAC7B,uDAAuD;IACvD,MAAM,qBACJ,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS,MAAM,CAAC,MAAM,0CAA0C;IAClH,MAAM,iBAAiB,qBACnB,OACA,KAAK,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;IAC5C,qDAAqD;IACrD,MAAM,gBAAgB;QAAC;WAAS,eAAe,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;KAAE;IAC5E,MAAM,eAAe,MAAM,KAAK,IAAI;IACpC,MAAM,qBAAqB,cAAc,QAAQ,CAAC,gBAC9C,eACA;IAEJ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,aAAa;QACb,cAAc,IAAI,IAAI;YAAC;SAAmB;QAC1C,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,gBAAgB,oBAAoB;YACpC,SAAS,GAAG,oBAAoB,SAAS,WAAW,CAAC;YACrD,OAAO;YACP,aAAa;YACb,cAAc;YACd,MAAM;YACN,OAAO;QACT;;YAEC;0BACD,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAiB;eAAhC;;;;;YAGf,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;8BAEhD,cAAA,8OAAC;wBAAK,WAAU;;4BACb,KAAK,QAAQ;4BAAC;4BAAI,KAAK,QAAQ;;;;;;;mBAJ7B,KAAK,QAAQ;;;;;;;;;;;AAU5B;uCAEe", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdfungsi.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdfungsi.json\";\r\n\r\nconst Kdfungsi = (props) => {\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"00\";\r\n    if (props.onChange) props.onChange(val);\r\n  };\r\n\r\n  // Map \"XX\" (default state) to \"00\" (component default)\r\n  const selectedValue = props.kdfungsi === \"XX\" ? \"00\" : props.kdfungsi || \"00\";\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={[selectedValue]}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihfungsi\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder=\"Pilih Fungsi\"\r\n      className={props.className || \"max-w-xs mb-2\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Fungsi\"\r\n      classNames={{\r\n        trigger: \"w-full max-w-full\",\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        base: \"w-full max-w-full\",\r\n      }}\r\n    >\r\n      <SelectItem key=\"00\" textValue=\"Semua Fungsi\">\r\n        Semua Fungsi\r\n      </SelectItem>\r\n      {data.map((kl, index) => (\r\n        <SelectItem\r\n          key={kl.kdfungsi}\r\n          textValue={`${kl.kdfungsi} - ${kl.nmfungsi}`}\r\n        >\r\n          {kl.kdfungsi} - {kl.nmfungsi}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdfungsi;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,WAAW,CAAC;IAChB,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC;IACrC;IAEA,uDAAuD;IACvD,MAAM,gBAAgB,MAAM,QAAQ,KAAK,OAAO,OAAO,MAAM,QAAQ,IAAI;IAEzE,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc;YAAC;SAAc;QAC7B,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAY;QACZ,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,cAAc;YACd,MAAM;QACR;;0BAEA,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAe;eAA9B;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,IAAI,sBACb,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,GAAG,QAAQ,EAAE;;wBAE3C,GAAG,QAAQ;wBAAC;wBAAI,GAAG,QAAQ;;mBAHvB,GAAG,QAAQ;;;;;;;;;;;AAQ1B;uCAEe", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdsfungsi.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdsfungsi.json\";\r\n\r\nconst Kdsfungsi = (props) => {\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"00\";\r\n    if (props.onChange) props.onChange(val);\r\n  };\r\n\r\n  // Map \"XX\" (default state) to \"00\" (component default)\r\n  const selectedValue =\r\n    props.kdsfungsi === \"XX\" ? \"00\" : props.kdsfungsi || \"00\";\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={[selectedValue]}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihsubfungsi\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder=\"Pilih Sub Fungsi\"\r\n      className={props.className || \"max-w-xs mb-2\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Sub Fungsi\"\r\n      classNames={{\r\n        trigger: \"w-full max-w-full\",\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        base: \"w-full max-w-full\",\r\n      }}\r\n    >\r\n      <SelectItem key=\"00\" textValue=\"Semua Sub Fungsi\">\r\n        Semua Sub Fungsi\r\n      </SelectItem>\r\n      {data\r\n        .filter((item) => item.kdfungsi === props.kdfungsi)\r\n        .map((kl, index) => (\r\n          <SelectItem\r\n            key={kl.kdsfungsi}\r\n            textValue={`${kl.kdsfungsi} - ${kl.nmsfungsi}`}\r\n          >\r\n            {kl.kdsfungsi} - {kl.nmsfungsi}\r\n          </SelectItem>\r\n        ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdsfungsi;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;IACjB,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC;IACrC;IAEA,uDAAuD;IACvD,MAAM,gBACJ,MAAM,SAAS,KAAK,OAAO,OAAO,MAAM,SAAS,IAAI;IAEvD,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc;YAAC;SAAc;QAC7B,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAY;QACZ,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,cAAc;YACd,MAAM;QACR;;0BAEA,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAmB;eAAlC;;;;;YAGf,KACE,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,MAAM,QAAQ,EACjD,GAAG,CAAC,CAAC,IAAI,sBACR,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;;wBAE7C,GAAG,SAAS;wBAAC;wBAAI,GAAG,SAAS;;mBAHzB,GAAG,SAAS;;;;;;;;;;;AAQ7B;uCAEe", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdprogram.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdprogram.json\";\r\n\r\nconst Kdprogram = (props) => {\r\n  const filteredData = data.filter(\r\n    (item) => item.kddept === props.kddept && item.kdunit === props.kdunit\r\n  );\r\n\r\n  const selectedValue = props.value ? [props.value] : [\"XX\"];\r\n\r\n  const handleSelectionChange = (keys) => {\r\n    const selected = Array.from(keys)[0];\r\n    props.onChange && props.onChange(selected);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={selectedValue}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihprogram\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder=\"Pilih Program\"\r\n      className={props.className || \"max-w-xs mb-2\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Program\"\r\n      classNames={{\r\n        trigger: \"w-full max-w-full\",\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        base: \"w-full max-w-full\",\r\n      }}\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Program\">\r\n        Semua Program\r\n      </SelectItem>\r\n      {filteredData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdprogram}\r\n          textValue={`${item.kdprogram} - ${item.nmprogram}`}\r\n        >\r\n          {item.kdprogram} - {item.nmprogram}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdprogram;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;IACjB,MAAM,eAAe,KAAK,MAAM,CAC9B,CAAC,OAAS,KAAK,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM;IAGxE,MAAM,gBAAgB,MAAM,KAAK,GAAG;QAAC,MAAM,KAAK;KAAC,GAAG;QAAC;KAAK;IAE1D,MAAM,wBAAwB,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;QACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;IACnC;IAEA,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc;QACd,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAY;QACZ,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,cAAc;YACd,MAAM;QACR;;0BAEA,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAgB;eAA/B;;;;;YAGf,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;;wBAEjD,KAAK,SAAS;wBAAC;wBAAI,KAAK,SAAS;;mBAH7B,KAAK,SAAS;;;;;;;;;;;AAQ7B;uCAEe", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdgiat.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdgiat.json\";\r\n\r\nconst Kdgiat = (props) => {\r\n  const filteredData = data.filter(\r\n    (item) =>\r\n      item.kddept === props.kddept &&\r\n      item.kdunit === props.kdunit &&\r\n      item.kdprogram === props.kdprogram\r\n  );\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={props.value ? [props.value] : [\"XX\"]}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange && props.onChange(selected);\r\n      }}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihgiat\"}\r\n      placeholder={props.placeholder || \"Pilih Kegiatan\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Kegiatan\">\r\n        Semua <PERSON>\r\n      </SelectItem>\r\n      {filteredData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdgiat}\r\n          textValue={`${item.kdgiat} - ${item.nmgiat}`}\r\n        >\r\n          {item.kdgiat} - {item.nmgiat}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdgiat;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,SAAS,CAAC;IACd,MAAM,eAAe,KAAK,MAAM,CAC9B,CAAC,OACC,KAAK,MAAM,KAAK,MAAM,MAAM,IAC5B,KAAK,MAAM,KAAK,MAAM,MAAM,IAC5B,KAAK,SAAS,KAAK,MAAM,SAAS;IAGtC,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,MAAM,KAAK,GAAG;YAAC,MAAM,KAAK;SAAC,GAAG;YAAC;SAAK;QAClD,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;QACnC;QACA,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;;0BAEtB,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAiB;eAAhC;;;;;YAGf,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE;;wBAE3C,KAAK,MAAM;wBAAC;wBAAI,KAAK,MAAM;;mBAHvB,KAAK,MAAM;;;;;;;;;;;AAQ1B;uCAEe", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdoutput.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport data from \"@/data/Kdoutput.json\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdoutput = (props) => {\r\n  const filteredData = data.filter(\r\n    (item) =>\r\n      item.kddept === props.kddept &&\r\n      item.kdunit === props.kdunit &&\r\n      item.kdprogram === props.kdprogram &&\r\n      item.kdgiat === props.kdgiat\r\n  );\r\n\r\n  const selectedValue = props.value ? [props.value] : [\"XX\"];\r\n\r\n  const handleSelectionChange = (keys) => {\r\n    const selected = Array.from(keys)[0];\r\n    props.onChange && props.onChange(selected);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={selectedValue}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihoutput\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder=\"Pilih Output\"\r\n      className={props.className || \"max-w-xs mb-2\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Output\"\r\n      classNames={{\r\n        trigger: \"w-full max-w-full\",\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        base: \"w-full max-w-full\",\r\n      }}\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Output\">\r\n        Semua Output\r\n      </SelectItem>\r\n      {filteredData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdoutput}\r\n          textValue={`${item.kdoutput} - ${item.nmoutput}`}\r\n        >\r\n          {item.kdoutput} - {item.nmoutput}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdoutput;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;AAAA;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,eAAe,KAAK,MAAM,CAC9B,CAAC,OACC,KAAK,MAAM,KAAK,MAAM,MAAM,IAC5B,KAAK,MAAM,KAAK,MAAM,MAAM,IAC5B,KAAK,SAAS,KAAK,MAAM,SAAS,IAClC,KAAK,MAAM,KAAK,MAAM,MAAM;IAGhC,MAAM,gBAAgB,MAAM,KAAK,GAAG;QAAC,MAAM,KAAK;KAAC,GAAG;QAAC;KAAK;IAE1D,MAAM,wBAAwB,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;QACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;IACnC;IAEA,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc;QACd,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAY;QACZ,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,SAAS;YACT,OAAO;YACP,aAAa;YACb,cAAc;YACd,MAAM;QACR;;0BAEA,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAe;eAA9B;;;;;YAGf,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;;wBAE/C,KAAK,QAAQ;wBAAC;wBAAI,KAAK,QAAQ;;mBAH3B,KAAK,QAAQ;;;;;;;;;;;AAQ5B;uCAEe", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdsoutput.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdsoutput = (props) => {\r\n  // Check if parent filters are selected to enable this component\r\n  const isEnabled =\r\n    props.status === \"pilihsuboutput\" || props.status === \"pilihsoutput\";\r\n\r\n  // Accept isDisabled from parent and combine with internal logic\r\n  const isActuallyDisabled = props.isDisabled || !isEnabled;\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={props.value ? [props.value] : [\"XX\"]}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange && props.onChange(selected);\r\n      }}\r\n      isDisabled={isActuallyDisabled}\r\n      placeholder={props.placeholder || \"Pilih Sub Output\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Sub Output\">\r\n        Semua Sub Output\r\n      </SelectItem>\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdsoutput;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEA,MAAM,YAAY,CAAC;IACjB,gEAAgE;IAChE,MAAM,YACJ,MAAM,MAAM,KAAK,oBAAoB,MAAM,MAAM,KAAK;IAExD,gEAAgE;IAChE,MAAM,qBAAqB,MAAM,UAAU,IAAI,CAAC;IAEhD,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,MAAM,KAAK,GAAG;YAAC,MAAM,KAAK;SAAC,GAAG;YAAC;SAAK;QAClD,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;QACnC;QACA,YAAY;QACZ,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;kBAEtB,cAAA,8OAAC,4NAAA,CAAA,aAAU;YAAU,WAAU;sBAAmB;WAAlC;;;;;;;;;;AAKtB;uCAEe", "debugId": null}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdkomponen.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdkomponen = (props) => {\r\n  return (\r\n    <Select\r\n      selectedKeys={props.value ? [props.value] : [\"XX\"]}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange && props.onChange(selected);\r\n      }}\r\n      isDisabled={props.status !== \"pilihkomponen\"}\r\n      placeholder={props.placeholder || \"Pilih Komponen\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Se<PERSON><PERSON>\">\r\n        <PERSON><PERSON><PERSON>\r\n      </SelectItem>\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdkomponen;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEA,MAAM,aAAa,CAAC;IAClB,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,MAAM,KAAK,GAAG;YAAC,MAAM,KAAK;SAAC,GAAG;YAAC;SAAK;QAClD,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;QACnC;QACA,YAAY,MAAM,MAAM,KAAK;QAC7B,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;kBAEtB,cAAA,8OAAC,4NAAA,CAAA,aAAU;YAAU,WAAU;sBAAiB;WAAhC;;;;;;;;;;AAKtB;uCAEe", "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdsubkomponen.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdsubkomponen = (props) => {\r\n  return (\r\n    <Select\r\n      selectedKeys={props.value ? [props.value] : [\"XX\"]}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange && props.onChange(selected);\r\n      }}\r\n      isDisabled={props.status !== \"pilihsubkomponen\"}\r\n      placeholder={props.placeholder || \"Pilih Sub Komponen\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Sub Komponen\">\r\n        Semua Sub Komponen\r\n      </SelectItem>\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdsubkomponen;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEA,MAAM,gBAAgB,CAAC;IACrB,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,MAAM,KAAK,GAAG;YAAC,MAAM,KAAK;SAAC,GAAG;YAAC;SAAK;QAClD,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;QACnC;QACA,YAAY,MAAM,MAAM,KAAK;QAC7B,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;kBAEtB,cAAA,8OAAC,4NAAA,CAAA,aAAU;YAAU,WAAU;sBAAqB;WAApC;;;;;;;;;;AAKtB;uCAEe", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdakun.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdakun = (props) => {\r\n  // Always default to AKUN (Kode Akun)\r\n  const getDefaultSelection = () => {\r\n    if (props.value) return [props.value];\r\n    return [\"AKUN\"];\r\n  };\r\n\r\n  // Add a handler to pass both value and type to parent\r\n  const handleSelectionChange = (keys) => {\r\n    const selected = Array.from(keys)[0];\r\n    // Pass the selected type (AKUN, BKPK, JENBEL) to parent\r\n    if (props.onChange) {\r\n      props.onChange(selected); // You can expand this to pass more info if needed\r\n    }\r\n    if (props.onTypeChange) {\r\n      props.onTypeChange(selected); // Optional: for explicit type callback\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={getDefaultSelection()}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.status !== \"pilihakun\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n      aria-label={props[\"aria-label\"] || \"<PERSON>lih <PERSON>\"}\r\n    >\r\n      {/* Always show Ko<PERSON>kun as the first option */}\r\n      <SelectItem key=\"AKUN\" textValue=\"Kode Akun\">\r\n        Kode Akun\r\n      </SelectItem>\r\n\r\n      {/* Show all other options */}\r\n      <SelectItem key=\"BKPK\" textValue=\"Kode BKPK\">\r\n        Kode BKPK\r\n      </SelectItem>\r\n      <SelectItem key=\"JENBEL\" textValue=\"Jenis Belanja\">\r\n        Jenis Belanja\r\n      </SelectItem>\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdakun;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEA,MAAM,SAAS,CAAC;IACd,qCAAqC;IACrC,MAAM,sBAAsB;QAC1B,IAAI,MAAM,KAAK,EAAE,OAAO;YAAC,MAAM,KAAK;SAAC;QACrC,OAAO;YAAC;SAAO;IACjB;IAEA,sDAAsD;IACtD,MAAM,wBAAwB,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;QACpC,wDAAwD;QACxD,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,WAAW,kDAAkD;QAC9E;QACA,IAAI,MAAM,YAAY,EAAE;YACtB,MAAM,YAAY,CAAC,WAAW,uCAAuC;QACvE;IACF;IAEA,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc;QACd,mBAAmB;QACnB,YAAY,MAAM,MAAM,KAAK;QAC7B,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;QACtB,cAAY,KAAK,CAAC,aAAa,IAAI;;0BAGnC,8OAAC,4NAAA,CAAA,aAAU;gBAAY,WAAU;0BAAY;eAA7B;;;;;0BAKhB,8OAAC,4NAAA,CAAA,aAAU;gBAAY,WAAU;0BAAY;eAA7B;;;;;0BAGhB,8OAAC,4NAAA,CAAA,aAAU;gBAAc,WAAU;0BAAgB;eAAnC;;;;;;;;;;;AAKtB;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdsdana.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdsdana.json\";\r\n\r\nconst Kdsdana = (props) => {\r\n  return (\r\n    <Select\r\n      selectedKeys={props.value ? [props.value] : [\"XX\"]}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange && props.onChange(selected);\r\n      }}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihsdana\"}\r\n      placeholder={props.placeholder || \"Pilih Sumber Dana\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Sumber Dana\">\r\n        Semua Sumber Dana\r\n      </SelectItem>\r\n      {data.map((kl, index) => (\r\n        <SelectItem\r\n          key={kl.kdsdana}\r\n          textValue={`${kl.kdsdana} - ${kl.nmsdana2}`}\r\n        >\r\n          {kl.kdsdana} - {kl.nmsdana2}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdsdana;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,UAAU,CAAC;IACf,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,MAAM,KAAK,GAAG;YAAC,MAAM,KAAK;SAAC,GAAG;YAAC;SAAK;QAClD,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;QACnC;QACA,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;;0BAEtB,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAoB;eAAnC;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,IAAI,sBACb,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,QAAQ,EAAE;;wBAE1C,GAAG,OAAO;wBAAC;wBAAI,GAAG,QAAQ;;mBAHtB,GAAG,OAAO;;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdregister.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdregister = (props) => {\r\n  return (\r\n    <Select\r\n      selectedKeys={props.value ? [props.value] : [\"XX\"]}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange && props.onChange(selected);\r\n      }}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihregister\"}\r\n      placeholder={props.placeholder || \"Pilih Register\"}\r\n      className={props.className}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" textValue=\"Semua Register\">\r\n        Semua Register\r\n      </SelectItem>\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdregister;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEA,MAAM,aAAa,CAAC;IAClB,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,MAAM,KAAK,GAAG;YAAC,MAAM,KAAK;SAAC,GAAG;YAAC;SAAK;QAClD,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC;QACnC;QACA,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS;QAC1B,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;kBAEtB,cAAA,8OAAC,4NAAA,CAAA,aAAU;YAAU,WAAU;sBAAiB;WAAhC;;;;;;;;;;AAKtB;uCAEe", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisInflasiInquiry.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdInflasiInquiry.json\";\r\n\r\nconst JenisInflasiInquiry = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Jenis Inflasi\"\r\n      className=\"max-w-full\"\r\n      aria-label=\"Pilih Jenis Inflasi\"\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Belanja dan Inflasi\">\r\n        Semua Belanja dan Inflasi\r\n      </SelectItem>\r\n      {data.map((inf, index) => (\r\n        <SelectItem\r\n          key={inf.kdinflasi}\r\n          value={inf.kdinflasi}\r\n          textValue={inf.nminflasi}\r\n        >\r\n          {inf.nminflasi}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisInflasiInquiry;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,sBAAsB,CAAC;IAC3B,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;QACV,cAAW;;0BAEX,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAA4B;eAAtD;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,IAAI,SAAS;oBACpB,WAAW,IAAI,SAAS;8BAEvB,IAAI,SAAS;mBAJT,IAAI,SAAS;;;;;;;;;;;AAS5B;uCAEe", "debugId": null}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisIkn.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdIkn.json\";\r\n\r\nconst JenisIkn = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Jenis IKN\"\r\n      className=\"max-w-xs mb-1\"\r\n      aria-label=\"<PERSON>lih Jenis IKN\"\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Belanja dan IKN\">\r\n        Semua Belanja dan IKN\r\n      </SelectItem>\r\n      {data.map((ikn, index) => (\r\n        <SelectItem key={ikn.kdikn} value={ikn.kdikn} textValue={ikn.nmikn}>\r\n          {ikn.nmikn}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisIkn;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,WAAW,CAAC;IAChB,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;QACV,cAAW;;0BAEX,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAAwB;eAAlD;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,4NAAA,CAAA,aAAU;oBAAiB,OAAO,IAAI,KAAK;oBAAE,WAAW,IAAI,KAAK;8BAC/D,IAAI,KAAK;mBADK,IAAI,KAAK;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisMiskin.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdMiskin.json\";\r\n\r\nconst JenisMiskin = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"<PERSON><PERSON>h Jen<PERSON> Kemiskinan Ekstrim\"\r\n      className=\"max-w-xs mb-1\"\r\n    >\r\n      <SelectItem\r\n        key=\"00\"\r\n        value=\"00\"\r\n        textValue=\"Semua Belanja dan Kemiskinan Ekstrim\"\r\n      >\r\n        Se<PERSON>a Belan<PERSON> dan Kemis<PERSON>an Ekstrim\r\n      </SelectItem>\r\n      {data.map((ms, index) => (\r\n        <SelectItem\r\n          key={ms.kdmiskin}\r\n          value={ms.kdmiskin}\r\n          textValue={ms.nmmiskin}\r\n        >\r\n          {ms.nmmiskin}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisMiskin;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,cAAc,CAAC;IACnB,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAET,OAAM;gBACN,WAAU;0BACX;eAHK;;;;;YAML,KAAK,GAAG,CAAC,CAAC,IAAI,sBACb,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,GAAG,QAAQ;oBAClB,WAAW,GAAG,QAAQ;8BAErB,GAAG,QAAQ;mBAJP,GAAG,QAAQ;;;;;;;;;;;AAS1B;uCAEe", "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisPangan.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdPangan.json\";\r\n\r\nconst JenisPangan = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"<PERSON><PERSON><PERSON>\"\r\n      className=\"max-w-xs mb-1\"\r\n    >\r\n      <SelectItem\r\n        key=\"00\"\r\n        value=\"00\"\r\n        textValue=\"Semua Belanja dan <PERSON>anan <PERSON>\"\r\n      >\r\n        Semua Bel<PERSON> dan <PERSON>\r\n      </SelectItem>\r\n      {data.map((pg, index) => (\r\n        <SelectItem\r\n          key={pg.kdpangan}\r\n          value={pg.kdpangan}\r\n          textValue={pg.nmpangan}\r\n        >\r\n          {pg.nmpangan}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisPangan;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,cAAc,CAAC;IACnB,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAET,OAAM;gBACN,WAAU;0BACX;eAHK;;;;;YAML,KAAK,GAAG,CAAC,CAAC,IAAI,sBACb,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,GAAG,QAAQ;oBAClB,WAAW,GAAG,QAAQ;8BAErB,GAAG,QAAQ;mBAJP,GAAG,QAAQ;;;;;;;;;;;AAS1B;uCAEe", "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisStuntingInquiry.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdStunting.json\";\r\n\r\nconst JenisStuntingInquiry = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Tematik Stunting\"\r\n      className=\"min-w-2xl max-w-full\"\r\n      aria-label={props[\"aria-label\"] || \"Pilih Tematik Stunting\"}\r\n    >\r\n      <SelectItem\r\n        key=\"00\"\r\n        value=\"00\"\r\n        textValue=\"Semua Belanja dan Tematik Stunting\"\r\n      >\r\n        Semua Belanja dan <PERSON>unting\r\n      </SelectItem>\r\n      {data.map((stun, index) => (\r\n        <SelectItem\r\n          key={stun.kdstunting}\r\n          value={stun.kdstunting}\r\n          textValue={`${stun.kdstunting} - ${stun.nmstunting}`}\r\n        >\r\n          {stun.kdstunting} - {stun.nmstunting}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisStuntingInquiry;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;QACV,cAAY,KAAK,CAAC,aAAa,IAAI;;0BAEnC,8OAAC,4NAAA,CAAA,aAAU;gBAET,OAAM;gBACN,WAAU;0BACX;eAHK;;;;;YAML,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,UAAU;oBACtB,WAAW,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE;;wBAEnD,KAAK,UAAU;wBAAC;wBAAI,KAAK,UAAU;;mBAJ/B,KAAK,UAAU;;;;;;;;;;;AAS9B;uCAEe", "debugId": null}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisPemilu.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdPemilu.json\";\r\n\r\nconst JenisPemilu = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"<PERSON><PERSON>h Belanja Pemilu\"\r\n      className=\"max-w-xs mb-1\"\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Belanja dan Pemilu\">\r\n        Semua Belanja dan Pemilu\r\n      </SelectItem>\r\n      {data.map((pm, index) => (\r\n        <SelectItem\r\n          key={pm.kdpemilu}\r\n          value={pm.kdpemilu}\r\n          textValue={pm.nmpemilu}\r\n        >\r\n          {pm.nmpemilu}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisPemilu;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,cAAc,CAAC;IACnB,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAA2B;eAArD;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,IAAI,sBACb,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,GAAG,QAAQ;oBAClB,WAAW,GAAG,QAAQ;8BAErB,GAAG,QAAQ;mBAJP,GAAG,QAAQ;;;;;;;;;;;AAS1B;uCAEe", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/KdPN.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/KdPN.json\";\r\n\r\nconst KodePN = (props) => {\r\n  const selectedValue =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? props.value\r\n      : \"00\";\r\n\r\n  return (\r\n    <Select\r\n      aria-label=\"Pilih Prioritas Nasional\"\r\n      selectedKeys={new Set([selectedValue])}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Prioritas Nasional\"\r\n      className=\"max-w-full\"\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Prioritas Nasional\">\r\n        Semua Prioritas Nasional\r\n      </SelectItem>\r\n      {data.map((item, index) => (\r\n        <SelectItem\r\n          key={item.kdpn}\r\n          value={item.kdpn}\r\n          textValue={`${item.kdpn} - ${item.nmpn}`}\r\n        >\r\n          {item.kdpn} - {item.nmpn}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default KodePN;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,SAAS,CAAC;IACd,MAAM,gBACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD,MAAM,KAAK,GACX;IAEN,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAW;QACX,cAAc,IAAI,IAAI;YAAC;SAAc;QACrC,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAA2B;eAArD;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,IAAI;oBAChB,WAAW,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;;wBAEvC,KAAK,IAAI;wBAAC;wBAAI,KAAK,IAAI;;mBAJnB,KAAK,IAAI;;;;;;;;;;;AASxB;uCAEe", "debugId": null}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/KdPP.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Prioritas.json\";\r\n\r\nconst KodePP = (props) => {\r\n  // Reset to \"00\" when kdPN changes\r\n  useEffect(() => {\r\n    if (props.kdPN && props.value !== \"00\") {\r\n      props.onChange(\"00\");\r\n    }\r\n  }, [props.kdPN]);\r\n\r\n  // Filter data based on selected kdPN (parent)\r\n  const filteredData =\r\n    props.kdPN && props.kdPN !== \"00\"\r\n      ? data.filter((item) => item.kdpn === props.kdPN)\r\n      : data;\r\n\r\n  // Debug log to verify filtering\r\n\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      aria-label=\"Pilih Program Prioritas\"\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Program Prioritas\"\r\n      className=\"max-w-full\"\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Program Prioritas\">\r\n        Semua Program Prioritas\r\n      </SelectItem>\r\n      {filteredData.map((item, index) => (\r\n        <SelectItem\r\n          key={`${item.kdpn}-${item.kdpp}`}\r\n          value={item.kdpp}\r\n          textValue={`${item.kdpp} - ${item.nmpp}`}\r\n        >\r\n          {item.kdpp} - {item.nmpp}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default KodePP;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,SAAS,CAAC;IACd,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,KAAK,MAAM;YACtC,MAAM,QAAQ,CAAC;QACjB;IACF,GAAG;QAAC,MAAM,IAAI;KAAC;IAEf,8CAA8C;IAC9C,MAAM,eACJ,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,OACzB,KAAK,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,MAAM,IAAI,IAC9C;IAEN,gCAAgC;IAEhC,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAW;QACX,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAA0B;eAApD;;;;;YAGf,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,IAAI;oBAChB,WAAW,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;;wBAEvC,KAAK,IAAI;wBAAC;wBAAI,KAAK,IAAI;;mBAJnB,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;;;;;;;;;;;AAS1C;uCAEe", "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/KdKegPP.jsx"], "sourcesContent": ["import React, { useEffect, useMemo } from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/KdKP.json\";\r\n\r\nconst KodeKegPP = (props) => {\r\n  // Reset to \"00\" when any parent filter changes\r\n  useEffect(() => {\r\n    if ((props.kdPN || props.kdPP) && props.value !== \"00\") {\r\n      props.onChange(\"00\");\r\n    }\r\n  }, [props.kdPN, props.kdPP]);\r\n\r\n  // Filter data based on selected parent filters\r\n  const filteredData = useMemo(() => {\r\n    let filtered = data;\r\n\r\n    if (props.kdPN && props.kdPN !== \"00\") {\r\n      const beforeFilter = filtered.length;\r\n      filtered = filtered.filter((item) => {\r\n        const match = item.kdpn === props.kdPN;\r\n        if (!match && beforeFilter < 5) {\r\n        }\r\n        return match;\r\n      });\r\n    }\r\n\r\n    if (props.kdPP && props.kdPP !== \"00\") {\r\n      const beforeFilter = filtered.length;\r\n\r\n      // Handle case where kdPP might be in format \"01-01\" (kdpn-kdpp)\r\n      let kdppValue = props.kdPP;\r\n      if (props.kdPP.includes(\"-\")) {\r\n        kdppValue = props.kdPP.split(\"-\")[1]; // Get the part after the dash\r\n      }\r\n\r\n      filtered = filtered.filter((item) => {\r\n        const match = item.kdpp === kdppValue;\r\n        if (!match && beforeFilter < 5) {\r\n        }\r\n        return match;\r\n      });\r\n    }\r\n\r\n    // Get unique kdkp values with their descriptions\r\n    const uniqueActivities = [];\r\n    const seen = new Set();\r\n\r\n    filtered.forEach((item) => {\r\n      if (!seen.has(item.kdkp)) {\r\n        seen.add(item.kdkp);\r\n        uniqueActivities.push({\r\n          kdkp: item.kdkp,\r\n          deskripsi: item.deskripsi,\r\n        });\r\n      }\r\n    });\r\n\r\n    return uniqueActivities.sort((a, b) => a.kdkp.localeCompare(b.kdkp));\r\n  }, [props.kdPN, props.kdPP]);\r\n\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      aria-label=\"Pilih Kegiatan Prioritas\"\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange(selected);\r\n      }}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n      className=\"max-w-full\"\r\n    >\r\n      <SelectItem key=\"00\" textValue=\"Semua Kegiatan Prioritas\">\r\n        Semua Kegiatan Prioritas\r\n      </SelectItem>\r\n      {filteredData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdkp}\r\n          value={item.kdkp}\r\n          textValue={`${item.kdkp} - ${item.deskripsi}`}\r\n        >\r\n          {item.kdkp} - {item.deskripsi}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default KodeKegPP;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;IACjB,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;YACtD,MAAM,QAAQ,CAAC;QACjB;IACF,GAAG;QAAC,MAAM,IAAI;QAAE,MAAM,IAAI;KAAC;IAE3B,+CAA+C;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,WAAW;QAEf,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM;YACrC,MAAM,eAAe,SAAS,MAAM;YACpC,WAAW,SAAS,MAAM,CAAC,CAAC;gBAC1B,MAAM,QAAQ,KAAK,IAAI,KAAK,MAAM,IAAI;gBACtC,IAAI,CAAC,SAAS,eAAe,GAAG,CAChC;gBACA,OAAO;YACT;QACF;QAEA,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM;YACrC,MAAM,eAAe,SAAS,MAAM;YAEpC,gEAAgE;YAChE,IAAI,YAAY,MAAM,IAAI;YAC1B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,YAAY,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,8BAA8B;YACtE;YAEA,WAAW,SAAS,MAAM,CAAC,CAAC;gBAC1B,MAAM,QAAQ,KAAK,IAAI,KAAK;gBAC5B,IAAI,CAAC,SAAS,eAAe,GAAG,CAChC;gBACA,OAAO;YACT;QACF;QAEA,iDAAiD;QACjD,MAAM,mBAAmB,EAAE;QAC3B,MAAM,OAAO,IAAI;QAEjB,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,IAAI,GAAG;gBACxB,KAAK,GAAG,CAAC,KAAK,IAAI;gBAClB,iBAAiB,IAAI,CAAC;oBACpB,MAAM,KAAK,IAAI;oBACf,WAAW,KAAK,SAAS;gBAC3B;YACF;QACF;QAEA,OAAO,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IACpE,GAAG;QAAC,MAAM,IAAI;QAAE,MAAM,IAAI;KAAC;IAE3B,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAW;QACX,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;QACtB,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAA2B;eAA1C;;;;;YAGf,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,IAAI;oBAChB,WAAW,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;;wBAE5C,KAAK,IAAI;wBAAC;wBAAI,KAAK,SAAS;;mBAJxB,KAAK,IAAI;;;;;;;;;;;AASxB;uCAEe", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/KdPRI.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useMemo } from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/KdPRI.json\";\r\n\r\nconst KodePRI = (props) => {\r\n  // Reset to \"00\" when any parent filter changes\r\n  useEffect(() => {\r\n    if ((props.kdPN || props.kdPP || props.KegPP) && props.value !== \"00\") {\r\n      props.onChange(\"00\");\r\n    }\r\n  }, [props.kdPN, props.kdPP, props.KegPP]);\r\n\r\n  // Filter data based on selected parent filters\r\n  const filteredData = useMemo(() => {\r\n    let filtered = data;\r\n\r\n    if (props.kdPN && props.kdPN !== \"00\") {\r\n      filtered = filtered.filter((item) => item.kdpn === props.kdPN);\r\n    }\r\n\r\n    if (props.kdPP && props.kdPP !== \"00\") {\r\n      // Handle case where kdPP might be in format \"01-01\" (kdpn-kdpp)\r\n      let kdppValue = props.kdPP;\r\n      if (props.kdPP.includes(\"-\")) {\r\n        kdppValue = props.kdPP.split(\"-\")[1]; // Get the part after the dash\r\n      }\r\n\r\n      filtered = filtered.filter((item) => item.kdpp === kdppValue);\r\n    }\r\n\r\n    if (props.KegPP && props.KegPP !== \"00\") {\r\n      filtered = filtered.filter((item) => item.kdkp === props.KegPP);\r\n    }\r\n\r\n    // Get unique kdproy values with their descriptions\r\n    const uniqueProjects = [];\r\n    const seen = new Set();\r\n\r\n    filtered.forEach((item) => {\r\n      if (!seen.has(item.kdproy)) {\r\n        seen.add(item.kdproy);\r\n        uniqueProjects.push({\r\n          kdproy: item.kdproy,\r\n          deskripsi: item.deskripsi,\r\n        });\r\n      }\r\n    });\r\n\r\n    return uniqueProjects.sort((a, b) => a.kdproy.localeCompare(b.kdproy));\r\n  }, [props.kdPN, props.kdPP, props.KegPP]);\r\n\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const selected = Array.from(keys)[0];\r\n        props.onChange(selected);\r\n      }}\r\n      size={props.size || \"sm\"}\r\n      disallowEmptySelection\r\n      className=\"form-select form-select-sm text-select max-w-full\"\r\n      aria-label=\".form-select-sm\"\r\n      placeholder=\"Pilih Proyek Prioritas\"\r\n    >\r\n      <SelectItem key=\"00\" textValue=\"Semua Proyek Prioritas\">\r\n        Semua Proyek Prioritas\r\n      </SelectItem>\r\n      {filteredData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdproy}\r\n          value={item.kdproy}\r\n          textValue={`${item.kdproy} - ${item.deskripsi}`}\r\n        >\r\n          {item.kdproy} - {item.deskripsi}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default KodePRI;\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;;;AAFA;;;;;AAKA,MAAM,UAAU,CAAC;IACf,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM;YACrE,MAAM,QAAQ,CAAC;QACjB;IACF,GAAG;QAAC,MAAM,IAAI;QAAE,MAAM,IAAI;QAAE,MAAM,KAAK;KAAC;IAExC,+CAA+C;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,WAAW;QAEf,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM;YACrC,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,MAAM,IAAI;QAC/D;QAEA,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,MAAM;YACrC,gEAAgE;YAChE,IAAI,YAAY,MAAM,IAAI;YAC1B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAC5B,YAAY,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,8BAA8B;YACtE;YAEA,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;QACrD;QAEA,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM;YACvC,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK,MAAM,KAAK;QAChE;QAEA,mDAAmD;QACnD,MAAM,iBAAiB,EAAE;QACzB,MAAM,OAAO,IAAI;QAEjB,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG;gBAC1B,KAAK,GAAG,CAAC,KAAK,MAAM;gBACpB,eAAe,IAAI,CAAC;oBAClB,QAAQ,KAAK,MAAM;oBACnB,WAAW,KAAK,SAAS;gBAC3B;YACF;QACF;QAEA,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE,MAAM;IACtE,GAAG;QAAC,MAAM,IAAI;QAAE,MAAM,IAAI;QAAE,MAAM,KAAK;KAAC;IAExC,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACpC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAM,MAAM,IAAI,IAAI;QACpB,sBAAsB;QACtB,WAAU;QACV,cAAW;QACX,aAAY;;0BAEZ,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAyB;eAAxC;;;;;YAGf,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,MAAM;oBAClB,WAAW,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;;wBAE9C,KAAK,MAAM;wBAAC;wBAAI,KAAK,SAAS;;mBAJ1B,KAAK,MAAM;;;;;;;;;;;AAS1B;uCAEe", "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisMP.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdMP.json\";\r\n\r\nconst JenisMP = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      aria-label=\"Pilih Major Project\"\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Major Project\"\r\n      className=\"max-w-full\"\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Major Project\">\r\n        Semua Major Project\r\n      </SelectItem>\r\n      {data.map((mp, index) => (\r\n        <SelectItem\r\n          key={mp.kdmp}\r\n          value={mp.kdmp}\r\n          textValue={`${mp.kdmp} - ${mp.nmmp}`}\r\n        >\r\n          {mp.kdmp} - {mp.nmmp}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisMP;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,UAAU,CAAC;IACf,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAW;QACX,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;;0BAEV,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAAsB;eAAhD;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,IAAI,sBACb,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,GAAG,IAAI;oBACd,WAAW,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;;wBAEnC,GAAG,IAAI;wBAAC;wBAAI,GAAG,IAAI;;mBAJf,GAAG,IAAI;;;;;;;;;;;AAStB;uCAEe", "debugId": null}}, {"offset": {"line": 1811, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/JenisTEMA.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"../../../data/KdTEMA.json\";\r\n\r\nconst JenisTEMA = (props) => {\r\n  // Ensure we have a valid selectedKeys Set\r\n  const selectedKeys =\r\n    props.value && props.value !== \"\" && props.value !== \"XX\"\r\n      ? [props.value]\r\n      : [\"00\"];\r\n\r\n  return (\r\n    <Select\r\n      isVirtualized\r\n      selectedKeys={new Set(selectedKeys)}\r\n      onSelectionChange={(keys) => {\r\n        const value = Array.from(keys)[0];\r\n        props.onChange(value);\r\n      }}\r\n      size=\"sm\"\r\n      placeholder=\"Pilih Tematik\"\r\n      className=\"max-w-full\"\r\n      aria-label={props[\"aria-label\"] || \"Pilih Tematik\"}\r\n    >\r\n      <SelectItem key=\"00\" value=\"00\" textValue=\"Semua Tematik\">\r\n        Semua Tematik\r\n      </SelectItem>\r\n      {data.map((tema, index) => (\r\n        <SelectItem\r\n          key={tema.kdtema}\r\n          value={tema.kdtema}\r\n          textValue={`${tema.kdtema} - ${tema.nmtema}`}\r\n        >\r\n          {tema.kdtema} - {tema.nmtema}\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default JenisTEMA;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;IACjB,0CAA0C;IAC1C,MAAM,eACJ,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,OACjD;QAAC,MAAM,KAAK;KAAC,GACb;QAAC;KAAK;IAEZ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,aAAa;QACb,cAAc,IAAI,IAAI;QACtB,mBAAmB,CAAC;YAClB,MAAM,QAAQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,QAAQ,CAAC;QACjB;QACA,MAAK;QACL,aAAY;QACZ,WAAU;QACV,cAAY,KAAK,CAAC,aAAa,IAAI;;0BAEnC,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAAgB;eAA1C;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,MAAM;oBAClB,WAAW,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE;;wBAE3C,KAAK,MAAM;wBAAC;wBAAI,KAAK,MAAM;;mBAJvB,KAAK,MAAM;;;;;;;;;;;AAS1B;uCAEe", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kddekon.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kddekon.json\";\r\n\r\nconst Kddekon = (props) => {\r\n  // Destructure props for easier access and clarity\r\n  const {\r\n    value,\r\n    onChange,\r\n    status,\r\n    size,\r\n    placeholder,\r\n    className,\r\n    popoverClassName,\r\n    triggerClassName,\r\n    isDisabled,\r\n  } = props;\r\n  // Standardized handler to always pass a string value to the parent\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"XX\"; // Use \"XX\" as the default key\r\n    if (onChange) onChange(val);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      isVirtualized\r\n      // Use selectedKeys (plural) which expects an array for better compatibility\r\n      selectedKeys={[String(value || \"XX\")]}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={isDisabled || status !== \"pilihdekon\"}\r\n      size={size || \"sm\"}\r\n      placeholder={placeholder || \"Pilih Kewenangan\"}\r\n      className={className || \"w-full min-w-0 max-w-full\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih <PERSON>wenangan\"\r\n      classNames={{\r\n        // Apply custom class from props for popover width, with a default\r\n        popoverContent: popoverClassName || \"w-80 sm:w-96\",\r\n        // Make trigger responsive - full width by default, can be overridden\r\n        trigger: `${triggerClassName || \"w-full\"} max-w-full`,\r\n        // Fix the value display to prevent stretching when long text is selected\r\n        value: \"truncate pr-8 max-w-full overflow-hidden\",\r\n        // Ensure the main input area doesn't expand\r\n        mainWrapper: \"w-full max-w-full\",\r\n        innerWrapper: \"w-full max-w-full overflow-hidden\",\r\n        // Additional constraint on the base element\r\n        base: \"w-full max-w-full\",\r\n        // Ensure the label area doesn't expand\r\n        label: \"truncate\",\r\n      }}\r\n    >\r\n      {\" \"}\r\n      <SelectItem key=\"XX\" textValue=\"Semua Kewenangan\">\r\n        Semua Kewenangan\r\n      </SelectItem>\r\n      {data.map((item) => (\r\n        <SelectItem\r\n          key={String(item.kddekon)}\r\n          textValue={`${item.kddekon} - ${item.nmdekon}`}\r\n        >\r\n          {/* This span applies ellipsis for clean UI with long text */}\r\n          <span className=\"block whitespace-nowrap overflow-hidden text-ellipsis\">\r\n            {item.kddekon} - {item.nmdekon}\r\n          </span>\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kddekon;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;;;;;;;AAGA,MAAM,UAAU,CAAC;IACf,kDAAkD;IAClD,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,gBAAgB,EAChB,UAAU,EACX,GAAG;IACJ,mEAAmE;IACnE,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,MAAM,8BAA8B;QACvE,IAAI,UAAU,SAAS;IACzB;IAEA,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,aAAa;QACb,4EAA4E;QAC5E,cAAc;YAAC,OAAO,SAAS;SAAM;QACrC,mBAAmB;QACnB,YAAY,cAAc,WAAW;QACrC,MAAM,QAAQ;QACd,aAAa,eAAe;QAC5B,WAAW,aAAa;QACxB,sBAAsB;QACtB,cAAW;QACX,YAAY;YACV,kEAAkE;YAClE,gBAAgB,oBAAoB;YACpC,qEAAqE;YACrE,SAAS,GAAG,oBAAoB,SAAS,WAAW,CAAC;YACrD,yEAAyE;YACzE,OAAO;YACP,4CAA4C;YAC5C,aAAa;YACb,cAAc;YACd,4CAA4C;YAC5C,MAAM;YACN,uCAAuC;YACvC,OAAO;QACT;;YAEC;0BACD,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAmB;eAAlC;;;;;YAGf,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,OAAO,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE;8BAG9C,cAAA,8OAAC;wBAAK,WAAU;;4BACb,KAAK,OAAO;4BAAC;4BAAI,KAAK,OAAO;;;;;;;mBAL3B,OAAO,KAAK,OAAO;;;;;;;;;;;AAWlC;uCAEe", "debugId": null}}, {"offset": {"line": 1973, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdkabkota.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdkabkota.json\";\r\n\r\nconst Kdkabkota = (props) => {\r\n  const { role, kdlokasi } = useContext(MyContext);\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"XX\";\r\n    if (props.onChange) {\r\n      props.onChange(val);\r\n    }\r\n  };\r\n\r\n  // Get the filter province value\r\n  const filterKdlokasi = props.kdlokasi || kdlokasi; // Determine available data\r\n  const availableData = data.filter((item) => {\r\n    // If kdlokasi is \"XX\" or falsy (no specific province selected), show no additional kabkota\r\n    // Only show kabkota when a specific province is selected\r\n    // Also exclude kdkabkota=\"XX\" to avoid duplicate keys with hardcoded \"Semua Kabupaten/Kota\"\r\n    return (\r\n      filterKdlokasi &&\r\n      filterKdlokasi !== \"XX\" &&\r\n      item.kdlokasi === filterKdlokasi &&\r\n      item.kdkabkota !== \"XX\"\r\n    );\r\n  }); // Ensure the selected value is valid\r\n  const availableKeys = [\"XX\", ...availableData.map((item) => item.kdkabkota)];\r\n  const currentValue = props.value || \"XX\";\r\n  const validSelectedValue = availableKeys.includes(currentValue)\r\n    ? currentValue\r\n    : \"XX\";\r\n\r\n  return (\r\n    <Select\r\n      isVirtualized\r\n      selectedKeys={[validSelectedValue]}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihkdkabkota\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder={props.placeholder || \"Pilih Kabupaten/Kota\"}\r\n      className={props.className || \"min-w-0 flex-[2]\"}\r\n      disallowEmptySelection\r\n      aria-label=\"Pilih Kabupaten/Kota\"\r\n    >\r\n      {\" \"}\r\n      <SelectItem key=\"XX\" textValue=\"Semua Kabupaten/Kota\">\r\n        Semua Kabupaten/Kota\r\n      </SelectItem>\r\n      {availableData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdkabkota}\r\n          textValue={`${item.kdkabkota} - ${item.nmkabkota}`}\r\n        >\r\n          <span className=\"block whitespace-nowrap overflow-hidden text-ellipsis\">\r\n            {item.kdkabkota} - {item.nmkabkota}\r\n          </span>\r\n        </SelectItem>\r\n      ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdkabkota;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;IACjB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iIAAA,CAAA,UAAS;IAC/C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC;QACjB;IACF;IAEA,gCAAgC;IAChC,MAAM,iBAAiB,MAAM,QAAQ,IAAI,UAAU,2BAA2B;IAC9E,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC;QACjC,2FAA2F;QAC3F,yDAAyD;QACzD,4FAA4F;QAC5F,OACE,kBACA,mBAAmB,QACnB,KAAK,QAAQ,KAAK,kBAClB,KAAK,SAAS,KAAK;IAEvB,IAAI,qCAAqC;IACzC,MAAM,gBAAgB;QAAC;WAAS,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,SAAS;KAAE;IAC5E,MAAM,eAAe,MAAM,KAAK,IAAI;IACpC,MAAM,qBAAqB,cAAc,QAAQ,CAAC,gBAC9C,eACA;IAEJ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,aAAa;QACb,cAAc;YAAC;SAAmB;QAClC,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;QACtB,cAAW;;YAEV;0BACD,8OAAC,4NAAA,CAAA,aAAU;gBAAU,WAAU;0BAAuB;eAAtC;;;;;YAGf,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,4NAAA,CAAA,aAAU;oBAET,WAAW,GAAG,KAAK,SAAS,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;8BAElD,cAAA,8OAAC;wBAAK,WAAU;;4BACb,KAAK,SAAS;4BAAC;4BAAI,KAAK,SAAS;;;;;;;mBAJ/B,KAAK,SAAS;;;;;;;;;;;AAU7B;uCAEe", "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdkanwil.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\nimport data from \"@/data/Kdkanwil.json\";\r\n\r\nconst Kdkanwil = (props) => {\r\n  const { role, kdkanwil } = useContext(MyContext);\r\n\r\n  const handleSelectionChange = (keys) => {\r\n    const selected = Array.from(keys)[0] || \"XX\";\r\n    if (props.onChange) {\r\n      props.onChange(selected);\r\n    }\r\n  };\r\n\r\n  // Get the filter province value (if provided)\r\n  const filterKdlokasi = props.kdlokasi;\r\n\r\n  // Filter Kanwil data based on province selection\r\n  const availableData = data.filter((item) => {\r\n    // If kdlokasi is \"XX\" or falsy (no specific province selected), show all kanwil\r\n    // When a specific province is selected, only show kanwil for that province\r\n    // Also exclude kdkanwil=\"XX\" to avoid duplicate keys with hardcoded \"Semua Kanwil\"\r\n    if (filterKdlokasi && filterKdlokasi !== \"XX\") {\r\n      return item.kdlokasi === filterKdlokasi && item.kdkanwil !== \"XX\";\r\n    }\r\n    // Show all kanwil when no province filter is applied\r\n    return item.kdkanwil !== \"XX\";\r\n  });\r\n\r\n  // Role-based filtering with fallback for empty/undefined roles\r\n  const shouldShowAll =\r\n    role === \"0\" || role === \"1\" || role === \"X\" || !role || role === \"\";\r\n\r\n  // Ensure the selected value is valid\r\n  const availableKeys = [\"XX\", ...availableData.map((item) => item.kdkanwil)];\r\n  const currentValue = props.value || \"XX\";\r\n  const validSelectedValue = availableKeys.includes(currentValue)\r\n    ? currentValue\r\n    : \"XX\";\r\n\r\n  return (\r\n    <Select\r\n      aria-label=\"Pilih Kanwil\"\r\n      selectedKeys={[validSelectedValue]}\r\n      onSelectionChange={handleSelectionChange}\r\n      isDisabled={props.isDisabled || props.status !== \"pilihkanwil\"}\r\n      size={props.size || \"sm\"}\r\n      placeholder={props.placeholder || \"Pilih Kanwil\"}\r\n      className={props.className || \"min-w-0 flex-[2]\"}\r\n      disallowEmptySelection\r\n    >\r\n      <SelectItem key=\"XX\" value=\"XX\" textValue=\"Semua Kanwil\">\r\n        Semua Kanwil\r\n      </SelectItem>\r\n      {shouldShowAll\r\n        ? availableData.map((item) => (\r\n            <SelectItem\r\n              key={item.kdkanwil}\r\n              value={item.kdkanwil}\r\n              textValue={`${item.kdkanwil} - ${item.nmkanwil}`}\r\n            >\r\n              {item.kdkanwil} - {item.nmkanwil}\r\n            </SelectItem>\r\n          ))\r\n        : availableData\r\n            .filter((item) => item.kdkanwil === kdkanwil)\r\n            .map((item) => (\r\n              <SelectItem\r\n                key={item.kdkanwil}\r\n                value={item.kdkanwil}\r\n                textValue={`${item.kdkanwil} - ${item.nmkanwil}`}\r\n              >\r\n                {item.kdkanwil} - {item.nmkanwil}\r\n              </SelectItem>\r\n            ))}\r\n    </Select>\r\n  );\r\n};\r\n\r\nexport default Kdkanwil;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;;;;;;;AAGA,MAAM,WAAW,CAAC;IAChB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iIAAA,CAAA,UAAS;IAE/C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACxC,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC;QACjB;IACF;IAEA,8CAA8C;IAC9C,MAAM,iBAAiB,MAAM,QAAQ;IAErC,iDAAiD;IACjD,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC;QACjC,gFAAgF;QAChF,2EAA2E;QAC3E,mFAAmF;QACnF,IAAI,kBAAkB,mBAAmB,MAAM;YAC7C,OAAO,KAAK,QAAQ,KAAK,kBAAkB,KAAK,QAAQ,KAAK;QAC/D;QACA,qDAAqD;QACrD,OAAO,KAAK,QAAQ,KAAK;IAC3B;IAEA,+DAA+D;IAC/D,MAAM,gBACJ,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,CAAC,QAAQ,SAAS;IAEpE,qCAAqC;IACrC,MAAM,gBAAgB;QAAC;WAAS,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;KAAE;IAC3E,MAAM,eAAe,MAAM,KAAK,IAAI;IACpC,MAAM,qBAAqB,cAAc,QAAQ,CAAC,gBAC9C,eACA;IAEJ,qBACE,8OAAC,4MAAA,CAAA,SAAM;QACL,cAAW;QACX,cAAc;YAAC;SAAmB;QAClC,mBAAmB;QACnB,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;QACjD,MAAM,MAAM,IAAI,IAAI;QACpB,aAAa,MAAM,WAAW,IAAI;QAClC,WAAW,MAAM,SAAS,IAAI;QAC9B,sBAAsB;;0BAEtB,8OAAC,4NAAA,CAAA,aAAU;gBAAU,OAAM;gBAAK,WAAU;0BAAe;eAAzC;;;;;YAGf,gBACG,cAAc,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,QAAQ;oBACpB,WAAW,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;;wBAE/C,KAAK,QAAQ;wBAAC;wBAAI,KAAK,QAAQ;;mBAJ3B,KAAK,QAAQ;;;;gEAOtB,cACG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,UACnC,GAAG,CAAC,CAAC,qBACJ,8OAAC,4NAAA,CAAA,aAAU;oBAET,OAAO,KAAK,QAAQ;oBACpB,WAAW,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;;wBAE/C,KAAK,QAAQ;wBAAC;wBAAI,KAAK,QAAQ;;mBAJ3B,KAAK,QAAQ;;;;;;;;;;;AASlC;uCAEe", "debugId": null}}, {"offset": {"line": 2172, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdkppn.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\r\nimport MyContext from \"@/stores/data/Context\";\r\nimport data from \"@/data/Kdkppn.json\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdkppn = (props) => {\r\n  const { role, kdkppn } = useContext(MyContext);\r\n\r\n  const handleSelectionChange = (keys) => {\r\n    const val = Array.from(keys)[0] || \"XX\";\r\n    if (props.onChange) {\r\n      props.onChange(val);\r\n    }\r\n  };\r\n\r\n  // Get the filter kanwil value (if provided)\r\n  const filterKdkanwil = props.kdkanwil;\r\n\r\n  // Filter KPPN data based on kanwil selection\r\n  const availableData = data.filter((item) => {\r\n    // If kdkanwil is \"XX\" or falsy (no specific kanwil selected), show no additional kppn\r\n    // Only show kppn when a specific kanwil is selected\r\n    // Also exclude kdkppn=\"XX\" to avoid duplicate keys with hardcoded \"Semua KPPN\"\r\n    return (\r\n      filterKdkanwil &&\r\n      filterKdkanwil !== \"XX\" &&\r\n      item.kdkanwil === filterKdkanwil &&\r\n      item.kdkppn !== \"XX\"\r\n    );\r\n  });\r\n\r\n  // Role-based filtering with fallback for empty/undefined roles\r\n  let options = [];\r\n  const shouldShowAll =\r\n    role === \"0\" || role === \"1\" || role === \"X\" || !role || role === \"\";\r\n\r\n  if (shouldShowAll) {\r\n    options.push(\r\n      <SelectItem key=\"XX\" value=\"XX\" textValue=\"Semua KPPN\">\r\n        Semua KPPN\r\n      </SelectItem>\r\n    );\r\n    options = options.concat(\r\n      availableData.map((item) => (\r\n        <SelectItem\r\n          key={item.kdkppn}\r\n          value={item.kdkppn}\r\n          textValue={`${item.kdkppn} - ${item.nmkppn}`}\r\n        >\r\n          {item.kdkppn} - {item.nmkppn}\r\n        </SelectItem>\r\n      ))\r\n    );\r\n  } else if (role === \"3\") {\r\n    options = availableData\r\n      .filter((item) => item.kdkppn === kdkppn)\r\n      .map((item) => (\r\n        <SelectItem\r\n          key={item.kdkppn}\r\n          value={item.kdkppn}\r\n          textValue={`${item.kdkppn} - ${item.nmkppn}`}\r\n        >\r\n          {item.kdkppn} - {item.nmkppn}\r\n        </SelectItem>\r\n      ));\r\n  } else {\r\n    options.push(\r\n      <SelectItem key=\"XX\" value=\"XX\" textValue=\"Semua KPPN\">\r\n        Semua KPPN\r\n      </SelectItem>\r\n    );\r\n  }\r\n\r\n  // Ensure the selected value is valid\r\n  const availableKeys = [\"XX\", ...availableData.map((item) => item.kdkppn)];\r\n  const currentValue = props.value || \"XX\";\r\n  const validSelectedValue = availableKeys.includes(currentValue)\r\n    ? currentValue\r\n    : \"XX\";\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"mt-2\">\r\n        <Select\r\n          selectedKeys={[validSelectedValue]}\r\n          onSelectionChange={handleSelectionChange}\r\n          className={\r\n            props.className || \"form-select form-select-sm text-select mb-2\"\r\n          }\r\n          size={props.size || \"sm\"}\r\n          aria-label=\"Pilih KPPN\"\r\n          isDisabled={props.isDisabled || props.status !== \"pilihkppn\"}\r\n          disallowEmptySelection\r\n          placeholder={props.placeholder || \"Pilih KPPN\"}\r\n        >\r\n          {options}\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Kdkppn;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;AAEA;AAAA;;;;;;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iIAAA,CAAA,UAAS;IAE7C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACnC,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC;QACjB;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB,MAAM,QAAQ;IAErC,6CAA6C;IAC7C,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC;QACjC,sFAAsF;QACtF,oDAAoD;QACpD,+EAA+E;QAC/E,OACE,kBACA,mBAAmB,QACnB,KAAK,QAAQ,KAAK,kBAClB,KAAK,MAAM,KAAK;IAEpB;IAEA,+DAA+D;IAC/D,IAAI,UAAU,EAAE;IAChB,MAAM,gBACJ,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,CAAC,QAAQ,SAAS;IAEpE,IAAI,eAAe;QACjB,QAAQ,IAAI,eACV,8OAAC,4NAAA,CAAA,aAAU;YAAU,OAAM;YAAK,WAAU;sBAAa;WAAvC;;;;;QAIlB,UAAU,QAAQ,MAAM,CACtB,cAAc,GAAG,CAAC,CAAC,qBACjB,8OAAC,4NAAA,CAAA,aAAU;gBAET,OAAO,KAAK,MAAM;gBAClB,WAAW,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE;;oBAE3C,KAAK,MAAM;oBAAC;oBAAI,KAAK,MAAM;;eAJvB,KAAK,MAAM;;;;;IAQxB,OAAO,IAAI,SAAS,KAAK;QACvB,UAAU,cACP,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK,QACjC,GAAG,CAAC,CAAC,qBACJ,8OAAC,4NAAA,CAAA,aAAU;gBAET,OAAO,KAAK,MAAM;gBAClB,WAAW,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE;;oBAE3C,KAAK,MAAM;oBAAC;oBAAI,KAAK,MAAM;;eAJvB,KAAK,MAAM;;;;;IAOxB,OAAO;QACL,QAAQ,IAAI,eACV,8OAAC,4NAAA,CAAA,aAAU;YAAU,OAAM;YAAK,WAAU;sBAAa;WAAvC;;;;;IAIpB;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QAAC;WAAS,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM;KAAE;IACzE,MAAM,eAAe,MAAM,KAAK,IAAI;IACpC,MAAM,qBAAqB,cAAc,QAAQ,CAAC,gBAC9C,eACA;IAEJ,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4MAAA,CAAA,SAAM;gBACL,cAAc;oBAAC;iBAAmB;gBAClC,mBAAmB;gBACnB,WACE,MAAM,SAAS,IAAI;gBAErB,MAAM,MAAM,IAAI,IAAI;gBACpB,cAAW;gBACX,YAAY,MAAM,UAAU,IAAI,MAAM,MAAM,KAAK;gBACjD,sBAAsB;gBACtB,aAAa,MAAM,WAAW,IAAI;0BAEjC;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/reference/referensi_inquiryMod/Kdsatker.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport data from \"@/data/Kdsatker.json\";\r\nimport { Select, SelectItem } from \"@heroui/react\";\r\n\r\nconst Kdsatker = (props) => {\r\n  const { isDisabled, ...otherProps } = props;\r\n\r\n  // Filter satker based on all parent filters\r\n  const filteredSatker = data.filter((item) => {\r\n    // Check Kementerian filter\r\n    if (props.kddept && props.kddept !== \"XX\" && item.kddept !== props.kddept) {\r\n      return false;\r\n    }\r\n\r\n    // Check Unit filter\r\n    if (props.kdunit && props.kdunit !== \"XX\" && item.kdunit !== props.kdunit) {\r\n      return false;\r\n    }\r\n\r\n    // Check Lokasi (Province) filter\r\n    if (\r\n      props.kdlokasi &&\r\n      props.kdlokasi !== \"XX\" &&\r\n      item.kdlokasi !== props.kdlokasi\r\n    ) {\r\n      return false;\r\n    }\r\n\r\n    // Check KPPN filter\r\n    if (props.kdkppn && props.kdkppn !== \"XX\" && item.kdkppn !== props.kdkppn) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"mt-2\">\r\n        <Select\r\n          selectedKeys={props.value ? new Set([props.value]) : new Set([\"XX\"])}\r\n          onSelectionChange={(keys) => {\r\n            const selected = Array.from(keys)[0] || \"XX\";\r\n            if (props.onChange) {\r\n              props.onChange(selected);\r\n            }\r\n          }}\r\n          className={props.className || \"form-select form-select-sm\"}\r\n          aria-label=\"Pilih Satker\"\r\n          isDisabled={isDisabled || props.status !== \"pilihsatker\"}\r\n          disallowEmptySelection={false}\r\n          placeholder={props.placeholder || \"Pilih Satker\"}\r\n          size={props.size || \"sm\"}\r\n        >\r\n          <SelectItem key=\"XX\" value=\"XX\" textValue=\"Semua Satker\">\r\n            Semua Satker\r\n          </SelectItem>\r\n          {filteredSatker.map((item) => (\r\n            <SelectItem\r\n              key={item.kdsatker}\r\n              value={item.kdsatker}\r\n              textValue={`${item.kdsatker} - ${item.nmsatker}`}\r\n            >\r\n              {item.kdsatker} - {item.nmsatker}\r\n            </SelectItem>\r\n          ))}\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Kdsatker;\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;AAAA;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,GAAG;IAEtC,4CAA4C;IAC5C,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAC;QAClC,2BAA2B;QAC3B,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACzE,OAAO;QACT;QAEA,oBAAoB;QACpB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACzE,OAAO;QACT;QAEA,iCAAiC;QACjC,IACE,MAAM,QAAQ,IACd,MAAM,QAAQ,KAAK,QACnB,KAAK,QAAQ,KAAK,MAAM,QAAQ,EAChC;YACA,OAAO;QACT;QAEA,oBAAoB;QACpB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACzE,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4MAAA,CAAA,SAAM;gBACL,cAAc,MAAM,KAAK,GAAG,IAAI,IAAI;oBAAC,MAAM,KAAK;iBAAC,IAAI,IAAI,IAAI;oBAAC;iBAAK;gBACnE,mBAAmB,CAAC;oBAClB,MAAM,WAAW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;oBACxC,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,QAAQ,CAAC;oBACjB;gBACF;gBACA,WAAW,MAAM,SAAS,IAAI;gBAC9B,cAAW;gBACX,YAAY,cAAc,MAAM,MAAM,KAAK;gBAC3C,wBAAwB;gBACxB,aAAa,MAAM,WAAW,IAAI;gBAClC,MAAM,MAAM,IAAI,IAAI;;kCAEpB,8OAAC,4NAAA,CAAA,aAAU;wBAAU,OAAM;wBAAK,WAAU;kCAAe;uBAAzC;;;;;oBAGf,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4NAAA,CAAA,aAAU;4BAET,OAAO,KAAK,QAAQ;4BACpB,WAAW,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE;;gCAE/C,KAAK,QAAQ;gCAAC;gCAAI,KAAK,QAAQ;;2BAJ3B,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;AAWhC;uCAEe", "debugId": null}}]}