{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/filterDOMProps.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/filterDOMProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps, GlobalDOMAttributes, LinkDOMProps} from '@react-types/shared';\n\nconst DOMPropNames = new Set([\n  'id'\n]);\n\nconst labelablePropNames = new Set([\n  'aria-label',\n  'aria-labelledby',\n  'aria-describedby',\n  'aria-details'\n]);\n\n// See LinkDOMProps in dom.d.ts.\nconst linkPropNames = new Set([\n  'href',\n  'hrefLang',\n  'target',\n  'rel',\n  'download',\n  'ping',\n  'referrerPolicy'\n]);\n\nconst globalAttrs = new Set([\n  'dir',\n  'lang',\n  'hidden',\n  'inert',\n  'translate'\n]);\n\nconst globalEvents = new Set([\n  'onClick',\n  'onAuxClick',\n  'onContextMenu',\n  'onDoubleClick',\n  'onMouseDown',\n  'onMouseEnter',\n  'onMouseLeave',\n  'onMouseMove',\n  'onMouseOut',\n  'onMouseOver',\n  'onMouseUp',\n  'onTouchCancel',\n  'onTouchEnd',\n  'onTouchMove',\n  'onTouchStart',\n  'onPointerDown',\n  'onPointerMove',\n  'onPointerUp',\n  'onPointerCancel',\n  'onPointerEnter',\n  'onPointerLeave',\n  'onPointerOver',\n  'onPointerOut',\n  'onGotPointerCapture',\n  'onLostPointerCapture',\n  'onScroll',\n  'onWheel',\n  'onAnimationStart',\n  'onAnimationEnd',\n  'onAnimationIteration',\n  'onTransitionCancel',\n  'onTransitionEnd',\n  'onTransitionRun',\n  'onTransitionStart'\n]);\n\ninterface Options {\n  /**\n   * If labelling associated aria properties should be included in the filter.\n   */\n  labelable?: boolean,\n  /** Whether the element is a link and should include DOM props for <a> elements. */\n  isLink?: boolean,\n  /** Whether to include global DOM attributes. */\n  global?: boolean,\n  /** Whether to include DOM events. */\n  events?: boolean,\n  /**\n   * A Set of other property names that should be included in the filter.\n   */\n  propNames?: Set<string>\n}\n\nconst propRe = /^(data-.*)$/;\n\n/**\n * Filters out all props that aren't valid DOM props or defined via override prop obj.\n * @param props - The component props to be filtered.\n * @param opts - Props to override.\n */\nexport function filterDOMProps(props: DOMProps & AriaLabelingProps & LinkDOMProps & GlobalDOMAttributes, opts: Options = {}): DOMProps & AriaLabelingProps & GlobalDOMAttributes {\n  let {labelable, isLink, global, events = global, propNames} = opts;\n  let filteredProps = {};\n\n  for (const prop in props) {\n    if (\n      Object.prototype.hasOwnProperty.call(props, prop) && (\n        DOMPropNames.has(prop) ||\n        (labelable && labelablePropNames.has(prop)) ||\n        (isLink && linkPropNames.has(prop)) ||\n        (global && globalAttrs.has(prop)) ||\n        (events && globalEvents.has(prop) || (prop.endsWith('Capture') && globalEvents.has(prop.slice(0, -7)))) ||\n        propNames?.has(prop) ||\n        propRe.test(prop)\n      )\n    ) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAID,MAAM,qCAAe,IAAI,IAAI;IAC3B;CACD;AAED,MAAM,2CAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;CACD;AAED,gCAAgC;AAChC,MAAM,sCAAgB,IAAI,IAAI;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,oCAAc,IAAI,IAAI;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qCAAe,IAAI,IAAI;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAmBD,MAAM,+BAAS;AAOR,SAAS,0CAAe,KAAwE;eAAE,iEAAgB,CAAC,CAAC;IACzH,IAAI,EAAA,WAAC,SAAS,EAAA,QAAE,MAAM,EAAA,QAAE,MAAM,EAAA,QAAE,SAAS,MAAA,EAAA,WAAQ,SAAS,EAAC,GAAG;IAC9D,IAAI,gBAAgB,CAAC;IAErB,IAAK,MAAM,QAAQ,MACjB,IACE,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,SAC1C,CAAA,mCAAa,GAAG,CAAC,SAChB,aAAa,yCAAmB,GAAG,CAAC,SACpC,UAAU,oCAAc,GAAG,CAAC,SAC5B,UAAU,kCAAY,GAAG,CAAC,SAC1B,UAAU,mCAAa,GAAG,CAAC,SAAU,KAAK,QAAQ,CAAC,cAAc,mCAAa,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAA,OAAA,CACjG,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,GAAG,CAAC,KAAA,KACf,6BAAO,IAAI,CAAC,KAAI,GAGlB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;IAIrC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/chain.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/chain.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Calls all functions in the order they were chained with the same arguments.\n */\nexport function chain(...callbacks: any[]): (...args: any[]) => void {\n  return (...args: any[]) => {\n    for (let callback of callbacks) {\n      if (typeof callback === 'function') {\n        callback(...args);\n      }\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;CAEC;;;AACM,SAAS;qCAAS;oCAAH,KAAmB;;IACvC,OAAO,CAAC;;YAAG;;QACT,KAAK,IAAI,YAAY,UACnB,IAAI,OAAO,aAAa,YACtB,YAAY;IAGlB;AACF", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useLayoutEffect.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React from 'react';\n\n// During SSR, React emits a warning when calling useLayoutEffect.\n// Since neither useLayoutEffect nor useEffect run on the server,\n// we can suppress this by replace it with a noop on the server.\nexport const useLayoutEffect: typeof React.useLayoutEffect = typeof document !== 'undefined'\n  ? React.useLayoutEffect\n  : () => {};\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAOM,MAAM,4CAAgD,OAAO,aAAa,cAC7E,CAAA,iKAAA,UAAI,EAAE,eAAe,GACrB,KAAO", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useEffectEvent.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useEffectEvent.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React, {useCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\n// Use the earliest effect type possible. useInsertionEffect runs during the mutation phase,\n// before all layout effects, but is available only in React 18 and later.\nconst useEarlyEffect = React['useInsertionEffect'] ?? useLayoutEffect;\n\nexport function useEffectEvent<T extends Function>(fn?: T): T {\n  const ref = useRef<T | null | undefined>(null);\n  useEarlyEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  // @ts-ignore\n  return useCallback<T>((...args) => {\n    const f = ref.current!;\n    return f?.(...args);\n  }, []);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,OAOsB;AAFvB,4FAA4F;AAC5F,0EAA0E;AAC1E,MAAM,uCAAiB,CAAA,kDAAA,CAAA,iKAAA,UAAI,CAAC,CAAC,qBAAqB,MAAA,QAA3B,oDAAA,KAAA,IAAA,kDAA+B,CAAA,2KAAA,kBAAc;AAE7D,SAAS,0CAAmC,EAAM;IACvD,MAAM,MAAM,CAAA,iKAAA,SAAK,EAAwB;IACzC,qCAAe;QACb,IAAI,OAAO,GAAG;IAChB,GAAG;QAAC;KAAG;IACP,aAAa;IACb,OAAO,CAAA,iKAAA,cAAU,EAAK,CAAC;;YAAG;;QACxB,MAAM,IAAI,IAAI,OAAO;QACrB,OAAO,MAAA,QAAA,MAAA,KAAA,IAAA,KAAA,IAAA,KAAO;IAChB,GAAG,EAAE;AACP", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useValueEffect.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useValueEffect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Dispatch, MutableRefObject, useRef, useState} from 'react';\nimport {useEffectEvent, useLayoutEffect} from './';\n\ntype SetValueAction<S> = (prev: S) => Generator<any, void, unknown>;\n\n// This hook works like `useState`, but when setting the value, you pass a generator function\n// that can yield multiple values. Each yielded value updates the state and waits for the next\n// layout effect, then continues the generator. This allows sequential updates to state to be\n// written linearly.\nexport function useValueEffect<S>(defaultValue: S | (() => S)): [S, Dispatch<SetValueAction<S>>] {\n  let [value, setValue] = useState(defaultValue);\n  let effect: MutableRefObject<Generator<S> | null> = useRef<Generator<S> | null>(null);\n\n  // Store the function in a ref so we can always access the current version\n  // which has the proper `value` in scope.\n  let nextRef = useEffectEvent(() => {\n    if (!effect.current) {\n      return;\n    }\n    // Run the generator to the next yield.\n    let newValue = effect.current.next();\n\n    // If the generator is done, reset the effect.\n    if (newValue.done) {\n      effect.current = null;\n      return;\n    }\n\n    // If the value is the same as the current value,\n    // then continue to the next yield. Otherwise,\n    // set the value in state and wait for the next layout effect.\n    if (value === newValue.value) {\n      nextRef();\n    } else {\n      setValue(newValue.value);\n    }\n  });\n\n  useLayoutEffect(() => {\n    // If there is an effect currently running, continue to the next yield.\n    if (effect.current) {\n      nextRef();\n    }\n  });\n\n  let queue = useEffectEvent(fn => {\n    effect.current = fn(value);\n    nextRef();\n  });\n\n  return [value, queue];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAWM,SAAS,0CAAkB,YAA2B;IAC3D,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,iKAAA,WAAO,EAAE;IACjC,IAAI,SAAgD,CAAA,iKAAA,SAAK,EAAuB;IAEhF,0EAA0E;IAC1E,yCAAyC;IACzC,IAAI,UAAU,CAAA,0KAAA,iBAAa,EAAE;QAC3B,IAAI,CAAC,OAAO,OAAO,EACjB;QAEF,uCAAuC;QACvC,IAAI,WAAW,OAAO,OAAO,CAAC,IAAI;QAElC,8CAA8C;QAC9C,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO,OAAO,GAAG;YACjB;QACF;QAEA,iDAAiD;QACjD,8CAA8C;QAC9C,8DAA8D;QAC9D,IAAI,UAAU,SAAS,KAAK,EAC1B;aAEA,SAAS,SAAS,KAAK;IAE3B;IAEA,CAAA,2KAAA,kBAAc,EAAE;QACd,uEAAuE;QACvE,IAAI,OAAO,OAAO,EAChB;IAEJ;IAEA,IAAI,QAAQ,CAAA,0KAAA,iBAAa,EAAE,CAAA;QACzB,OAAO,OAAO,GAAG,GAAG;QACpB;IACF;IAEA,OAAO;QAAC;QAAO;KAAM;AACvB", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useId.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useId.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useSSRSafeId} from '@react-aria/ssr';\nimport {useValueEffect} from './';\n\n// copied from SSRProvider.tsx to reduce exports, if needed again, consider sharing\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nexport let idsUpdaterMap: Map<string, { current: string | null }[]> = new Map();\n// This allows us to clean up the idsUpdaterMap when the id is no longer used.\n// Map is a strong reference, so unused ids wouldn't be cleaned up otherwise.\n// This can happen in suspended components where mount/unmount is not called.\nlet registry;\nif (typeof FinalizationRegistry !== 'undefined') {\n  registry = new FinalizationRegistry<string>((heldValue) => {\n    idsUpdaterMap.delete(heldValue);\n  });\n}\n\n/**\n * If a default is not provided, generate an id.\n * @param defaultId - Default component id.\n */\nexport function useId(defaultId?: string): string {\n  let [value, setValue] = useState(defaultId);\n  let nextId = useRef(null);\n\n  let res = useSSRSafeId(value);\n  let cleanupRef = useRef(null);\n\n  if (registry) {\n    registry.register(cleanupRef, res);\n  }\n\n  if (canUseDOM) {\n    const cacheIdRef = idsUpdaterMap.get(res);\n    if (cacheIdRef && !cacheIdRef.includes(nextId)) {\n      cacheIdRef.push(nextId);\n    } else {\n      idsUpdaterMap.set(res, [nextId]);\n    }\n  }\n\n  useLayoutEffect(() => {\n    let r = res;\n    return () => {\n      // In Suspense, the cleanup function may be not called\n      // when it is though, also remove it from the finalization registry.\n      if (registry) {\n        registry.unregister(cleanupRef);\n      }\n      idsUpdaterMap.delete(r);\n    };\n  }, [res]);\n\n  // This cannot cause an infinite loop because the ref is always cleaned up.\n  // eslint-disable-next-line\n  useEffect(() => {\n    let newId = nextId.current;\n    if (newId) { setValue(newId); }\n\n    return () => {\n      if (newId) { nextId.current = null; }\n    };\n  });\n\n  return res;\n}\n\n/**\n * Merges two ids.\n * Different ids will trigger a side-effect and re-render components hooked up with `useId`.\n */\nexport function mergeIds(idA: string, idB: string): string {\n  if (idA === idB) {\n    return idA;\n  }\n\n  let setIdsA = idsUpdaterMap.get(idA);\n  if (setIdsA) {\n    setIdsA.forEach(ref => (ref.current = idB));\n    return idB;\n  }\n\n  let setIdsB = idsUpdaterMap.get(idB);\n  if (setIdsB) {\n    setIdsB.forEach((ref) => (ref.current = idA));\n    return idA;\n  }\n\n  return idB;\n}\n\n/**\n * Used to generate an id, and after render, check if that id is rendered so we know\n * if we can use it in places such as labelledby.\n * @param depArray - When to recalculate if the id is in the DOM.\n */\nexport function useSlotId(depArray: ReadonlyArray<any> = []): string {\n  let id = useId();\n  let [resolvedId, setResolvedId] = useValueEffect(id);\n  let updateId = useCallback(() => {\n    setResolvedId(function *() {\n      yield id;\n\n      yield document.getElementById(id) ? id : undefined;\n    });\n  }, [id, setResolvedId]);\n\n  useLayoutEffect(updateId, [id, updateId, ...depArray]);\n\n  return resolvedId;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAOD,mFAAmF;AACnF,IAAI,kCAAY,QACd,OAAO,WAAW,eAClB,OAAO,QAAQ,IACf,OAAO,QAAQ,CAAC,aAAa;AAGxB,IAAI,4CAA2D,IAAI;AAC1E,8EAA8E;AAC9E,6EAA6E;AAC7E,6EAA6E;AAC7E,IAAI;AACJ,IAAI,OAAO,yBAAyB,aAClC,iCAAW,IAAI,qBAA6B,CAAC;IAC3C,0CAAc,MAAM,CAAC;AACvB;AAOK,SAAS,0CAAM,SAAkB;IACtC,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,iKAAA,WAAO,EAAE;IACjC,IAAI,SAAS,CAAA,iKAAA,SAAK,EAAE;IAEpB,IAAI,MAAM,CAAA,qKAAA,eAAW,EAAE;IACvB,IAAI,aAAa,CAAA,iKAAA,SAAK,EAAE;IAExB,IAAI,gCACF,+BAAS,QAAQ,CAAC,YAAY;IAGhC,IAAI,iCAAW;QACb,MAAM,aAAa,0CAAc,GAAG,CAAC;QACrC,IAAI,cAAc,CAAC,WAAW,QAAQ,CAAC,SACrC,WAAW,IAAI,CAAC;aAEhB,0CAAc,GAAG,CAAC,KAAK;YAAC;SAAO;IAEnC;IAEA,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,IAAI;QACR,OAAO;YACL,sDAAsD;YACtD,oEAAoE;YACpE,IAAI,gCACF,+BAAS,UAAU,CAAC;YAEtB,0CAAc,MAAM,CAAC;QACvB;IACF,GAAG;QAAC;KAAI;IAER,2EAA2E;IAC3E,2BAA2B;IAC3B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QAAQ,OAAO,OAAO;QAC1B,IAAI,OAAS,SAAS;QAEtB,OAAO;YACL,IAAI,OAAS,OAAO,OAAO,GAAG;QAChC;IACF;IAEA,OAAO;AACT;AAMO,SAAS,0CAAS,GAAW,EAAE,GAAW;IAC/C,IAAI,QAAQ,KACV,OAAO;IAGT,IAAI,UAAU,0CAAc,GAAG,CAAC;IAChC,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAA,MAAQ,IAAI,OAAO,GAAG;QACtC,OAAO;IACT;IAEA,IAAI,UAAU,0CAAc,GAAG,CAAC;IAChC,IAAI,SAAS;QACX,QAAQ,OAAO,CAAC,CAAC,MAAS,IAAI,OAAO,GAAG;QACxC,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS;mBAAU,iEAA+B,EAAE;IACzD,IAAI,KAAK;IACT,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,0KAAA,iBAAa,EAAE;IACjD,IAAI,WAAW,CAAA,iKAAA,cAAU,EAAE;QACzB,cAAc;YACZ,MAAM;YAEN,MAAM,SAAS,cAAc,CAAC,MAAM,KAAK;QAC3C;IACF,GAAG;QAAC;QAAI;KAAc;IAEtB,CAAA,2KAAA,kBAAc,EAAE,UAAU;QAAC;QAAI;WAAa;KAAS;IAErD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/mergeProps.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/mergeProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from './chain';\nimport clsx from 'clsx';\nimport {mergeIds} from './useId';\n\ninterface Props {\n  [key: string]: any\n}\n\ntype PropsArg = Props | null | undefined;\n\n// taken from: https://stackoverflow.com/questions/51603250/typescript-3-parameter-list-intersection-type/51604379#51604379\ntype TupleTypes<T> = { [P in keyof T]: T[P] } extends { [key: number]: infer V } ? NullToObject<V> : never;\ntype NullToObject<T> = T extends (null | undefined) ? {} : T;\n\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n/**\n * Merges multiple props objects together. Event handlers are chained,\n * classNames are combined, and ids are deduplicated - different ids\n * will trigger a side-effect and re-render components hooked up with `useId`.\n * For all other props, the last prop object overrides all previous ones.\n * @param args - Multiple sets of props to merge together.\n */\nexport function mergeProps<T extends PropsArg[]>(...args: T): UnionToIntersection<TupleTypes<T>> {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result: Props = {...args[0]};\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n\n      // Chain events\n      if (\n        typeof a === 'function' &&\n        typeof b === 'function' &&\n        // This is a lot faster than a regex.\n        key[0] === 'o' &&\n        key[1] === 'n' &&\n        key.charCodeAt(2) >= /* 'A' */ 65 &&\n        key.charCodeAt(2) <= /* 'Z' */ 90\n      ) {\n        result[key] = chain(a, b);\n\n        // Merge classnames, sometimes classNames are empty string which eval to false, so we just need to do a type check\n      } else if (\n        (key === 'className' || key === 'UNSAFE_className') &&\n        typeof a === 'string' &&\n        typeof b === 'string'\n      ) {\n        result[key] = clsx(a, b);\n      } else if (key === 'id' && a && b) {\n        result.id = mergeIds(a, b);\n        // Override others\n      } else {\n        result[key] = b !== undefined ? b : a;\n      }\n    }\n  }\n\n  return result as UnionToIntersection<TupleTypes<T>>;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAyBM,SAAS;;QAAoC,uBAAH,KAAU;;IACzD,oFAAoF;IACpF,uDAAuD;IACvD,IAAI,SAAgB;QAAC,GAAG,IAAI,CAAC,EAAE;IAAA;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,QAAQ,IAAI,CAAC,EAAE;QACnB,IAAK,IAAI,OAAO,MAAO;YACrB,IAAI,IAAI,MAAM,CAAC,IAAI;YACnB,IAAI,IAAI,KAAK,CAAC,IAAI;YAElB,eAAe;YACf,IACE,OAAO,MAAM,cACb,OAAO,MAAM,cACb,qCAAqC;YACrC,GAAG,CAAC,EAAE,KAAK,OACX,GAAG,CAAC,EAAE,KAAK,OACX,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,MAC/B,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,IAE/B,MAAM,CAAC,IAAI,GAAG,CAAA,iKAAA,QAAI,EAAE,GAAG;iBAGlB,IACJ,CAAA,QAAQ,eAAe,QAAQ,kBAAiB,KACjD,OAAO,MAAM,YACb,OAAO,MAAM,UAEb,MAAM,CAAC,IAAI,GAAG,CAAA,4IAAA,UAAG,EAAE,GAAG;iBACjB,IAAI,QAAQ,QAAQ,KAAK,GAC9B,OAAO,EAAE,GAAG,CAAA,iKAAA,WAAO,EAAE,GAAG;iBAGxB,MAAM,CAAC,IAAI,GAAG,MAAM,YAAY,IAAI;QAExC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/domHelpers.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/domHelpers.ts"], "sourcesContent": ["export const getOwnerDocument = (el: Element | null | undefined): Document => {\n  return el?.ownerDocument ?? document;\n};\n\nexport const getOwnerWindow = (\n  el: (Window & typeof global) | Element | null | undefined\n): Window & typeof global => {\n  if (el && 'window' in el && el.window === el) {\n    return el;\n  }\n\n  const doc = getOwnerDocument(el as Element | null | undefined);\n  return doc.defaultView || window;\n};\n\n/**\n * Type guard that checks if a value is a Node. Verifies the presence and type of the nodeType property.\n */\nfunction isNode(value: unknown): value is Node {\n  return value !== null &&\n    typeof value === 'object' &&\n    'nodeType' in value &&\n    typeof (value as Node).nodeType === 'number';\n}\n/**\n * Type guard that checks if a node is a ShadowRoot. Uses nodeType and host property checks to\n * distinguish ShadowRoot from other DocumentFragments.\n */\nexport function isShadowRoot(node: Node | null): node is ShadowRoot {\n  return isNode(node) &&\n    node.nodeType === Node.DOCUMENT_FRAGMENT_NODE &&\n    'host' in node;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,4CAAmB,CAAC;QACxB;IAAP,OAAO,CAAA,oBAAA,OAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAI,aAAa,MAAA,QAAjB,sBAAA,KAAA,IAAA,oBAAqB;AAC9B;AAEO,MAAM,4CAAiB,CAC5B;IAEA,IAAI,MAAM,YAAY,MAAM,GAAG,MAAM,KAAK,IACxC,OAAO;IAGT,MAAM,MAAM,0CAAiB;IAC7B,OAAO,IAAI,WAAW,IAAI;AAC5B;AAEA;;CAEC,GACD,SAAS,6BAAO,KAAc;IAC5B,OAAO,UAAU,QACf,OAAO,UAAU,YACjB,cAAc,SACd,OAAQ,MAAe,QAAQ,KAAK;AACxC;AAKO,SAAS,0CAAa,IAAiB;IAC5C,OAAO,6BAAO,SACZ,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAC7C,UAAU;AACd", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/isElementVisible.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isElementVisible.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getOwnerWindow} from './domHelpers';\n\nconst supportsCheckVisibility = typeof Element !== 'undefined' && 'checkVisibility' in Element.prototype;\n\nfunction isStyleVisible(element: Element) {\n  const windowObject = getOwnerWindow(element);\n  if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) {\n    return false;\n  }\n\n  let {display, visibility} = element.style;\n\n  let isVisible = (\n    display !== 'none' &&\n    visibility !== 'hidden' &&\n    visibility !== 'collapse'\n  );\n\n  if (isVisible) {\n    const {getComputedStyle} = element.ownerDocument.defaultView as unknown as Window;\n    let {display: computedDisplay, visibility: computedVisibility} = getComputedStyle(element);\n\n    isVisible = (\n      computedDisplay !== 'none' &&\n      computedVisibility !== 'hidden' &&\n      computedVisibility !== 'collapse'\n    );\n  }\n\n  return isVisible;\n}\n\nfunction isAttributeVisible(element: Element, childElement?: Element) {\n  return (\n    !element.hasAttribute('hidden') &&\n    // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') &&\n    (element.nodeName === 'DETAILS' &&\n      childElement &&\n      childElement.nodeName !== 'SUMMARY'\n      ? element.hasAttribute('open')\n      : true)\n  );\n}\n\n/**\n * Adapted from https://github.com/testing-library/jest-dom and\n * https://github.com/vuejs/vue-test-utils-next/.\n * Licensed under the MIT License.\n * @param element - Element to evaluate for display or visibility.\n */\nexport function isElementVisible(element: Element, childElement?: Element): boolean {\n  if (supportsCheckVisibility) {\n    return element.checkVisibility();\n  }\n  \n  return (\n    element.nodeName !== '#comment' &&\n    isStyleVisible(element) &&\n    isAttributeVisible(element, childElement) &&\n    (!element.parentElement || isElementVisible(element.parentElement, element))\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAID,MAAM,gDAA0B,OAAO,YAAY,eAAe,qBAAqB,QAAQ,SAAS;AAExG,SAAS,qCAAe,OAAgB;IACtC,MAAM,eAAe,CAAA,sKAAA,iBAAa,EAAE;IACpC,IAAI,CAAE,CAAA,mBAAmB,aAAa,WAAU,KAAM,CAAE,CAAA,mBAAmB,aAAa,UAAS,GAC/F,OAAO;IAGT,IAAI,EAAA,SAAC,OAAO,EAAA,YAAE,UAAU,EAAC,GAAG,QAAQ,KAAK;IAEzC,IAAI,YACF,YAAY,UACZ,eAAe,YACf,eAAe;IAGjB,IAAI,WAAW;QACb,MAAM,EAAA,kBAAC,gBAAgB,EAAC,GAAG,QAAQ,aAAa,CAAC,WAAW;QAC5D,IAAI,EAAC,SAAS,eAAe,EAAE,YAAY,kBAAkB,EAAC,GAAG,iBAAiB;QAElF,YACE,oBAAoB,UACpB,uBAAuB,YACvB,uBAAuB;IAE3B;IAEA,OAAO;AACT;AAEA,SAAS,yCAAmB,OAAgB,EAAE,YAAsB;IAClE,OACE,CAAC,QAAQ,YAAY,CAAC,aACtB,yCAAyC;IACzC,CAAC,QAAQ,YAAY,CAAC,oCACrB,CAAA,QAAQ,QAAQ,KAAK,aACpB,gBACA,aAAa,QAAQ,KAAK,YACxB,QAAQ,YAAY,CAAC,UACrB,IAAG;AAEX;AAQO,SAAS,0CAAiB,OAAgB,EAAE,YAAsB;IACvE,IAAI,+CACF,OAAO,QAAQ,eAAe;IAGhC,OACE,QAAQ,QAAQ,KAAK,cACrB,qCAAe,YACf,yCAAmB,SAAS,iBAC3B,CAAA,CAAC,QAAQ,aAAa,IAAI,0CAAiB,QAAQ,aAAa,EAAE,QAAO;AAE9E", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/isFocusable.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isFocusable.ts"], "sourcesContent": ["/*\n * Copyright 2025 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isElementVisible} from './isElementVisible';\n\nconst focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])',\n  'permission'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nfunction isInert(element: Element): boolean {\n  let node: Element | null = element;\n  while (node != null) {\n    if (node instanceof node.ownerDocument.defaultView!.HTMLElement && node.inert) {\n      return true;\n    }\n\n    node = node.parentElement;\n  }\n\n  return false;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAID,MAAM,0CAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mDAA6B,wCAAkB,IAAI,CAAC,qBAAqB;AAE/E,wCAAkB,IAAI,CAAC;AACvB,MAAM,kDAA4B,wCAAkB,IAAI,CAAC;AAElD,SAAS,0CAAY,OAAgB;IAC1C,OAAO,QAAQ,OAAO,CAAC,qDAA+B,CAAA,4KAAA,mBAAe,EAAE,YAAY,CAAC,8BAAQ;AAC9F;AAEO,SAAS,0CAAW,OAAgB;IACzC,OAAO,QAAQ,OAAO,CAAC,oDAA8B,CAAA,4KAAA,mBAAe,EAAE,YAAY,CAAC,8BAAQ;AAC7F;AAEA,SAAS,8BAAQ,OAAgB;IAC/B,IAAI,OAAuB;IAC3B,MAAO,QAAQ,KAAM;QACnB,IAAI,gBAAgB,KAAK,aAAa,CAAC,WAAW,CAAE,WAAW,IAAI,KAAK,KAAK,EAC3E,OAAO;QAGT,OAAO,KAAK,aAAa;IAC3B;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/isVirtualEvent.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isVirtualEvent.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAeM,SAAS,0CAAe,KAAgC;IAC7D,0BAA0B;IAC1B,IAAK,MAAc,cAAc,KAAK,KAAK,MAAM,SAAS,EACxD,OAAO;IAGT,oIAAoI;IACpI,oIAAoI;IACpI,qCAAqC;IACrC,IAAI,CAAA,oKAAA,YAAQ,OAAQ,MAAuB,WAAW,EACpD,OAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,KAAK;IAGrD,OAAO,MAAM,MAAM,KAAK,KAAK,CAAE,MAAuB,WAAW;AACnE;AAEO,SAAS,0CAAsB,KAAmB;IACvD,yEAAyE;IACzE,uFAAuF;IACvF,kGAAkG;IAClG,mHAAmH;IACnH,oHAAoH;IACpH,8DAA8D;IAC9D,OACG,CAAC,CAAA,oKAAA,YAAQ,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KACtD,MAAM,KAAK,KAAK,KACf,MAAM,MAAM,KAAK,KACjB,MAAM,QAAQ,KAAK,KACnB,MAAM,MAAM,KAAK,KACjB,MAAM,WAAW,KAAK;AAG5B", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/DOMFunctions.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/shadowdom/DOMFunctions.ts"], "sourcesContent": ["// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,kIAAkI;AAQ3H,SAAS,0CACd,IAA6B,EAC7B,SAAkC;IAElC,IAAI,CAAC,CAAA,qKAAA,YAAQ,KACX,OAAO,aAAa,OAAO,KAAK,QAAQ,CAAC,aAAa;IAGxD,IAAI,CAAC,QAAQ,CAAC,WACZ,OAAO;IAGT,IAAI,cAAqD;IAEzD,MAAO,gBAAgB,KAAM;QAC3B,IAAI,gBAAgB,MAClB,OAAO;QAGT,IAAK,YAAgC,OAAO,KAAK,UAC9C,YAAgC,YAAY,EAC7C,AACA,cAAe,OADM,KAC0B,YAAY,CAAE,UAAU;aAClE,IAAI,CAAA,sKAAA,eAAW,EAAE,cACtB,AACA,cAAc,YAAY,EADE,EACE;aAE9B,cAAc,YAAY,UAAU;IAExC;IAEA,OAAO;AACT;AAKO,MAAM,4CAAmB;QAAC,uEAAgB,QAAQ;QAOvD;IANA,IAAI,CAAC,CAAA,qKAAA,YAAQ,KACX,OAAO,IAAI,aAAa;IAE1B,IAAI,gBAAgC,IAAI,aAAa;IAErD,MAAO,iBAAiB,gBAAgB,iBAAA,CAAA,CACxC,4BAAA,cAAc,UAAU,MAAA,QAAxB,8BAAA,KAAA,IAAA,KAAA,IAAA,0BAA0B,aAAa,EACrC,gBAAgB,cAAc,UAAU,CAAC,aAAa;IAGxD,OAAO;AACT;AAKO,SAAS,0CAAgC,KAAQ;IACtD,IAAI,CAAA,qKAAA,YAAQ,OAAQ,MAAM,MAAM,CAAiB,UAAU,EAAE;QAC3D,IAAI,MAAM,YAAY,EACpB,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAElC;IACA,OAAO,MAAM,MAAM;AACrB", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/runAfterTransition.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/runAfterTransition.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We store a global list of elements that are currently transitioning,\n// mapped to a set of CSS properties that are transitioning for that element.\n// This is necessary rather than a simple count of transitions because of browser\n// bugs, e.g. Chrome sometimes fires both transitionend and transitioncancel rather\n// than one or the other. So we need to track what's actually transitioning so that\n// we can ignore these duplicate events.\nlet transitionsByElement = new Map<EventTarget, Set<string>>();\n\n// A list of callbacks to call once there are no transitioning elements.\nlet transitionCallbacks = new Set<() => void>();\n\nfunction setupGlobalEvents() {\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  function isTransitionEvent(event: Event): event is TransitionEvent {\n    return 'propertyName' in event;\n  }\n\n  let onTransitionStart = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Add the transitioning property to the list for this element.\n    let transitions = transitionsByElement.get(e.target);\n    if (!transitions) {\n      transitions = new Set();\n      transitionsByElement.set(e.target, transitions);\n\n      // The transitioncancel event must be registered on the element itself, rather than as a global\n      // event. This enables us to handle when the node is deleted from the document while it is transitioning.\n      // In that case, the cancel event would have nowhere to bubble to so we need to handle it directly.\n      e.target.addEventListener('transitioncancel', onTransitionEnd, {\n        once: true\n      });\n    }\n\n    transitions.add(e.propertyName);\n  };\n\n  let onTransitionEnd = (e: Event) => {\n    if (!isTransitionEvent(e) || !e.target) {\n      return;\n    }\n    // Remove property from list of transitioning properties.\n    let properties = transitionsByElement.get(e.target);\n    if (!properties) {\n      return;\n    }\n\n    properties.delete(e.propertyName);\n\n    // If empty, remove transitioncancel event, and remove the element from the list of transitioning elements.\n    if (properties.size === 0) {\n      e.target.removeEventListener('transitioncancel', onTransitionEnd);\n      transitionsByElement.delete(e.target);\n    }\n\n    // If no transitioning elements, call all of the queued callbacks.\n    if (transitionsByElement.size === 0) {\n      for (let cb of transitionCallbacks) {\n        cb();\n      }\n\n      transitionCallbacks.clear();\n    }\n  };\n\n  document.body.addEventListener('transitionrun', onTransitionStart);\n  document.body.addEventListener('transitionend', onTransitionEnd);\n}\n\nif (typeof document !== 'undefined') {\n  if (document.readyState !== 'loading') {\n    setupGlobalEvents();\n  } else {\n    document.addEventListener('DOMContentLoaded', setupGlobalEvents);\n  }\n}\n\n/**\n * Cleans up any elements that are no longer in the document.\n * This is necessary because we can't rely on transitionend events to fire\n * for elements that are removed from the document while transitioning.\n */\nfunction cleanupDetachedElements() {\n  for (const [eventTarget] of transitionsByElement) {\n    // Similar to `eventTarget instanceof Element && !eventTarget.isConnected`, but avoids\n    // the explicit instanceof check, since it may be different in different contexts.\n    if ('isConnected' in eventTarget && !eventTarget.isConnected) {\n      transitionsByElement.delete(eventTarget);\n    }\n  }\n}\n\nexport function runAfterTransition(fn: () => void): void {\n  // Wait one frame to see if an animation starts, e.g. a transition on mount.\n  requestAnimationFrame(() => {\n    cleanupDetachedElements();\n    // If no transitions are running, call the function immediately.\n    // Otherwise, add it to a list of callbacks to run at the end of the animation.\n    if (transitionsByElement.size === 0) {\n      fn();\n    } else {\n      transitionCallbacks.add(fn);\n    }\n  });\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED,uEAAuE;AACvE,6EAA6E;AAC7E,iFAAiF;AACjF,mFAAmF;AACnF,mFAAmF;AACnF,wCAAwC;;;;AACxC,IAAI,6CAAuB,IAAI;AAE/B,wEAAwE;AACxE,IAAI,4CAAsB,IAAI;AAE9B,SAAS;IACP,IAAI,OAAO,WAAW,aACpB;IAGF,SAAS,kBAAkB,KAAY;QACrC,OAAO,kBAAkB;IAC3B;IAEA,IAAI,oBAAoB,CAAC;QACvB,IAAI,CAAC,kBAAkB,MAAM,CAAC,EAAE,MAAM,EACpC;QAEF,+DAA+D;QAC/D,IAAI,cAAc,2CAAqB,GAAG,CAAC,EAAE,MAAM;QACnD,IAAI,CAAC,aAAa;YAChB,cAAc,IAAI;YAClB,2CAAqB,GAAG,CAAC,EAAE,MAAM,EAAE;YAEnC,+FAA+F;YAC/F,yGAAyG;YACzG,mGAAmG;YACnG,EAAE,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,iBAAiB;gBAC7D,MAAM;YACR;QACF;QAEA,YAAY,GAAG,CAAC,EAAE,YAAY;IAChC;IAEA,IAAI,kBAAkB,CAAC;QACrB,IAAI,CAAC,kBAAkB,MAAM,CAAC,EAAE,MAAM,EACpC;QAEF,yDAAyD;QACzD,IAAI,aAAa,2CAAqB,GAAG,CAAC,EAAE,MAAM;QAClD,IAAI,CAAC,YACH;QAGF,WAAW,MAAM,CAAC,EAAE,YAAY;QAEhC,2GAA2G;QAC3G,IAAI,WAAW,IAAI,KAAK,GAAG;YACzB,EAAE,MAAM,CAAC,mBAAmB,CAAC,oBAAoB;YACjD,2CAAqB,MAAM,CAAC,EAAE,MAAM;QACtC;QAEA,kEAAkE;QAClE,IAAI,2CAAqB,IAAI,KAAK,GAAG;YACnC,KAAK,IAAI,MAAM,0CACb;YAGF,0CAAoB,KAAK;QAC3B;IACF;IAEA,SAAS,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;IAChD,SAAS,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;AAClD;AAEA,IAAI,OAAO,aAAa,aAAA;IACtB,IAAI,SAAS,UAAU,KAAK,WAC1B;SAEA,SAAS,gBAAgB,CAAC,oBAAoB;;AAIlD;;;;CAIC,GACD,SAAS;IACP,KAAK,MAAM,CAAC,YAAY,IAAI,2CAC1B,AACA,kFAAkF,IADI;IAEtF,IAAI,iBAAiB,eAAe,CAAC,YAAY,WAAW,EAC1D,2CAAqB,MAAM,CAAC;AAGlC;AAEO,SAAS,0CAAmB,EAAc;IAC/C,4EAA4E;IAC5E,sBAAsB;QACpB;QACA,gEAAgE;QAChE,+EAA+E;QAC/E,IAAI,2CAAqB,IAAI,KAAK,GAChC;aAEA,0CAAoB,GAAG,CAAC;IAE5B;AACF", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useSyncRef.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useSyncRef.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from './';\n\ninterface ContextValue<T> {\n  ref?: MutableRefObject<T | null>\n}\n\n// Syncs ref from context with ref passed to hook\nexport function useSyncRef<T>(context?: ContextValue<T> | null, ref?: RefObject<T | null>): void {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) {\n          context.ref.current = null;\n        }\n      };\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAWM,SAAS,0CAAc,OAAgC,EAAE,GAAyB;IACvF,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,WAAW,QAAQ,GAAG,IAAI,KAAK;YACjC,QAAQ,GAAG,CAAC,OAAO,GAAG,IAAI,OAAO;YACjC,OAAO;gBACL,IAAI,QAAQ,GAAG,EACb,QAAQ,GAAG,CAAC,OAAO,GAAG;YAE1B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useObjectRef.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useObjectRef.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, useCallback, useMemo, useRef} from 'react';\n\n/**\n * Offers an object ref for a given callback ref or an object ref. Especially\n * helfpul when passing forwarded refs (created using `React.forwardRef`) to\n * React Aria hooks.\n *\n * @param ref The original ref intended to be used.\n * @returns An object ref that updates the given ref.\n * @see https://react.dev/reference/react/forwardRef\n */\nexport function useObjectRef<T>(ref?: ((instance: T | null) => (() => void) | void) | MutableRefObject<T | null> | null): MutableRefObject<T | null> {\n  const objRef: MutableRefObject<T | null> = useRef<T>(null);\n  const cleanupRef: MutableRefObject<(() => void) | void> = useRef(undefined);\n\n  const refEffect = useCallback(\n    (instance: T | null) => {\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return () => {\n          if (typeof refCleanup === 'function') {\n            refCleanup();\n          } else {\n            refCallback(null);\n          }\n        };\n      } else if (ref) {\n        ref.current = instance;\n        return () => {\n          ref.current = null;\n        };\n      }\n    },\n    [ref]\n  );\n\n  return useMemo(\n    () => ({\n      get current() {\n        return objRef.current;\n      },\n      set current(value) {\n        objRef.current = value;\n        if (cleanupRef.current) {\n          cleanupRef.current();\n          cleanupRef.current = undefined;\n        }\n\n        if (value != null) {\n          cleanupRef.current = refEffect(value);\n        }\n      }\n    }),\n    [refEffect]\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS,0CAAgB,GAAuF;IACrH,MAAM,SAAqC,CAAA,iKAAA,SAAK,EAAK;IACrD,MAAM,aAAoD,CAAA,iKAAA,SAAK,EAAE;IAEjE,MAAM,YAAY,CAAA,iKAAA,cAAU,EAC1B,CAAC;QACC,IAAI,OAAO,QAAQ,YAAY;YAC7B,MAAM,cAAc;YACpB,MAAM,aAAa,YAAY;YAC/B,OAAO;gBACL,IAAI,OAAO,eAAe,YACxB;qBAEA,YAAY;YAEhB;QACF,OAAO,IAAI,KAAK;YACd,IAAI,OAAO,GAAG;YACd,OAAO;gBACL,IAAI,OAAO,GAAG;YAChB;QACF;IACF,GACA;QAAC;KAAI;IAGP,OAAO,CAAA,iKAAA,UAAM,EACX,IAAO,CAAA;YACL,IAAI,WAAU;gBACZ,OAAO,OAAO,OAAO;YACvB;YACA,IAAI,SAAQ,MAAO;gBACjB,OAAO,OAAO,GAAG;gBACjB,IAAI,WAAW,OAAO,EAAE;oBACtB,WAAW,OAAO;oBAClB,WAAW,OAAO,GAAG;gBACvB;gBAEA,IAAI,SAAS,MACX,WAAW,OAAO,GAAG,UAAU;YAEnC;QACF,CAAA,GACA;QAAC;KAAU;AAEf", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/mergeRefs.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/mergeRefs.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, Ref} from 'react';\n\n/**\n * Merges multiple refs into one. Works with either callback or object refs.\n */\nexport function mergeRefs<T>(...refs: Array<Ref<T> | MutableRefObject<T> | null | undefined>): Ref<T> {\n  if (refs.length === 1 && refs[0]) {\n    return refs[0];\n  }\n\n  return (value: T | null) => {\n    let hasCleanup = false;\n\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, value);\n      hasCleanup ||= typeof cleanup == 'function';\n      return cleanup;\n    });\n\n    if (hasCleanup) {\n      return () => {\n        cleanups.forEach((cleanup, i) => {\n          if (typeof cleanup === 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        });\n      };\n    }\n  };\n}\n\nfunction setRef<T>(ref: Ref<T> | MutableRefObject<T> | null | undefined, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref != null) {\n    ref.current = value;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAOM,SAAS;;QAAgB,uBAAH,KAA+D;;IAC1F,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,EAC9B,OAAO,IAAI,CAAC,EAAE;IAGhB,OAAO,CAAC;QACN,IAAI,aAAa;QAEjB,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA;YACxB,MAAM,UAAU,6BAAO,KAAK;YAC5B,cAAA,CAAA,aAAe,OAAO,WAAW,UAAA;YACjC,OAAO;QACT;QAEA,IAAI,YACF,OAAO;YACL,SAAS,OAAO,CAAC,CAAC,SAAS;gBACzB,IAAI,OAAO,YAAY,YACrB;qBAEA,6BAAO,IAAI,CAAC,EAAE,EAAE;YAEpB;QACF;IAEJ;AACF;AAEA,SAAS,6BAAU,GAAoD,EAAE,KAAQ;IAC/E,IAAI,OAAO,QAAQ,YACjB,OAAO,IAAI;SACN,IAAI,OAAO,MAChB,IAAI,OAAO,GAAG;AAElB", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useGlobalListeners.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useGlobalListeners.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef} from 'react';\n\ninterface GlobalListeners {\n  addGlobalListener<K extends keyof WindowEventMap>(el: Window, type: K, listener: (this: Document, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void,\n  addGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void,\n  removeGlobalListener<K extends keyof DocumentEventMap>(el: EventTarget, type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void,\n  removeGlobalListener(el: EventTarget, type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void,\n  removeAllGlobalListeners(): void\n}\n\nexport function useGlobalListeners(): GlobalListeners {\n  let globalListeners = useRef(new Map());\n  let addGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    // Make sure we remove the listener after it is called with the `once` option.\n    let fn = options?.once ? (...args) => {\n      globalListeners.current.delete(listener);\n      listener(...args);\n    } : listener;\n    globalListeners.current.set(listener, {type, eventTarget, fn, options});\n    eventTarget.addEventListener(type, fn, options);\n  }, []);\n  let removeGlobalListener = useCallback((eventTarget, type, listener, options) => {\n    let fn = globalListeners.current.get(listener)?.fn || listener;\n    eventTarget.removeEventListener(type, fn, options);\n    globalListeners.current.delete(listener);\n  }, []);\n  let removeAllGlobalListeners = useCallback(() => {\n    globalListeners.current.forEach((value, key) => {\n      removeGlobalListener(value.eventTarget, value.type, key, value.options);\n    });\n  }, [removeGlobalListener]);\n\n   \n  useEffect(() => {\n    return removeAllGlobalListeners;\n  }, [removeAllGlobalListeners]);\n\n  return {addGlobalListener, removeGlobalListener, removeAllGlobalListeners};\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS;IACd,IAAI,kBAAkB,CAAA,iKAAA,SAAK,EAAE,IAAI;IACjC,IAAI,oBAAoB,CAAA,iKAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;QAChE,8EAA8E;QAC9E,IAAI,KAAK,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,IAAI,IAAG,CAAC;;gBAAG;;YAC3B,gBAAgB,OAAO,CAAC,MAAM,CAAC;YAC/B,YAAY;QACd,IAAI;QACJ,gBAAgB,OAAO,CAAC,GAAG,CAAC,UAAU;kBAAC;yBAAM;gBAAa;qBAAI;QAAO;QACrE,YAAY,gBAAgB,CAAC,MAAM,IAAI;IACzC,GAAG,EAAE;IACL,IAAI,uBAAuB,CAAA,iKAAA,cAAU,EAAE,CAAC,aAAa,MAAM,UAAU;YAC1D;QAAT,IAAI,KAAK,CAAA,CAAA,+BAAA,gBAAgB,OAAO,CAAC,GAAG,CAAC,SAAA,MAAA,QAA5B,iCAAA,KAAA,IAAA,KAAA,IAAA,6BAAuC,EAAE,KAAI;QACtD,YAAY,mBAAmB,CAAC,MAAM,IAAI;QAC1C,gBAAgB,OAAO,CAAC,MAAM,CAAC;IACjC,GAAG,EAAE;IACL,IAAI,2BAA2B,CAAA,iKAAA,cAAU,EAAE;QACzC,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YACtC,qBAAqB,MAAM,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,MAAM,OAAO;QACxE;IACF,GAAG;QAAC;KAAqB;IAGzB,CAAA,iKAAA,YAAQ,EAAE;QACR,OAAO;IACT,GAAG;QAAC;KAAyB;IAE7B,OAAO;2BAAC;8BAAmB;kCAAsB;IAAwB;AAC3E", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/ShadowTreeWalker.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/shadowdom/ShadowTreeWalker.ts"], "sourcesContent": ["// https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/ShadowTreeWalker.ts\n\nimport {nodeContains} from './DOMFunctions';\nimport {shadowDOM} from '@react-stately/flags';\n\nexport class ShadowTreeWalker implements TreeWalker {\n  public readonly filter: NodeFilter | null;\n  public readonly root: Node;\n  public readonly whatToShow: number;\n\n  private _doc: Document;\n  private _walkerStack: Array<TreeWalker> = [];\n  private _currentNode: Node;\n  private _currentSetFor: Set<TreeWalker> = new Set();\n\n  constructor(\n      doc: Document,\n      root: Node,\n      whatToShow?: number,\n      filter?: NodeFilter | null\n    ) {\n    this._doc = doc;\n    this.root = root;\n    this.filter = filter ?? null;\n    this.whatToShow = whatToShow ?? NodeFilter.SHOW_ALL;\n    this._currentNode = root;\n\n    this._walkerStack.unshift(\n      doc.createTreeWalker(root, whatToShow, this._acceptNode)\n    );\n\n    const shadowRoot = (root as Element).shadowRoot;\n\n    if (shadowRoot) {\n      const walker = this._doc.createTreeWalker(\n        shadowRoot,\n        this.whatToShow,\n        {acceptNode: this._acceptNode}\n      );\n\n      this._walkerStack.unshift(walker);\n    }\n  }\n\n  private _acceptNode = (node: Node): number => {\n    if (node.nodeType === Node.ELEMENT_NODE) {\n      const shadowRoot = (node as Element).shadowRoot;\n\n      if (shadowRoot) {\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        this._walkerStack.unshift(walker);\n\n        return NodeFilter.FILTER_ACCEPT;\n      } else {\n        if (typeof this.filter === 'function') {\n          return this.filter(node);\n        } else if (this.filter?.acceptNode) {\n          return this.filter.acceptNode(node);\n        } else if (this.filter === null) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n      }\n    }\n\n    return NodeFilter.FILTER_SKIP;\n  };\n\n  public get currentNode(): Node {\n    return this._currentNode;\n  }\n\n  public set currentNode(node: Node) {\n    if (!nodeContains(this.root, node)) {\n      throw new Error(\n        'Cannot set currentNode to a node that is not contained by the root node.'\n      );\n    }\n\n    const walkers: TreeWalker[] = [];\n    let curNode: Node | null | undefined = node;\n    let currentWalkerCurrentNode = node;\n\n    this._currentNode = node;\n\n    while (curNode && curNode !== this.root) {\n      if (curNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n        const shadowRoot = curNode as ShadowRoot;\n\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        walkers.push(walker);\n\n        walker.currentNode = currentWalkerCurrentNode;\n\n        this._currentSetFor.add(walker);\n\n        curNode = currentWalkerCurrentNode = shadowRoot.host;\n      } else {\n        curNode = curNode.parentNode;\n      }\n    }\n\n    const walker = this._doc.createTreeWalker(\n      this.root,\n      this.whatToShow,\n      {acceptNode: this._acceptNode}\n    );\n\n    walkers.push(walker);\n\n    walker.currentNode = currentWalkerCurrentNode;\n\n    this._currentSetFor.add(walker);\n\n    this._walkerStack = walkers;\n  }\n\n  public get doc(): Document {\n    return this._doc;\n  }\n\n  public firstChild(): Node | null {\n    let currentNode = this.currentNode;\n    let newNode = this.nextNode();\n    if (!nodeContains(currentNode, newNode)) {\n      this.currentNode = currentNode;\n      return null;\n    }\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public lastChild(): Node | null {\n    let walker = this._walkerStack[0];\n    let newNode = walker.lastChild();\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public nextNode(): Node | null {\n    const nextNode = this._walkerStack[0].nextNode();\n\n    if (nextNode) {\n      const shadowRoot = (nextNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(nextNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(nextNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          this.currentNode = nextNode;\n          return nextNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (nextNode) {\n        this.currentNode = nextNode;\n      }\n      return nextNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n  public previousNode(): Node | null {\n    const currentWalker = this._walkerStack[0];\n\n    if (currentWalker.currentNode === currentWalker.root) {\n      if (this._currentSetFor.has(currentWalker)) {\n        this._currentSetFor.delete(currentWalker);\n\n        if (this._walkerStack.length > 1) {\n          this._walkerStack.shift();\n          let newNode = this.previousNode();\n          if (newNode) {\n            this.currentNode = newNode;\n          }\n          return newNode;\n        } else {\n          return null;\n        }\n      }\n\n      return null;\n    }\n\n    const previousNode = currentWalker.previousNode();\n\n    if (previousNode) {\n      const shadowRoot = (previousNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(previousNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(previousNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          if (previousNode) {\n            this.currentNode = previousNode;\n          }\n          return previousNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.lastChild();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (previousNode) {\n        this.currentNode = previousNode;\n      }\n      return previousNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.previousNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n    /**\n     * @deprecated\n     */\n  public nextSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public previousSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public parentNode(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n}\n\n/**\n * ShadowDOM safe version of document.createTreeWalker.\n */\nexport function createShadowTreeWalker(\n    doc: Document,\n    root: Node,\n    whatToShow?: number,\n    filter?: NodeFilter | null\n): TreeWalker {\n  if (shadowDOM()) {\n    return new ShadowTreeWalker(doc, root, whatToShow, filter);\n  }\n  return doc.createTreeWalker(root, whatToShow, filter);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,0HAA0H;AAKnH,MAAM;IAmEX,IAAW,cAAoB;QAC7B,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,IAAW,YAAY,IAAU,EAAE;QACjC,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,IAAI,CAAC,IAAI,EAAE,OAC3B,MAAM,IAAI,MACR;QAIJ,MAAM,UAAwB,EAAE;QAChC,IAAI,UAAmC;QACvC,IAAI,2BAA2B;QAE/B,IAAI,CAAC,YAAY,GAAG;QAEpB,MAAO,WAAW,YAAY,IAAI,CAAC,IAAI,CACrC,IAAI,QAAQ,QAAQ,KAAK,KAAK,sBAAsB,EAAE;YACpD,MAAM,aAAa;YAEnB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;gBAAC,YAAY,IAAI,CAAC,WAAW;YAAA;YAG/B,QAAQ,IAAI,CAAC;YAEb,OAAO,WAAW,GAAG;YAErB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YAExB,UAAU,2BAA2B,WAAW,IAAI;QACtD,OACE,UAAU,QAAQ,UAAU;QAIhC,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,UAAU,EACf;YAAC,YAAY,IAAI,CAAC,WAAW;QAAA;QAG/B,QAAQ,IAAI,CAAC;QAEb,OAAO,WAAW,GAAG;QAErB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,IAAW,MAAgB;QACzB,OAAO,IAAI,CAAC,IAAI;IAClB;IAEO,aAA0B;QAC/B,IAAI,cAAc,IAAI,CAAC,WAAW;QAClC,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,CAAC,CAAA,wKAAA,eAAW,EAAE,aAAa,UAAU;YACvC,IAAI,CAAC,WAAW,GAAG;YACnB,OAAO;QACT;QACA,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;QAErB,OAAO;IACT;IAEO,YAAyB;QAC9B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE;QACjC,IAAI,UAAU,OAAO,SAAS;QAC9B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;QAErB,OAAO;IACT;IAEO,WAAwB;QAC7B,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ;QAE9C,IAAI,UAAU;YACZ,MAAM,aAAc,SAAqB,UAAU;YAEnD,IAAI,YAAY;oBAKH;gBAJX,IAAI;gBAEJ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,aAAa,IAAI,CAAC,MAAM,CAAC;qBACpB,IAAA,CAAI,eAAA,IAAI,CAAC,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,UAAU,EAChC,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAGtC,IAAI,eAAe,WAAW,aAAa,EAAE;oBAC3C,IAAI,CAAC,WAAW,GAAG;oBACnB,OAAO;gBACT;gBAEA,4DAA4D;gBAC5D,qBAAqB;gBACrB,IAAI,UAAU,IAAI,CAAC,QAAQ;gBAC3B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT;YAEA,IAAI,UACF,IAAI,CAAC,WAAW,GAAG;YAErB,OAAO;QACT,OAAO;YACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;gBAEvB,IAAI,UAAU,IAAI,CAAC,QAAQ;gBAC3B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT,OACE,OAAO;QAEX;IACF;IAEO,eAA4B;QACjC,MAAM,gBAAgB,IAAI,CAAC,YAAY,CAAC,EAAE;QAE1C,IAAI,cAAc,WAAW,KAAK,cAAc,IAAI,EAAE;YACpD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB;gBAC1C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAE3B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;oBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;oBACvB,IAAI,UAAU,IAAI,CAAC,YAAY;oBAC/B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;oBAErB,OAAO;gBACT,OACE,OAAO;YAEX;YAEA,OAAO;QACT;QAEA,MAAM,eAAe,cAAc,YAAY;QAE/C,IAAI,cAAc;YAChB,MAAM,aAAc,aAAyB,UAAU;YAEvD,IAAI,YAAY;oBAKH;gBAJX,IAAI;gBAEJ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,aAAa,IAAI,CAAC,MAAM,CAAC;qBACpB,IAAA,CAAI,eAAA,IAAI,CAAC,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,UAAU,EAChC,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAGtC,IAAI,eAAe,WAAW,aAAa,EAAE;oBAC3C,IAAI,cACF,IAAI,CAAC,WAAW,GAAG;oBAErB,OAAO;gBACT;gBAEA,4DAA4D;gBAC5D,qBAAqB;gBACrB,IAAI,UAAU,IAAI,CAAC,SAAS;gBAC5B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT;YAEA,IAAI,cACF,IAAI,CAAC,WAAW,GAAG;YAErB,OAAO;QACT,OAAO;YACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;gBAEvB,IAAI,UAAU,IAAI,CAAC,YAAY;gBAC/B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT,OACE,OAAO;QAEX;IACF;IAEE;;KAEC,GACI,cAA2B;QAChC,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IAEE;;KAEC,GACI,kBAA+B;QACpC,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IAEE;;KAEC,GACI,aAA0B;QAC/B,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IA/RA,YACI,GAAa,EACb,IAAU,EACV,UAAmB,EACnB,MAA0B,CAC1B;aATI,YAAA,GAAkC,EAAE;aAEpC,cAAA,GAAkC,IAAI;aA+BtC,WAAA,GAAc,CAAC;YACrB,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY,EAAE;gBACvC,MAAM,aAAc,KAAiB,UAAU;gBAE/C,IAAI,YAAY;oBACd,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;wBAAC,YAAY,IAAI,CAAC,WAAW;oBAAA;oBAG/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;oBAE1B,OAAO,WAAW,aAAa;gBACjC,OAAO;wBAGM;oBAFX,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,OAAO,IAAI,CAAC,MAAM,CAAC;yBACd,IAAA,CAAI,eAAA,IAAI,CAAC,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,UAAU,EAChC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;yBACzB,IAAI,IAAI,CAAC,MAAM,KAAK,MACzB,OAAO,WAAW,aAAa;gBAEnC;YACF;YAEA,OAAO,WAAW,WAAW;QAC/B;QAjDE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,WAAA,QAAA,WAAA,KAAA,IAAA,SAAU;QACxB,IAAI,CAAC,UAAU,GAAG,eAAA,QAAA,eAAA,KAAA,IAAA,aAAc,WAAW,QAAQ;QACnD,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI,CAAC,YAAY,CAAC,OAAO,CACvB,IAAI,gBAAgB,CAAC,MAAM,YAAY,IAAI,CAAC,WAAW;QAGzD,MAAM,aAAc,KAAiB,UAAU;QAE/C,IAAI,YAAY;YACd,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;gBAAC,YAAY,IAAI,CAAC,WAAW;YAAA;YAG/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;QAC5B;IACF;AAqQF;AAKO,SAAS,0CACZ,GAAa,EACb,IAAU,EACV,UAAmB,EACnB,MAA0B;IAE5B,IAAI,CAAA,qKAAA,YAAQ,KACV,OAAO,IAAI,0CAAiB,KAAK,MAAM,YAAY;IAErD,OAAO,IAAI,gBAAgB,CAAC,MAAM,YAAY;AAChD", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/isScrollable.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/isScrollable.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport function isScrollable(node: Element | null, checkForOverflow?: boolean): boolean {\n  if (!node) {\n    return false;\n  }\n  let style = window.getComputedStyle(node);\n  let isScrollable = /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n\n  if (isScrollable && checkForOverflow) {\n    isScrollable = node.scrollHeight !== node.clientHeight || node.scrollWidth !== node.clientWidth;\n  }\n\n  return isScrollable;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAEM,SAAS,0CAAa,IAAoB,EAAE,gBAA0B;IAC3E,IAAI,CAAC,MACH,OAAO;IAET,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,IAAI,eAAe,gBAAgB,IAAI,CAAC,MAAM,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,SAAS;IAE1F,IAAI,gBAAgB,kBAClB,eAAe,KAAK,YAAY,KAAK,KAAK,YAAY,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAGjG,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/getScrollParent.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/getScrollParent.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParent(node: Element, checkForOverflow?: boolean): Element {\n  let scrollableNode: Element | null = node;\n  if (isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  while (scrollableNode && !isScrollable(scrollableNode, checkForOverflow)) {\n    scrollableNode = scrollableNode.parentElement;\n  }\n\n  return scrollableNode || document.scrollingElement || document.documentElement;\n}\n\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAIM,SAAS,0CAAgB,IAAa,EAAE,gBAA0B;IACvE,IAAI,iBAAiC;IACrC,IAAI,CAAA,wKAAA,eAAW,EAAE,gBAAgB,mBAC/B,iBAAiB,eAAe,aAAa;IAG/C,MAAO,kBAAkB,CAAC,CAAA,wKAAA,eAAW,EAAE,gBAAgB,kBACrD,iBAAiB,eAAe,aAAa;IAG/C,OAAO,kBAAkB,SAAS,gBAAgB,IAAI,SAAS,eAAe;AAChF", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useFormReset.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useFormReset.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useFormReset<T>(\n  ref: RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null> | undefined,\n  initialValue: T,\n  onReset: (value: T) => void\n): void {\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(initialValue);\n    }\n  });\n\n  useEffect(() => {\n    let form = ref?.current?.form;\n\n    form?.addEventListener('reset', handleReset);\n    return () => {\n      form?.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMM,SAAS,0CACd,GAA6F,EAC7F,YAAe,EACf,OAA2B;IAE3B,IAAI,cAAc,CAAA,0KAAA,iBAAa,EAAE;QAC/B,IAAI,SACF,QAAQ;IAEZ;IAEA,CAAA,iKAAA,YAAQ,EAAE;YACG;QAAX,IAAI,OAAO,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,CAAA,eAAA,IAAK,OAAO,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAc,IAAI;QAE7B,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,gBAAgB,CAAC,SAAS;QAChC,OAAO;YACL,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,mBAAmB,CAAC,SAAS;QACrC;IACF,GAAG;QAAC;QAAK;KAAY;AACvB", "debugId": null}}, {"offset": {"line": 1191, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useLabels.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useLabels.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {useId} from './useId';\n\n/**\n * Merges aria-label and aria-labelledby into aria-labelledby when both exist.\n * @param props - Aria label props.\n * @param defaultLabel - Default value for aria-label when not present.\n */\nexport function useLabels(props: DOMProps & AriaLabelingProps, defaultLabel?: string): DOMProps & AriaLabelingProps {\n  let {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  } = props;\n\n  // If there is both an aria-label and aria-labelledby,\n  // combine them by pointing to the element itself.\n  id = useId(id);\n  if (labelledBy && label) {\n    let ids = new Set([id, ...labelledBy.trim().split(/\\s+/)]);\n    labelledBy = [...ids].join(' ');\n  } else if (labelledBy) {\n    labelledBy = labelledBy.trim().split(/\\s+/).join(' ');\n  }\n\n  // If no labels are provided, use the default\n  if (!label && !labelledBy && defaultLabel) {\n    label = defaultLabel;\n  }\n\n  return {\n    id,\n    'aria-label': label,\n    'aria-labelledby': labelledBy\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAUM,SAAS,0CAAU,KAAmC,EAAE,YAAqB;IAClF,IAAI,EAAA,IACF,EAAE,EACF,cAAc,KAAK,EACnB,mBAAmB,UAAU,EAC9B,GAAG;IAEJ,sDAAsD;IACtD,kDAAkD;IAClD,KAAK,CAAA,iKAAA,QAAI,EAAE;IACX,IAAI,cAAc,OAAO;QACvB,IAAI,MAAM,IAAI,IAAI;YAAC;eAAO,WAAW,IAAI,GAAG,KAAK,CAAC;SAAO;QACzD,aAAa;eAAI;SAAI,CAAC,IAAI,CAAC;IAC7B,OAAO,IAAI,YACT,aAAa,WAAW,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC;IAGnD,6CAA6C;IAC7C,IAAI,CAAC,SAAS,CAAC,cAAc,cAC3B,QAAQ;IAGV,OAAO;YACL;QACA,cAAc;QACd,mBAAmB;IACrB;AACF", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useDescription.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useDescription.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useState} from 'react';\n\nlet descriptionId = 0;\nconst descriptionNodes = new Map<string, {refCount: number, element: Element}>();\n\nexport function useDescription(description?: string): AriaLabelingProps {\n  let [id, setId] = useState<string | undefined>();\n\n  useLayoutEffect(() => {\n    if (!description) {\n      return;\n    }\n\n    let desc = descriptionNodes.get(description);\n    if (!desc) {\n      let id = `react-aria-description-${descriptionId++}`;\n      setId(id);\n\n      let node = document.createElement('div');\n      node.id = id;\n      node.style.display = 'none';\n      node.textContent = description;\n      document.body.appendChild(node);\n      desc = {refCount: 0, element: node};\n      descriptionNodes.set(description, desc);\n    } else {\n      setId(desc.element.id);\n    }\n\n    desc.refCount++;\n    return () => {\n      if (desc && --desc.refCount === 0) {\n        desc.element.remove();\n        descriptionNodes.delete(description);\n      }\n    };\n  }, [description]);\n\n  return {\n    'aria-describedby': description ? id : undefined\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMD,IAAI,sCAAgB;AACpB,MAAM,yCAAmB,IAAI;AAEtB,SAAS,0CAAe,WAAoB;IACjD,IAAI,CAAC,IAAI,MAAM,GAAG,CAAA,iKAAA,WAAO;IAEzB,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,CAAC,aACH;QAGF,IAAI,OAAO,uCAAiB,GAAG,CAAC;QAChC,IAAI,CAAC,MAAM;YACT,IAAI,KAAK,AAAC,uBAAuB,GAAE,CAAiB,MAAjB;YACnC,MAAM;YAEN,IAAI,OAAO,SAAS,aAAa,CAAC;YAClC,KAAK,EAAE,GAAG;YACV,KAAK,KAAK,CAAC,OAAO,GAAG;YACrB,KAAK,WAAW,GAAG;YACnB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;gBAAC,UAAU;gBAAG,SAAS;YAAI;YAClC,uCAAiB,GAAG,CAAC,aAAa;QACpC,OACE,MAAM,KAAK,OAAO,CAAC,EAAE;QAGvB,KAAK,QAAQ;QACb,OAAO;YACL,IAAI,QAAQ,EAAE,KAAK,QAAQ,KAAK,GAAG;gBACjC,KAAK,OAAO,CAAC,MAAM;gBACnB,uCAAiB,MAAM,CAAC;YAC1B;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL,oBAAoB,cAAc,KAAK;IACzC;AACF", "debugId": null}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useResizeObserver.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useResizeObserver.ts"], "sourcesContent": ["\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\nfunction hasResizeObserver() {\n  return typeof window.ResizeObserver !== 'undefined';\n}\n\ntype useResizeObserverOptionsType<T> = {\n  ref: RefObject<T | undefined | null> | undefined,\n  box?: ResizeObserverBoxOptions,\n  onResize: () => void\n}\n\nexport function useResizeObserver<T extends Element>(options: useResizeObserverOptionsType<T>): void {\n  const {ref, box, onResize} = options;\n\n  useEffect(() => {\n    let element = ref?.current;\n    if (!element) {\n      return;\n    }\n\n    if (!hasResizeObserver()) {\n      window.addEventListener('resize', onResize, false);\n      return () => {\n        window.removeEventListener('resize', onResize, false);\n      };\n    } else {\n\n      const resizeObserverInstance = new window.ResizeObserver((entries) => {\n        if (!entries.length) {\n          return;\n        }\n\n        onResize();\n      });\n      resizeObserverInstance.observe(element, {box});\n\n      return () => {\n        if (element) {\n          resizeObserverInstance.unobserve(element);\n        }\n      };\n    }\n\n  }, [onResize, ref, box]);\n}\n"], "names": [], "mappings": ";;;;;AAIA,SAAS;IACP,OAAO,OAAO,OAAO,cAAc,KAAK;AAC1C;AAQO,SAAS,0CAAqC,OAAwC;IAC3F,MAAM,EAAA,KAAC,GAAG,EAAA,KAAE,GAAG,EAAA,UAAE,QAAQ,EAAC,GAAG;IAE7B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,UAAU,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,OAAO;QAC1B,IAAI,CAAC,SACH;QAGF,IAAI,CAAC,2CAAqB;YACxB,OAAO,gBAAgB,CAAC,UAAU,UAAU;YAC5C,OAAO;gBACL,OAAO,mBAAmB,CAAC,UAAU,UAAU;YACjD;QACF,OAAO;YAEL,MAAM,yBAAyB,IAAI,OAAO,cAAc,CAAC,CAAC;gBACxD,IAAI,CAAC,QAAQ,MAAM,EACjB;gBAGF;YACF;YACA,uBAAuB,OAAO,CAAC,SAAS;qBAAC;YAAG;YAE5C,OAAO;gBACL,IAAI,SACF,uBAAuB,SAAS,CAAC;YAErC;QACF;IAEF,GAAG;QAAC;QAAU;QAAK;KAAI;AACzB", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/keyboard.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/keyboard.tsx"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isMac} from './platform';\n\ninterface Event {\n  altKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean\n}\n\nexport function isCtrlKeyPressed(e: Event): boolean {\n  if (isMac()) {\n    return e.metaKey;\n  }\n\n  return e.ctrlKey;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAUM,SAAS,0CAAiB,CAAQ;IACvC,IAAI,CAAA,oKAAA,QAAI,KACN,OAAO,EAAE,OAAO;IAGlB,OAAO,EAAE,OAAO;AAClB", "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useEvent.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useEvent.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useEvent<K extends keyof GlobalEventHandlersEventMap>(\n  ref: RefObject<EventTarget | null>,\n  event: K | (string & {}),\n  handler?: (this: Document, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n): void {\n  let handleEvent = useEffectEvent(handler);\n  let isDisabled = handler == null;\n\n  useEffect(() => {\n    if (isDisabled || !ref.current) {\n      return;\n    }\n\n    let element = ref.current;\n    element.addEventListener(event, handleEvent as EventListener, options);\n    return () => {\n      element.removeEventListener(event, handleEvent as EventListener, options);\n    };\n  }, [ref, event, options, isDisabled, handleEvent]);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMM,SAAS,0CACd,GAAkC,EAClC,KAAwB,EACxB,OAAqE,EACrE,OAA2C;IAE3C,IAAI,cAAc,CAAA,0KAAA,iBAAa,EAAE;IACjC,IAAI,aAAa,WAAW;IAE5B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,cAAc,CAAC,IAAI,OAAO,EAC5B;QAGF,IAAI,UAAU,IAAI,OAAO;QACzB,QAAQ,gBAAgB,CAAC,OAAO,aAA8B;QAC9D,OAAO;YACL,QAAQ,mBAAmB,CAAC,OAAO,aAA8B;QACnE;IACF,GAAG;QAAC;QAAK;QAAO;QAAS;QAAY;KAAY;AACnD", "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/getScrollParents.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/getScrollParents.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParents(node: Element, checkForOverflow?: boolean): Element[] {\n  const scrollParents: Element[] = [];\n\n  while (node && node !== document.documentElement) {\n    if (isScrollable(node, checkForOverflow)) {\n      scrollParents.push(node);\n    }\n    node = node.parentElement as Element;\n  }\n\n  return scrollParents;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAIM,SAAS,0CAAiB,IAAa,EAAE,gBAA0B;IACxE,MAAM,gBAA2B,EAAE;IAEnC,MAAO,QAAQ,SAAS,SAAS,eAAe,CAAE;QAChD,IAAI,CAAA,wKAAA,eAAW,EAAE,MAAM,mBACrB,cAAc,IAAI,CAAC;QAErB,OAAO,KAAK,aAAa;IAC3B;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/scrollIntoView.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/scrollIntoView.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getScrollParents} from './getScrollParents';\n\ninterface ScrollIntoViewportOpts {\n  /** The optional containing element of the target to be centered in the viewport. */\n  containingElement?: Element | null\n}\n\n/**\n * Scrolls `scrollView` so that `element` is visible.\n * Similar to `element.scrollIntoView({block: 'nearest'})` (not supported in Edge),\n * but doesn't affect parents above `scrollView`.\n */\nexport function scrollIntoView(scrollView: HTMLElement, element: HTMLElement): void {\n  let offsetX = relativeOffset(scrollView, element, 'left');\n  let offsetY = relativeOffset(scrollView, element, 'top');\n  let width = element.offsetWidth;\n  let height = element.offsetHeight;\n  let x = scrollView.scrollLeft;\n  let y = scrollView.scrollTop;\n\n  // Account for top/left border offsetting the scroll top/Left + scroll padding\n  let {\n    borderTopWidth,\n    borderLeftWidth,\n    scrollPaddingTop,\n    scrollPaddingRight,\n    scrollPaddingBottom,\n    scrollPaddingLeft\n  } = getComputedStyle(scrollView);\n\n  let borderAdjustedX = x + parseInt(borderLeftWidth, 10);\n  let borderAdjustedY = y + parseInt(borderTopWidth, 10);\n  // Ignore end/bottom border via clientHeight/Width instead of offsetHeight/Width\n  let maxX = borderAdjustedX + scrollView.clientWidth;\n  let maxY = borderAdjustedY + scrollView.clientHeight;\n\n  // Get scroll padding values as pixels - defaults to 0 if no scroll padding\n  // is used.\n  let scrollPaddingTopNumber = parseInt(scrollPaddingTop, 10) || 0;\n  let scrollPaddingBottomNumber = parseInt(scrollPaddingBottom, 10) || 0;\n  let scrollPaddingRightNumber = parseInt(scrollPaddingRight, 10) || 0;\n  let scrollPaddingLeftNumber = parseInt(scrollPaddingLeft, 10) || 0;\n\n  if (offsetX <= x + scrollPaddingLeftNumber) {\n    x = offsetX - parseInt(borderLeftWidth, 10) - scrollPaddingLeftNumber;\n  } else if (offsetX + width > maxX - scrollPaddingRightNumber) {\n    x += offsetX + width - maxX + scrollPaddingRightNumber;\n  }\n  if (offsetY <= borderAdjustedY + scrollPaddingTopNumber) {\n    y = offsetY - parseInt(borderTopWidth, 10) - scrollPaddingTopNumber;\n  } else if (offsetY + height > maxY - scrollPaddingBottomNumber) {\n    y += offsetY + height - maxY + scrollPaddingBottomNumber;\n  }\n\n  scrollView.scrollLeft = x;\n  scrollView.scrollTop = y;\n}\n\n/**\n * Computes the offset left or top from child to ancestor by accumulating\n * offsetLeft or offsetTop through intervening offsetParents.\n */\nfunction relativeOffset(ancestor: HTMLElement, child: HTMLElement, axis: 'left'|'top') {\n  const prop = axis === 'left' ? 'offsetLeft' : 'offsetTop';\n  let sum = 0;\n  while (child.offsetParent) {\n    sum += child[prop];\n    if (child.offsetParent === ancestor) {\n      // Stop once we have found the ancestor we are interested in.\n      break;\n    } else if (child.offsetParent.contains(ancestor)) {\n      // If the ancestor is not `position:relative`, then we stop at\n      // _its_ offset parent, and we subtract off _its_ offset, so that\n      // we end up with the proper offset from child to ancestor.\n      sum -= ancestor[prop];\n      break;\n    }\n    child = child.offsetParent as HTMLElement;\n  }\n  return sum;\n}\n\n/**\n * Scrolls the `targetElement` so it is visible in the viewport. Accepts an optional `opts.containingElement`\n * that will be centered in the viewport prior to scrolling the targetElement into view. If scrolling is prevented on\n * the body (e.g. targetElement is in a popover), this will only scroll the scroll parents of the targetElement up to but not including the body itself.\n */\nexport function scrollIntoViewport(targetElement: Element | null, opts?: ScrollIntoViewportOpts): void {\n  if (targetElement && document.contains(targetElement)) {\n    let root = document.scrollingElement || document.documentElement;\n    let isScrollPrevented = window.getComputedStyle(root).overflow === 'hidden';\n    // If scrolling is not currently prevented then we aren’t in a overlay nor is a overlay open, just use element.scrollIntoView to bring the element into view\n    if (!isScrollPrevented) {\n      let {left: originalLeft, top: originalTop} = targetElement.getBoundingClientRect();\n\n      // use scrollIntoView({block: 'nearest'}) instead of .focus to check if the element is fully in view or not since .focus()\n      // won't cause a scroll if the element is already focused and doesn't behave consistently when an element is partially out of view horizontally vs vertically\n      targetElement?.scrollIntoView?.({block: 'nearest'});\n      let {left: newLeft, top: newTop} = targetElement.getBoundingClientRect();\n      // Account for sub pixel differences from rounding\n      if ((Math.abs(originalLeft - newLeft) > 1) || (Math.abs(originalTop - newTop) > 1)) {\n        opts?.containingElement?.scrollIntoView?.({block: 'center', inline: 'center'});\n        targetElement.scrollIntoView?.({block: 'nearest'});\n      }\n    } else {\n      let scrollParents = getScrollParents(targetElement);\n      // If scrolling is prevented, we don't want to scroll the body since it might move the overlay partially offscreen and the user can't scroll it back into view.\n      for (let scrollParent of scrollParents) {\n        scrollIntoView(scrollParent as HTMLElement, targetElement as HTMLElement);\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAcM,SAAS,0CAAe,UAAuB,EAAE,OAAoB;IAC1E,IAAI,UAAU,qCAAe,YAAY,SAAS;IAClD,IAAI,UAAU,qCAAe,YAAY,SAAS;IAClD,IAAI,QAAQ,QAAQ,WAAW;IAC/B,IAAI,SAAS,QAAQ,YAAY;IACjC,IAAI,IAAI,WAAW,UAAU;IAC7B,IAAI,IAAI,WAAW,SAAS;IAE5B,8EAA8E;IAC9E,IAAI,EAAA,gBACF,cAAc,EAAA,iBACd,eAAe,EAAA,kBACf,gBAAgB,EAAA,oBAChB,kBAAkB,EAAA,qBAClB,mBAAmB,EAAA,mBACnB,iBAAiB,EAClB,GAAG,iBAAiB;IAErB,IAAI,kBAAkB,IAAI,SAAS,iBAAiB;IACpD,IAAI,kBAAkB,IAAI,SAAS,gBAAgB;IACnD,gFAAgF;IAChF,IAAI,OAAO,kBAAkB,WAAW,WAAW;IACnD,IAAI,OAAO,kBAAkB,WAAW,YAAY;IAEpD,2EAA2E;IAC3E,WAAW;IACX,IAAI,yBAAyB,SAAS,kBAAkB,OAAO;IAC/D,IAAI,4BAA4B,SAAS,qBAAqB,OAAO;IACrE,IAAI,2BAA2B,SAAS,oBAAoB,OAAO;IACnE,IAAI,0BAA0B,SAAS,mBAAmB,OAAO;IAEjE,IAAI,WAAW,IAAI,yBACjB,IAAI,UAAU,SAAS,iBAAiB,MAAM;SACzC,IAAI,UAAU,QAAQ,OAAO,0BAClC,KAAK,UAAU,QAAQ,OAAO;IAEhC,IAAI,WAAW,kBAAkB,wBAC/B,IAAI,UAAU,SAAS,gBAAgB,MAAM;SACxC,IAAI,UAAU,SAAS,OAAO,2BACnC,KAAK,UAAU,SAAS,OAAO;IAGjC,WAAW,UAAU,GAAG;IACxB,WAAW,SAAS,GAAG;AACzB;AAEA;;;CAGC,GACD,SAAS,qCAAe,QAAqB,EAAE,KAAkB,EAAE,IAAkB;IACnF,MAAM,OAAO,SAAS,SAAS,eAAe;IAC9C,IAAI,MAAM;IACV,MAAO,MAAM,YAAY,CAAE;QACzB,OAAO,KAAK,CAAC,KAAK;QAClB,IAAI,MAAM,YAAY,KAAK,UAEzB;aACK,IAAI,MAAM,YAAY,CAAC,QAAQ,CAAC,WAAW;YAChD,8DAA8D;YAC9D,iEAAiE;YACjE,2DAA2D;YAC3D,OAAO,QAAQ,CAAC,KAAK;YACrB;QACF;QACA,QAAQ,MAAM,YAAY;IAC5B;IACA,OAAO;AACT;AAOO,SAAS,0CAAmB,aAA6B,EAAE,IAA6B;IAC7F,IAAI,iBAAiB,SAAS,QAAQ,CAAC,gBAAgB;QACrD,IAAI,OAAO,SAAS,gBAAgB,IAAI,SAAS,eAAe;QAChE,IAAI,oBAAoB,OAAO,gBAAgB,CAAC,MAAM,QAAQ,KAAK;QACnE,4JAA4J;QAC5J,IAAI,CAAC,mBAAmB;gBAGtB,AACA,0HAD0H,mCACmC;YAC7J;YAJA,IAAI,EAAC,MAAM,YAAY,EAAE,KAAK,WAAW,EAAC,GAAG,cAAc,qBAAqB;YAIhF,kBAAA,QAAA,kBAAA,KAAA,IAAA,KAAA,IAAA,CAAA,gCAAA,cAAe,cAAc,MAAA,QAA7B,kCAAA,KAAA,IAAA,KAAA,IAAA,8BAAA,IAAA,CAAA,eAAgC;gBAAC,OAAO;YAAS;YACjD,IAAI,EAAC,MAAM,OAAO,EAAE,KAAK,MAAM,EAAC,GAAG,cAAc,qBAAqB;YACtE,kDAAkD;YAClD,IAAK,KAAK,GAAG,CAAC,eAAe,WAAW,KAAO,KAAK,GAAG,CAAC,cAAc,UAAU,GAAI;oBAClF,wCAAA,yBACA;gBADA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,0BAAA,KAAM,iBAAiB,MAAA,QAAvB,4BAAA,KAAA,IAAA,KAAA,IAAA,CAAA,yCAAA,wBAAyB,cAAc,MAAA,QAAvC,2CAAA,KAAA,IAAA,KAAA,IAAA,uCAAA,IAAA,CAAA,yBAA0C;oBAAC,OAAO;oBAAU,QAAQ;gBAAQ;iBAC5E,iCAAA,cAAc,cAAc,MAAA,QAA5B,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAA,IAAA,CAAA,eAA+B;oBAAC,OAAO;gBAAS;YAClD;QACF,OAAO;YACL,IAAI,gBAAgB,CAAA,4KAAA,mBAAe,EAAE;YACrC,+JAA+J;YAC/J,KAAK,IAAI,gBAAgB,cACvB,0CAAe,cAA6B;QAEhD;IACF;AACF", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/constants.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/constants.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Custom event names for updating the autocomplete's aria-activedecendant.\nexport const CLEAR_FOCUS_EVENT = 'react-aria-clear-focus';\nexport const FOCUS_EVENT = 'react-aria-focus';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED,2EAA2E;;;;;AACpE,MAAM,4CAAoB;AAC1B,MAAM,4CAAc", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/utils/dist/useUpdateLayoutEffect.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/utils/dist/packages/%40react-aria/utils/src/useUpdateLayoutEffect.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {EffectCallback, useRef} from 'react';\nimport {useLayoutEffect} from './useLayoutEffect';\n\n// Like useLayoutEffect, but only called for updates after the initial render.\nexport function useUpdateLayoutEffect(effect: EffectCallback, dependencies: any[]): void {\n  const isInitialMount = useRef(true);\n  const lastDeps = useRef<any[] | null>(null);\n\n  useLayoutEffect(() => {\n    isInitialMount.current = true;\n    return () => {\n      isInitialMount.current = false;\n    };\n  }, []);\n\n  useLayoutEffect(() => {\n    if (isInitialMount.current) {\n      isInitialMount.current = false;\n    } else if (!lastDeps.current || dependencies.some((dep, i) => !Object.is(dep, lastDeps[i]))) {\n      effect();\n    }\n    lastDeps.current = dependencies;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, dependencies);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAMM,SAAS,0CAAsB,MAAsB,EAAE,YAAmB;IAC/E,MAAM,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC9B,MAAM,WAAW,CAAA,iKAAA,SAAK,EAAgB;IAEtC,CAAA,2KAAA,kBAAc,EAAE;QACd,eAAe,OAAO,GAAG;QACzB,OAAO;YACL,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG,EAAE;IAEL,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,eAAe,OAAO,EACxB,eAAe,OAAO,GAAG;aACpB,IAAI,CAAC,SAAS,OAAO,IAAI,aAAa,IAAI,CAAC,CAAC,KAAK,IAAM,CAAC,OAAO,EAAE,CAAC,KAAK,QAAQ,CAAC,EAAE,IACvF;QAEF,SAAS,OAAO,GAAG;IACnB,uDAAuD;IACzD,GAAG;AACL", "debugId": null}}, {"offset": {"line": 1591, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/focus/dist/useFocusRing.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/useFocusRing.ts"], "sourcesContent": ["import {DOMAttributes} from '@react-types/shared';\nimport {isFocusVisible, useFocus, useFocusVisibleListener, useFocusWithin} from '@react-aria/interactions';\nimport {useCallback, useRef, useState} from 'react';\n\nexport interface AriaFocusRingProps {\n  /**\n   * Whether to show the focus ring when something\n   * inside the container element has focus (true), or\n   * only if the container itself has focus (false).\n   * @default 'false'\n   */\n  within?: boolean,\n\n  /** Whether the element is a text input. */\n  isTextInput?: boolean,\n\n  /** Whether the element will be auto focused. */\n  autoFocus?: boolean\n}\n\nexport interface FocusRingAria {\n  /** Whether the element is currently focused. */\n  isFocused: boolean,\n\n  /** Whether keyboard focus should be visible. */\n  isFocusVisible: boolean,\n\n  /** Props to apply to the container element with the focus ring. */\n  focusProps: DOMAttributes\n}\n\n/**\n * Determines whether a focus ring should be shown to indicate keyboard focus.\n * Focus rings are visible only when the user is interacting with a keyboard,\n * not with a mouse, touch, or other input methods.\n */\nexport function useFocusRing(props: AriaFocusRingProps = {}): FocusRingAria {\n  let {\n    autoFocus = false,\n    isTextInput,\n    within\n  } = props;\n  let state = useRef({\n    isFocused: false,\n    isFocusVisible: autoFocus || isFocusVisible()\n  });\n  let [isFocused, setFocused] = useState(false);\n  let [isFocusVisibleState, setFocusVisible] = useState(() => state.current.isFocused && state.current.isFocusVisible);\n\n  let updateState = useCallback(() => setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n\n  let onFocusChange = useCallback(isFocused => {\n    state.current.isFocused = isFocused;\n    setFocused(isFocused);\n    updateState();\n  }, [updateState]);\n\n  useFocusVisibleListener((isFocusVisible) => {\n    state.current.isFocusVisible = isFocusVisible;\n    updateState();\n  }, [], {isTextInput});\n\n  let {focusProps} = useFocus({\n    isDisabled: within,\n    onFocusChange\n  });\n\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !within,\n    onFocusWithinChange: onFocusChange\n  });\n\n  return {\n    isFocused,\n    isFocusVisible: isFocusVisibleState,\n    focusProps: within ? focusWithinProps : focusProps\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAoCO,SAAS;gBAAa,iEAA4B,CAAC,CAAC;IACzD,IAAI,EAAA,WACF,YAAY,KAAA,EAAA,aACZ,WAAW,EAAA,QACX,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ,CAAA,iKAAA,SAAK,EAAE;QACjB,WAAW;QACX,gBAAgB,aAAa,CAAA,kLAAA,iBAAa;IAC5C;IACA,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvC,IAAI,CAAC,qBAAqB,gBAAgB,GAAG,CAAA,iKAAA,WAAO,EAAE,IAAM,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc;IAEnH,IAAI,cAAc,CAAA,iKAAA,cAAU,EAAE,IAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,IAAI,MAAM,OAAO,CAAC,cAAc,GAAG,EAAE;IAEhH,IAAI,gBAAgB,CAAA,iKAAA,cAAU,EAAE,CAAA;QAC9B,MAAM,OAAO,CAAC,SAAS,GAAG;QAC1B,WAAW;QACX;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,kLAAA,0BAAsB,EAAE,CAAC;QACvB,MAAM,OAAO,CAAC,cAAc,GAAG;QAC/B;IACF,GAAG,EAAE,EAAE;qBAAC;IAAW;IAEnB,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;QAC1B,YAAY;uBACZ;IACF;IAEA,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,iLAAA,iBAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB;IACvB;IAEA,OAAO;mBACL;QACA,gBAAgB;QAChB,YAAY,SAAS,mBAAmB;IAC1C;AACF", "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/focus/dist/FocusScope.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/FocusScope.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  createShadowTreeWalker,\n  getActiveElement,\n  getEventTarget,\n  getOwnerDocument,\n  isAndroid,\n  isChrome,\n  isFocusable,\n  isTabbable,\n  ShadowTreeWalker,\n  useLayoutEffect\n} from '@react-aria/utils';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport React, {JSX, ReactNode, useContext, useEffect, useMemo, useRef} from 'react';\n\nexport interface FocusScopeProps {\n  /** The contents of the focus scope. */\n  children: ReactNode,\n\n  /**\n   * Whether to contain focus inside the scope, so users cannot\n   * move focus outside, for example in a modal dialog.\n   */\n  contain?: boolean,\n\n  /**\n   * Whether to restore focus back to the element that was focused\n   * when the focus scope mounted, after the focus scope unmounts.\n   */\n  restoreFocus?: boolean,\n\n  /** Whether to auto focus the first focusable element in the focus scope on mount. */\n  autoFocus?: boolean\n}\n\nexport interface FocusManagerOptions {\n  /** The element to start searching from. The currently focused element by default. */\n  from?: Element,\n  /** Whether to only include tabbable elements, or all focusable elements. */\n  tabbable?: boolean,\n  /** Whether focus should wrap around when it reaches the end of the scope. */\n  wrap?: boolean,\n  /** A callback that determines whether the given element is focused. */\n  accept?: (node: Element) => boolean\n}\n\nexport interface FocusManager {\n  /** Moves focus to the next focusable or tabbable element in the focus scope. */\n  focusNext(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the previous focusable or tabbable element in the focus scope. */\n  focusPrevious(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the first focusable or tabbable element in the focus scope. */\n  focusFirst(opts?: FocusManagerOptions): FocusableElement | null,\n  /** Moves focus to the last focusable or tabbable element in the focus scope. */\n  focusLast(opts?: FocusManagerOptions): FocusableElement | null\n}\n\ntype ScopeRef = RefObject<Element[] | null> | null;\ninterface IFocusContext {\n  focusManager: FocusManager,\n  parentNode: TreeNode | null\n}\n\nconst FocusContext = React.createContext<IFocusContext | null>(null);\nconst RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\n\nlet activeScope: ScopeRef = null;\n\n// This is a hacky DOM-based implementation of a FocusScope until this RFC lands in React:\n// https://github.com/reactjs/rfcs/pull/109\n\n/**\n * A FocusScope manages focus for its descendants. It supports containing focus inside\n * the scope, restoring focus to the previously focused element on unmount, and auto\n * focusing children on mount. It also acts as a container for a programmatic focus\n * management interface that can be used to move focus forward and back in response\n * to user events.\n */\nexport function FocusScope(props: FocusScopeProps): JSX.Element {\n  let {children, contain, restoreFocus, autoFocus} = props;\n  let startRef = useRef<HTMLSpanElement>(null);\n  let endRef = useRef<HTMLSpanElement>(null);\n  let scopeRef = useRef<Element[]>([]);\n  let {parentNode} = useContext(FocusContext) || {};\n\n  // Create a tree node here so we can add children to it even before it is added to the tree.\n  let node = useMemo(() => new TreeNode({scopeRef}), [scopeRef]);\n\n  useLayoutEffect(() => {\n    // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n    // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n    // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n    // that is being added should get the activeScope as its parent.\n    let parent = parentNode || focusScopeTree.root;\n    if (focusScopeTree.getTreeNode(parent.scopeRef) && activeScope && !isAncestorScope(activeScope, parent.scopeRef)) {\n      let activeNode = focusScopeTree.getTreeNode(activeScope);\n      if (activeNode) {\n        parent = activeNode;\n      }\n    }\n\n    // Add the node to the parent, and to the tree.\n    parent.addChild(node);\n    focusScopeTree.addNode(node);\n  }, [node, parentNode]);\n\n  useLayoutEffect(() => {\n    let node = focusScopeTree.getTreeNode(scopeRef);\n    if (node) {\n      node.contain = !!contain;\n    }\n  }, [contain]);\n\n  useLayoutEffect(() => {\n    // Find all rendered nodes between the sentinels and add them to the scope.\n    let node = startRef.current?.nextSibling!;\n    let nodes: Element[] = [];\n    let stopPropagation = e => e.stopPropagation();\n    while (node && node !== endRef.current) {\n      nodes.push(node as Element);\n      // Stop custom restore focus event from propagating to parent focus scopes.\n      node.addEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      node = node.nextSibling as Element;\n    }\n\n    scopeRef.current = nodes;\n\n    return () => {\n      for (let node of nodes) {\n        node.removeEventListener(RESTORE_FOCUS_EVENT, stopPropagation);\n      }\n    };\n  }, [children]);\n\n  useActiveScopeTracker(scopeRef, restoreFocus, contain);\n  useFocusContainment(scopeRef, contain);\n  useRestoreFocus(scopeRef, restoreFocus, contain);\n  useAutoFocus(scopeRef, autoFocus);\n\n  // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n  // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n  useEffect(() => {\n    const activeElement = getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined));\n    let scope: TreeNode | null = null;\n\n    if (isElementInScope(activeElement, scopeRef.current)) {\n      // We need to traverse the focusScope tree and find the bottom most scope that\n      // contains the active element and set that as the activeScope.\n      for (let node of focusScopeTree.traverse()) {\n        if (node.scopeRef && isElementInScope(activeElement, node.scopeRef.current)) {\n          scope = node;\n        }\n      }\n\n      if (scope === focusScopeTree.getTreeNode(scopeRef)) {\n        activeScope = scope.scopeRef;\n      }\n    }\n  }, [scopeRef]);\n\n  // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n  // in useRestoreFocus cleanup runs.\n  useLayoutEffect(() => {\n    return () => {\n      // Scope may have been re-parented.\n      let parentScope = focusScopeTree.getTreeNode(scopeRef)?.parent?.scopeRef ?? null;\n\n      if (\n        (scopeRef === activeScope || isAncestorScope(scopeRef, activeScope)) &&\n        (!parentScope || focusScopeTree.getTreeNode(parentScope))\n      ) {\n        activeScope = parentScope;\n      }\n      focusScopeTree.removeTreeNode(scopeRef);\n    };\n  }, [scopeRef]);\n\n  let focusManager = useMemo(() => createFocusManagerForScope(scopeRef), []);\n  let value = useMemo(() => ({\n    focusManager,\n    parentNode: node\n  }), [node, focusManager]);\n\n  return (\n    <FocusContext.Provider value={value}>\n      <span data-focus-scope-start hidden ref={startRef} />\n      {children}\n      <span data-focus-scope-end hidden ref={endRef} />\n    </FocusContext.Provider>\n  );\n}\n\n/**\n * Returns a FocusManager interface for the parent FocusScope.\n * A FocusManager can be used to programmatically move focus within\n * a FocusScope, e.g. in response to user events like keyboard navigation.\n */\nexport function useFocusManager(): FocusManager | undefined {\n  return useContext(FocusContext)?.focusManager;\n}\n\nfunction createFocusManagerForScope(scopeRef: React.RefObject<Element[] | null>): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[0].previousElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node : sentinel;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = {}) {\n      let scope = scopeRef.current!;\n      let {from, tabbable, wrap, accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(scope[0] ?? undefined))!;\n      let sentinel = scope[scope.length - 1].nextElementSibling!;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = isElementInScope(node, scope) ? node  : sentinel;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = sentinel;\n        previousNode = walker.previousNode() as FocusableElement;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    },\n    focusFirst(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[0].previousElementSibling!;\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = {}) {\n      let scope = scopeRef.current!;\n      let {tabbable, accept} = opts;\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable, accept}, scope);\n      walker.currentNode = scope[scope.length - 1].nextElementSibling!;\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode;\n    }\n  };\n}\n\nfunction getScopeRoot(scope: Element[]) {\n  return scope[0].parentElement!;\n}\n\nfunction shouldContainFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.contain) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return true;\n}\n\nfunction isTabbableRadio(element: HTMLInputElement) {\n  if (element.checked) {\n    return true;\n  }\n  let radios: HTMLInputElement[] = [];\n  if (!element.form) {\n    radios = ([...getOwnerDocument(element).querySelectorAll(`input[type=\"radio\"][name=\"${CSS.escape(element.name)}\"]`)] as HTMLInputElement[]).filter(radio => !radio.form);\n  } else {\n    let radioList = element.form?.elements?.namedItem(element.name) as RadioNodeList;\n    radios = [...(radioList ?? [])] as HTMLInputElement[];\n  }\n  if (!radios) {\n    return false;\n  }\n  let anyChecked = radios.some(radio => radio.checked);\n\n  return !anyChecked;\n}\n\nfunction useFocusContainment(scopeRef: RefObject<Element[] | null>, contain?: boolean) {\n  let focusedNode = useRef<FocusableElement>(undefined);\n\n  let raf = useRef<ReturnType<typeof requestAnimationFrame>>(undefined);\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    if (!contain) {\n      // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n        raf.current = undefined;\n      }\n      return;\n    }\n\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    // Handle the Tab key to contain focus within the scope\n    let onKeyDown = (e) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = getActiveElement(ownerDocument);\n      let scope = scopeRef.current;\n      if (!scope || !isElementInScope(focusedElement, scope)) {\n        return;\n      }\n\n      let scopeRoot = getScopeRoot(scope);\n      let walker = getFocusableTreeWalker(scopeRoot, {tabbable: true}, scope);\n      if (!focusedElement) {\n        return;\n      }\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      if (!nextElement) {\n        walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling! : scope[0].previousElementSibling!;\n        nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n      }\n\n      e.preventDefault();\n      if (nextElement) {\n        focusElement(nextElement, true);\n      }\n    };\n\n    let onFocus: EventListener = (e) => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) && isElementInScope(getEventTarget(e) as Element, scopeRef.current)) {\n        activeScope = scopeRef;\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      } else if (shouldContainFocus(scopeRef) && !isElementInChildScope(getEventTarget(e) as Element, scopeRef)) {\n        // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n        // restore focus to the previously focused node or the first tabbable element in the active scope.\n        if (focusedNode.current) {\n          focusedNode.current.focus();\n        } else if (activeScope && activeScope.current) {\n          focusFirstInScope(activeScope.current);\n        }\n      } else if (shouldContainFocus(scopeRef)) {\n        focusedNode.current = getEventTarget(e) as FocusableElement;\n      }\n    };\n\n    let onBlur: EventListener = (e) => {\n      // Firefox doesn't shift focus back to the Dialog properly without this\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n      raf.current = requestAnimationFrame(() => {\n        // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n        // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n        // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n        let modality = getInteractionModality();\n        let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && isAndroid() && isChrome();\n\n        // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n        let activeElement = getActiveElement(ownerDocument);\n        if (!shouldSkipFocusRestore && activeElement && shouldContainFocus(scopeRef) && !isElementInChildScope(activeElement, scopeRef)) {\n          activeScope = scopeRef;\n          let target = getEventTarget(e) as FocusableElement;\n          if (target && target.isConnected) {\n            focusedNode.current = target;\n            focusedNode.current?.focus();\n          } else if (activeScope.current) {\n            focusFirstInScope(activeScope.current);\n          }\n        }\n      });\n    };\n\n    ownerDocument.addEventListener('keydown', onKeyDown, false);\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    scope?.forEach(element => element.addEventListener('focusout', onBlur, false));\n    return () => {\n      ownerDocument.removeEventListener('keydown', onKeyDown, false);\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n      scope?.forEach(element => element.removeEventListener('focusout', onBlur, false));\n    };\n  }, [scopeRef, contain]);\n\n  // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n\n  useLayoutEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, [raf]);\n}\n\nfunction isElementInAnyScope(element: Element) {\n  return isElementInChildScope(element);\n}\n\nfunction isElementInScope(element?: Element | null, scope?: Element[] | null) {\n  if (!element) {\n    return false;\n  }\n  if (!scope) {\n    return false;\n  }\n  return scope.some(node => node.contains(element));\n}\n\nfunction isElementInChildScope(element: Element, scope: ScopeRef = null) {\n  // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n  if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) {\n    return true;\n  }\n\n  // node.contains in isElementInScope covers child scopes that are also DOM children,\n  // but does not cover child scopes in portals.\n  for (let {scopeRef: s} of focusScopeTree.traverse(focusScopeTree.getTreeNode(scope))) {\n    if (s && isElementInScope(element, s.current)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/** @private */\nexport function isElementInChildOfActiveScope(element: Element): boolean {\n  return isElementInChildScope(element, activeScope);\n}\n\nfunction isAncestorScope(ancestor: ScopeRef, scope: ScopeRef) {\n  let parent = focusScopeTree.getTreeNode(scope)?.parent;\n  while (parent) {\n    if (parent.scopeRef === ancestor) {\n      return true;\n    }\n    parent = parent.parent;\n  }\n  return false;\n}\n\nfunction focusElement(element: FocusableElement | null, scroll = false) {\n  if (element != null && !scroll) {\n    try {\n      focusSafely(element);\n    } catch {\n      // ignore\n    }\n  } else if (element != null) {\n    try {\n      element.focus();\n    } catch {\n      // ignore\n    }\n  }\n}\n\nfunction getFirstInScope(scope: Element[], tabbable = true) {\n  let sentinel = scope[0].previousElementSibling!;\n  let scopeRoot = getScopeRoot(scope);\n  let walker = getFocusableTreeWalker(scopeRoot, {tabbable}, scope);\n  walker.currentNode = sentinel;\n  let nextNode = walker.nextNode();\n\n  // If the scope does not contain a tabbable element, use the first focusable element.\n  if (tabbable && !nextNode) {\n    scopeRoot = getScopeRoot(scope);\n    walker = getFocusableTreeWalker(scopeRoot, {tabbable: false}, scope);\n    walker.currentNode = sentinel;\n    nextNode = walker.nextNode();\n  }\n\n  return nextNode as FocusableElement;\n}\n\nfunction focusFirstInScope(scope: Element[], tabbable:boolean = true) {\n  focusElement(getFirstInScope(scope, tabbable));\n}\n\nfunction useAutoFocus(scopeRef: RefObject<Element[] | null>, autoFocus?: boolean) {\n  const autoFocusRef = React.useRef(autoFocus);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      activeScope = scopeRef;\n      const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n      if (!isElementInScope(getActiveElement(ownerDocument), activeScope.current) && scopeRef.current) {\n        focusFirstInScope(scopeRef.current);\n      }\n    }\n    autoFocusRef.current = false;\n  }, [scopeRef]);\n}\n\nfunction useActiveScopeTracker(scopeRef: RefObject<Element[] | null>, restore?: boolean, contain?: boolean) {\n  // tracks the active scope, in case restore and contain are both false.\n  // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n  useLayoutEffect(() => {\n    if (restore || contain) {\n      return;\n    }\n\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n\n    let onFocus = (e) => {\n      let target = getEventTarget(e) as Element;\n      if (isElementInScope(target, scopeRef.current)) {\n        activeScope = scopeRef;\n      } else if (!isElementInAnyScope(target)) {\n        activeScope = null;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n  }, [scopeRef, restore, contain]);\n}\n\nfunction shouldRestoreFocus(scopeRef: ScopeRef) {\n  let scope = focusScopeTree.getTreeNode(activeScope);\n  while (scope && scope.scopeRef !== scopeRef) {\n    if (scope.nodeToRestore) {\n      return false;\n    }\n\n    scope = scope.parent;\n  }\n\n  return scope?.scopeRef === scopeRef;\n}\n\nfunction useRestoreFocus(scopeRef: RefObject<Element[] | null>, restoreFocus?: boolean, contain?: boolean) {\n  // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n  // eslint-disable-next-line no-restricted-globals\n  const nodeToRestoreRef = useRef(typeof document !== 'undefined' ? getActiveElement(getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined)) as FocusableElement : null);\n\n  // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n  // restoring-non-containing scopes should only care if they become active so they can perform the restore\n  useLayoutEffect(() => {\n    let scope = scopeRef.current;\n    const ownerDocument = getOwnerDocument(scope ? scope[0] : undefined);\n    if (!restoreFocus || contain) {\n      return;\n    }\n\n    let onFocus = () => {\n      // If focusing an element in a child scope of the currently active scope, the child becomes active.\n      // Moving out of the active scope to an ancestor is not allowed.\n      if ((!activeScope || isAncestorScope(activeScope, scopeRef)) &&\n        isElementInScope(getActiveElement(ownerDocument), scopeRef.current)\n      ) {\n        activeScope = scopeRef;\n      }\n    };\n\n    ownerDocument.addEventListener('focusin', onFocus, false);\n    scope?.forEach(element => element.addEventListener('focusin', onFocus, false));\n    return () => {\n      ownerDocument.removeEventListener('focusin', onFocus, false);\n      scope?.forEach(element => element.removeEventListener('focusin', onFocus, false));\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [scopeRef, contain]);\n\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    // Handle the Tab key so that tabbing out of the scope goes to the next element\n    // after the node that had focus when the scope mounted. This is important when\n    // using portals for overlays, so that focus goes to the expected element when\n    // tabbing out of the overlay.\n    let onKeyDown = (e: KeyboardEvent) => {\n      if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !shouldContainFocus(scopeRef) || e.isComposing) {\n        return;\n      }\n\n      let focusedElement = ownerDocument.activeElement as FocusableElement;\n      if (!isElementInChildScope(focusedElement, scopeRef) || !shouldRestoreFocus(scopeRef)) {\n        return;\n      }\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // Create a DOM tree walker that matches all tabbable elements\n      let walker = getFocusableTreeWalker(ownerDocument.body, {tabbable: true});\n\n      // Find the next tabbable element after the currently focused element\n      walker.currentNode = focusedElement;\n      let nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n\n      if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n        nodeToRestore = undefined;\n        treeNode.nodeToRestore = undefined;\n      }\n\n      // If there is no next element, or it is outside the current scope, move focus to the\n      // next element after the node to restore to instead.\n      if ((!nextElement || !isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n        walker.currentNode = nodeToRestore;\n\n        // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n        do {\n          nextElement = (e.shiftKey ? walker.previousNode() : walker.nextNode()) as FocusableElement;\n        } while (isElementInChildScope(nextElement, scopeRef));\n\n        e.preventDefault();\n        e.stopPropagation();\n        if (nextElement) {\n          focusElement(nextElement, true);\n        } else {\n          // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n          // then move focus to the body.\n          // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n          if (!isElementInAnyScope(nodeToRestore)) {\n            focusedElement.blur();\n          } else {\n            focusElement(nodeToRestore, true);\n          }\n        }\n      }\n    };\n\n    if (!contain) {\n      ownerDocument.addEventListener('keydown', onKeyDown as EventListener, true);\n    }\n\n    return () => {\n      if (!contain) {\n        ownerDocument.removeEventListener('keydown', onKeyDown as EventListener, true);\n      }\n    };\n  }, [scopeRef, restoreFocus, contain]);\n\n  // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n  useLayoutEffect(() => {\n    const ownerDocument = getOwnerDocument(scopeRef.current ? scopeRef.current[0] : undefined);\n\n    if (!restoreFocus) {\n      return;\n    }\n\n    let treeNode = focusScopeTree.getTreeNode(scopeRef);\n    if (!treeNode) {\n      return;\n    }\n    treeNode.nodeToRestore = nodeToRestoreRef.current ?? undefined;\n    return () => {\n      let treeNode = focusScopeTree.getTreeNode(scopeRef);\n      if (!treeNode) {\n        return;\n      }\n      let nodeToRestore = treeNode.nodeToRestore;\n\n      // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n      let activeElement = getActiveElement(ownerDocument);\n      if (\n        restoreFocus\n        && nodeToRestore\n        && (\n          ((activeElement && isElementInChildScope(activeElement, scopeRef)) || (activeElement === ownerDocument.body && shouldRestoreFocus(scopeRef)))\n        )\n      ) {\n        // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n        let clonedTree = focusScopeTree.clone();\n        requestAnimationFrame(() => {\n          // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n          if (ownerDocument.activeElement === ownerDocument.body) {\n            // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n            let treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                restoreFocusToElement(treeNode.nodeToRestore);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n\n            // If no nodeToRestore was found, focus the first element in the nearest\n            // ancestor scope that is still in the tree.\n            treeNode = clonedTree.getTreeNode(scopeRef);\n            while (treeNode) {\n              if (treeNode.scopeRef && treeNode.scopeRef.current && focusScopeTree.getTreeNode(treeNode.scopeRef)) {\n                let node = getFirstInScope(treeNode.scopeRef.current, true);\n                restoreFocusToElement(node);\n                return;\n              }\n              treeNode = treeNode.parent;\n            }\n          }\n        });\n      }\n    };\n  }, [scopeRef, restoreFocus]);\n}\n\nfunction restoreFocusToElement(node: FocusableElement) {\n  // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n  // For example, virtualized collection components reuse DOM elements, so the original element\n  // might still exist in the DOM but representing a different item.\n  if (node.dispatchEvent(new CustomEvent(RESTORE_FOCUS_EVENT, {bubbles: true, cancelable: true}))) {\n    focusElement(node);\n  }\n}\n\n/**\n * Create a [TreeWalker]{@link https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker}\n * that matches all focusable/tabbable elements.\n */\nexport function getFocusableTreeWalker(root: Element, opts?: FocusManagerOptions, scope?: Element[]): ShadowTreeWalker | TreeWalker {\n  let filter = opts?.tabbable ? isTabbable : isFocusable;\n\n  // Ensure that root is an Element or fall back appropriately\n  let rootElement = root?.nodeType === Node.ELEMENT_NODE ? (root as Element) : null;\n\n  // Determine the document to use\n  let doc = getOwnerDocument(rootElement);\n\n  // Create a TreeWalker, ensuring the root is an Element or Document\n  let walker = createShadowTreeWalker(\n    doc,\n    root || doc,\n    NodeFilter.SHOW_ELEMENT,\n    {\n      acceptNode(node) {\n        // Skip nodes inside the starting node.\n        if (opts?.from?.contains(node)) {\n          return NodeFilter.FILTER_REJECT;\n        }\n\n        if (opts?.tabbable\n          && (node as Element).tagName === 'INPUT'\n          && (node as HTMLInputElement).getAttribute('type') === 'radio') {\n          // If the radio is in a form, we can get all the other radios by name\n          if (!isTabbableRadio(node as HTMLInputElement)) {\n            return NodeFilter.FILTER_REJECT;\n          }\n          // If the radio is in the same group as the current node and none are selected, we can skip it\n          if ((walker.currentNode as Element).tagName === 'INPUT'\n            && (walker.currentNode as HTMLInputElement).type === 'radio'\n            && (walker.currentNode as HTMLInputElement).name === (node as HTMLInputElement).name) {\n            return NodeFilter.FILTER_REJECT;\n          }\n        }\n\n        if (filter(node as Element)\n          && (!scope || isElementInScope(node as Element, scope))\n          && (!opts?.accept || opts.accept(node as Element))\n        ) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n\n        return NodeFilter.FILTER_SKIP;\n      }\n    }\n  );\n\n  if (opts?.from) {\n    walker.currentNode = opts.from;\n  }\n\n  return walker;\n}\n\n/**\n * Creates a FocusManager object that can be used to move focus within an element.\n */\nexport function createFocusManager(ref: RefObject<Element | null>, defaultOptions: FocusManagerOptions = {}): FocusManager {\n  return {\n    focusNext(opts: FocusManagerOptions = {}) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      }\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (!nextNode && wrap) {\n        walker.currentNode = root;\n        nextNode = walker.nextNode() as FocusableElement;\n      }\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusPrevious(opts: FocusManagerOptions = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {from, tabbable = defaultOptions.tabbable, wrap = defaultOptions.wrap, accept = defaultOptions.accept} = opts;\n      let node = from || getActiveElement(getOwnerDocument(root));\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      if (root.contains(node)) {\n        walker.currentNode = node!;\n      } else {\n        let next = last(walker);\n        if (next) {\n          focusElement(next, true);\n        }\n        return next ?? null;\n      }\n      let previousNode = walker.previousNode() as FocusableElement;\n      if (!previousNode && wrap) {\n        walker.currentNode = root;\n        let lastNode = last(walker);\n        if (!lastNode) {\n          // couldn't wrap\n          return null;\n        }\n        previousNode = lastNode;\n      }\n      if (previousNode) {\n        focusElement(previousNode, true);\n      }\n      return previousNode ?? null;\n    },\n    focusFirst(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let nextNode = walker.nextNode() as FocusableElement;\n      if (nextNode) {\n        focusElement(nextNode, true);\n      }\n      return nextNode;\n    },\n    focusLast(opts = defaultOptions) {\n      let root = ref.current;\n      if (!root) {\n        return null;\n      }\n      let {tabbable = defaultOptions.tabbable, accept = defaultOptions.accept} = opts;\n      let walker = getFocusableTreeWalker(root, {tabbable, accept});\n      let next = last(walker);\n      if (next) {\n        focusElement(next, true);\n      }\n      return next ?? null;\n    }\n  };\n}\n\nfunction last(walker: ShadowTreeWalker | TreeWalker) {\n  let next: FocusableElement | undefined = undefined;\n  let last: FocusableElement;\n  do {\n    last = walker.lastChild() as FocusableElement;\n    if (last) {\n      next = last;\n    }\n  } while (last);\n  return next;\n}\n\n\nclass Tree {\n  root: TreeNode;\n  private fastMap = new Map<ScopeRef, TreeNode>();\n\n  constructor() {\n    this.root = new TreeNode({scopeRef: null});\n    this.fastMap.set(null, this.root);\n  }\n\n  get size(): number {\n    return this.fastMap.size;\n  }\n\n  getTreeNode(data: ScopeRef): TreeNode | undefined {\n    return this.fastMap.get(data);\n  }\n\n  addTreeNode(scopeRef: ScopeRef, parent: ScopeRef, nodeToRestore?: FocusableElement): void {\n    let parentNode = this.fastMap.get(parent ?? null);\n    if (!parentNode) {\n      return;\n    }\n    let node = new TreeNode({scopeRef});\n    parentNode.addChild(node);\n    node.parent = parentNode;\n    this.fastMap.set(scopeRef, node);\n    if (nodeToRestore) {\n      node.nodeToRestore = nodeToRestore;\n    }\n  }\n\n  addNode(node: TreeNode): void {\n    this.fastMap.set(node.scopeRef, node);\n  }\n\n  removeTreeNode(scopeRef: ScopeRef): void {\n    // never remove the root\n    if (scopeRef === null) {\n      return;\n    }\n    let node = this.fastMap.get(scopeRef);\n    if (!node) {\n      return;\n    }\n    let parentNode = node.parent;\n    // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n    // if we are, then replace the siblings restore with the restore from the scope we're removing\n    for (let current of this.traverse()) {\n      if (\n        current !== node &&\n        node.nodeToRestore &&\n        current.nodeToRestore &&\n        node.scopeRef &&\n        node.scopeRef.current &&\n        isElementInScope(current.nodeToRestore, node.scopeRef.current)\n      ) {\n        current.nodeToRestore = node.nodeToRestore;\n      }\n    }\n    let children = node.children;\n    if (parentNode) {\n      parentNode.removeChild(node);\n      if (children.size > 0) {\n        children.forEach(child => parentNode && parentNode.addChild(child));\n      }\n    }\n\n    this.fastMap.delete(node.scopeRef);\n  }\n\n  // Pre Order Depth First\n  *traverse(node: TreeNode = this.root): Generator<TreeNode> {\n    if (node.scopeRef != null) {\n      yield node;\n    }\n    if (node.children.size > 0) {\n      for (let child of node.children) {\n        yield* this.traverse(child);\n      }\n    }\n  }\n\n  clone(): Tree {\n    let newTree = new Tree();\n    for (let node of this.traverse()) {\n      newTree.addTreeNode(node.scopeRef, node.parent?.scopeRef ?? null, node.nodeToRestore);\n    }\n    return newTree;\n  }\n}\n\nclass TreeNode {\n  public scopeRef: ScopeRef;\n  public nodeToRestore?: FocusableElement;\n  public parent?: TreeNode;\n  public children: Set<TreeNode> = new Set();\n  public contain = false;\n\n  constructor(props: {scopeRef: ScopeRef}) {\n    this.scopeRef = props.scopeRef;\n  }\n  addChild(node: TreeNode): void {\n    this.children.add(node);\n    node.parent = this;\n  }\n  removeChild(node: TreeNode): void {\n    this.children.delete(node);\n    node.parent = undefined;\n  }\n}\n\nexport let focusScopeTree: Tree = new Tree();\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAkED,MAAM,qCAAA,WAAA,GAAe,CAAA,GAAA,wKAAI,EAAE,aAAa,CAAuB;AAC/D,MAAM,4CAAsB;AAE5B,IAAI,oCAAwB;AAYrB,SAAS,0CAAW,KAAsB;IAC/C,IAAI,EAAA,UAAC,QAAQ,EAAA,SAAE,OAAO,EAAA,cAAE,YAAY,EAAA,WAAE,SAAS,EAAC,GAAG;IACnD,IAAI,WAAW,CAAA,iKAAA,SAAK,EAAmB;IACvC,IAAI,SAAS,CAAA,iKAAA,SAAK,EAAmB;IACrC,IAAI,WAAW,CAAA,iKAAA,SAAK,EAAa,EAAE;IACnC,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,iKAAA,aAAS,EAAE,uCAAiB,CAAC;IAEhD,4FAA4F;IAC5F,IAAI,OAAO,CAAA,GAAA,wKAAM,EAAE,IAAM,IAAI,+BAAS;sBAAC;QAAQ,IAAI;QAAC;KAAS;IAE7D,CAAA,2KAAA,kBAAc,EAAE;QACd,+FAA+F;QAC/F,mGAAmG;QACnG,mGAAmG;QACnG,gEAAgE;QAChE,IAAI,SAAS,cAAc,0CAAe,IAAI;QAC9C,IAAI,0CAAe,WAAW,CAAC,OAAO,QAAQ,KAAK,qCAAe,CAAC,sCAAgB,mCAAa,OAAO,QAAQ,GAAG;YAChH,IAAI,aAAa,0CAAe,WAAW,CAAC;YAC5C,IAAI,YACF,SAAS;QAEb;QAEA,+CAA+C;QAC/C,OAAO,QAAQ,CAAC;QAChB,0CAAe,OAAO,CAAC;IACzB,GAAG;QAAC;QAAM;KAAW;IAErB,CAAA,GAAA,0LAAc,EAAE;QACd,IAAI,OAAO,0CAAe,WAAW,CAAC;QACtC,IAAI,MACF,KAAK,OAAO,GAAG,CAAC,CAAC;IAErB,GAAG;QAAC;KAAQ;IAEZ,CAAA,2KAAA,kBAAc,EAAE;YAEH;QADX,2EAA2E;QAC3E,IAAI,OAAA,CAAO,oBAAA,SAAS,OAAO,MAAA,QAAhB,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAkB,WAAW;QACxC,IAAI,QAAmB,EAAE;QACzB,IAAI,kBAAkB,CAAA,IAAK,EAAE,eAAe;QAC5C,MAAO,QAAQ,SAAS,OAAO,OAAO,CAAE;YACtC,MAAM,IAAI,CAAC;YACX,2EAA2E;YAC3E,KAAK,gBAAgB,CAAC,2CAAqB;YAC3C,OAAO,KAAK,WAAW;QACzB;QAEA,SAAS,OAAO,GAAG;QAEnB,OAAO;YACL,KAAK,IAAI,QAAQ,MACf,KAAK,mBAAmB,CAAC,2CAAqB;QAElD;IACF,GAAG;QAAC;KAAS;IAEb,4CAAsB,UAAU,cAAc;IAC9C,0CAAoB,UAAU;IAC9B,sCAAgB,UAAU,cAAc;IACxC,mCAAa,UAAU;IAEvB,mGAAmG;IACnG,uGAAuG;IACvG,CAAA,iKAAA,YAAQ,EAAE;QACR,MAAM,gBAAgB,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QACjG,IAAI,QAAyB;QAE7B,IAAI,uCAAiB,eAAe,SAAS,OAAO,GAAG;YACrD,8EAA8E;YAC9E,+DAA+D;YAC/D,KAAK,IAAI,QAAQ,0CAAe,QAAQ,GACtC,IAAI,KAAK,QAAQ,IAAI,uCAAiB,eAAe,KAAK,QAAQ,CAAC,OAAO,GACxE,QAAQ;YAIZ,IAAI,UAAU,0CAAe,WAAW,CAAC,WACvC,oCAAc,MAAM,QAAQ;QAEhC;IACF,GAAG;QAAC;KAAS;IAEb,yGAAyG;IACzG,mCAAmC;IACnC,CAAA,2KAAA,kBAAc,EAAE;QACd,OAAO;gBAEa,oCAAA;gBAAA;YADlB,mCAAmC;YACnC,IAAI,cAAc,CAAA,8CAAA,CAAA,8BAAA,0CAAe,WAAW,CAAC,SAAA,MAAA,QAA3B,gCAAA,KAAA,IAAA,KAAA,IAAA,CAAA,qCAAA,4BAAsC,MAAM,MAAA,QAA5C,uCAAA,KAAA,IAAA,KAAA,IAAA,mCAA8C,QAAQ,MAAA,QAAtD,gDAAA,KAAA,IAAA,8CAA0D;YAE5E,IACG,CAAA,aAAa,qCAAe,sCAAgB,UAAU,kCAAW,KACjE,CAAA,CAAC,eAAe,0CAAe,WAAW,CAAC,YAAW,GAEvD,oCAAc;YAEhB,0CAAe,cAAc,CAAC;QAChC;IACF,GAAG;QAAC;KAAS;IAEb,IAAI,eAAe,CAAA,GAAA,wKAAM,EAAE,IAAM,iDAA2B,WAAW,EAAE;IACzE,IAAI,QAAQ,CAAA,iKAAA,UAAM,EAAE,IAAO,CAAA;0BACzB;YACA,YAAY;QACd,CAAA,GAAI;QAAC;QAAM;KAAa;IAExB,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,mCAAa,QAAQ,EAAA;QAAC,OAAO;qBAC5B,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,QAAA;QAAK,0BAAA;QAAuB,QAAA;QAAO,KAAK;QACxC,UAAA,WAAA,GACD,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,QAAA;QAAK,wBAAA;QAAqB,QAAA;QAAO,KAAK;;AAG7C;AAOO,SAAS;QACP;IAAP,OAAA,CAAO,cAAA,CAAA,GAAA,2KAAS,EAAE,mCAAA,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,YAA0B,YAAY;AAC/C;AAEA,SAAS,iDAA2B,QAA2C;IAC7E,OAAO;QACL;uBAAU,iEAA4B,CAAC,CAAC;YACtC,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,QAAQ,EAAA,MAAE,IAAI,EAAA,QAAE,MAAM,EAAC,GAAG;gBACgB;YAArD,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,CAAA,UAAA,KAAK,CAAC,EAAE,MAAA,QAAR,YAAA,KAAA,IAAA,UAAY;YACjE,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,sBAAsB;YAC9C,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,uCAAiB,MAAM,SAAS,OAAO;YAC5D,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,CAAC,YAAY,MAAM;gBACrB,OAAO,WAAW,GAAG;gBACrB,WAAW,OAAO,QAAQ;YAC5B;YACA,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA;gBAAc,wEAA4B,CAAC,CAAC;YAC1C,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,QAAQ,EAAA,MAAE,IAAI,EAAA,QAAE,MAAM,EAAC,GAAG;gBACgB;YAArD,IAAI,OAAO,QAAQ,CAAA,uKAAA,oBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,CAAA,UAAA,KAAK,CAAC,EAAE,MAAA,QAAR,YAAA,KAAA,IAAA,UAAY;YACjE,IAAI,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB;YACzD,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,uCAAiB,MAAM,SAAS,OAAQ;YAC7D,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,CAAC,gBAAgB,MAAM;gBACzB,OAAO,WAAW,GAAG;gBACrB,eAAe,OAAO,YAAY;YACpC;YACA,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO;QACT;QACA;uBAAW,iEAAO,CAAC,CAAC;YAClB,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,UAAC,QAAQ,EAAA,QAAE,MAAM,EAAC,GAAG;YACzB,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,sBAAsB;YACpD,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA;uBAAU,iEAAO,CAAC,CAAC;YACjB,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,EAAA,UAAC,QAAQ,EAAA,QAAE,MAAM,EAAC,GAAG;YACzB,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;0BAAC;wBAAU;YAAM,GAAG;YACnE,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB;YAC/D,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO;QACT;IACF;AACF;AAEA,SAAS,mCAAa,KAAgB;IACpC,OAAO,KAAK,CAAC,EAAE,CAAC,aAAa;AAC/B;AAEA,SAAS,yCAAmB,QAAkB;IAC5C,IAAI,QAAQ,0CAAe,WAAW,CAAC;IACvC,MAAO,SAAS,MAAM,QAAQ,KAAK,SAAU;QAC3C,IAAI,MAAM,OAAO,EACf,OAAO;QAGT,QAAQ,MAAM,MAAM;IACtB;IAEA,OAAO;AACT;AAEA,SAAS,sCAAgB,OAAyB;IAChD,IAAI,QAAQ,OAAO,EACjB,OAAO;IAET,IAAI,SAA6B,EAAE;IACnC,IAAI,CAAC,QAAQ,IAAI,EACf,SAAU;WAAI,CAAA,GAAA,sLAAe,EAAE,SAAS,gBAAgB,CAAC,AAAC,0BAA0B,UAAE,IAAI,MAAM,CAAC,QAAQ,IAAI,GAAE,EAAE,CAAC;KAAE,CAAwB,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,IAAI;SAClK;YACW,wBAAA;QAAhB,IAAI,YAAA,CAAY,gBAAA,QAAQ,IAAI,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,CAAA,yBAAA,cAAc,QAAQ,MAAA,QAAtB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAwB,SAAS,CAAC,QAAQ,IAAI;QAC9D,SAAS;eAAK,cAAA,QAAA,cAAA,KAAA,IAAA,YAAa,EAAE;SAAE;IACjC;IACA,IAAI,CAAC,QACH,OAAO;IAET,IAAI,aAAa,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,OAAO;IAEnD,OAAO,CAAC;AACV;AAEA,SAAS,0CAAoB,QAAqC,EAAE,OAAiB;IACnF,IAAI,cAAc,CAAA,iKAAA,SAAK,EAAoB;IAE3C,IAAI,MAAM,CAAA,iKAAA,SAAK,EAA4C;IAC3D,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,QAAQ,SAAS,OAAO;QAC5B,IAAI,CAAC,SAAS;YACZ,sGAAsG;YACtG,IAAI,IAAI,OAAO,EAAE;gBACf,qBAAqB,IAAI,OAAO;gBAChC,IAAI,OAAO,GAAG;YAChB;YACA;QACF;QAEA,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAE1D,uDAAuD;QACvD,IAAI,YAAY,CAAC;YACf,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,yCAAmB,aAAa,EAAE,WAAW,EACzG;YAGF,IAAI,iBAAiB,CAAA,wKAAA,mBAAe,EAAE;YACtC,IAAI,QAAQ,SAAS,OAAO;YAC5B,IAAI,CAAC,SAAS,CAAC,uCAAiB,gBAAgB,QAC9C;YAGF,IAAI,YAAY,mCAAa;YAC7B,IAAI,SAAS,0CAAuB,WAAW;gBAAC,UAAU;YAAI,GAAG;YACjE,IAAI,CAAC,gBACH;YAEF,OAAO,WAAW,GAAG;YACrB,IAAI,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YACvE,IAAI,CAAC,aAAa;gBAChB,OAAO,WAAW,GAAG,EAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,kBAAkB,GAAI,KAAK,CAAC,EAAE,CAAC,sBAAsB;gBAC/G,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YACrE;YAEA,EAAE,cAAc;YAChB,IAAI,aACF,mCAAa,aAAa;QAE9B;QAEA,IAAI,UAAyB,CAAC;YAC5B,mGAAmG;YACnG,gEAAgE;YAChE,IAAK,CAAA,CAAC,qCAAe,sCAAgB,mCAAa,SAAQ,KAAM,uCAAiB,CAAA,wKAAA,iBAAa,EAAE,IAAe,SAAS,OAAO,GAAG;gBAChI,oCAAc;gBACd,YAAY,OAAO,GAAG,CAAA,wKAAA,iBAAa,EAAE;YACvC,OAAO,IAAI,yCAAmB,aAAa,CAAC,4CAAsB,CAAA,wKAAA,iBAAa,EAAE,IAAe,WAAW;gBACzG,+FAA+F;gBAC/F,kGAAkG;gBAClG,IAAI,YAAY,OAAO,EACrB,YAAY,OAAO,CAAC,KAAK;qBACpB,IAAI,qCAAe,kCAAY,OAAO,EAC3C,wCAAkB,kCAAY,OAAO;YAEzC,OAAO,IAAI,yCAAmB,WAC5B,YAAY,OAAO,GAAG,CAAA,wKAAA,iBAAa,EAAE;QAEzC;QAEA,IAAI,SAAwB,CAAC;YAC3B,uEAAuE;YACvE,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;YAElC,IAAI,OAAO,GAAG,sBAAsB;gBAClC,iHAAiH;gBACjH,kHAAkH;gBAClH,mHAAmH;gBACnH,IAAI,WAAW,CAAA,kLAAA,yBAAqB;gBACpC,IAAI,yBAA0B,CAAA,aAAa,aAAa,aAAa,IAAG,KAAM,CAAA,oKAAA,YAAQ,OAAO,CAAA,GAAA,4KAAO;gBAEpG,mGAAmG;gBACnG,IAAI,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;gBACrC,IAAI,CAAC,0BAA0B,iBAAiB,yCAAmB,aAAa,CAAC,4CAAsB,eAAe,WAAW;oBAC/H,oCAAc;oBACd,IAAI,SAAS,CAAA,wKAAA,iBAAa,EAAE;oBAC5B,IAAI,UAAU,OAAO,WAAW,EAAE;4BAEhC;wBADA,YAAY,OAAO,GAAG;yBACtB,uBAAA,YAAY,OAAO,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAqB,KAAK;oBAC5B,OAAO,IAAI,kCAAY,OAAO,EAC5B,wCAAkB,kCAAY,OAAO;gBAEzC;YACF;QACF;QAEA,cAAc,gBAAgB,CAAC,WAAW,WAAW;QACrD,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,YAAY,QAAQ;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,WAAW;YACxD,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;YAC1E,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,YAAY,QAAQ;QAC5E;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,uFAAuF;IAEvF,CAAA,2KAAA,kBAAc,EAAE;QACd,OAAO;YACL,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;QAEpC;IACF,GAAG;QAAC;KAAI;AACV;AAEA,SAAS,0CAAoB,OAAgB;IAC3C,OAAO,4CAAsB;AAC/B;AAEA,SAAS,uCAAiB,OAAwB,EAAE,KAAwB;IAC1E,IAAI,CAAC,SACH,OAAO;IAET,IAAI,CAAC,OACH,OAAO;IAET,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;AAC1C;AAEA,SAAS,4CAAsB,OAAgB;gBAAE,iEAAkB,IAAI;IACrE,+FAA+F;IAC/F,IAAI,mBAAmB,WAAW,QAAQ,OAAO,CAAC,gCAChD,OAAO;IAGT,oFAAoF;IACpF,8CAA8C;IAC9C,KAAK,IAAI,EAAC,UAAU,CAAC,EAAC,IAAI,0CAAe,QAAQ,CAAC,0CAAe,WAAW,CAAC,QAAS;QACpF,IAAI,KAAK,uCAAiB,SAAS,EAAE,OAAO,GAC1C,OAAO;IAEX;IAEA,OAAO;AACT;AAGO,SAAS,0CAA8B,OAAgB;IAC5D,OAAO,4CAAsB,SAAS;AACxC;AAEA,SAAS,sCAAgB,QAAkB,EAAE,KAAe;QAC7C;IAAb,IAAI,SAAA,CAAS,8BAAA,0CAAe,WAAW,CAAC,MAAA,MAAA,QAA3B,gCAAA,KAAA,IAAA,KAAA,IAAA,4BAAmC,MAAM;IACtD,MAAO,OAAQ;QACb,IAAI,OAAO,QAAQ,KAAK,UACtB,OAAO;QAET,SAAS,OAAO,MAAM;IACxB;IACA,OAAO;AACT;AAEA,SAAS,mCAAa,OAAgC;iBAAE,iEAAS,KAAK;IACpE,IAAI,WAAW,QAAQ,CAAC,QACtB,IAAI;QACF,CAAA,8KAAA,cAAU,EAAE;IACd,EAAE,UAAM;IACN,SAAS;IACX;SACK,IAAI,WAAW,MACpB,IAAI;QACF,QAAQ,KAAK;IACf,EAAE,UAAM;IACN,SAAS;IACX;AAEJ;AAEA,SAAS,sCAAgB,KAAgB;mBAAE,iEAAW,IAAI;IACxD,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC,sBAAsB;IAC9C,IAAI,YAAY,mCAAa;IAC7B,IAAI,SAAS,0CAAuB,WAAW;kBAAC;IAAQ,GAAG;IAC3D,OAAO,WAAW,GAAG;IACrB,IAAI,WAAW,OAAO,QAAQ;IAE9B,qFAAqF;IACrF,IAAI,YAAY,CAAC,UAAU;QACzB,YAAY,mCAAa;QACzB,SAAS,0CAAuB,WAAW;YAAC,UAAU;QAAK,GAAG;QAC9D,OAAO,WAAW,GAAG;QACrB,WAAW,OAAO,QAAQ;IAC5B;IAEA,OAAO;AACT;AAEA,SAAS,wCAAkB,KAAgB;mBAAE,iEAAmB,IAAI;IAClE,mCAAa,sCAAgB,OAAO;AACtC;AAEA,SAAS,mCAAa,QAAqC,EAAE,SAAmB;IAC9E,MAAM,eAAe,CAAA,iKAAA,UAAI,EAAE,MAAM,CAAC;IAClC,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,oCAAc;YACd,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;YAChF,IAAI,CAAC,uCAAiB,CAAA,wKAAA,mBAAe,EAAE,gBAAgB,kCAAY,OAAO,KAAK,SAAS,OAAO,EAC7F,wCAAkB,SAAS,OAAO;QAEtC;QACA,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;KAAS;AACf;AAEA,SAAS,4CAAsB,QAAqC,EAAE,OAAiB,EAAE,OAAiB;IACxG,uEAAuE;IACvE,iFAAiF;IACjF,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,WAAW,SACb;QAGF,IAAI,QAAQ,SAAS,OAAO;QAC5B,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAE1D,IAAI,UAAU,CAAC;YACb,IAAI,SAAS,CAAA,GAAA,sLAAa,EAAE;YAC5B,IAAI,uCAAiB,QAAQ,SAAS,OAAO,GAC3C,oCAAc;iBACT,IAAI,CAAC,0CAAoB,SAC9B,oCAAc;QAElB;QAEA,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;QAC5E;IACF,GAAG;QAAC;QAAU;QAAS;KAAQ;AACjC;AAEA,SAAS,yCAAmB,QAAkB;IAC5C,IAAI,QAAQ,0CAAe,WAAW,CAAC;IACvC,MAAO,SAAS,MAAM,QAAQ,KAAK,SAAU;QAC3C,IAAI,MAAM,aAAa,EACrB,OAAO;QAGT,QAAQ,MAAM,MAAM;IACtB;IAEA,OAAO,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,QAAQ,MAAK;AAC7B;AAEA,SAAS,sCAAgB,QAAqC,EAAE,YAAsB,EAAE,OAAiB;IACvG,kIAAkI;IAClI,iDAAiD;IACjD,MAAM,mBAAmB,CAAA,iKAAA,SAAK,EAAE,OAAO,aAAa,cAAc,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG,cAAkC;IAE/K,4IAA4I;IAC5I,yGAAyG;IACzG,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,QAAQ,SAAS,OAAO;QAC5B,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,QAAQ,KAAK,CAAC,EAAE,GAAG;QAC1D,IAAI,CAAC,gBAAgB,SACnB;QAGF,IAAI,UAAU;YACZ,mGAAmG;YACnG,gEAAgE;YAChE,IAAK,CAAA,CAAC,qCAAe,sCAAgB,mCAAa,SAAQ,KACxD,uCAAiB,CAAA,GAAA,wLAAe,EAAE,gBAAgB,SAAS,OAAO,GAElE,oCAAc;QAElB;QAEA,cAAc,gBAAgB,CAAC,WAAW,SAAS;QACnD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,gBAAgB,CAAC,WAAW,SAAS;QACvE,OAAO;YACL,cAAc,mBAAmB,CAAC,WAAW,SAAS;YACtD,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,OAAO,CAAC,CAAA,UAAW,QAAQ,mBAAmB,CAAC,WAAW,SAAS;QAC5E;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAU;KAAQ;IAEtB,CAAA,2KAAA,kBAAc,EAAE;QACd,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QAEhF,IAAI,CAAC,cACH;QAGF,+EAA+E;QAC/E,+EAA+E;QAC/E,8EAA8E;QAC9E,8BAA8B;QAC9B,IAAI,YAAY,CAAC;YACf,IAAI,EAAE,GAAG,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,yCAAmB,aAAa,EAAE,WAAW,EACzG;YAGF,IAAI,iBAAiB,cAAc,aAAa;YAChD,IAAI,CAAC,4CAAsB,gBAAgB,aAAa,CAAC,yCAAmB,WAC1E;YAEF,IAAI,WAAW,0CAAe,WAAW,CAAC;YAC1C,IAAI,CAAC,UACH;YAEF,IAAI,gBAAgB,SAAS,aAAa;YAE1C,8DAA8D;YAC9D,IAAI,SAAS,0CAAuB,cAAc,IAAI,EAAE;gBAAC,UAAU;YAAI;YAEvE,qEAAqE;YACrE,OAAO,WAAW,GAAG;YACrB,IAAI,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;YAEvE,IAAI,CAAC,iBAAiB,CAAC,cAAc,WAAW,IAAI,kBAAkB,cAAc,IAAI,EAAE;gBACxF,gBAAgB;gBAChB,SAAS,aAAa,GAAG;YAC3B;YAEA,qFAAqF;YACrF,qDAAqD;YACrD,IAAK,CAAA,CAAC,eAAe,CAAC,4CAAsB,aAAa,SAAQ,KAAM,eAAe;gBACpF,OAAO,WAAW,GAAG;gBAErB,kGAAkG;gBAClG,GACE,cAAe,EAAE,QAAQ,GAAG,OAAO,YAAY,KAAK,OAAO,QAAQ;uBAC5D,4CAAsB,aAAa,UAAW;gBAEvD,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,IAAI,aACF,mCAAa,aAAa;qBAG1B,+BAA+B;gBAC/B,0IAA0I;gBAC1I,IAAI,CAAC,0CAAoB,gBACvB,eAAe,IAAI;qBAEnB,mCAAa,eAAe;YAGlC;QACF;QAEA,IAAI,CAAC,SACH,cAAc,gBAAgB,CAAC,WAAW,WAA4B;QAGxE,OAAO;YACL,IAAI,CAAC,SACH,cAAc,mBAAmB,CAAC,WAAW,WAA4B;QAE7E;IACF,GAAG;QAAC;QAAU;QAAc;KAAQ;IAEpC,+GAA+G;IAC/G,CAAA,GAAA,0LAAc,EAAE;QACd,MAAM,gBAAgB,CAAA,sKAAA,mBAAe,EAAE,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,EAAE,GAAG;QAEhF,IAAI,CAAC,cACH;QAGF,IAAI,WAAW,0CAAe,WAAW,CAAC;QAC1C,IAAI,CAAC,UACH;YAEuB;QAAzB,SAAS,aAAa,GAAG,CAAA,4BAAA,iBAAiB,OAAO,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAA4B;QACrD,OAAO;YACL,IAAI,WAAW,0CAAe,WAAW,CAAC;YAC1C,IAAI,CAAC,UACH;YAEF,IAAI,gBAAgB,SAAS,aAAa;YAE1C,wGAAwG;YACxG,IAAI,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;YACrC,IACE,gBACG,iBAEA,CAAC,iBAAiB,4CAAsB,eAAe,aAAe,kBAAkB,cAAc,IAAI,IAAI,yCAAmB,SAAS,GAE7I;gBACA,6GAA6G;gBAC7G,IAAI,aAAa,0CAAe,KAAK;gBACrC,sBAAsB;oBACpB,0HAA0H;oBAC1H,IAAI,cAAc,aAAa,KAAK,cAAc,IAAI,EAAE;wBACtD,oFAAoF;wBACpF,IAAI,WAAW,WAAW,WAAW,CAAC;wBACtC,MAAO,SAAU;4BACf,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,WAAW,EAAE;gCAChE,4CAAsB,SAAS,aAAa;gCAC5C;4BACF;4BACA,WAAW,SAAS,MAAM;wBAC5B;wBAEA,wEAAwE;wBACxE,4CAA4C;wBAC5C,WAAW,WAAW,WAAW,CAAC;wBAClC,MAAO,SAAU;4BACf,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,OAAO,IAAI,0CAAe,WAAW,CAAC,SAAS,QAAQ,GAAG;gCACnG,IAAI,OAAO,sCAAgB,SAAS,QAAQ,CAAC,OAAO,EAAE;gCACtD,4CAAsB;gCACtB;4BACF;4BACA,WAAW,SAAS,MAAM;wBAC5B;oBACF;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAa;AAC7B;AAEA,SAAS,4CAAsB,IAAsB;IACnD,6FAA6F;IAC7F,6FAA6F;IAC7F,kEAAkE;IAClE,IAAI,KAAK,aAAa,CAAC,IAAI,YAAY,2CAAqB;QAAC,SAAS;QAAM,YAAY;IAAI,KAC1F,mCAAa;AAEjB;AAMO,SAAS,0CAAuB,IAAa,EAAE,IAA0B,EAAE,KAAiB;IACjG,IAAI,SAAS,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,IAAG,CAAA,sKAAA,cAAS,IAAI,CAAA,uKAAA,cAAU;IAErD,4DAA4D;IAC5D,IAAI,cAAc,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,MAAK,KAAK,YAAY,GAAI,OAAmB;IAE7E,gCAAgC;IAChC,IAAI,MAAM,CAAA,sKAAA,mBAAe,EAAE;IAE3B,mEAAmE;IACnE,IAAI,SAAS,CAAA,4KAAA,yBAAqB,EAChC,KACA,QAAQ,KACR,WAAW,YAAY,EACvB;QACE,YAAW,IAAI;gBAET;YADJ,uCAAuC;YACvC,IAAI,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,aAAA,KAAM,IAAI,MAAA,QAAV,eAAA,KAAA,IAAA,KAAA,IAAA,WAAY,QAAQ,CAAC,OACvB,OAAO,WAAW,aAAa;YAGjC,IAAI,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,QAAQ,KACZ,KAAiB,OAAO,KAAK,WAC7B,KAA0B,YAAY,CAAC,YAAY,SAAS;gBAChE,qEAAqE;gBACrE,IAAI,CAAC,sCAAgB,OACnB,OAAO,WAAW,aAAa;gBAEjC,8FAA8F;gBAC9F,IAAK,OAAO,WAAW,CAAa,OAAO,KAAK,WAC1C,OAAO,WAAW,CAAsB,IAAI,KAAK,WACjD,OAAO,WAAW,CAAsB,IAAI,KAAM,KAA0B,IAAI,EACpF,OAAO,WAAW,aAAa;YAEnC;YAEA,IAAI,OAAO,SACL,CAAA,CAAC,SAAS,uCAAiB,MAAiB,MAAK,KACjD,CAAA,CAAA,CAAC,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,MAAM,KAAI,KAAK,MAAM,CAAC,KAAe,GAEhD,OAAO,WAAW,aAAa;YAGjC,OAAO,WAAW,WAAW;QAC/B;IACF;IAGF,IAAI,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,IAAI,EACZ,OAAO,WAAW,GAAG,KAAK,IAAI;IAGhC,OAAO;AACT;AAKO,SAAS,yCAAmB,GAA8B;yBAAE,iEAAsC,CAAC,CAAC;IACzG,OAAO;QACL;gBAAU,wEAA4B,CAAC,CAAC;YACtC,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,WAAW,eAAe,QAAQ,EAAA,MAAE,OAAO,eAAe,IAAI,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC7G,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE;YACrD,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,KAAK,QAAQ,CAAC,OAChB,OAAO,WAAW,GAAG;YAEvB,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,CAAC,YAAY,MAAM;gBACrB,OAAO,WAAW,GAAG;gBACrB,WAAW,OAAO,QAAQ;YAC5B;YACA,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA;gBAAc,wEAA4B,cAAc;YACtD,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,MAAC,IAAI,EAAA,UAAE,WAAW,eAAe,QAAQ,EAAA,MAAE,OAAO,eAAe,IAAI,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC7G,IAAI,OAAO,QAAQ,CAAA,wKAAA,mBAAe,EAAE,CAAA,sKAAA,mBAAe,EAAE;YACrD,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,KAAK,QAAQ,CAAC,OAChB,OAAO,WAAW,GAAG;iBAChB;gBACL,IAAI,OAAO,2BAAK;gBAChB,IAAI,MACF,mCAAa,MAAM;gBAErB,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ;YACjB;YACA,IAAI,eAAe,OAAO,YAAY;YACtC,IAAI,CAAC,gBAAgB,MAAM;gBACzB,OAAO,WAAW,GAAG;gBACrB,IAAI,WAAW,2BAAK;gBACpB,IAAI,CAAC,UACH,AACA,OAAO,SADS;gBAGlB,eAAe;YACjB;YACA,IAAI,cACF,mCAAa,cAAc;YAE7B,OAAO,iBAAA,QAAA,iBAAA,KAAA,IAAA,eAAgB;QACzB;QACA;uBAAW,iEAAO,cAAc;YAC9B,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,UAAC,WAAW,eAAe,QAAQ,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC3E,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,WAAW,OAAO,QAAQ;YAC9B,IAAI,UACF,mCAAa,UAAU;YAEzB,OAAO;QACT;QACA;gBAAU,wEAAO,cAAc;YAC7B,IAAI,OAAO,IAAI,OAAO;YACtB,IAAI,CAAC,MACH,OAAO;YAET,IAAI,EAAA,UAAC,WAAW,eAAe,QAAQ,EAAA,QAAE,SAAS,eAAe,MAAM,EAAC,GAAG;YAC3E,IAAI,SAAS,0CAAuB,MAAM;0BAAC;wBAAU;YAAM;YAC3D,IAAI,OAAO,2BAAK;YAChB,IAAI,MACF,mCAAa,MAAM;YAErB,OAAO,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ;QACjB;IACF;AACF;AAEA,SAAS,2BAAK,MAAqC;IACjD,IAAI,OAAqC;IACzC,IAAI;IACJ,GAAG;QACD,OAAO,OAAO,SAAS;QACvB,IAAI,MACF,OAAO;IAEX,QAAS,KAAM;IACf,OAAO;AACT;AAGA,MAAM;IASJ,IAAI,OAAe;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI;IAC1B;IAEA,YAAY,IAAc,EAAwB;QAChD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;IAC1B;IAEA,YAAY,QAAkB,EAAE,MAAgB,EAAE,aAAgC,EAAQ;QACxF,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,WAAA,QAAA,WAAA,KAAA,IAAA,SAAU;QAC5C,IAAI,CAAC,YACH;QAEF,IAAI,OAAO,IAAI,+BAAS;sBAAC;QAAQ;QACjC,WAAW,QAAQ,CAAC;QACpB,KAAK,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU;QAC3B,IAAI,eACF,KAAK,aAAa,GAAG;IAEzB;IAEA,QAAQ,IAAc,EAAQ;QAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;IAClC;IAEA,eAAe,QAAkB,EAAQ;QACvC,wBAAwB;QACxB,IAAI,aAAa,MACf;QAEF,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MACH;QAEF,IAAI,aAAa,KAAK,MAAM;QAC5B,+HAA+H;QAC/H,8FAA8F;QAC9F,KAAK,IAAI,WAAW,IAAI,CAAC,QAAQ,GAC/B,IACE,YAAY,QACZ,KAAK,aAAa,IAClB,QAAQ,aAAa,IACrB,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,OAAO,IACrB,uCAAiB,QAAQ,aAAa,EAAE,KAAK,QAAQ,CAAC,OAAO,GAE7D,QAAQ,aAAa,GAAG,KAAK,aAAa;QAG9C,IAAI,WAAW,KAAK,QAAQ;QAC5B,IAAI,YAAY;YACd,WAAW,WAAW,CAAC;YACvB,IAAI,SAAS,IAAI,GAAG,GAClB,SAAS,OAAO,CAAC,CAAA,QAAS,cAAc,WAAW,QAAQ,CAAC;QAEhE;QAEA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ;IACnC;IAEA,wBAAwB;IACxB,CAAC,WAA0D;mBAAjD,iEAAiB,IAAI,CAAC,IAAI;QAClC,IAAI,KAAK,QAAQ,IAAI,MACnB,MAAM;QAER,IAAI,KAAK,QAAQ,CAAC,IAAI,GAAG,GACvB,KAAK,IAAI,SAAS,KAAK,QAAQ,CAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC;IAG3B;IAEA,QAAc;YAGyB;QAFrC,IAAI,UAAU,IAAI;YAEmB;QADrC,KAAK,IAAI,QAAQ,IAAI,CAAC,QAAQ,GAC5B,QAAQ,WAAW,CAAC,KAAK,QAAQ,EAAE,CAAA,wBAAA,CAAA,eAAA,KAAK,MAAM,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,QAAQ,MAAA,QAArB,0BAAA,KAAA,IAAA,wBAAyB,MAAM,KAAK,aAAa;QAEtF,OAAO;IACT;IApFA,aAAc;aAFN,OAAA,GAAU,IAAI;QAGpB,IAAI,CAAC,IAAI,GAAG,IAAI,+BAAS;YAAC,UAAU;QAAI;QACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI;IAClC;AAkFF;AAEA,MAAM;IAUJ,SAAS,IAAc,EAAQ;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAClB,KAAK,MAAM,GAAG,IAAI;IACpB;IACA,YAAY,IAAc,EAAQ;QAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrB,KAAK,MAAM,GAAG;IAChB;IAVA,YAAY,KAA2B,CAAE;aAHlC,QAAA,GAA0B,IAAI;aAC9B,OAAA,GAAU;QAGf,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;IAChC;AASF;AAEO,IAAI,4CAAuB,IAAI", "debugId": null}}, {"offset": {"line": 2417, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/focus/dist/virtualFocus.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/focus/dist/packages/%40react-aria/focus/src/virtualFocus.ts"], "sourcesContent": ["import {getActiveElement, getOwnerDocument} from '@react-aria/utils';\n\nexport function moveVirtualFocus(to: Element | null): void {\n  let from = getVirtuallyFocusedElement(getOwnerDocument(to));\n  if (from !== to) {\n    if (from) {\n      dispatchVirtualBlur(from, to);\n    }\n    if (to) {\n      dispatchVirtualFocus(to, from);\n    }\n  }\n}\n\nexport function dispatchVirtualBlur(from: Element, to: Element | null): void {\n  from.dispatchEvent(new FocusEvent('blur', {relatedTarget: to}));\n  from.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: to}));\n}\n\nexport function dispatchVirtualFocus(to: Element, from: Element | null): void {\n  to.dispatchEvent(new FocusEvent('focus', {relatedTarget: from}));\n  to.dispatchEvent(new FocusEvent('focusin', {bubbles: true, relatedTarget: from}));\n}\n\nexport function getVirtuallyFocusedElement(document: Document): Element | null {\n  let activeElement = getActiveElement(document);\n  let activeDescendant = activeElement?.getAttribute('aria-activedescendant');\n  if (activeDescendant) {\n    return document.getElementById(activeDescendant) || activeElement;\n  }\n\n  return activeElement;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEO,SAAS,0CAAiB,EAAkB;IACjD,IAAI,OAAO,0CAA2B,CAAA,sKAAA,mBAAe,EAAE;IACvD,IAAI,SAAS,IAAI;QACf,IAAI,MACF,0CAAoB,MAAM;QAE5B,IAAI,IACF,0CAAqB,IAAI;IAE7B;AACF;AAEO,SAAS,0CAAoB,IAAa,EAAE,EAAkB;IACnE,KAAK,aAAa,CAAC,IAAI,WAAW,QAAQ;QAAC,eAAe;IAAE;IAC5D,KAAK,aAAa,CAAC,IAAI,WAAW,YAAY;QAAC,SAAS;QAAM,eAAe;IAAE;AACjF;AAEO,SAAS,0CAAqB,EAAW,EAAE,IAAoB;IACpE,GAAG,aAAa,CAAC,IAAI,WAAW,SAAS;QAAC,eAAe;IAAI;IAC7D,GAAG,aAAa,CAAC,IAAI,WAAW,WAAW;QAAC,SAAS;QAAM,eAAe;IAAI;AAChF;AAEO,SAAS,0CAA2B,QAAkB;IAC3D,IAAI,gBAAgB,CAAA,wKAAA,mBAAe,EAAE;IACrC,IAAI,mBAAmB,kBAAA,QAAA,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAe,YAAY,CAAC;IACnD,IAAI,kBACF,OAAO,SAAS,cAAc,CAAC,qBAAqB;IAGtD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/Overlay.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/Overlay.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ClearPressResponder} from '@react-aria/interactions';\nimport {FocusScope} from '@react-aria/focus';\nimport React, {ReactNode, useContext, useMemo, useState} from 'react';\nimport ReactDOM from 'react-dom';\nimport {useIsSSR} from '@react-aria/ssr';\nimport {useLayoutEffect} from '@react-aria/utils';\nimport {useUNSAFE_PortalContext} from './PortalProvider';\n\nexport interface OverlayProps {\n  /**\n   * The container element in which the overlay portal will be placed.\n   * @default document.body\n   */\n  portalContainer?: Element,\n  /** The overlay to render in the portal. */\n  children: ReactNode,\n  /**\n   * Disables default focus management for the overlay, including containment and restoration.\n   * This option should be used very carefully. When focus management is disabled, you must\n   * implement focus containment and restoration to ensure the overlay is keyboard accessible.\n   */\n  disableFocusManagement?: boolean,\n  /**\n   * Whether to contain focus within the overlay.\n   */\n  shouldContainFocus?: boolean,\n  /**\n   * Whether the overlay is currently performing an exit animation. When true,\n   * focus is allowed to move outside.\n   */\n  isExiting?: boolean\n}\n\nexport const OverlayContext: React.Context<{contain: boolean, setContain: React.Dispatch<React.SetStateAction<boolean>>} | null> =\n  React.createContext<{contain: boolean, setContain: React.Dispatch<React.SetStateAction<boolean>>} | null>(null);\n\n/**\n * A container which renders an overlay such as a popover or modal in a portal,\n * and provides a focus scope for the child elements.\n */\nexport function Overlay(props: OverlayProps): React.ReactPortal | null {\n  let isSSR = useIsSSR();\n  let {portalContainer = isSSR ? null : document.body, isExiting} = props;\n  let [contain, setContain] = useState(false);\n  let contextValue = useMemo(() => ({contain, setContain}), [contain, setContain]);\n\n  let {getContainer} = useUNSAFE_PortalContext();\n  if (!props.portalContainer && getContainer) {\n    portalContainer = getContainer();\n  }\n\n  if (!portalContainer) {\n    return null;\n  }\n\n  let contents = props.children;\n  if (!props.disableFocusManagement) {\n    contents = (\n      <FocusScope restoreFocus contain={(props.shouldContainFocus || contain) && !isExiting}>\n        {contents}\n      </FocusScope>\n    );\n  }\n\n  contents = (\n    <OverlayContext.Provider value={contextValue}>\n      <ClearPressResponder>\n        {contents}\n      </ClearPressResponder>\n    </OverlayContext.Provider>\n  );\n\n  return ReactDOM.createPortal(contents, portalContainer);\n}\n\n/** @private */\nexport function useOverlayFocusContain(): void {\n  let ctx = useContext(OverlayContext);\n  let setContain = ctx?.setContain;\n  useLayoutEffect(() => {\n    setContain?.(true);\n  }, [setContain]);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAmCM,MAAM,4CAAA,WAAA,GACX,CAAA,iKAAA,UAAI,EAAE,aAAa,CAAuF;AAMrG,SAAS,0CAAQ,KAAmB;IACzC,IAAI,QAAQ,CAAA,qKAAA,WAAO;IACnB,IAAI,EAAA,iBAAC,kBAAkB,QAAQ,OAAO,SAAS,IAAI,EAAA,WAAE,SAAS,EAAC,GAAG;IAClE,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IACrC,IAAI,eAAe,CAAA,iKAAA,UAAM,EAAE,IAAO,CAAA;qBAAC;wBAAS;QAAU,CAAA,GAAI;QAAC;QAAS;KAAW;IAE/E,IAAI,EAAA,cAAC,YAAY,EAAC,GAAG,CAAA,6KAAA,0BAAsB;IAC3C,IAAI,CAAC,MAAM,eAAe,IAAI,cAC5B,kBAAkB;IAGpB,IAAI,CAAC,iBACH,OAAO;IAGT,IAAI,WAAW,MAAM,QAAQ;IAC7B,IAAI,CAAC,MAAM,sBAAsB,EAC/B,WAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,sKAAA,aAAS,GAAA;QAAE,cAAA;QAAa,SAAU,CAAA,MAAM,kBAAkB,IAAI,OAAM,KAAM,CAAC;OACzE;IAKP,WAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,0CAAe,QAAQ,EAAA;QAAC,OAAO;qBAC9B,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,iLAAA,sBAAkB,GAAA,MAChB;IAKP,OAAA,WAAA,GAAO,CAAA,wKAAA,UAAO,EAAE,YAAY,CAAC,UAAU;AACzC;AAGO,SAAS;IACd,IAAI,MAAM,CAAA,iKAAA,aAAS,EAAE;IACrB,IAAI,aAAa,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,UAAU;IAChC,CAAA,2KAAA,kBAAc,EAAE;QACd,eAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAa;IACf,GAAG;QAAC;KAAW;AACjB", "debugId": null}}, {"offset": {"line": 2532, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/usePreventScroll.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/usePreventScroll.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain, getScrollParent, isIOS, useLayoutEffect} from '@react-aria/utils';\n\ninterface PreventScrollOptions {\n  /** Whether the scroll lock is disabled. */\n  isDisabled?: boolean\n}\n\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\n\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n  'checkbox',\n  'radio',\n  'range',\n  'color',\n  'file',\n  'image',\n  'button',\n  'submit',\n  'reset'\n]);\n\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */\nexport function usePreventScroll(options: PreventScrollOptions = {}): void {\n  let {isDisabled} = options;\n\n  useLayoutEffect(() => {\n    if (isDisabled) {\n      return;\n    }\n\n    preventScrollCount++;\n    if (preventScrollCount === 1) {\n      if (isIOS()) {\n        restore = preventScrollMobileSafari();\n      } else {\n        restore = preventScrollStandard();\n      }\n    }\n\n    return () => {\n      preventScrollCount--;\n      if (preventScrollCount === 0) {\n        restore();\n      }\n    };\n  }, [isDisabled]);\n}\n\n// For most browsers, all we need to do is set `overflow: hidden` on the root element, and\n// add some padding to prevent the page from shifting when the scrollbar is hidden.\nfunction preventScrollStandard() {\n  let scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  return chain(\n    scrollbarWidth > 0 &&\n      // Use scrollbar-gutter when supported because it also works for fixed positioned elements.\n      ('scrollbarGutter' in document.documentElement.style\n        ? setStyle(document.documentElement, 'scrollbarGutter', 'stable')\n        : setStyle(document.documentElement, 'paddingRight', `${scrollbarWidth}px`)),\n    setStyle(document.documentElement, 'overflow', 'hidden')\n  );\n}\n\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Set `overscroll-behavior: contain` on nested scrollable regions so they do not scroll the page when at\n//    the top or bottom. Work around a bug where this does not work when the element does not actually overflow\n//    by preventing default in a `touchmove` event.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n  let scrollable: Element;\n  let restoreScrollableStyles;\n  let onTouchStart = (e: TouchEvent) => {\n    // Store the nearest scrollable parent element from the element that the user touched.\n    scrollable = getScrollParent(e.target as Element, true);\n    if (scrollable === document.documentElement && scrollable === document.body) {\n      return;\n    }\n\n    // Prevent scrolling up when at the top and scrolling down when at the bottom\n    // of a nested scrollable area, otherwise mobile Safari will start scrolling\n    // the window instead.\n    if (scrollable instanceof HTMLElement && window.getComputedStyle(scrollable).overscrollBehavior === 'auto') {\n      restoreScrollableStyles = setStyle(scrollable, 'overscrollBehavior', 'contain');\n    }\n  };\n\n  let onTouchMove = (e: TouchEvent) => {\n    // Prevent scrolling the window.\n    if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n      e.preventDefault();\n      return;\n    }\n\n    // overscroll-behavior should prevent scroll chaining, but currently does not\n    // if the element doesn't actually overflow. https://bugs.webkit.org/show_bug.cgi?id=243452\n    // This checks that both the width and height do not overflow, otherwise we might\n    // block horizontal scrolling too. In that case, adding `touch-action: pan-x` to\n    // the element will prevent vertical page scrolling. We can't add that automatically\n    // because it must be set before the touchstart event.\n    if (scrollable.scrollHeight === scrollable.clientHeight && scrollable.scrollWidth === scrollable.clientWidth) {\n      e.preventDefault();\n    }\n  };\n\n  let onTouchEnd = () => {\n    if (restoreScrollableStyles) {\n      restoreScrollableStyles();\n    }\n  };\n\n  let onFocus = (e: FocusEvent) => {\n    let target = e.target as HTMLElement;\n    if (willOpenKeyboard(target)) {\n      setupStyles();\n\n      // Apply a transform to trick Safari into thinking the input is at the top of the page\n      // so it doesn't try to scroll it into view.\n      target.style.transform = 'translateY(-2000px)';\n      requestAnimationFrame(() => {\n        target.style.transform = '';\n\n        // This will have prevented the browser from scrolling the focused element into view,\n        // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n        if (visualViewport) {\n          if (visualViewport.height < window.innerHeight) {\n            // If the keyboard is already visible, do this after one additional frame\n            // to wait for the transform to be removed.\n            requestAnimationFrame(() => {\n              scrollIntoView(target);\n            });\n          } else {\n            // Otherwise, wait for the visual viewport to resize before scrolling so we can\n            // measure the correct position to scroll to.\n            visualViewport.addEventListener('resize', () => scrollIntoView(target), {once: true});\n          }\n        }\n      });\n    }\n  };\n\n  let restoreStyles: null | (() => void) = null;\n  let setupStyles = () => {\n    if (restoreStyles) {\n      return;\n    }\n\n    let onWindowScroll = () => {\n      // Last resort. If the window scrolled, scroll it back to the top.\n      // It should always be at the top because the body will have a negative margin (see below).\n      window.scrollTo(0, 0);\n    };\n\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n\n    restoreStyles = chain(\n      addEvent(window, 'scroll', onWindowScroll),\n      setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`),\n      setStyle(document.documentElement, 'overflow', 'hidden'),\n      setStyle(document.body, 'marginTop', `-${scrollY}px`),\n      () => {\n        window.scrollTo(scrollX, scrollY);\n      }\n    );\n\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n  };\n\n  let removeEvents = chain(\n    addEvent(document, 'touchstart', onTouchStart, {passive: false, capture: true}),\n    addEvent(document, 'touchmove', onTouchMove, {passive: false, capture: true}),\n    addEvent(document, 'touchend', onTouchEnd, {passive: false, capture: true}),\n    addEvent(document, 'focus', onFocus, true)\n  );\n\n  return () => {\n    // Restore styles and scroll the page back to where it was.\n    restoreScrollableStyles?.();\n    restoreStyles?.();\n    removeEvents();\n  };\n}\n\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element: HTMLElement, style: string, value: string) {\n  let cur = element.style[style];\n  element.style[style] = value;\n\n  return () => {\n    element.style[style] = cur;\n  };\n}\n\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent<K extends keyof GlobalEventHandlersEventMap>(\n  target: Document | Window,\n  event: K,\n  handler: (this: Document | Window, ev: GlobalEventHandlersEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n) {\n  // internal function, so it's ok to ignore the difficult to fix type error\n  // @ts-ignore\n  target.addEventListener(event, handler, options);\n  return () => {\n    // @ts-ignore\n    target.removeEventListener(event, handler, options);\n  };\n}\n\nfunction scrollIntoView(target: Element) {\n  let root = document.scrollingElement || document.documentElement;\n  let nextTarget: Element | null = target;\n  while (nextTarget && nextTarget !== root) {\n    // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n    let scrollable = getScrollParent(nextTarget);\n    if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== nextTarget) {\n      let scrollableTop = scrollable.getBoundingClientRect().top;\n      let targetTop = nextTarget.getBoundingClientRect().top;\n      if (targetTop > scrollableTop + nextTarget.clientHeight) {\n        scrollable.scrollTop += targetTop - scrollableTop;\n      }\n    }\n\n    nextTarget = scrollable.parentElement;\n  }\n}\n\nfunction willOpenKeyboard(target: Element) {\n  return (\n    (target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type)) ||\n    target instanceof HTMLTextAreaElement ||\n    (target instanceof HTMLElement && target.isContentEditable)\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GASD,MAAM,uCAAiB,OAAO,aAAa,eAAe,OAAO,cAAc;AAE/E,sEAAsE;AACtE,MAAM,0CAAoB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,mIAAmI;AACnI,IAAI,2CAAqB;AACzB,IAAI;AAOG,SAAS;kBAAiB,iEAAgC,CAAC,CAAC;IACjE,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG;IAEnB,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,YACF;QAGF;QACA,IAAI,6CAAuB,GAAA;YACzB,IAAI,CAAA,mKAAA,SAAI,KACN,gCAAU;iBAEV,gCAAU;;QAId,OAAO;YACL;YACA,IAAI,6CAAuB,GACzB;QAEJ;IACF,GAAG;QAAC;KAAW;AACjB;AAEA,0FAA0F;AAC1F,mFAAmF;AACnF,SAAS;IACP,IAAI,iBAAiB,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW;IAC7E,OAAO,CAAA,iKAAA,QAAI,EACT,iBAAiB,KACf,2FAA2F;IAC1F,CAAA,qBAAqB,SAAS,eAAe,CAAC,KAAK,GAChD,+BAAS,SAAS,eAAe,EAAE,mBAAmB,YACtD,+BAAS,SAAS,eAAe,EAAE,gBAAgB,UAAG,gBAAe,EAAE,CAAC,GAAA,GAC9E,+BAAS,SAAS,eAAe,EAAE,YAAY;AAEnD;AAEA,wEAAwE;AACxE,gDAAgD;AAChD,EAAE;AACF,8FAA8F;AAC9F,sGAAsG;AACtG,mCAAmC;AACnC,6GAA6G;AAC7G,2EAA2E;AAC3E,4GAA4G;AAC5G,sGAAsG;AACtG,EAAE;AACF,oGAAoG;AACpG,EAAE;AACF,+GAA+G;AAC/G,oBAAoB;AACpB,4GAA4G;AAC5G,+GAA+G;AAC/G,mDAAmD;AACnD,uGAAuG;AACvG,qGAAqG;AACrG,4GAA4G;AAC5G,4DAA4D;AAC5D,kHAAkH;AAClH,0GAA0G;AAC1G,oFAAoF;AACpF,gHAAgH;AAChH,oFAAoF;AACpF,SAAS;IACP,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe,CAAC;QAClB,sFAAsF;QACtF,aAAa,CAAA,2KAAA,kBAAc,EAAE,EAAE,MAAM,EAAa;QAClD,IAAI,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,EACzE;QAGF,6EAA6E;QAC7E,4EAA4E;QAC5E,sBAAsB;QACtB,IAAI,sBAAsB,eAAe,OAAO,gBAAgB,CAAC,YAAY,kBAAkB,KAAK,QAClG,0BAA0B,+BAAS,YAAY,sBAAsB;IAEzE;IAEA,IAAI,cAAc,CAAC;QACjB,gCAAgC;QAChC,IAAI,CAAC,cAAc,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,EAAE;YAC1F,EAAE,cAAc;YAChB;QACF;QAEA,6EAA6E;QAC7E,2FAA2F;QAC3F,iFAAiF;QACjF,gFAAgF;QAChF,oFAAoF;QACpF,sDAAsD;QACtD,IAAI,WAAW,YAAY,KAAK,WAAW,YAAY,IAAI,WAAW,WAAW,KAAK,WAAW,WAAW,EAC1G,EAAE,cAAc;IAEpB;IAEA,IAAI,aAAa;QACf,IAAI,yBACF;IAEJ;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,uCAAiB,SAAS;YAC5B;YAEA,sFAAsF;YACtF,4CAA4C;YAC5C,OAAO,KAAK,CAAC,SAAS,GAAG;YACzB,sBAAsB;gBACpB,OAAO,KAAK,CAAC,SAAS,GAAG;gBAEzB,qFAAqF;gBACrF,wFAAwF;gBACxF,IAAI,sCAAA;oBACF,IAAI,qCAAe,MAAM,GAAG,OAAO,WAAW,EAC5C,AACA,2CAA2C,8BAD8B;oBAEzE,sBAAsB;wBACpB,qCAAe;oBACjB;yBAGA,6CAA6C;oBAC7C,qCAAe,gBAAgB,CAAC,UAAU,IAAM,qCAAe,SAAS;wBAAC,MAAM;oBAAI;;YAGzF;QACF;IACF;IAEA,IAAI,gBAAqC;IACzC,IAAI,cAAc;QAChB,IAAI,eACF;QAGF,IAAI,iBAAiB;YACnB,kEAAkE;YAClE,2FAA2F;YAC3F,OAAO,QAAQ,CAAC,GAAG;QACrB;QAEA,4DAA4D;QAC5D,0FAA0F;QAC1F,6FAA6F;QAC7F,IAAI,UAAU,OAAO,WAAW;QAChC,IAAI,UAAU,OAAO,WAAW;QAEhC,gBAAgB,CAAA,iKAAA,QAAI,EAClB,+BAAS,QAAQ,UAAU,iBAC3B,+BAAS,SAAS,eAAe,EAAE,gBAAgB,UAAG,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW,EAAC,EAAE,CAAC,KAClH,+BAAS,SAAS,eAAe,EAAE,YAAY,WAC/C,+BAAS,SAAS,IAAI,EAAE,aAAa,AAAC,CAAC,UAAE,SAAQ,EAAE,CAAC,KACpD;YACE,OAAO,QAAQ,CAAC,SAAS;QAC3B;QAGF,qFAAqF;QACrF,OAAO,QAAQ,CAAC,GAAG;IACrB;IAEA,IAAI,eAAe,CAAA,iKAAA,QAAI,EACrB,+BAAS,UAAU,cAAc,cAAc;QAAC,SAAS;QAAO,SAAS;IAAI,IAC7E,+BAAS,UAAU,aAAa,aAAa;QAAC,SAAS;QAAO,SAAS;IAAI,IAC3E,+BAAS,UAAU,YAAY,YAAY;QAAC,SAAS;QAAO,SAAS;IAAI,IACzE,+BAAS,UAAU,SAAS,SAAS;IAGvC,OAAO;QACL,2DAA2D;QAC3D,4BAAA,QAAA,4BAAA,KAAA,IAAA,KAAA,IAAA;QACA,kBAAA,QAAA,kBAAA,KAAA,IAAA,KAAA,IAAA;QACA;IACF;AACF;AAEA,gGAAgG;AAChG,SAAS,+BAAS,OAAoB,EAAE,KAAa,EAAE,KAAa;IAClE,IAAI,MAAM,QAAQ,KAAK,CAAC,MAAM;IAC9B,QAAQ,KAAK,CAAC,MAAM,GAAG;IAEvB,OAAO;QACL,QAAQ,KAAK,CAAC,MAAM,GAAG;IACzB;AACF;AAEA,6EAA6E;AAC7E,SAAS,+BACP,MAAyB,EACzB,KAAQ,EACR,OAA6E,EAC7E,OAA2C;IAE3C,0EAA0E;IAC1E,aAAa;IACb,OAAO,gBAAgB,CAAC,OAAO,SAAS;IACxC,OAAO;QACL,aAAa;QACb,OAAO,mBAAmB,CAAC,OAAO,SAAS;IAC7C;AACF;AAEA,SAAS,qCAAe,MAAe;IACrC,IAAI,OAAO,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAChE,IAAI,aAA6B;IACjC,MAAO,cAAc,eAAe,KAAM;QACxC,0GAA0G;QAC1G,IAAI,aAAa,CAAA,2KAAA,kBAAc,EAAE;QACjC,IAAI,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,IAAI,eAAe,YAAY;YACxG,IAAI,gBAAgB,WAAW,qBAAqB,GAAG,GAAG;YAC1D,IAAI,YAAY,WAAW,qBAAqB,GAAG,GAAG;YACtD,IAAI,YAAY,gBAAgB,WAAW,YAAY,EACrD,WAAW,SAAS,IAAI,YAAY;QAExC;QAEA,aAAa,WAAW,aAAa;IACvC;AACF;AAEA,SAAS,uCAAiB,MAAe;IACvC,OACG,kBAAkB,oBAAoB,CAAC,wCAAkB,GAAG,CAAC,OAAO,IAAI,KACzE,kBAAkB,uBACjB,kBAAkB,eAAe,OAAO,iBAAiB;AAE9D", "debugId": null}}, {"offset": {"line": 2748, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/useCloseOnScroll.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/useCloseOnScroll.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\n\n// This behavior moved from useOverlayTrigger to useOverlayPosition.\n// For backward compatibility, where useOverlayTrigger handled hiding the popover on close,\n// it sets a close function here mapped from the trigger element. This way we can avoid\n// forcing users to pass an onClose function to useOverlayPosition which could be considered\n// a breaking change.\nexport const onCloseMap: WeakMap<Element, () => void> = new WeakMap();\n\ninterface CloseOnScrollOptions {\n  triggerRef: RefObject<Element | null>,\n  isOpen?: boolean,\n  onClose?: (() => void) | null\n}\n\n/** @private */\nexport function useCloseOnScroll(opts: CloseOnScrollOptions): void {\n  let {triggerRef, isOpen, onClose} = opts;\n\n  useEffect(() => {\n    if (!isOpen || onClose === null) {\n      return;\n    }\n\n    let onScroll = (e: Event) => {\n      // Ignore if scrolling an scrollable region outside the trigger's tree.\n      let target = e.target;\n      // window is not a Node and doesn't have contain, but window contains everything\n      if (!triggerRef.current || ((target instanceof Node) && !target.contains(triggerRef.current))) {\n        return;\n      }\n\n      // Ignore scroll events on any input or textarea as the cursor position can cause it to scroll\n      // such as in a combobox. Clicking the dropdown button places focus on the input, and if the\n      // text inside the input extends beyond the 'end', then it will scroll so the cursor is visible at the end.\n      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n        return;\n      }\n\n      let onCloseHandler = onClose || onCloseMap.get(triggerRef.current);\n      if (onCloseHandler) {\n        onCloseHandler();\n      }\n    };\n\n    window.addEventListener('scroll', onScroll, true);\n    return () => {\n      window.removeEventListener('scroll', onScroll, true);\n    };\n  }, [isOpen, onClose, triggerRef]);\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAUM,MAAM,4CAA2C,IAAI;AASrD,SAAS,0CAAiB,IAA0B;IACzD,IAAI,EAAA,YAAC,UAAU,EAAA,QAAE,MAAM,EAAA,SAAE,OAAO,EAAC,GAAG;IAEpC,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,CAAC,UAAU,YAAY,MACzB;QAGF,IAAI,WAAW,CAAC;YACd,uEAAuE;YACvE,IAAI,SAAS,EAAE,MAAM;YACrB,gFAAgF;YAChF,IAAI,CAAC,WAAW,OAAO,IAAM,kBAAkB,QAAS,CAAC,OAAO,QAAQ,CAAC,WAAW,OAAO,GACzF;YAGF,8FAA8F;YAC9F,4FAA4F;YAC5F,2GAA2G;YAC3G,IAAI,EAAE,MAAM,YAAY,oBAAoB,EAAE,MAAM,YAAY,qBAC9D;YAGF,IAAI,iBAAiB,WAAW,0CAAW,GAAG,CAAC,WAAW,OAAO;YACjE,IAAI,gBACF;QAEJ;QAEA,OAAO,gBAAgB,CAAC,UAAU,UAAU;QAC5C,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU,UAAU;QACjD;IACF,GAAG;QAAC;QAAQ;QAAS;KAAW;AAClC", "debugId": null}}, {"offset": {"line": 2797, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/useOverlayTrigger.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/useOverlayTrigger.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {DOMProps, RefObject} from '@react-types/shared';\nimport {onCloseMap} from './useCloseOnScroll';\nimport {OverlayTriggerState} from '@react-stately/overlays';\nimport {useEffect} from 'react';\nimport {useId} from '@react-aria/utils';\n\nexport interface OverlayTriggerProps {\n  /** Type of overlay that is opened by the trigger. */\n  type: 'dialog' | 'menu' | 'listbox' | 'tree' | 'grid'\n}\n\nexport interface OverlayTriggerAria {\n  /** Props for the trigger element. */\n  triggerProps: AriaButtonProps,\n\n  /** Props for the overlay container element. */\n  overlayProps: DOMProps\n}\n\n/**\n * Handles the behavior and accessibility for an overlay trigger, e.g. a button\n * that opens a popover, menu, or other overlay that is positioned relative to the trigger.\n */\nexport function useOverlayTrigger(props: OverlayTriggerProps, state: OverlayTriggerState, ref?: RefObject<Element | null>): OverlayTriggerAria {\n  let {type} = props;\n  let {isOpen} = state;\n\n  // Backward compatibility. Share state close function with useOverlayPosition so it can close on scroll\n  // without forcing users to pass onClose.\n  useEffect(() => {\n    if (ref && ref.current) {\n      onCloseMap.set(ref.current, state.close);\n    }\n  });\n\n  // Aria 1.1 supports multiple values for aria-haspopup other than just menus.\n  // https://www.w3.org/TR/wai-aria-1.1/#aria-haspopup\n  // However, we only add it for menus for now because screen readers often\n  // announce it as a menu even for other values.\n  let ariaHasPopup: undefined | boolean | 'listbox' = undefined;\n  if (type === 'menu') {\n    ariaHasPopup = true;\n  } else if (type === 'listbox') {\n    ariaHasPopup = 'listbox';\n  }\n\n  let overlayId = useId();\n  return {\n    triggerProps: {\n      'aria-haspopup': ariaHasPopup,\n      'aria-expanded': isOpen,\n      'aria-controls': isOpen ? overlayId : undefined,\n      onPress: state.toggle\n    },\n    overlayProps: {\n      id: overlayId\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GA0BM,SAAS,0CAAkB,KAA0B,EAAE,KAA0B,EAAE,GAA+B;IACvH,IAAI,EAAA,MAAC,IAAI,EAAC,GAAG;IACb,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG;IAEf,uGAAuG;IACvG,yCAAyC;IACzC,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,OAAO,IAAI,OAAO,EACpB,CAAA,+KAAA,aAAS,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,MAAM,KAAK;IAE3C;IAEA,6EAA6E;IAC7E,oDAAoD;IACpD,yEAAyE;IACzE,+CAA+C;IAC/C,IAAI,eAAgD;IACpD,IAAI,SAAS,QACX,eAAe;SACV,IAAI,SAAS,WAClB,eAAe;IAGjB,IAAI,YAAY,CAAA,iKAAA,QAAI;IACpB,OAAO;QACL,cAAc;YACZ,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB,SAAS,YAAY;YACtC,SAAS,MAAM,MAAM;QACvB;QACA,cAAc;YACZ,IAAI;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 2850, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/calculatePosition.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/calculatePosition.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Axis, Placement, PlacementAxis, SizeAxis} from '@react-types/overlays';\nimport {clamp, isWebKit} from '@react-aria/utils';\n\ninterface Position {\n  top?: number,\n  left?: number,\n  bottom?: number,\n  right?: number\n}\n\ninterface Dimensions {\n  width: number,\n  height: number,\n  totalWidth: number,\n  totalHeight: number,\n  top: number,\n  left: number,\n  scroll: Position\n}\n\ninterface ParsedPlacement {\n  placement: PlacementAxis,\n  crossPlacement: PlacementAxis,\n  axis: Axis,\n  crossAxis: Axis,\n  size: SizeAxis,\n  crossSize: SizeAxis\n}\n\ninterface Offset {\n  top: number,\n  left: number,\n  width: number,\n  height: number\n}\n\ninterface PositionOpts {\n  arrowSize: number,\n  placement: Placement,\n  targetNode: Element,\n  overlayNode: Element,\n  scrollNode: Element,\n  padding: number,\n  shouldFlip: boolean,\n  boundaryElement: Element,\n  offset: number,\n  crossOffset: number,\n  maxHeight?: number,\n  arrowBoundaryOffset?: number\n}\n\ntype HeightGrowthDirection = 'top' | 'bottom';\n\nexport interface PositionResult {\n  position: Position,\n  arrowOffsetLeft?: number,\n  arrowOffsetTop?: number,\n  maxHeight: number,\n  placement: PlacementAxis\n}\n\nconst AXIS = {\n  top: 'top',\n  bottom: 'top',\n  left: 'left',\n  right: 'left'\n};\n\nconst FLIPPED_DIRECTION = {\n  top: 'bottom',\n  bottom: 'top',\n  left: 'right',\n  right: 'left'\n};\n\nconst CROSS_AXIS = {\n  top: 'left',\n  left: 'top'\n};\n\nconst AXIS_SIZE = {\n  top: 'height',\n  left: 'width'\n};\n\nconst TOTAL_SIZE = {\n  width: 'totalWidth',\n  height: 'totalHeight'\n};\n\nconst PARSED_PLACEMENT_CACHE = {};\n\nlet visualViewport = typeof document !== 'undefined' ? window.visualViewport : null;\n\nfunction getContainerDimensions(containerNode: Element): Dimensions {\n  let width = 0, height = 0, totalWidth = 0, totalHeight = 0, top = 0, left = 0;\n  let scroll: Position = {};\n  let isPinchZoomedIn = (visualViewport?.scale ?? 1) > 1;\n\n  if (containerNode.tagName === 'BODY') {\n    let documentElement = document.documentElement;\n    totalWidth = documentElement.clientWidth;\n    totalHeight = documentElement.clientHeight;\n    width = visualViewport?.width ?? totalWidth;\n    height = visualViewport?.height ?? totalHeight;\n    scroll.top = documentElement.scrollTop || containerNode.scrollTop;\n    scroll.left = documentElement.scrollLeft || containerNode.scrollLeft;\n\n    // The goal of the below is to get a top/left value that represents the top/left of the visual viewport with\n    // respect to the layout viewport origin. This combined with the scrollTop/scrollLeft will allow us to calculate\n    // coordinates/values with respect to the visual viewport or with respect to the layout viewport.\n    if (visualViewport) {\n      top = visualViewport.offsetTop;\n      left = visualViewport.offsetLeft;\n    }\n  } else {\n    ({width, height, top, left} = getOffset(containerNode));\n    scroll.top = containerNode.scrollTop;\n    scroll.left = containerNode.scrollLeft;\n    totalWidth = width;\n    totalHeight = height;\n  }\n\n  if (isWebKit() && (containerNode.tagName === 'BODY' || containerNode.tagName === 'HTML') && isPinchZoomedIn) {\n    // Safari will report a non-zero scrollTop/Left for the non-scrolling body/HTML element when pinch zoomed in unlike other browsers.\n    // Set to zero for parity calculations so we get consistent positioning of overlays across all browsers.\n    // Also switch to visualViewport.pageTop/pageLeft so that we still accomodate for scroll positioning for body/HTML elements that are actually scrollable\n    // before pinch zoom happens\n    scroll.top = 0;\n    scroll.left = 0;\n    top = visualViewport?.pageTop ?? 0;\n    left = visualViewport?.pageLeft ?? 0;\n  }\n\n  return {width, height, totalWidth, totalHeight, scroll, top, left};\n}\n\nfunction getScroll(node: Element): Offset {\n  return {\n    top: node.scrollTop,\n    left: node.scrollLeft,\n    width: node.scrollWidth,\n    height: node.scrollHeight\n  };\n}\n\n// Determines the amount of space required when moving the overlay to ensure it remains in the boundary\nfunction getDelta(\n  axis: Axis,\n  offset: number,\n  size: number,\n  // The dimensions of the boundary element that the popover is\n  // positioned within (most of the time this is the <body>).\n  boundaryDimensions: Dimensions,\n  // The dimensions of the containing block element that the popover is\n  // positioned relative to (e.g. parent with position: relative).\n  // Usually this is the same as the boundary element, but if the popover\n  // is portaled somewhere other than the body and has an ancestor with\n  // position: relative/absolute, it will be different.\n  containerDimensions: Dimensions,\n  padding: number,\n  containerOffsetWithBoundary: Offset\n) {\n  let containerScroll = containerDimensions.scroll[axis] ?? 0;\n  // The height/width of the boundary. Matches the axis along which we are adjusting the overlay position\n  let boundarySize = boundaryDimensions[AXIS_SIZE[axis]];\n  // Calculate the edges of the boundary (accomodating for the boundary padding) and the edges of the overlay.\n  // Note that these values are with respect to the visual viewport (aka 0,0 is the top left of the viewport)\n  let boundaryStartEdge = boundaryDimensions.scroll[AXIS[axis]] + padding;\n  let boundaryEndEdge = boundarySize + boundaryDimensions.scroll[AXIS[axis]] - padding;\n  let startEdgeOffset = offset - containerScroll + containerOffsetWithBoundary[axis] - boundaryDimensions[AXIS[axis]];\n  let endEdgeOffset = offset - containerScroll + size + containerOffsetWithBoundary[axis] - boundaryDimensions[AXIS[axis]];\n\n  // If any of the overlay edges falls outside of the boundary, shift the overlay the required amount to align one of the overlay's\n  // edges with the closest boundary edge.\n  if (startEdgeOffset < boundaryStartEdge) {\n    return boundaryStartEdge - startEdgeOffset;\n  } else if (endEdgeOffset > boundaryEndEdge) {\n    return Math.max(boundaryEndEdge - endEdgeOffset, boundaryStartEdge - startEdgeOffset);\n  } else {\n    return 0;\n  }\n}\n\nfunction getMargins(node: Element): Position {\n  let style = window.getComputedStyle(node);\n  return {\n    top: parseInt(style.marginTop, 10) || 0,\n    bottom: parseInt(style.marginBottom, 10) || 0,\n    left: parseInt(style.marginLeft, 10) || 0,\n    right: parseInt(style.marginRight, 10) || 0\n  };\n}\n\nfunction parsePlacement(input: Placement): ParsedPlacement {\n  if (PARSED_PLACEMENT_CACHE[input]) {\n    return PARSED_PLACEMENT_CACHE[input];\n  }\n\n  let [placement, crossPlacement] = input.split(' ');\n  let axis: Axis = AXIS[placement] || 'right';\n  let crossAxis: Axis = CROSS_AXIS[axis];\n\n  if (!AXIS[crossPlacement]) {\n    crossPlacement = 'center';\n  }\n\n  let size = AXIS_SIZE[axis];\n  let crossSize = AXIS_SIZE[crossAxis];\n  PARSED_PLACEMENT_CACHE[input] = {placement, crossPlacement, axis, crossAxis, size, crossSize};\n  return PARSED_PLACEMENT_CACHE[input];\n}\n\nfunction computePosition(\n  childOffset: Offset,\n  boundaryDimensions: Dimensions,\n  overlaySize: Offset,\n  placementInfo: ParsedPlacement,\n  offset: number,\n  crossOffset: number,\n  containerOffsetWithBoundary: Offset,\n  isContainerPositioned: boolean,\n  arrowSize: number,\n  arrowBoundaryOffset: number\n) {\n  let {placement, crossPlacement, axis, crossAxis, size, crossSize} = placementInfo;\n  let position: Position = {};\n\n  // button position\n  position[crossAxis] = childOffset[crossAxis] ?? 0;\n  if (crossPlacement === 'center') {\n    //  + (button size / 2) - (overlay size / 2)\n    // at this point the overlay center should match the button center\n    position[crossAxis]! += ((childOffset[crossSize] ?? 0) - (overlaySize[crossSize] ?? 0)) / 2;\n  } else if (crossPlacement !== crossAxis) {\n    //  + (button size) - (overlay size)\n    // at this point the overlay bottom should match the button bottom\n    position[crossAxis]! += (childOffset[crossSize] ?? 0) - (overlaySize[crossSize] ?? 0);\n  }/* else {\n    the overlay top should match the button top\n  } */\n\n  position[crossAxis]! += crossOffset;\n\n  // overlay top overlapping arrow with button bottom\n  const minPosition = childOffset[crossAxis] - overlaySize[crossSize] + arrowSize + arrowBoundaryOffset;\n  // overlay bottom overlapping arrow with button top\n  const maxPosition = childOffset[crossAxis] + childOffset[crossSize] - arrowSize - arrowBoundaryOffset;\n  position[crossAxis] = clamp(position[crossAxis]!, minPosition, maxPosition);\n\n  // Floor these so the position isn't placed on a partial pixel, only whole pixels. Shouldn't matter if it was floored or ceiled, so chose one.\n  if (placement === axis) {\n    // If the container is positioned (non-static), then we use the container's actual\n    // height, as `bottom` will be relative to this height.  But if the container is static,\n    // then it can only be the `document.body`, and `bottom` will be relative to _its_\n    // container, which should be as large as boundaryDimensions.\n    const containerHeight = (isContainerPositioned ? containerOffsetWithBoundary[size] : boundaryDimensions[TOTAL_SIZE[size]]);\n    position[FLIPPED_DIRECTION[axis]] = Math.floor(containerHeight - childOffset[axis] + offset);\n  } else {\n    position[axis] = Math.floor(childOffset[axis] + childOffset[size] + offset);\n  }\n  return position;\n}\n\nfunction getMaxHeight(\n  position: Position,\n  boundaryDimensions: Dimensions,\n  containerOffsetWithBoundary: Offset,\n  isContainerPositioned: boolean,\n  margins: Position,\n  padding: number,\n  overlayHeight: number,\n  heightGrowthDirection: HeightGrowthDirection\n) {\n  const containerHeight = (isContainerPositioned ? containerOffsetWithBoundary.height : boundaryDimensions[TOTAL_SIZE.height]);\n  // For cases where position is set via \"bottom\" instead of \"top\", we need to calculate the true overlay top with respect to the boundary. Reverse calculate this with the same method\n  // used in computePosition.\n  let overlayTop = position.top != null ? containerOffsetWithBoundary.top + position.top : containerOffsetWithBoundary.top + (containerHeight - (position.bottom ?? 0) - overlayHeight);\n  let maxHeight = heightGrowthDirection !== 'top' ?\n    // We want the distance between the top of the overlay to the bottom of the boundary\n    Math.max(0,\n      (boundaryDimensions.height + boundaryDimensions.top + (boundaryDimensions.scroll.top ?? 0)) // this is the bottom of the boundary\n      - overlayTop // this is the top of the overlay\n      - ((margins.top ?? 0) + (margins.bottom ?? 0) + padding) // save additional space for margin and padding\n    )\n    // We want the distance between the bottom of the overlay to the top of the boundary\n    : Math.max(0,\n      (overlayTop + overlayHeight) // this is the bottom of the overlay\n      - (boundaryDimensions.top + (boundaryDimensions.scroll.top ?? 0)) // this is the top of the boundary\n      - ((margins.top ?? 0) + (margins.bottom ?? 0) + padding) // save additional space for margin and padding\n    );\n  return Math.min(boundaryDimensions.height - (padding * 2), maxHeight);\n}\n\nfunction getAvailableSpace(\n  boundaryDimensions: Dimensions,\n  containerOffsetWithBoundary: Offset,\n  childOffset: Offset,\n  margins: Position,\n  padding: number,\n  placementInfo: ParsedPlacement\n) {\n  let {placement, axis, size} = placementInfo;\n  if (placement === axis) {\n    return Math.max(0, childOffset[axis] - boundaryDimensions[axis] - (boundaryDimensions.scroll[axis] ?? 0) + containerOffsetWithBoundary[axis] - (margins[axis] ?? 0) - margins[FLIPPED_DIRECTION[axis]] - padding);\n  }\n\n  return Math.max(0, boundaryDimensions[size] + boundaryDimensions[axis] + boundaryDimensions.scroll[axis] - containerOffsetWithBoundary[axis] - childOffset[axis] - childOffset[size] - (margins[axis] ?? 0) - margins[FLIPPED_DIRECTION[axis]] - padding);\n}\n\nexport function calculatePositionInternal(\n  placementInput: Placement,\n  childOffset: Offset,\n  overlaySize: Offset,\n  scrollSize: Offset,\n  margins: Position,\n  padding: number,\n  flip: boolean,\n  boundaryDimensions: Dimensions,\n  containerDimensions: Dimensions,\n  containerOffsetWithBoundary: Offset,\n  offset: number,\n  crossOffset: number,\n  isContainerPositioned: boolean,\n  userSetMaxHeight: number | undefined,\n  arrowSize: number,\n  arrowBoundaryOffset: number\n): PositionResult {\n  let placementInfo = parsePlacement(placementInput);\n  let {size, crossAxis, crossSize, placement, crossPlacement} = placementInfo;\n  let position = computePosition(childOffset, boundaryDimensions, overlaySize, placementInfo, offset, crossOffset, containerOffsetWithBoundary, isContainerPositioned, arrowSize, arrowBoundaryOffset);\n  let normalizedOffset = offset;\n  let space = getAvailableSpace(\n    boundaryDimensions,\n    containerOffsetWithBoundary,\n    childOffset,\n    margins,\n    padding + offset,\n    placementInfo\n  );\n\n  // Check if the scroll size of the overlay is greater than the available space to determine if we need to flip\n  if (flip && scrollSize[size] > space) {\n    let flippedPlacementInfo = parsePlacement(`${FLIPPED_DIRECTION[placement]} ${crossPlacement}` as Placement);\n    let flippedPosition = computePosition(childOffset, boundaryDimensions, overlaySize, flippedPlacementInfo, offset, crossOffset, containerOffsetWithBoundary, isContainerPositioned, arrowSize, arrowBoundaryOffset);\n    let flippedSpace = getAvailableSpace(\n      boundaryDimensions,\n      containerOffsetWithBoundary,\n      childOffset,\n      margins,\n      padding + offset,\n      flippedPlacementInfo\n    );\n\n    // If the available space for the flipped position is greater than the original available space, flip.\n    if (flippedSpace > space) {\n      placementInfo = flippedPlacementInfo;\n      position = flippedPosition;\n      normalizedOffset = offset;\n    }\n  }\n\n  // Determine the direction the height of the overlay can grow so that we can choose how to calculate the max height\n  let heightGrowthDirection: HeightGrowthDirection = 'bottom';\n  if (placementInfo.axis === 'top') {\n    if (placementInfo.placement === 'top') {\n      heightGrowthDirection = 'top';\n    } else if (placementInfo.placement === 'bottom') {\n      heightGrowthDirection = 'bottom';\n    }\n  } else if (placementInfo.crossAxis === 'top') {\n    if (placementInfo.crossPlacement === 'top') {\n      heightGrowthDirection = 'bottom';\n    } else if (placementInfo.crossPlacement === 'bottom') {\n      heightGrowthDirection = 'top';\n    }\n  }\n\n  let delta = getDelta(crossAxis, position[crossAxis]!, overlaySize[crossSize], boundaryDimensions, containerDimensions, padding, containerOffsetWithBoundary);\n  position[crossAxis]! += delta;\n\n  let maxHeight = getMaxHeight(\n    position,\n    boundaryDimensions,\n    containerOffsetWithBoundary,\n    isContainerPositioned,\n    margins,\n    padding,\n    overlaySize.height,\n    heightGrowthDirection\n  );\n\n  if (userSetMaxHeight && userSetMaxHeight < maxHeight) {\n    maxHeight = userSetMaxHeight;\n  }\n\n  overlaySize.height = Math.min(overlaySize.height, maxHeight);\n\n  position = computePosition(childOffset, boundaryDimensions, overlaySize, placementInfo, normalizedOffset, crossOffset, containerOffsetWithBoundary, isContainerPositioned, arrowSize, arrowBoundaryOffset);\n  delta = getDelta(crossAxis, position[crossAxis]!, overlaySize[crossSize], boundaryDimensions, containerDimensions, padding, containerOffsetWithBoundary);\n  position[crossAxis]! += delta;\n\n  let arrowPosition: Position = {};\n\n  // All values are transformed so that 0 is at the top/left of the overlay depending on the orientation\n  // Prefer the arrow being in the center of the trigger/overlay anchor element\n  // childOffset[crossAxis] + .5 * childOffset[crossSize] = absolute position with respect to the trigger's coordinate system that would place the arrow in the center of the trigger\n  // position[crossAxis] - margins[AXIS[crossAxis]] = value use to transform the position to a value with respect to the overlay's coordinate system. A child element's (aka arrow) position absolute's \"0\"\n  // is positioned after the margin of its parent (aka overlay) so we need to subtract it to get the proper coordinate transform\n  let preferredArrowPosition = childOffset[crossAxis] + .5 * childOffset[crossSize] - position[crossAxis]! - margins[AXIS[crossAxis]];\n\n  // Min/Max position limits for the arrow with respect to the overlay\n  const arrowMinPosition = arrowSize / 2 + arrowBoundaryOffset;\n  // overlaySize[crossSize] - margins = true size of the overlay\n  const overlayMargin = AXIS[crossAxis] === 'left' ? (margins.left ?? 0) + (margins.right ?? 0) : (margins.top ?? 0) + (margins.bottom ?? 0);\n  const arrowMaxPosition = overlaySize[crossSize] - overlayMargin - (arrowSize / 2) - arrowBoundaryOffset;\n\n  // Min/Max position limits for the arrow with respect to the trigger/overlay anchor element\n  // Same margin accomodation done here as well as for the preferredArrowPosition\n  const arrowOverlappingChildMinEdge = childOffset[crossAxis] + (arrowSize / 2) - (position[crossAxis] + margins[AXIS[crossAxis]]);\n  const arrowOverlappingChildMaxEdge = childOffset[crossAxis] + childOffset[crossSize] - (arrowSize / 2) - (position[crossAxis] + margins[AXIS[crossAxis]]);\n\n  // Clamp the arrow positioning so that it always is within the bounds of the anchor and the overlay\n  const arrowPositionOverlappingChild = clamp(preferredArrowPosition, arrowOverlappingChildMinEdge, arrowOverlappingChildMaxEdge);\n  arrowPosition[crossAxis] = clamp(arrowPositionOverlappingChild, arrowMinPosition, arrowMaxPosition);\n\n  return {\n    position,\n    maxHeight: maxHeight,\n    arrowOffsetLeft: arrowPosition.left,\n    arrowOffsetTop: arrowPosition.top,\n    placement: placementInfo.placement\n  };\n}\n\n/**\n * Determines where to place the overlay with regards to the target and the position of an optional indicator.\n */\nexport function calculatePosition(opts: PositionOpts): PositionResult {\n  let {\n    placement,\n    targetNode,\n    overlayNode,\n    scrollNode,\n    padding,\n    shouldFlip,\n    boundaryElement,\n    offset,\n    crossOffset,\n    maxHeight,\n    arrowSize = 0,\n    arrowBoundaryOffset = 0\n  } = opts;\n\n  let container = overlayNode instanceof HTMLElement ? getContainingBlock(overlayNode) : document.documentElement;\n  let isViewportContainer = container === document.documentElement;\n  const containerPositionStyle = window.getComputedStyle(container).position;\n  let isContainerPositioned = !!containerPositionStyle && containerPositionStyle !== 'static';\n  let childOffset: Offset = isViewportContainer ? getOffset(targetNode) : getPosition(targetNode, container);\n\n  if (!isViewportContainer) {\n    let {marginTop, marginLeft} = window.getComputedStyle(targetNode);\n    childOffset.top += parseInt(marginTop, 10) || 0;\n    childOffset.left += parseInt(marginLeft, 10) || 0;\n  }\n\n  let overlaySize: Offset = getOffset(overlayNode);\n  let margins = getMargins(overlayNode);\n  overlaySize.width += (margins.left ?? 0) + (margins.right ?? 0);\n  overlaySize.height += (margins.top ?? 0) + (margins.bottom ?? 0);\n\n  let scrollSize = getScroll(scrollNode);\n  let boundaryDimensions = getContainerDimensions(boundaryElement);\n  let containerDimensions = getContainerDimensions(container);\n  // If the container is the HTML element wrapping the body element, the retrieved scrollTop/scrollLeft will be equal to the\n  // body element's scroll. Set the container's scroll values to 0 since the overlay's edge position value in getDelta don't then need to be further offset\n  // by the container scroll since they are essentially the same containing element and thus in the same coordinate system\n  let containerOffsetWithBoundary: Offset = boundaryElement.tagName === 'BODY' ? getOffset(container) : getPosition(container, boundaryElement);\n  if (container.tagName === 'HTML' && boundaryElement.tagName === 'BODY') {\n    containerDimensions.scroll.top = 0;\n    containerDimensions.scroll.left = 0;\n  }\n\n  return calculatePositionInternal(\n    placement,\n    childOffset,\n    overlaySize,\n    scrollSize,\n    margins,\n    padding,\n    shouldFlip,\n    boundaryDimensions,\n    containerDimensions,\n    containerOffsetWithBoundary,\n    offset,\n    crossOffset,\n    isContainerPositioned,\n    maxHeight,\n    arrowSize,\n    arrowBoundaryOffset\n  );\n}\n\nfunction getOffset(node: Element): Offset {\n  let {top, left, width, height} = node.getBoundingClientRect();\n  let {scrollTop, scrollLeft, clientTop, clientLeft} = document.documentElement;\n  return {\n    top: top + scrollTop - clientTop,\n    left: left + scrollLeft - clientLeft,\n    width,\n    height\n  };\n}\n\nfunction getPosition(node: Element, parent: Element): Offset {\n  let style = window.getComputedStyle(node);\n  let offset: Offset;\n  if (style.position === 'fixed') {\n    let {top, left, width, height} = node.getBoundingClientRect();\n    offset = {top, left, width, height};\n  } else {\n    offset = getOffset(node);\n    let parentOffset = getOffset(parent);\n    let parentStyle = window.getComputedStyle(parent);\n    parentOffset.top += (parseInt(parentStyle.borderTopWidth, 10) || 0) - parent.scrollTop;\n    parentOffset.left += (parseInt(parentStyle.borderLeftWidth, 10) || 0) - parent.scrollLeft;\n    offset.top -= parentOffset.top;\n    offset.left -= parentOffset.left;\n  }\n\n  offset.top -= parseInt(style.marginTop, 10) || 0;\n  offset.left -= parseInt(style.marginLeft, 10) || 0;\n  return offset;\n}\n\n// Returns the containing block of an element, which is the element that\n// this element will be positioned relative to.\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block\nfunction getContainingBlock(node: HTMLElement): Element {\n  // The offsetParent of an element in most cases equals the containing block.\n  // https://w3c.github.io/csswg-drafts/cssom-view/#dom-htmlelement-offsetparent\n  let offsetParent = node.offsetParent;\n\n  // The offsetParent algorithm terminates at the document body,\n  // even if the body is not a containing block. Double check that\n  // and use the documentElement if so.\n  if (\n    offsetParent &&\n    offsetParent === document.body &&\n    window.getComputedStyle(offsetParent).position === 'static' &&\n    !isContainingBlock(offsetParent)\n  ) {\n    offsetParent = document.documentElement;\n  }\n\n  // TODO(later): handle table elements?\n\n  // The offsetParent can be null if the element has position: fixed, or a few other cases.\n  // We have to walk up the tree manually in this case because fixed positioned elements\n  // are still positioned relative to their containing block, which is not always the viewport.\n  if (offsetParent == null) {\n    offsetParent = node.parentElement;\n    while (offsetParent && !isContainingBlock(offsetParent)) {\n      offsetParent = offsetParent.parentElement;\n    }\n  }\n\n  // Fall back to the viewport.\n  return offsetParent || document.documentElement;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\nfunction isContainingBlock(node: Element): boolean {\n  let style = window.getComputedStyle(node);\n  return (\n    style.transform !== 'none' ||\n    /transform|perspective/.test(style.willChange) ||\n    style.filter !== 'none' ||\n    style.contain === 'paint' ||\n    ('backdropFilter' in style && style.backdropFilter !== 'none') ||\n    ('WebkitBackdropFilter' in style && style.WebkitBackdropFilter !== 'none')\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GA+DD,MAAM,6BAAO;IACX,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;AACT;AAEA,MAAM,0CAAoB;IACxB,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;AACT;AAEA,MAAM,mCAAa;IACjB,KAAK;IACL,MAAM;AACR;AAEA,MAAM,kCAAY;IAChB,KAAK;IACL,MAAM;AACR;AAEA,MAAM,mCAAa;IACjB,OAAO;IACP,QAAQ;AACV;AAEA,MAAM,+CAAyB,CAAC;AAEhC,IAAI,uCAAiB,OAAO,aAAa,cAAc,OAAO,cAAc,GAAG;AAE/E,SAAS,6CAAuB,aAAsB;IACpD,IAAI,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,cAAc,GAAG,MAAM,GAAG,OAAO;IAC5E,IAAI,SAAmB,CAAC;QACD;IAAvB,IAAI,kBAAmB,CAAA,CAAA,wBAAA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,KAAK,MAAA,QAArB,0BAAA,KAAA,IAAA,wBAAyB,CAAA,IAAK;IAErD,IAAI,cAAc,OAAO,KAAK,QAAQ;QACpC,IAAI,kBAAkB,SAAS,eAAe;QAC9C,aAAa,gBAAgB,WAAW;QACxC,cAAc,gBAAgB,YAAY;YAClC;QAAR,QAAQ,CAAA,wBAAA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,KAAK,MAAA,QAArB,0BAAA,KAAA,IAAA,wBAAyB;YACxB;QAAT,SAAS,CAAA,yBAAA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,MAAM,MAAA,QAAtB,2BAAA,KAAA,IAAA,yBAA0B;QACnC,OAAO,GAAG,GAAG,gBAAgB,SAAS,IAAI,cAAc,SAAS;QACjE,OAAO,IAAI,GAAG,gBAAgB,UAAU,IAAI,cAAc,UAAU;QAEpE,4GAA4G;QAC5G,gHAAgH;QAChH,iGAAiG;QACjG,IAAI,sCAAgB;YAClB,MAAM,qCAAe,SAAS;YAC9B,OAAO,qCAAe,UAAU;QAClC;IACF,OAAO;QACJ,CAAA,EAAA,OAAC,KAAK,EAAA,QAAE,MAAM,EAAA,KAAE,GAAG,EAAA,MAAE,IAAI,EAAC,GAAG,gCAAU,cAAa;QACrD,OAAO,GAAG,GAAG,cAAc,SAAS;QACpC,OAAO,IAAI,GAAG,cAAc,UAAU;QACtC,aAAa;QACb,cAAc;IAChB;IAEA,IAAI,CAAA,mKAAA,YAAO,OAAQ,CAAA,cAAc,OAAO,KAAK,UAAU,cAAc,OAAO,KAAK,MAAK,KAAM,iBAAiB;QAC3G,mIAAmI;QACnI,wGAAwG;QACxG,wJAAwJ;QACxJ,4BAA4B;QAC5B,OAAO,GAAG,GAAG;QACb,OAAO,IAAI,GAAG;YACR;QAAN,MAAM,CAAA,0BAAA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,OAAO,MAAA,QAAvB,4BAAA,KAAA,IAAA,0BAA2B;YAC1B;QAAP,OAAO,CAAA,2BAAA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,QAAQ,MAAA,QAAxB,6BAAA,KAAA,IAAA,2BAA4B;IACrC;IAEA,OAAO;eAAC;gBAAO;oBAAQ;qBAAY;gBAAa;aAAQ;cAAK;IAAI;AACnE;AAEA,SAAS,gCAAU,IAAa;IAC9B,OAAO;QACL,KAAK,KAAK,SAAS;QACnB,MAAM,KAAK,UAAU;QACrB,OAAO,KAAK,WAAW;QACvB,QAAQ,KAAK,YAAY;IAC3B;AACF;AAEA,uGAAuG;AACvG,SAAS,+BACP,IAAU,EACV,MAAc,EACd,IAAY,EAEZ,AADA,2DAC2D,EADE;AAE7D,kBAA8B,EAC9B,AACA,gEAAgE,KADK;AAErE,uEAAuE;AACvE,qEAAqE;AACrE,qDAAqD;AACrD,mBAA+B,EAC/B,OAAe,EACf,2BAAmC;QAEb;IAAtB,IAAI,kBAAkB,CAAA,mCAAA,oBAAoB,MAAM,CAAC,KAAK,MAAA,QAAhC,qCAAA,KAAA,IAAA,mCAAoC;IAC1D,uGAAuG;IACvG,IAAI,eAAe,kBAAkB,CAAC,+BAAS,CAAC,KAAK,CAAC;IACtD,4GAA4G;IAC5G,2GAA2G;IAC3G,IAAI,oBAAoB,mBAAmB,MAAM,CAAC,0BAAI,CAAC,KAAK,CAAC,GAAG;IAChE,IAAI,kBAAkB,eAAe,mBAAmB,MAAM,CAAC,0BAAI,CAAC,KAAK,CAAC,GAAG;IAC7E,IAAI,kBAAkB,SAAS,kBAAkB,2BAA2B,CAAC,KAAK,GAAG,kBAAkB,CAAC,0BAAI,CAAC,KAAK,CAAC;IACnH,IAAI,gBAAgB,SAAS,kBAAkB,OAAO,2BAA2B,CAAC,KAAK,GAAG,kBAAkB,CAAC,0BAAI,CAAC,KAAK,CAAC;IAExH,iIAAiI;IACjI,wCAAwC;IACxC,IAAI,kBAAkB,mBACpB,OAAO,oBAAoB;SACtB,IAAI,gBAAgB,iBACzB,OAAO,KAAK,GAAG,CAAC,kBAAkB,eAAe,oBAAoB;SAErE,OAAO;AAEX;AAEA,SAAS,iCAAW,IAAa;IAC/B,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,OAAO;QACL,KAAK,SAAS,MAAM,SAAS,EAAE,OAAO;QACtC,QAAQ,SAAS,MAAM,YAAY,EAAE,OAAO;QAC5C,MAAM,SAAS,MAAM,UAAU,EAAE,OAAO;QACxC,OAAO,SAAS,MAAM,WAAW,EAAE,OAAO;IAC5C;AACF;AAEA,SAAS,qCAAe,KAAgB;IACtC,IAAI,4CAAsB,CAAC,MAAM,EAC/B,OAAO,4CAAsB,CAAC,MAAM;IAGtC,IAAI,CAAC,WAAW,eAAe,GAAG,MAAM,KAAK,CAAC;IAC9C,IAAI,OAAa,0BAAI,CAAC,UAAU,IAAI;IACpC,IAAI,YAAkB,gCAAU,CAAC,KAAK;IAEtC,IAAI,CAAC,0BAAI,CAAC,eAAe,EACvB,iBAAiB;IAGnB,IAAI,OAAO,+BAAS,CAAC,KAAK;IAC1B,IAAI,YAAY,+BAAS,CAAC,UAAU;IACpC,4CAAsB,CAAC,MAAM,GAAG;mBAAC;wBAAW;cAAgB;mBAAM;cAAW;mBAAM;IAAS;IAC5F,OAAO,4CAAsB,CAAC,MAAM;AACtC;AAEA,SAAS,sCACP,WAAmB,EACnB,kBAA8B,EAC9B,WAAmB,EACnB,aAA8B,EAC9B,MAAc,EACd,WAAmB,EACnB,2BAAmC,EACnC,qBAA8B,EAC9B,SAAiB,EACjB,mBAA2B;IAE3B,IAAI,EAAA,WAAC,SAAS,EAAA,gBAAE,cAAc,EAAA,MAAE,IAAI,EAAA,WAAE,SAAS,EAAA,MAAE,IAAI,EAAA,WAAE,SAAS,EAAC,GAAG;IACpE,IAAI,WAAqB,CAAC;QAGJ;IADtB,kBAAkB;IAClB,QAAQ,CAAC,UAAU,GAAG,CAAA,yBAAA,WAAW,CAAC,UAAU,MAAA,QAAtB,2BAAA,KAAA,IAAA,yBAA0B;QAIpB,wBAAgC,wBAIjC,yBAAgC;IAP3D,IAAI,mBAAmB,UACrB,AACA,4CAD4C,sBACsB;IAClE,QAAQ,CAAC,UAAU,IAAM,CAAC,CAAA,CAAA,yBAAA,WAAW,CAAC,UAAU,MAAA,QAAtB,2BAAA,KAAA,IAAA,yBAA0B,CAAA,IAAM,CAAA,CAAA,yBAAA,WAAW,CAAC,UAAU,MAAA,QAAtB,2BAAA,KAAA,IAAA,yBAA0B,CAAA,CAAC,IAAK;SACrF,IAAI,mBAAmB,WAC5B,AACA,oCADoC,8BAC8B;IAClE,QAAQ,CAAC,UAAU,IAAM,CAAA,CAAA,0BAAA,WAAW,CAAC,UAAU,MAAA,QAAtB,4BAAA,KAAA,IAAA,0BAA0B,CAAA,IAAM,CAAA,CAAA,0BAAA,WAAW,CAAC,UAAU,MAAA,QAAtB,4BAAA,KAAA,IAAA,0BAA0B,CAAA;IACpF;;IAEC,GAEF,QAAQ,CAAC,UAAU,IAAK;IAExB,mDAAmD;IACnD,MAAM,cAAc,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,GAAG,YAAY;IAClF,mDAAmD;IACnD,MAAM,cAAc,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,GAAG,YAAY;IAClF,QAAQ,CAAC,UAAU,GAAG,CAAA,qKAAA,QAAI,EAAE,QAAQ,CAAC,UAAU,EAAG,aAAa;IAE/D,8IAA8I;IAC9I,IAAI,cAAc,MAAM;QACtB,kFAAkF;QAClF,wFAAwF;QACxF,kFAAkF;QAClF,6DAA6D;QAC7D,MAAM,kBAAmB,wBAAwB,2BAA2B,CAAC,KAAK,GAAG,kBAAkB,CAAC,gCAAU,CAAC,KAAK,CAAC;QACzH,QAAQ,CAAC,uCAAiB,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,kBAAkB,WAAW,CAAC,KAAK,GAAG;IACvF,OACE,QAAQ,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG;IAEtE,OAAO;AACT;AAEA,SAAS,mCACP,QAAkB,EAClB,kBAA8B,EAC9B,2BAAmC,EACnC,qBAA8B,EAC9B,OAAiB,EACjB,OAAe,EACf,aAAqB,EACrB,qBAA4C;IAE5C,MAAM,kBAAmB,wBAAwB,4BAA4B,MAAM,GAAG,kBAAkB,CAAC,iCAAW,MAAM,CAAC;QAGoB;IAF/I,qLAAqL;IACrL,2BAA2B;IAC3B,IAAI,aAAa,SAAS,GAAG,IAAI,OAAO,4BAA4B,GAAG,GAAG,SAAS,GAAG,GAAG,4BAA4B,GAAG,GAAI,CAAA,kBAAmB,CAAA,CAAA,mBAAA,SAAS,MAAM,MAAA,QAAf,qBAAA,KAAA,IAAA,mBAAmB,CAAA,IAAK,aAAY;QAIxH,gCAEnD,cAAqB,iBAKI,iCACzB,eAAqB;IAX7B,IAAI,YAAY,0BAA0B,QACxC,AACA,KAAK,GAAG,CAAC,GACN,mBAAmB,MAAM,GAAG,mBAAmB,GAAG,GAAI,CAAA,CAAA,iBAF2B,gBAE3B,mBAAmB,MAAM,CAAC,GAAG,MAAA,QAA7B,mCAAA,KAAA,IAAA,iCAAiC,CAAA,IACtF,WAAW,iCAAiC;OAC3C,CAAC,CAAA,CAAA,eAAA,QAAQ,GAAG,MAAA,QAAX,iBAAA,KAAA,IAAA,eAAe,CAAA,IAAM,CAAA,CAAA,kBAAA,QAAQ,MAAM,MAAA,QAAd,oBAAA,KAAA,IAAA,kBAAkB,CAAA,IAAK,QAAS,+CAA+C;IAAlD,KAGtD,KAAK,GAAG,CAAC,GACR,aAAa,cAAe,oCAAoC;OAC9D,CAAA,mBAAmB,GAAG,GAAI,CAAA,CAAA,kCAAA,mBAAmB,MAAM,CAAC,GAAG,MAAA,QAA7B,oCAAA,KAAA,IAAA,kCAAiC,CAAA,EAAI,kCAAkC;IAArC,IAC5D,CAAC,CAAA,CAAA,gBAAA,QAAQ,GAAG,MAAA,QAAX,kBAAA,KAAA,IAAA,gBAAe,CAAA,IAAM,CAAA,CAAA,mBAAA,QAAQ,MAAM,MAAA,QAAd,qBAAA,KAAA,IAAA,mBAAkB,CAAA,IAAK,QAAS,+CAA+C;IAAlD;IAE1D,OAAO,KAAK,GAAG,CAAC,mBAAmB,MAAM,GAAI,UAAU,GAAI;AAC7D;AAEA,SAAS,wCACP,kBAA8B,EAC9B,2BAAmC,EACnC,WAAmB,EACnB,OAAiB,EACjB,OAAe,EACf,aAA8B;IAE9B,IAAI,EAAA,WAAC,SAAS,EAAA,MAAE,IAAI,EAAA,MAAE,IAAI,EAAC,GAAG;QAEuC,iCAA6E;IADlJ,IAAI,cAAc,MAChB,OAAO,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAI,CAAA,CAAA,kCAAA,mBAAmB,MAAM,CAAC,KAAK,MAAA,QAA/B,oCAAA,KAAA,IAAA,kCAAmC,CAAA,IAAK,2BAA2B,CAAC,KAAK,GAAI,CAAA,CAAA,gBAAA,OAAO,CAAC,KAAK,MAAA,QAAb,kBAAA,KAAA,IAAA,gBAAiB,CAAA,IAAK,OAAO,CAAC,uCAAiB,CAAC,KAAK,CAAC,GAAG;QAGnB;IAAxL,OAAO,KAAK,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAG,mBAAmB,MAAM,CAAC,KAAK,GAAG,2BAA2B,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAI,CAAA,CAAA,iBAAA,OAAO,CAAC,KAAK,MAAA,QAAb,mBAAA,KAAA,IAAA,iBAAiB,CAAA,IAAK,OAAO,CAAC,uCAAiB,CAAC,KAAK,CAAC,GAAG;AACnP;AAEO,SAAS,0CACd,cAAyB,EACzB,WAAmB,EACnB,WAAmB,EACnB,UAAkB,EAClB,OAAiB,EACjB,OAAe,EACf,IAAa,EACb,kBAA8B,EAC9B,mBAA+B,EAC/B,2BAAmC,EACnC,MAAc,EACd,WAAmB,EACnB,qBAA8B,EAC9B,gBAAoC,EACpC,SAAiB,EACjB,mBAA2B;IAE3B,IAAI,gBAAgB,qCAAe;IACnC,IAAI,EAAA,MAAC,IAAI,EAAA,WAAE,SAAS,EAAA,WAAE,SAAS,EAAA,WAAE,SAAS,EAAA,gBAAE,cAAc,EAAC,GAAG;IAC9D,IAAI,WAAW,sCAAgB,aAAa,oBAAoB,aAAa,eAAe,QAAQ,aAAa,6BAA6B,uBAAuB,WAAW;IAChL,IAAI,mBAAmB;IACvB,IAAI,QAAQ,wCACV,oBACA,6BACA,aACA,SACA,UAAU,QACV;IAGF,8GAA8G;IAC9G,IAAI,QAAQ,UAAU,CAAC,KAAK,GAAG,OAAO;QACpC,IAAI,uBAAuB,qCAAe,UAAG,uCAAiB,CAAC,UAAU,EAAC,CAAC,IAAE,CAAgB,MAAhB;QAC7E,IAAI,kBAAkB,sCAAgB,aAAa,oBAAoB,aAAa,sBAAsB,QAAQ,aAAa,6BAA6B,uBAAuB,WAAW;QAC9L,IAAI,eAAe,wCACjB,oBACA,6BACA,aACA,SACA,UAAU,QACV;QAGF,sGAAsG;QACtG,IAAI,eAAe,OAAO;YACxB,gBAAgB;YAChB,WAAW;YACX,mBAAmB;QACrB;IACF;IAEA,mHAAmH;IACnH,IAAI,wBAA+C;IACnD,IAAI,cAAc,IAAI,KAAK,OAAO;QAChC,IAAI,cAAc,SAAS,KAAK,OAC9B,wBAAwB;aACnB,IAAI,cAAc,SAAS,KAAK,UACrC,wBAAwB;IAE5B,OAAO,IAAI,cAAc,SAAS,KAAK,OAAO;QAC5C,IAAI,cAAc,cAAc,KAAK,OACnC,wBAAwB;aACnB,IAAI,cAAc,cAAc,KAAK,UAC1C,wBAAwB;IAE5B;IAEA,IAAI,QAAQ,+BAAS,WAAW,QAAQ,CAAC,UAAU,EAAG,WAAW,CAAC,UAAU,EAAE,oBAAoB,qBAAqB,SAAS;IAChI,QAAQ,CAAC,UAAU,IAAK;IAExB,IAAI,YAAY,mCACd,UACA,oBACA,6BACA,uBACA,SACA,SACA,YAAY,MAAM,EAClB;IAGF,IAAI,oBAAoB,mBAAmB,WACzC,YAAY;IAGd,YAAY,MAAM,GAAG,KAAK,GAAG,CAAC,YAAY,MAAM,EAAE;IAElD,WAAW,sCAAgB,aAAa,oBAAoB,aAAa,eAAe,kBAAkB,aAAa,6BAA6B,uBAAuB,WAAW;IACtL,QAAQ,+BAAS,WAAW,QAAQ,CAAC,UAAU,EAAG,WAAW,CAAC,UAAU,EAAE,oBAAoB,qBAAqB,SAAS;IAC5H,QAAQ,CAAC,UAAU,IAAK;IAExB,IAAI,gBAA0B,CAAC;IAE/B,sGAAsG;IACtG,6EAA6E;IAC7E,mLAAmL;IACnL,yMAAyM;IACzM,8HAA8H;IAC9H,IAAI,yBAAyB,WAAW,CAAC,UAAU,GAAG,KAAK,WAAW,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,GAAI,OAAO,CAAC,0BAAI,CAAC,UAAU,CAAC;IAEnI,oEAAoE;IACpE,MAAM,mBAAmB,YAAY,IAAI;QAEW,eAAsB,gBAAuB,cAAqB;IADtH,8DAA8D;IAC9D,MAAM,gBAAgB,0BAAI,CAAC,UAAU,KAAK,SAAU,CAAA,CAAA,gBAAA,QAAQ,IAAI,MAAA,QAAZ,kBAAA,KAAA,IAAA,gBAAgB,CAAA,IAAM,CAAA,CAAA,iBAAA,QAAQ,KAAK,MAAA,QAAb,mBAAA,KAAA,IAAA,iBAAiB,CAAA,IAAM,CAAA,CAAA,eAAA,QAAQ,GAAG,MAAA,QAAX,iBAAA,KAAA,IAAA,eAAe,CAAA,IAAM,CAAA,CAAA,kBAAA,QAAQ,MAAM,MAAA,QAAd,oBAAA,KAAA,IAAA,kBAAkB,CAAA;IACxI,MAAM,mBAAmB,WAAW,CAAC,UAAU,GAAG,gBAAiB,YAAY,IAAK;IAEpF,2FAA2F;IAC3F,+EAA+E;IAC/E,MAAM,+BAA+B,WAAW,CAAC,UAAU,GAAI,YAAY,IAAM,CAAA,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,0BAAI,CAAC,UAAU,CAAA;IAC9H,MAAM,+BAA+B,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,GAAI,YAAY,IAAM,CAAA,QAAQ,CAAC,UAAU,GAAG,OAAO,CAAC,0BAAI,CAAC,UAAU,CAAA;IAEvJ,mGAAmG;IACnG,MAAM,gCAAgC,CAAA,qKAAA,QAAI,EAAE,wBAAwB,8BAA8B;IAClG,aAAa,CAAC,UAAU,GAAG,CAAA,qKAAA,QAAI,EAAE,+BAA+B,kBAAkB;IAElF,OAAO;kBACL;QACA,WAAW;QACX,iBAAiB,cAAc,IAAI;QACnC,gBAAgB,cAAc,GAAG;QACjC,WAAW,cAAc,SAAS;IACpC;AACF;AAKO,SAAS,0CAAkB,IAAkB;IAClD,IAAI,EAAA,WACF,SAAS,EAAA,YACT,UAAU,EAAA,aACV,WAAW,EAAA,YACX,UAAU,EAAA,SACV,OAAO,EAAA,YACP,UAAU,EAAA,iBACV,eAAe,EAAA,QACf,MAAM,EAAA,aACN,WAAW,EAAA,WACX,SAAS,EAAA,WACT,YAAY,CAAA,EAAA,qBACZ,sBAAsB,CAAA,EACvB,GAAG;IAEJ,IAAI,YAAY,uBAAuB,cAAc,yCAAmB,eAAe,SAAS,eAAe;IAC/G,IAAI,sBAAsB,cAAc,SAAS,eAAe;IAChE,MAAM,yBAAyB,OAAO,gBAAgB,CAAC,WAAW,QAAQ;IAC1E,IAAI,wBAAwB,CAAC,CAAC,0BAA0B,2BAA2B;IACnF,IAAI,cAAsB,sBAAsB,gCAAU,cAAc,kCAAY,YAAY;IAEhG,IAAI,CAAC,qBAAqB;QACxB,IAAI,EAAA,WAAC,SAAS,EAAA,YAAE,UAAU,EAAC,GAAG,OAAO,gBAAgB,CAAC;QACtD,YAAY,GAAG,IAAI,SAAS,WAAW,OAAO;QAC9C,YAAY,IAAI,IAAI,SAAS,YAAY,OAAO;IAClD;IAEA,IAAI,cAAsB,gCAAU;IACpC,IAAI,UAAU,iCAAW;QACH,eAAsB;IAA5C,YAAY,KAAK,IAAK,CAAA,CAAA,gBAAA,QAAQ,IAAI,MAAA,QAAZ,kBAAA,KAAA,IAAA,gBAAgB,CAAA,IAAM,CAAA,CAAA,iBAAA,QAAQ,KAAK,MAAA,QAAb,mBAAA,KAAA,IAAA,iBAAiB,CAAA;QACtC,cAAqB;IAA5C,YAAY,MAAM,IAAK,CAAA,CAAA,eAAA,QAAQ,GAAG,MAAA,QAAX,iBAAA,KAAA,IAAA,eAAe,CAAA,IAAM,CAAA,CAAA,kBAAA,QAAQ,MAAM,MAAA,QAAd,oBAAA,KAAA,IAAA,kBAAkB,CAAA;IAE9D,IAAI,aAAa,gCAAU;IAC3B,IAAI,qBAAqB,6CAAuB;IAChD,IAAI,sBAAsB,6CAAuB;IACjD,0HAA0H;IAC1H,yJAAyJ;IACzJ,wHAAwH;IACxH,IAAI,8BAAsC,gBAAgB,OAAO,KAAK,SAAS,gCAAU,aAAa,kCAAY,WAAW;IAC7H,IAAI,UAAU,OAAO,KAAK,UAAU,gBAAgB,OAAO,KAAK,QAAQ;QACtE,oBAAoB,MAAM,CAAC,GAAG,GAAG;QACjC,oBAAoB,MAAM,CAAC,IAAI,GAAG;IACpC;IAEA,OAAO,0CACL,WACA,aACA,aACA,YACA,SACA,SACA,YACA,oBACA,qBACA,6BACA,QACA,aACA,uBACA,WACA,WACA;AAEJ;AAEA,SAAS,gCAAU,IAAa;IAC9B,IAAI,EAAA,KAAC,GAAG,EAAA,MAAE,IAAI,EAAA,OAAE,KAAK,EAAA,QAAE,MAAM,EAAC,GAAG,KAAK,qBAAqB;IAC3D,IAAI,EAAA,WAAC,SAAS,EAAA,YAAE,UAAU,EAAA,WAAE,SAAS,EAAA,YAAE,UAAU,EAAC,GAAG,SAAS,eAAe;IAC7E,OAAO;QACL,KAAK,MAAM,YAAY;QACvB,MAAM,OAAO,aAAa;eAC1B;gBACA;IACF;AACF;AAEA,SAAS,kCAAY,IAAa,EAAE,MAAe;IACjD,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,IAAI;IACJ,IAAI,MAAM,QAAQ,KAAK,SAAS;QAC9B,IAAI,EAAA,KAAC,GAAG,EAAA,MAAE,IAAI,EAAA,OAAE,KAAK,EAAA,QAAE,MAAM,EAAC,GAAG,KAAK,qBAAqB;QAC3D,SAAS;iBAAC;kBAAK;mBAAM;oBAAO;QAAM;IACpC,OAAO;QACL,SAAS,gCAAU;QACnB,IAAI,eAAe,gCAAU;QAC7B,IAAI,cAAc,OAAO,gBAAgB,CAAC;QAC1C,aAAa,GAAG,IAAK,CAAA,SAAS,YAAY,cAAc,EAAE,OAAO,CAAA,IAAK,OAAO,SAAS;QACtF,aAAa,IAAI,IAAK,CAAA,SAAS,YAAY,eAAe,EAAE,OAAO,CAAA,IAAK,OAAO,UAAU;QACzF,OAAO,GAAG,IAAI,aAAa,GAAG;QAC9B,OAAO,IAAI,IAAI,aAAa,IAAI;IAClC;IAEA,OAAO,GAAG,IAAI,SAAS,MAAM,SAAS,EAAE,OAAO;IAC/C,OAAO,IAAI,IAAI,SAAS,MAAM,UAAU,EAAE,OAAO;IACjD,OAAO;AACT;AAEA,wEAAwE;AACxE,+CAA+C;AAC/C,oEAAoE;AACpE,SAAS,yCAAmB,IAAiB;IAC3C,4EAA4E;IAC5E,8EAA8E;IAC9E,IAAI,eAAe,KAAK,YAAY;IAEpC,8DAA8D;IAC9D,gEAAgE;IAChE,qCAAqC;IACrC,IACE,gBACA,iBAAiB,SAAS,IAAI,IAC9B,OAAO,gBAAgB,CAAC,cAAc,QAAQ,KAAK,YACnD,CAAC,wCAAkB,eAEnB,eAAe,SAAS,eAAe;IAGzC,sCAAsC;IAEtC,yFAAyF;IACzF,sFAAsF;IACtF,6FAA6F;IAC7F,IAAI,gBAAgB,MAAM;QACxB,eAAe,KAAK,aAAa;QACjC,MAAO,gBAAgB,CAAC,wCAAkB,cACxC,eAAe,aAAa,aAAa;IAE7C;IAEA,6BAA6B;IAC7B,OAAO,gBAAgB,SAAS,eAAe;AACjD;AAEA,qGAAqG;AACrG,SAAS,wCAAkB,IAAa;IACtC,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,OACE,MAAM,SAAS,KAAK,UACpB,wBAAwB,IAAI,CAAC,MAAM,UAAU,KAC7C,MAAM,MAAM,KAAK,UACjB,MAAM,OAAO,KAAK,WACjB,oBAAoB,SAAS,MAAM,cAAc,KAAK,UACtD,0BAA0B,SAAS,MAAM,oBAAoB,KAAK;AAEvE", "debugId": null}}, {"offset": {"line": 3215, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/useOverlayPosition.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/useOverlayPosition.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {calculatePosition, PositionResult} from './calculatePosition';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {Placement, PlacementAxis, PositionProps} from '@react-types/overlays';\nimport {useCallback, useEffect, useRef, useState} from 'react';\nimport {useCloseOnScroll} from './useCloseOnScroll';\nimport {useLayoutEffect, useResizeObserver} from '@react-aria/utils';\nimport {useLocale} from '@react-aria/i18n';\n\nexport interface AriaPositionProps extends PositionProps {\n  /**\n   * Cross size of the overlay arrow in pixels.\n   * @default 0\n   */\n  arrowSize?: number,\n  /**\n   * Element that that serves as the positioning boundary.\n   * @default document.body\n   */\n  boundaryElement?: Element,\n  /**\n   * The ref for the element which the overlay positions itself with respect to.\n   */\n  targetRef: RefObject<Element | null>,\n  /**\n   * The ref for the overlay element.\n   */\n  overlayRef: RefObject<Element | null>,\n  /**\n   * A ref for the scrollable region within the overlay.\n   * @default overlayRef\n   */\n  scrollRef?: RefObject<Element | null>,\n  /**\n   * Whether the overlay should update its position automatically.\n   * @default true\n   */\n  shouldUpdatePosition?: boolean,\n  /** Handler that is called when the overlay should close. */\n  onClose?: (() => void) | null,\n  /**\n   * The maxHeight specified for the overlay element.\n   * By default, it will take all space up to the current viewport height.\n   */\n  maxHeight?: number,\n  /**\n   * The minimum distance the arrow's edge should be from the edge of the overlay element.\n   * @default 0\n   */\n  arrowBoundaryOffset?: number\n}\n\nexport interface PositionAria {\n  /** Props for the overlay container element. */\n  overlayProps: DOMAttributes,\n  /** Props for the overlay tip arrow if any. */\n  arrowProps: DOMAttributes,\n  /** Placement of the overlay with respect to the overlay trigger. */\n  placement: PlacementAxis | null,\n  /** Updates the position of the overlay. */\n  updatePosition(): void\n}\n\ninterface ScrollAnchor {\n  type: 'top' | 'bottom',\n  offset: number\n}\n\nlet visualViewport = typeof document !== 'undefined' ? window.visualViewport : null;\n\n/**\n * Handles positioning overlays like popovers and menus relative to a trigger\n * element, and updating the position when the window resizes.\n */\nexport function useOverlayPosition(props: AriaPositionProps): PositionAria {\n  let {direction} = useLocale();\n  let {\n    arrowSize = 0,\n    targetRef,\n    overlayRef,\n    scrollRef = overlayRef,\n    placement = 'bottom' as Placement,\n    containerPadding = 12,\n    shouldFlip = true,\n    boundaryElement = typeof document !== 'undefined' ? document.body : null,\n    offset = 0,\n    crossOffset = 0,\n    shouldUpdatePosition = true,\n    isOpen = true,\n    onClose,\n    maxHeight,\n    arrowBoundaryOffset = 0\n  } = props;\n  let [position, setPosition] = useState<PositionResult | null>(null);\n\n  let deps = [\n    shouldUpdatePosition,\n    placement,\n    overlayRef.current,\n    targetRef.current,\n    scrollRef.current,\n    containerPadding,\n    shouldFlip,\n    boundaryElement,\n    offset,\n    crossOffset,\n    isOpen,\n    direction,\n    maxHeight,\n    arrowBoundaryOffset,\n    arrowSize\n  ];\n\n  // Note, the position freezing breaks if body sizes itself dynamicly with the visual viewport but that might\n  // just be a non-realistic use case\n  // Upon opening a overlay, record the current visual viewport scale so we can freeze the overlay styles\n  let lastScale = useRef(visualViewport?.scale);\n  useEffect(() => {\n    if (isOpen) {\n      lastScale.current = visualViewport?.scale;\n    }\n  }, [isOpen]);\n\n  let updatePosition = useCallback(() => {\n    if (shouldUpdatePosition === false || !isOpen || !overlayRef.current || !targetRef.current || !boundaryElement) {\n      return;\n    }\n\n    if (visualViewport?.scale !== lastScale.current) {\n      return;\n    }\n\n    // Determine a scroll anchor based on the focused element.\n    // This stores the offset of the anchor element from the scroll container\n    // so it can be restored after repositioning. This way if the overlay height\n    // changes, the focused element appears to stay in the same position.\n    let anchor: ScrollAnchor | null = null;\n    if (scrollRef.current && scrollRef.current.contains(document.activeElement)) {\n      let anchorRect = document.activeElement?.getBoundingClientRect();\n      let scrollRect = scrollRef.current.getBoundingClientRect();\n      // Anchor from the top if the offset is in the top half of the scrollable element,\n      // otherwise anchor from the bottom.\n      anchor = {\n        type: 'top',\n        offset: (anchorRect?.top ?? 0) - scrollRect.top\n      };\n      if (anchor.offset > scrollRect.height / 2) {\n        anchor.type = 'bottom';\n        anchor.offset = (anchorRect?.bottom ?? 0) - scrollRect.bottom;\n      }\n    }\n\n    // Always reset the overlay's previous max height if not defined by the user so that we can compensate for\n    // RAC collections populating after a second render and properly set a correct max height + positioning when it populates.\n    let overlay = (overlayRef.current as HTMLElement);\n    if (!maxHeight && overlayRef.current) {\n      overlay.style.top = '0px';\n      overlay.style.bottom = '';\n      overlay.style.maxHeight = (window.visualViewport?.height ?? window.innerHeight) + 'px';\n    }\n\n    let position = calculatePosition({\n      placement: translateRTL(placement, direction),\n      overlayNode: overlayRef.current,\n      targetNode: targetRef.current,\n      scrollNode: scrollRef.current || overlayRef.current,\n      padding: containerPadding,\n      shouldFlip,\n      boundaryElement,\n      offset,\n      crossOffset,\n      maxHeight,\n      arrowSize,\n      arrowBoundaryOffset\n    });\n\n    if (!position.position) {\n      return;\n    }\n\n    // Modify overlay styles directly so positioning happens immediately without the need of a second render\n    // This is so we don't have to delay autoFocus scrolling or delay applying preventScroll for popovers\n    overlay.style.top = '';\n    overlay.style.bottom = '';\n    overlay.style.left = '';\n    overlay.style.right = '';\n\n    Object.keys(position.position).forEach(key => overlay.style[key] = (position.position!)[key] + 'px');\n    overlay.style.maxHeight = position.maxHeight != null ?  position.maxHeight + 'px' : '';\n\n    // Restore scroll position relative to anchor element.\n    if (anchor && document.activeElement && scrollRef.current) {\n      let anchorRect = document.activeElement.getBoundingClientRect();\n      let scrollRect = scrollRef.current.getBoundingClientRect();\n      let newOffset = anchorRect[anchor.type] - scrollRect[anchor.type];\n      scrollRef.current.scrollTop += newOffset - anchor.offset;\n    }\n\n    // Trigger a set state for a second render anyway for arrow positioning\n    setPosition(position);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps);\n\n  // Update position when anything changes\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useLayoutEffect(updatePosition, deps);\n\n  // Update position on window resize\n  useResize(updatePosition);\n\n  // Update position when the overlay changes size (might need to flip).\n  useResizeObserver({\n    ref: overlayRef,\n    onResize: updatePosition\n  });\n\n  // Update position when the target changes size (might need to flip).\n  useResizeObserver({\n    ref: targetRef,\n    onResize: updatePosition\n  });\n\n  // Reposition the overlay and do not close on scroll while the visual viewport is resizing.\n  // This will ensure that overlays adjust their positioning when the iOS virtual keyboard appears.\n  let isResizing = useRef(false);\n  useLayoutEffect(() => {\n    let timeout: ReturnType<typeof setTimeout>;\n    let onResize = () => {\n      isResizing.current = true;\n      clearTimeout(timeout);\n\n      timeout = setTimeout(() => {\n        isResizing.current = false;\n      }, 500);\n\n      updatePosition();\n    };\n\n    // Only reposition the overlay if a scroll event happens immediately as a result of resize (aka the virtual keyboard has appears)\n    // We don't want to reposition the overlay if the user has pinch zoomed in and is scrolling the viewport around.\n    let onScroll = () => {\n      if (isResizing.current) {\n        onResize();\n      }\n    };\n\n    visualViewport?.addEventListener('resize', onResize);\n    visualViewport?.addEventListener('scroll', onScroll);\n    return () => {\n      visualViewport?.removeEventListener('resize', onResize);\n      visualViewport?.removeEventListener('scroll', onScroll);\n    };\n  }, [updatePosition]);\n\n  let close = useCallback(() => {\n    if (!isResizing.current) {\n      onClose?.();\n    }\n  }, [onClose, isResizing]);\n\n  // When scrolling a parent scrollable region of the trigger (other than the body),\n  // we hide the popover. Otherwise, its position would be incorrect.\n  useCloseOnScroll({\n    triggerRef: targetRef,\n    isOpen,\n    onClose: onClose && close\n  });\n\n  return {\n    overlayProps: {\n      style: {\n        position: 'absolute',\n        zIndex: 100000, // should match the z-index in ModalTrigger\n        ...position?.position,\n        maxHeight: position?.maxHeight ?? '100vh'\n      }\n    },\n    placement: position?.placement ?? null,\n    arrowProps: {\n      'aria-hidden': 'true',\n      role: 'presentation',\n      style: {\n        left: position?.arrowOffsetLeft,\n        top: position?.arrowOffsetTop\n      }\n    },\n    updatePosition\n  };\n}\n\nfunction useResize(onResize) {\n  useLayoutEffect(() => {\n    window.addEventListener('resize', onResize, false);\n    return () => {\n      window.removeEventListener('resize', onResize, false);\n    };\n  }, [onResize]);\n}\n\nfunction translateRTL(position, direction) {\n  if (direction === 'rtl') {\n    return position.replace('start', 'right').replace('end', 'left');\n  }\n  return position.replace('start', 'left').replace('end', 'right');\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqED,IAAI,uCAAiB,OAAO,aAAa,cAAc,OAAO,cAAc,GAAG;AAMxE,SAAS,0CAAmB,KAAwB;IACzD,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,kKAAA,YAAQ;IAC1B,IAAI,EAAA,WACF,YAAY,CAAA,EAAA,WACZ,SAAS,EAAA,YACT,UAAU,EAAA,WACV,YAAY,UAAA,EAAA,WACZ,YAAY,QAAA,EAAA,kBACZ,mBAAmB,EAAA,EAAA,YACnB,aAAa,IAAA,EAAA,iBACb,kBAAkB,OAAO,aAAa,cAAc,SAAS,IAAI,GAAG,IAAA,EAAA,QACpE,SAAS,CAAA,EAAA,aACT,cAAc,CAAA,EAAA,sBACd,uBAAuB,IAAA,EAAA,QACvB,SAAS,IAAA,EAAA,SACT,OAAO,EAAA,WACP,SAAS,EAAA,qBACT,sBAAsB,CAAA,EACvB,GAAG;IACJ,IAAI,CAAC,UAAU,YAAY,GAAG,CAAA,iKAAA,WAAO,EAAyB;IAE9D,IAAI,OAAO;QACT;QACA;QACA,WAAW,OAAO;QAClB,UAAU,OAAO;QACjB,UAAU,OAAO;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4GAA4G;IAC5G,mCAAmC;IACnC,uGAAuG;IACvG,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,KAAK;IAC5C,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QACF,UAAU,OAAO,GAAG,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,KAAK;IAE7C,GAAG;QAAC;KAAO;IAEX,IAAI,iBAAiB,CAAA,iKAAA,cAAU,EAAE;QAC/B,IAAI,yBAAyB,SAAS,CAAC,UAAU,CAAC,WAAW,OAAO,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,iBAC7F;QAGF,IAAI,CAAA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,KAAK,MAAK,UAAU,OAAO,EAC7C;QAGF,0DAA0D;QAC1D,yEAAyE;QACzE,4EAA4E;QAC5E,qEAAqE;QACrE,IAAI,SAA8B;QAClC,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,QAAQ,CAAC,SAAS,aAAa,GAAG;gBAC1D;YAAjB,IAAI,aAAA,CAAa,0BAAA,SAAS,aAAa,MAAA,QAAtB,4BAAA,KAAA,IAAA,KAAA,IAAA,wBAAwB,qBAAqB;YAC9D,IAAI,aAAa,UAAU,OAAO,CAAC,qBAAqB;gBAK7C;YAJX,kFAAkF;YAClF,oCAAoC;YACpC,SAAS;gBACP,MAAM;gBACN,QAAS,CAAA,CAAA,kBAAA,eAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAY,GAAG,MAAA,QAAf,oBAAA,KAAA,IAAA,kBAAmB,CAAA,IAAK,WAAW,GAAG;YACjD;YACA,IAAI,OAAO,MAAM,GAAG,WAAW,MAAM,GAAG,GAAG;gBACzC,OAAO,IAAI,GAAG;oBACG;gBAAjB,OAAO,MAAM,GAAI,CAAA,CAAA,qBAAA,eAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAY,MAAM,MAAA,QAAlB,uBAAA,KAAA,IAAA,qBAAsB,CAAA,IAAK,WAAW,MAAM;YAC/D;QACF;QAEA,0GAA0G;QAC1G,0HAA0H;QAC1H,IAAI,UAAW,WAAW,OAAO;QACjC,IAAI,CAAC,aAAa,WAAW,OAAO,EAAE;gBAGT;YAF3B,QAAQ,KAAK,CAAC,GAAG,GAAG;YACpB,QAAQ,KAAK,CAAC,MAAM,GAAG;gBACI;YAA3B,QAAQ,KAAK,CAAC,SAAS,GAAI,CAAA,CAAA,gCAAA,CAAA,yBAAA,OAAO,cAAc,MAAA,QAArB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAuB,MAAM,MAAA,QAA7B,kCAAA,KAAA,IAAA,gCAAiC,OAAO,WAAU,IAAK;QACpF;QAEA,IAAI,WAAW,CAAA,gLAAA,oBAAgB,EAAE;YAC/B,WAAW,mCAAa,WAAW;YACnC,aAAa,WAAW,OAAO;YAC/B,YAAY,UAAU,OAAO;YAC7B,YAAY,UAAU,OAAO,IAAI,WAAW,OAAO;YACnD,SAAS;wBACT;6BACA;oBACA;yBACA;uBACA;uBACA;iCACA;QACF;QAEA,IAAI,CAAC,SAAS,QAAQ,EACpB;QAGF,wGAAwG;QACxG,qGAAqG;QACrG,QAAQ,KAAK,CAAC,GAAG,GAAG;QACpB,QAAQ,KAAK,CAAC,MAAM,GAAG;QACvB,QAAQ,KAAK,CAAC,IAAI,GAAG;QACrB,QAAQ,KAAK,CAAC,KAAK,GAAG;QAEtB,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,OAAO,CAAC,CAAA,MAAO,QAAQ,KAAK,CAAC,IAAI,GAAI,SAAS,QAAU,CAAC,IAAI,GAAG;QAC/F,QAAQ,KAAK,CAAC,SAAS,GAAG,SAAS,SAAS,IAAI,OAAQ,SAAS,SAAS,GAAG,OAAO;QAEpF,sDAAsD;QACtD,IAAI,UAAU,SAAS,aAAa,IAAI,UAAU,OAAO,EAAE;YACzD,IAAI,aAAa,SAAS,aAAa,CAAC,qBAAqB;YAC7D,IAAI,aAAa,UAAU,OAAO,CAAC,qBAAqB;YACxD,IAAI,YAAY,UAAU,CAAC,OAAO,IAAI,CAAC,GAAG,UAAU,CAAC,OAAO,IAAI,CAAC;YACjE,UAAU,OAAO,CAAC,SAAS,IAAI,YAAY,OAAO,MAAM;QAC1D;QAEA,uEAAuE;QACvE,YAAY;IACd,uDAAuD;IACvD,GAAG;IAEH,wCAAwC;IACxC,uDAAuD;IACvD,CAAA,2KAAA,kBAAc,EAAE,gBAAgB;IAEhC,mCAAmC;IACnC,gCAAU;IAEV,sEAAsE;IACtE,CAAA,6KAAA,oBAAgB,EAAE;QAChB,KAAK;QACL,UAAU;IACZ;IAEA,qEAAqE;IACrE,CAAA,6KAAA,oBAAgB,EAAE;QAChB,KAAK;QACL,UAAU;IACZ;IAEA,2FAA2F;IAC3F,iGAAiG;IACjG,IAAI,aAAa,CAAA,iKAAA,SAAK,EAAE;IACxB,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI;QACJ,IAAI,WAAW;YACb,WAAW,OAAO,GAAG;YACrB,aAAa;YAEb,UAAU,WAAW;gBACnB,WAAW,OAAO,GAAG;YACvB,GAAG;YAEH;QACF;QAEA,iIAAiI;QACjI,gHAAgH;QAChH,IAAI,WAAW;YACb,IAAI,WAAW,OAAO,EACpB;QAEJ;QAEA,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,gBAAgB,CAAC,UAAU;QAC3C,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,gBAAgB,CAAC,UAAU;QAC3C,OAAO;YACL,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,mBAAmB,CAAC,UAAU;YAC9C,yCAAA,QAAA,yCAAA,KAAA,IAAA,KAAA,IAAA,qCAAgB,mBAAmB,CAAC,UAAU;QAChD;IACF,GAAG;QAAC;KAAe;IAEnB,IAAI,QAAQ,CAAA,iKAAA,cAAU,EAAE;QACtB,IAAI,CAAC,WAAW,OAAO,EACrB,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA;IAEJ,GAAG;QAAC;QAAS;KAAW;IAExB,kFAAkF;IAClF,mEAAmE;IACnE,CAAA,+KAAA,mBAAe,EAAE;QACf,YAAY;gBACZ;QACA,SAAS,WAAW;IACtB;QAQiB,qBAGJ;IATb,OAAO;QACL,cAAc;YACZ,OAAO;gBACL,UAAU;gBACV,QAAQ;mBACL,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,QAAb;gBACA,WAAW,CAAA,sBAAA,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,SAAS,MAAA,QAAnB,wBAAA,KAAA,IAAA,sBAAuB;YACpC;QACF;QACA,WAAW,CAAA,sBAAA,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,SAAS,MAAA,QAAnB,wBAAA,KAAA,IAAA,sBAAuB;QAClC,YAAY;YACV,eAAe;YACf,MAAM;YACN,OAAO;gBACL,IAAI,EAAE,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,eAAe;gBAC/B,GAAG,EAAE,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,cAAc;YAC/B;QACF;wBACA;IACF;AACF;AAEA,SAAS,gCAAU,QAAQ;IACzB,CAAA,2KAAA,kBAAc,EAAE;QACd,OAAO,gBAAgB,CAAC,UAAU,UAAU;QAC5C,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU,UAAU;QACjD;IACF,GAAG;QAAC;KAAS;AACf;AAEA,SAAS,mCAAa,QAAQ,EAAE,SAAS;IACvC,IAAI,cAAc,OAChB,OAAO,SAAS,OAAO,CAAC,SAAS,SAAS,OAAO,CAAC,OAAO;IAE3D,OAAO,SAAS,OAAO,CAAC,SAAS,QAAQ,OAAO,CAAC,OAAO;AAC1D", "debugId": null}}, {"offset": {"line": 3436, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/ar-AE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/ar-AE.json"], "sourcesContent": ["{\n  \"dismiss\": \"تجاهل\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,mCAAK,CAAC;AACtC", "debugId": null}}, {"offset": {"line": 3449, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/bg-BG.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/bg-BG.json"], "sourcesContent": ["{\n  \"dismiss\": \"Отхвърляне\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,sEAAU,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 3462, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/cs-CZ.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"dismiss\": \"Odstranit\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,SAAS,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3475, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/da-DK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/da-DK.json"], "sourcesContent": ["{\n  \"dismiss\": \"Luk\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,GAAG,CAAC;AACpC", "debugId": null}}, {"offset": {"line": 3488, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/de-DE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/de-DE.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>ße<PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,YAAS,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3501, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/el-GR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/el-GR.json"], "sourcesContent": ["{\n  \"dismiss\": \"Απόρριψη\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,wDAAQ,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 3514, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/en-US.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/en-US.json"], "sourcesContent": ["{\n  \"dismiss\": \"Dism<PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,OAAO,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/es-ES.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/es-ES.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,SAAS,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3540, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/et-EE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/et-EE.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,SAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/fi-FI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/fi-FI.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,YAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3566, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/fr-FR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/fr-FR.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,OAAO,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 3579, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/he-IL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/he-IL.json"], "sourcesContent": ["{\n  \"dismiss\": \"התעלם\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,mCAAK,CAAC;AACtC", "debugId": null}}, {"offset": {"line": 3592, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/hr-HR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/hr-HR.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,MAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3605, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/hu-HU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/hu-HU.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,gBAAU,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 3618, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/it-IT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/it-IT.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,MAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3631, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/ja-JP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/ja-JP.json"], "sourcesContent": ["{\n  \"dismiss\": \"閉じる\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,wBAAG,CAAC;AACpC", "debugId": null}}, {"offset": {"line": 3644, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/ko-KR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/ko-KR.json"], "sourcesContent": ["{\n  \"dismiss\": \"무시\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,gBAAE,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 3657, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/lt-LT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/lt-LT.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,OAAO,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 3670, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/lv-LV.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/lv-LV.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,mBAAO,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 3683, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/nb-NO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/nb-NO.json"], "sourcesContent": ["{\n  \"dismiss\": \"Lukk\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,IAAI,CAAC;AACrC", "debugId": null}}, {"offset": {"line": 3696, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/nl-NL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/nl-NL.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,OAAO,CAAC;AACxC", "debugId": null}}, {"offset": {"line": 3709, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/pl-PL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/pl-PL.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,QAAQ,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 3722, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/pt-BR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/pt-BR.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,SAAS,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3735, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/pt-PT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/pt-PT.json"], "sourcesContent": ["{\n  \"dismiss\": \"Dispensar\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,SAAS,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3748, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/ro-RO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/ro-RO.json"], "sourcesContent": ["{\n  \"dismiss\": \"Revocare\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,QAAQ,CAAC;AACzC", "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/ru-RU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/ru-RU.json"], "sourcesContent": ["{\n  \"dismiss\": \"Пропустить\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,sEAAU,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 3774, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/sk-SK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/sk-SK.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,kBAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3787, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/sl-SI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/sl-SI.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,MAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3800, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/sr-SP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/sr-SP.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,MAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3813, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/sv-SE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/sv-SE.json"], "sourcesContent": ["{\n  \"dismiss\": \"Avvisa\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,MAAM,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/tr-TR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/tr-TR.json"], "sourcesContent": ["{\n  \"dismiss\": \"<PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,KAAK,CAAC;AACtC", "debugId": null}}, {"offset": {"line": 3839, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/uk-UA.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/uk-UA.json"], "sourcesContent": ["{\n  \"dismiss\": \"Скасувати\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,+DAAS,CAAC;AAC1C", "debugId": null}}, {"offset": {"line": 3852, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/zh-CN.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/zh-CN.json"], "sourcesContent": ["{\n  \"dismiss\": \"取消\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,gBAAE,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 3865, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/zh-TW.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/intl/zh-TW.json"], "sourcesContent": ["{\n  \"dismiss\": \"關閉\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,WAAW,AAAC,gBAAE,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 3878, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/intlStrings.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/%2A.js"], "sourcesContent": ["const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4BAAiB;IACf,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;IACT,6KAAS,UAAA;AACX", "debugId": null}}, {"offset": {"line": 3992, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/overlays/dist/DismissButton.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/overlays/dist/packages/%40react-aria/overlays/src/DismissButton.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport React, {JSX} from 'react';\nimport {useLabels} from '@react-aria/utils';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {VisuallyHidden} from '@react-aria/visually-hidden';\n\nexport interface DismissButtonProps extends AriaLabelingProps, DOMProps {\n  /** Called when the dismiss button is activated. */\n  onDismiss?: () => void\n}\n\n/**\n * A visually hidden button that can be used to allow screen reader\n * users to dismiss a modal or popup when there is no visual\n * affordance to do so.\n */\nexport function DismissButton(props: DismissButtonProps): JSX.Element {\n  let {onDismiss, ...otherProps} = props;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/overlays');\n\n  let labels = useLabels(otherProps, stringFormatter.format('dismiss'));\n\n  let onClick = () => {\n    if (onDismiss) {\n      onDismiss();\n    }\n  };\n\n  return (\n    <VisuallyHidden>\n      <button\n        {...labels}\n        tabIndex={-1}\n        onClick={onClick}\n        style={{width: 1, height: 1}} />\n    </VisuallyHidden>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,SAAS,0CAAc,KAAyB;IACrD,IAAI,EAAA,WAAC,SAAS,EAAE,GAAG,YAAW,GAAG;IACjC,IAAI,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,sKAAA,CAAA,UAAA,CAAW,GAAG;IAEhE,IAAI,SAAS,CAAA,qKAAA,YAAQ,EAAE,YAAY,gBAAgB,MAAM,CAAC;IAE1D,IAAI,UAAU;QACZ,IAAI,WACF;IAEJ;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,uLAAA,iBAAa,GAAA,MAAA,WAAA,GACZ,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,UAAA;QACE,GAAG,MAAM;QACV,UAAU,CAAA;QACV,SAAS;QACT,OAAO;YAAC,OAAO;YAAG,QAAQ;QAAC;;AAGnC", "debugId": null}}, {"offset": {"line": 4041, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/label/dist/useLabel.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/label/dist/packages/%40react-aria/label/src/useLabel.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMAttributes, DOMProps, LabelableProps} from '@react-types/shared';\nimport {ElementType, LabelHTMLAttributes} from 'react';\nimport {useId, useLabels} from '@react-aria/utils';\n\nexport interface LabelAriaProps extends LabelableProps, DOMProps, AriaLabelingProps {\n  /**\n   * The HTML element used to render the label, e.g. 'label', or 'span'.\n   * @default 'label'\n   */\n  labelElementType?: ElementType\n}\n\nexport interface LabelAria {\n  /** Props to apply to the label container element. */\n  labelProps: DOMAttributes | LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props to apply to the field container element being labeled. */\n  fieldProps: AriaLabelingProps & DOMProps\n}\n\n/**\n * Provides the accessibility implementation for labels and their associated elements.\n * Labels provide context for user inputs.\n * @param props - The props for labels and fields.\n */\nexport function useLabel(props: LabelAriaProps): LabelAria {\n  let {\n    id,\n    label,\n    'aria-labelledby': ariaLabelledby,\n    'aria-label': ariaLabel,\n    labelElementType = 'label'\n  } = props;\n\n  id = useId(id);\n  let labelId = useId();\n  let labelProps = {};\n  if (label) {\n    ariaLabelledby = ariaLabelledby ? `${labelId} ${ariaLabelledby}` : labelId;\n    labelProps = {\n      id: labelId,\n      htmlFor: labelElementType === 'label' ? id : undefined\n    };\n  } else if (!ariaLabelledby && !ariaLabel && process.env.NODE_ENV !== 'production') {\n    console.warn('If you do not provide a visible label, you must specify an aria-label or aria-labelledby attribute for accessibility');\n  }\n\n  let fieldProps = useLabels({\n    id,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby\n  });\n\n  return {\n    labelProps,\n    fieldProps\n  };\n}\n"], "names": [], "mappings": ";;;AAsD8C,QAAQ,GAAG,CAAC,QAAQ;;;;AAtDlE;;;;;;;;;;CAUC,GA0BM,SAAS,0CAAS,KAAqB;IAC5C,IAAI,EAAA,IACF,EAAE,EAAA,OACF,KAAK,EACL,mBAAmB,cAAc,EACjC,cAAc,SAAS,EAAA,kBACvB,mBAAmB,OAAA,EACpB,GAAG;IAEJ,KAAK,CAAA,iKAAA,QAAI,EAAE;IACX,IAAI,UAAU,CAAA,iKAAA,QAAI;IAClB,IAAI,aAAa,CAAC;IAClB,IAAI,OAAO;QACT,iBAAiB,iBAAiB,UAAG,SAAQ,CAAC,IAAE,CAAgB,MAAhB,kBAAmB;QACnE,aAAa;YACX,IAAI;YACJ,SAAS,qBAAqB,UAAU,KAAK;QAC/C;IACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,iEAAsC,cACnE,QAAQ,IAAI,CAAC;IAGf,IAAI,aAAa,CAAA,qKAAA,YAAQ,EAAE;YACzB;QACA,cAAc;QACd,mBAAmB;IACrB;IAEA,OAAO;oBACL;oBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4086, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/label/dist/useField.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/label/dist/packages/%40react-aria/label/src/useField.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, HelpTextProps, Validation} from '@react-types/shared';\nimport {LabelAria, LabelAriaProps, useLabel} from './useLabel';\nimport {mergeProps, useSlotId} from '@react-aria/utils';\n\nexport interface AriaFieldProps extends LabelAriaProps, HelpTextProps, Omit<Validation<any>, 'isRequired'> {}\n\nexport interface FieldAria extends LabelAria {\n  /** Props for the description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for input fields.\n * Fields accept user input, gain context from their label, and may display a description or error message.\n * @param props - Props for the Field.\n */\nexport function useField(props: AriaFieldProps): FieldAria {\n  let {description, errorMessage, isInvalid, validationState} = props;\n  let {labelProps, fieldProps} = useLabel(props);\n\n  let descriptionId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n  let errorMessageId = useSlotId([Boolean(description), Boolean(errorMessage), isInvalid, validationState]);\n\n  fieldProps = mergeProps(fieldProps, {\n    'aria-describedby': [\n      descriptionId,\n      // Use aria-describedby for error message because aria-errormessage is unsupported using VoiceOver or NVDA. See https://github.com/adobe/react-spectrum/issues/1346#issuecomment-740136268\n      errorMessageId,\n      props['aria-describedby']\n    ].filter(Boolean).join(' ') || undefined\n  });\n\n  return {\n    labelProps,\n    fieldProps,\n    descriptionProps: {\n      id: descriptionId\n    },\n    errorMessageProps: {\n      id: errorMessageId\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GAoBM,SAAS,0CAAS,KAAqB;IAC5C,IAAI,EAAA,aAAC,WAAW,EAAA,cAAE,YAAY,EAAA,WAAE,SAAS,EAAA,iBAAE,eAAe,EAAC,GAAG;IAC9D,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,oKAAA,WAAO,EAAE;IAExC,IAAI,gBAAgB,CAAA,iKAAA,YAAQ,EAAE;QAAC,QAAQ;QAAc,QAAQ;QAAe;QAAW;KAAgB;IACvG,IAAI,iBAAiB,CAAA,iKAAA,YAAQ,EAAE;QAAC,QAAQ;QAAc,QAAQ;QAAe;QAAW;KAAgB;IAExG,aAAa,CAAA,sKAAA,aAAS,EAAE,YAAY;QAClC,oBAAoB;YAClB;YACA,0LAA0L;YAC1L;YACA,KAAK,CAAC,mBAAmB;SAC1B,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;IACjC;IAEA,OAAO;oBACL;oBACA;QACA,kBAAkB;YAChB,IAAI;QACN;QACA,mBAAmB;YACjB,IAAI;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 4144, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/form/dist/useFormValidation.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/form/dist/packages/%40react-aria/form/src/useFormValidation.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FormValidationState} from '@react-stately/form';\nimport {RefObject, Validation, ValidationResult} from '@react-types/shared';\nimport {setInteractionModality} from '@react-aria/interactions';\nimport {useEffect, useRef} from 'react';\nimport {useEffectEvent, useLayoutEffect} from '@react-aria/utils';\n\ntype ValidatableElement = HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;\n\ninterface FormValidationProps<T> extends Validation<T> {\n  focus?: () => void\n}\n\nexport function useFormValidation<T>(props: FormValidationProps<T>, state: FormValidationState, ref: RefObject<ValidatableElement | null> | undefined): void {\n  let {validationBehavior, focus} = props;\n\n  // This is a useLayoutEffect so that it runs before the useEffect in useFormValidationState, which commits the validation change.\n  useLayoutEffect(() => {\n    if (validationBehavior === 'native' && ref?.current && !ref.current.disabled) {\n      let errorMessage = state.realtimeValidation.isInvalid ? state.realtimeValidation.validationErrors.join(' ') || 'Invalid value.' : '';\n      ref.current.setCustomValidity(errorMessage);\n\n      // Prevent default tooltip for validation message.\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=605277\n      if (!ref.current.hasAttribute('title')) {\n        ref.current.title = '';\n      }\n\n      if (!state.realtimeValidation.isInvalid) {\n        state.updateValidation(getNativeValidity(ref.current));\n      }\n    }\n  });\n\n  let isIgnoredReset = useRef(false);\n  let onReset = useEffectEvent(() => {\n    if (!isIgnoredReset.current) {\n      state.resetValidation();\n    }\n  });\n\n  let onInvalid = useEffectEvent((e: Event) => {\n    // Only commit validation if we are not already displaying one.\n    // This avoids clearing server errors that the user didn't actually fix.\n    if (!state.displayValidation.isInvalid) {\n      state.commitValidation();\n    }\n\n    // Auto focus the first invalid input in a form, unless the error already had its default prevented.\n    let form = ref?.current?.form;\n    if (!e.defaultPrevented && ref && form && getFirstInvalidInput(form) === ref.current) {\n      if (focus) {\n        focus();\n      } else {\n        ref.current?.focus();\n      }\n\n      // Always show focus ring.\n      setInteractionModality('keyboard');\n    }\n\n    // Prevent default browser error UI from appearing.\n    e.preventDefault();\n  });\n\n  let onChange = useEffectEvent(() => {\n    state.commitValidation();\n  });\n\n  useEffect(() => {\n    let input = ref?.current;\n    if (!input) {\n      return;\n    }\n\n    let form = input.form;\n\n    let reset = form?.reset;\n    if (form) {\n      // Try to detect React's automatic form reset behavior so we don't clear\n      // validation errors that are returned by server actions.\n      // To do this, we ignore programmatic form resets that occur outside a user event.\n      // This is best-effort. There may be false positives, e.g. setTimeout.\n      form.reset = () => {\n        // React uses MessageChannel for scheduling, so ignore 'message' events.\n        isIgnoredReset.current = !window.event || (window.event.type === 'message' && window.event.target instanceof MessagePort);\n        reset?.call(form);\n        isIgnoredReset.current = false;\n      };\n    }\n\n    input.addEventListener('invalid', onInvalid);\n    input.addEventListener('change', onChange);\n    form?.addEventListener('reset', onReset);\n    return () => {\n      input!.removeEventListener('invalid', onInvalid);\n      input!.removeEventListener('change', onChange);\n      form?.removeEventListener('reset', onReset);\n      if (form) {\n        // @ts-ignore\n        form.reset = reset;\n      }\n    };\n  }, [ref, onInvalid, onChange, onReset, validationBehavior]);\n}\n\nfunction getValidity(input: ValidatableElement) {\n  // The native ValidityState object is live, meaning each property is a getter that returns the current state.\n  // We need to create a snapshot of the validity state at the time this function is called to avoid unpredictable React renders.\n  let validity = input.validity;\n  return {\n    badInput: validity.badInput,\n    customError: validity.customError,\n    patternMismatch: validity.patternMismatch,\n    rangeOverflow: validity.rangeOverflow,\n    rangeUnderflow: validity.rangeUnderflow,\n    stepMismatch: validity.stepMismatch,\n    tooLong: validity.tooLong,\n    tooShort: validity.tooShort,\n    typeMismatch: validity.typeMismatch,\n    valueMissing: validity.valueMissing,\n    valid: validity.valid\n  };\n}\n\nfunction getNativeValidity(input: ValidatableElement): ValidationResult {\n  return {\n    isInvalid: !input.validity.valid,\n    validationDetails: getValidity(input),\n    validationErrors: input.validationMessage ? [input.validationMessage] : []\n  };\n}\n\nfunction getFirstInvalidInput(form: HTMLFormElement): ValidatableElement | null {\n  for (let i = 0; i < form.elements.length; i++) {\n    let element = form.elements[i] as ValidatableElement;\n    if (!element.validity.valid) {\n      return element;\n    }\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAcM,SAAS,0CAAqB,KAA6B,EAAE,KAA0B,EAAE,GAAqD;IACnJ,IAAI,EAAA,oBAAC,kBAAkB,EAAA,OAAE,KAAK,EAAC,GAAG;IAElC,iIAAiI;IACjI,CAAA,2KAAA,kBAAc,EAAE;QACd,IAAI,uBAAuB,YAAA,CAAY,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,OAAO,KAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAC5E,IAAI,eAAe,MAAM,kBAAkB,CAAC,SAAS,GAAG,MAAM,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,mBAAmB;YAClI,IAAI,OAAO,CAAC,iBAAiB,CAAC;YAE9B,kDAAkD;YAClD,sDAAsD;YACtD,IAAI,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,UAC5B,IAAI,OAAO,CAAC,KAAK,GAAG;YAGtB,IAAI,CAAC,MAAM,kBAAkB,CAAC,SAAS,EACrC,MAAM,gBAAgB,CAAC,wCAAkB,IAAI,OAAO;QAExD;IACF;IAEA,IAAI,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC5B,IAAI,UAAU,CAAA,0KAAA,iBAAa,EAAE;QAC3B,IAAI,CAAC,eAAe,OAAO,EACzB,MAAM,eAAe;IAEzB;IAEA,IAAI,YAAY,CAAA,0KAAA,iBAAa,EAAE,CAAC;YAQnB;QAPX,+DAA+D;QAC/D,wEAAwE;QACxE,IAAI,CAAC,MAAM,iBAAiB,CAAC,SAAS,EACpC,MAAM,gBAAgB;QAGxB,oGAAoG;QACpG,IAAI,OAAO,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,CAAA,eAAA,IAAK,OAAO,MAAA,QAAZ,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAc,IAAI;QAC7B,IAAI,CAAC,EAAE,gBAAgB,IAAI,OAAO,QAAQ,2CAAqB,UAAU,IAAI,OAAO,EAAE;gBAIlF;YAHF,IAAI,OACF;kBAEA,gBAAA,IAAI,OAAO,MAAA,QAAX,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAa,KAAK;YAGpB,0BAA0B;YAC1B,CAAA,kLAAA,yBAAqB,EAAE;QACzB;QAEA,mDAAmD;QACnD,EAAE,cAAc;IAClB;IAEA,IAAI,WAAW,CAAA,0KAAA,iBAAa,EAAE;QAC5B,MAAM,gBAAgB;IACxB;IAEA,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QAAQ,QAAA,QAAA,QAAA,KAAA,IAAA,KAAA,IAAA,IAAK,OAAO;QACxB,IAAI,CAAC,OACH;QAGF,IAAI,OAAO,MAAM,IAAI;QAErB,IAAI,QAAQ,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;QACvB,IAAI,MACF,AACA,yDAAyD,eADe;QAExE,kFAAkF;QAClF,sEAAsE;QACtE,KAAK,KAAK,GAAG;YACX,wEAAwE;YACxE,eAAe,OAAO,GAAG,CAAC,OAAO,KAAK,IAAK,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,YAAY;YAC7G,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI,CAAC;YACZ,eAAe,OAAO,GAAG;QAC3B;QAGF,MAAM,gBAAgB,CAAC,WAAW;QAClC,MAAM,gBAAgB,CAAC,UAAU;QACjC,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,gBAAgB,CAAC,SAAS;QAChC,OAAO;YACL,MAAO,mBAAmB,CAAC,WAAW;YACtC,MAAO,mBAAmB,CAAC,UAAU;YACrC,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,mBAAmB,CAAC,SAAS;YACnC,IAAI,MACF,AACA,KAAK,KAAK,GADG,AACA;QAEjB;IACF,GAAG;QAAC;QAAK;QAAW;QAAU;QAAS;KAAmB;AAC5D;AAEA,SAAS,kCAAY,KAAyB;IAC5C,6GAA6G;IAC7G,+HAA+H;IAC/H,IAAI,WAAW,MAAM,QAAQ;IAC7B,OAAO;QACL,UAAU,SAAS,QAAQ;QAC3B,aAAa,SAAS,WAAW;QACjC,iBAAiB,SAAS,eAAe;QACzC,eAAe,SAAS,aAAa;QACrC,gBAAgB,SAAS,cAAc;QACvC,cAAc,SAAS,YAAY;QACnC,SAAS,SAAS,OAAO;QACzB,UAAU,SAAS,QAAQ;QAC3B,cAAc,SAAS,YAAY;QACnC,cAAc,SAAS,YAAY;QACnC,OAAO,SAAS,KAAK;IACvB;AACF;AAEA,SAAS,wCAAkB,KAAyB;IAClD,OAAO;QACL,WAAW,CAAC,MAAM,QAAQ,CAAC,KAAK;QAChC,mBAAmB,kCAAY;QAC/B,kBAAkB,MAAM,iBAAiB,GAAG;YAAC,MAAM,iBAAiB;SAAC,GAAG,EAAE;IAC5E;AACF;AAEA,SAAS,2CAAqB,IAAqB;IACjD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,IAAK;QAC7C,IAAI,UAAU,KAAK,QAAQ,CAAC,EAAE;QAC9B,IAAI,CAAC,QAAQ,QAAQ,CAAC,KAAK,EACzB,OAAO;IAEX;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4272, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/textfield/dist/useTextField.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/textfield/dist/packages/%40react-aria/textfield/src/useTextField.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTextFieldProps} from '@react-types/textfield';\nimport {DOMAttributes, ValidationResult} from '@react-types/shared';\nimport {filterDOMProps, getOwnerWindow, mergeProps, useFormReset} from '@react-aria/utils';\nimport React, {\n  ChangeEvent,\n  HTMLAttributes,\n  type JSX,\n  LabelHTMLAttributes,\n  RefObject,\n  useEffect,\n  useState\n} from 'react';\nimport {useControlledState} from '@react-stately/utils';\nimport {useField} from '@react-aria/label';\nimport {useFocusable} from '@react-aria/interactions';\nimport {useFormValidation} from '@react-aria/form';\nimport {useFormValidationState} from '@react-stately/form';\n\n/**\n * A map of HTML element names and their interface types.\n * For example `'a'` -> `HTMLAnchorElement`.\n */\ntype IntrinsicHTMLElements = {\n  [K in keyof IntrinsicHTMLAttributes]: IntrinsicHTMLAttributes[K] extends HTMLAttributes<infer T> ? T : never\n};\n\n/**\n * A map of HTML element names and their attribute interface types.\n * For example `'a'` -> `AnchorHTMLAttributes<HTMLAnchorElement>`.\n */\ntype IntrinsicHTMLAttributes = JSX.IntrinsicElements;\n\ntype DefaultElementType = 'input';\n\n/**\n * The intrinsic HTML element names that `useTextField` supports; e.g. `input`,\n * `textarea`.\n */\ntype TextFieldIntrinsicElements = keyof Pick<IntrinsicHTMLElements, 'input' | 'textarea'>;\n\n/**\n * The HTML element interfaces that `useTextField` supports based on what is\n * defined for `TextFieldIntrinsicElements`; e.g. `HTMLInputElement`,\n * `HTMLTextAreaElement`.\n */\ntype TextFieldHTMLElementType = Pick<IntrinsicHTMLElements, TextFieldIntrinsicElements>;\n\n/**\n * The HTML attributes interfaces that `useTextField` supports based on what\n * is defined for `TextFieldIntrinsicElements`; e.g. `InputHTMLAttributes`,\n * `TextareaHTMLAttributes`.\n */\ntype TextFieldHTMLAttributesType = Pick<IntrinsicHTMLAttributes, TextFieldIntrinsicElements>;\n\n/**\n * The type of `inputProps` returned by `useTextField`; e.g. `InputHTMLAttributes`,\n * `TextareaHTMLAttributes`.\n */\ntype TextFieldInputProps<T extends TextFieldIntrinsicElements> = TextFieldHTMLAttributesType[T];\n\nexport interface AriaTextFieldOptions<T extends TextFieldIntrinsicElements> extends AriaTextFieldProps<TextFieldHTMLElementType[T]> {\n  /**\n   * The HTML element used to render the input, e.g. 'input', or 'textarea'.\n   * It determines whether certain HTML attributes will be included in `inputProps`.\n   * For example, [`type`](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-type).\n   * @default 'input'\n   */\n  inputElementType?: T,\n  /**\n   * Controls whether inputted text is automatically capitalized and, if so, in what manner.\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/autocapitalize).\n   */\n  autoCapitalize?: 'off' | 'none' | 'on' | 'sentences' | 'words' | 'characters',\n  /**\n   * An enumerated attribute that defines what action label or icon to preset for the enter key on virtual keyboards. See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/enterkeyhint).\n   */\n  enterKeyHint?: 'enter' | 'done' | 'go' | 'next' | 'previous' | 'search' | 'send'\n}\n\n/**\n * The type of `ref` object that can be passed to `useTextField` based on the given\n * intrinsic HTML element name; e.g.`RefObject<HTMLInputElement>`,\n * `RefObject<HTMLTextAreaElement>`.\n */\ntype TextFieldRefObject<T extends TextFieldIntrinsicElements> = RefObject<TextFieldHTMLElementType[T] | null>;\n\nexport interface TextFieldAria<T extends TextFieldIntrinsicElements = DefaultElementType> extends ValidationResult {\n  /** Props for the input element. */\n  inputProps: TextFieldInputProps<T>,\n  /** Props for the text field's visible label element, if any. */\n  labelProps: DOMAttributes | LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the text field's description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the text field's error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a text field.\n * @param props - Props for the text field.\n * @param ref - Ref to the HTML input or textarea element.\n */\nexport function useTextField<T extends TextFieldIntrinsicElements = DefaultElementType>(\n  props: AriaTextFieldOptions<T>,\n  ref: TextFieldRefObject<T>\n): TextFieldAria<T> {\n  let {\n    inputElementType = 'input',\n    isDisabled = false,\n    isRequired = false,\n    isReadOnly = false,\n    type = 'text',\n    validationBehavior = 'aria'\n  } = props;\n  let [value, setValue] = useControlledState<string>(props.value, props.defaultValue || '', props.onChange);\n  let {focusableProps} = useFocusable<TextFieldHTMLElementType[T]>(props, ref);\n  let validationState = useFormValidationState({\n    ...props,\n    value\n  });\n  let {isInvalid, validationErrors, validationDetails} = validationState.displayValidation;\n  let {labelProps, fieldProps, descriptionProps, errorMessageProps} = useField({\n    ...props,\n    isInvalid,\n    errorMessage: props.errorMessage || validationErrors\n  });\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  const inputOnlyProps = {\n    type,\n    pattern: props.pattern\n  };\n\n  let [initialValue] = useState(value);\n  useFormReset(ref, props.defaultValue ?? initialValue, setValue);\n  useFormValidation(props, validationState, ref);\n\n  useEffect(() => {\n    // This works around a React/Chrome bug that prevents textarea elements from validating when controlled.\n    // We prevent React from updating defaultValue (i.e. children) of textarea when `value` changes,\n    // which causes Chrome to skip validation. Only updating `value` is ok in our case since our\n    // textareas are always controlled. React is planning on removing this synchronization in a\n    // future major version.\n    // https://github.com/facebook/react/issues/19474\n    // https://github.com/facebook/react/issues/11896\n    if (ref.current instanceof getOwnerWindow(ref.current).HTMLTextAreaElement) {\n      let input = ref.current;\n      Object.defineProperty(input, 'defaultValue', {\n        get: () => input.value,\n        set: () => {},\n        configurable: true\n      });\n    }\n  }, [ref]);\n\n  return {\n    labelProps,\n    inputProps: mergeProps(\n      domProps,\n      inputElementType === 'input' ? inputOnlyProps : undefined,\n      {\n        disabled: isDisabled,\n        readOnly: isReadOnly,\n        required: isRequired && validationBehavior === 'native',\n        'aria-required': (isRequired && validationBehavior === 'aria') || undefined,\n        'aria-invalid': isInvalid || undefined,\n        'aria-errormessage': props['aria-errormessage'],\n        'aria-activedescendant': props['aria-activedescendant'],\n        'aria-autocomplete': props['aria-autocomplete'],\n        'aria-haspopup': props['aria-haspopup'],\n        'aria-controls': props['aria-controls'],\n        value,\n        onChange: (e: ChangeEvent<HTMLInputElement>) => setValue(e.target.value),\n        autoComplete: props.autoComplete,\n        autoCapitalize: props.autoCapitalize,\n        maxLength: props.maxLength,\n        minLength: props.minLength,\n        name: props.name,\n        form: props.form,\n        placeholder: props.placeholder,\n        inputMode: props.inputMode,\n        autoCorrect: props.autoCorrect,\n        spellCheck: props.spellCheck,\n        [parseInt(React.version, 10) >= 17 ? 'enterKeyHint' : 'enterkeyhint']: props.enterKeyHint,\n\n        // Clipboard events\n        onCopy: props.onCopy,\n        onCut: props.onCut,\n        onPaste: props.onPaste,\n\n        // Composition events\n        onCompositionEnd: props.onCompositionEnd,\n        onCompositionStart: props.onCompositionStart,\n        onCompositionUpdate: props.onCompositionUpdate,\n\n        // Selection events\n        onSelect: props.onSelect,\n\n        // Input events\n        onBeforeInput: props.onBeforeInput,\n        onInput: props.onInput,\n        ...focusableProps,\n        ...fieldProps\n      }\n    ),\n    descriptionProps,\n    errorMessageProps,\n    isInvalid,\n    validationErrors,\n    validationDetails\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAwGM,SAAS,yCACd,KAA8B,EAC9B,GAA0B;IAE1B,IAAI,EAAA,kBACF,mBAAmB,OAAA,EAAA,YACnB,aAAa,KAAA,EAAA,YACb,aAAa,KAAA,EAAA,YACb,aAAa,KAAA,EAAA,MACb,OAAO,MAAA,EAAA,oBACP,qBAAqB,MAAA,EACtB,GAAG;IACJ,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,iLAAA,qBAAiB,EAAU,MAAM,KAAK,EAAE,MAAM,YAAY,IAAI,IAAI,MAAM,QAAQ;IACxG,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,+KAAA,eAAW,EAA+B,OAAO;IACxE,IAAI,kBAAkB,CAAA,oLAAA,yBAAqB,EAAE;QAC3C,GAAG,KAAK;eACR;IACF;IACA,IAAI,EAAA,WAAC,SAAS,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAC,GAAG,gBAAgB,iBAAiB;IACxF,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAC,GAAG,CAAA,oKAAA,WAAO,EAAE;QAC3E,GAAG,KAAK;mBACR;QACA,cAAc,MAAM,YAAY,IAAI;IACtC;IACA,IAAI,WAAW,CAAA,0KAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,MAAM,iBAAiB;cACrB;QACA,SAAS,MAAM,OAAO;IACxB;IAEA,IAAI,CAAC,aAAa,GAAG,CAAA,iKAAA,WAAO,EAAE;QACZ;IAAlB,CAAA,wKAAA,eAAW,EAAE,KAAK,CAAA,sBAAA,MAAM,YAAY,MAAA,QAAlB,wBAAA,KAAA,IAAA,sBAAsB,cAAc;IACtD,CAAA,4KAAA,oBAAgB,EAAE,OAAO,iBAAiB;IAE1C,CAAA,iKAAA,YAAQ,EAAE;QACR,wGAAwG;QACxG,gGAAgG;QAChG,4FAA4F;QAC5F,2FAA2F;QAC3F,wBAAwB;QACxB,iDAAiD;QACjD,iDAAiD;QACjD,IAAI,IAAI,OAAO,YAAY,CAAA,sKAAA,iBAAa,EAAE,IAAI,OAAO,EAAE,mBAAmB,EAAE;YAC1E,IAAI,QAAQ,IAAI,OAAO;YACvB,OAAO,cAAc,CAAC,OAAO,gBAAgB;gBAC3C,KAAK,IAAM,MAAM,KAAK;gBACtB,KAAK,KAAO;gBACZ,cAAc;YAChB;QACF;IACF,GAAG;QAAC;KAAI;IAER,OAAO;oBACL;QACA,YAAY,CAAA,sKAAA,aAAS,EACnB,UACA,qBAAqB,UAAU,iBAAiB,WAChD;YACE,UAAU;YACV,UAAU;YACV,UAAU,cAAc,uBAAuB;YAC/C,iBAAkB,cAAc,uBAAuB,UAAW;YAClE,gBAAgB,aAAa;YAC7B,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,yBAAyB,KAAK,CAAC,wBAAwB;YACvD,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,iBAAiB,KAAK,CAAC,gBAAgB;YACvC,iBAAiB,KAAK,CAAC,gBAAgB;mBACvC;YACA,UAAU,CAAC,IAAqC,SAAS,EAAE,MAAM,CAAC,KAAK;YACvE,cAAc,MAAM,YAAY;YAChC,gBAAgB,MAAM,cAAc;YACpC,WAAW,MAAM,SAAS;YAC1B,WAAW,MAAM,SAAS;YAC1B,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;YAC9B,WAAW,MAAM,SAAS;YAC1B,aAAa,MAAM,WAAW;YAC9B,YAAY,MAAM,UAAU;YAC5B,CAAC,SAAS,CAAA,iKAAA,UAAI,EAAE,OAAO,EAAE,OAAO,KAAK,iBAAiB,eAAe,EAAE,MAAM,YAAY;YAEzF,mBAAmB;YACnB,QAAQ,MAAM,MAAM;YACpB,OAAO,MAAM,KAAK;YAClB,SAAS,MAAM,OAAO;YAEtB,qBAAqB;YACrB,kBAAkB,MAAM,gBAAgB;YACxC,oBAAoB,MAAM,kBAAkB;YAC5C,qBAAqB,MAAM,mBAAmB;YAE9C,mBAAmB;YACnB,UAAU,MAAM,QAAQ;YAExB,eAAe;YACf,eAAe,MAAM,aAAa;YAClC,SAAS,MAAM,OAAO;YACtB,GAAG,cAAc;YACjB,GAAG,UAAU;QACf;0BAEF;2BACA;mBACA;0BACA;2BACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4401, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/toggle/dist/useToggle.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/toggle/dist/packages/%40react-aria/toggle/src/useToggle.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaToggleProps} from '@react-types/checkbox';\nimport {filterDOMProps, mergeProps, useFormReset} from '@react-aria/utils';\nimport {InputHTMLAttributes, LabelHTMLAttributes} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {ToggleState} from '@react-stately/toggle';\nimport {useFocusable, usePress} from '@react-aria/interactions';\n\nexport interface ToggleAria {\n  /** Props to be spread on the label element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props to be spread on the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Whether the toggle is selected. */\n  isSelected: boolean,\n  /** Whether the toggle is in a pressed state. */\n  isPressed: boolean,\n  /** Whether the toggle is disabled. */\n  isDisabled: boolean,\n  /** Whether the toggle is read only. */\n  isReadOnly: boolean,\n  /** Whether the toggle is invalid. */\n  isInvalid: boolean\n}\n\n/**\n * Handles interactions for toggle elements, e.g. Checkboxes and Switches.\n */\nexport function useToggle(props: AriaToggleProps, state: ToggleState, ref: RefObject<HTMLInputElement | null>): ToggleAria {\n  let {\n    isDisabled = false,\n    isReadOnly = false,\n    value,\n    name,\n    form,\n    children,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    validationState = 'valid',\n    isInvalid,\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick\n  } = props;\n\n  let onChange = (e) => {\n    // since we spread props on label, onChange will end up there as well as in here.\n    // so we have to stop propagation at the lowest level that we care about\n    e.stopPropagation();\n    state.setSelected(e.target.checked);\n  };\n\n  let hasChildren = children != null;\n  let hasAriaLabel = ariaLabel != null || ariaLabelledby != null;\n  if (!hasChildren && !hasAriaLabel && process.env.NODE_ENV !== 'production') {\n    console.warn('If you do not provide children, you must specify an aria-label for accessibility');\n  }\n\n  // Handle press state for keyboard interactions and cases where labelProps is not used.\n  let {pressProps, isPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick,\n    isDisabled\n  });\n\n  // Handle press state on the label.\n  let {pressProps: labelProps, isPressed: isLabelPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPressUp,\n    onClick,\n    onPress(e) {\n      onPress?.(e);\n      state.toggle();\n      ref.current?.focus();\n    },\n    isDisabled: isDisabled || isReadOnly\n  });\n\n  let {focusableProps} = useFocusable(props, ref);\n  let interactions = mergeProps(pressProps, focusableProps);\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  useFormReset(ref, state.defaultSelected, state.setSelected);\n\n  return {\n    labelProps: mergeProps(labelProps, {onClick: e => e.preventDefault()}),\n    inputProps: mergeProps(domProps, {\n      'aria-invalid': isInvalid || validationState === 'invalid' || undefined,\n      'aria-errormessage': props['aria-errormessage'],\n      'aria-controls': props['aria-controls'],\n      'aria-readonly': isReadOnly || undefined,\n      onChange,\n      disabled: isDisabled,\n      ...(value == null ? {} : {value}),\n      name,\n      form,\n      type: 'checkbox',\n      ...interactions\n    }),\n    isSelected: state.isSelected,\n    isPressed: isPressed || isLabelPressed,\n    isDisabled,\n    isReadOnly,\n    isInvalid: isInvalid || validationState === 'invalid'\n  };\n}\n"], "names": [], "mappings": ";;;AAoEuC,QAAQ,GAAG,CAAC,QAAQ;;;;;;;;AApE3D;;;;;;;;;;CAUC,GA6BM,SAAS,0CAAU,KAAsB,EAAE,KAAkB,EAAE,GAAuC;IAC3G,IAAI,EAAA,YACF,aAAa,KAAA,EAAA,YACb,aAAa,KAAA,EAAA,OACb,KAAK,EAAA,MACL,IAAI,EAAA,MACJ,IAAI,EAAA,UACJ,QAAQ,EACR,cAAc,SAAS,EACvB,mBAAmB,cAAc,EAAA,iBACjC,kBAAkB,OAAA,EAAA,WAClB,SAAS,EAAA,cACT,YAAY,EAAA,YACZ,UAAU,EAAA,eACV,aAAa,EAAA,SACb,OAAO,EAAA,WACP,SAAS,EAAA,SACT,OAAO,EACR,GAAG;IAEJ,IAAI,WAAW,CAAC;QACd,iFAAiF;QACjF,wEAAwE;QACxE,EAAE,eAAe;QACjB,MAAM,WAAW,CAAC,EAAE,MAAM,CAAC,OAAO;IACpC;IAEA,IAAI,cAAc,YAAY;IAC9B,IAAI,eAAe,aAAa,QAAQ,kBAAkB;IAC1D,IAAI,CAAC,eAAe,CAAC,oEAAyC,cAC5D,QAAQ,IAAI,CAAC;IAGf,uFAAuF;IACvF,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;sBACrC;oBACA;uBACA;iBACA;mBACA;iBACA;oBACA;IACF;IAEA,mCAAmC;IACnC,IAAI,EAAC,YAAY,UAAU,EAAE,WAAW,cAAc,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;sBACjE;oBACA;uBACA;mBACA;iBACA;QACA,SAAQ,CAAC;gBAGP;YAFA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAU;YACV,MAAM,MAAM;aACZ,eAAA,IAAI,OAAO,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,KAAK;QACpB;QACA,YAAY,cAAc;IAC5B;IAEA,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,2LAAW,EAAE,OAAO;IAC3C,IAAI,eAAe,CAAA,sKAAA,aAAS,EAAE,YAAY;IAC1C,IAAI,WAAW,CAAA,GAAA,wLAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,CAAA,wKAAA,eAAW,EAAE,KAAK,MAAM,eAAe,EAAE,MAAM,WAAW;IAE1D,OAAO;QACL,YAAY,CAAA,sKAAA,aAAS,EAAE,YAAY;YAAC,SAAS,CAAA,IAAK,EAAE,cAAc;QAAE;QACpE,YAAY,CAAA,sKAAA,aAAS,EAAE,UAAU;YAC/B,gBAAgB,aAAa,oBAAoB,aAAa;YAC9D,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,iBAAiB,KAAK,CAAC,gBAAgB;YACvC,iBAAiB,cAAc;sBAC/B;YACA,UAAU;YACV,GAAI,SAAS,OAAO,CAAC,IAAI;uBAAC;YAAK,CAAC;kBAChC;kBACA;YACA,MAAM;YACN,GAAG,YAAY;QACjB;QACA,YAAY,MAAM,UAAU;QAC5B,WAAW,aAAa;oBACxB;oBACA;QACA,WAAW,aAAa,oBAAoB;IAC9C;AACF", "debugId": null}}, {"offset": {"line": 4496, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/switch/dist/useSwitch.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/switch/dist/packages/%40react-aria/switch/src/useSwitch.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaSwitchProps} from '@react-types/switch';\nimport {InputHTMLAttributes, LabelHTMLAttributes} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {ToggleState} from '@react-stately/toggle';\nimport {useToggle} from '@react-aria/toggle';\n\nexport interface SwitchAria {\n  /** Props for the label wrapper element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Whether the switch is selected. */\n  isSelected: boolean,\n  /** Whether the switch is in a pressed state. */\n  isPressed: boolean,\n  /** Whether the switch is disabled. */\n  isDisabled: boolean,\n  /** Whether the switch is read only. */\n  isReadOnly: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a switch component.\n * A switch is similar to a checkbox, but represents on/off values as opposed to selection.\n * @param props - Props for the switch.\n * @param state - State for the switch, as returned by `useToggleState`.\n * @param ref - Ref to the HTML input element.\n */\nexport function useSwitch(props: AriaSwitchProps, state: ToggleState, ref: RefObject<HTMLInputElement | null>): SwitchAria {\n  let {labelProps, inputProps, isSelected, isPressed, isDisabled, isReadOnly} = useToggle(props, state, ref);\n\n  return {\n    labelProps,\n    inputProps: {\n      ...inputProps,\n      role: 'switch',\n      checked: isSelected\n    },\n    isSelected,\n    isPressed,\n    isDisabled,\n    isReadOnly\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GA8BM,SAAS,0CAAU,KAAsB,EAAE,KAAkB,EAAE,GAAuC;IAC3G,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAA,YAAE,UAAU,EAAA,WAAE,SAAS,EAAA,YAAE,UAAU,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,sKAAA,YAAQ,EAAE,OAAO,OAAO;IAEtG,OAAO;oBACL;QACA,YAAY;YACV,GAAG,UAAU;YACb,MAAM;YACN,SAAS;QACX;oBACA;mBACA;oBACA;oBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4532, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/ar-AE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/ar-AE.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"اضغط مطولاً أو اضغط على Alt + السهم لأسفل لفتح القائمة\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,sSAAsD,CAAC;AAChG", "debugId": null}}, {"offset": {"line": 4545, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/bg-BG.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/bg-BG.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Натиснете продължително или натиснете Alt+ стрелка надолу, за да отворите менюто\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,sdAAgF,CAAC;AAC1H", "debugId": null}}, {"offset": {"line": 4558, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/cs-CZ.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/cs-CZ.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Dlouhým stiskem nebo stisknutím kláves Alt + šipka dolů otevřete nabídku\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,sGAAwE,CAAC;AAClH", "debugId": null}}, {"offset": {"line": 4571, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/da-DK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/da-DK.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"<PERSON>t tryk eller tryk på Alt + pil ned for at åbne menuen\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,+DAAyD,CAAC;AACnG", "debugId": null}}, {"offset": {"line": 4584, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/de-DE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/de-DE.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"<PERSON>ück<PERSON> Si<PERSON> lange oder drücken Sie Alt + Nach-unten, um das Menü zu öffnen\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,sFAA0E,CAAC;AACpH", "debugId": null}}, {"offset": {"line": 4597, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/el-GR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/el-GR.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Πιέστε παρατεταμένα ή πατήστε Alt + κάτω βέλος για να ανοίξετε το μενού\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,iZAAuE,CAAC;AACjH", "debugId": null}}, {"offset": {"line": 4610, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/en-US.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/en-US.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Long press or press Alt + ArrowDown to open menu\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,gDAAgD,CAAC;AAC1F", "debugId": null}}, {"offset": {"line": 4623, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/es-ES.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/es-ES.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Mantenga pulsado o pulse Alt + flecha abajo para abrir el menú\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,iEAA8D,CAAC;AACxG", "debugId": null}}, {"offset": {"line": 4636, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/et-EE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/et-EE.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Menüü avamiseks vajutage pikalt või vajutage klahve Alt + allanool\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,2EAAkE,CAAC;AAC5G", "debugId": null}}, {"offset": {"line": 4649, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/fi-FI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/fi-FI.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"<PERSON><PERSON> valik<PERSON> pohjassa tai näppäinyhdistelmällä Alt + <PERSON><PERSON><PERSON>\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,oFAAwE,CAAC;AAClH", "debugId": null}}, {"offset": {"line": 4662, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/fr-FR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/fr-FR.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Appuyez de manière prolongée ou appuyez sur Alt + Flèche vers le bas pour ouvrir le menu.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,wGAAyF,CAAC;AACnI", "debugId": null}}, {"offset": {"line": 4675, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/he-IL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/he-IL.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"לחץ לחיצה ארוכה או הקש Alt + ArrowDown כדי לפתוח את התפריט\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,sQAA0D,CAAC;AACpG", "debugId": null}}, {"offset": {"line": 4688, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/hr-HR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/hr-HR.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Dugo pritisnite ili pritisnite Alt + strelicu prema dolje za otvaranje izbornika\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,gFAAgF,CAAC;AAC1H", "debugId": null}}, {"offset": {"line": 4701, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/hu-HU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/hu-HU.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Nyo<PERSON>ja meg hosszan, vagy nyomja meg az Alt + lefele nyíl gombot a menü me<PERSON>itásához\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,gGAAoF,CAAC;AAC9H", "debugId": null}}, {"offset": {"line": 4714, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/it-IT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/it-IT.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Premere a lungo o premere Alt + Freccia giù per aprire il menu\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,iEAA8D,CAAC;AACxG", "debugId": null}}, {"offset": {"line": 4727, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/ja-JP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/ja-JP.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"長押しまたは Alt+下矢印キーでメニューを開く\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,6JAAwB,CAAC;AAClE", "debugId": null}}, {"offset": {"line": 4740, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/ko-KR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/ko-KR.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"길게 누르거나 Alt + 아래쪽 화살표를 눌러 메뉴 열기\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,oKAA+B,CAAC;AACzE", "debugId": null}}, {"offset": {"line": 4753, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/lt-LT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/lt-LT.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Norėdami atidaryti meniu, nuspaudę palaikykite arba paspauskite „Alt + ArrowDown“.\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,4GAAkF,CAAC;AAC5H", "debugId": null}}, {"offset": {"line": 4766, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/lv-LV.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/lv-LV.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Lai atvērtu izvēlni, turiet nospiestu vai nospiediet taustiņu kombināciju Alt + lejupvērstā bultiņa\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,6IAAmG,CAAC;AAC7I", "debugId": null}}, {"offset": {"line": 4779, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/nb-NO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/nb-NO.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Langt trykk eller trykk Alt + PilNed for å åpne menyen\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,4DAAsD,CAAC;AAChG", "debugId": null}}, {"offset": {"line": 4792, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/nl-NL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/nl-NL.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Druk lang op Alt + pijl-omlaag of druk op Alt om het menu te openen\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,mEAAmE,CAAC;AAC7G", "debugId": null}}, {"offset": {"line": 4805, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/pl-PL.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/pl-PL.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Naciśnij i przytrzymaj lub naciśnij klawisze Alt + Strzałka w dół, aby otworzyć menu\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,qHAAoF,CAAC;AAC9H", "debugId": null}}, {"offset": {"line": 4818, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/pt-BR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/pt-BR.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Pressione e segure ou pressione Alt + Seta para baixo para abrir o menu\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,uEAAuE,CAAC;AACjH", "debugId": null}}, {"offset": {"line": 4831, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/pt-PT.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/pt-PT.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Prima continuamente ou prima Alt + Seta Para Baixo para abrir o menu\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,oEAAoE,CAAC;AAC9G", "debugId": null}}, {"offset": {"line": 4844, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/ro-RO.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/ro-RO.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Apăsați lung sau apăsați pe Alt + săgeată în jos pentru a deschide meniul\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,gHAAyE,CAAC;AACnH", "debugId": null}}, {"offset": {"line": 4857, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/ru-RU.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/ru-RU.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Нажмите и удерживайте или нажмите Alt + Стрелка вниз, чтобы открыть меню\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,wZAAwE,CAAC;AAClH", "debugId": null}}, {"offset": {"line": 4870, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/sk-SK.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/sk-SK.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Ponuku otvoríte dlhým stlačením alebo stlačením klávesu Alt + klávesu so šípkou nadol\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,4HAAqF,CAAC;AAC/H", "debugId": null}}, {"offset": {"line": 4883, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/sl-SI.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/sl-SI.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Za odprtje menija pritisnite in držite gumb ali pritisnite Alt+puščica navzdol\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,gGAA8E,CAAC;AACxH", "debugId": null}}, {"offset": {"line": 4896, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/sr-SP.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/sr-SP.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Dugo pritisnite ili pritisnite Alt + strelicu prema dole da otvorite meni\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,yEAAyE,CAAC;AACnH", "debugId": null}}, {"offset": {"line": 4909, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/sv-SE.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/sv-SE.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"<PERSON><PERSON><PERSON> nedtryckt eller tryck på Alt + pil nedåt för att öppna menyn\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,gFAAiE,CAAC;AAC3G", "debugId": null}}, {"offset": {"line": 4922, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/tr-TR.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/tr-TR.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Menüyü açmak için uzun basın veya Alt + Aşağı Ok tuşuna basın\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,6GAA6D,CAAC;AACvG", "debugId": null}}, {"offset": {"line": 4935, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/uk-UA.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/uk-UA.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"Довго або звичайно натисніть комбінацію клавіш Alt і стрілка вниз, щоб відкрити меню\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,4eAAoF,CAAC;AAC9H", "debugId": null}}, {"offset": {"line": 4948, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/zh-CN.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/zh-CN.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"长按或按 Alt + 向下方向键以打开菜单\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,uHAAqB,CAAC;AAC/D", "debugId": null}}, {"offset": {"line": 4961, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/zh-TW.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/intl/zh-TW.json"], "sourcesContent": ["{\n  \"longPressMessage\": \"長按或按 Alt+向下鍵以開啟功能表\"\n}\n"], "names": [], "mappings": ";;;;AAAA,4BAAiB;IAAG,oBAAoB,AAAC,6GAAkB,CAAC;AAC5D", "debugId": null}}, {"offset": {"line": 4974, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/intlStrings.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/src/%2A.js"], "sourcesContent": ["const _temp0 = require(\"../intl/ar-AE.json\");\nconst _temp1 = require(\"../intl/bg-BG.json\");\nconst _temp2 = require(\"../intl/cs-CZ.json\");\nconst _temp3 = require(\"../intl/da-DK.json\");\nconst _temp4 = require(\"../intl/de-DE.json\");\nconst _temp5 = require(\"../intl/el-GR.json\");\nconst _temp6 = require(\"../intl/en-US.json\");\nconst _temp7 = require(\"../intl/es-ES.json\");\nconst _temp8 = require(\"../intl/et-EE.json\");\nconst _temp9 = require(\"../intl/fi-FI.json\");\nconst _temp10 = require(\"../intl/fr-FR.json\");\nconst _temp11 = require(\"../intl/he-IL.json\");\nconst _temp12 = require(\"../intl/hr-HR.json\");\nconst _temp13 = require(\"../intl/hu-HU.json\");\nconst _temp14 = require(\"../intl/it-IT.json\");\nconst _temp15 = require(\"../intl/ja-JP.json\");\nconst _temp16 = require(\"../intl/ko-KR.json\");\nconst _temp17 = require(\"../intl/lt-LT.json\");\nconst _temp18 = require(\"../intl/lv-LV.json\");\nconst _temp19 = require(\"../intl/nb-NO.json\");\nconst _temp20 = require(\"../intl/nl-NL.json\");\nconst _temp21 = require(\"../intl/pl-PL.json\");\nconst _temp22 = require(\"../intl/pt-BR.json\");\nconst _temp23 = require(\"../intl/pt-PT.json\");\nconst _temp24 = require(\"../intl/ro-RO.json\");\nconst _temp25 = require(\"../intl/ru-RU.json\");\nconst _temp26 = require(\"../intl/sk-SK.json\");\nconst _temp27 = require(\"../intl/sl-SI.json\");\nconst _temp28 = require(\"../intl/sr-SP.json\");\nconst _temp29 = require(\"../intl/sv-SE.json\");\nconst _temp30 = require(\"../intl/tr-TR.json\");\nconst _temp31 = require(\"../intl/uk-UA.json\");\nconst _temp32 = require(\"../intl/zh-CN.json\");\nconst _temp33 = require(\"../intl/zh-TW.json\");\nmodule.exports = {\n  \"ar-AE\": _temp0,\n  \"bg-BG\": _temp1,\n  \"cs-CZ\": _temp2,\n  \"da-DK\": _temp3,\n  \"de-DE\": _temp4,\n  \"el-GR\": _temp5,\n  \"en-US\": _temp6,\n  \"es-ES\": _temp7,\n  \"et-EE\": _temp8,\n  \"fi-FI\": _temp9,\n  \"fr-FR\": _temp10,\n  \"he-IL\": _temp11,\n  \"hr-HR\": _temp12,\n  \"hu-HU\": _temp13,\n  \"it-IT\": _temp14,\n  \"ja-JP\": _temp15,\n  \"ko-KR\": _temp16,\n  \"lt-LT\": _temp17,\n  \"lv-LV\": _temp18,\n  \"nb-NO\": _temp19,\n  \"nl-NL\": _temp20,\n  \"pl-PL\": _temp21,\n  \"pt-BR\": _temp22,\n  \"pt-PT\": _temp23,\n  \"ro-RO\": _temp24,\n  \"ru-RU\": _temp25,\n  \"sk-SK\": _temp26,\n  \"sl-SI\": _temp27,\n  \"sr-SP\": _temp28,\n  \"sv-SE\": _temp29,\n  \"tr-TR\": _temp30,\n  \"uk-UA\": _temp31,\n  \"zh-CN\": _temp32,\n  \"zh-TW\": _temp33\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,4BAAiB;IACf,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;IACT,yKAAS,UAAA;AACX", "debugId": null}}, {"offset": {"line": 5088, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/useMenuTrigger.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/src/useMenuTrigger.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaMenuOptions} from './useMenu';\nimport {FocusableElement, RefObject} from '@react-types/shared';\nimport {focusWithoutScrolling, useId} from '@react-aria/utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {MenuTriggerState} from '@react-stately/menu';\nimport {MenuTriggerType} from '@react-types/menu';\nimport {PressProps, useLongPress} from '@react-aria/interactions';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useOverlayTrigger} from '@react-aria/overlays';\n\nexport interface AriaMenuTriggerProps {\n  /** The type of menu that the menu trigger opens. */\n  type?: 'menu' | 'listbox',\n  /** Whether menu trigger is disabled. */\n  isDisabled?: boolean,\n  /** How menu is triggered. */\n  trigger?: MenuTriggerType\n}\n\nexport interface MenuTriggerAria<T> {\n  /** Props for the menu trigger element. */\n  menuTriggerProps: AriaButtonProps,\n\n  /** Props for the menu. */\n  menuProps: AriaMenuOptions<T>\n}\n\n/**\n * Provides the behavior and accessibility implementation for a menu trigger.\n * @param props - Props for the menu trigger.\n * @param state - State for the menu trigger.\n * @param ref - Ref to the HTML element trigger for the menu.\n */\nexport function useMenuTrigger<T>(props: AriaMenuTriggerProps, state: MenuTriggerState, ref: RefObject<Element | null>): MenuTriggerAria<T> {\n  let {\n    type = 'menu',\n    isDisabled,\n    trigger = 'press'\n  } = props;\n\n  let menuTriggerId = useId();\n  let {triggerProps, overlayProps} = useOverlayTrigger({type}, state, ref);\n\n  let onKeyDown = (e) => {\n    if (isDisabled) {\n      return;\n    }\n\n    if (trigger === 'longPress' && !e.altKey) {\n      return;\n    }\n\n    if (ref && ref.current) {\n      switch (e.key) {\n        case 'Enter':\n        case ' ':\n          // React puts listeners on the same root, so even if propagation was stopped, immediate propagation is still possible.\n          // useTypeSelect will handle the spacebar first if it's running, so we don't want to open if it's handled it already.\n          // We use isDefaultPrevented() instead of isPropagationStopped() because createEventHandler stops propagation by default.\n          // And default prevented means that the event was handled by something else (typeahead), so we don't want to open the menu.\n          if (trigger === 'longPress' || e.isDefaultPrevented()) {\n            return;\n          }\n          // fallthrough\n        case 'ArrowDown':\n          // Stop propagation, unless it would already be handled by useKeyboard.\n          if (!('continuePropagation' in e)) {\n            e.stopPropagation();\n          }\n          e.preventDefault();\n          state.toggle('first');\n          break;\n        case 'ArrowUp':\n          if (!('continuePropagation' in e)) {\n            e.stopPropagation();\n          }\n          e.preventDefault();\n          state.toggle('last');\n          break;\n        default:\n          // Allow other keys.\n          if ('continuePropagation' in e) {\n            e.continuePropagation();\n          }\n      }\n    }\n  };\n\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/menu');\n  let {longPressProps} = useLongPress({\n    isDisabled: isDisabled || trigger !== 'longPress',\n    accessibilityDescription: stringFormatter.format('longPressMessage'),\n    onLongPressStart() {\n      state.close();\n    },\n    onLongPress() {\n      state.open('first');\n    }\n  });\n\n  let pressProps: PressProps =  {\n    preventFocusOnPress: true,\n    onPressStart(e) {\n      // For consistency with native, open the menu on mouse/key down, but touch up.\n      if (e.pointerType !== 'touch' && e.pointerType !== 'keyboard' && !isDisabled) {\n        // Ensure trigger has focus before opening the menu so it can be restored by FocusScope on close.\n        focusWithoutScrolling(e.target as FocusableElement);\n\n        // If opened with a screen reader, auto focus the first item.\n        // Otherwise, the menu itself will be focused.\n        state.open(e.pointerType === 'virtual' ? 'first' : null);\n      }\n    },\n    onPress(e) {\n      if (e.pointerType === 'touch' && !isDisabled) {\n        // Ensure trigger has focus before opening the menu so it can be restored by FocusScope on close.\n        focusWithoutScrolling(e.target as FocusableElement);\n\n        state.toggle();\n      }\n    }\n  };\n\n  // omit onPress from triggerProps since we override it above.\n  delete triggerProps.onPress;\n\n  return {\n    // @ts-ignore - TODO we pass out both DOMAttributes AND AriaButtonProps, but useButton will discard the longPress event handlers, it's only through PressResponder magic that this works for RSP and RAC. it does not work in aria examples\n    menuTriggerProps: {\n      ...triggerProps,\n      ...(trigger === 'press' ? pressProps : longPressProps),\n      id: menuTriggerId,\n      onKeyDown\n    },\n    menuProps: {\n      ...overlayProps,\n      'aria-labelledby': menuTriggerId,\n      autoFocus: state.focusStrategy || true,\n      onClose: state.close\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqCM,SAAS,0CAAkB,KAA2B,EAAE,KAAuB,EAAE,GAA8B;IACpH,IAAI,EAAA,MACF,OAAO,MAAA,EAAA,YACP,UAAU,EAAA,SACV,UAAU,OAAA,EACX,GAAG;IAEJ,IAAI,gBAAgB,CAAA,iKAAA,QAAI;IACxB,IAAI,EAAA,cAAC,YAAY,EAAA,cAAE,YAAY,EAAC,GAAG,CAAA,gLAAA,oBAAgB,EAAE;cAAC;IAAI,GAAG,OAAO;IAEpE,IAAI,YAAY,CAAC;QACf,IAAI,YACF;QAGF,IAAI,YAAY,eAAe,CAAC,EAAE,MAAM,EACtC;QAGF,IAAI,OAAO,IAAI,OAAO,EACpB,OAAQ,EAAE,GAAG;YACX,KAAK;Y<PERSON><PERSON>,KAAK;g<PERSON>CH,sHAAsH;gBACtH,qHAAqH;gBACrH,yHAAyH;gBACzH,2HAA2H;gBAC3H,IAAI,YAAY,eAAe,EAAE,kBAAkB,IACjD;YAEF,cAAc;YAChB,KAAK;gBACH,uEAAuE;gBACvE,IAAI,CAAE,CAAA,yBAAyB,CAAA,GAC7B,EAAE,eAAe;gBAEnB,EAAE,cAAc;gBAChB,MAAM,MAAM,CAAC;gBACb;YACF,KAAK;gBACH,IAAI,CAAE,CAAA,yBAAyB,CAAA,GAC7B,EAAE,eAAe;gBAEnB,EAAE,cAAc;gBAChB,MAAM,MAAM,CAAC;gBACb;YACF;gBACE,oBAAoB;gBACpB,IAAI,yBAAyB,GAC3B,EAAE,mBAAmB;QAE3B;IAEJ;IAEA,IAAI,kBAAkB,CAAA,sLAAA,8BAA0B,EAAE,CAAA,GAAA,uBAAA,kKAAA,CAAA,UAAA,CAAW,GAAG;IAChE,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,+KAAA,eAAW,EAAE;QAClC,YAAY,cAAc,YAAY;QACtC,0BAA0B,gBAAgB,MAAM,CAAC;QACjD;YACE,MAAM,KAAK;QACb;QACA;YACE,MAAM,IAAI,CAAC;QACb;IACF;IAEA,IAAI,aAA0B;QAC5B,qBAAqB;QACrB,cAAa,CAAC;YACZ,8EAA8E;YAC9E,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,cAAc,CAAC,YAAY;gBAC5E,iGAAiG;gBACjG,CAAA,iLAAA,wBAAoB,EAAE,EAAE,MAAM;gBAE9B,6DAA6D;gBAC7D,8CAA8C;gBAC9C,MAAM,IAAI,CAAC,EAAE,WAAW,KAAK,YAAY,UAAU;YACrD;QACF;QACA,SAAQ,CAAC;YACP,IAAI,EAAE,WAAW,KAAK,WAAW,CAAC,YAAY;gBAC5C,iGAAiG;gBACjG,CAAA,iLAAA,wBAAoB,EAAE,EAAE,MAAM;gBAE9B,MAAM,MAAM;YACd;QACF;IACF;IAEA,6DAA6D;IAC7D,OAAO,aAAa,OAAO;IAE3B,OAAO;QACL,2OAA2O;QAC3O,kBAAkB;YAChB,GAAG,YAAY;YACf,GAAI,YAAY,UAAU,aAAa,cAAc;YACrD,IAAI;uBACJ;QACF;QACA,WAAW;YACT,GAAG,YAAY;YACf,mBAAmB;YACnB,WAAW,MAAM,aAAa,IAAI;YAClC,SAAS,MAAM,KAAK;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 5204, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Key} from '@react-types/shared';\nimport {TreeState} from '@react-stately/tree';\n\ninterface MenuData {\n  onClose?: () => void,\n  onAction?: (key: Key) => void,\n  shouldUseVirtualFocus?: boolean\n}\n\nexport const menuData: WeakMap<TreeState<unknown>, MenuData> = new WeakMap<TreeState<unknown>, MenuData>();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAWM,MAAM,4CAAkD,IAAI", "debugId": null}}, {"offset": {"line": 5224, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/useMenuItem.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/src/useMenuItem.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, DOMProps, FocusableElement, FocusEvents, HoverEvents, Key, KeyboardEvents, PressEvent, PressEvents, RefObject} from '@react-types/shared';\nimport {filterDOMProps, handleLinkClick, mergeProps, useLinkProps, useRouter, useSlotId} from '@react-aria/utils';\nimport {getItemCount} from '@react-stately/collections';\nimport {isFocusVisible, useFocus, useHover, useKeyboard, usePress} from '@react-aria/interactions';\nimport {menuData} from './utils';\nimport {MouseEvent, useRef} from 'react';\nimport {SelectionManager} from '@react-stately/selection';\nimport {TreeState} from '@react-stately/tree';\nimport {useSelectableItem} from '@react-aria/selection';\n\nexport interface MenuItemAria {\n  /** Props for the menu item element. */\n  menuItemProps: DOMAttributes,\n\n  /** Props for the main text element inside the menu item. */\n  labelProps: DOMAttributes,\n\n  /** Props for the description text element inside the menu item, if any. */\n  descriptionProps: DOMAttributes,\n\n  /** Props for the keyboard shortcut text element inside the item, if any. */\n  keyboardShortcutProps: DOMAttributes,\n\n  /** Whether the item is currently focused. */\n  isFocused: boolean,\n  /** Whether the item is keyboard focused. */\n  isFocusVisible: boolean,\n  /** Whether the item is currently selected. */\n  isSelected: boolean,\n  /** Whether the item is currently in a pressed state. */\n  isPressed: boolean,\n  /** Whether the item is disabled. */\n  isDisabled: boolean\n}\n\nexport interface AriaMenuItemProps extends DOMProps, PressEvents, HoverEvents, KeyboardEvents, FocusEvents  {\n  /**\n   * Whether the menu item is disabled.\n   * @deprecated - pass disabledKeys to useTreeState instead.\n   */\n  isDisabled?: boolean,\n\n  /**\n   * Whether the menu item is selected.\n   * @deprecated - pass selectedKeys to useTreeState instead.\n   */\n  isSelected?: boolean,\n\n  /** A screen reader only label for the menu item. */\n  'aria-label'?: string,\n\n  /** The unique key for the menu item. */\n  key: Key,\n\n  /**\n   * Handler that is called when the menu should close after selecting an item.\n   * @deprecated - pass to the menu instead.\n   */\n  onClose?: () => void,\n\n  /**\n   * Whether the menu should close when the menu item is selected.\n   * @default true\n   */\n  closeOnSelect?: boolean,\n\n  /** Whether the menu item is contained in a virtual scrolling menu. */\n  isVirtualized?: boolean,\n\n  /**\n   * Handler that is called when the user activates the item.\n   * @deprecated - pass to the menu instead.\n   */\n  onAction?: (key: Key) => void,\n\n  /** What kind of popup the item opens. */\n  'aria-haspopup'?: 'menu' | 'dialog',\n\n  /** Indicates whether the menu item's popup element is expanded or collapsed. */\n  'aria-expanded'?: boolean | 'true' | 'false',\n\n  /** Identifies the menu item's popup element whose contents or presence is controlled by the menu item. */\n  'aria-controls'?: string,\n\n  /** Override of the selection manager. By default, `state.selectionManager` is used. */\n  selectionManager?: SelectionManager\n}\n\n/**\n * Provides the behavior and accessibility implementation for an item in a menu.\n * See `useMenu` for more details about menus.\n * @param props - Props for the item.\n * @param state - State for the menu, as returned by `useTreeState`.\n */\nexport function useMenuItem<T>(props: AriaMenuItemProps, state: TreeState<T>, ref: RefObject<FocusableElement | null>): MenuItemAria {\n  let {\n    id,\n    key,\n    closeOnSelect,\n    isVirtualized,\n    'aria-haspopup': hasPopup,\n    onPressStart: pressStartProp,\n    onPressUp: pressUpProp,\n    onPress,\n    onPressChange: pressChangeProp,\n    onPressEnd,\n    onClick: onClickProp,\n    onHoverStart: hoverStartProp,\n    onHoverChange,\n    onHoverEnd,\n    onKeyDown,\n    onKeyUp,\n    onFocus,\n    onFocusChange,\n    onBlur,\n    selectionManager = state.selectionManager\n  } = props;\n\n  let isTrigger = !!hasPopup;\n  let isTriggerExpanded = isTrigger && props['aria-expanded'] === 'true';\n  let isDisabled = props.isDisabled ?? selectionManager.isDisabled(key);\n  let isSelected = props.isSelected ?? selectionManager.isSelected(key);\n  let data = menuData.get(state)!;\n  let item = state.collection.getItem(key);\n  let onClose = props.onClose || data.onClose;\n  let router = useRouter();\n  let performAction = () => {\n    if (isTrigger) {\n      return;\n    }\n\n    if (item?.props?.onAction) {\n      item.props.onAction();\n    } else if (props.onAction) {\n      props.onAction(key);\n    }\n\n    if (data.onAction) {\n      // Must reassign to variable otherwise `this` binding gets messed up. Something to do with WeakMap.\n      let onAction = data.onAction;\n      onAction(key);\n    }\n  };\n\n  let role = 'menuitem';\n  if (!isTrigger) {\n    if (selectionManager.selectionMode === 'single') {\n      role = 'menuitemradio';\n    } else if (selectionManager.selectionMode === 'multiple') {\n      role = 'menuitemcheckbox';\n    }\n  }\n\n  let labelId = useSlotId();\n  let descriptionId = useSlotId();\n  let keyboardId = useSlotId();\n\n  let ariaProps = {\n    id,\n    'aria-disabled': isDisabled || undefined,\n    role,\n    'aria-label': props['aria-label'],\n    'aria-labelledby': labelId,\n    'aria-describedby': [descriptionId, keyboardId].filter(Boolean).join(' ') || undefined,\n    'aria-controls': props['aria-controls'],\n    'aria-haspopup': hasPopup,\n    'aria-expanded': props['aria-expanded']\n  };\n\n  if (selectionManager.selectionMode !== 'none' && !isTrigger) {\n    ariaProps['aria-checked'] = isSelected;\n  }\n\n  if (isVirtualized) {\n    ariaProps['aria-posinset'] = item?.index;\n    ariaProps['aria-setsize'] = getItemCount(state.collection);\n  }\n\n  let onPressStart = (e: PressEvent) => {\n    // Trigger native click event on keydown unless this is a link (the browser will trigger onClick then).\n    if (e.pointerType === 'keyboard' && !selectionManager.isLink(key)) {\n      (e.target as HTMLElement).click();\n    }\n\n    pressStartProp?.(e);\n  };\n  let isPressedRef = useRef(false);\n  let onPressChange = (isPressed: boolean) => {\n    pressChangeProp?.(isPressed);\n    isPressedRef.current = isPressed;\n  };\n\n  let onPressUp = (e: PressEvent) => {\n    // If interacting with mouse, allow the user to mouse down on the trigger button,\n    // drag, and release over an item (matching native behavior).\n    if (e.pointerType === 'mouse') {\n      if (!isPressedRef.current) {\n        (e.target as HTMLElement).click();\n      }\n    }\n\n    // Pressing a menu item should close by default in single selection mode but not multiple\n    // selection mode, except if overridden by the closeOnSelect prop.\n    if (e.pointerType !== 'keyboard' && !isTrigger && onClose && (closeOnSelect ?? (selectionManager.selectionMode !== 'multiple' || selectionManager.isLink(key)))) {\n      onClose();\n    }\n\n    pressUpProp?.(e);\n  };\n\n  let onClick = (e: MouseEvent<FocusableElement>) => {\n    onClickProp?.(e);\n    performAction();\n    handleLinkClick(e, router, item!.props.href, item?.props.routerOptions);\n  };\n\n  let {itemProps, isFocused} = useSelectableItem({\n    id,\n    selectionManager: selectionManager,\n    key,\n    ref,\n    shouldSelectOnPressUp: true,\n    allowsDifferentPressOrigin: true,\n    // Disable all handling of links in useSelectable item\n    // because we handle it ourselves. The behavior of menus\n    // is slightly different from other collections because\n    // actions are performed on key down rather than key up.\n    linkBehavior: 'none',\n    shouldUseVirtualFocus: data.shouldUseVirtualFocus\n  });\n\n  let {pressProps, isPressed} = usePress({\n    onPressStart,\n    onPress,\n    onPressUp,\n    onPressChange,\n    onPressEnd,\n    isDisabled\n  });\n  let {hoverProps} = useHover({\n    isDisabled,\n    onHoverStart(e) {\n      // Hovering over an already expanded sub dialog trigger should keep focus in the dialog.\n      if (!isFocusVisible() && !(isTriggerExpanded && hasPopup)) {\n        selectionManager.setFocused(true);\n        selectionManager.setFocusedKey(key);\n      }\n      hoverStartProp?.(e);\n    },\n    onHoverChange,\n    onHoverEnd\n  });\n\n  let {keyboardProps} = useKeyboard({\n    onKeyDown: (e) => {\n      // Ignore repeating events, which may have started on the menu trigger before moving\n      // focus to the menu item. We want to wait for a second complete key press sequence.\n      if (e.repeat) {\n        e.continuePropagation();\n        return;\n      }\n\n      switch (e.key) {\n        case ' ':\n          if (!isDisabled && selectionManager.selectionMode === 'none' && !isTrigger && closeOnSelect !== false && onClose) {\n            onClose();\n          }\n          break;\n        case 'Enter':\n          // The Enter key should always close on select, except if overridden.\n          if (!isDisabled && closeOnSelect !== false && !isTrigger && onClose) {\n            onClose();\n          }\n          break;\n        default:\n          if (!isTrigger) {\n            e.continuePropagation();\n          }\n\n          onKeyDown?.(e);\n          break;\n      }\n    },\n    onKeyUp\n  });\n\n  let {focusProps} = useFocus({onBlur, onFocus, onFocusChange});\n  let domProps = filterDOMProps(item?.props);\n  delete domProps.id;\n  let linkProps = useLinkProps(item?.props);\n\n  return {\n    menuItemProps: {\n      ...ariaProps,\n      ...mergeProps(\n        domProps,\n        linkProps,\n        isTrigger \n          ? {onFocus: itemProps.onFocus, 'data-collection': itemProps['data-collection'], 'data-key': itemProps['data-key']} \n          : itemProps,\n        pressProps,\n        hoverProps,\n        keyboardProps,\n        focusProps,\n        // Prevent DOM focus from moving on mouse down when using virtual focus or this is a submenu/subdialog trigger.\n        data.shouldUseVirtualFocus || isTrigger ? {onMouseDown: e => e.preventDefault()} : undefined,\n        isDisabled ? undefined : {onClick}\n      ),\n      // If a submenu is expanded, set the tabIndex to -1 so that shift tabbing goes out of the menu instead of the parent menu item.\n      tabIndex: itemProps.tabIndex != null && isTriggerExpanded && !data.shouldUseVirtualFocus ? -1 : itemProps.tabIndex\n    },\n    labelProps: {\n      id: labelId\n    },\n    descriptionProps: {\n      id: descriptionId\n    },\n    keyboardShortcutProps: {\n      id: keyboardId\n    },\n    isFocused,\n    isFocusVisible: isFocused && selectionManager.isFocused && isFocusVisible() && !isTriggerExpanded,\n    isSelected,\n    isPressed,\n    isDisabled\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAgGM,SAAS,0CAAe,KAAwB,EAAE,KAAmB,EAAE,GAAuC;IACnH,IAAI,EAAA,IACF,EAAE,EAAA,KACF,GAAG,EAAA,eACH,aAAa,EAAA,eACb,aAAa,EACb,iBAAiB,QAAQ,EACzB,cAAc,cAAc,EAC5B,WAAW,WAAW,EAAA,SACtB,OAAO,EACP,eAAe,eAAe,EAAA,YAC9B,UAAU,EACV,SAAS,WAAW,EACpB,cAAc,cAAc,EAAA,eAC5B,aAAa,EAAA,YACb,UAAU,EAAA,WACV,SAAS,EAAA,SACT,OAAO,EAAA,SACP,OAAO,EAAA,eACP,aAAa,EAAA,QACb,MAAM,EAAA,kBACN,mBAAmB,MAAM,gBAAgB,EAC1C,GAAG;IAEJ,IAAI,YAAY,CAAC,CAAC;IAClB,IAAI,oBAAoB,aAAa,KAAK,CAAC,gBAAgB,KAAK;QAC/C;IAAjB,IAAI,aAAa,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB,iBAAiB,UAAU,CAAC;QAChD;IAAjB,IAAI,aAAa,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB,iBAAiB,UAAU,CAAC;IACjE,IAAI,OAAO,CAAA,gKAAA,WAAO,EAAE,GAAG,CAAC;IACxB,IAAI,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;IACpC,IAAI,UAAU,MAAM,OAAO,IAAI,KAAK,OAAO;IAC3C,IAAI,SAAS,CAAA,oKAAA,YAAQ;IACrB,IAAI,gBAAgB;YAKd;QAJJ,IAAI,WACF;QAGF,IAAI,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,CAAA,cAAA,KAAM,KAAK,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAa,QAAQ,EACvB,KAAK,KAAK,CAAC,QAAQ;aACd,IAAI,MAAM,QAAQ,EACvB,MAAM,QAAQ,CAAC;QAGjB,IAAI,KAAK,QAAQ,EAAE;YACjB,mGAAmG;YACnG,IAAI,WAAW,KAAK,QAAQ;YAC5B,SAAS;QACX;IACF;IAEA,IAAI,OAAO;IACX,IAAI,CAAC,WAAW;QACd,IAAI,iBAAiB,aAAa,KAAK,UACrC,OAAO;aACF,IAAI,iBAAiB,aAAa,KAAK,YAC5C,OAAO;IAEX;IAEA,IAAI,UAAU,CAAA,iKAAA,YAAQ;IACtB,IAAI,gBAAgB,CAAA,iKAAA,YAAQ;IAC5B,IAAI,aAAa,CAAA,iKAAA,YAAQ;IAEzB,IAAI,YAAY;YACd;QACA,iBAAiB,cAAc;cAC/B;QACA,cAAc,KAAK,CAAC,aAAa;QACjC,mBAAmB;QACnB,oBAAoB;YAAC;YAAe;SAAW,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;QAC7E,iBAAiB,KAAK,CAAC,gBAAgB;QACvC,iBAAiB;QACjB,iBAAiB,KAAK,CAAC,gBAAgB;IACzC;IAEA,IAAI,iBAAiB,aAAa,KAAK,UAAU,CAAC,WAChD,SAAS,CAAC,eAAe,GAAG;IAG9B,IAAI,eAAe;QACjB,SAAS,CAAC,gBAAgB,GAAG,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;QACxC,SAAS,CAAC,eAAe,GAAG,CAAA,iLAAA,eAAW,EAAE,MAAM,UAAU;IAC3D;IAEA,IAAI,eAAe,CAAC;QAClB,uGAAuG;QACvG,IAAI,EAAE,WAAW,KAAK,cAAc,CAAC,iBAAiB,MAAM,CAAC,MAC1D,EAAE,MAAM,CAAiB,KAAK;QAGjC,mBAAA,QAAA,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAiB;IACnB;IACA,IAAI,eAAe,CAAA,iKAAA,SAAK,EAAE;IAC1B,IAAI,gBAAgB,CAAC;QACnB,oBAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAkB;QAClB,aAAa,OAAO,GAAG;IACzB;IAEA,IAAI,YAAY,CAAC;QACf,iFAAiF;QACjF,6DAA6D;QAC7D,IAAI,EAAE,WAAW,KAAK,SACpB;YAAA,IAAI,CAAC,aAAa,OAAO,EACtB,EAAE,MAAM,CAAiB,KAAK;QACjC;QAGF,yFAAyF;QACzF,kEAAkE;QAClE,IAAI,EAAE,WAAW,KAAK,cAAc,CAAC,aAAa,WAAY,CAAA,kBAAA,QAAA,kBAAA,KAAA,IAAA,gBAAkB,iBAAiB,aAAa,KAAK,cAAc,iBAAiB,MAAM,CAAC,IAAI,GAC3J;QAGF,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAc;IAChB;IAEA,IAAI,UAAU,CAAC;QACb,gBAAA,QAAA,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAc;QACd;QACA,CAAA,oKAAA,kBAAc,EAAE,GAAG,QAAQ,KAAM,KAAK,CAAC,IAAI,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK,CAAC,aAAa;IACxE;IAEA,IAAI,EAAA,WAAC,SAAS,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,iLAAA,oBAAgB,EAAE;YAC7C;QACA,kBAAkB;aAClB;aACA;QACA,uBAAuB;QACvB,4BAA4B;QAC5B,sDAAsD;QACtD,wDAAwD;QACxD,uDAAuD;QACvD,wDAAwD;QACxD,cAAc;QACd,uBAAuB,KAAK,qBAAqB;IACnD;IAEA,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;sBACrC;iBACA;mBACA;uBACA;oBACA;oBACA;IACF;IACA,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;oBAC1B;QACA,cAAa,CAAC;YACZ,wFAAwF;YACxF,IAAI,CAAC,CAAA,kLAAA,iBAAa,OAAO,CAAE,CAAA,qBAAqB,QAAO,GAAI;gBACzD,iBAAiB,UAAU,CAAC;gBAC5B,iBAAiB,aAAa,CAAC;YACjC;YACA,mBAAA,QAAA,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAiB;QACnB;uBACA;oBACA;IACF;IAEA,IAAI,EAAA,eAAC,aAAa,EAAC,GAAG,CAAA,8KAAA,cAAU,EAAE;QAChC,WAAW,CAAC;YACV,oFAAoF;YACpF,oFAAoF;YACpF,IAAI,EAAE,MAAM,EAAE;gBACZ,EAAE,mBAAmB;gBACrB;YACF;YAEA,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,IAAI,CAAC,cAAc,iBAAiB,aAAa,KAAK,UAAU,CAAC,aAAa,kBAAkB,SAAS,SACvG;oBAEF;gBACF,KAAK;oBACH,qEAAqE;oBACrE,IAAI,CAAC,cAAc,kBAAkB,SAAS,CAAC,aAAa,SAC1D;oBAEF;gBACF;oBACE,IAAI,CAAC,WACH,EAAE,mBAAmB;oBAGvB,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAY;oBACZ;YACJ;QACF;iBACA;IACF;IAEA,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;gBAAC;iBAAQ;uBAAS;IAAa;IAC3D,IAAI,WAAW,CAAA,0KAAA,iBAAa,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;IACzC,OAAO,SAAS,EAAE;IAClB,IAAI,YAAY,CAAA,oKAAA,eAAW,EAAE,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,KAAK;IAExC,OAAO;QACL,eAAe;YACb,GAAG,SAAS;YACZ,GAAG,CAAA,sKAAA,aAAS,EACV,UACA,WACA,YACI;gBAAC,SAAS,UAAU,OAAO;gBAAE,mBAAmB,SAAS,CAAC,kBAAkB;gBAAE,YAAY,SAAS,CAAC,WAAW;YAAA,IAC/G,WACJ,YACA,YACA,eACA,YACA,AACA,KAAK,qBAAqB,IAAI,YAAY,qEADqE;gBACpE,aAAa,CAAA,IAAK,EAAE,cAAc;YAAE,IAAI,WACnF,aAAa,YAAY;yBAAC;YAAO,EAClC;YACD,+HAA+H;YAC/H,UAAU,UAAU,QAAQ,IAAI,QAAQ,qBAAqB,CAAC,KAAK,qBAAqB,GAAG,CAAA,IAAK,UAAU,QAAQ;QACpH;QACA,YAAY;YACV,IAAI;QACN;QACA,kBAAkB;YAChB,IAAI;QACN;QACA,uBAAuB;YACrB,IAAI;QACN;mBACA;QACA,gBAAgB,aAAa,iBAAiB,SAAS,IAAI,CAAA,kLAAA,iBAAa,OAAO,CAAC;oBAChF;mBACA;oBACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5436, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/useMenuSection.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/src/useMenuSection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport {ReactNode} from 'react';\nimport {useId} from '@react-aria/utils';\n\nexport interface AriaMenuSectionProps {\n  /** The heading for the section. */\n  heading?: ReactNode,\n  /** An accessibility label for the section. Required if `heading` is not present. */\n  'aria-label'?: string\n}\n\nexport interface MenuSectionAria {\n  /** Props for the wrapper list item. */\n  itemProps: DOMAttributes,\n\n  /** Props for the heading element, if any. */\n  headingProps: DOMAttributes,\n\n  /** Props for the group element. */\n  groupProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a section in a menu.\n * See `useMenu` for more details about menus.\n * @param props - Props for the section.\n */\nexport function useMenuSection(props: AriaMenuSectionProps): MenuSectionAria {\n  let {heading, 'aria-label': ariaLabel} = props;\n  let headingId = useId();\n\n  return {\n    itemProps: {\n      role: 'presentation'\n    },\n    headingProps: heading ? {\n      // Techincally, menus cannot contain headings according to ARIA.\n      // We hide the heading from assistive technology, using role=\"presentation\",\n      // and only use it as a label for the nested group.\n      id: headingId,\n      role: 'presentation'\n    } : {},\n    groupProps: {\n      role: 'group',\n      'aria-label': ariaLabel,\n      'aria-labelledby': heading ? headingId : undefined\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GA6BM,SAAS,0CAAe,KAA2B;IACxD,IAAI,EAAA,SAAC,OAAO,EAAE,cAAc,SAAS,EAAC,GAAG;IACzC,IAAI,YAAY,CAAA,iKAAA,QAAI;IAEpB,OAAO;QACL,WAAW;YACT,MAAM;QACR;QACA,cAAc,UAAU;YACtB,gEAAgE;YAChE,4EAA4E;YAC5E,mDAAmD;YACnD,IAAI;YACJ,MAAM;QACR,IAAI,CAAC;QACL,YAAY;YACV,MAAM;YACN,cAAc;YACd,mBAAmB,UAAU,YAAY;QAC3C;IACF;AACF", "debugId": null}}, {"offset": {"line": 5478, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/menu/dist/useMenu.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/menu/dist/packages/%40react-aria/menu/src/useMenu.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaMenuProps} from '@react-types/menu';\nimport {DOMAttributes, KeyboardDelegate, KeyboardEvents, RefObject} from '@react-types/shared';\nimport {filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {menuData} from './utils';\nimport {TreeState} from '@react-stately/tree';\nimport {useSelectableList} from '@react-aria/selection';\n\nexport interface MenuAria {\n  /** Props for the menu element. */\n  menuProps: DOMAttributes\n}\n\nexport interface AriaMenuOptions<T> extends Omit<AriaMenuProps<T>, 'children'>, KeyboardEvents {\n  /** Whether the menu uses virtual scrolling. */\n  isVirtualized?: boolean,\n  /**\n   * An optional keyboard delegate implementation for type to select,\n   * to override the default.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n  /**\n   * Whether the menu items should use virtual focus instead of being focused directly.\n   */\n  shouldUseVirtualFocus?: boolean\n}\n\n/**\n * Provides the behavior and accessibility implementation for a menu component.\n * A menu displays a list of actions or options that a user can choose.\n * @param props - Props for the menu.\n * @param state - State for the menu, as returned by `useListState`.\n */\nexport function useMenu<T>(props: AriaMenuOptions<T>, state: TreeState<T>, ref: RefObject<HTMLElement | null>): MenuAria {\n  let {\n    shouldFocusWrap = true,\n    onKeyDown,\n    onKeyUp,\n    ...otherProps\n  } = props;\n\n  if (!props['aria-label'] && !props['aria-labelledby'] && process.env.NODE_ENV !== 'production') {\n    console.warn('An aria-label or aria-labelledby prop is required for accessibility.');\n  }\n\n  let domProps = filterDOMProps(props, {labelable: true});\n  let {listProps} = useSelectableList({\n    ...otherProps,\n    ref,\n    selectionManager: state.selectionManager,\n    collection: state.collection,\n    disabledKeys: state.disabledKeys,\n    shouldFocusWrap,\n    linkBehavior: 'override'\n  });\n\n  menuData.set(state, {\n    onClose: props.onClose,\n    onAction: props.onAction,\n    shouldUseVirtualFocus: props.shouldUseVirtualFocus\n  });\n\n  return {\n    menuProps: mergeProps(domProps, {onKeyDown, onKeyUp}, {\n      role: 'menu',\n      ...listProps,\n      onKeyDown: (e) => {\n        // don't clear the menu selected keys if the user is presses escape since escape closes the menu\n        if (e.key !== 'Escape' || props.shouldUseVirtualFocus) {\n          listProps.onKeyDown?.(e);\n        }\n      }\n    })\n  };\n}\n"], "names": [], "mappings": ";;;AAoD2D,QAAQ,GAAG,CAAC,QAAQ;;;;;;;;AApD/E;;;;;;;;;;CAUC,GAkCM,SAAS,0CAAW,KAAyB,EAAE,KAAmB,EAAE,GAAkC;IAC3G,IAAI,EAAA,iBACF,kBAAkB,IAAA,EAAA,WAClB,SAAS,EAAA,SACT,OAAO,EACP,GAAG,YACJ,GAAG;IAEJ,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,kBAAkB,wDAA6B,cAChF,QAAQ,IAAI,CAAC;IAGf,IAAI,WAAW,CAAA,GAAA,wLAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,iLAAA,oBAAgB,EAAE;QAClC,GAAG,UAAU;aACb;QACA,kBAAkB,MAAM,gBAAgB;QACxC,YAAY,MAAM,UAAU;QAC5B,cAAc,MAAM,YAAY;yBAChC;QACA,cAAc;IAChB;IAEA,CAAA,GAAA,wKAAO,EAAE,GAAG,CAAC,OAAO;QAClB,SAAS,MAAM,OAAO;QACtB,UAAU,MAAM,QAAQ;QACxB,uBAAuB,MAAM,qBAAqB;IACpD;IAEA,OAAO;QACL,WAAW,CAAA,sKAAA,aAAS,EAAE,UAAU;uBAAC;qBAAW;QAAO,GAAG;YACpD,MAAM;YACN,GAAG,SAAS;YACZ,WAAW,CAAC;oBAGR;gBAFF,gGAAgG;gBAChG,IAAI,EAAE,GAAG,KAAK,YAAY,MAAM,qBAAqB,EAAA,CACnD,uBAAA,UAAU,SAAS,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAA,IAAA,CAAA,WAAsB;YAE1B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 5540, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/i18n/dist/useLocalizedStringFormatter.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useLocalizedStringFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {LocalizedString, LocalizedStringDictionary, LocalizedStringFormatter, LocalizedStrings} from '@internationalized/string';\nimport {useLocale} from './context';\nimport {useMemo} from 'react';\n\nconst cache = new WeakMap();\nfunction getCachedDictionary<K extends string, T extends LocalizedString>(strings: LocalizedStrings<K, T>): LocalizedStringDictionary<K, T> {\n  let dictionary = cache.get(strings);\n  if (!dictionary) {\n    dictionary = new LocalizedStringDictionary(strings);\n    cache.set(strings, dictionary);\n  }\n\n  return dictionary;\n}\n\n/**\n * Returns a cached LocalizedStringDictionary for the given strings.\n */\nexport function useLocalizedStringDictionary<K extends string = string, T extends LocalizedString = string>(strings: LocalizedStrings<K, T>, packageName?: string): LocalizedStringDictionary<K, T> {\n  return (packageName && LocalizedStringDictionary.getGlobalDictionaryForPackage(packageName)) || getCachedDictionary(strings);\n}\n\n/**\n * Provides localized string formatting for the current locale. Supports interpolating variables,\n * selecting the correct pluralization, and formatting numbers. Automatically updates when the locale changes.\n * @param strings - A mapping of languages to localized strings by key.\n */\nexport function useLocalizedStringFormatter<K extends string = string, T extends LocalizedString = string>(strings: LocalizedStrings<K, T>, packageName?: string): LocalizedStringFormatter<K, T> {\n  let {locale} = useLocale();\n  let dictionary = useLocalizedStringDictionary(strings, packageName);\n  return useMemo(() => new LocalizedStringFormatter(locale, dictionary), [locale, dictionary]);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAMD,MAAM,8BAAQ,IAAI;AAClB,SAAS,0CAAiE,OAA+B;IACvG,IAAI,aAAa,4BAAM,GAAG,CAAC;IAC3B,IAAI,CAAC,YAAY;QACf,aAAa,IAAI,CAAA,0LAAA,4BAAwB,EAAE;QAC3C,4BAAM,GAAG,CAAC,SAAS;IACrB;IAEA,OAAO;AACT;AAKO,SAAS,0CAA4F,OAA+B,EAAE,WAAoB;IAC/J,OAAQ,eAAe,CAAA,0LAAA,4BAAwB,EAAE,6BAA6B,CAAC,gBAAiB,0CAAoB;AACtH;AAOO,SAAS,0CAA2F,OAA+B,EAAE,WAAoB;IAC9J,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,kKAAA,YAAQ;IACvB,IAAI,aAAa,0CAA6B,SAAS;IACvD,OAAO,CAAA,iKAAA,UAAM,EAAE,IAAM,IAAI,CAAA,yLAAA,2BAAuB,EAAE,QAAQ,aAAa;QAAC;QAAQ;KAAW;AAC7F", "debugId": null}}, {"offset": {"line": 5587, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/i18n/dist/useCollator.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/i18n/dist/packages/%40react-aria/i18n/src/useCollator.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useLocale} from './context';\n\nlet cache = new Map<string, Intl.Collator>();\n\n/**\n * Provides localized string collation for the current locale. Automatically updates when the locale changes,\n * and handles caching of the collator for performance.\n * @param options - Collator options.\n */\nexport function useCollator(options?: Intl.CollatorOptions): Intl.Collator {\n  let {locale} = useLocale();\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (cache.has(cacheKey)) {\n    return cache.get(cacheKey)!;\n  }\n\n  let formatter = new Intl.Collator(locale, options);\n  cache.set(cacheKey, formatter);\n  return formatter;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAID,IAAI,8BAAQ,IAAI;AAOT,SAAS,0CAAY,OAA8B;IACxD,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,kKAAA,YAAQ;IAEvB,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAA,IAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,4BAAM,GAAG,CAAC,WACZ,OAAO,4BAAM,GAAG,CAAC;IAGnB,IAAI,YAAY,IAAI,KAAK,QAAQ,CAAC,QAAQ;IAC1C,4BAAM,GAAG,CAAC,UAAU;IACpB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5617, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/visually-hidden/dist/VisuallyHidden.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/visually-hidden/dist/packages/%40react-aria/visually-hidden/src/VisuallyHidden.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes} from '@react-types/shared';\nimport {mergeProps} from '@react-aria/utils';\nimport React, {CSSProperties, JSX, JSXElementConstructor, ReactNode, useMemo, useState} from 'react';\nimport {useFocusWithin} from '@react-aria/interactions';\n\nexport interface VisuallyHiddenProps extends DOMAttributes {\n  /** The content to visually hide. */\n  children?: ReactNode,\n\n  /**\n   * The element type for the container.\n   * @default 'div'\n   */\n  elementType?: string | JSXElementConstructor<any>,\n\n  /** Whether the element should become visible on focus, for example skip links. */\n  isFocusable?: boolean\n}\n\nconst styles: CSSProperties = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  clipPath: 'inset(50%)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  width: '1px',\n  whiteSpace: 'nowrap'\n};\n\nexport interface VisuallyHiddenAria {\n  visuallyHiddenProps: DOMAttributes\n}\n\n/**\n * Provides props for an element that hides its children visually\n * but keeps content visible to assistive technology.\n */\nexport function useVisuallyHidden(props: VisuallyHiddenProps = {}): VisuallyHiddenAria {\n  let {\n    style,\n    isFocusable\n  } = props;\n\n  let [isFocused, setFocused] = useState(false);\n  let {focusWithinProps} = useFocusWithin({\n    isDisabled: !isFocusable,\n    onFocusWithinChange: (val) => setFocused(val)\n  });\n\n  // If focused, don't hide the element.\n  let combinedStyles = useMemo(() => {\n    if (isFocused) {\n      return style;\n    } else if (style) {\n      return {...styles, ...style};\n    } else {\n      return styles;\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isFocused]);\n\n  return {\n    visuallyHiddenProps: {\n      ...focusWithinProps,\n      style: combinedStyles\n    }\n  };\n}\n\n/**\n * VisuallyHidden hides its children visually, while keeping content visible\n * to screen readers.\n */\nexport function VisuallyHidden(props: VisuallyHiddenProps): JSX.Element {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  let {children, elementType: Element = 'div', isFocusable, style, ...otherProps} = props;\n  let {visuallyHiddenProps} = useVisuallyHidden(props);\n\n  return (\n    <Element {...mergeProps(otherProps, visuallyHiddenProps)}>\n      {children}\n    </Element>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqBD,MAAM,+BAAwB;IAC5B,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,SAAS;IACT,UAAU;IACV,OAAO;IACP,YAAY;AACd;AAUO,SAAS;gBAAkB,iEAA6B,CAAC,CAAC;IAC/D,IAAI,EAAA,OACF,KAAK,EAAA,aACL,WAAW,EACZ,GAAG;IAEJ,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvC,IAAI,EAAA,kBAAC,gBAAgB,EAAC,GAAG,CAAA,iLAAA,iBAAa,EAAE;QACtC,YAAY,CAAC;QACb,qBAAqB,CAAC,MAAQ,WAAW;IAC3C;IAEA,sCAAsC;IACtC,IAAI,iBAAiB,CAAA,iKAAA,UAAM,EAAE;QAC3B,IAAI,WACF,OAAO;aACF,IAAI,OACT,OAAO;YAAC,GAAG,4BAAM;YAAE,GAAG,KAAK;QAAA;aAE3B,OAAO;IAEX,uDAAuD;IACvD,GAAG;QAAC;KAAU;IAEd,OAAO;QACL,qBAAqB;YACnB,GAAG,gBAAgB;YACnB,OAAO;QACT;IACF;AACF;AAMO,SAAS,0CAAe,KAA0B;IACvD,6DAA6D;IAC7D,IAAI,EAAA,UAAC,QAAQ,EAAE,aAAa,UAAU,KAAK,EAAA,aAAE,WAAW,EAAA,OAAE,KAAK,EAAE,GAAG,YAAW,GAAG;IAClF,IAAI,EAAA,qBAAC,mBAAmB,EAAC,GAAG,0CAAkB;IAE9C,OAAA,WAAA,GACE,CAAA,GAAA,6JAAA,CAAA,UAAA,EAAA,aAAA,CAAC,SAAY,CAAA,sKAAA,aAAS,EAAE,YAAY,sBACjC;AAGP", "debugId": null}}, {"offset": {"line": 5688, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/dialog/dist/useDialog.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/dialog/dist/packages/%40react-aria/dialog/src/useDialog.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaDialogProps} from '@react-types/dialog';\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {filterDOMProps, useSlotId} from '@react-aria/utils';\nimport {focusSafely} from '@react-aria/interactions';\nimport {useEffect, useRef} from 'react';\nimport {useOverlayFocusContain} from '@react-aria/overlays';\n\nexport interface DialogAria {\n  /** Props for the dialog container element. */\n  dialogProps: DOMAttributes,\n\n  /** Props for the dialog title element. */\n  titleProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a dialog component.\n * A dialog is an overlay shown above other content in an application.\n */\nexport function useDialog(props: AriaDialogProps, ref: RefObject<FocusableElement | null>): DialogAria {\n  let {\n    role = 'dialog'\n  } = props;\n  let titleId: string | undefined = useSlotId();\n  titleId = props['aria-label'] ? undefined : titleId;\n\n  let isRefocusing = useRef(false);\n\n  // Focus the dialog itself on mount, unless a child element is already focused.\n  useEffect(() => {\n    if (ref.current && !ref.current.contains(document.activeElement)) {\n      focusSafely(ref.current);\n\n      // Safari on iOS does not move the VoiceOver cursor to the dialog\n      // or announce that it has opened until it has rendered. A workaround\n      // is to wait for half a second, then blur and re-focus the dialog.\n      let timeout = setTimeout(() => {\n        // Check that the dialog is still focused, or focused was lost to the body.\n        if (document.activeElement === ref.current || document.activeElement === document.body) {\n          isRefocusing.current = true;\n          if (ref.current) {\n            ref.current.blur();\n            focusSafely(ref.current);\n          }\n          isRefocusing.current = false;\n        }\n      }, 500);\n\n      return () => {\n        clearTimeout(timeout);\n      };\n    }\n  }, [ref]);\n\n  useOverlayFocusContain();\n\n  // We do not use aria-modal due to a Safari bug which forces the first focusable element to be focused\n  // on mount when inside an iframe, no matter which element we programmatically focus.\n  // See https://bugs.webkit.org/show_bug.cgi?id=211934.\n  // useModal sets aria-hidden on all elements outside the dialog, so the dialog will behave as a modal\n  // even without aria-modal on the dialog itself.\n  return {\n    dialogProps: {\n      ...filterDOMProps(props, {labelable: true}),\n      role,\n      tabIndex: -1,\n      'aria-labelledby': props['aria-labelledby'] || titleId,\n      // Prevent blur events from reaching useOverlay, which may cause\n      // popovers to close. Since focus is contained within the dialog,\n      // we don't want this to occur due to the above useEffect.\n      onBlur: e => {\n        if (isRefocusing.current) {\n          e.stopPropagation();\n        }\n      }\n    },\n    titleProps: {\n      id: titleId\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqBM,SAAS,0CAAU,KAAsB,EAAE,GAAuC;IACvF,IAAI,EAAA,MACF,OAAO,QAAA,EACR,GAAG;IACJ,IAAI,UAA8B,CAAA,iKAAA,YAAQ;IAC1C,UAAU,KAAK,CAAC,aAAa,GAAG,YAAY;IAE5C,IAAI,eAAe,CAAA,iKAAA,SAAK,EAAE;IAE1B,+EAA+E;IAC/E,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,aAAa,GAAG;YAChE,CAAA,8KAAA,cAAU,EAAE,IAAI,OAAO;YAEvB,iEAAiE;YACjE,qEAAqE;YACrE,mEAAmE;YACnE,IAAI,UAAU,WAAW;gBACvB,2EAA2E;gBAC3E,IAAI,SAAS,aAAa,KAAK,IAAI,OAAO,IAAI,SAAS,aAAa,KAAK,SAAS,IAAI,EAAE;oBACtF,aAAa,OAAO,GAAG;oBACvB,IAAI,IAAI,OAAO,EAAE;wBACf,IAAI,OAAO,CAAC,IAAI;wBAChB,CAAA,8KAAA,cAAU,EAAE,IAAI,OAAO;oBACzB;oBACA,aAAa,OAAO,GAAG;gBACzB;YACF,GAAG;YAEH,OAAO;gBACL,aAAa;YACf;QACF;IACF,GAAG;QAAC;KAAI;IAER,CAAA,sKAAA,yBAAqB;IAErB,sGAAsG;IACtG,qFAAqF;IACrF,sDAAsD;IACtD,qGAAqG;IACrG,gDAAgD;IAChD,OAAO;QACL,aAAa;YACX,GAAG,CAAA,0KAAA,iBAAa,EAAE,OAAO;gBAAC,WAAW;YAAI,EAAE;kBAC3C;YACA,UAAU,CAAA;YACV,mBAAmB,KAAK,CAAC,kBAAkB,IAAI;YAC/C,gEAAgE;YAChE,iEAAiE;YACjE,0DAA0D;YAC1D,QAAQ,CAAA;gBACN,IAAI,aAAa,OAAO,EACtB,EAAE,eAAe;YAErB;QACF;QACA,YAAY;YACV,IAAI;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 5772, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/utils.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Key} from '@react-types/shared';\nimport {isAppleDevice, useId} from '@react-aria/utils';\nimport {RefObject} from 'react';\n\ninterface Event {\n  altKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean\n}\n\nexport function isNonContiguousSelectionModifier(e: Event): boolean {\n  // Ctrl + Arrow Up/Arrow Down has a system wide meaning on macOS, so use Alt instead.\n  // On Windows and Ubuntu, Alt + Space has a system wide meaning.\n  return isAppleDevice() ? e.altKey : e.ctrlKey;\n}\n\nexport function getItemElement(collectionRef: RefObject<HTMLElement | null>, key: Key): Element | null | undefined {\n  let selector = `[data-key=\"${CSS.escape(String(key))}\"]`;\n  let collection = collectionRef.current?.dataset.collection;\n  if (collection) {\n    selector = `[data-collection=\"${CSS.escape(collection)}\"]${selector}`;\n  }\n  return collectionRef.current?.querySelector(selector);\n}\n\nconst collectionMap = new WeakMap<Collection<any>, string>();\nexport function useCollectionId(collection: Collection<any>): string {\n  let id = useId();\n  collectionMap.set(collection, id);\n  return id;\n}\n\nexport function getCollectionId(collection: Collection<any>): string {\n  return collectionMap.get(collection)!;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAYM,SAAS,0CAAiC,CAAQ;IACvD,qFAAqF;IACrF,gEAAgE;IAChE,OAAO,CAAA,oKAAA,gBAAY,MAAM,EAAE,MAAM,GAAG,EAAE,OAAO;AAC/C;AAEO,SAAS,0CAAe,aAA4C,EAAE,GAAQ;QAElE,wBAIV;IALP,IAAI,WAAW,AAAC,WAAW,GAA0B,EAAE,CAAC,IAA3B,IAAI,MAAM,CAAC,OAAO;IAC/C,IAAI,aAAA,CAAa,yBAAA,cAAc,OAAO,MAAA,QAArB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAuB,OAAO,CAAC,UAAU;IAC1D,IAAI,YACF,WAAW,AAAC,kBAAkB,GAA6B,OAA3B,IAAI,MAAM,CAAC,aAAY,EAAE,IAAE,CAAU;IAEvE,OAAA,CAAO,0BAAA,cAAc,OAAO,MAAA,QAArB,4BAAA,KAAA,IAAA,KAAA,IAAA,wBAAuB,aAAa,CAAC;AAC9C;AAEA,MAAM,sCAAgB,IAAI;AACnB,SAAS,0CAAgB,UAA2B;IACzD,IAAI,KAAK,CAAA,iKAAA,QAAI;IACb,oCAAc,GAAG,CAAC,YAAY;IAC9B,OAAO;AACT;AAEO,SAAS,0CAAgB,UAA2B;IACzD,OAAO,oCAAc,GAAG,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 5818, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/useSelectableItem.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/useSelectableItem.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain, isCtrlKeyPressed, mergeProps, openLink, useId, useRouter} from '@react-aria/utils';\nimport {DOMAttributes, DOMProps, FocusableElement, Key, LongPressEvent, PointerType, PressEvent, RefObject} from '@react-types/shared';\nimport {focusSafely, PressHookProps, useLongPress, usePress} from '@react-aria/interactions';\nimport {getCollectionId, isNonContiguousSelectionModifier} from './utils';\nimport {moveVirtualFocus} from '@react-aria/focus';\nimport {MultipleSelectionManager} from '@react-stately/selection';\nimport {useEffect, useRef} from 'react';\n\nexport interface SelectableItemOptions extends DOMProps {\n  /**\n   * An interface for reading and updating multiple selection state.\n   */\n  selectionManager: MultipleSelectionManager,\n  /**\n   * A unique key for the item.\n   */\n  key: Key,\n  /**\n   * Ref to the item.\n   */\n  ref: RefObject<FocusableElement | null>,\n  /**\n   * By default, selection occurs on pointer down. This can be strange if selecting an\n   * item causes the UI to disappear immediately (e.g. menus).\n   */\n  shouldSelectOnPressUp?: boolean,\n  /**\n   * Whether selection requires the pointer/mouse down and up events to occur on the same target or triggers selection on\n   * the target of the pointer/mouse up event.\n   */\n  allowsDifferentPressOrigin?: boolean,\n  /**\n   * Whether the option is contained in a virtual scroller.\n   */\n  isVirtualized?: boolean,\n  /**\n   * Function to focus the item.\n   */\n  focus?: () => void,\n  /**\n   * Whether the option should use virtual focus instead of being focused directly.\n   */\n  shouldUseVirtualFocus?: boolean,\n  /** Whether the item is disabled. */\n  isDisabled?: boolean,\n  /**\n   * Handler that is called when a user performs an action on the item. The exact user event depends on\n   * the collection's `selectionBehavior` prop and the interaction modality.\n   */\n  onAction?: () => void,\n  /**\n   * The behavior of links in the collection.\n   * - 'action': link behaves like onAction.\n   * - 'selection': link follows selection interactions (e.g. if URL drives selection).\n   * - 'override': links override all other interactions (link items are not selectable).\n   * - 'none': links are disabled for both selection and actions (e.g. handled elsewhere).\n   * @default 'action'\n   */\n  linkBehavior?: 'action' | 'selection' | 'override' | 'none'\n}\n\nexport interface SelectableItemStates {\n  /** Whether the item is currently in a pressed state. */\n  isPressed: boolean,\n  /** Whether the item is currently selected. */\n  isSelected: boolean,\n  /** Whether the item is currently focused. */\n  isFocused: boolean,\n  /**\n   * Whether the item is non-interactive, i.e. both selection and actions are disabled and the item may\n   * not be focused. Dependent on `disabledKeys` and `disabledBehavior`.\n   */\n  isDisabled: boolean,\n  /**\n   * Whether the item may be selected, dependent on `selectionMode`, `disabledKeys`, and `disabledBehavior`.\n   */\n  allowsSelection: boolean,\n  /**\n   * Whether the item has an action, dependent on `onAction`, `disabledKeys`,\n   * and `disabledBehavior`. It may also change depending on the current selection state\n   * of the list (e.g. when selection is primary). This can be used to enable or disable hover\n   * styles or other visual indications of interactivity.\n   */\n  hasAction: boolean\n}\n\nexport interface SelectableItemAria extends SelectableItemStates {\n  /**\n   * Props to be spread on the item root node.\n   */\n  itemProps: DOMAttributes\n}\n\n/**\n * Handles interactions with an item in a selectable collection.\n */\nexport function useSelectableItem(options: SelectableItemOptions): SelectableItemAria {\n  let {\n    id,\n    selectionManager: manager,\n    key,\n    ref,\n    shouldSelectOnPressUp,\n    shouldUseVirtualFocus,\n    focus,\n    isDisabled,\n    onAction,\n    allowsDifferentPressOrigin,\n    linkBehavior = 'action'\n  } = options;\n  let router = useRouter();\n  id = useId(id);\n  let onSelect = (e: PressEvent | LongPressEvent | PointerEvent) => {\n    if (e.pointerType === 'keyboard' && isNonContiguousSelectionModifier(e)) {\n      manager.toggleSelection(key);\n    } else {\n      if (manager.selectionMode === 'none') {\n        return;\n      }\n\n      if (manager.isLink(key)) {\n        if (linkBehavior === 'selection' && ref.current) {\n          let itemProps = manager.getItemProps(key);\n          router.open(ref.current, e, itemProps.href, itemProps.routerOptions);\n          // Always set selected keys back to what they were so that select and combobox close.\n          manager.setSelectedKeys(manager.selectedKeys);\n          return;\n        } else if (linkBehavior === 'override' || linkBehavior === 'none') {\n          return;\n        }\n      }\n\n      if (manager.selectionMode === 'single') {\n        if (manager.isSelected(key) && !manager.disallowEmptySelection) {\n          manager.toggleSelection(key);\n        } else {\n          manager.replaceSelection(key);\n        }\n      } else if (e && e.shiftKey) {\n        manager.extendSelection(key);\n      } else if (manager.selectionBehavior === 'toggle' || (e && (isCtrlKeyPressed(e) || e.pointerType === 'touch' || e.pointerType === 'virtual'))) {\n        // if touch or virtual (VO) then we just want to toggle, otherwise it's impossible to multi select because they don't have modifier keys\n        manager.toggleSelection(key);\n      } else {\n        manager.replaceSelection(key);\n      }\n    }\n  };\n\n  // Focus the associated DOM node when this item becomes the focusedKey\n  // TODO: can't make this useLayoutEffect bacause it breaks menus inside dialogs\n  // However, if this is a useEffect, it runs twice and dispatches two blur events and immediately sets\n  // aria-activeDescendant in useAutocomplete... I've worked around this for now\n  useEffect(() => {\n    let isFocused = key === manager.focusedKey;\n    if (isFocused && manager.isFocused) {\n      if (!shouldUseVirtualFocus) {\n        if (focus) {\n          focus();\n        } else if (document.activeElement !== ref.current && ref.current) {\n          focusSafely(ref.current);\n        }\n      } else {\n        moveVirtualFocus(ref.current);\n      }\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [ref, key, manager.focusedKey, manager.childFocusStrategy, manager.isFocused, shouldUseVirtualFocus]);\n\n  isDisabled = isDisabled || manager.isDisabled(key);\n  // Set tabIndex to 0 if the element is focused, or -1 otherwise so that only the last focused\n  // item is tabbable.  If using virtual focus, don't set a tabIndex at all so that VoiceOver\n  // on iOS 14 doesn't try to move real DOM focus to the item anyway.\n  let itemProps: SelectableItemAria['itemProps'] = {};\n  if (!shouldUseVirtualFocus && !isDisabled) {\n    itemProps = {\n      tabIndex: key === manager.focusedKey ? 0 : -1,\n      onFocus(e) {\n        if (e.target === ref.current) {\n          manager.setFocusedKey(key);\n        }\n      }\n    };\n  } else if (isDisabled) {\n    itemProps.onMouseDown = (e) => {\n      // Prevent focus going to the body when clicking on a disabled item.\n      e.preventDefault();\n    };\n  }\n\n  // With checkbox selection, onAction (i.e. navigation) becomes primary, and occurs on a single click of the row.\n  // Clicking the checkbox enters selection mode, after which clicking anywhere on any row toggles selection for that row.\n  // With highlight selection, onAction is secondary, and occurs on double click. Single click selects the row.\n  // With touch, onAction occurs on single tap, and long press enters selection mode.\n  let isLinkOverride = manager.isLink(key) && linkBehavior === 'override';\n  let hasLinkAction = manager.isLink(key) && linkBehavior !== 'selection' && linkBehavior !== 'none';\n  let allowsSelection = !isDisabled && manager.canSelectItem(key) && !isLinkOverride;\n  let allowsActions = (onAction || hasLinkAction) && !isDisabled;\n  let hasPrimaryAction = allowsActions && (\n    manager.selectionBehavior === 'replace'\n      ? !allowsSelection\n      : !allowsSelection || manager.isEmpty\n  );\n  let hasSecondaryAction = allowsActions && allowsSelection && manager.selectionBehavior === 'replace';\n  let hasAction = hasPrimaryAction || hasSecondaryAction;\n  let modality = useRef<PointerType | null>(null);\n\n  let longPressEnabled = hasAction && allowsSelection;\n  let longPressEnabledOnPressStart = useRef(false);\n  let hadPrimaryActionOnPressStart = useRef(false);\n  let collectionItemProps = manager.getItemProps(key);\n\n  let performAction = (e) => {\n    if (onAction) {\n      onAction();\n    }\n\n    if (hasLinkAction && ref.current) {\n      router.open(ref.current, e, collectionItemProps.href, collectionItemProps.routerOptions);\n    }\n  };\n\n  // By default, selection occurs on pointer down. This can be strange if selecting an\n  // item causes the UI to disappear immediately (e.g. menus).\n  // If shouldSelectOnPressUp is true, we use onPressUp instead of onPressStart.\n  // onPress requires a pointer down event on the same element as pointer up. For menus,\n  // we want to be able to have the pointer down on the trigger that opens the menu and\n  // the pointer up on the menu item rather than requiring a separate press.\n  // For keyboard events, selection still occurs on key down.\n  let itemPressProps: PressHookProps = {ref};\n  if (shouldSelectOnPressUp) {\n    itemPressProps.onPressStart = (e) => {\n      modality.current = e.pointerType;\n      longPressEnabledOnPressStart.current = longPressEnabled;\n      if (e.pointerType === 'keyboard' && (!hasAction || isSelectionKey())) {\n        onSelect(e);\n      }\n    };\n\n    // If allowsDifferentPressOrigin and interacting with mouse, make selection happen on pressUp (e.g. open menu on press down, selection on menu item happens on press up.)\n    // Otherwise, have selection happen onPress (prevents listview row selection when clicking on interactable elements in the row)\n    if (!allowsDifferentPressOrigin) {\n      itemPressProps.onPress = (e) => {\n        if (hasPrimaryAction || (hasSecondaryAction && e.pointerType !== 'mouse')) {\n          if (e.pointerType === 'keyboard' && !isActionKey()) {\n            return;\n          }\n\n          performAction(e);\n        } else if (e.pointerType !== 'keyboard' && allowsSelection) {\n          onSelect(e);\n        }\n      };\n    } else {\n      itemPressProps.onPressUp = hasPrimaryAction ? undefined : (e) => {\n        if (e.pointerType === 'mouse' && allowsSelection) {\n          onSelect(e);\n        }\n      };\n\n      itemPressProps.onPress = hasPrimaryAction ? performAction : (e) => {\n        if (e.pointerType !== 'keyboard' && e.pointerType !== 'mouse' && allowsSelection) {\n          onSelect(e);\n        }\n      };\n    }\n  } else {\n    itemPressProps.onPressStart = (e) => {\n      modality.current = e.pointerType;\n      longPressEnabledOnPressStart.current = longPressEnabled;\n      hadPrimaryActionOnPressStart.current = hasPrimaryAction;\n\n      // Select on mouse down unless there is a primary action which will occur on mouse up.\n      // For keyboard, select on key down. If there is an action, the Space key selects on key down,\n      // and the Enter key performs onAction on key up.\n      if (\n        allowsSelection && (\n          (e.pointerType === 'mouse' && !hasPrimaryAction) ||\n          (e.pointerType === 'keyboard' && (!allowsActions || isSelectionKey()))\n        )\n      ) {\n        onSelect(e);\n      }\n    };\n\n    itemPressProps.onPress = (e) => {\n      // Selection occurs on touch up. Primary actions always occur on pointer up.\n      // Both primary and secondary actions occur on Enter key up. The only exception\n      // is secondary actions, which occur on double click with a mouse.\n      if (\n        e.pointerType === 'touch' ||\n        e.pointerType === 'pen' ||\n        e.pointerType === 'virtual' ||\n        (e.pointerType === 'keyboard' && hasAction && isActionKey()) ||\n        (e.pointerType === 'mouse' && hadPrimaryActionOnPressStart.current)\n      ) {\n        if (hasAction) {\n          performAction(e);\n        } else if (allowsSelection) {\n          onSelect(e);\n        }\n      }\n    };\n  }\n\n  itemProps['data-collection'] = getCollectionId(manager.collection);\n  itemProps['data-key'] = key;\n  itemPressProps.preventFocusOnPress = shouldUseVirtualFocus;\n\n  // When using virtual focus, make sure the focused key gets updated on press.\n  if (shouldUseVirtualFocus) {\n    itemPressProps = mergeProps(itemPressProps, {\n      onPressStart(e) {\n        if (e.pointerType !== 'touch') {\n          manager.setFocused(true);\n          manager.setFocusedKey(key);\n        }\n      },\n      onPress(e) {\n        if (e.pointerType === 'touch') {\n          manager.setFocused(true);\n          manager.setFocusedKey(key);\n        }\n      }\n    });\n  }\n\n  if (collectionItemProps) {\n    for (let key of ['onPressStart', 'onPressEnd', 'onPressChange', 'onPress', 'onPressUp', 'onClick']) {\n      if (collectionItemProps[key]) {\n        itemPressProps[key] = chain(itemPressProps[key], collectionItemProps[key]);\n      }\n    }\n  }\n\n  let {pressProps, isPressed} = usePress(itemPressProps);\n\n  // Double clicking with a mouse with selectionBehavior = 'replace' performs an action.\n  let onDoubleClick = hasSecondaryAction ? (e) => {\n    if (modality.current === 'mouse') {\n      e.stopPropagation();\n      e.preventDefault();\n      performAction(e);\n    }\n  } : undefined;\n\n  // Long pressing an item with touch when selectionBehavior = 'replace' switches the selection behavior\n  // to 'toggle'. This changes the single tap behavior from performing an action (i.e. navigating) to\n  // selecting, and may toggle the appearance of a UI affordance like checkboxes on each item.\n  let {longPressProps} = useLongPress({\n    isDisabled: !longPressEnabled,\n    onLongPress(e) {\n      if (e.pointerType === 'touch') {\n        onSelect(e);\n        manager.setSelectionBehavior('toggle');\n      }\n    }\n  });\n\n  // Prevent native drag and drop on long press if we also select on long press.\n  // Once the user is in selection mode, they can long press again to drag.\n  // Use a capturing listener to ensure this runs before useDrag, regardless of\n  // the order the props get merged.\n  let onDragStartCapture = e => {\n    if (modality.current === 'touch' && longPressEnabledOnPressStart.current) {\n      e.preventDefault();\n    }\n  };\n\n  // Prevent default on link clicks so that we control exactly\n  // when they open (to match selection behavior).\n  let onClick = linkBehavior !== 'none' && manager.isLink(key) ? e => {\n    if (!(openLink as any).isOpening) {\n      e.preventDefault();\n    }\n  } : undefined;\n\n  return {\n    itemProps: mergeProps(\n      itemProps,\n      allowsSelection || hasPrimaryAction || (shouldUseVirtualFocus && !isDisabled) ? pressProps : {},\n      longPressEnabled ? longPressProps : {},\n      {onDoubleClick, onDragStartCapture, onClick, id},\n      // Prevent DOM focus from moving on mouse down when using virtual focus\n      shouldUseVirtualFocus ? {onMouseDown: e => e.preventDefault()} : undefined\n    ),\n    isPressed,\n    isSelected: manager.isSelected(key),\n    isFocused: manager.isFocused && manager.focusedKey === key,\n    isDisabled,\n    allowsSelection,\n    hasAction\n  };\n}\n\nfunction isActionKey() {\n  let event = window.event as KeyboardEvent;\n  return event?.key === 'Enter';\n}\n\nfunction isSelectionKey() {\n  let event = window.event as KeyboardEvent;\n  return event?.key === ' ' || event?.code === 'Space';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAkGM,SAAS,0CAAkB,OAA8B;IAC9D,IAAI,EAAA,IACF,EAAE,EACF,kBAAkB,OAAO,EAAA,KACzB,GAAG,EAAA,KACH,GAAG,EAAA,uBACH,qBAAqB,EAAA,uBACrB,qBAAqB,EAAA,OACrB,KAAK,EAAA,YACL,UAAU,EAAA,UACV,QAAQ,EAAA,4BACR,0BAA0B,EAAA,cAC1B,eAAe,QAAA,EAChB,GAAG;IACJ,IAAI,SAAS,CAAA,oKAAA,YAAQ;IACrB,KAAK,CAAA,iKAAA,QAAI,EAAE;IACX,IAAI,WAAW,CAAC;QACd,IAAI,EAAE,WAAW,KAAK,cAAc,CAAA,qKAAA,mCAA+B,EAAE,IACnE,QAAQ,eAAe,CAAC;aACnB;YACL,IAAI,QAAQ,aAAa,KAAK,QAC5B;YAGF,IAAI,QAAQ,MAAM,CAAC,MAAM;gBACvB,IAAI,iBAAiB,eAAe,IAAI,OAAO,EAAE;oBAC/C,IAAI,YAAY,QAAQ,YAAY,CAAC;oBACrC,OAAO,IAAI,CAAC,IAAI,OAAO,EAAE,GAAG,UAAU,IAAI,EAAE,UAAU,aAAa;oBACnE,qFAAqF;oBACrF,QAAQ,eAAe,CAAC,QAAQ,YAAY;oBAC5C;gBACF,OAAO,IAAI,iBAAiB,cAAc,iBAAiB,QACzD;YAEJ;YAEA,IAAI,QAAQ,aAAa,KAAK,UAAA;gBAC5B,IAAI,QAAQ,UAAU,CAAC,QAAQ,CAAC,QAAQ,sBAAsB,EAC5D,QAAQ,eAAe,CAAC;qBAExB,QAAQ,gBAAgB,CAAC;mBAEtB,IAAI,KAAK,EAAE,QAAQ,EACxB,QAAQ,eAAe,CAAC;iBACnB,IAAI,QAAQ,iBAAiB,KAAK,YAAa,KAAM,CAAA,CAAA,oKAAA,mBAAe,EAAE,MAAM,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,SAAQ,GACxI,AACA,QAAQ,eAAe,CAAC,gHADgH;iBAGxI,QAAQ,gBAAgB,CAAC;QAE7B;IACF;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,qGAAqG;IACrG,8EAA8E;IAC9E,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,YAAY,QAAQ,QAAQ,UAAU;QAC1C,IAAI,aAAa,QAAQ,SAAS,EAAA;YAChC,IAAI,CAAC,uBAAuB;gBAC1B,IAAI,OACF;qBACK,IAAI,SAAS,aAAa,KAAK,IAAI,OAAO,IAAI,IAAI,OAAO,EAC9D,CAAA,8KAAA,cAAU,EAAE,IAAI,OAAO;YAE3B,OACE,CAAA,wKAAA,mBAAe,EAAE,IAAI,OAAO;;IAGlC,uDAAuD;IACvD,GAAG;QAAC;QAAK;QAAK,QAAQ,UAAU;QAAE,QAAQ,kBAAkB;QAAE,QAAQ,SAAS;QAAE;KAAsB;IAEvG,aAAa,cAAc,QAAQ,UAAU,CAAC;IAC9C,6FAA6F;IAC7F,2FAA2F;IAC3F,mEAAmE;IACnE,IAAI,YAA6C,CAAC;IAClD,IAAI,CAAC,yBAAyB,CAAC,YAC7B,YAAY;QACV,UAAU,QAAQ,QAAQ,UAAU,GAAG,IAAI,CAAA;QAC3C,SAAQ,CAAC;YACP,IAAI,EAAE,MAAM,KAAK,IAAI,OAAO,EAC1B,QAAQ,aAAa,CAAC;QAE1B;IACF;SACK,IAAI,YACT,UAAU,WAAW,GAAG,CAAC;QACvB,oEAAoE;QACpE,EAAE,cAAc;IAClB;IAGF,gHAAgH;IAChH,wHAAwH;IACxH,6GAA6G;IAC7G,mFAAmF;IACnF,IAAI,iBAAiB,QAAQ,MAAM,CAAC,QAAQ,iBAAiB;IAC7D,IAAI,gBAAgB,QAAQ,MAAM,CAAC,QAAQ,iBAAiB,eAAe,iBAAiB;IAC5F,IAAI,kBAAkB,CAAC,cAAc,QAAQ,aAAa,CAAC,QAAQ,CAAC;IACpE,IAAI,gBAAiB,CAAA,YAAY,aAAY,KAAM,CAAC;IACpD,IAAI,mBAAmB,iBACrB,CAAA,QAAQ,iBAAiB,KAAK,YAC1B,CAAC,kBACD,CAAC,mBAAmB,QAAQ,OAAM;IAExC,IAAI,qBAAqB,iBAAiB,mBAAmB,QAAQ,iBAAiB,KAAK;IAC3F,IAAI,YAAY,oBAAoB;IACpC,IAAI,WAAW,CAAA,iKAAA,SAAK,EAAsB;IAE1C,IAAI,mBAAmB,aAAa;IACpC,IAAI,+BAA+B,CAAA,iKAAA,SAAK,EAAE;IAC1C,IAAI,+BAA+B,CAAA,iKAAA,SAAK,EAAE;IAC1C,IAAI,sBAAsB,QAAQ,YAAY,CAAC;IAE/C,IAAI,gBAAgB,CAAC;QACnB,IAAI,UACF;QAGF,IAAI,iBAAiB,IAAI,OAAO,EAC9B,OAAO,IAAI,CAAC,IAAI,OAAO,EAAE,GAAG,oBAAoB,IAAI,EAAE,oBAAoB,aAAa;IAE3F;IAEA,oFAAoF;IACpF,4DAA4D;IAC5D,8EAA8E;IAC9E,sFAAsF;IACtF,qFAAqF;IACrF,0EAA0E;IAC1E,2DAA2D;IAC3D,IAAI,iBAAiC;aAAC;IAAG;IACzC,IAAI,uBAAuB;QACzB,eAAe,YAAY,GAAG,CAAC;YAC7B,SAAS,OAAO,GAAG,EAAE,WAAW;YAChC,6BAA6B,OAAO,GAAG;YACvC,IAAI,EAAE,WAAW,KAAK,cAAe,CAAA,CAAC,aAAa,sCAAe,GAChE,SAAS;QAEb;QAEA,yKAAyK;QACzK,+HAA+H;QAC/H,IAAI,CAAC,4BACH,eAAe,OAAO,GAAG,CAAC;YACxB,IAAI,oBAAqB,sBAAsB,EAAE,WAAW,KAAK,SAAU;gBACzE,IAAI,EAAE,WAAW,KAAK,cAAc,CAAC,qCACnC;gBAGF,cAAc;YAChB,OAAO,IAAI,EAAE,WAAW,KAAK,cAAc,iBACzC,SAAS;QAEb;aACK;YACL,eAAe,SAAS,GAAG,mBAAmB,YAAY,CAAC;gBACzD,IAAI,EAAE,WAAW,KAAK,WAAW,iBAC/B,SAAS;YAEb;YAEA,eAAe,OAAO,GAAG,mBAAmB,gBAAgB,CAAC;gBAC3D,IAAI,EAAE,WAAW,KAAK,cAAc,EAAE,WAAW,KAAK,WAAW,iBAC/D,SAAS;YAEb;QACF;IACF,OAAO;QACL,eAAe,YAAY,GAAG,CAAC;YAC7B,SAAS,OAAO,GAAG,EAAE,WAAW;YAChC,6BAA6B,OAAO,GAAG;YACvC,6BAA6B,OAAO,GAAG;YAEvC,sFAAsF;YACtF,8FAA8F;YAC9F,iDAAiD;YACjD,IACE,mBACE,CAAC,EAAE,WAAW,KAAK,WAAW,CAAC,oBAC9B,EAAE,WAAW,KAAK,cAAe,CAAA,CAAC,iBAAiB,sCAAe,CAAE,GAGvE,SAAS;QAEb;QAEA,eAAe,OAAO,GAAG,CAAC;YACxB,4EAA4E;YAC5E,+EAA+E;YAC/E,kEAAkE;YAClE,IACE,EAAE,WAAW,KAAK,WAClB,EAAE,WAAW,KAAK,SAClB,EAAE,WAAW,KAAK,aACjB,EAAE,WAAW,KAAK,cAAc,aAAa,uCAC7C,EAAE,WAAW,KAAK,WAAW,6BAA6B,OAAO,EAClE;gBACA,IAAI,WACF,cAAc;qBACT,IAAI,iBACT,SAAS;YAEb;QACF;IACF;IAEA,SAAS,CAAC,kBAAkB,GAAG,CAAA,qKAAA,kBAAc,EAAE,QAAQ,UAAU;IACjE,SAAS,CAAC,WAAW,GAAG;IACxB,eAAe,mBAAmB,GAAG;IAErC,6EAA6E;IAC7E,IAAI,uBACF,iBAAiB,CAAA,sKAAA,aAAS,EAAE,gBAAgB;QAC1C,cAAa,CAAC;YACZ,IAAI,EAAE,WAAW,KAAK,SAAS;gBAC7B,QAAQ,UAAU,CAAC;gBACnB,QAAQ,aAAa,CAAC;YACxB;QACF;QACA,SAAQ,CAAC;YACP,IAAI,EAAE,WAAW,KAAK,SAAS;gBAC7B,QAAQ,UAAU,CAAC;gBACnB,QAAQ,aAAa,CAAC;YACxB;QACF;IACF;IAGF,IAAI,qBAAqB;QACvB,KAAK,IAAI,OAAO;YAAC;YAAgB;YAAc;YAAiB;YAAW;YAAa;SAAU,CAChG,IAAI,mBAAmB,CAAC,IAAI,EAC1B,cAAc,CAAC,IAAI,GAAG,CAAA,iKAAA,QAAI,EAAE,cAAc,CAAC,IAAI,EAAE,mBAAmB,CAAC,IAAI;IAG/E;IAEA,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;IAEvC,sFAAsF;IACtF,IAAI,gBAAgB,qBAAqB,CAAC;QACxC,IAAI,SAAS,OAAO,KAAK,SAAS;YAChC,EAAE,eAAe;YACjB,EAAE,cAAc;YAChB,cAAc;QAChB;IACF,IAAI;IAEJ,sGAAsG;IACtG,mGAAmG;IACnG,4FAA4F;IAC5F,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,+KAAA,eAAW,EAAE;QAClC,YAAY,CAAC;QACb,aAAY,CAAC;YACX,IAAI,EAAE,WAAW,KAAK,SAAS;gBAC7B,SAAS;gBACT,QAAQ,oBAAoB,CAAC;YAC/B;QACF;IACF;IAEA,8EAA8E;IAC9E,yEAAyE;IACzE,6EAA6E;IAC7E,kCAAkC;IAClC,IAAI,qBAAqB,CAAA;QACvB,IAAI,SAAS,OAAO,KAAK,WAAW,6BAA6B,OAAO,EACtE,EAAE,cAAc;IAEpB;IAEA,4DAA4D;IAC5D,gDAAgD;IAChD,IAAI,UAAU,iBAAiB,UAAU,QAAQ,MAAM,CAAC,OAAO,CAAA;QAC7D,IAAI,CAAE,CAAA,oKAAA,WAAO,EAAU,SAAS,EAC9B,EAAE,cAAc;IAEpB,IAAI;IAEJ,OAAO;QACL,WAAW,CAAA,sKAAA,aAAS,EAClB,WACA,mBAAmB,oBAAqB,yBAAyB,CAAC,aAAc,aAAa,CAAC,GAC9F,mBAAmB,iBAAiB,CAAC,GACrC;2BAAC;gCAAe;qBAAoB;gBAAS;QAAE,GAC/C,AACA,wBAAwB,+CAD+C;YAC9C,aAAa,CAAA,IAAK,EAAE,cAAc;QAAE,IAAI;mBAEnE;QACA,YAAY,QAAQ,UAAU,CAAC;QAC/B,WAAW,QAAQ,SAAS,IAAI,QAAQ,UAAU,KAAK;oBACvD;yBACA;mBACA;IACF;AACF;AAEA,SAAS;IACP,IAAI,QAAQ,OAAO,KAAK;IACxB,OAAO,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,GAAG,MAAK;AACxB;AAEA,SAAS;IACP,IAAI,QAAQ,OAAO,KAAK;IACxB,OAAO,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,GAAG,MAAK,OAAO,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI,MAAK;AAC/C", "debugId": null}}, {"offset": {"line": 6072, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/useTypeSelect.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/useTypeSelect.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, Key, KeyboardDelegate} from '@react-types/shared';\nimport {KeyboardEvent, useRef} from 'react';\nimport {MultipleSelectionManager} from '@react-stately/selection';\n\n/**\n * Controls how long to wait before clearing the typeahead buffer.\n */\nconst TYPEAHEAD_DEBOUNCE_WAIT_MS = 1000; // 1 second\n\nexport interface AriaTypeSelectOptions {\n  /**\n   * A delegate that returns collection item keys with respect to visual layout.\n   */\n  keyboardDelegate: KeyboardDelegate,\n  /**\n   * An interface for reading and updating multiple selection state.\n   */\n  selectionManager: MultipleSelectionManager,\n  /**\n   * Called when an item is focused by typing.\n   */\n  onTypeSelect?: (key: Key) => void\n}\n\nexport interface TypeSelectAria {\n  /**\n   * Props to be spread on the owner of the options.\n   */\n  typeSelectProps: DOMAttributes\n}\n\n/**\n * Handles typeahead interactions with collections.\n */\nexport function useTypeSelect(options: AriaTypeSelectOptions): TypeSelectAria {\n  let {keyboardDelegate, selectionManager, onTypeSelect} = options;\n  let state = useRef<{search: string, timeout: ReturnType<typeof setTimeout> | undefined}>({\n    search: '',\n    timeout: undefined\n  }).current;\n\n  let onKeyDown = (e: KeyboardEvent) => {\n    let character = getStringForKey(e.key);\n    if (!character || e.ctrlKey || e.metaKey || !e.currentTarget.contains(e.target as HTMLElement) || (state.search.length === 0 && character === ' ')) {\n      return;\n    }\n\n    // Do not propagate the Spacebar event if it's meant to be part of the search.\n    // When we time out, the search term becomes empty, hence the check on length.\n    // Trimming is to account for the case of pressing the Spacebar more than once,\n    // which should cycle through the selection/deselection of the focused item.\n    if (character === ' ' && state.search.trim().length > 0) {\n      e.preventDefault();\n      if (!('continuePropagation' in e)) {\n        e.stopPropagation();\n      }\n    }\n\n    state.search += character;\n\n    if (keyboardDelegate.getKeyForSearch != null) {\n      // Use the delegate to find a key to focus.\n      // Prioritize items after the currently focused item, falling back to searching the whole list.\n      let key = keyboardDelegate.getKeyForSearch(state.search, selectionManager.focusedKey);\n\n      // If no key found, search from the top.\n      if (key == null) {\n        key = keyboardDelegate.getKeyForSearch(state.search);\n      }\n\n      if (key != null) {\n        selectionManager.setFocusedKey(key);\n        if (onTypeSelect) {\n          onTypeSelect(key);\n        }\n      }\n    }\n\n    clearTimeout(state.timeout);\n    state.timeout = setTimeout(() => {\n      state.search = '';\n    }, TYPEAHEAD_DEBOUNCE_WAIT_MS);\n  };\n\n  return {\n    typeSelectProps: {\n      // Using a capturing listener to catch the keydown event before\n      // other hooks in order to handle the Spacebar event.\n      onKeyDownCapture: keyboardDelegate.getKeyForSearch ? onKeyDown : undefined\n    }\n  };\n}\n\nfunction getStringForKey(key: string) {\n  // If the key is of length 1, it is an ASCII value.\n  // Otherwise, if there are no ASCII characters in the key name,\n  // it is a Unicode character.\n  // See https://www.w3.org/TR/uievents-key/\n  if (key.length === 1 || !/^[A-Z]/i.test(key)) {\n    return key;\n  }\n\n  return '';\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAMD;;CAEC,GACD,MAAM,mDAA6B,MAAM,WAAW;AA2B7C,SAAS,0CAAc,OAA8B;IAC1D,IAAI,EAAA,kBAAC,gBAAgB,EAAA,kBAAE,gBAAgB,EAAA,cAAE,YAAY,EAAC,GAAG;IACzD,IAAI,QAAQ,CAAA,iKAAA,SAAK,EAAwE;QACvF,QAAQ;QACR,SAAS;IACX,GAAG,OAAO;IAEV,IAAI,YAAY,CAAC;QACf,IAAI,YAAY,sCAAgB,EAAE,GAAG;QACrC,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,EAAE,OAAO,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,KAAqB,MAAM,MAAM,CAAC,MAAM,KAAK,KAAK,cAAc,KAC5I;QAGF,8EAA8E;QAC9E,8EAA8E;QAC9E,+EAA+E;QAC/E,4EAA4E;QAC5E,IAAI,cAAc,OAAO,MAAM,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YACvD,EAAE,cAAc;YAChB,IAAI,CAAE,CAAA,yBAAyB,CAAA,GAC7B,EAAE,eAAe;QAErB;QAEA,MAAM,MAAM,IAAI;QAEhB,IAAI,iBAAiB,eAAe,IAAI,MAAM;YAC5C,2CAA2C;YAC3C,+FAA+F;YAC/F,IAAI,MAAM,iBAAiB,eAAe,CAAC,MAAM,MAAM,EAAE,iBAAiB,UAAU;YAEpF,wCAAwC;YACxC,IAAI,OAAO,MACT,MAAM,iBAAiB,eAAe,CAAC,MAAM,MAAM;YAGrD,IAAI,OAAO,MAAM;gBACf,iBAAiB,aAAa,CAAC;gBAC/B,IAAI,cACF,aAAa;YAEjB;QACF;QAEA,aAAa,MAAM,OAAO;QAC1B,MAAM,OAAO,GAAG,WAAW;YACzB,MAAM,MAAM,GAAG;QACjB,GAAG;IACL;IAEA,OAAO;QACL,iBAAiB;YACf,+DAA+D;YAC/D,qDAAqD;YACrD,kBAAkB,iBAAiB,eAAe,GAAG,YAAY;QACnE;IACF;AACF;AAEA,SAAS,sCAAgB,GAAW;IAClC,mDAAmD;IACnD,+DAA+D;IAC/D,6BAA6B;IAC7B,0CAA0C;IAC1C,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,UAAU,IAAI,CAAC,MACtC,OAAO;IAGT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 6146, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/useSelectableCollection.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/useSelectableCollection.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CLEAR_FOCUS_EVENT, FOCUS_EVENT, focusWithoutScrolling, getActiveElement, isCtrlKeyPressed, mergeProps, scrollIntoView, scrollIntoViewport, useEffectEvent, useEvent, useRouter, useUpdateLayoutEffect} from '@react-aria/utils';\nimport {dispatchVirtualFocus, getFocusableTreeWalker, moveVirtualFocus} from '@react-aria/focus';\nimport {DOMAttributes, FocusableElement, FocusStrategy, Key, KeyboardDelegate, RefObject} from '@react-types/shared';\nimport {flushSync} from 'react-dom';\nimport {FocusEvent, KeyboardEvent, useEffect, useRef} from 'react';\nimport {focusSafely, getInteractionModality} from '@react-aria/interactions';\nimport {getItemElement, isNonContiguousSelectionModifier, useCollectionId} from './utils';\nimport {MultipleSelectionManager} from '@react-stately/selection';\nimport {useLocale} from '@react-aria/i18n';\nimport {useTypeSelect} from './useTypeSelect';\n\nexport interface AriaSelectableCollectionOptions {\n  /**\n   * An interface for reading and updating multiple selection state.\n   */\n  selectionManager: MultipleSelectionManager,\n  /**\n   * A delegate object that implements behavior for keyboard focus movement.\n   */\n  keyboardDelegate: KeyboardDelegate,\n  /**\n   * The ref attached to the element representing the collection.\n   */\n  ref: RefObject<HTMLElement | null>,\n  /**\n   * Whether the collection or one of its items should be automatically focused upon render.\n   * @default false\n   */\n  autoFocus?: boolean | FocusStrategy,\n  /**\n   * Whether focus should wrap around when the end/start is reached.\n   * @default false\n   */\n  shouldFocusWrap?: boolean,\n  /**\n   * Whether the collection allows empty selection.\n   * @default false\n   */\n  disallowEmptySelection?: boolean,\n  /**\n   * Whether the collection allows the user to select all items via keyboard shortcut.\n   * @default false\n   */\n  disallowSelectAll?: boolean,\n  /**\n   * Whether pressing the Escape should clear selection in the collection or not.\n   * @default 'clearSelection'\n   */\n  escapeKeyBehavior?: 'clearSelection' | 'none',\n  /**\n   * Whether selection should occur automatically on focus.\n   * @default false\n   */\n  selectOnFocus?: boolean,\n  /**\n   * Whether typeahead is disabled.\n   * @default false\n   */\n  disallowTypeAhead?: boolean,\n  /**\n   * Whether the collection items should use virtual focus instead of being focused directly.\n   */\n  shouldUseVirtualFocus?: boolean,\n  /**\n   * Whether navigation through tab key is enabled.\n   */\n  allowsTabNavigation?: boolean,\n  /**\n   * Whether the collection items are contained in a virtual scroller.\n   */\n  isVirtualized?: boolean,\n  /**\n   * The ref attached to the scrollable body. Used to provide automatic scrolling on item focus for non-virtualized collections.\n   * If not provided, defaults to the collection ref.\n   */\n  scrollRef?: RefObject<HTMLElement | null>,\n  /**\n   * The behavior of links in the collection.\n   * - 'action': link behaves like onAction.\n   * - 'selection': link follows selection interactions (e.g. if URL drives selection).\n   * - 'override': links override all other interactions (link items are not selectable).\n   * @default 'action'\n   */\n  linkBehavior?: 'action' | 'selection' | 'override'\n}\n\nexport interface SelectableCollectionAria {\n  /** Props for the collection element. */\n  collectionProps: DOMAttributes\n}\n\n/**\n * Handles interactions with selectable collections.\n */\nexport function useSelectableCollection(options: AriaSelectableCollectionOptions): SelectableCollectionAria {\n  let {\n    selectionManager: manager,\n    keyboardDelegate: delegate,\n    ref,\n    autoFocus = false,\n    shouldFocusWrap = false,\n    disallowEmptySelection = false,\n    disallowSelectAll = false,\n    escapeKeyBehavior = 'clearSelection',\n    selectOnFocus = manager.selectionBehavior === 'replace',\n    disallowTypeAhead = false,\n    shouldUseVirtualFocus,\n    allowsTabNavigation = false,\n    isVirtualized,\n    // If no scrollRef is provided, assume the collection ref is the scrollable region\n    scrollRef = ref,\n    linkBehavior = 'action'\n  } = options;\n  let {direction} = useLocale();\n  let router = useRouter();\n\n  let onKeyDown = (e: KeyboardEvent) => {\n    // Prevent option + tab from doing anything since it doesn't move focus to the cells, only buttons/checkboxes\n    if (e.altKey && e.key === 'Tab') {\n      e.preventDefault();\n    }\n\n    // Keyboard events bubble through portals. Don't handle keyboard events\n    // for elements outside the collection (e.g. menus).\n    if (!ref.current?.contains(e.target as Element)) {\n      return;\n    }\n\n    const navigateToKey = (key: Key | undefined, childFocus?: FocusStrategy) => {\n      if (key != null) {\n        if (manager.isLink(key) && linkBehavior === 'selection' && selectOnFocus && !isNonContiguousSelectionModifier(e)) {\n          // Set focused key and re-render synchronously to bring item into view if needed.\n          flushSync(() => {\n            manager.setFocusedKey(key, childFocus);\n          });\n\n          let item = getItemElement(ref, key);\n          let itemProps = manager.getItemProps(key);\n          if (item) {\n            router.open(item, e, itemProps.href, itemProps.routerOptions);\n          }\n\n          return;\n        }\n\n        manager.setFocusedKey(key, childFocus);\n\n        if (manager.isLink(key) && linkBehavior === 'override') {\n          return;\n        }\n\n        if (e.shiftKey && manager.selectionMode === 'multiple') {\n          manager.extendSelection(key);\n        } else if (selectOnFocus && !isNonContiguousSelectionModifier(e)) {\n          manager.replaceSelection(key);\n        }\n      }\n    };\n\n    switch (e.key) {\n      case 'ArrowDown': {\n        if (delegate.getKeyBelow) {\n          let nextKey = manager.focusedKey != null\n              ? delegate.getKeyBelow?.(manager.focusedKey)\n              : delegate.getFirstKey?.();\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = delegate.getFirstKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      }\n      case 'ArrowUp': {\n        if (delegate.getKeyAbove) {\n          let nextKey = manager.focusedKey != null\n              ? delegate.getKeyAbove?.(manager.focusedKey)\n              : delegate.getLastKey?.();\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = delegate.getLastKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      }\n      case 'ArrowLeft': {\n        if (delegate.getKeyLeftOf) {\n          let nextKey: Key | undefined | null = manager.focusedKey != null ? delegate.getKeyLeftOf?.(manager.focusedKey) : null;\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = direction === 'rtl' ? delegate.getFirstKey?.(manager.focusedKey) : delegate.getLastKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey, direction === 'rtl' ? 'first' : 'last');\n          }\n        }\n        break;\n      }\n      case 'ArrowRight': {\n        if (delegate.getKeyRightOf) {\n          let nextKey: Key | undefined | null = manager.focusedKey != null ? delegate.getKeyRightOf?.(manager.focusedKey) : null;\n          if (nextKey == null && shouldFocusWrap) {\n            nextKey = direction === 'rtl' ? delegate.getLastKey?.(manager.focusedKey) : delegate.getFirstKey?.(manager.focusedKey);\n          }\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey, direction === 'rtl' ? 'last' : 'first');\n          }\n        }\n        break;\n      }\n      case 'Home':\n        if (delegate.getFirstKey) {\n          if (manager.focusedKey === null && e.shiftKey) {\n            return;\n          }\n          e.preventDefault();\n          let firstKey: Key | null = delegate.getFirstKey(manager.focusedKey, isCtrlKeyPressed(e));\n          manager.setFocusedKey(firstKey);\n          if (firstKey != null) {\n            if (isCtrlKeyPressed(e) && e.shiftKey && manager.selectionMode === 'multiple') {\n              manager.extendSelection(firstKey);\n            } else if (selectOnFocus) {\n              manager.replaceSelection(firstKey);\n            }\n          }\n        }\n        break;\n      case 'End':\n        if (delegate.getLastKey) {\n          if (manager.focusedKey === null && e.shiftKey) {\n            return;\n          }\n          e.preventDefault();\n          let lastKey = delegate.getLastKey(manager.focusedKey, isCtrlKeyPressed(e));\n          manager.setFocusedKey(lastKey);\n          if (lastKey != null) {\n            if (isCtrlKeyPressed(e) && e.shiftKey && manager.selectionMode === 'multiple') {\n              manager.extendSelection(lastKey);\n            } else if (selectOnFocus) {\n              manager.replaceSelection(lastKey);\n            }\n          }\n        }\n        break;\n      case 'PageDown':\n        if (delegate.getKeyPageBelow && manager.focusedKey != null) {\n          let nextKey = delegate.getKeyPageBelow(manager.focusedKey);\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      case 'PageUp':\n        if (delegate.getKeyPageAbove && manager.focusedKey != null) {\n          let nextKey = delegate.getKeyPageAbove(manager.focusedKey);\n          if (nextKey != null) {\n            e.preventDefault();\n            navigateToKey(nextKey);\n          }\n        }\n        break;\n      case 'a':\n        if (isCtrlKeyPressed(e) && manager.selectionMode === 'multiple' && disallowSelectAll !== true) {\n          e.preventDefault();\n          manager.selectAll();\n        }\n        break;\n      case 'Escape':\n        if (escapeKeyBehavior === 'clearSelection' && !disallowEmptySelection && manager.selectedKeys.size !== 0) {\n          e.stopPropagation();\n          e.preventDefault();\n          manager.clearSelection();\n        }\n        break;\n      case 'Tab': {\n        if (!allowsTabNavigation) {\n          // There may be elements that are \"tabbable\" inside a collection (e.g. in a grid cell).\n          // However, collections should be treated as a single tab stop, with arrow key navigation internally.\n          // We don't control the rendering of these, so we can't override the tabIndex to prevent tabbing.\n          // Instead, we handle the Tab key, and move focus manually to the first/last tabbable element\n          // in the collection, so that the browser default behavior will apply starting from that element\n          // rather than the currently focused one.\n          if (e.shiftKey) {\n            ref.current.focus();\n          } else {\n            let walker = getFocusableTreeWalker(ref.current, {tabbable: true});\n            let next: FocusableElement | undefined = undefined;\n            let last: FocusableElement;\n            do {\n              last = walker.lastChild() as FocusableElement;\n              if (last) {\n                next = last;\n              }\n            } while (last);\n\n            if (next && !next.contains(document.activeElement)) {\n              focusWithoutScrolling(next);\n            }\n          }\n          break;\n        }\n      }\n    }\n  };\n\n  // Store the scroll position so we can restore it later.\n  /// TODO: should this happen all the time??\n  let scrollPos = useRef({top: 0, left: 0});\n  useEvent(scrollRef, 'scroll', isVirtualized ? undefined : () => {\n    scrollPos.current = {\n      top: scrollRef.current?.scrollTop ?? 0,\n      left: scrollRef.current?.scrollLeft ?? 0\n    };\n  });\n\n  let onFocus = (e: FocusEvent) => {\n    if (manager.isFocused) {\n      // If a focus event bubbled through a portal, reset focus state.\n      if (!e.currentTarget.contains(e.target)) {\n        manager.setFocused(false);\n      }\n\n      return;\n    }\n\n    // Focus events can bubble through portals. Ignore these events.\n    if (!e.currentTarget.contains(e.target)) {\n      return;\n    }\n\n    manager.setFocused(true);\n    if (manager.focusedKey == null) {\n      let navigateToKey = (key: Key | undefined | null) => {\n        if (key != null) {\n          manager.setFocusedKey(key);\n          if (selectOnFocus && !manager.isSelected(key)) {\n            manager.replaceSelection(key);\n          }\n        }\n      };\n      // If the user hasn't yet interacted with the collection, there will be no focusedKey set.\n      // Attempt to detect whether the user is tabbing forward or backward into the collection\n      // and either focus the first or last item accordingly.\n      let relatedTarget = e.relatedTarget as Element;\n      if (relatedTarget && (e.currentTarget.compareDocumentPosition(relatedTarget) & Node.DOCUMENT_POSITION_FOLLOWING)) {\n        navigateToKey(manager.lastSelectedKey ?? delegate.getLastKey?.());\n      } else {\n        navigateToKey(manager.firstSelectedKey ?? delegate.getFirstKey?.());\n      }\n    } else if (!isVirtualized && scrollRef.current) {\n      // Restore the scroll position to what it was before.\n      scrollRef.current.scrollTop = scrollPos.current.top;\n      scrollRef.current.scrollLeft = scrollPos.current.left;\n    }\n\n    if (manager.focusedKey != null && scrollRef.current) {\n      // Refocus and scroll the focused item into view if it exists within the scrollable region.\n      let element = getItemElement(ref, manager.focusedKey);\n      if (element instanceof HTMLElement) {\n        // This prevents a flash of focus on the first/last element in the collection, or the collection itself.\n        if (!element.contains(document.activeElement) && !shouldUseVirtualFocus) {\n          focusWithoutScrolling(element);\n        }\n\n        let modality = getInteractionModality();\n        if (modality === 'keyboard') {\n          scrollIntoViewport(element, {containingElement: ref.current});\n        }\n      }\n    }\n  };\n\n  let onBlur = (e) => {\n    // Don't set blurred and then focused again if moving focus within the collection.\n    if (!e.currentTarget.contains(e.relatedTarget as HTMLElement)) {\n      manager.setFocused(false);\n    }\n  };\n\n  // Ref to track whether the first item in the collection should be automatically focused. Specifically used for autocomplete when user types\n  // to focus the first key AFTER the collection updates.\n  // TODO: potentially expand the usage of this\n  let shouldVirtualFocusFirst = useRef(false);\n  // Add event listeners for custom virtual events. These handle updating the focused key in response to various keyboard events\n  // at the autocomplete level\n  // TODO: fix type later\n  useEvent(ref, FOCUS_EVENT, !shouldUseVirtualFocus ? undefined : (e: any) => {\n    let {detail} = e;\n    e.stopPropagation();\n    manager.setFocused(true);\n    // If the user is typing forwards, autofocus the first option in the list.\n    if (detail?.focusStrategy === 'first') {\n      shouldVirtualFocusFirst.current = true;\n    }\n  });\n\n  let updateActiveDescendant = useEffectEvent(() => {\n    let keyToFocus = delegate.getFirstKey?.() ?? null;\n\n    // If no focusable items exist in the list, make sure to clear any activedescendant that may still exist and move focus back to\n    // the original active element (e.g. the autocomplete input)\n    if (keyToFocus == null) {\n      let previousActiveElement = getActiveElement();\n      moveVirtualFocus(ref.current);\n      dispatchVirtualFocus(previousActiveElement!, null);\n\n      // If there wasn't a focusable key but the collection had items, then that means we aren't in an intermediate load state and all keys are disabled.\n      // Reset shouldVirtualFocusFirst so that we don't erronously autofocus an item when the collection is filtered again.\n      if (manager.collection.size > 0) {\n        shouldVirtualFocusFirst.current = false;\n      }\n    } else {\n      manager.setFocusedKey(keyToFocus);\n      // Only set shouldVirtualFocusFirst to false if we've successfully set the first key as the focused key\n      // If there wasn't a key to focus, we might be in a temporary loading state so we'll want to still focus the first key\n      // after the collection updates after load\n      shouldVirtualFocusFirst.current = false;\n    }\n  });\n\n  useUpdateLayoutEffect(() => {\n    if (shouldVirtualFocusFirst.current) {\n      updateActiveDescendant();\n    }\n\n  }, [manager.collection, updateActiveDescendant]);\n\n  let resetFocusFirstFlag = useEffectEvent(() => {\n    // If user causes the focused key to change in any other way, clear shouldVirtualFocusFirst so we don't\n    // accidentally move focus from under them. Skip this if the collection was empty because we might be in a load\n    // state and will still want to focus the first item after load\n    if (manager.collection.size > 0) {\n      shouldVirtualFocusFirst.current = false;\n    }\n  });\n\n  useUpdateLayoutEffect(() => {\n    resetFocusFirstFlag();\n  }, [manager.focusedKey, resetFocusFirstFlag]);\n\n  useEvent(ref, CLEAR_FOCUS_EVENT, !shouldUseVirtualFocus ? undefined : (e: any) => {\n    e.stopPropagation();\n    manager.setFocused(false);\n    if (e.detail?.clearFocusKey) {\n      manager.setFocusedKey(null);\n    }\n  });\n\n  const autoFocusRef = useRef(autoFocus);\n  const didAutoFocusRef = useRef(false);\n  useEffect(() => {\n    if (autoFocusRef.current) {\n      let focusedKey: Key | null = null;\n\n      // Check focus strategy to determine which item to focus\n      if (autoFocus === 'first') {\n        focusedKey = delegate.getFirstKey?.() ?? null;\n      } if (autoFocus === 'last') {\n        focusedKey = delegate.getLastKey?.() ?? null;\n      }\n\n      // If there are any selected keys, make the first one the new focus target\n      let selectedKeys = manager.selectedKeys;\n      if (selectedKeys.size) {\n        for (let key of selectedKeys) {\n          if (manager.canSelectItem(key)) {\n            focusedKey = key;\n            break;\n          }\n        }\n      }\n\n      manager.setFocused(true);\n      manager.setFocusedKey(focusedKey);\n\n      // If no default focus key is selected, focus the collection itself.\n      if (focusedKey == null && !shouldUseVirtualFocus && ref.current) {\n        focusSafely(ref.current);\n      }\n\n      // Wait until the collection has items to autofocus.\n      if (manager.collection.size > 0) {\n        autoFocusRef.current = false;\n        didAutoFocusRef.current = true;\n      }\n    }\n  });\n\n  // Scroll the focused element into view when the focusedKey changes.\n  let lastFocusedKey = useRef(manager.focusedKey);\n  let raf = useRef<number | null>(null);\n  useEffect(() => {\n    if (manager.isFocused && manager.focusedKey != null && (manager.focusedKey !== lastFocusedKey.current || didAutoFocusRef.current) && scrollRef.current && ref.current) {\n      let modality = getInteractionModality();\n      let element = getItemElement(ref, manager.focusedKey);\n      if (!(element instanceof HTMLElement)) {\n        // If item element wasn't found, return early (don't update autoFocusRef and lastFocusedKey).\n        // The collection may initially be empty (e.g. virtualizer), so wait until the element exists.\n        return;\n      }\n\n      if (modality === 'keyboard' || didAutoFocusRef.current) {\n\n        if (raf.current) {\n          cancelAnimationFrame(raf.current);\n        }\n\n        raf.current = requestAnimationFrame(() => {\n          if (scrollRef.current) {\n            scrollIntoView(scrollRef.current, element);\n            // Avoid scroll in iOS VO, since it may cause overlay to close (i.e. RAC submenu)\n            if (modality !== 'virtual') {\n              scrollIntoViewport(element, {containingElement: ref.current});\n            }\n          }\n        });\n      }\n    }\n\n    // If the focused key becomes null (e.g. the last item is deleted), focus the whole collection.\n    if (!shouldUseVirtualFocus && manager.isFocused && manager.focusedKey == null && lastFocusedKey.current != null && ref.current) {\n      focusSafely(ref.current);\n    }\n\n    lastFocusedKey.current = manager.focusedKey;\n    didAutoFocusRef.current = false;\n  });\n\n  useEffect(() => {\n    return () => {\n      if (raf.current) {\n        cancelAnimationFrame(raf.current);\n      }\n    };\n  }, []);\n\n  // Intercept FocusScope restoration since virtualized collections can reuse DOM nodes.\n  useEvent(ref, 'react-aria-focus-scope-restore', e => {\n    e.preventDefault();\n    manager.setFocused(true);\n  });\n\n  let handlers = {\n    onKeyDown,\n    onFocus,\n    onBlur,\n    onMouseDown(e) {\n      // Ignore events that bubbled through portals.\n      if (scrollRef.current === e.target) {\n        // Prevent focus going to the collection when clicking on the scrollbar.\n        e.preventDefault();\n      }\n    }\n  };\n\n  let {typeSelectProps} = useTypeSelect({\n    keyboardDelegate: delegate,\n    selectionManager: manager\n  });\n\n  if (!disallowTypeAhead) {\n    handlers = mergeProps(typeSelectProps, handlers);\n  }\n\n  // If nothing is focused within the collection, make the collection itself tabbable.\n  // This will be marshalled to either the first or last item depending on where focus came from.\n  let tabIndex: number | undefined = undefined;\n  if (!shouldUseVirtualFocus) {\n    tabIndex = manager.focusedKey == null ? 0 : -1;\n  }\n\n  let collectionId = useCollectionId(manager.collection);\n  return {\n    collectionProps: mergeProps(handlers, {\n      tabIndex,\n      'data-collection': collectionId\n    })\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAgGM,SAAS,0CAAwB,OAAwC;IAC9E,IAAI,EACF,kBAAkB,OAAO,EACzB,kBAAkB,QAAQ,EAAA,KAC1B,GAAG,EAAA,WACH,YAAY,KAAA,EAAA,iBACZ,kBAAkB,KAAA,EAAA,wBAClB,yBAAyB,KAAA,EAAA,mBACzB,oBAAoB,KAAA,EAAA,mBACpB,oBAAoB,gBAAA,EAAA,eACpB,gBAAgB,QAAQ,iBAAiB,KAAK,SAAA,EAAA,mBAC9C,oBAAoB,KAAA,EAAA,uBACpB,qBAAqB,EAAA,qBACrB,sBAAsB,KAAA,EAAA,eACtB,aAAa,EAAA,WACb,AACA,YAAY,GAAA,EAAA,cACZ,eAAe,QAAA,EAChB,GAAG,uBAHgF;IAIpF,IAAI,EAAA,WAAC,SAAS,EAAC,GAAG,CAAA,kKAAA,YAAQ;IAC1B,IAAI,SAAS,CAAA,oKAAA,YAAQ;IAErB,IAAI,YAAY,CAAC;YAQV;QAPL,6GAA6G;QAC7G,IAAI,EAAE,MAAM,IAAI,EAAE,GAAG,KAAK,OACxB,EAAE,cAAc;QAGlB,uEAAuE;QACvE,oDAAoD;QACpD,IAAI,CAAA,CAAA,CAAC,eAAA,IAAI,OAAO,MAAA,QAAX,iBAAA,KAAA,IAAA,KAAA,IAAA,aAAa,QAAQ,CAAC,EAAE,MAAM,CAAA,GACjC;QAGF,MAAM,gBAAgB,CAAC,KAAsB;YAC3C,IAAI,OAAO,MAAM;gBACf,IAAI,QAAQ,MAAM,CAAC,QAAQ,iBAAiB,eAAe,iBAAiB,CAAC,CAAA,qKAAA,mCAA+B,EAAE,IAAI;oBAChH,iFAAiF;oBACjF,CAAA,wKAAA,YAAQ,EAAE;wBACR,QAAQ,aAAa,CAAC,KAAK;oBAC7B;oBAEA,IAAI,OAAO,CAAA,qKAAA,iBAAa,EAAE,KAAK;oBAC/B,IAAI,YAAY,QAAQ,YAAY,CAAC;oBACrC,IAAI,MACF,OAAO,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,EAAE,UAAU,aAAa;oBAG9D;gBACF;gBAEA,QAAQ,aAAa,CAAC,KAAK;gBAE3B,IAAI,QAAQ,MAAM,CAAC,QAAQ,iBAAiB,YAC1C;gBAGF,IAAI,EAAE,QAAQ,IAAI,QAAQ,aAAa,KAAK,YAC1C,QAAQ,eAAe,CAAC;qBACnB,IAAI,iBAAiB,CAAC,CAAA,qKAAA,mCAA+B,EAAE,IAC5D,QAAQ,gBAAgB,CAAC;YAE7B;QACF;QAEA,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,IAAI,SAAS,WAAW,EAAE;wBAElB,uBACA,uBAEM;oBAJZ,IAAI,UAAU,QAAQ,UAAU,IAAI,OAAA,CAC9B,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,UAAuB,QAAQ,UAAU,IAAA,CACzC,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA;oBACN,IAAI,WAAW,QAAQ,iBACrB,UAAA,CAAU,yBAAA,SAAS,WAAW,MAAA,QAApB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAA,IAAA,CAAA,UAAuB,QAAQ,UAAU;oBAErD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,WAAW,EAAE;wBAElB,uBACA,sBAEM;oBAJZ,IAAI,UAAU,QAAQ,UAAU,IAAI,OAAA,CAC9B,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,UAAuB,QAAQ,UAAU,IAAA,CACzC,uBAAA,SAAS,UAAU,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAA,IAAA,CAAA;oBACN,IAAI,WAAW,QAAQ,iBACrB,UAAA,CAAU,wBAAA,SAAS,UAAU,MAAA,QAAnB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,UAAsB,QAAQ,UAAU;oBAEpD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,YAAY,EAAE;wBAC0C,wBAEjC,wBAA6C;oBAF/E,IAAI,UAAkC,QAAQ,UAAU,IAAI,OAAA,CAAO,yBAAA,SAAS,YAAY,MAAA,QAArB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAA,IAAA,CAAA,UAAwB,QAAQ,UAAU,IAAI;oBACjH,IAAI,WAAW,QAAQ,iBACrB,UAAU,cAAc,QAAA,CAAQ,yBAAA,SAAS,WAAW,MAAA,QAApB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAA,IAAA,CAAA,UAAuB,QAAQ,UAAU,IAAA,CAAI,wBAAA,SAAS,UAAU,MAAA,QAAnB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,UAAsB,QAAQ,UAAU;oBAEvH,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc,SAAS,cAAc,QAAQ,UAAU;oBACzD;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,aAAa,EAAE;wBACyC,yBAEjC,uBAA4C;oBAF9E,IAAI,UAAkC,QAAQ,UAAU,IAAI,OAAA,CAAO,0BAAA,SAAS,aAAa,MAAA,QAAtB,4BAAA,KAAA,IAAA,KAAA,IAAA,wBAAA,IAAA,CAAA,UAAyB,QAAQ,UAAU,IAAI;oBAClH,IAAI,WAAW,QAAQ,iBACrB,UAAU,cAAc,QAAA,CAAQ,wBAAA,SAAS,UAAU,MAAA,QAAnB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,UAAsB,QAAQ,UAAU,IAAA,CAAI,yBAAA,SAAS,WAAW,MAAA,QAApB,2BAAA,KAAA,IAAA,KAAA,IAAA,uBAAA,IAAA,CAAA,UAAuB,QAAQ,UAAU;oBAEvH,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc,SAAS,cAAc,QAAQ,SAAS;oBACxD;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS,WAAW,EAAE;oBACxB,IAAI,QAAQ,UAAU,KAAK,QAAQ,EAAE,QAAQ,EAC3C;oBAEF,EAAE,cAAc;oBAChB,IAAI,WAAuB,SAAS,WAAW,CAAC,QAAQ,UAAU,EAAE,CAAA,oKAAA,mBAAe,EAAE;oBACrF,QAAQ,aAAa,CAAC;oBACtB,IAAI,YAAY,MAAM;wBACpB,IAAI,CAAA,oKAAA,mBAAe,EAAE,MAAM,EAAE,QAAQ,IAAI,QAAQ,aAAa,KAAK,YACjE,QAAQ,eAAe,CAAC;6BACnB,IAAI,eACT,QAAQ,gBAAgB,CAAC;oBAE7B;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,SAAS,UAAU,EAAE;oBACvB,IAAI,QAAQ,UAAU,KAAK,QAAQ,EAAE,QAAQ,EAC3C;oBAEF,EAAE,cAAc;oBAChB,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,UAAU,EAAE,CAAA,oKAAA,mBAAe,EAAE;oBACvE,QAAQ,aAAa,CAAC;oBACtB,IAAI,WAAW,MAAM;wBACnB,IAAI,CAAA,oKAAA,mBAAe,EAAE,MAAM,EAAE,QAAQ,IAAI,QAAQ,aAAa,KAAK,YACjE,QAAQ,eAAe,CAAC;6BACnB,IAAI,eACT,QAAQ,gBAAgB,CAAC;oBAE7B;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,SAAS,eAAe,IAAI,QAAQ,UAAU,IAAI,MAAM;oBAC1D,IAAI,UAAU,SAAS,eAAe,CAAC,QAAQ,UAAU;oBACzD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,SAAS,eAAe,IAAI,QAAQ,UAAU,IAAI,MAAM;oBAC1D,IAAI,UAAU,SAAS,eAAe,CAAC,QAAQ,UAAU;oBACzD,IAAI,WAAW,MAAM;wBACnB,EAAE,cAAc;wBAChB,cAAc;oBAChB;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,CAAA,oKAAA,mBAAe,EAAE,MAAM,QAAQ,aAAa,KAAK,cAAc,sBAAsB,MAAM;oBAC7F,EAAE,cAAc;oBAChB,QAAQ,SAAS;gBACnB;gBACA;YACF,KAAK;gBACH,IAAI,sBAAsB,oBAAoB,CAAC,0BAA0B,QAAQ,YAAY,CAAC,IAAI,KAAK,GAAG;oBACxG,EAAE,eAAe;oBACjB,EAAE,cAAc;oBAChB,QAAQ,cAAc;gBACxB;gBACA;YACF,KAAK;gBACH,IAAI,CAAC,qBAAqB;oBACxB,uFAAuF;oBACvF,qGAAqG;oBACrG,iGAAiG;oBACjG,6FAA6F;oBAC7F,gGAAgG;oBAChG,yCAAyC;oBACzC,IAAI,EAAE,QAAQ,EACZ,IAAI,OAAO,CAAC,KAAK;yBACZ;wBACL,IAAI,SAAS,CAAA,sKAAA,yBAAqB,EAAE,IAAI,OAAO,EAAE;4BAAC,UAAU;wBAAI;wBAChE,IAAI,OAAqC;wBACzC,IAAI;wBACJ,GAAG;4BACD,OAAO,OAAO,SAAS;4BACvB,IAAI,MACF,OAAO;wBAEX,QAAS,KAAM;wBAEf,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,SAAS,aAAa,GAC/C,CAAA,iLAAA,wBAAoB,EAAE;oBAE1B;oBACA;gBACF;QAEJ;IACF;IAEA,wDAAwD;IACxD,2CAA2C;IAC3C,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE;QAAC,KAAK;QAAG,MAAM;IAAC;IACvC,CAAA,oKAAA,WAAO,EAAE,WAAW,UAAU,gBAAgB,YAAY;YAEjD,oBACC;YADD,8BACC;QAFR,UAAU,OAAO,GAAG;YAClB,KAAK,CAAA,+BAAA,CAAA,qBAAA,UAAU,OAAO,MAAA,QAAjB,uBAAA,KAAA,IAAA,KAAA,IAAA,mBAAmB,SAAS,MAAA,QAA5B,iCAAA,KAAA,IAAA,+BAAgC;YACrC,MAAM,CAAA,gCAAA,CAAA,sBAAA,UAAU,OAAO,MAAA,QAAjB,wBAAA,KAAA,IAAA,KAAA,IAAA,oBAAmB,UAAU,MAAA,QAA7B,kCAAA,KAAA,IAAA,gCAAiC;QACzC;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,QAAQ,SAAS,EAAE;YACrB,gEAAgE;YAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC,QAAQ,UAAU,CAAC;YAGrB;QACF;QAEA,gEAAgE;QAChE,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GACpC;QAGF,QAAQ,UAAU,CAAC;QACnB,IAAI,QAAQ,UAAU,IAAI,MAAM;gBAca,sBAEC;YAf5C,IAAI,gBAAgB,CAAC;gBACnB,IAAI,OAAO,MAAM;oBACf,QAAQ,aAAa,CAAC;oBACtB,IAAI,iBAAiB,CAAC,QAAQ,UAAU,CAAC,MACvC,QAAQ,gBAAgB,CAAC;gBAE7B;YACF;YACA,0FAA0F;YAC1F,wFAAwF;YACxF,uDAAuD;YACvD,IAAI,gBAAgB,EAAE,aAAa;gBAEnB,0BAEA;YAHhB,IAAI,iBAAkB,EAAE,aAAa,CAAC,uBAAuB,CAAC,iBAAiB,KAAK,2BAA2B,EAC7G,cAAc,CAAA,2BAAA,QAAQ,eAAe,MAAA,QAAvB,6BAAA,KAAA,IAAA,2BAAA,CAA2B,uBAAA,SAAS,UAAU,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAA,IAAA,CAAA;iBAEzC,cAAc,CAAA,4BAAA,QAAQ,gBAAgB,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAAA,CAA4B,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA;QAE9C,OAAO,IAAI,CAAC,iBAAiB,UAAU,OAAO,EAAE;YAC9C,qDAAqD;YACrD,UAAU,OAAO,CAAC,SAAS,GAAG,UAAU,OAAO,CAAC,GAAG;YACnD,UAAU,OAAO,CAAC,UAAU,GAAG,UAAU,OAAO,CAAC,IAAI;QACvD;QAEA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,OAAO,EAAE;YACnD,2FAA2F;YAC3F,IAAI,UAAU,CAAA,qKAAA,iBAAa,EAAE,KAAK,QAAQ,UAAU;YACpD,IAAI,mBAAmB,aAAa;gBAClC,wGAAwG;gBACxG,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS,aAAa,KAAK,CAAC,uBAChD,CAAA,iLAAA,wBAAoB,EAAE;gBAGxB,IAAI,WAAW,CAAA,kLAAA,yBAAqB;gBACpC,IAAI,aAAa,YACf,CAAA,0KAAA,qBAAiB,EAAE,SAAS;oBAAC,mBAAmB,IAAI,OAAO;gBAAA;YAE/D;QACF;IACF;IAEA,IAAI,SAAS,CAAC;QACZ,kFAAkF;QAClF,IAAI,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,aAAa,GAC3C,QAAQ,UAAU,CAAC;IAEvB;IAEA,4IAA4I;IAC5I,uDAAuD;IACvD,6CAA6C;IAC7C,IAAI,0BAA0B,CAAA,iKAAA,SAAK,EAAE;IACrC,8HAA8H;IAC9H,4BAA4B;IAC5B,uBAAuB;IACvB,CAAA,oKAAA,WAAO,EAAE,KAAK,CAAA,qKAAA,cAAU,GAAG,CAAC,wBAAwB,YAAY,CAAC;QAC/D,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG;QACf,EAAE,eAAe;QACjB,QAAQ,UAAU,CAAC;QACnB,0EAA0E;QAC1E,IAAI,CAAA,WAAA,QAAA,WAAA,KAAA,IAAA,KAAA,IAAA,OAAQ,aAAa,MAAK,SAC5B,wBAAwB,OAAO,GAAG;IAEtC;IAEA,IAAI,yBAAyB,CAAA,0KAAA,iBAAa,EAAE;YACzB;YAAA;QAAjB,IAAI,aAAa,CAAA,yBAAA,CAAA,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,SAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAA4B;QAE7C,+HAA+H;QAC/H,4DAA4D;QAC5D,IAAI,cAAc,MAAM;YACtB,IAAI,wBAAwB,CAAA,wKAAA,mBAAe;YAC3C,CAAA,wKAAA,mBAAe,EAAE,IAAI,OAAO;YAC5B,CAAA,wKAAA,uBAAmB,EAAE,uBAAwB;YAE7C,mJAAmJ;YACnJ,qHAAqH;YACrH,IAAI,QAAQ,UAAU,CAAC,IAAI,GAAG,GAC5B,wBAAwB,OAAO,GAAG;QAEtC,OAAO;YACL,QAAQ,aAAa,CAAC;YACtB,uGAAuG;YACvG,sHAAsH;YACtH,0CAA0C;YAC1C,wBAAwB,OAAO,GAAG;QACpC;IACF;IAEA,CAAA,iLAAA,wBAAoB,EAAE;QACpB,IAAI,wBAAwB,OAAO,EACjC;IAGJ,GAAG;QAAC,QAAQ,UAAU;QAAE;KAAuB;IAE/C,IAAI,sBAAsB,CAAA,0KAAA,iBAAa,EAAE;QACvC,uGAAuG;QACvG,+GAA+G;QAC/G,+DAA+D;QAC/D,IAAI,QAAQ,UAAU,CAAC,IAAI,GAAG,GAC5B,wBAAwB,OAAO,GAAG;IAEtC;IAEA,CAAA,iLAAA,wBAAoB,EAAE;QACpB;IACF,GAAG;QAAC,QAAQ,UAAU;QAAE;KAAoB;IAE5C,CAAA,oKAAA,WAAO,EAAE,KAAK,CAAA,qKAAA,oBAAgB,GAAG,CAAC,wBAAwB,YAAY,CAAC;YAGjE;QAFJ,EAAE,eAAe;QACjB,QAAQ,UAAU,CAAC;QACnB,IAAA,CAAI,YAAA,EAAE,MAAM,MAAA,QAAR,cAAA,KAAA,IAAA,KAAA,IAAA,UAAU,aAAa,EACzB,QAAQ,aAAa,CAAC;IAE1B;IAEA,MAAM,eAAe,CAAA,iKAAA,SAAK,EAAE;IAC5B,MAAM,kBAAkB,CAAA,iKAAA,SAAK,EAAE;IAC/B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;gBAKT,uBAEA;YANf,IAAI,aAAyB;gBAId;YAFf,wDAAwD;YACxD,IAAI,cAAc,SAChB,aAAa,CAAA,yBAAA,CAAA,wBAAA,SAAS,WAAW,MAAA,QAApB,0BAAA,KAAA,IAAA,KAAA,IAAA,sBAAA,IAAA,CAAA,SAAA,MAAA,QAAA,2BAAA,KAAA,IAAA,yBAA4B;gBAE5B;YADb,IAAI,cAAc,QAClB,aAAa,CAAA,wBAAA,CAAA,uBAAA,SAAS,UAAU,MAAA,QAAnB,yBAAA,KAAA,IAAA,KAAA,IAAA,qBAAA,IAAA,CAAA,SAAA,MAAA,QAAA,0BAAA,KAAA,IAAA,wBAA2B;YAG1C,0EAA0E;YAC1E,IAAI,eAAe,QAAQ,YAAY;YACvC,IAAI,aAAa,IAAI,EAAE;gBACrB,KAAK,IAAI,OAAO,aACd,IAAI,QAAQ,aAAa,CAAC,MAAM;oBAC9B,aAAa;oBACb;gBACF;YAEJ;YAEA,QAAQ,UAAU,CAAC;YACnB,QAAQ,aAAa,CAAC;YAEtB,oEAAoE;YACpE,IAAI,cAAc,QAAQ,CAAC,yBAAyB,IAAI,OAAO,EAC7D,CAAA,8KAAA,cAAU,EAAE,IAAI,OAAO;YAGzB,oDAAoD;YACpD,IAAI,QAAQ,UAAU,CAAC,IAAI,GAAG,GAAG;gBAC/B,aAAa,OAAO,GAAG;gBACvB,gBAAgB,OAAO,GAAG;YAC5B;QACF;IACF;IAEA,oEAAoE;IACpE,IAAI,iBAAiB,CAAA,iKAAA,SAAK,EAAE,QAAQ,UAAU;IAC9C,IAAI,MAAM,CAAA,iKAAA,SAAK,EAAiB;IAChC,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAS,CAAA,QAAQ,UAAU,KAAK,eAAe,OAAO,IAAI,gBAAgB,OAAM,KAAM,UAAU,OAAO,IAAI,IAAI,OAAO,EAAE;YACrK,IAAI,WAAW,CAAA,kLAAA,yBAAqB;YACpC,IAAI,UAAU,CAAA,qKAAA,iBAAa,EAAE,KAAK,QAAQ,UAAU;YACpD,IAAI,CAAE,CAAA,mBAAmB,WAAU,GACjC,AACA,6FAD6F,CACC;YAC9F;YAGF,IAAI,aAAa,cAAc,gBAAgB,OAAO,EAAE;gBAEtD,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;gBAGlC,IAAI,OAAO,GAAG,sBAAsB;oBAClC,IAAI,UAAU,OAAO,EAAE;wBACrB,CAAA,0KAAA,iBAAa,EAAE,UAAU,OAAO,EAAE;wBAClC,iFAAiF;wBACjF,IAAI,aAAa,WACf,CAAA,0KAAA,qBAAiB,EAAE,SAAS;4BAAC,mBAAmB,IAAI,OAAO;wBAAA;oBAE/D;gBACF;YACF;QACF;QAEA,+FAA+F;QAC/F,IAAI,CAAC,yBAAyB,QAAQ,SAAS,IAAI,QAAQ,UAAU,IAAI,QAAQ,eAAe,OAAO,IAAI,QAAQ,IAAI,OAAO,EAC5H,CAAA,8KAAA,cAAU,EAAE,IAAI,OAAO;QAGzB,eAAe,OAAO,GAAG,QAAQ,UAAU;QAC3C,gBAAgB,OAAO,GAAG;IAC5B;IAEA,CAAA,iKAAA,YAAQ,EAAE;QACR,OAAO;YACL,IAAI,IAAI,OAAO,EACb,qBAAqB,IAAI,OAAO;QAEpC;IACF,GAAG,EAAE;IAEL,sFAAsF;IACtF,CAAA,oKAAA,WAAO,EAAE,KAAK,kCAAkC,CAAA;QAC9C,EAAE,cAAc;QAChB,QAAQ,UAAU,CAAC;IACrB;IAEA,IAAI,WAAW;mBACb;iBACA;gBACA;QACA,aAAY,CAAC;YACX,8CAA8C;YAC9C,IAAI,UAAU,OAAO,KAAK,EAAE,MAAM,EAChC,AACA,EAAE,cAAc,wDADwD;QAG5E;IACF;IAEA,IAAI,EAAA,iBAAC,eAAe,EAAC,GAAG,CAAA,6KAAA,gBAAY,EAAE;QACpC,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,IAAI,CAAC,mBACH,WAAW,CAAA,sKAAA,aAAS,EAAE,iBAAiB;IAGzC,oFAAoF;IACpF,+FAA+F;IAC/F,IAAI,WAA+B;IACnC,IAAI,CAAC,uBACH,WAAW,QAAQ,UAAU,IAAI,OAAO,IAAI,CAAA;IAG9C,IAAI,eAAe,CAAA,qKAAA,kBAAc,EAAE,QAAQ,UAAU;IACrD,OAAO;QACL,iBAAiB,CAAA,sKAAA,aAAS,EAAE,UAAU;sBACpC;YACA,mBAAmB;QACrB;IACF;AACF", "debugId": null}}, {"offset": {"line": 6558, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/DOMLayoutDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {getItemElement} from './utils';\nimport {Key, LayoutDelegate, Rect, RefObject, Size} from '@react-types/shared';\n\nexport class DOMLayoutDelegate implements LayoutDelegate {\n  private ref: RefObject<HTMLElement | null>;\n\n  constructor(ref: RefObject<HTMLElement | null>) {\n    this.ref = ref;\n  }\n\n  getItemRect(key: Key): Rect | null {\n    let container = this.ref.current;\n    if (!container) {\n      return null;\n    }\n    let item = key != null ? getItemElement(this.ref, key) : null;\n    if (!item) {\n      return null;\n    }\n\n    let containerRect = container.getBoundingClientRect();\n    let itemRect = item.getBoundingClientRect();\n\n    return {\n      x: itemRect.left - containerRect.left + container.scrollLeft,\n      y: itemRect.top - containerRect.top + container.scrollTop,\n      width: itemRect.width,\n      height: itemRect.height\n    };\n  }\n\n  getContentSize(): Size {\n    let container = this.ref.current;\n    return {\n      width: container?.scrollWidth ?? 0,\n      height: container?.scrollHeight ?? 0\n    };\n  }\n\n  getVisibleRect(): Rect {\n    let container = this.ref.current;\n    return {\n      x: container?.scrollLeft ?? 0,\n      y: container?.scrollTop ?? 0,\n      width: container?.offsetWidth ?? 0,\n      height: container?.offsetHeight ?? 0\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAKM,MAAM;IAOX,YAAY,GAAQ,EAAe;QACjC,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO;QAChC,IAAI,CAAC,WACH,OAAO;QAET,IAAI,OAAO,OAAO,OAAO,CAAA,qKAAA,iBAAa,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO;QACzD,IAAI,CAAC,MACH,OAAO;QAGT,IAAI,gBAAgB,UAAU,qBAAqB;QACnD,IAAI,WAAW,KAAK,qBAAqB;QAEzC,OAAO;YACL,GAAG,SAAS,IAAI,GAAG,cAAc,IAAI,GAAG,UAAU,UAAU;YAC5D,GAAG,SAAS,GAAG,GAAG,cAAc,GAAG,GAAG,UAAU,SAAS;YACzD,OAAO,SAAS,KAAK;YACrB,QAAQ,SAAS,MAAM;QACzB;IACF;IAEA,iBAAuB;QACrB,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO;YAEvB,wBACC;QAFV,OAAO;YACL,OAAO,CAAA,yBAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,WAAW,MAAA,QAAtB,2BAAA,KAAA,IAAA,yBAA0B;YACjC,QAAQ,CAAA,0BAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,YAAY,MAAA,QAAvB,4BAAA,KAAA,IAAA,0BAA2B;QACrC;IACF;IAEA,iBAAuB;QACrB,IAAI,YAAY,IAAI,CAAC,GAAG,CAAC,OAAO;YAE3B,uBACA,sBACI,wBACC;QAJV,OAAO;YACL,GAAG,CAAA,wBAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,UAAU,MAAA,QAArB,0BAAA,KAAA,IAAA,wBAAyB;YAC5B,GAAG,CAAA,uBAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,SAAS,MAAA,QAApB,yBAAA,KAAA,IAAA,uBAAwB;YAC3B,OAAO,CAAA,yBAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,WAAW,MAAA,QAAtB,2BAAA,KAAA,IAAA,yBAA0B;YACjC,QAAQ,CAAA,0BAAA,cAAA,QAAA,cAAA,KAAA,IAAA,KAAA,IAAA,UAAW,YAAY,MAAA,QAAvB,4BAAA,KAAA,IAAA,0BAA2B;QACrC;IACF;IAzCA,YAAY,GAAkC,CAAE;QAC9C,IAAI,CAAC,GAAG,GAAG;IACb;AAwCF", "debugId": null}}, {"offset": {"line": 6616, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/ListKeyboardDelegate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Collection, Direction, DisabledBehavior, Key, KeyboardDelegate, LayoutDelegate, Node, Orientation, Rect, RefObject} from '@react-types/shared';\nimport {DOMLayoutDelegate} from './DOMLayoutDelegate';\nimport {isScrollable} from '@react-aria/utils';\n\ninterface ListKeyboardDelegateOptions<T> {\n  collection: Collection<Node<T>>,\n  ref: RefObject<HTMLElement | null>,\n  collator?: Intl.Collator,\n  layout?: 'stack' | 'grid',\n  orientation?: Orientation,\n  direction?: Direction,\n  disabledKeys?: Set<Key>,\n  disabledBehavior?: DisabledBehavior,\n  layoutDelegate?: LayoutDelegate\n}\n\nexport class ListKeyboardDelegate<T> implements KeyboardDelegate {\n  private collection: Collection<Node<T>>;\n  private disabledKeys: Set<Key>;\n  private disabledBehavior: DisabledBehavior;\n  private ref: RefObject<HTMLElement | null>;\n  private collator: Intl.Collator | undefined;\n  private layout: 'stack' | 'grid';\n  private orientation?: Orientation;\n  private direction?: Direction;\n  private layoutDelegate: LayoutDelegate;\n\n  constructor(collection: Collection<Node<T>>, disabledKeys: Set<Key>, ref: RefObject<HTMLElement | null>, collator?: Intl.Collator);\n  constructor(options: ListKeyboardDelegateOptions<T>);\n  constructor(...args: any[]) {\n    if (args.length === 1) {\n      let opts = args[0] as ListKeyboardDelegateOptions<T>;\n      this.collection = opts.collection;\n      this.ref = opts.ref;\n      this.collator = opts.collator;\n      this.disabledKeys = opts.disabledKeys || new Set();\n      this.disabledBehavior = opts.disabledBehavior || 'all';\n      this.orientation = opts.orientation || 'vertical';\n      this.direction = opts.direction;\n      this.layout = opts.layout || 'stack';\n      this.layoutDelegate = opts.layoutDelegate || new DOMLayoutDelegate(opts.ref);\n    } else {\n      this.collection = args[0];\n      this.disabledKeys = args[1];\n      this.ref = args[2];\n      this.collator = args[3];\n      this.layout = 'stack';\n      this.orientation = 'vertical';\n      this.disabledBehavior = 'all';\n      this.layoutDelegate = new DOMLayoutDelegate(this.ref);\n    }\n\n    // If this is a vertical stack, remove the left/right methods completely\n    // so they aren't called by useDroppableCollection.\n    if (this.layout === 'stack' && this.orientation === 'vertical') {\n      this.getKeyLeftOf = undefined;\n      this.getKeyRightOf = undefined;\n    }\n  }\n\n  private isDisabled(item: Node<unknown>) {\n    return this.disabledBehavior === 'all' && (item.props?.isDisabled || this.disabledKeys.has(item.key));\n  }\n\n  private findNextNonDisabled(key: Key | null, getNext: (key: Key) => Key | null): Key | null {\n    let nextKey = key;\n    while (nextKey != null) {\n      let item = this.collection.getItem(nextKey);\n      if (item?.type === 'item' && !this.isDisabled(item)) {\n        return nextKey;\n      }\n\n      nextKey = getNext(nextKey);\n    }\n\n    return null;\n  }\n\n  getNextKey(key: Key): Key | null {\n    let nextKey: Key | null = key;\n    nextKey = this.collection.getKeyAfter(nextKey);\n    return this.findNextNonDisabled(nextKey, key => this.collection.getKeyAfter(key));\n  }\n\n  getPreviousKey(key: Key): Key | null {\n    let nextKey: Key | null = key;\n    nextKey = this.collection.getKeyBefore(nextKey);\n    return this.findNextNonDisabled(nextKey, key => this.collection.getKeyBefore(key));\n  }\n\n  private findKey(\n    key: Key,\n    nextKey: (key: Key) => Key | null,\n    shouldSkip: (prevRect: Rect, itemRect: Rect) => boolean\n  ) {\n    let tempKey: Key | null = key;\n    let itemRect = this.layoutDelegate.getItemRect(tempKey);\n    if (!itemRect || tempKey == null) {\n      return null;\n    }\n\n    // Find the item above or below in the same column.\n    let prevRect = itemRect;\n    do {\n      tempKey = nextKey(tempKey);\n      if (tempKey == null) {\n        break;\n      }\n      itemRect = this.layoutDelegate.getItemRect(tempKey);\n    } while (itemRect && shouldSkip(prevRect, itemRect) && tempKey != null);\n\n    return tempKey;\n  }\n\n  private isSameRow(prevRect: Rect, itemRect: Rect) {\n    return prevRect.y === itemRect.y || prevRect.x !== itemRect.x;\n  }\n\n  private isSameColumn(prevRect: Rect, itemRect: Rect) {\n    return prevRect.x === itemRect.x || prevRect.y !== itemRect.y;\n  }\n\n  getKeyBelow(key: Key): Key | null {\n    if (this.layout === 'grid' && this.orientation === 'vertical') {\n      return this.findKey(key, (key) => this.getNextKey(key), this.isSameRow);\n    } else {\n      return this.getNextKey(key);\n    }\n  }\n\n  getKeyAbove(key: Key): Key | null {\n    if (this.layout === 'grid' && this.orientation === 'vertical') {\n      return this.findKey(key, (key) => this.getPreviousKey(key), this.isSameRow);\n    } else {\n      return this.getPreviousKey(key);\n    }\n  }\n\n  private getNextColumn(key: Key, right: boolean) {\n    return right ? this.getPreviousKey(key) : this.getNextKey(key);\n  }\n\n  getKeyRightOf?(key: Key): Key | null {\n    // This is a temporary solution for CardView until we refactor useSelectableCollection.\n    // https://github.com/orgs/adobe/projects/19/views/32?pane=issue&itemId=77825042\n    let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyRightOf' : 'getKeyLeftOf';\n    if (this.layoutDelegate[layoutDelegateMethod]) {\n      key = this.layoutDelegate[layoutDelegateMethod](key);\n      return this.findNextNonDisabled(key, key => this.layoutDelegate[layoutDelegateMethod](key));\n    }\n\n    if (this.layout === 'grid') {\n      if (this.orientation === 'vertical') {\n        return this.getNextColumn(key, this.direction === 'rtl');\n      } else {\n        return this.findKey(key, (key) => this.getNextColumn(key, this.direction === 'rtl'), this.isSameColumn);\n      }\n    } else if (this.orientation === 'horizontal') {\n      return this.getNextColumn(key, this.direction === 'rtl');\n    }\n\n    return null;\n  }\n\n  getKeyLeftOf?(key: Key): Key | null {\n    let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyLeftOf' : 'getKeyRightOf';\n    if (this.layoutDelegate[layoutDelegateMethod]) {\n      key = this.layoutDelegate[layoutDelegateMethod](key);\n      return this.findNextNonDisabled(key, key => this.layoutDelegate[layoutDelegateMethod](key));\n    }\n\n    if (this.layout === 'grid') {\n      if (this.orientation === 'vertical') {\n        return this.getNextColumn(key, this.direction === 'ltr');\n      } else {\n        return this.findKey(key, (key) => this.getNextColumn(key, this.direction === 'ltr'), this.isSameColumn);\n      }\n    } else if (this.orientation === 'horizontal') {\n      return this.getNextColumn(key, this.direction === 'ltr');\n    }\n\n    return null;\n  }\n\n  getFirstKey(): Key | null {\n    let key = this.collection.getFirstKey();\n    return this.findNextNonDisabled(key, key => this.collection.getKeyAfter(key));\n  }\n\n  getLastKey(): Key | null {\n    let key = this.collection.getLastKey();\n    return this.findNextNonDisabled(key, key => this.collection.getKeyBefore(key));\n  }\n\n  getKeyPageAbove(key: Key): Key | null {\n    let menu = this.ref.current;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n    if (!itemRect) {\n      return null;\n    }\n\n    if (menu && !isScrollable(menu)) {\n      return this.getFirstKey();\n    }\n\n    let nextKey: Key | null = key;\n    if (this.orientation === 'horizontal') {\n      let pageX = Math.max(0, itemRect.x + itemRect.width - this.layoutDelegate.getVisibleRect().width);\n\n      while (itemRect && itemRect.x > pageX && nextKey != null) {\n        nextKey = this.getKeyAbove(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    } else {\n      let pageY = Math.max(0, itemRect.y + itemRect.height - this.layoutDelegate.getVisibleRect().height);\n\n      while (itemRect && itemRect.y > pageY && nextKey != null) {\n        nextKey = this.getKeyAbove(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    }\n\n    return nextKey ?? this.getFirstKey();\n  }\n\n  getKeyPageBelow(key: Key): Key | null {\n    let menu = this.ref.current;\n    let itemRect = this.layoutDelegate.getItemRect(key);\n    if (!itemRect) {\n      return null;\n    }\n\n    if (menu && !isScrollable(menu)) {\n      return this.getLastKey();\n    }\n\n    let nextKey: Key | null = key;\n    if (this.orientation === 'horizontal') {\n      let pageX = Math.min(this.layoutDelegate.getContentSize().width, itemRect.y - itemRect.width + this.layoutDelegate.getVisibleRect().width);\n\n      while (itemRect && itemRect.x < pageX && nextKey != null) {\n        nextKey = this.getKeyBelow(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    } else {\n      let pageY = Math.min(this.layoutDelegate.getContentSize().height, itemRect.y - itemRect.height + this.layoutDelegate.getVisibleRect().height);\n\n      while (itemRect && itemRect.y < pageY && nextKey != null) {\n        nextKey = this.getKeyBelow(nextKey);\n        itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n      }\n    }\n\n    return nextKey ?? this.getLastKey();\n  }\n\n  getKeyForSearch(search: string, fromKey?: Key): Key | null {\n    if (!this.collator) {\n      return null;\n    }\n\n    let collection = this.collection;\n    let key = fromKey || this.getFirstKey();\n    while (key != null) {\n      let item = collection.getItem(key);\n      if (!item) {\n        return null;\n      }\n      let substring = item.textValue.slice(0, search.length);\n      if (item.textValue && this.collator.compare(substring, search) === 0) {\n        return key;\n      }\n\n      key = this.getNextKey(key);\n    }\n\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAkBM,MAAM;IA4CH,WAAW,IAAmB,EAAE;YACK;QAA3C,OAAO,IAAI,CAAC,gBAAgB,KAAK,SAAU,CAAA,CAAA,CAAA,cAAA,KAAK,KAAK,MAAA,QAAV,gBAAA,KAAA,IAAA,KAAA,IAAA,YAAY,UAAU,KAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,CAAA;IACrG;IAEQ,oBAAoB,GAAe,EAAE,OAAiC,EAAc;QAC1F,IAAI,UAAU;QACd,MAAO,WAAW,KAAM;YACtB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACnC,IAAI,CAAA,SAAA,QAAA,SAAA,KAAA,IAAA,KAAA,IAAA,KAAM,IAAI,MAAK,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,OAC5C,OAAO;YAGT,UAAU,QAAQ;QACpB;QAEA,OAAO;IACT;IAEA,WAAW,GAAQ,EAAc;QAC/B,IAAI,UAAsB;QAC1B,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QACtC,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IAC9E;IAEA,eAAe,GAAQ,EAAc;QACnC,IAAI,UAAsB;QAC1B,UAAU,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC/E;IAEQ,QACN,GAAQ,EACR,OAAiC,EACjC,UAAuD,EACvD;QACA,IAAI,UAAsB;QAC1B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,YAAY,WAAW,MAC1B,OAAO;QAGT,mDAAmD;QACnD,IAAI,WAAW;QACf,GAAG;YACD,UAAU,QAAQ;YAClB,IAAI,WAAW,MACb;YAEF,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC7C,QAAS,YAAY,WAAW,UAAU,aAAa,WAAW,KAAM;QAExE,OAAO;IACT;IAEQ,UAAU,QAAc,EAAE,QAAc,EAAE;QAChD,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,SAAS,CAAC;IAC/D;IAEQ,aAAa,QAAc,EAAE,QAAc,EAAE;QACnD,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,SAAS,CAAC;IAC/D;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,WAAW,KAAK,YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,SAAS;aAEtE,OAAO,IAAI,CAAC,UAAU,CAAC;IAE3B;IAEA,YAAY,GAAQ,EAAc;QAChC,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,WAAW,KAAK,YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,SAAS;aAE1E,OAAO,IAAI,CAAC,cAAc,CAAC;IAE/B;IAEQ,cAAc,GAAQ,EAAE,KAAc,EAAE;QAC9C,OAAO,QAAQ,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC;IAC5D;IAEA,cAAe,GAAQ,EAAc;QACnC,uFAAuF;QACvF,gFAAgF;QAChF,IAAI,uBAAuB,IAAI,CAAC,SAAS,KAAK,QAAQ,kBAAkB;QACxE,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;YAChD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;QACxF;QAEA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,YACvB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;iBAElD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,YAAY;QAE1G,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,cAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;QAGpD,OAAO;IACT;IAEA,aAAc,GAAQ,EAAc;QAClC,IAAI,uBAAuB,IAAI,CAAC,SAAS,KAAK,QAAQ,iBAAiB;QACvE,IAAI,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;YAChD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC;QACxF;QAEA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,YACvB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;iBAElD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,YAAY;QAE1G,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,cAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,KAAK;QAGpD,OAAO;IACT;IAEA,cAA0B;QACxB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;QACrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IAC1E;IAEA,aAAyB;QACvB,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU;QACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC3E;IAEA,gBAAgB,GAAQ,EAAc;QACpC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO;QAC3B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,QAAQ,CAAC,CAAA,wKAAA,eAAW,EAAE,OACxB,OAAO,IAAI,CAAC,WAAW;QAGzB,IAAI,UAAsB;QAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;YACrC,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,KAAK;YAEhG,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF,OAAO;YACL,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;YAElG,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF;QAEA,OAAO,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW,IAAI,CAAC,WAAW;IACpC;IAEA,gBAAgB,GAAQ,EAAc;QACpC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO;QAC3B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UACH,OAAO;QAGT,IAAI,QAAQ,CAAC,CAAA,wKAAA,eAAW,EAAE,OACxB,OAAO,IAAI,CAAC,UAAU;QAGxB,IAAI,UAAsB;QAC1B,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc;YACrC,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,KAAK,EAAE,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,KAAK;YAEzI,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF,OAAO;YACL,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM,EAAE,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,GAAG,MAAM;YAE5I,MAAO,YAAY,SAAS,CAAC,GAAG,SAAS,WAAW,KAAM;gBACxD,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC3B,WAAW,WAAW,OAAO,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACtE;QACF;QAEA,OAAO,YAAA,QAAA,YAAA,KAAA,IAAA,UAAW,IAAI,CAAC,UAAU;IACnC;IAEA,gBAAgB,MAAc,EAAE,OAAa,EAAc;QACzD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAChB,OAAO;QAGT,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,MAAM,WAAW,IAAI,CAAC,WAAW;QACrC,MAAO,OAAO,KAAM;YAClB,IAAI,OAAO,WAAW,OAAO,CAAC;YAC9B,IAAI,CAAC,MACH,OAAO;YAET,IAAI,YAAY,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM;YACrD,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,YAAY,GACjE,OAAO;YAGT,MAAM,IAAI,CAAC,UAAU,CAAC;QACxB;QAEA,OAAO;IACT;IAxPA,YAAY,GAAG,IAAW,CAAE;QAC1B,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;YACjC,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;YACnB,IAAI,CAAC,QAAQ,GAAG,KAAK,QAAQ;YAC7B,IAAI,CAAC,YAAY,GAAG,KAAK,YAAY,IAAI,IAAI;YAC7C,IAAI,CAAC,gBAAgB,GAAG,KAAK,gBAAgB,IAAI;YACjD,IAAI,CAAC,WAAW,GAAG,KAAK,WAAW,IAAI;YACvC,IAAI,CAAC,SAAS,GAAG,KAAK,SAAS;YAC/B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;YAC7B,IAAI,CAAC,cAAc,GAAG,KAAK,cAAc,IAAI,IAAI,CAAA,iLAAA,oBAAgB,EAAE,KAAK,GAAG;QAC7E,OAAO;YACL,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA,iLAAA,oBAAgB,EAAE,IAAI,CAAC,GAAG;QACtD;QAEA,wEAAwE;QACxE,mDAAmD;QACnD,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,WAAW,KAAK,YAAY;YAC9D,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;AA4NF", "debugId": null}}, {"offset": {"line": 6812, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/selection/dist/useSelectableList.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/selection/dist/packages/%40react-aria/selection/src/useSelectableList.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaSelectableCollectionOptions, useSelectableCollection} from './useSelectableCollection';\nimport {Collection, DOMAttributes, Key, KeyboardDelegate, LayoutDelegate, Node} from '@react-types/shared';\nimport {ListKeyboardDelegate} from './ListKeyboardDelegate';\nimport {useCollator} from '@react-aria/i18n';\nimport {useMemo} from 'react';\n\nexport interface AriaSelectableListOptions extends Omit<AriaSelectableCollectionOptions, 'keyboardDelegate'> {\n  /**\n   * State of the collection.\n   */\n  collection: Collection<Node<unknown>>,\n  /**\n   * A delegate object that implements behavior for keyboard focus movement.\n   */\n  keyboardDelegate?: KeyboardDelegate,\n  /**\n   * A delegate object that provides layout information for items in the collection.\n   * By default this uses the DOM, but this can be overridden to implement things like\n   * virtualized scrolling.\n   */\n  layoutDelegate?: LayoutDelegate,\n  /**\n   * The item keys that are disabled. These items cannot be selected, focused, or otherwise interacted with.\n   */\n  disabledKeys: Set<Key>\n}\n\nexport interface SelectableListAria {\n  /**\n   * Props for the option element.\n   */\n  listProps: DOMAttributes\n}\n\n/**\n * Handles interactions with a selectable list.\n */\nexport function useSelectableList(props: AriaSelectableListOptions): SelectableListAria {\n  let {\n    selectionManager,\n    collection,\n    disabledKeys,\n    ref,\n    keyboardDelegate,\n    layoutDelegate\n  } = props;\n\n  // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n  // When virtualized, the layout object will be passed in as a prop and override this.\n  let collator = useCollator({usage: 'search', sensitivity: 'base'});\n  let disabledBehavior = selectionManager.disabledBehavior;\n  let delegate = useMemo(() => (\n    keyboardDelegate || new ListKeyboardDelegate({\n      collection,\n      disabledKeys,\n      disabledBehavior,\n      ref,\n      collator,\n      layoutDelegate\n    })\n  ), [keyboardDelegate, layoutDelegate, collection, disabledKeys, ref, collator, disabledBehavior]);\n\n  let {collectionProps} = useSelectableCollection({\n    ...props,\n    ref,\n    selectionManager,\n    keyboardDelegate: delegate\n  });\n\n  return {\n    listProps: collectionProps\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAuCM,SAAS,0CAAkB,KAAgC;IAChE,IAAI,EAAA,kBACF,gBAAgB,EAAA,YAChB,UAAU,EAAA,cACV,YAAY,EAAA,KACZ,GAAG,EAAA,kBACH,gBAAgB,EAAA,gBAChB,cAAc,EACf,GAAG;IAEJ,0HAA0H;IAC1H,qFAAqF;IACrF,IAAI,WAAW,CAAA,sKAAA,cAAU,EAAE;QAAC,OAAO;QAAU,aAAa;IAAM;IAChE,IAAI,mBAAmB,iBAAiB,gBAAgB;IACxD,IAAI,WAAW,CAAA,iKAAA,UAAM,EAAE,IACrB,oBAAoB,IAAI,CAAA,oLAAA,uBAAmB,EAAE;wBAC3C;0BACA;8BACA;iBACA;sBACA;4BACA;QACF,IACC;QAAC;QAAkB;QAAgB;QAAY;QAAc;QAAK;QAAU;KAAiB;IAEhG,IAAI,EAAA,iBAAC,eAAe,EAAC,GAAG,CAAA,uLAAA,0BAAsB,EAAE;QAC9C,GAAG,KAAK;aACR;0BACA;QACA,kBAAkB;IACpB;IAEA,OAAO;QACL,WAAW;IACb;AACF", "debugId": null}}, {"offset": {"line": 6874, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/tooltip/dist/useTooltip.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/tooltip/dist/packages/%40react-aria/tooltip/src/useTooltip.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaTooltipProps} from '@react-types/tooltip';\nimport {DOMAttributes} from '@react-types/shared';\nimport {filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {TooltipTriggerState} from '@react-stately/tooltip';\nimport {useHover} from '@react-aria/interactions';\n\nexport interface TooltipAria {\n  /**\n   * Props for the tooltip element.\n   */\n  tooltipProps: DOMAttributes\n}\n\n/**\n * Provides the accessibility implementation for a Tooltip component.\n */\nexport function useTooltip(props: AriaTooltipProps, state?: TooltipTriggerState): TooltipAria {\n  let domProps = filterDOMProps(props, {labelable: true});\n\n  let {hoverProps} = useHover({\n    onHoverStart: () => state?.open(true),\n    onHoverEnd: () => state?.close()\n  });\n\n  return {\n    tooltipProps: mergeProps(domProps, hoverProps, {\n      role: 'tooltip'\n    })\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GAkBM,SAAS,0CAAW,KAAuB,EAAE,KAA2B;IAC7E,IAAI,WAAW,CAAA,0KAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAErD,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;QAC1B,cAAc,IAAM,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,IAAI,CAAC;QAChC,YAAY,IAAM,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,KAAK;IAChC;IAEA,OAAO;QACL,cAAc,CAAA,sKAAA,aAAS,EAAE,UAAU,YAAY;YAC7C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 6912, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/tooltip/dist/useTooltipTrigger.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/tooltip/dist/packages/%40react-aria/tooltip/src/useTooltipTrigger.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, RefObject} from '@react-types/shared';\nimport {getInteractionModality, isFocusVisible, useFocusable, useHover} from '@react-aria/interactions';\nimport {mergeProps, useId} from '@react-aria/utils';\nimport {TooltipTriggerProps} from '@react-types/tooltip';\nimport {TooltipTriggerState} from '@react-stately/tooltip';\nimport {useEffect, useRef} from 'react';\n\nexport interface TooltipTriggerAria {\n  /**\n   * Props for the trigger element.\n   */\n  triggerProps: DOMAttributes,\n\n  /**\n   * Props for the overlay container element.\n   */\n  tooltipProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a tooltip trigger, e.g. a button\n * that shows a description when focused or hovered.\n */\nexport function useTooltipTrigger(props: TooltipTriggerProps, state: TooltipTriggerState, ref: RefObject<FocusableElement | null>) : TooltipTriggerAria {\n  let {\n    isDisabled,\n    trigger\n  } = props;\n\n  let tooltipId = useId();\n\n  let isHovered = useRef(false);\n  let isFocused = useRef(false);\n\n  let handleShow = () => {\n    if (isHovered.current || isFocused.current) {\n      state.open(isFocused.current);\n    }\n  };\n\n  let handleHide = (immediate?: boolean) => {\n    if (!isHovered.current && !isFocused.current) {\n      state.close(immediate);\n    }\n  };\n\n  useEffect(() => {\n    let onKeyDown = (e) => {\n      if (ref && ref.current) {\n        // Escape after clicking something can give it keyboard focus\n        // dismiss tooltip on esc key press\n        if (e.key === 'Escape') {\n          e.stopPropagation();\n          state.close(true);\n        }\n      }\n    };\n    if (state.isOpen) {\n      document.addEventListener('keydown', onKeyDown, true);\n      return () => {\n        document.removeEventListener('keydown', onKeyDown, true);\n      };\n    }\n  }, [ref, state]);\n\n  let onHoverStart = () => {\n    if (trigger === 'focus') {\n      return;\n    }\n    // In chrome, if you hover a trigger, then another element obscures it, due to keyboard\n    // interactions for example, hover will end. When hover is restored after that element disappears,\n    // focus moves on for example, then the tooltip will reopen. We check the modality to know if the hover\n    // is the result of moving the mouse.\n    if (getInteractionModality() === 'pointer') {\n      isHovered.current = true;\n    } else {\n      isHovered.current = false;\n    }\n    handleShow();\n  };\n\n  let onHoverEnd = () => {\n    if (trigger === 'focus') {\n      return;\n    }\n    // no matter how the trigger is left, we should close the tooltip\n    isFocused.current = false;\n    isHovered.current = false;\n    handleHide();\n  };\n\n  let onPressStart = () => {\n    // no matter how the trigger is pressed, we should close the tooltip\n    isFocused.current = false;\n    isHovered.current = false;\n    handleHide(true);\n  };\n\n  let onFocus = () => {\n    let isVisible = isFocusVisible();\n    if (isVisible) {\n      isFocused.current = true;\n      handleShow();\n    }\n  };\n\n  let onBlur = () => {\n    isFocused.current = false;\n    isHovered.current = false;\n    handleHide(true);\n  };\n\n  let {hoverProps} = useHover({\n    isDisabled,\n    onHoverStart,\n    onHoverEnd\n  });\n\n  let {focusableProps} = useFocusable({\n    isDisabled,\n    onFocus,\n    onBlur\n  }, ref);\n\n  return {\n    triggerProps: {\n      'aria-describedby': state.isOpen ? tooltipId : undefined,\n      ...mergeProps(focusableProps, hoverProps, {\n        onPointerDown: onPressStart,\n        onKeyDown: onPressStart,\n        tabIndex: undefined\n      })\n    },\n    tooltipProps: {\n      id: tooltipId\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAyBM,SAAS,0CAAkB,KAA0B,EAAE,KAA0B,EAAE,GAAuC;IAC/H,IAAI,EAAA,YACF,UAAU,EAAA,SACV,OAAO,EACR,GAAG;IAEJ,IAAI,YAAY,CAAA,iKAAA,QAAI;IAEpB,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE;IACvB,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE;IAEvB,IAAI,aAAa;QACf,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,EACxC,MAAM,IAAI,CAAC,UAAU,OAAO;IAEhC;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,EAC1C,MAAM,KAAK,CAAC;IAEhB;IAEA,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,YAAY,CAAC;YACf,IAAI,OAAO,IAAI,OAAO,EACpB,AACA,mCAAmC,0BAD0B;YAE7D;gBAAA,IAAI,EAAE,GAAG,KAAK,UAAU;oBACtB,EAAE,eAAe;oBACjB,MAAM,KAAK,CAAC;gBACd;YAAA;QAEJ;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,SAAS,gBAAgB,CAAC,WAAW,WAAW;YAChD,OAAO;gBACL,SAAS,mBAAmB,CAAC,WAAW,WAAW;YACrD;QACF;IACF,GAAG;QAAC;QAAK;KAAM;IAEf,IAAI,eAAe;QACjB,IAAI,YAAY,SACd;QAEF,uFAAuF;QACvF,kGAAkG;QAClG,uGAAuG;QACvG,qCAAqC;QACrC,IAAI,CAAA,kLAAA,yBAAqB,QAAQ,WAC/B,UAAU,OAAO,GAAG;aAEpB,UAAU,OAAO,GAAG;QAEtB;IACF;IAEA,IAAI,aAAa;QACf,IAAI,YAAY,SACd;QAEF,iEAAiE;QACjE,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB;IACF;IAEA,IAAI,eAAe;QACjB,oEAAoE;QACpE,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB,WAAW;IACb;IAEA,IAAI,UAAU;QACZ,IAAI,YAAY,CAAA,kLAAA,iBAAa;QAC7B,IAAI,WAAW;YACb,UAAU,OAAO,GAAG;YACpB;QACF;IACF;IAEA,IAAI,SAAS;QACX,UAAU,OAAO,GAAG;QACpB,UAAU,OAAO,GAAG;QACpB,WAAW;IACb;IAEA,IAAI,EAAA,YAAC,UAAU,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;oBAC1B;sBACA;oBACA;IACF;IAEA,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,+KAAA,eAAW,EAAE;oBAClC;iBACA;gBACA;IACF,GAAG;IAEH,OAAO;QACL,cAAc;YACZ,oBAAoB,MAAM,MAAM,GAAG,YAAY;YAC/C,GAAG,CAAA,sKAAA,aAAS,EAAE,gBAAgB,YAAY;gBACxC,eAAe;gBACf,WAAW;gBACX,UAAU;YACZ,EAAE;QACJ;QACA,cAAc;YACZ,IAAI;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 7030, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@react-aria/button/dist/useButton.module.js.map", "sources": ["file:///D:/sintesaNEXT2/node_modules/%40react-aria/button/dist/packages/%40react-aria/button/src/useButton.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {\n  AnchorHTMLAttributes,\n  ButtonHTMLAttributes,\n  ElementType,\n  HTMLAttributes,\n  InputHTMLAttributes,\n  RefObject\n} from 'react';\nimport {AriaButtonProps} from '@react-types/button';\nimport {DOMAttributes} from '@react-types/shared';\nimport {filterDOMProps, mergeProps} from '@react-aria/utils';\nimport {useFocusable, usePress} from '@react-aria/interactions';\n\nexport interface AriaButtonOptions<E extends ElementType> extends Omit<AriaButtonProps<E>, 'children'> {}\n\nexport interface ButtonAria<T> {\n  /** Props for the button element. */\n  buttonProps: T,\n  /** Whether the button is currently pressed. */\n  isPressed: boolean\n}\n\n// Order with overrides is important: 'button' should be default\nexport function useButton(props: AriaButtonOptions<'button'>, ref: RefObject<HTMLButtonElement | null>): ButtonAria<ButtonHTMLAttributes<HTMLButtonElement>>;\nexport function useButton(props: AriaButtonOptions<'a'>, ref: RefObject<HTMLAnchorElement | null>): ButtonAria<AnchorHTMLAttributes<HTMLAnchorElement>>;\nexport function useButton(props: AriaButtonOptions<'div'>, ref: RefObject<HTMLDivElement | null>): ButtonAria<HTMLAttributes<HTMLDivElement>>;\nexport function useButton(props: AriaButtonOptions<'input'>, ref: RefObject<HTMLInputElement | null>): ButtonAria<InputHTMLAttributes<HTMLInputElement>>;\nexport function useButton(props: AriaButtonOptions<'span'>, ref: RefObject<HTMLSpanElement | null>): ButtonAria<HTMLAttributes<HTMLSpanElement>>;\nexport function useButton(props: AriaButtonOptions<ElementType>, ref: RefObject<Element | null>): ButtonAria<DOMAttributes>;\n/**\n * Provides the behavior and accessibility implementation for a button component. Handles mouse, keyboard, and touch interactions,\n * focus behavior, and ARIA props for both native button elements and custom element types.\n * @param props - Props to be applied to the button.\n * @param ref - A ref to a DOM element for the button.\n */\nexport function useButton(props: AriaButtonOptions<ElementType>, ref: RefObject<any>): ButtonAria<HTMLAttributes<any>> {\n  let {\n    elementType = 'button',\n    isDisabled,\n    onPress,\n    onPressStart,\n    onPressEnd,\n    onPressUp,\n    onPressChange,\n    preventFocusOnPress,\n    // @ts-ignore - undocumented\n    allowFocusWhenDisabled,\n    onClick,\n    href,\n    target,\n    rel,\n    type = 'button'\n  } = props;\n  let additionalProps;\n  if (elementType === 'button') {\n    additionalProps = {\n      type,\n      disabled: isDisabled,\n      form: props.form,\n      formAction: props.formAction,\n      formEncType: props.formEncType,\n      formMethod: props.formMethod,\n      formNoValidate: props.formNoValidate,\n      formTarget: props.formTarget,\n      name: props.name,\n      value: props.value\n    };\n  } else {\n    additionalProps = {\n      role: 'button',\n      href: elementType === 'a' && !isDisabled ? href : undefined,\n      target: elementType === 'a' ? target : undefined,\n      type: elementType === 'input' ? type : undefined,\n      disabled: elementType === 'input' ? isDisabled : undefined,\n      'aria-disabled': !isDisabled || elementType === 'input' ? undefined : isDisabled,\n      rel: elementType === 'a' ? rel : undefined\n    };\n  }\n\n  let {pressProps, isPressed} = usePress({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress,\n    onPressUp,\n    onClick,\n    isDisabled,\n    preventFocusOnPress,\n    ref\n  });\n\n  let {focusableProps} = useFocusable(props, ref);\n  if (allowFocusWhenDisabled) {\n    focusableProps.tabIndex = isDisabled ? -1 : focusableProps.tabIndex;\n  }\n  let buttonProps = mergeProps(focusableProps, pressProps, filterDOMProps(props, {labelable: true}));\n\n  return {\n    isPressed, // Used to indicate press state for visual\n    buttonProps: mergeProps(additionalProps, buttonProps, {\n      'aria-haspopup': props['aria-haspopup'],\n      'aria-expanded': props['aria-expanded'],\n      'aria-controls': props['aria-controls'],\n      'aria-pressed': props['aria-pressed'],\n      'aria-current': props['aria-current']\n    })\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC,GAqCM,SAAS,0CAAU,KAAqC,EAAE,GAAmB;IAClF,IAAI,EAAA,aACF,cAAc,QAAA,EAAA,YACd,UAAU,EAAA,SACV,OAAO,EAAA,cACP,YAAY,EAAA,YACZ,UAAU,EAAA,WACV,SAAS,EAAA,eACT,aAAa,EAAA,qBACb,mBAAmB,EAAA,wBACnB,AACA,sBAAsB,EAAA,IADM,KAE5B,OAAO,EAAA,MACP,IAAI,EAAA,QACJ,MAAM,EAAA,KACN,GAAG,EAAA,MACH,OAAO,QAAA,EACR,GAAG;IACJ,IAAI;IACJ,IAAI,gBAAgB,UAClB,kBAAkB;cAChB;QACA,UAAU;QACV,MAAM,MAAM,IAAI;QAChB,YAAY,MAAM,UAAU;QAC5B,aAAa,MAAM,WAAW;QAC9B,YAAY,MAAM,UAAU;QAC5B,gBAAgB,MAAM,cAAc;QACpC,YAAY,MAAM,UAAU;QAC5B,MAAM,MAAM,IAAI;QAChB,OAAO,MAAM,KAAK;IACpB;SAEA,kBAAkB;QAChB,MAAM;QACN,MAAM,gBAAgB,OAAO,CAAC,aAAa,OAAO;QAClD,QAAQ,gBAAgB,MAAM,SAAS;QACvC,MAAM,gBAAgB,UAAU,OAAO;QACvC,UAAU,gBAAgB,UAAU,aAAa;QACjD,iBAAiB,CAAC,cAAc,gBAAgB,UAAU,YAAY;QACtE,KAAK,gBAAgB,MAAM,MAAM;IACnC;IAGF,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,2KAAA,WAAO,EAAE;sBACrC;oBACA;uBACA;iBACA;mBACA;iBACA;oBACA;6BACA;aACA;IACF;IAEA,IAAI,EAAA,gBAAC,cAAc,EAAC,GAAG,CAAA,+KAAA,eAAW,EAAE,OAAO;IAC3C,IAAI,wBACF,eAAe,QAAQ,GAAG,aAAa,CAAA,IAAK,eAAe,QAAQ;IAErE,IAAI,cAAc,CAAA,sKAAA,aAAS,EAAE,gBAAgB,YAAY,CAAA,0KAAA,iBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IAE/F,OAAO;mBACL;QACA,aAAa,CAAA,sKAAA,aAAS,EAAE,iBAAiB,aAAa;YACpD,iBAAiB,KAAK,CAAC,gBAAgB;YACvC,iBAAiB,KAAK,CAAC,gBAAgB;YACvC,iBAAiB,KAAK,CAAC,gBAAgB;YACvC,gBAAgB,KAAK,CAAC,eAAe;YACrC,gBAAgB,KAAK,CAAC,eAAe;QACvC;IACF;AACF", "debugId": null}}]}