{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/auth/authLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuthLayoutWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthLayoutWrapper() from the server but AuthLayoutWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/auth/authLayout.tsx <module evaluation>\",\n    \"AuthLayoutWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6EACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/features/auth/authLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuthLayoutWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthLayoutWrapper() from the server but AuthLayoutWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/auth/authLayout.tsx\",\n    \"AuthLayoutWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yDACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/app/%28auth%29/layout.tsx"], "sourcesContent": ["import { AuthLayoutWrapper } from \"@/components/features/auth/authLayout\";\r\nimport \"@/styles/globals.css\";\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return <AuthLayoutWrapper>{children}</AuthLayoutWrapper>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBAAO,8OAAC,oJAAA,CAAA,oBAAiB;kBAAE;;;;;;AAC7B", "debugId": null}}]}