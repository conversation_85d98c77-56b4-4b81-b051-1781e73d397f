{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/Omspan.jsx"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\n\r\nconst showToast = (pesan) => {\r\n  Swal.close(); // Menutup notifikasi yang sedang ditampilkan\r\n\r\n  setTimeout(() => {\r\n    Swal.fire({\r\n      text: `${pesan} `,\r\n      position: \"top-end\",\r\n      showConfirmButton: false,\r\n      toast: true,\r\n      timer: 5000,\r\n      background: \"black\",\r\n      color: \"#ffffff\",\r\n      showClass: {\r\n        popup: \"animate__animated \",\r\n      },\r\n    });\r\n  }, 500); // Menunggu 500ms sebelum menampilkan notifikasi baru\r\n};\r\nconst NotifikasiToast = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} 👋`,\r\n    position: \"top-end\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 3000,\r\n    background: \"#C16DFA\",\r\n    color: \"#ffffff\",\r\n  });\r\n};\r\n\r\nconst NotifikasiToastEPA = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} `,\r\n    position: \"top-start\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 3000,\r\n    background: \"#17c3fa\",\r\n    color: \"#ffffff\",\r\n  });\r\n};\r\n\r\nexport const Toast = Swal.mixin({\r\n  toast: true,\r\n  position: \"top-start\",\r\n  showConfirmButton: false,\r\n  timer: 3000,\r\n  timerProgressBar: true,\r\n  customClass: {\r\n    popup: \"custom-toast-font custom-toast-primary-light\",\r\n  },\r\n  didOpen: (toast) => {\r\n    toast.onmouseenter = Swal.stopTimer;\r\n    toast.onmouseleave = Swal.resumeTimer;\r\n  },\r\n});\r\n\r\nconst NotifikasiDisclaimer = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} `,\r\n    position: \"top-start\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 5000,\r\n    background: \"#FF5733\",\r\n    color: \"#ffffff\",\r\n    showCloseButton: true,\r\n  });\r\n};\r\n\r\nconst UserLogin = async (pesan) => {\r\n  await Swal.fire({\r\n    text: `${pesan} `,\r\n    position: \"bottom-start\",\r\n    showConfirmButton: false,\r\n    toast: true,\r\n    timer: 3000,\r\n    background: \"#FF5733\",\r\n    color: \"#ffffff\",\r\n    showCloseButton: true,\r\n    animation: false,\r\n  });\r\n};\r\n\r\nexport const Omspan = (username, message) => {\r\n  showToast(username, message);\r\n};\r\n\r\nexport const Tunda = (username) => {\r\n  showToast(username, \"Tunda OMSPAN berhasil\");\r\n};\r\n\r\nexport const Pesan = (pesan) => {\r\n  showToast(pesan);\r\n};\r\nexport const NotifPesan = (pesan) => {\r\n  NotifikasiToast(pesan);\r\n};\r\n\r\nexport const NotifDisclaimer = (pesan) => {\r\n  NotifikasiDisclaimer(pesan);\r\n};\r\n\r\n// export const ToastUserLogin = (pesan) => {\r\n//   UserLogin(pesan);\r\n// };\r\n\r\nexport const EPANOTIF = (pesan) => {\r\n  NotifikasiToastEPA(pesan);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,MAAM,YAAY,CAAC;IACjB,gKAAA,CAAA,UAAI,CAAC,KAAK,IAAI,6CAA6C;IAE3D,WAAW;QACT,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACR,MAAM,GAAG,MAAM,CAAC,CAAC;YACjB,UAAU;YACV,mBAAmB;YACnB,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,WAAW;gBACT,OAAO;YACT;QACF;IACF,GAAG,MAAM,qDAAqD;AAChE;AACA,MAAM,kBAAkB,OAAO;IAC7B,MAAM,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,GAAG,MAAM,GAAG,CAAC;QACnB,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;AACF;AAEA,MAAM,qBAAqB,OAAO;IAChC,MAAM,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,GAAG,MAAM,CAAC,CAAC;QACjB,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;AACF;AAEO,MAAM,QAAQ,gKAAA,CAAA,UAAI,CAAC,KAAK,CAAC;IAC9B,OAAO;IACP,UAAU;IACV,mBAAmB;IACnB,OAAO;IACP,kBAAkB;IAClB,aAAa;QACX,OAAO;IACT;IACA,SAAS,CAAC;QACR,MAAM,YAAY,GAAG,gKAAA,CAAA,UAAI,CAAC,SAAS;QACnC,MAAM,YAAY,GAAG,gKAAA,CAAA,UAAI,CAAC,WAAW;IACvC;AACF;AAEA,MAAM,uBAAuB,OAAO;IAClC,MAAM,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,GAAG,MAAM,CAAC,CAAC;QACjB,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;QACP,iBAAiB;IACnB;AACF;AAEA,MAAM,YAAY,OAAO;IACvB,MAAM,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACd,MAAM,GAAG,MAAM,CAAC,CAAC;QACjB,UAAU;QACV,mBAAmB;QACnB,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;QACP,iBAAiB;QACjB,WAAW;IACb;AACF;AAEO,MAAM,SAAS,CAAC,UAAU;IAC/B,UAAU,UAAU;AACtB;AAEO,MAAM,QAAQ,CAAC;IACpB,UAAU,UAAU;AACtB;AAEO,MAAM,QAAQ,CAAC;IACpB,UAAU;AACZ;AACO,MAAM,aAAa,CAAC;IACzB,gBAAgB;AAClB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,qBAAqB;AACvB;AAMO,MAAM,WAAW,CAAC;IACvB,mBAAmB;AACrB", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/sintesaNEXT2/src/components/ui/feedback/toastError.jsx"], "sourcesContent": ["import Swal from \"sweetalert2\";\r\n\r\n// Fungsi untuk menampilkan pesan toast dengan SweetAlert2\r\nconst ToastError = (title, text, icon = \"error\") => {\r\n  Swal.fire({\r\n    title,\r\n    text,\r\n    icon,\r\n    position: \"top-end\", // Menentukan posisi di atas sebelah kanan\r\n    toast: true,\r\n    showConfirmButton: false, // Tidak menampilkan tombol OK\r\n    timer: 5000,\r\n    showCloseButton: true,\r\n    background: \"red\",\r\n    color: \"white\",\r\n    // color: \"#716add\",\r\n    // customClass: {\r\n    //   container: \"toast-container\",\r\n    //   popup: \"colored-toast\",\r\n    // },\r\n    timerProgressBar: true,\r\n  });\r\n};\r\n\r\n// Fungsi untuk menampilkan pesan error berdasarkan kode status HTTP\r\nconst handleHttpError = (status, text) => {\r\n  switch (status) {\r\n    case 400:\r\n      ToastError(`<PERSON><PERSON>ahan <PERSON>, Permintaan tidak valid. (${text})`);\r\n      break;\r\n    case 401:\r\n      ToastError(\r\n        `Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (${text})`\r\n      );\r\n      break;\r\n    case 403:\r\n      ToastError(`<PERSON><PERSON><PERSON>, <PERSON>ks<PERSON> ke sumber daya dilarang. (${text})`);\r\n      break;\r\n    case 404:\r\n      ToastError(`Error Refresh Token. Silahkan Login Ulang... (${text})`);\r\n      break;\r\n    case 429:\r\n      ToastError(\r\n        `Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${text})`\r\n      );\r\n      break;\r\n    case 422:\r\n      ToastError(\r\n        `Unprocessable Entity, Permintaan tidak dapat diolah. (${text})`\r\n      );\r\n      break;\r\n    case 500:\r\n      ToastError(\"Kesalahan Pada Query\", text);\r\n      break;\r\n    case 503:\r\n      ToastError(\r\n        `Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${text})`\r\n      );\r\n      break;\r\n    case 504:\r\n      ToastError(`Waktu Habis, Permintaan waktu habis. (${text})`);\r\n      break;\r\n    case 505:\r\n      ToastError(\r\n        `Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${text})`\r\n      );\r\n      break;\r\n    case 507:\r\n      ToastError(\r\n        `Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${text})`\r\n      );\r\n      break;\r\n    case 511:\r\n      ToastError(`Autentikasi Diperlukan, Autentikasi diperlukan. (${text})`);\r\n      break;\r\n    default:\r\n      ToastError(`Kesalahan Server, ${text} `);\r\n      break;\r\n  }\r\n};\r\n\r\nexport { ToastError, handleHttpError };\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,0DAA0D;AAC1D,MAAM,aAAa,CAAC,OAAO,MAAM,OAAO,OAAO;IAC7C,gKAAA,CAAA,UAAI,CAAC,IAAI,CAAC;QACR;QACA;QACA;QACA,UAAU;QACV,OAAO;QACP,mBAAmB;QACnB,OAAO;QACP,iBAAiB;QACjB,YAAY;QACZ,OAAO;QACP,oBAAoB;QACpB,iBAAiB;QACjB,kCAAkC;QAClC,4BAA4B;QAC5B,KAAK;QACL,kBAAkB;IACpB;AACF;AAEA,oEAAoE;AACpE,MAAM,kBAAkB,CAAC,QAAQ;IAC/B,OAAQ;QACN,KAAK;YACH,WAAW,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACpE;QACF,KAAK;YACH,WACE,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YAEtE;QACF,KAAK;YACH,WAAW,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YACpE;QACF,KAAK;YACH,WAAW,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACnE;QACF,KAAK;YACH,WACE,CAAC,kEAAkE,EAAE,KAAK,CAAC,CAAC;YAE9E;QACF,KAAK;YACH,WACE,CAAC,sDAAsD,EAAE,KAAK,CAAC,CAAC;YAElE;QACF,KAAK;YACH,WAAW,wBAAwB;YACnC;QACF,KAAK;YACH,WACE,CAAC,0DAA0D,EAAE,KAAK,CAAC,CAAC;YAEtE;QACF,KAAK;YACH,WAAW,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC3D;QACF,KAAK;YACH,WACE,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAEnE;QACF,KAAK;YACH,WACE,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAEnE;QACF,KAAK;YACH,WAAW,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACtE;QACF;YACE,WAAW,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACvC;IACJ;AACF", "debugId": null}}]}